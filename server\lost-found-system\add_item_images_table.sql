USE lost_found;

-- 创建物品图片表
CREATE TABLE IF NOT EXISTS item_images (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    item_id BIGINT NOT NULL COMMENT '物品ID',
    item_type VARCHAR(10) NOT NULL COMMENT '物品类型(LOST/FOUND)',
    image_url VARCHAR(255) NOT NULL COMMENT '图片URL',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_item_id_type (item_id, item_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品图片表';

-- 添加注释
ALTER TABLE item_images COMMENT = '存储失物和拾物的多张图片';
