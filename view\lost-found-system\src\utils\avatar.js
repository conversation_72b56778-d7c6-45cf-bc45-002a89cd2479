/**
 * 生成默认头像
 * 基于用户名首字符和随机背景色
 */

// 预定义的背景颜色
const COLORS = [
  '#f56a00', // 橙色
  '#7265e6', // 紫色
  '#ffbf00', // 黄色
  '#00a2ae', // 青色
  '#1890ff', // 蓝色
  '#52c41a', // 绿色
  '#f5222d', // 红色
  '#fa541c', // 火红
  '#13c2c2', // 青柠
  '#eb2f96'  // 粉色
]

/**
 * 获取用户名首字符
 * @param {string} username 用户名
 * @returns {string} 首字符（大写）
 */
export function getFirstChar(username) {
  if (!username) return '?'
  
  // 获取第一个字符
  const firstChar = username.charAt(0)
  
  // 如果是英文字符，转为大写
  if (/[a-zA-Z]/.test(firstChar)) {
    return firstChar.toUpperCase()
  }
  
  // 如果是中文或其他字符，直接返回
  return firstChar
}

/**
 * 根据用户ID获取固定的背景色
 * @param {number} userId 用户ID
 * @returns {string} 背景色
 */
export function getBackgroundColor(userId) {
  // 如果没有用户ID，随机选择一个颜色
  if (!userId) {
    const randomIndex = Math.floor(Math.random() * COLORS.length)
    return COLORS[randomIndex]
  }
  
  // 根据用户ID选择固定的颜色
  const colorIndex = userId % COLORS.length
  return COLORS[colorIndex]
}

/**
 * 生成默认头像的样式对象
 * @param {string} username 用户名
 * @param {number} userId 用户ID
 * @returns {object} 样式对象
 */
export function generateAvatarStyle(username, userId) {
  return {
    backgroundColor: getBackgroundColor(userId),
    color: '#fff',
    fontSize: '18px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  }
}

/**
 * 生成默认头像组件
 * @param {string} username 用户名
 * @param {number} userId 用户ID
 * @returns {object} 头像配置
 */
export function generateDefaultAvatar(username, userId) {
  return {
    text: getFirstChar(username),
    style: generateAvatarStyle(username, userId)
  }
}
