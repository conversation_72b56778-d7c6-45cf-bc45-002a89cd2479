package com.tomato.lostfoundsystem.enums;

public enum FileType {
    IMAGE("IMAGE"),   // 图片
    AUDIO("AUDIO"),   // 音频
    VIDEO("VIDEO"),   // 视频
    DOCUMENT("DOCUMENT");  // 文档

    private final String type;

    // 枚举构造器
    FileType(String type) {
        this.type = type;
    }

    // 获取枚举类型
    public String getType() {
        return type;
    }

    // 根据值获取枚举类型
    public static FileType fromValue(String value) {
        for (FileType fileType : values()) {
            if (fileType.getType().equalsIgnoreCase(value)) {
                return fileType;
            }
        }
        throw new IllegalArgumentException("Invalid file type: " + value);
    }

    @Override
    public String toString() {
        return this.type;  // 返回文件类型名称（IMAGE, AUDIO, VIDEO）
    }
}
