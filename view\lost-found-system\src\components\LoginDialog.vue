<template>
  <el-dialog
    v-model="dialogVisible"
    width="448px"
    :close-on-click-modal="false"
    :show-close="true"
    class="google-style-dialog"
    top="5vh"
    :append-to-body="true"
    :destroy-on-close="false"
    transition="dialog-fade"
    @close="handleClose"
  >
    <!-- 关闭按钮 -->
    <div class="dialog-close-btn" @click="handleClose">
      <el-icon><Close /></el-icon>
    </div>

    <div class="auth-container">
      <!-- 头部 -->
      <div class="auth-header">
        <!-- 只显示系统Logo，移除旁边的文字 -->
        <div class="header-content">
          <div class="logo-container">
            <img src="/images/logo_text.png" alt="校园失物招领系统" class="system-logo" />
          </div>
          <div class="title-container">
            <h1 class="auth-title">{{ isLogin ? '登录' : '注册' }}</h1>
          </div>
        </div>
      </div>

      <!-- 登录/注册切换按钮 (仅在移动端显示) -->
      <div class="auth-toggle-mobile">
        <span
          :class="{ 'active': isLogin }"
          @click="isLogin = true"
        >
          登录
        </span>
        <span
          :class="{ 'active': !isLogin }"
          @click="isLogin = false"
        >
          注册
        </span>
      </div>

      <!-- 登录表单 -->
      <div v-if="isLogin" class="form-wrapper">
        <!-- 登录方式切换 -->
        <div class="login-type-tabs">
          <div
            class="login-type-tab"
            :class="{ 'active': loginType === 'account' }"
            @click="loginType = 'account'"
          >
            <el-icon><User /></el-icon>
            <span>账号密码登录</span>
          </div>
          <div
            class="login-type-tab"
            :class="{ 'active': loginType === 'verify' }"
            @click="loginType = 'verify'"
          >
            <el-icon><Message /></el-icon>
            <span>验证码登录</span>
          </div>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="auth-form"
          autocomplete="off"
          @submit.prevent="handleLogin"
        >
          <!-- 账号密码登录 -->
          <template v-if="loginType === 'account'">
            <el-form-item prop="username" class="floating-label-form-item">
              <el-input
                v-model="loginForm.username"
                class="auth-input"
                placeholder=" "
                autocomplete="username"
                :class="{ 'is-filled': loginForm.username }"
              >
                <template #prefix>
                  <el-icon class="input-icon"><User /></el-icon>
                </template>
              </el-input>
              <label class="floating-label">用户名</label>
            </el-form-item>

            <el-form-item prop="password" class="floating-label-form-item">
              <el-input
                v-model="loginForm.password"
                type="password"
                class="auth-input"
                placeholder=" "
                autocomplete="current-password"
                show-password
                :class="{ 'is-filled': loginForm.password }"
              >
                <template #prefix>
                  <el-icon class="input-icon"><Lock /></el-icon>
                </template>
              </el-input>
              <label class="floating-label">密码</label>
            </el-form-item>

            <el-form-item prop="verifyCode" class="verify-code-form-item floating-label-form-item">
              <div class="captcha-container graphic-captcha-container">
                <el-input
                  v-model="loginForm.verifyCode"
                  class="auth-input captcha-input verification-input"
                  placeholder=" "
                  autocomplete="off"
                  maxlength="6"
                  :class="{ 'is-filled': loginForm.verifyCode }"
                >
                  <template #prefix>
                    <el-icon class="input-icon"><Key /></el-icon>
                  </template>
                </el-input>
                <label class="floating-label">验证码</label>
                <div class="captcha-img-container" @click="refreshCaptcha">
                  <img v-if="captchaUrl" :src="captchaUrl" alt="验证码" class="captcha-img" />
                  <div v-else class="captcha-loading">
                    <el-icon class="is-loading"><Loading /></el-icon>
                  </div>
                </div>
              </div>
            </el-form-item>
          </template>

          <!-- 验证码登录 -->
          <template v-if="loginType === 'verify'">
            <!-- 验证方式选择器 (简化为单选按钮) -->
            <el-form-item prop="verifyType" class="verify-type-radio">
              <el-radio-group v-model="loginForm.verifyType" size="large">
                <el-radio label="email">邮箱验证</el-radio>
                <el-radio label="phone">手机验证</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 邮箱/手机号输入 -->
            <el-form-item :prop="loginForm.verifyType === 'email' ? 'email' : 'phone'" class="floating-label-form-item">
              <el-input
                v-model="loginForm[loginForm.verifyType]"
                class="auth-input"
                placeholder=" "
                :class="{ 'is-filled': loginForm[loginForm.verifyType] }"
              >
                <template #prefix>
                  <el-icon class="input-icon" v-if="loginForm.verifyType === 'email'"><Message /></el-icon>
                  <el-icon class="input-icon" v-else><Iphone /></el-icon>
                </template>
              </el-input>
              <label class="floating-label">{{ loginForm.verifyType === 'email' ? '邮箱' : '手机号' }}</label>
            </el-form-item>

            <!-- 验证码 -->
            <el-form-item prop="code" class="verify-code-form-item floating-label-form-item">
              <div class="captcha-container verify-code-container">
                <el-input
                  v-model="loginForm.code"
                  class="auth-input captcha-input verification-input"
                  placeholder=" "
                  maxlength="6"
                  :class="{ 'is-filled': loginForm.code }"
                >
                  <template #prefix>
                    <el-icon class="input-icon"><Key /></el-icon>
                  </template>
                </el-input>
                <label class="floating-label">验证码</label>
                <el-button
                  type="primary"
                  class="code-btn"
                  :disabled="codeTimer > 0"
                  @click="handleSendCode"
                >
                  {{ codeTimer > 0 ? `${codeTimer}秒后重试` : '获取验证码' }}
                </el-button>
              </div>
            </el-form-item>
          </template>

          <!-- 用户协议 -->
          <el-form-item prop="agreement" class="agreement-checkbox">
            <el-checkbox v-model="loginForm.agreement">
              我已阅读并同意
              <el-link type="primary" @click.stop="showAgreement">《用户协议》</el-link>
              和
              <el-link type="primary" @click.stop="showPrivacy">《隐私政策》</el-link>
            </el-checkbox>
          </el-form-item>

          <!-- 记住用户名和忘记密码 -->
          <div class="login-options">
            <el-checkbox v-model="rememberUsername" v-if="loginType === 'account'" class="google-style-checkbox">
              记住我
            </el-checkbox>
            <!-- TODO: 当升级到 Element Plus 3.0.0 时，将 type="text" 改为 type="link" -->
            <el-button type="text" @click="showForgotPassword" class="forgot-link">
              忘记密码?
            </el-button>
          </div>

          <!-- 登录按钮 -->
          <div class="button-container">
            <!-- TODO: 当升级到 Element Plus 3.0.0 时，将 type="text" 改为 type="link" -->
            <el-button
              type="text"
              @click="isLogin = false"
              class="switch-mode-btn"
            >
              注册
            </el-button>
            <el-button
              type="primary"
              native-type="submit"
              :loading="loading"
              class="next-button"
            >
              登录
            </el-button>
          </div>
        </el-form>
      </div>

      <!-- 注册表单 -->
      <div v-else class="form-wrapper">
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          class="auth-form"
          autocomplete="off"
          @submit.prevent="handleRegister"
        >
          <!-- 用户名 -->
          <el-form-item prop="username" class="floating-label-form-item">
            <el-input
              v-model="registerForm.username"
              class="auth-input"
              placeholder=" "
              autocomplete="username"
              :class="{ 'is-filled': registerForm.username }"
            >
              <template #prefix>
                <el-icon class="input-icon"><User /></el-icon>
              </template>
            </el-input>
            <label class="floating-label">用户名</label>
          </el-form-item>

          <!-- 密码 -->
          <el-form-item prop="password" class="floating-label-form-item">
            <el-input
              v-model="registerForm.password"
              type="password"
              class="auth-input"
              placeholder=" "
              autocomplete="new-password"
              show-password
              :class="{ 'is-filled': registerForm.password }"
            >
              <template #prefix>
                <el-icon class="input-icon"><Lock /></el-icon>
              </template>
            </el-input>
            <label class="floating-label">密码</label>
          </el-form-item>

          <!-- 确认密码 -->
          <el-form-item prop="confirmPassword" class="floating-label-form-item">
            <el-input
              v-model="registerForm.confirmPassword"
              type="password"
              class="auth-input"
              placeholder=" "
              autocomplete="new-password"
              show-password
              :class="{ 'is-filled': registerForm.confirmPassword }"
            >
              <template #prefix>
                <el-icon class="input-icon"><Lock /></el-icon>
              </template>
            </el-input>
            <label class="floating-label">确认密码</label>
          </el-form-item>

          <!-- 验证方式选择器 (简化为单选按钮) -->
          <el-form-item prop="verifyType" class="verify-type-radio">
            <el-radio-group v-model="registerForm.verifyType" size="large">
              <el-radio label="email">邮箱注册</el-radio>
              <el-radio label="phone">手机注册</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 邮箱 -->
          <el-form-item
            v-if="registerForm.verifyType === 'email'"
            prop="email"
            class="floating-label-form-item"
          >
            <el-input
              v-model="registerForm.email"
              class="auth-input"
              placeholder=" "
              autocomplete="email"
              :class="{ 'is-filled': registerForm.email }"
            >
              <template #prefix>
                <el-icon class="input-icon"><Message /></el-icon>
              </template>
            </el-input>
            <label class="floating-label">邮箱</label>
          </el-form-item>

          <!-- 手机号 -->
          <el-form-item
            v-if="registerForm.verifyType === 'phone'"
            prop="phone"
            class="floating-label-form-item"
          >
            <el-input
              v-model="registerForm.phone"
              class="auth-input"
              placeholder=" "
              autocomplete="tel"
              :class="{ 'is-filled': registerForm.phone }"
            >
              <template #prefix>
                <el-icon class="input-icon"><Phone /></el-icon>
              </template>
            </el-input>
            <label class="floating-label">手机号</label>
          </el-form-item>

          <!-- 验证码 -->
          <el-form-item prop="code" class="verify-code-form-item floating-label-form-item">
            <div class="captcha-container verify-code-container">
              <el-input
                v-model="registerForm.code"
                class="auth-input captcha-input verification-input"
                placeholder=" "
                autocomplete="new-code"
                maxlength="6"
                :class="{ 'is-filled': registerForm.code }"
              >
                <template #prefix>
                  <el-icon class="input-icon"><Key /></el-icon>
                </template>
              </el-input>
              <label class="floating-label">验证码</label>
              <el-button
                type="primary"
                class="code-btn"
                :disabled="registerCodeTimer > 0"
                @click="handleSendRegisterCode"
              >
                {{ registerCodeTimer > 0 ? `${registerCodeTimer}秒后重试` : '获取验证码' }}
              </el-button>
            </div>
          </el-form-item>

          <!-- 用户协议 -->
          <el-form-item prop="agreement" class="agreement-checkbox">
            <el-checkbox v-model="registerForm.agreement">
              我已阅读并同意
              <el-link type="primary" @click.stop="showAgreement">《用户协议》</el-link>
              和
              <el-link type="primary" @click.stop="showPrivacy">《隐私政策》</el-link>
            </el-checkbox>
          </el-form-item>

          <!-- 注册按钮和登录提示 -->
          <div class="button-container">
            <!-- TODO: 当升级到 Element Plus 3.0.0 时，将 type="text" 改为 type="link" -->
            <el-button
              type="text"
              @click="isLogin = true"
              class="switch-mode-btn"
            >
              已有账号？登录
            </el-button>
            <el-button
              type="primary"
              native-type="submit"
              class="next-button"
              :loading="registerLoading"
            >
              注册
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useUserStore } from '../stores'
import { ElMessage } from 'element-plus'
// 导入所需图标
import {
  Loading,
  User,
  Lock,
  Message,
  Key,
  Phone,
  Iphone
} from '@element-plus/icons-vue'
import { getCaptcha, sendEmailCode, sendPhoneCode, login, register } from '../api/user'
import { initOnlineStatusMonitor } from '../utils/online-status'
import { initWebSocketClient } from '../utils/websocket'

// 定义属性和事件
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  defaultTab: {
    type: String,
    default: 'login' // 'login' 或 'register'
  }
})

const emit = defineEmits(['update:visible', 'login-success', 'register-success', 'close'])

// 对话框状态
const dialogVisible = ref(props.visible)
const isLogin = ref(props.defaultTab === 'login')

// 监听 visible 属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && loginType.value === 'account') {
    refreshCaptcha()
  }
})

// 监听对话框状态变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 监听登录/注册切换
watch(isLogin, () => {
  if (isLogin.value && loginType.value === 'account') {
    refreshCaptcha()
  }
})

// 简单的防抖函数
function debounce(fn, delay = 300) {
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 用户存储
const userStore = useUserStore()

// 登录相关状态
const loginFormRef = ref(null)
const loading = ref(false)
const loginType = ref('account')
const captchaUrl = ref('')
const codeTimer = ref(0)
const rememberUsername = ref(localStorage.getItem('rememberUsername') === 'true')
let codeTimerInstance = null

// 初始化登录表单
const loginForm = reactive({
  username: rememberUsername.value ? localStorage.getItem('rememberedUsername') || '' : '',
  password: '',
  verifyCode: '',
  verifyType: 'email',
  email: '',
  phone: '',
  code: '',
  agreement: true // 默认勾选，因为用户在注册时已经同意过
})

// 登录表单验证规则
const loginRules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应在6-20个字符之间', trigger: 'blur' }
  ],
  verifyCode: [
    { required: true, message: '请输入图形验证码', trigger: 'blur' }
  ],
  verifyType: [
    { required: true, message: '请选择登录方式', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度应为6位', trigger: 'blur' }
  ],
  agreement: [
    { required: true, message: '请阅读并同意用户协议和隐私政策', trigger: 'change' }
  ]
})

// 注册相关状态
const registerFormRef = ref(null)
const registerLoading = ref(false)
const registerCodeTimer = ref(0)
let registerCodeTimerInstance = null

// 初始化注册表单
const registerForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  verifyType: 'email',
  email: '',
  phone: '',
  code: '',
  agreement: false
})

// 注册表单验证规则
const registerRules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (_, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  verifyType: [
    { required: true, message: '请选择注册方式', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ],
  agreement: [
    {
      validator: (_, value, callback) => {
        if (!value) {
          callback(new Error('请阅读并同意用户协议和隐私政策'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
})

// 获取图形验证码的实际实现
const doRefreshCaptcha = async () => {
  try {
    captchaUrl.value = '' // 清空当前验证码，显示加载状态
    console.log('开始获取图形验证码...')

    // 使用封装的API请求
    const res = await getCaptcha()
    console.log('验证码响应:', res)

    if (res && res.code === 200 && res.data) {
      // 处理返回的验证码数据
      const captchaData = res.data

      // 存储验证码ID
      if (captchaData.captchaId) {
        localStorage.setItem('captchaId', captchaData.captchaId)
        console.log('验证码ID已保存:', captchaData.captchaId)
      } else {
        console.warn('响应中没有验证码ID')
      }

      // 处理验证码图像
      if (captchaData.image) {
        // 如果是对象格式，使用image属性
        captchaUrl.value = `data:image/png;base64,${captchaData.image}`
        console.log('验证码图像已设置，长度:', captchaData.image.length)
      } else if (typeof captchaData === 'string') {
        // 如果是字符串格式，直接使用
        captchaUrl.value = `data:image/png;base64,${captchaData}`
        console.log('验证码图像已设置，长度:', captchaData.length)
      } else {
        console.error('不支持的验证码数据格式:', captchaData)
        throw new Error('验证码数据格式不支持')
      }

      console.log('验证码获取成功，URL长度:', captchaUrl.value.length)
    } else {
      console.error('验证码响应错误:', res)
      throw new Error(res?.message || '获取验证码失败')
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    if (error.response) {
      console.error('验证码请求响应状态:', error.response.status)
      console.error('验证码请求响应数据:', error.response.data)
    }
    ElMessage.error({
      message: '获取验证码失败，点击重试',
      duration: 2000
    })
  }
}

// 使用防抖包装验证码刷新函数
const refreshCaptcha = debounce(doRefreshCaptcha, 500)

// 发送登录验证码
const handleSendCode = async () => {
  // 验证表单
  try {
    // 验证登录方式
    if (!loginForm.verifyType) {
      ElMessage.warning('请选择登录方式')
      return
    }

    // 验证邮箱或手机号
    const field = loginForm.verifyType
    const value = loginForm[field]
    if (!value) {
      ElMessage.warning(`请先输入${field === 'email' ? '邮箱' : '手机号'}`)
      return
    }

    // 验证用户协议
    if (!loginForm.agreement) {
      ElMessage.warning('请阅读并同意用户协议和隐私政策')
      return
    }

    // 验证邮箱格式
    if (field === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      ElMessage.warning('请输入正确的邮箱格式')
      return
    }

    // 验证手机号格式
    if (field === 'phone' && !/^1[3-9]\d{9}$/.test(value)) {
      ElMessage.warning('请输入正确的手机号格式')
      return
    }

    const api = field === 'email' ? sendEmailCode : sendPhoneCode
    await api({
      [field]: value,
      isRegister: false
    })
    ElMessage.success('验证码已发送')
    codeTimer.value = 60
    codeTimerInstance = setInterval(() => {
      if (codeTimer.value > 0) {
        codeTimer.value--
      } else {
        clearInterval(codeTimerInstance)
      }
    }, 1000)
  } catch (error) {
    ElMessage.error('发送验证码失败')
  }
}

// 发送注册验证码
const handleSendRegisterCode = async () => {
  // 验证表单
  try {
    // 验证用户名和密码
    if (!registerForm.username || !registerForm.password || !registerForm.confirmPassword) {
      ElMessage.warning('请先填写用户名和密码')
      return
    }

    // 验证密码一致性
    if (registerForm.password !== registerForm.confirmPassword) {
      ElMessage.warning('两次输入的密码不一致')
      return
    }

    // 验证注册方式
    if (!registerForm.verifyType) {
      ElMessage.warning('请选择注册方式')
      return
    }

    // 验证邮箱或手机号是否已填写
    const field = registerForm.verifyType
    const value = registerForm[field]
    if (!value) {
      ElMessage.warning(`请先输入${field === 'email' ? '邮箱' : '手机号'}`)
      return
    }

    // 验证邮箱格式
    if (field === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      ElMessage.warning('请输入正确的邮箱格式')
      return
    }

    // 验证手机号格式
    if (field === 'phone' && !/^1[3-9]\d{9}$/.test(value)) {
      ElMessage.warning('请输入正确的手机号格式')
      return
    }

    // 验证用户协议
    if (!registerForm.agreement) {
      ElMessage.warning('请阅读并同意用户协议和隐私政策')
      return
    }

    // 验证邮箱格式
    if (field === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      ElMessage.warning('请输入正确的邮箱格式')
      return
    }

    // 验证手机号格式
    if (field === 'phone' && !/^1[3-9]\d{9}$/.test(value)) {
      ElMessage.warning('请输入正确的手机号格式')
      return
    }

    // 构建验证码请求数据
    const verifyCodeData = {
      isRegister: true // 注册场景
    }

    // 根据注册方式设置邮箱或手机号
    if (field === 'email') {
      verifyCodeData.email = value
      verifyCodeData.phone = null
    } else {
      verifyCodeData.phone = value
      verifyCodeData.email = null
    }

    const api = field === 'email' ? sendEmailCode : sendPhoneCode
    await api(verifyCodeData)

    ElMessage.success('验证码已发送')
    registerCodeTimer.value = 60
    registerCodeTimerInstance = setInterval(() => {
      if (registerCodeTimer.value > 0) {
        registerCodeTimer.value--
      } else {
        clearInterval(registerCodeTimerInstance)
      }
    }, 1000)
  } catch (error) {
    ElMessage.error('发送验证码失败')
  }
}

// 显示用户协议
const showAgreement = () => {
  ElMessage.info('用户协议内容')
}

// 显示隐私政策
const showPrivacy = () => {
  ElMessage.info('隐私政策内容')
}

// 显示忘记密码
const showForgotPassword = () => {
  ElMessage.info('请通过验证码登录，或联系管理员重置密码')
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()

    // 验证用户协议
    if (!loginForm.agreement) {
      ElMessage.warning('请阅读并同意用户协议和隐私政策')
      return
    }

    // 根据登录类型进行额外验证
    if (loginType.value === 'account') {
      // 验证账号密码登录的必要字段
      if (!loginForm.username) {
        ElMessage.warning('请输入用户名')
        return
      }
      if (loginForm.username.length < 3 || loginForm.username.length > 20) {
        ElMessage.warning('用户名长度应在3-20个字符之间')
        return
      }
      if (!loginForm.password) {
        ElMessage.warning('请输入密码')
        return
      }
      if (loginForm.password.length < 6 || loginForm.password.length > 20) {
        ElMessage.warning('密码长度应在6-20个字符之间')
        return
      }
      if (!loginForm.verifyCode) {
        ElMessage.warning('请输入图形验证码')
        return
      }

      // 验证验证码ID
      const captchaId = localStorage.getItem('captchaId')
      if (!captchaId) {
        ElMessage.warning('验证码已过期，请刷新验证码')
        refreshCaptcha()
        return
      }
    } else {
      // 验证验证码登录的必要字段
      if (!loginForm.verifyType) {
        ElMessage.warning('请选择登录方式')
        return
      }

      const field = loginForm.verifyType
      const value = loginForm[field]

      if (!value) {
        ElMessage.warning(`请先输入${field === 'email' ? '邮箱' : '手机号'}`)
        return
      }

      // 验证邮箱格式
      if (field === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        ElMessage.warning('请输入正确的邮箱格式')
        return
      }

      // 验证手机号格式
      if (field === 'phone' && !/^1[3-9]\d{9}$/.test(value)) {
        ElMessage.warning('请输入正确的手机号格式')
        return
      }

      if (!loginForm.code) {
        ElMessage.warning('请输入验证码')
        return
      }

      // 验证验证码长度
      if (loginForm.code.length !== 6) {
        ElMessage.warning('验证码长度应为6位')
        return
      }
    }

    loading.value = true

    // 根据登录类型构建登录数据
    const loginData = {
      ...loginForm,
      verifyCode: loginType.value === 'account' ? loginForm.verifyCode : undefined,
      code: loginType.value === 'verify' ? loginForm.code : undefined
    }

    // 添加验证码ID（如果是账号密码登录）
    if (loginType.value === 'account') {
      // 从localStorage获取验证码ID
      const captchaId = localStorage.getItem('captchaId')
      if (!captchaId) {
        ElMessage.warning('验证码已过期，请刷新验证码')
        refreshCaptcha()
        return
      }
      loginData.captchaId = captchaId
    }

    // 清除不需要的字段
    if (loginType.value === 'account') {
      delete loginData.email
      delete loginData.phone
      delete loginData.code
      delete loginData.verifyType
    } else {
      delete loginData.username
      delete loginData.password
      delete loginData.verifyCode
      delete loginData.captchaId
      if (loginForm.verifyType === 'email') {
        delete loginData.phone
      } else {
        delete loginData.email
      }
    }

    // 移除 agreement 字段，后端不需要这个字段
    delete loginData.agreement

    console.log('发送登录请求数据:', JSON.stringify(loginData))

    const res = await login(loginData)

    if (res && res.code === 200) {
      // 根据后端返回的数据格式调整
      if (!res.data) {
        throw new Error('登录响应数据格式错误：缺少返回数据')
      }

      // 检查返回数据格式
      console.log('登录响应数据:', res.data)

      // 获取token和用户信息
      const token = res.data.token
      const backendUserInfo = res.data.userInfo

      if (!token) {
        throw new Error('登录响应数据格式错误：缺少token')
      }

      if (!backendUserInfo) {
        throw new Error('登录响应数据格式错误：缺少用户信息')
      }

      // 设置token
      userStore.setToken(token)

      // 确保角色信息正确处理
      let roles = []
      if (backendUserInfo.role) {
        // 如果role是字符串，转换为数组
        roles = [backendUserInfo.role]
      }

      // 构建用户信息对象
      const userInfo = {
        id: backendUserInfo.id,
        userId: backendUserInfo.id,
        username: backendUserInfo.username,
        roles: roles,
        role: backendUserInfo.role,
        avatar: backendUserInfo.avatar || '',
        email: backendUserInfo.email || '',
        phone: backendUserInfo.phone || ''
      }

      // 更新store中的用户信息
      userStore.setUser(userInfo)

      // 保存用户信息到本地存储
      localStorage.setItem('userInfo', JSON.stringify(userInfo))

      // 触发全局用户登录事件
      window.dispatchEvent(new CustomEvent('user-login', {
        detail: userInfo
      }))

      // 如果选择了记住用户名，保存用户名
      if (rememberUsername.value && loginType.value === 'account') {
        localStorage.setItem('rememberedUsername', loginForm.username)
      }

      // 初始化WebSocket和在线状态监听
      initWebSocketClient(token)
      initOnlineStatusMonitor()

      ElMessage.success('登录成功')

      // 关闭对话框
      dialogVisible.value = false

      // 触发登录成功事件
      emit('login-success', userInfo)
    } else {
      throw new Error(res?.message || '登录失败')
    }
  } catch (error) {
    console.error('登录失败:', error)

    // 更详细的错误处理
    if (error.response) {
      // 服务器返回了错误状态码
      console.error('响应状态码:', error.response.status)
      console.error('响应数据:', error.response.data)

      if (error.response.data && error.response.data.message) {
        // 根据错误消息提供更具体的反馈
        const errorMsg = error.response.data.message;

        // 处理常见错误
        if (errorMsg.includes('验证码')) {
          // 验证码相关错误
          ElMessage.error({
            message: errorMsg,
            duration: 2000
          });
          // 自动刷新验证码
          refreshCaptcha();
        } else if (errorMsg.includes('用户名') || errorMsg.includes('密码')) {
          // 用户名或密码错误
          ElMessage.error({
            message: errorMsg,
            duration: 2000
          });
          // 清空密码字段
          loginForm.password = '';
          // 自动刷新验证码
          refreshCaptcha();
        } else {
          // 其他错误
          ElMessage.error({
            message: errorMsg,
            duration: 2000
          });
        }
      } else if (error.response.status === 400) {
        // 对于400错误，可能是验证码错误或参数格式错误
        if (loginType.value === 'account') {
          ElMessage.error({
            message: '登录失败，请检查用户名、密码和验证码',
            duration: 2000
          });
          // 自动刷新验证码
          refreshCaptcha();
        } else {
          ElMessage.error({
            message: '登录失败，请检查邮箱/手机号和验证码',
            duration: 2000
          });
        }
      } else if (error.response.status === 401) {
        ElMessage.error({
          message: '用户名或密码错误',
          duration: 2000
        });
        // 清空密码字段
        loginForm.password = '';
        // 自动刷新验证码
        refreshCaptcha();
      } else {
        ElMessage.error({
          message: `服务器错误 (${error.response.status})`,
          duration: 2000
        });
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('未收到响应:', error.request)
      ElMessage.error({
        message: '服务器无响应，请检查网络连接',
        duration: 2000
      });
    } else {
      // 请求设置时发生错误
      console.error('请求错误:', error.message)
      ElMessage.error({
        message: error.message || '登录失败，请稍后重试',
        duration: 2000
      });
    }

    // 刷新验证码
    if (loginType.value === 'account') {
      refreshCaptcha()
    }
  } finally {
    loading.value = false
  }
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    registerLoading.value = true
    await registerFormRef.value.validate()

    // 构建注册数据
    const registerData = {
      username: registerForm.username,
      password: registerForm.password,
      confirmPassword: registerForm.confirmPassword,
      code: registerForm.code
    }

    // 根据注册方式添加邮箱或手机号
    if (registerForm.verifyType === 'email') {
      registerData.email = registerForm.email
    } else {
      registerData.phone = registerForm.phone
    }

    const res = await register(registerData)

    // 检查是否返回了token和用户信息（注册即登录）
    if (res && res.code === 200 && res.data) {
      // 检查返回数据格式
      console.log('注册响应数据:', res.data)

      // 获取token和用户信息
      const token = res.data.token
      const backendUserInfo = res.data.userInfo

      if (!token) {
        throw new Error('注册响应数据格式错误：缺少token')
      }

      if (!backendUserInfo) {
        throw new Error('注册响应数据格式错误：缺少用户信息')
      }

      // 设置token
      userStore.setToken(token)

      // 确保角色信息正确处理
      let roles = []
      if (backendUserInfo.role) {
        // 如果role是字符串，转换为数组
        roles = [backendUserInfo.role]
      }

      // 构建用户信息对象
      const userInfo = {
        id: backendUserInfo.id,
        userId: backendUserInfo.id,
        username: backendUserInfo.username,
        roles: roles,
        role: backendUserInfo.role,
        avatar: backendUserInfo.avatar || '',
        email: backendUserInfo.email || '',
        phone: backendUserInfo.phone || ''
      }

      // 保存用户信息到本地存储
      localStorage.setItem('userInfo', JSON.stringify(userInfo))

      // 更新store中的用户信息
      userStore.setUser(userInfo)

      // 触发全局用户登录事件
      window.dispatchEvent(new CustomEvent('user-login', {
        detail: userInfo
      }))

      // 初始化WebSocket和在线状态监听
      initWebSocketClient(token)
      initOnlineStatusMonitor()

      ElMessage.success('注册成功并已自动登录')

      // 关闭对话框
      dialogVisible.value = false

      // 触发登录成功事件
      emit('login-success', userInfo)
    } else {
      // 如果没有返回token，则按照原来的逻辑处理
      ElMessage.success({
        message: '注册成功，请登录',
        duration: 2000
      })

      // 切换到登录页
      isLogin.value = true

      // 如果是邮箱注册，自动填充邮箱
      if (registerForm.verifyType === 'email') {
        loginForm.verifyType = 'email'
        loginForm.email = registerForm.email
        loginType.value = 'verify'
      }
      // 如果是手机注册，自动填充手机号
      else if (registerForm.verifyType === 'phone') {
        loginForm.verifyType = 'phone'
        loginForm.phone = registerForm.phone
        loginType.value = 'verify'
      }

      // 触发注册成功事件
      emit('register-success')
    }
  } catch (error) {
    console.error('注册失败:', error)
    // 重置验证码计时器
    registerCodeTimer.value = 0
    if (registerCodeTimerInstance) {
      clearInterval(registerCodeTimerInstance)
    }

    // 更详细的错误处理
    if (error.response) {
      // 服务器返回了错误状态码
      console.error('响应状态码:', error.response.status)
      console.error('响应数据:', error.response.data)

      if (error.response.data && error.response.data.message) {
        // 根据错误消息提供更具体的反馈
        const errorMsg = error.response.data.message;

        // 处理常见错误
        if (errorMsg.includes('验证码')) {
          // 验证码相关错误
          ElMessage.error({
            message: errorMsg,
            duration: 2000
          });
        } else if (errorMsg.includes('用户名')) {
          // 用户名相关错误
          ElMessage.error({
            message: errorMsg,
            duration: 2000
          });
          // 清空用户名字段
          registerForm.username = '';
        } else if (errorMsg.includes('密码')) {
          // 密码相关错误
          ElMessage.error({
            message: errorMsg,
            duration: 2000
          });
          // 清空密码字段
          registerForm.password = '';
          registerForm.confirmPassword = '';
        } else if (errorMsg.includes('邮箱')) {
          // 邮箱相关错误
          ElMessage.error({
            message: errorMsg,
            duration: 2000
          });
          // 清空邮箱字段
          registerForm.email = '';
        } else if (errorMsg.includes('手机')) {
          // 手机相关错误
          ElMessage.error({
            message: errorMsg,
            duration: 2000
          });
          // 清空手机字段
          registerForm.phone = '';
        } else {
          // 其他错误
          ElMessage.error({
            message: errorMsg,
            duration: 2000
          });
        }
      } else if (error.response.status === 400) {
        // 对于400错误，可能是参数格式错误
        ElMessage.error({
          message: '注册失败，请检查输入信息是否正确',
          duration: 2000
        });
      } else {
        ElMessage.error({
          message: `服务器错误 (${error.response.status})`,
          duration: 2000
        });
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('未收到响应:', error.request)
      ElMessage.error({
        message: '服务器无响应，请检查网络连接',
        duration: 2000
      });
    } else {
      // 请求设置时发生错误
      console.error('请求错误:', error.message)
      ElMessage.error({
        message: error.message || '注册失败，请稍后重试',
        duration: 2000
      });
    }
  } finally {
    registerLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  // 清理计时器
  if (codeTimerInstance) {
    clearInterval(codeTimerInstance)
    codeTimerInstance = null
  }
  if (registerCodeTimerInstance) {
    clearInterval(registerCodeTimerInstance)
    registerCodeTimerInstance = null
  }

  // 重置表单
  if (loginFormRef.value) {
    // 保存当前的 agreement 值
    const currentAgreement = loginForm.agreement;

    // 重置表单
    loginFormRef.value.resetFields();

    // 恢复 agreement 值
    loginForm.agreement = currentAgreement;
  }
  if (registerFormRef.value) {
    registerFormRef.value.resetFields();
  }

  // 触发关闭事件
  emit('close')
}

// 组件挂载时
onMounted(() => {
  if (dialogVisible.value && loginType.value === 'account') {
    refreshCaptcha()
  }
})

// 组件卸载时
onUnmounted(() => {
  if (codeTimerInstance) {
    clearInterval(codeTimerInstance)
    codeTimerInstance = null
  }
  if (registerCodeTimerInstance) {
    clearInterval(registerCodeTimerInstance)
    registerCodeTimerInstance = null
  }
})
</script>

<style scoped>
/* 全局字体样式 */
.auth-container {
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, sans-serif;
}

/* 谷歌风格对话框 */
:deep(.el-dialog) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 24px 38px 3px rgba(0, 0, 0, 0.14),
              0 9px 46px 8px rgba(0, 0, 0, 0.12),
              0 11px 15px -7px rgba(0, 0, 0, 0.2);
  max-width: 448px;
  width: 100%;
  height: auto;
  max-height: 90vh; /* 限制最大高度为视口高度的90% */
  transition: all 0.3s;
  margin-top: 5vh !important; /* 调整顶部边距，确保对话框在视口中居中 */
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 0;
}

:deep(.el-dialog__headerbtn) {
  display: none;
}

:deep(.el-dialog__body) {
  padding: 0;
  overflow-y: auto; /* 允许内容滚动 */
  max-height: calc(90vh - 50px); /* 减去一些空间给标题和边距 */
}

/* 关闭按钮 */
.dialog-close-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s;
}

.dialog-close-btn:hover {
  background-color: #f1f3f4;
}

.dialog-close-btn .el-icon {
  font-size: 16px;
  color: #5f6368;
}

/* 主容器样式 */
.auth-container {
  padding: 28px 40px 32px;
  background-color: #fff;
  box-sizing: border-box;
}

/* 头部样式 */
.auth-header {
  margin-bottom: 16px; /* 减少底部间距 */
}

/* 头部内容布局 */
.header-content {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 改为左对齐 */
  gap: 12px;
  padding-left: 10px; /* 添加少量左侧内边距 */
  max-width: 380px; /* 限制最大宽度 */
  margin: 0 auto; /* 居中显示 */
}

/* Logo容器 */
.logo-container {
  display: flex;
  align-items: center;
}

.system-logo {
  width: 160px; /* 增大logo宽度 */
  height: 64px; /* 增大logo高度 */
  object-fit: contain;
  border-radius: 8px; /* 增加圆角 */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)); /* 增强阴影效果 */
  transition: transform 0.3s ease, filter 0.3s ease; /* 添加过渡效果 */
}

.system-logo:hover {
  transform: scale(1.03); /* 悬停时轻微放大 */
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.15)); /* 悬停时增强阴影 */
}

/* 标题容器 */
.title-container {
  text-align: left;
}

.auth-title {
  font-size: 24px; /* 增大字体 */
  color: #202124;
  margin-bottom: 4px;
  font-weight: 600; /* 增加字体粗细 */
  line-height: 1.2;
  letter-spacing: 0.5px; /* 增加字间距 */
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, sans-serif; /* 使用更美观的字体 */
}

.auth-subtitle {
  font-size: 14px; /* 减小字体 */
  color: #5f6368;
  font-weight: 400;
  line-height: 1.4;
  opacity: 0.9;
}

/* 移动端登录/注册切换 */
.auth-toggle-mobile {
  display: none;
  margin-bottom: 24px;
  text-align: center;
}

.auth-toggle-mobile span {
  display: inline-block;
  padding: 8px 12px;
  margin: 0 4px;
  color: #5f6368;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.auth-toggle-mobile span.active {
  color: #3b78e7; /* 更柔和的蓝色 */
  font-weight: 500;
}

.auth-toggle-mobile span:hover:not(.active) {
  background-color: #f1f3f4;
}

/* 登录方式切换 */
.login-type-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #dadce0;
  position: relative;
}

.login-type-tab {
  flex: 1;
  padding: 14px 0;
  text-align: center;
  cursor: pointer;
  color: #5f6368;
  transition: all 0.25s;
  position: relative;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  letter-spacing: 0.25px;
}

.login-type-tab.active {
  color: #3b78e7; /* 更柔和的蓝色 */
  font-weight: 500;
}

.login-type-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #3b78e7; /* 更柔和的蓝色 */
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  transition: all 0.25s ease;
}

.login-type-tab:hover:not(.active) {
  background-color: rgba(74, 138, 244, 0.04); /* 更柔和的蓝色 */
  color: #4a8af4; /* 更柔和的蓝色 */
}

.login-type-tab .el-icon {
  font-size: 18px;
  margin-top: -2px;
}

/* 表单样式 */
.form-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 320px;
  opacity: 0;
  animation: fadeInForm 0.35s ease-out forwards;
  overflow-y: auto; /* 添加垂直滚动条，以防内容过多 */
}

@keyframes fadeInForm {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-form {
  margin-bottom: 16px;
}

/* 浮动标签表单项 */
.floating-label-form-item {
  position: relative;
  margin-bottom: 16px; /* 调整表单项之间的间距 */
}

.auth-input :deep(.el-input__wrapper) {
  height: 42px; /* 进一步减小输入框高度 */
  border-radius: 6px;
  transition: all 0.25s;
  box-shadow: none;
  border: 1px solid #dadce0;
  padding: 0 16px;
  background-color: #f8f9fa;
}

/* 确保输入框内的文本不会与图标重叠 */
.auth-input :deep(.el-input__inner) {
  padding-left: 38px; /* 增加左侧内边距 */
  font-size: 15px;
  color: #202124;
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, sans-serif; /* 使用更美观的字体 */
}

/* 调整输入框前缀图标容器的样式 */
.auth-input :deep(.el-input__prefix) {
  left: 10px; /* 调整图标位置 */
}

/* 密码输入框样式优化 */
.auth-input :deep(.el-input__password-icon) {
  color: #5f6368;
  font-size: 16px;
  margin-right: 4px;
}

.auth-input :deep(.el-input__password-icon:hover) {
  color: #3b78e7; /* 更柔和的蓝色 */
}

/* 密码显示样式优化 */
.auth-input :deep(.el-input__inner) {
  letter-spacing: 1px;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.auth-input :deep(.el-input__wrapper:hover) {
  border-color: #d2e3fc;
  background-color: #fff;
}

.auth-input :deep(.el-input__wrapper.is-focus) {
  border-color: #3b78e7; /* 更柔和的蓝色 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  background-color: #fff;
}

.floating-label {
  position: absolute;
  left: 58px; /* 增加左侧距离，避免与图标重叠 */
  top: 11px; /* 调整垂直位置以适应新的输入框高度 */
  color: #5f6368;
  font-size: 14px; /* 减小字体大小 */
  transition: all 0.25s ease;
  pointer-events: none;
  background: #f8f9fa;
  padding: 0 4px;
  z-index: 1;
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, sans-serif; /* 使用更美观的字体 */
}

.auth-input:hover + .floating-label {
  background: #fff;
}

.auth-input:focus-within + .floating-label,
.auth-input.is-filled + .floating-label {
  top: -9px;
  left: 12px;
  font-size: 12px;
  color: #3b78e7; /* 更柔和的蓝色 */
  background: #fff;
  font-weight: 500;
}

/* 验证码输入框聚焦时的浮动标签位置 */
.captcha-input-wrapper .auth-input:focus-within + .floating-label,
.captcha-input-wrapper .auth-input.is-filled + .floating-label {
  top: -8px;
  left: 12px;
  font-size: 11px;
}

.input-icon {
  color: #5f6368;
  font-size: 18px;
  margin-right: 12px; /* 增加右侧边距 */
  margin-left: 4px; /* 增加左侧边距 */
}

.auth-input:focus-within :deep(.el-input__prefix) .input-icon {
  color: #3b78e7; /* 更柔和的蓝色 */
}

/* 验证码行容器 */
.captcha-row {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 0;
}

.captcha-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 验证码容器样式 */
.captcha-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
  height: 40px;
  box-sizing: border-box;
}

.graphic-captcha-container {
  height: 40px;
  display: flex;
  align-items: center;
  position: relative;
}

.captcha-input {
  flex: 1;
  max-width: 180px;
  height: 40px;
}

/* 验证码输入框样式优化 */
.captcha-input :deep(.el-input__inner) {
  letter-spacing: 2px;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  padding-left: 38px;
  font-size: 15px;
  height: 40px;
  border-radius: 4px;
  line-height: 40px;
}

/* 验证码输入框特殊样式 */
.verification-input {
  width: 100%;
  max-width: 190px;
  margin-right: 10px;
}

.verification-input :deep(.el-input__inner) {
  letter-spacing: 1px;
  font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
  font-weight: 500;
  padding-left: 38px;
  font-size: 14px;
  height: 40px;
}

.captcha-img-container {
  width: 110px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 1px solid #dcdfe6;
  transition: all 0.25s ease;
  margin: 0;
  flex-shrink: 0;
  flex-grow: 0;
  box-shadow: none;
  position: relative;
  z-index: 1;
  padding: 0;
  box-sizing: border-box;
  line-height: 0;
}

.captcha-img-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(240, 245, 255, 0.5) 0%, rgba(230, 240, 255, 0.5) 100%);
  z-index: -1;
  opacity: 0.8;
  transition: opacity 0.25s ease;
}

.captcha-img-container:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* 增强阴影 */
  transform: translateY(-1px);
}

.captcha-img-container:hover::before {
  opacity: 0.4; /* 悬停时降低渐变透明度 */
}

.captcha-img {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 使用contain确保完整显示 */
  transform: scale(0.95); /* 稍微缩小以确保边缘不被裁剪 */
  image-rendering: -webkit-optimize-contrast; /* 提高图像清晰度 */
  image-rendering: crisp-edges; /* 提高图像清晰度 */
  display: block; /* 确保图片作为块级元素 */
  margin: 0 auto; /* 水平居中 */
}

.captcha-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #1a73e8;
  font-size: 18px;
  background-color: #f8f9fa;
}

/* 验证方式单选按钮 */
.verify-type-radio {
  margin-bottom: 16px; /* 减少底部间距 */
}

.verify-type-radio :deep(.el-radio-group) {
  display: flex;
  width: 100%;
  gap: 10px;
}

/* 验证码登录专用样式 */
.verify-code-form-item {
  margin-bottom: 16px; /* 调整表单项之间的间距 */
}

.verify-code-form-item :deep(.el-form-item__content) {
  display: flex;
}

.verify-code-container {
  justify-content: space-between;
  align-items: center;
  position: relative;
  width: 100%;
}

/* 确保验证码输入框和按钮高度一致 */
.verify-code-container .el-input,
.verify-code-container .el-button {
  height: 40px;
  line-height: 40px;
}

/* 验证码浮动标签特殊样式 */
.verify-code-form-item .floating-label {
  left: 38px;
}

.verify-code-form-item .el-input.is-filled + .floating-label,
.verify-code-form-item .el-input:focus-within + .floating-label {
  transform: translateY(-20px) scale(0.85);
  color: #3b78e7;
}

.verify-type-radio :deep(.el-radio) {
  margin-right: 0;
  flex: 1;
  display: flex;
  justify-content: center;
  font-size: 15px;
  color: #5f6368;
  border: 1px solid #dadce0;
  border-radius: 6px;
  padding: 10px 0;
  transition: all 0.25s;
  background-color: #f8f9fa;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.verify-type-radio :deep(.el-radio:hover) {
  border-color: #d2e3fc;
  background-color: #fff;
  transform: translateY(-1px);
}

.verify-type-radio :deep(.el-radio__input) {
  margin-right: 8px;
}

.verify-type-radio :deep(.el-radio__inner) {
  border-color: #5f6368;
  transition: all 0.25s;
  width: 16px;
  height: 16px;
}

.verify-type-radio :deep(.el-radio__inner:hover) {
  border-color: #3b78e7; /* 更柔和的蓝色 */
}

.verify-type-radio :deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #3b78e7; /* 更柔和的蓝色 */
  font-weight: 500;
}

.verify-type-radio :deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #3b78e7; /* 更柔和的蓝色 */
  border-color: #3b78e7; /* 更柔和的蓝色 */
}

.verify-type-radio :deep(.el-radio.is-checked) {
  border-color: #3b78e7; /* 更柔和的蓝色 */
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

/* 验证码按钮样式 */
.code-btn {
  width: 105px;
  height: 40px;
  border-radius: 4px;
  font-size: 13px;
  padding: 0;
  background: linear-gradient(135deg, #4a8af4 0%, #3b78e7 100%);
  border: none;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(59, 120, 231, 0.25);
  transition: all 0.25s ease;
  color: white;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  flex-grow: 0;
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, sans-serif;
  box-sizing: border-box;
  line-height: 40px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.code-btn:hover {
  background: linear-gradient(135deg, #5a94f5 0%, #4a8af4 100%); /* 更柔和的悬停渐变 */
  box-shadow: 0 3px 6px rgba(59, 120, 231, 0.3);
  transform: translateY(-1px);
}

.code-btn:disabled {
  background: linear-gradient(135deg, #e8e8e8 0%, #d8d8d8 100%);
  border: none;
  color: #a8a8a8;
  transform: none;
  box-shadow: none;
  opacity: 0.8;
}

/* 添加点击时的波纹效果 */
.code-btn:active:not(:disabled)::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 150%;
  height: 150%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 1;
  animation: ripple 0.6s ease-out;
}

/* 登录选项样式 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 4px 0 12px; /* 减少上下边距 */
  font-size: 15px;
}

.login-options :deep(.el-checkbox) {
  height: auto;
  display: flex;
  align-items: center;
}

.login-options :deep(.el-checkbox__input) {
  vertical-align: middle;
}

.login-options :deep(.el-checkbox__inner) {
  border-color: #5f6368;
  border-radius: 3px;
  transition: all 0.25s;
  width: 16px;
  height: 16px;
}

.login-options :deep(.el-checkbox__inner:hover) {
  border-color: #3b78e7; /* 更柔和的蓝色 */
}

.login-options :deep(.el-checkbox__label) {
  color: #5f6368;
  padding-left: 10px;
  font-size: 15px;
}

.login-options :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #3b78e7; /* 更柔和的蓝色 */
}

.login-options :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #3b78e7; /* 更柔和的蓝色 */
  border-color: #3b78e7; /* 更柔和的蓝色 */
}

.forgot-link {
  color: #3b78e7 !important; /* 更柔和的蓝色 */
  text-decoration: none;
  font-weight: 500;
  font-size: 15px;
  padding: 0;
  transition: all 0.25s;
}

.forgot-link:hover {
  text-decoration: underline;
  background-color: transparent !important;
  color: #4a8af4 !important; /* 更柔和的蓝色 */
}

/* 按钮容器 */
.button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px 0 8px; /* 减少上下边距 */
}

.next-button-wrapper {
  display: inline-block;
}

.next-button {
  min-width: 100px;
  height: 40px;
  font-size: 15px;
  border-radius: 8px;
  /* 使用更柔和的蓝色渐变，与获取验证码按钮保持一致 */
  background: linear-gradient(135deg, #4a8af4 0%, #3b78e7 100%);
  border: none;
  font-weight: 600; /* 增加字体粗细 */
  padding: 0 28px;
  letter-spacing: 0.5px; /* 增加字间距 */
  box-shadow: 0 2px 4px rgba(59, 120, 231, 0.25); /* 更柔和的阴影 */
  transition: all 0.25s ease;
  color: white;
  position: relative;
  overflow: hidden;
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, sans-serif; /* 使用更美观的字体 */
}

.next-button:hover {
  background: linear-gradient(135deg, #5a94f5 0%, #4a8af4 100%); /* 更柔和的悬停渐变 */
  box-shadow: 0 3px 6px rgba(59, 120, 231, 0.3);
  transform: translateY(-1px);
}

.next-button:focus {
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.4), 0 2px 6px rgba(26, 115, 232, 0.3);
  outline: none;
}

/* 添加点击时的波纹效果 */
.next-button:active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 150%;
  height: 150%;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 1;
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

.switch-mode-btn {
  color: #3b78e7; /* 更柔和的蓝色 */
  font-weight: 600; /* 增加字体粗细 */
  background: transparent;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.25s;
  letter-spacing: 0.5px; /* 增加字间距 */
  height: 40px;
  font-size: 15px;
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, sans-serif; /* 使用更美观的字体 */
}

.switch-mode-btn:hover {
  background-color: rgba(74, 138, 244, 0.04); /* 更柔和的蓝色 */
  color: #4a8af4; /* 更柔和的蓝色 */
  transform: translateY(-1px);
}

.switch-mode-btn:focus {
  box-shadow: 0 0 0 2px rgba(74, 138, 244, 0.2); /* 更柔和的蓝色 */
}

/* 注册提示 */
.register-hint {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 12px;
  font-size: 14px;
  color: #5f6368;
}

.register-link {
  margin-left: 8px;
  color: #3b78e7; /* 更柔和的蓝色 */
  font-weight: 500;
}

.register-link:hover {
  text-decoration: underline;
  color: #4a8af4; /* 更柔和的蓝色 */
}

/* 协议文本 */
.agreement-text {
  text-align: center;
  font-size: 13px;
  color: #5f6368;
  margin-top: 14px;
}

.agreement-checkbox {
  margin-top: 12px; /* 减少顶部间距 */
  margin-bottom: 16px; /* 增加底部间距，为错误消息留出空间 */
  position: relative;
}

.agreement-checkbox :deep(.el-checkbox) {
  height: auto;
  display: flex;
  align-items: flex-start;
}

.agreement-checkbox :deep(.el-checkbox__input) {
  vertical-align: top;
  margin-top: 3px;
}

.agreement-checkbox :deep(.el-checkbox__inner) {
  border-color: #5f6368;
  border-radius: 3px;
  transition: all 0.25s;
  width: 16px;
  height: 16px;
}

.agreement-checkbox :deep(.el-checkbox__inner:hover) {
  border-color: #3b78e7; /* 更柔和的蓝色 */
}

.agreement-checkbox :deep(.el-checkbox__label) {
  color: #5f6368;
  font-size: 15px;
  padding-left: 10px;
  line-height: 1.5;
}

.agreement-checkbox :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  color: #5f6368;
}

.agreement-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #3b78e7; /* 更柔和的蓝色 */
  border-color: #3b78e7; /* 更柔和的蓝色 */
}

.agreement-checkbox :deep(.el-link) {
  color: #3b78e7; /* 更柔和的蓝色 */
  font-weight: 500;
  padding: 0;
  font-size: 15px;
}

.agreement-checkbox :deep(.el-link:hover) {
  text-decoration: underline;
  color: #4a8af4; /* 更柔和的蓝色 */
  background-color: transparent !important;
}

/* 表单项动画 */
.el-form-item {
  animation: fadeIn 0.25s ease-out forwards;
  opacity: 0;
  position: relative;
  margin-bottom: 24px;
}

/* 表单错误消息样式 */
.el-form-item :deep(.el-form-item__error) {
  position: absolute;
  top: 100%;
  left: 0;
  padding-top: 1px;
  font-size: 11px;
  color: #f56c6c;
  line-height: 1.1;
  z-index: 2;
  white-space: normal;
  overflow: visible;
  max-width: 100%;
  text-align: left;
  word-break: break-word;
  margin-top: 1px;
}

/* 浮动标签表单项的错误消息位置调整 */
.floating-label-form-item :deep(.el-form-item__error) {
  top: calc(100% - 4px);
  left: 12px;
}

/* 验证码表单项的错误消息位置调整 */
.verify-code-form-item :deep(.el-form-item__error) {
  top: calc(100% - 4px);
  left: 12px;
}

/* 协议复选框的错误消息位置调整 */
.agreement-checkbox :deep(.el-form-item__error) {
  top: calc(100% - 4px);
  left: 12px;
}

.el-form-item:nth-child(1) { animation-delay: 0.03s; }
.el-form-item:nth-child(2) { animation-delay: 0.06s; }
.el-form-item:nth-child(3) { animation-delay: 0.09s; }
.el-form-item:nth-child(4) { animation-delay: 0.12s; }
.el-form-item:nth-child(5) { animation-delay: 0.15s; }
.el-form-item:nth-child(6) { animation-delay: 0.18s; }

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 对话框过渡效果 */
:deep(.dialog-fade-enter-active),
:deep(.dialog-fade-leave-active) {
  transition: all 0.3s ease-out;
}

:deep(.dialog-fade-enter-from),
:deep(.dialog-fade-leave-to) {
  transform: translateY(20px);
  opacity: 0;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  :deep(.el-dialog) {
    width: 100% !important;
    margin: 0;
    border-radius: 0;
    max-height: 100vh;
    overflow-y: auto;
  }

  .auth-container {
    padding: 20px 20px 24px;
  }

  .system-logo {
    width: 64px;
    height: 64px;
  }

  .auth-header {
    margin-bottom: 16px;
  }

  .auth-title {
    font-size: 20px;
  }

  .auth-subtitle {
    font-size: 14px;
    max-width: 260px;
  }

  .auth-toggle-mobile {
    display: flex;
    justify-content: center;
  }

  .login-type-tabs {
    margin-bottom: 20px;
  }

  .login-type-tab {
    padding: 10px 0;
    font-size: 13px;
  }

  .auth-input :deep(.el-input__wrapper) {
    height: 44px;
  }

  .floating-label {
    top: 12px;
  }

  .floating-label-form-item {
    margin-bottom: 12px;
  }

  .captcha-img-container {
    height: 44px;
    width: 120px; /* 增加宽度确保验证码完整显示 */
  }

  .code-btn {
    height: 44px;
    min-width: 100px;
    font-size: 13px;
    padding: 0 8px;
  }

  .verify-type-radio :deep(.el-radio-group) {
    flex-direction: column;
    gap: 12px;
  }

  .login-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
  }

  .button-container {
    justify-content: space-between;
    margin: 16px 0 12px;
  }

  .next-button {
    width: 100%;
    height: 36px;
  }

  .switch-mode-btn {
    height: 36px;
    padding: 6px 12px;
  }
}
</style>
