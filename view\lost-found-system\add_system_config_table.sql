USE lost_found;

-- 检查system_config表是否存在
CREATE TABLE IF NOT EXISTS system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入初始配置
INSERT INTO system_config (config_key, config_value, description)
VALUES 
('autodl.clip.api.url', 'http://your-autodl-instance-ip:8000', '智能匹配服务API地址'),
('autodl.clip.service.check-enabled', 'true', '是否启用服务可用性检查'),
('autodl.clip.service.connection-timeout', '3000', '连接超时时间（毫秒）'),
('autodl.clip.service.script-path', './clip_faiss_service/start_clip_service.sh', '服务启动脚本路径')
ON DUPLICATE KEY UPDATE 
    description = VALUES(description);
