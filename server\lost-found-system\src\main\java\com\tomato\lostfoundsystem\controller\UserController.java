package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.DeactivateAccountDTO;
import com.tomato.lostfoundsystem.dto.LoginRequestDTO;
import com.tomato.lostfoundsystem.dto.RegisterRequestDTO;
import com.tomato.lostfoundsystem.dto.UserProfileDTO;
import com.tomato.lostfoundsystem.entity.User;
import com.tomato.lostfoundsystem.service.CaptchaService;
import com.tomato.lostfoundsystem.service.RedisService;
import com.tomato.lostfoundsystem.service.UserService;
import com.tomato.lostfoundsystem.utils.JWTUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;


    private final CaptchaService captchaService;  // 注入验证码服务

    private final JWTUtil jwtUtil;


    /**
     * 用户注册接口
     */
    @PostMapping("/register")
    public Result register(@Valid @RequestBody RegisterRequestDTO registerRequestDTO) {

        return userService.register(registerRequestDTO);

    }

    /**
     * 用户登录接口
     * @param loginDTO 登录请求数据
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result login(@Valid @RequestBody LoginRequestDTO loginDTO) {
        log.info("接收到登录请求 - 用户名: {}, 邮箱: {}, 手机号: {}",
                loginDTO.getUsername(), loginDTO.getEmail(), loginDTO.getPhone());

        try {
            // 调用 UserService 处理登录逻辑
            Result result = userService.login(loginDTO);

            if (result.getCode() == 200) {
                log.info("用户登录成功 - 用户名/邮箱/手机: {}/{}/{}",
                        loginDTO.getUsername(), loginDTO.getEmail(), loginDTO.getPhone());
            } else {
                log.warn("用户登录失败 - 用户名/邮箱/手机: {}/{}/{}, 原因: {}",
                        loginDTO.getUsername(), loginDTO.getEmail(), loginDTO.getPhone(),
                        result.getMessage());
            }

            return result;
        } catch (Exception e) {
            log.error("登录过程发生异常: {}", e.getMessage(), e);
            return Result.fail("登录失败，服务器内部错误");
        }
    }


    @PostMapping("/logout")
    public Result<String> logout(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        log.info("获取请求头:{}",token);
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.fail("未提供有效的 Token，退出失败");
        }

        token = token.substring(7); // 去掉 "Bearer " 前缀
        boolean success = userService.logout(token);

        if (success) {
            return Result.success("退出登录成功");
        } else {
            return Result.fail("退出失败，Token 无效或用户未登录");
        }
    }

    /**
     * 生成图形验证码
     */
    @PostMapping("/generateCaptcha")
    public Result<Map<String, Object>> generateCaptcha() {
        try {
            // 生成验证码图像和UUID
            Map<String, Object> captchaData = captchaService.generateCaptchaImage();
            log.info("验证码生成成功 - 标识符: {}", captchaData.get("captchaId"));
            return Result.success("验证码生成成功", captchaData);
        } catch (Exception e) {
            log.error("验证码生成失败", e);
            return Result.fail("验证码生成失败");
        }
    }


    /**
     * 获取用户个人资料
     */
    @GetMapping("/profile")
    public Result<UserProfileDTO> getUserProfile(HttpServletRequest request) {
        // 获取请求头中的 token
        String token = request.getHeader("Authorization");

        if (token == null || !token.startsWith("Bearer ")) {
            return Result.fail("未提供有效的 Token");
        }

        token = token.substring(7); // 去掉 "Bearer " 前缀

        // 解析 token 获取 userId
        Long userId = jwtUtil.getUserIdFromToken(token);
        if (userId == null) {
            return Result.fail("无效的 Token");
        }

        // 使用 userId 获取用户信息
        User user = userService.getUserById(userId);
        if (user == null) {
            return Result.fail("用户不存在");
        }

        // 将用户信息封装到 DTO 中返回
        UserProfileDTO userProfileDTO = new UserProfileDTO();
        userProfileDTO.setId(user.getId());
        userProfileDTO.setUsername(user.getUsername());
        userProfileDTO.setEmail(user.getEmail());
        userProfileDTO.setPhone(user.getPhone());
        userProfileDTO.setRole(user.getRole());
        userProfileDTO.setCreateTime(user.getCreateTime());
        userProfileDTO.setAvatar(user.getAvatar()); // 添加头像URL
        userProfileDTO.setIsActive(user.getIsActive());
        userProfileDTO.setDeleted(user.getDeleted());

        log.info("用户信息获取成功: {}", userProfileDTO);
        return Result.success("用户信息获取成功", userProfileDTO);
    }

    /**
     * 更新用户个人资料
     */
    @PutMapping("/update-profile")
    public Result<String> updateUserProfile(@RequestBody UserProfileDTO userProfileDTO, HttpServletRequest request) {
        // 获取请求头中的 token
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.fail("未提供有效的 Token");
        }

        token = token.substring(7); // 去掉 "Bearer " 前缀

        // 解析 token 获取 userId
        Long userId = jwtUtil.getUserIdFromToken(token);
        if (userId == null) {
            return Result.fail("无效的 Token");
        }

        // 设置从token解析出的 userId
        userProfileDTO.setId(userId);

        // 调用 service 层处理用户信息更新
        boolean isUpdated = userService.updateUserProfile(userProfileDTO);
        if (isUpdated) {
            return Result.success("用户资料更新成功");
        } else {
            return Result.fail("更新失败，邮箱或手机号已被其他用户使用");
        }
    }

    /**
     * 用户注销账号
     */
    @PostMapping("/deactivate")
    public Result<String> deactivateAccount(@RequestBody DeactivateAccountDTO dto, HttpServletRequest request) {
        // 获取请求头中的 token
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.fail("未提供有效的 Token");
        }

        token = token.substring(7); // 去掉 "Bearer " 前缀

        // 解析 token 获取 userId
        Long userId = jwtUtil.getUserIdFromToken(token);
        if (userId == null) {
            return Result.fail("无效的 Token");
        }

        // 验证确认信息
        if (!"CONFIRM".equals(dto.getConfirmation())) {
            return Result.fail("请输入 CONFIRM 以确认注销账号");
        }

        // 调用 service 层处理账号注销
        return userService.deactivateAccount(userId, dto.getPassword());
    }

    /**
     * 上传用户头像
     */
    @PostMapping("/avatar")
    public Result<String> uploadAvatar(@RequestParam("file") MultipartFile file, HttpServletRequest request) {
        // 获取请求头中的 token
        String token = request.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            return Result.fail("未提供有效的 Token");
        }

        token = token.substring(7); // 去掉 "Bearer " 前缀

        // 解析 token 获取 userId
        Long userId = jwtUtil.getUserIdFromToken(token);
        if (userId == null) {
            return Result.fail("无效的 Token");
        }

        // 调用 service 层处理头像上传
        return userService.uploadAvatar(userId, file);
    }

}





