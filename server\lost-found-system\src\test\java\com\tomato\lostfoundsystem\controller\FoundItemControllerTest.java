package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.FoundItemDTO;
import com.tomato.lostfoundsystem.service.FoundItemService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.containsString;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.xpath;

@ExtendWith(MockitoExtension.class)
public class FoundItemControllerTest {

    @Mock
    private FoundItemService foundItemService;

    @InjectMocks
    private FoundItemController foundItemController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(foundItemController)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();
    }

    // 添加全局异常处理器
    public static class GlobalExceptionHandler {
        @ExceptionHandler(Exception.class)
        public Result<Object> handleException(Exception e) {
            return Result.fail(e.getMessage());
        }
    }

    /**
     * 测试使用多张图片发布拾物
     */
    @Test
    void publishFoundItem_WithMultipleImages_ShouldReturnSuccess() throws Exception {
        // 准备测试数据 - 创建多个图片文件
        MockMultipartFile image1 = new MockMultipartFile(
                "images",
                "test1.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test image content 1".getBytes()
        );
        
        MockMultipartFile image2 = new MockMultipartFile(
                "images",
                "test2.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test image content 2".getBytes()
        );
        
        MockMultipartFile image3 = new MockMultipartFile(
                "images",
                "test3.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test image content 3".getBytes()
        );

        // 模拟服务层返回成功结果
        when(foundItemService.publishFoundItem(any(FoundItemDTO.class)))
                .thenReturn(Result.success("发布成功,等待审核", null));

        // 执行测试
        mockMvc.perform(multipart("/api/found-items/publish")
                        .file(image1)
                        .file(image2)
                        .file(image3)
                        .param("itemName", "测试拾物")
                        .param("description", "这是一个详细的拾物描述，包含了物品的颜色、形状和特征，长度超过20个字符。")
                        .param("foundTime", "2023-05-01T10:00:00")
                        .param("foundLocation", "图书馆")
                        .requestAttr("userId", 1L)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(content().contentType("application/xml;charset=UTF-8"))
                .andExpect(xpath("/r/code").string("200"))
                .andExpect(xpath("/r/message").string("发布成功,等待审核"));

        // 验证服务层方法被调用
        verify(foundItemService, times(1)).publishFoundItem(any(FoundItemDTO.class));
    }

    /**
     * 测试使用单张图片发布拾物
     */
    @Test
    void publishFoundItem_WithSingleImage_ShouldReturnSuccess() throws Exception {
        // 准备测试数据
        MockMultipartFile image = new MockMultipartFile(
                "image",
                "test.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test image content".getBytes()
        );

        // 模拟服务层返回成功结果
        when(foundItemService.publishFoundItem(any(FoundItemDTO.class)))
                .thenReturn(Result.success("发布成功,等待审核", null));

        // 执行测试
        mockMvc.perform(multipart("/api/found-items/publish")
                        .file(image)
                        .param("itemName", "测试拾物")
                        .param("description", "这是一个详细的拾物描述，包含了物品的颜色、形状和特征，长度超过20个字符。")
                        .param("foundTime", "2023-05-01T10:00:00")
                        .param("foundLocation", "图书馆")
                        .requestAttr("userId", 1L)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(content().contentType("application/xml;charset=UTF-8"))
                .andExpect(xpath("/r/code").string("200"))
                .andExpect(xpath("/r/message").string("发布成功,等待审核"));

        // 验证服务层方法被调用
        verify(foundItemService, times(1)).publishFoundItem(any(FoundItemDTO.class));
    }

    /**
     * 测试没有图片发布拾物
     */
    @Test
    void publishFoundItem_WithoutImage_ShouldReturnError() throws Exception {
        // 执行测试
        mockMvc.perform(multipart("/api/found-items/publish")
                        .param("itemName", "测试拾物")
                        .param("description", "这是一个详细的拾物描述，包含了物品的颜色、形状和特征，长度超过20个字符。")
                        .param("foundTime", "2023-05-01T10:00:00")
                        .param("foundLocation", "图书馆")
                        .requestAttr("userId", 1L)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(content().contentType("application/xml;charset=UTF-8"))
                .andExpect(xpath("/r/code").string("400"))
                .andExpect(xpath("/r/message").string("请至少上传一张拾物照片"));

        // 验证服务层方法未被调用
        verify(foundItemService, never()).publishFoundItem(any(FoundItemDTO.class));
    }

    /**
     * 测试描述太短发布拾物
     */
    @Test
    void publishFoundItem_WithShortDescription_ShouldReturnError() throws Exception {
        // 准备测试数据
        MockMultipartFile image = new MockMultipartFile(
                "image",
                "test.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test image content".getBytes()
        );

        // 执行测试
        mockMvc.perform(multipart("/api/found-items/publish")
                        .file(image)
                        .param("itemName", "测试拾物")
                        .param("description", "描述太短")
                        .param("foundTime", "2023-05-01T10:00:00")
                        .param("foundLocation", "图书馆")
                        .requestAttr("userId", 1L)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(content().contentType("application/xml;charset=UTF-8"))
                .andExpect(xpath("/r/code").string("400"))
                .andExpect(xpath("/r/message").string(containsString("物品描述长度必须在20-500个字符之间")));

        // 验证服务层方法未被调用
        verify(foundItemService, never()).publishFoundItem(any(FoundItemDTO.class));
    }
}
