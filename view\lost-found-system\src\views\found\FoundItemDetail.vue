<template>
  <div class="found-item-detail">
    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <h2 class="title">拾物详情</h2>
          <div class="actions">
            <el-button type="primary" @click="router.push('/found-items')">
              <el-icon><Back /></el-icon>
              返回列表
            </el-button>
            <el-button
              v-if="item.userId === currentUserId && item.status === 'UNCLAIMED'"
              type="primary"
              @click="router.push(`/found-items/edit/${item.id}`)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button
              v-if="item.userId === currentUserId && item.status === 'UNCLAIMED'"
              type="danger"
              @click="handleDelete"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>

            <el-button
              v-if="item.userId !== currentUserId"
              type="info"
              @click="contactPublisher"
            >
              <el-icon><ChatRound /></el-icon>
              联系发布者
            </el-button>
          </div>
        </div>
      </template>

      <div class="detail-content">
        <!-- 左侧图片区域 -->
        <div class="image-section" v-if="imageList.length > 0">
          <el-carousel
            :interval="4000"
            height="400px"
            :autoplay="imageList.length > 1"
            indicator-position="outside"
            class="image-carousel"
            arrow="always"
            v-if="imageList.length > 0"
          >
            <el-carousel-item v-for="(url, index) in imageList" :key="index">
              <div class="carousel-image-container">
                <el-image
                  :src="url"
                  fit="contain"
                  :preview-src-list="imageList"
                  :initial-index="index"
                  class="carousel-image"
                  :zoom-rate="1.2"
                  :preview-teleported="true"
                  @error="handleImageError"
                >
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                      <span>加载中...</span>
                    </div>
                  </template>
                  <template #error>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                      <span>加载失败</span>
                    </div>
                  </template>
                </el-image>
              </div>
            </el-carousel-item>
          </el-carousel>

          <!-- 缩略图预览 -->
          <div class="image-thumbnails" v-if="imageList.length > 1">
            <div
              v-for="(url, index) in imageList"
              :key="index"
              class="thumbnail-item"
              :class="{ active: index === activeImageIndex }"
              @click="setActiveImage(index)"
            >
              <el-image
                :src="url"
                fit="cover"
                class="thumbnail-image"
              />
            </div>
          </div>
        </div>

        <!-- 右侧信息区域 -->
        <div class="info-section">
          <div class="info-header">
            <h1 class="item-name">{{ item.itemName }}</h1>
            <el-tag :type="getStatusType(item.status)" class="status-tag">
              {{ item.status === 'UNCLAIMED' ? '未认领' : '已归还' }}
            </el-tag>
          </div>

          <el-tabs v-model="activeTab" class="info-tabs">
            <el-tab-pane label="基本信息" name="basic">
              <el-descriptions :column="1" border class="detail-info">
                <el-descriptions-item label="物品名称">
                  <span class="info-value">{{ item.itemName }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="拾获地点">
                  <span class="info-value">{{ item.foundLocation }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="拾获时间">
                  <span class="info-value">{{ item.foundTime }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="发布人">
                  <span class="info-value">{{ item.username || '匿名用户' }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </el-tab-pane>

            <el-tab-pane label="详细描述" name="description">
              <div class="description-box">
                {{ item.description }}
              </div>
            </el-tab-pane>

            <el-tab-pane label="时间信息" name="time">
              <el-descriptions :column="1" border class="detail-info">
                <el-descriptions-item label="发布时间">
                  <span class="info-value">{{ item.createdAt }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="更新时间">
                  <span class="info-value">{{ item.updatedAt }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Back, Edit, Delete, Picture, ChatRound } from '@element-plus/icons-vue';
import { getFoundItemDetail, deleteFoundItem } from '../../api/found';
import { createConversation } from '../../api/chat';
import { useUserStore } from '../../stores';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const currentUserId = computed(() => userStore.userInfo?.id);
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
const defaultImage = '/default-image.jpg';  // 修改为根目录下的路径

const item = ref({});
const activeTab = ref('basic');

// 计算图片URL
const imageUrl = computed(() => {
  if (!item.value.imageUrl) {
    console.log('没有图片URL，使用默认图片');
    return defaultImage;
  }

  // 如果已经是完整URL，直接返回
  if (item.value.imageUrl.startsWith('http')) {
    return item.value.imageUrl;
  }

  // 处理相对路径
  const url = item.value.imageUrl.startsWith('/')
    ? item.value.imageUrl
    : `/${item.value.imageUrl}`;

  const fullUrl = `${apiBaseUrl}${url}`;
  console.log('计算后的图片URL：', fullUrl);
  return fullUrl;
});

// 图片列表
const imageList = computed(() => {
  if (!item.value) return [];

  // 如果有imageUrls属性（多图片），使用它
  if (item.value.imageUrls && item.value.imageUrls.length > 0) {
    // 确保主图不重复显示
    if (item.value.imageUrl) {
      // 过滤掉与主图相同的URL，避免重复
      const filteredUrls = item.value.imageUrls.filter(url => url !== item.value.imageUrl);
      // 将主图放在第一位
      return [item.value.imageUrl, ...filteredUrls];
    }
    return item.value.imageUrls;
  }

  // 否则，如果有单张图片，使用它
  if (item.value.imageUrl) {
    return [item.value.imageUrl];
  }

  return [];
});

// 当前活动图片索引
const activeImageIndex = ref(0);

// 获取详情
const fetchDetail = async () => {
  try {
    const res = await getFoundItemDetail(route.params.id);
    console.log('获取到的详情数据：', res);
    if (res.code === 200) {
      item.value = res.data;
      console.log('图片URL信息：', {
        raw: item.value.imageUrl,
        full: imageUrl.value,
        apiBaseUrl,
        isDefault: !item.value.imageUrl
      });
    } else {
      ElMessage.error(res.message || '获取详情失败');
    }
  } catch (error) {
    console.error('获取详情失败：', error);
    ElMessage.error('获取详情失败');
  }
};

// 处理删除
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除这条拾物信息吗？此操作不可恢复', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await deleteFoundItem(item.value.id);
    ElMessage.success('删除成功');
    router.push('/found-items');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};



// 联系发布者
const contactPublisher = async () => {
  try {
    // 检查参数
    console.log('创建会话参数检查:', {
      currentUserId: currentUserId.value,
      publisherId: item.value.userId,
      userInfo: userStore.userInfo
    });

    // 参数验证
    if (!currentUserId.value) {
      ElMessage.warning({
        message: '您需要先登录才能联系发布者',
        duration: 2000
      });
      return;
    }

    if (!item.value.userId) {
      ElMessage.warning({
        message: '无法获取发布者信息，请刷新页面重试',
        duration: 2000
      });
      return;
    }

    // 创建会话
    const chatRes = await createConversation(currentUserId.value, item.value.userId);
    console.log('创建会话响应:', chatRes);

    if (chatRes.code === 200) {
      ElMessage.success({
        message: '已创建会话，即将跳转到聊天页面...',
        duration: 1500
      });

      // 延迟跳转，让用户看到提示信息
      setTimeout(() => {
        router.push('/chat');
      }, 1500);
    }
  } catch (error) {
    console.error('创建会话失败：', error);
    ElMessage.error('创建会话失败，请稍后重试');
  }
};

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'UNCLAIMED':
      return 'warning';
    case 'RETURNED':
      return 'success';
    default:
      return 'info';
  }
};

// 设置当前活动图片
const setActiveImage = (index) => {
  activeImageIndex.value = index;
};

// 处理图片加载错误
const handleImageError = (e) => {
  console.error('图片加载错误：', e);
  console.log('图片加载错误详情：', {
    currentImageUrl: item.value.imageUrl,
    fullImageUrl: imageUrl.value,
    apiBaseUrl,
    error: e
  });

  // 如果当前不是默认图片，尝试使用默认图片
  if (imageUrl.value !== defaultImage) {
    console.log('尝试使用默认图片');
    item.value.imageUrl = null; // 触发重新计算imageUrl
  } else {
    ElMessage.warning('图片加载失败，请稍后重试');
  }
};

onMounted(() => {
  fetchDetail();
});
</script>

<style scoped>
.found-item-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.detail-card {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  margin: 0;
  font-size: 20px;
  color: #303133;
}

.actions {
  display: flex;
  gap: 8px;
}

.detail-content {
  display: flex;
  gap: 24px;
  padding: 20px;
}

.image-section {
  flex: 0 0 40%;
  max-width: 40%;
  background-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.image-carousel {
  width: 100%;
  height: 100%;
}

.carousel-image-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.carousel-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s;
}

.carousel-image:hover {
  transform: scale(1.05);
}

.image-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
}

.image-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.info-section {
  flex: 2;
  min-width: 400px;
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.item-name {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  flex: 1;
}

.status-tag {
  font-size: 14px;
  padding: 6px 12px;
  margin-left: 16px;
}

.info-tabs {
  margin-bottom: 24px;
}

.detail-info {
  margin-bottom: 0;
}

.description-box {
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  min-height: 100px;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

.info-value {
  font-weight: 500;
  color: #303133;
}

:deep(.el-carousel__item) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-carousel__arrow) {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 36px;
  height: 36px;
}

:deep(.el-carousel__arrow:hover) {
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  font-size: 15px;
  padding: 0 20px;
}

:deep(.el-tabs__item.is-active) {
  font-weight: 600;
}

.image-thumbnails {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  justify-content: center;
}

.thumbnail-item {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s;
}

.thumbnail-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.thumbnail-item.active {
  border-color: var(--el-color-primary);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media screen and (max-width: 768px) {
  .detail-content {
    flex-direction: column;
  }

  .image-section {
    max-width: 100%;
  }

  .info-section {
    min-width: 100%;
  }

  .image-thumbnails {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 8px;
  }
}
</style>