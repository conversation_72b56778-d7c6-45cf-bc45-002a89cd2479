```mermaid
graph TD
    %% 标题
    title[校园失物招领系统架构图]
    style title fill:none,stroke:none

    %% 客户端层
    subgraph ClientLayer[客户端层]
        Client[用户/客户端]
        AdminClient[管理员/客户端]
    end
    style ClientLayer fill:#EBF5FB,stroke:#3498DB
    style Client fill:#D6EAF8,stroke:#2E86C1
    style AdminClient fill:#D6EAF8,stroke:#2E86C1

    %% 前端应用层
    subgraph FrontendLayer[前端应用层]
        Frontend[前端应用<br>Vue 3 + Vite]
        AdminFrontend[管理后台<br>Vue 3 + Vite]
    end
    style FrontendLayer fill:#E9F7EF,stroke:#27AE60
    style Frontend fill:#D5F5E3,stroke:#27AE60
    style AdminFrontend fill:#D5F5E3,stroke:#27AE60

    %% API网关层
    subgraph GatewayLayer[API网关层]
        Gateway[API网关/负载均衡]
    end
    style GatewayLayer fill:#FEF5E7,stroke:#E67E22
    style Gateway fill:#FAD7A0,stroke:#E67E22

    %% 后端服务层
    subgraph BackendLayer[后端服务层]
        Backend[Spring Boot应用]
        Auth[认证授权服务<br>JWT]
        Service[业务逻辑层<br>Service]
        DAO[数据访问层<br>MyBatis]
        WebSocket[WebSocket服务<br>STOMP]
    end
    style BackendLayer fill:#FEF9E7,stroke:#F1C40F
    style Backend fill:#F9E79F,stroke:#F1C40F
    style Auth fill:#FCF3CF,stroke:#F1C40F
    style Service fill:#FCF3CF,stroke:#F1C40F
    style DAO fill:#FCF3CF,stroke:#F1C40F
    style WebSocket fill:#FCF3CF,stroke:#F1C40F

    %% 数据存储层
    subgraph StorageLayer[数据存储层]
        MySQL[(数据库<br>MySQL)]
        Redis[(缓存服务<br>Redis)]
        Kafka[(消息队列<br>Kafka)]
    end
    style StorageLayer fill:#FDEDEC,stroke:#E74C3C
    style MySQL fill:#F5B7B1,stroke:#E74C3C
    style Redis fill:#F5B7B1,stroke:#E74C3C
    style Kafka fill:#FADBD8,stroke:#E74C3C

    %% 外部服务层
    subgraph ExternalLayer[外部服务层]
        OSS[文件存储服务<br>阿里云OSS]
        CLIP[智能匹配服务<br>CLIP+FAISS]
        GPU[GPU计算资源<br>AutoDL]
    end
    style ExternalLayer fill:#EBF0F2,stroke:#2C3E50
    style OSS fill:#D6DBDF,stroke:#2C3E50
    style CLIP fill:#D6DBDF,stroke:#2C3E50
    style GPU fill:#D6DBDF,stroke:#2C3E50

    %% 主要连接关系 - 垂直方向
    ClientLayer --> FrontendLayer
    FrontendLayer --> GatewayLayer
    GatewayLayer --> BackendLayer
    BackendLayer --> StorageLayer
    BackendLayer --> ExternalLayer

    %% 详细连接
    Auth --> Redis
    Service --> DAO
    DAO --> MySQL
    WebSocket --> Redis
    WebSocket --> Kafka
    Service --> OSS
    Service --> CLIP
    CLIP --> GPU
```
