package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.VerifyCodeRequestDTO;
import com.tomato.lostfoundsystem.service.VerifyCodeService;
import jakarta.validation.constraints.Email;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/verify")
@RequiredArgsConstructor
public class VerifyCodeController {
        @Autowired
        private VerifyCodeService verifyCodeService;

        @PostMapping("/sendEmailCode")
        public Result<String> sendEmailVerifyCode(@RequestBody VerifyCodeRequestDTO dto) {
            // 获取 isRegister 参数，判断是注册还是登录验证码
            boolean isRegister = dto.isRegister();
            log.info("收到发送邮箱验证码请求 - 邮箱: {}, 是否注册场景: {}", dto.getEmail(), isRegister);
            String email = dto.getEmail();

            // 添加参数验证日志
            if (email == null || email.trim().isEmpty()) {
                log.warn("邮箱为空，请求被拒绝");
                return Result.fail("邮箱不能为空");
            }

            // 调用服务发送验证码
            Result<String> result = verifyCodeService.sendEmailVerifyCode(email, isRegister);
            log.info("验证码服务返回结果 - 状态: {}, 消息: {}", result.getCode(), result.getMessage());

            return result;
        }

        @PostMapping("/sendPhoneCode")
        public Result<String> sendPhoneVerifyCode(@RequestBody VerifyCodeRequestDTO dto) {
            // 添加详细日志
            log.info("收到发送手机验证码请求 - 手机号: {}, 是否注册场景: {}", dto.getPhone(), dto.isRegister());

            // 获取 isRegister 参数，判断是注册还是登录验证码
            boolean isRegister = dto.isRegister();
            String phone = dto.getPhone();

            // 添加参数验证日志
            if (phone == null || phone.trim().isEmpty()) {
                log.warn("手机号为空，请求被拒绝");
                return Result.fail("手机号不能为空");
            }

            // 调用服务发送验证码
            log.info("准备调用验证码服务发送短信 - 手机号: {}, 是否注册场景: {}", phone, isRegister);
            Result<String> result = verifyCodeService.sendPhoneVerifyCode(phone, isRegister);
            log.info("验证码服务返回结果 - 状态: {}, 消息: {}", result.getCode(), result.getMessage());

            return result;
        }
    }
