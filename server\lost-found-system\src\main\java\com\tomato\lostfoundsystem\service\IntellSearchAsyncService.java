package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.FoundItem;
import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.entity.MatchHistory;
import com.tomato.lostfoundsystem.entity.MatchResult;
import com.tomato.lostfoundsystem.mapper.FoundItemMapper;
import com.tomato.lostfoundsystem.mapper.LostItemMapper;
import com.tomato.lostfoundsystem.mapper.MatchHistoryMapper;
import com.tomato.lostfoundsystem.mapper.MatchResultMapper;
import com.tomato.lostfoundsystem.utils.ClipFaissClientRefactored;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 智能搜索异步服务
 * 用于处理所有类型的智能搜索异步任务
 */
@Slf4j
@Service
public class IntellSearchAsyncService {

    @Autowired
    private ClipFaissClientRefactored clipFaissClient;

    @Autowired
    private MatchHistoryMapper matchHistoryMapper;

    @Autowired
    private MatchResultMapper matchResultMapper;

    @Autowired
    private MatchNotificationService matchNotificationService;

    @Autowired
    private LostItemMapper lostItemMapper;

    @Autowired
    private FoundItemMapper foundItemMapper;

    @Value("${match.notification.similarity-threshold:0.7}")
    private Float notificationSimilarityThreshold;

    @Value("${match.result.similarity-threshold:0.5}")
    private Float resultSimilarityThreshold;

    @Value("${match.notification.auto-notify:true}")
    private Boolean autoNotify;

    private static final int MAX_RESULTS = 20;

    /**
     * 异步执行图像搜索任务
     *
     * @param matchHistoryId 匹配历史ID
     * @param imageFeatures  图像特征向量
     * @param targetType     目标物品类型
     * @return CompletableFuture<Map<String, Object>> 包含搜索结果的CompletableFuture
     */
    @Async("searchExecutor")
    public CompletableFuture<Result<Map<String, Object>>> asyncImageSearch(
            Long matchHistoryId,
            byte[] imageFeatures,
            String targetType) {

        log.info("【异步图像搜索】开始执行，匹配历史ID: {}", matchHistoryId);

        try {
            // 获取匹配历史记录
            MatchHistory matchHistory = matchHistoryMapper.getMatchHistoryById(matchHistoryId);
            if (matchHistory == null) {
                return CompletableFuture.completedFuture(
                        Result.fail("匹配历史记录不存在: " + matchHistoryId));
            }

            // 使用图像特征向量搜索相似物品
            Map<String, Object> searchResults = clipFaissClient.searchByImageVector(
                    imageFeatures, targetType, MAX_RESULTS);

            log.info("【异步图像搜索】搜索完成，匹配历史ID: {}, 结果: {}",
                    matchHistoryId, searchResults != null ? "成功" : "失败");

            // 处理搜索结果
            Result<Map<String, Object>> result = processImageSearchResults(
                    searchResults, matchHistory, targetType);

            log.info("【异步图像搜索】处理完成，匹配历史ID: {}", matchHistoryId);

            return CompletableFuture.completedFuture(result);

        } catch (Exception e) {
            log.error("【异步图像搜索】执行异常，匹配历史ID: {}", matchHistoryId, e);
            return CompletableFuture.completedFuture(
                    Result.fail("异步图像搜索失败: " + e.getMessage()));
        }
    }

    /**
     * 异步执行文本搜索任务
     *
     * @param matchHistoryId 匹配历史ID
     * @param textFeatures   文本特征向量
     * @param targetType     目标物品类型
     * @return CompletableFuture<Map<String, Object>> 包含搜索结果的CompletableFuture
     */
    @Async("searchExecutor")
    public CompletableFuture<Result<Map<String, Object>>> asyncTextSearch(
            Long matchHistoryId,
            byte[] textFeatures,
            String targetType) {

        log.info("【异步文本搜索】开始执行，匹配历史ID: {}", matchHistoryId);

        try {
            // 获取匹配历史记录
            MatchHistory matchHistory = matchHistoryMapper.getMatchHistoryById(matchHistoryId);
            if (matchHistory == null) {
                return CompletableFuture.completedFuture(
                        Result.fail("匹配历史记录不存在: " + matchHistoryId));
            }

            // 使用文本特征向量搜索相似物品
            Map<String, Object> searchResults = clipFaissClient.searchByTextVector(
                    textFeatures, targetType, MAX_RESULTS);

            log.info("【异步文本搜索】搜索完成，匹配历史ID: {}, 结果: {}",
                    matchHistoryId, searchResults != null ? "成功" : "失败");

            // 处理搜索结果
            Result<Map<String, Object>> result = processSearchResults(
                    searchResults, matchHistory, targetType);

            log.info("【异步文本搜索】处理完成，匹配历史ID: {}", matchHistoryId);

            return CompletableFuture.completedFuture(result);

        } catch (Exception e) {
            log.error("【异步文本搜索】执行异常，匹配历史ID: {}", matchHistoryId, e);
            return CompletableFuture.completedFuture(
                    Result.fail("异步文本搜索失败: " + e.getMessage()));
        }
    }

    /**
     * 异步执行混合搜索任务
     *
     * @param matchHistoryId 匹配历史ID
     * @param imageFeatures  图像特征向量
     * @param textFeatures   文本特征向量
     * @param targetType     目标物品类型
     * @return CompletableFuture<Map<String, Object>> 包含搜索结果的CompletableFuture
     */
    @Async("searchExecutor")
    public CompletableFuture<Result<Map<String, Object>>> asyncMixedSearch(
            Long matchHistoryId,
            byte[] imageFeatures,
            byte[] textFeatures,
            String targetType) {

        log.info("【异步混合搜索】开始执行，匹配历史ID: {}", matchHistoryId);

        try {
            // 获取匹配历史记录
            MatchHistory matchHistory = matchHistoryMapper.getMatchHistoryById(matchHistoryId);
            if (matchHistory == null) {
                return CompletableFuture.completedFuture(
                        Result.fail("匹配历史记录不存在: " + matchHistoryId));
            }

            // 使用混合特征向量搜索相似物品
            Map<String, Object> searchResults = clipFaissClient.searchByMixedVectors(
                    imageFeatures, textFeatures, targetType, MAX_RESULTS);

            log.info("【异步混合搜索】搜索完成，匹配历史ID: {}, 结果: {}",
                    matchHistoryId, searchResults != null ? "成功" : "失败");

            // 处理搜索结果
            Result<Map<String, Object>> result = processSearchResults(
                    searchResults, matchHistory, targetType);

            log.info("【异步混合搜索】处理完成，匹配历史ID: {}", matchHistoryId);

            return CompletableFuture.completedFuture(result);

        } catch (Exception e) {
            log.error("【异步混合搜索】执行异常，匹配历史ID: {}", matchHistoryId, e);
            return CompletableFuture.completedFuture(
                    Result.fail("异步混合搜索失败: " + e.getMessage()));
        }
    }

    /**
     * 处理图像搜索结果，包含图像-图像和图像-文本匹配结果
     */
    @SuppressWarnings("unchecked")
    private Result<Map<String, Object>> processImageSearchResults(Map<String, Object> searchResults, MatchHistory matchHistory, String targetType) {
        try {
            if (searchResults == null || searchResults.isEmpty()) {
                log.info("【智能匹配】未找到匹配结果，匹配历史ID: {}", matchHistory.getId());
                matchHistoryMapper.updateResultCount(matchHistory.getId(), 0);
                return Result.success(Map.of("results", Map.of(
                    "image_to_image", List.of(),
                    "image_to_text", List.of(),
                    "combined", List.of()
                )));
            }

            // 获取不同类型的结果
            List<Map<String, Object>> imageToImageResults = (List<Map<String, Object>>) searchResults.getOrDefault("image_to_image", List.of());
            List<Map<String, Object>> imageToTextResults = (List<Map<String, Object>>) searchResults.getOrDefault("image_to_text", List.of());
            List<Map<String, Object>> combinedResults = (List<Map<String, Object>>) searchResults.getOrDefault("combined", List.of());

            log.info("【智能匹配】找到匹配结果: 图像-图像={}, 图像-文本={}, 综合={}",
                    imageToImageResults.size(), imageToTextResults.size(), combinedResults.size());

            // 处理结果，填充物品详情
            List<Map<String, Object>> processedImageToImageResults = processResultsWithItemDetails(imageToImageResults, targetType);
            List<Map<String, Object>> processedImageToTextResults = processResultsWithItemDetails(imageToTextResults, targetType);
            List<Map<String, Object>> processedCombinedResults = processResultsWithItemDetails(combinedResults, targetType);

            // 更新匹配历史记录
            int totalResults = processedCombinedResults.size();
            matchHistory.setResultCount(totalResults);
            matchHistoryMapper.updateResultCount(matchHistory.getId(), totalResults);

            // 保存匹配结果到数据库
            List<MatchResult> matchResultsList = new ArrayList<>();
            for (Map<String, Object> result : processedCombinedResults) {
                Long itemId = Long.valueOf(result.get("id").toString());
                Float similarity = (Float) result.get("similarity");

                MatchResult matchResult = new MatchResult();
                matchResult.setMatchHistoryId(matchHistory.getId());
                matchResult.setItemId(itemId);
                matchResult.setItemType(targetType);
                matchResult.setSimilarityScore(new BigDecimal(similarity.toString()));
                matchResultsList.add(matchResult);
            }

            // 批量保存匹配结果
            if (!matchResultsList.isEmpty()) {
                matchResultMapper.batchInsertMatchResults(matchResultsList);
            }

            // 处理高相似度匹配并发送通知
            if (autoNotify && !processedCombinedResults.isEmpty()) {
                matchNotificationService.processHighSimilarityMatches(
                    processedCombinedResults,
                    matchHistory.getId(),
                    matchHistory.getUserId(),
                    notificationSimilarityThreshold
                );
            }

            // 返回结果
            Map<String, Object> results = new HashMap<>();
            results.put("matchHistoryId", matchHistory.getId());
            results.put("results", Map.of(
                "image_to_image", processedImageToImageResults,
                "image_to_text", processedImageToTextResults,
                "combined", processedCombinedResults
            ));

            return Result.success(results);
        } catch (Exception e) {
            log.error("处理图像搜索结果时发生异常", e);
            return Result.fail("处理搜索结果失败: " + e.getMessage());
        }
    }

    /**
     * 处理搜索结果
     */
    @SuppressWarnings("unchecked")
    private Result<Map<String, Object>> processSearchResults(Map<String, Object> searchResults, MatchHistory matchHistory, String targetType) {
        try {
            if (searchResults == null || searchResults.isEmpty()) {
                log.info("【智能匹配】未找到匹配结果，匹配历史ID: {}", matchHistory.getId());
                matchHistoryMapper.updateResultCount(matchHistory.getId(), 0);
                return Result.success(Map.of(
                    "matchHistoryId", matchHistory.getId(),
                    "items", List.of()
                ));
            }

            List<Map<String, Object>> results = new ArrayList<>();

            // 处理不同类型的结果
            if (searchResults.containsKey("combined")) {
                // 如果包含combined字段，则使用combined结果
                results = (List<Map<String, Object>>) searchResults.get("combined");
            } else if (searchResults.containsKey("results")) {
                // 如果包含results字段，则使用results结果
                results = (List<Map<String, Object>>) searchResults.get("results");
            }

            // 处理结果，填充物品详情
            List<Map<String, Object>> processedResults = processResultsWithItemDetails(results, targetType);

            // 更新匹配历史记录
            matchHistory.setResultCount(processedResults.size());
            matchHistoryMapper.updateResultCount(matchHistory.getId(), processedResults.size());

            // 保存匹配结果到数据库
            List<MatchResult> matchResultsList = new ArrayList<>();
            for (Map<String, Object> result : processedResults) {
                Long itemId = Long.valueOf(result.get("id").toString());
                Float similarity = (Float) result.get("similarity");

                MatchResult matchResult = new MatchResult();
                matchResult.setMatchHistoryId(matchHistory.getId());
                matchResult.setItemId(itemId);
                matchResult.setItemType(targetType);
                matchResult.setSimilarityScore(new BigDecimal(similarity.toString()));
                matchResultsList.add(matchResult);
            }

            // 批量保存匹配结果
            if (!matchResultsList.isEmpty()) {
                matchResultMapper.batchInsertMatchResults(matchResultsList);
            }

            // 处理高相似度匹配并发送通知
            if (autoNotify && !processedResults.isEmpty()) {
                matchNotificationService.processHighSimilarityMatches(
                    processedResults,
                    matchHistory.getId(),
                    matchHistory.getUserId(),
                    notificationSimilarityThreshold
                );
            }

            // 返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("matchHistoryId", matchHistory.getId());
            response.put("items", processedResults);

            return Result.success(response);
        } catch (Exception e) {
            log.error("处理搜索结果时发生异常", e);
            return Result.error("处理搜索结果失败: " + e.getMessage());
        }
    }

    /**
     * 处理搜索结果，填充物品详情
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> processResultsWithItemDetails(List<Map<String, Object>> results, String itemType) {
        if (results == null || results.isEmpty()) {
            return List.of();
        }

        List<Map<String, Object>> processedResults = new ArrayList<>();
        int filteredCount = 0;

        for (Map<String, Object> result : results) {
            try {
                // 获取物品ID
                Long itemId = Long.valueOf(result.get("item_id").toString());

                // 获取相似度
                Float similarity = Float.valueOf(result.get("similarity").toString());

                // 添加相似度阈值过滤 - 只保留相似度 >= 0.5 的结果
                if (similarity < resultSimilarityThreshold) {
                    log.debug("【智能匹配】过滤低相似度结果: itemId={}, similarity={}, threshold={}",
                             itemId, similarity, resultSimilarityThreshold);
                    filteredCount++;
                    continue;  // 跳过低相似度的结果
                }

                // 获取匹配类型
                String matchType = (String) result.getOrDefault("match_type", "");

                // 获取匹配详情
                Map<String, Object> matchDetails = (Map<String, Object>) result.getOrDefault("match_details", null);

                // 根据物品类型获取详情
                Map<String, Object> itemDetails = new HashMap<>();
                itemDetails.put("id", itemId);
                itemDetails.put("similarity", similarity);
                itemDetails.put("match_type", matchType);

                if (matchDetails != null) {
                    itemDetails.put("match_details", matchDetails);
                }

                if ("LOST".equals(itemType)) {
                    LostItem lostItem = lostItemMapper.selectById(itemId);
                    if (lostItem == null) {
                        log.warn("未找到失物信息，ID: {}", itemId);
                        continue;
                    }

                    itemDetails.put("name", lostItem.getItemName());
                    itemDetails.put("description", lostItem.getDescription());
                    itemDetails.put("location", lostItem.getLostLocation());
                    itemDetails.put("time", lostItem.getLostTime());
                    itemDetails.put("imageUrl", lostItem.getImageUrl());
                    itemDetails.put("status", lostItem.getStatus());
                    itemDetails.put("userId", lostItem.getUserId());
                    itemDetails.put("itemType", "LOST");
                } else {
                    FoundItem foundItem = foundItemMapper.selectById(itemId);
                    if (foundItem == null) {
                        log.warn("未找到拾物信息，ID: {}", itemId);
                        continue;
                    }

                    itemDetails.put("name", foundItem.getItemName());
                    itemDetails.put("description", foundItem.getDescription());
                    itemDetails.put("location", foundItem.getFoundLocation());
                    itemDetails.put("time", foundItem.getFoundTime());
                    itemDetails.put("imageUrl", foundItem.getImageUrl());
                    itemDetails.put("status", foundItem.getStatus());
                    itemDetails.put("userId", foundItem.getUserId());
                    itemDetails.put("itemType", "FOUND");
                }

                processedResults.add(itemDetails);
            } catch (Exception e) {
                log.error("处理搜索结果项时发生异常", e);
            }
        }

        if (filteredCount > 0) {
            log.info("【智能匹配】相似度过滤: 过滤了 {} 个低于阈值 {} 的结果，剩余 {} 个结果",
                    filteredCount, resultSimilarityThreshold, processedResults.size());
        }

        return processedResults;
    }
} 