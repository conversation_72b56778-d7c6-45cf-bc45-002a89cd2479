```mermaid
graph TB
    %% 标题
    title[校园失物招领系统部署架构图]
    style title fill:none,stroke:none

    %% 客户端设备
    subgraph UserDevice[用户设备]
        Browser[Web浏览器]
    end
    style UserDevice fill:#D6EAF8,stroke:#2E86C1

    %% 开发环境
    subgraph DevEnvironment[开发环境]
        subgraph DevFrontend[前端开发环境]
            ViteDevServer[本地Vite开发服务器<br>端口:5173]
            FrontendCode[Vue 3 + Element Plus]
            NodeJS[Node.js环境<br>v16+]
        end

        subgraph DevBackend[后端开发环境]
            DevSpringBoot[本地Spring Boot应用<br>端口:8081]
            DevTomcat[内嵌Tomcat]
            Java[Java环境<br>JDK 17]
            Maven[Maven<br>依赖管理]
        end

        subgraph DevDatabase[本地数据库]
            DevMySQL[本地MySQL<br>端口:3306]
            DevRedis[本地Redis<br>端口:6379]
        end

        subgraph DevMQ[本地消息队列]
            DevKafka[本地Kafka<br>端口:9092]
            Dev<PERSON><PERSON>eeper[本地Zookeeper<br>端口:2181]
        end

        subgraph DevPython[本地Python环境]
            PythonEnv[Python 3.9+]
            VirtualEnv[clip_faiss_env<br>虚拟环境]
            PyTorch[PyTorch<br>CUDA支持]
            FAISS[FAISS-GPU]
            FastAPI[FastAPI<br>端口:8000]
        end
    end
    style DevEnvironment fill:#E8F8F5,stroke:#1ABC9C
    style DevFrontend fill:#D1F2EB,stroke:#1ABC9C
    style DevBackend fill:#D1F2EB,stroke:#1ABC9C
    style DevDatabase fill:#D1F2EB,stroke:#1ABC9C
    style DevMQ fill:#D1F2EB,stroke:#1ABC9C
    style DevPython fill:#D1F2EB,stroke:#1ABC9C

    %% 测试环境
    subgraph TestEnvironment[测试环境]
        subgraph TestServer[测试服务器]
            subgraph TestFrontend[前端测试环境]
                TestNginx[Nginx]
                StaticFiles[静态资源<br>Vue 3构建产物]
            end

            subgraph TestBackend[后端测试环境]
                TestSpringBoot[Spring Boot应用<br>端口:8081]
                TestTomcat[Tomcat]
                TestJava[Java环境<br>JDK 17]
            end

            subgraph TestDatabase[测试数据库]
                TestMySQL[MySQL<br>端口:3306]
                TestRedis[Redis<br>端口:6379]
            end

            subgraph TestMQ[消息队列]
                TestKafka[Kafka<br>端口:9092]
                TestZookeeper[Zookeeper<br>端口:2181]
            end

            subgraph TestPython[Python环境]
                TestPythonEnv[Python 3.9+]
                TestVirtualEnv[clip_faiss_env<br>虚拟环境]
                TestPyTorch[PyTorch<br>CUDA支持]
                TestFAISS[FAISS-GPU]
                TestFastAPI[FastAPI<br>端口:8000]
            end
        end
    end
    style TestEnvironment fill:#FCF3CF,stroke:#F1C40F
    style TestServer fill:#F9E79F,stroke:#F1C40F
    style TestFrontend fill:#FDEBD0,stroke:#F1C40F
    style TestBackend fill:#FDEBD0,stroke:#F1C40F
    style TestDatabase fill:#FDEBD0,stroke:#F1C40F
    style TestMQ fill:#FDEBD0,stroke:#F1C40F
    style TestPython fill:#FDEBD0,stroke:#F1C40F

    %% 共享外部服务
    subgraph ExternalServices[外部服务]
        subgraph CloudServices[云服务]
            OSS[阿里云OSS<br>存储图片和文件]
            SMS[阿里云短信服务<br>发送验证码]
        end

        subgraph AIServer[AI服务器 AutoDL]
            CLIP[CLIP+FAISS服务<br>端口:8000]
            GPU[GPU资源<br>用于特征提取]
            AutoDLPython[Python环境<br>PyTorch + CUDA]
        end
    end
    style ExternalServices fill:#EBF0F2,stroke:#2C3E50
    style CloudServices fill:#D6DBDF,stroke:#2C3E50
    style AIServer fill:#D6DBDF,stroke:#2C3E50

    %% 开发环境连接关系
    Browser -- HTTP --> ViteDevServer
    ViteDevServer --> FrontendCode
    FrontendCode --> NodeJS
    FrontendCode -- API请求 --> DevSpringBoot
    FrontendCode -- WebSocket --> DevSpringBoot
    DevSpringBoot --> DevTomcat
    DevSpringBoot --> Java
    DevSpringBoot --> Maven
    DevSpringBoot -- JDBC --> DevMySQL
    DevSpringBoot -- Redis协议 --> DevRedis
    DevSpringBoot -- Kafka协议 --> DevKafka
    DevKafka -- ZK协议 --> DevZookeeper
    DevSpringBoot -- RESTful API --> FastAPI
    FastAPI --> PythonEnv
    PythonEnv --> VirtualEnv
    VirtualEnv --> PyTorch
    VirtualEnv --> FAISS

    %% 测试环境连接关系
    Browser -- HTTPS --> TestNginx
    TestNginx -- 文件系统 --> StaticFiles
    TestNginx -- 反向代理 --> TestTomcat
    TestTomcat --> TestSpringBoot
    TestSpringBoot --> TestJava
    TestSpringBoot -- JDBC --> TestMySQL
    TestSpringBoot -- Redis协议 --> TestRedis
    TestSpringBoot -- Kafka协议 --> TestKafka
    TestKafka -- ZK协议 --> TestZookeeper
    TestSpringBoot -- RESTful API --> TestFastAPI
    TestFastAPI --> TestPythonEnv
    TestPythonEnv --> TestVirtualEnv
    TestVirtualEnv --> TestPyTorch
    TestVirtualEnv --> TestFAISS

    %% 外部服务连接
    DevSpringBoot -- HTTPS --> OSS
    TestSpringBoot -- HTTPS --> OSS
    DevSpringBoot -- HTTPS --> SMS
    TestSpringBoot -- HTTPS --> SMS
    DevSpringBoot -- RESTful API --> CLIP
    TestSpringBoot -- RESTful API --> CLIP
    CLIP --> AutoDLPython
    CLIP -- CUDA --> GPU
```
