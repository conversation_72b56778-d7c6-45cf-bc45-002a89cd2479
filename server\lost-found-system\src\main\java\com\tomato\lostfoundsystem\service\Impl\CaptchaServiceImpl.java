package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.service.CaptchaService;
import com.tomato.lostfoundsystem.utils.CaptchaUtil;
import com.tomato.lostfoundsystem.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CaptchaServiceImpl implements CaptchaService {

    @Resource
    private RedisUtil redisUtil;

    @Value("${captcha.cache.enabled:true}")
    private boolean captchaCacheEnabled;

    @Value("${captcha.cache.size:10}")
    private int captchaCacheSize;

    @Value("${captcha.expiration.minutes:5}")
    private int captchaExpirationMinutes;

    // 本地缓存，用于存储预生成的验证码
    private final ConcurrentHashMap<String, CaptchaData> localCaptchaCache = new ConcurrentHashMap<>();

    // 验证码数据类
    private static class CaptchaData {
        private final String imageBase64;
        private final String answer;
        private final long createTime;

        public CaptchaData(String imageBase64, String answer) {
            this.imageBase64 = imageBase64;
            this.answer = answer;
            this.createTime = System.currentTimeMillis();
        }

        public boolean isExpired(int expirationMinutes) {
            return System.currentTimeMillis() - createTime > TimeUnit.MINUTES.toMillis(expirationMinutes);
        }
    }

    @Override
    public Map<String, Object> generateCaptchaImage() throws Exception {
        // 生成UUID作为验证码标识符
        String captchaId = UUID.randomUUID().toString();
        log.info("使用UUID生成验证码: {}", captchaId);

        // 尝试从缓存获取预生成的验证码
        CaptchaData cachedCaptcha = null;
        if (captchaCacheEnabled && !localCaptchaCache.isEmpty()) {
            // 获取一个未过期的缓存验证码
            for (String key : localCaptchaCache.keySet()) {
                CaptchaData data = localCaptchaCache.remove(key);
                if (data != null && !data.isExpired(captchaExpirationMinutes)) {
                    cachedCaptcha = data;
                    break;
                }
            }
        }

        // 准备返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("captchaId", captchaId);

        // 如果没有可用的缓存验证码，则生成新的
        if (cachedCaptcha == null) {
            // 生成验证码问题和答案
            String question = CaptchaUtil.generateCaptchaQuestion();
            int answer = CaptchaUtil.calculateAnswer(question);

            log.info("生成新的验证码 - 问题: {}, 答案: {}, 标识符: {}", question, answer, captchaId);

            // 生成验证码图像
            ByteArrayOutputStream outputStream = CaptchaUtil.generateCaptchaImage(question);

            // 存储到Redis，使用UUID作为键
            String captchaKey = "captcha:answer:" + captchaId;
            redisUtil.set(captchaKey, String.valueOf(answer), captchaExpirationMinutes);
            log.info("验证码已存储到Redis - 键名: {}, 答案: {}, 过期时间: {}分钟", captchaKey, answer, captchaExpirationMinutes);

            // 异步预生成验证码
            if (captchaCacheEnabled) {
                asyncPreGenerateCaptchas();
            }

            // 将图像转换为Base64
            String base64Image = Base64.getEncoder().encodeToString(outputStream.toByteArray());
            result.put("image", base64Image);

            return result;
        } else {
            // 使用缓存的验证码
            log.info("使用缓存的验证码, 标识符: {}", captchaId);

            // 存储到Redis
            String captchaKey = "captcha:answer:" + captchaId;
            redisUtil.set(captchaKey, cachedCaptcha.answer, captchaExpirationMinutes);
            log.info("缓存验证码已存储到Redis - 键名: {}, 答案: {}, 过期时间: {}分钟", captchaKey, cachedCaptcha.answer, captchaExpirationMinutes);

            // 直接使用缓存的Base64图像
            result.put("image", cachedCaptcha.imageBase64);

            return result;
        }
    }

    @Override
    public boolean validateCaptchaAnswer(String userAnswer, String captchaId) {
        if (userAnswer == null || userAnswer.trim().isEmpty()) {
            log.warn("验证码答案为空");
            return false;
        }

        if (captchaId == null || captchaId.trim().isEmpty()) {
            log.warn("验证码标识符为空");
            return false;
        }

        String captchaKey = "captcha:answer:" + captchaId;

        // 从Redis获取存储的答案
        String storedAnswer = redisUtil.get(captchaKey);

        log.info("验证码校验 - 用户输入: {}, 标识符: {}, 键名: {}, 存储答案: {}",
                userAnswer, captchaId, captchaKey, storedAnswer);

        // 验证答案
        boolean isValid = storedAnswer != null && storedAnswer.equals(userAnswer);

        // 验证成功后删除Redis中的答案，防止重复使用
        if (isValid) {
            redisUtil.delete(captchaKey);
            log.info("验证码验证成功，已从Redis删除 - 键名: {}", captchaKey);
        } else {
            if (storedAnswer == null) {
                log.warn("验证码不存在或已过期 - 标识符: {}", captchaId);
            } else {
                log.warn("验证码验证失败 - 用户输入: {}, 存储答案: {}", userAnswer, storedAnswer);
            }
        }

        return isValid;
    }

    /**
     * 异步预生成验证码
     */
    @Async
    public void asyncPreGenerateCaptchas() {
        try {
            // 检查缓存大小，如果已达到上限则不再生成
            if (localCaptchaCache.size() >= captchaCacheSize) {
                return;
            }

            // 计算需要生成的验证码数量
            int generateCount = captchaCacheSize - localCaptchaCache.size();

            if (log.isDebugEnabled()) {
                log.debug("开始预生成 {} 个验证码", generateCount);
            }

            // 预生成验证码
            for (int i = 0; i < generateCount; i++) {
                String question = CaptchaUtil.generateCaptchaQuestion();
                int answer = CaptchaUtil.calculateAnswer(question);

                // 生成验证码图像
                ByteArrayOutputStream outputStream = CaptchaUtil.generateCaptchaImage(question);
                String base64Image = Base64.getEncoder().encodeToString(outputStream.toByteArray());

                // 存入本地缓存
                String cacheKey = UUID.randomUUID().toString();
                localCaptchaCache.put(cacheKey, new CaptchaData(base64Image, String.valueOf(answer)));
            }

            if (log.isDebugEnabled()) {
                log.debug("预生成验证码完成，当前缓存大小: {}", localCaptchaCache.size());
            }
        } catch (Exception e) {
            log.error("预生成验证码失败", e);
        }
    }
}
