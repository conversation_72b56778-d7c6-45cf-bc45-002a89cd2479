-- 系统公告相关表结构和测试数据
-- 适用于lost-found数据库
-- 与后端实现完全兼容

-- 设置外键检查为0，避免外键约束问题
SET FOREIGN_KEY_CHECKS=0;

-- 创建系统公告表
DROP TABLE IF EXISTS `system_announcements`;
CREATE TABLE `system_announcements` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `importance` enum('NORMAL','IMPORTANT','URGENT') DEFAULT 'NORMAL' COMMENT '重要程度',
  `start_time` datetime NOT NULL COMMENT '生效时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `created_by` bigint(20) NOT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` enum('DRAFT','PUBLISHED','EXPIRED') DEFAULT 'DRAFT' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_status_time` (`status`, `start_time`, `end_time`),
  CONSTRAINT `fk_system_announcements_creator` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户公告阅读状态表
DROP TABLE IF EXISTS `user_announcement_reads`;
CREATE TABLE `user_announcement_reads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `announcement_id` bigint(20) NOT NULL COMMENT '公告ID',
  `read_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_announcement` (`user_id`, `announcement_id`),
  KEY `idx_announcement_id` (`announcement_id`),
  CONSTRAINT `fk_user_announcement_reads_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_user_announcement_reads_announcement` FOREIGN KEY (`announcement_id`) REFERENCES `system_announcements` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS=1;

-- 插入测试数据到系统公告表
-- 使用ID为5的用户（testuser1，SUPER_ADMIN）作为创建者

-- 插入紧急公告
INSERT INTO `system_announcements` 
(`title`, `content`, `importance`, `start_time`, `end_time`, `created_by`, `status`) 
VALUES 
('系统维护通知', '尊敬的用户，我们将于2023年12月15日凌晨2:00-4:00进行系统维护，期间系统将暂停服务。给您带来的不便，敬请谅解。', 
'URGENT', '2023-12-10 00:00:00', '2023-12-16 00:00:00', 5, 'PUBLISHED');

-- 插入重要公告
INSERT INTO `system_announcements` 
(`title`, `content`, `importance`, `start_time`, `end_time`, `created_by`, `status`) 
VALUES 
('失物招领平台使用指南', '为了提高失物招领效率，我们更新了平台使用指南。请所有用户仔细阅读，了解如何正确发布失物和招领信息，以及如何使用智能匹配功能。', 
'IMPORTANT', '2023-11-01 00:00:00', NULL, 5, 'PUBLISHED');

-- 插入普通公告
INSERT INTO `system_announcements` 
(`title`, `content`, `importance`, `start_time`, `end_time`, `created_by`, `status`) 
VALUES 
('校园失物招领活动', '我们将于本月20日在学生中心举办校园失物招领专场活动，欢迎所有同学参加。活动现场将展示近期收集的失物，并提供现场认领服务。', 
'NORMAL', '2023-11-15 00:00:00', '2023-12-20 00:00:00', 5, 'PUBLISHED');

-- 插入已过期公告
INSERT INTO `system_announcements` 
(`title`, `content`, `importance`, `start_time`, `end_time`, `created_by`, `status`) 
VALUES 
('系统升级完成通知', '系统升级已于昨日完成，新版本增加了多项功能，包括智能匹配算法优化、界面美化等。感谢您的支持与理解。', 
'NORMAL', '2023-10-01 00:00:00', '2023-10-31 00:00:00', 5, 'PUBLISHED');

-- 插入草稿状态公告
INSERT INTO `system_announcements` 
(`title`, `content`, `importance`, `start_time`, `end_time`, `created_by`, `status`) 
VALUES 
('新功能预告', '我们即将推出全新的移动端应用，敬请期待！', 
'IMPORTANT', '2024-01-01 00:00:00', NULL, 5, 'DRAFT');

-- 插入未来生效的公告
INSERT INTO `system_announcements` 
(`title`, `content`, `importance`, `start_time`, `end_time`, `created_by`, `status`) 
VALUES 
('2024年春季学期失物招领须知', '新学期开始，请大家注意保管好自己的物品。如有丢失，请及时在平台发布信息。', 
'NORMAL', '2024-02-01 00:00:00', NULL, 5, 'PUBLISHED');

-- 插入长文本公告
INSERT INTO `system_announcements` 
(`title`, `content`, `importance`, `start_time`, `end_time`, `created_by`, `status`) 
VALUES 
('关于加强校园失物招领管理的通知', 
'为进一步规范校园失物招领管理，提高服务质量，特制定以下规定：\n\n
1. 所有拾获的物品应在24小时内上交至失物招领中心或在平台发布信息；\n
2. 贵重物品（如手机、电脑、钱包等）必须由管理员验证后才能发布；\n
3. 认领物品时，认领人需提供有效证件和物品特征描述；\n
4. 发布虚假信息者将被平台禁言处理；\n
5. 平台保留对违规行为进行处理的权利。\n\n
请所有用户遵守以上规定，共同维护良好的校园环境。', 
'IMPORTANT', '2023-11-10 00:00:00', NULL, 5, 'PUBLISHED');

-- 插入当前有效的紧急公告
INSERT INTO `system_announcements` 
(`title`, `content`, `importance`, `start_time`, `end_time`, `created_by`, `status`) 
VALUES 
('重要安全提醒', '近期校园内出现多起财物丢失事件，请同学们妥善保管个人物品，特别是在图书馆、食堂等公共场所。', 
'URGENT', CURRENT_DATE(), DATE_ADD(CURRENT_DATE(), INTERVAL 7 DAY), 5, 'PUBLISHED');

-- 插入一些用户公告阅读记录
-- 使用ID为7和8的用户（王小明和wangyang）

-- 用户7已读了ID为1的公告
INSERT INTO `user_announcement_reads` (`user_id`, `announcement_id`) 
VALUES (7, 1);

-- 用户7已读了ID为2的公告
INSERT INTO `user_announcement_reads` (`user_id`, `announcement_id`) 
VALUES (7, 2);

-- 用户8已读了ID为1的公告
INSERT INTO `user_announcement_reads` (`user_id`, `announcement_id`) 
VALUES (8, 1);
