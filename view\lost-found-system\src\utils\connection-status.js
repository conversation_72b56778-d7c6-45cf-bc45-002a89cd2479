/**
 * 连接状态管理模块
 *
 * 这个模块负责管理WebSocket连接状态，并提供统一的接口给其他模块使用。
 * 它降低了各模块之间的耦合度，使用事件总线模式进行通信。
 *
 * 使用Pinia Store集中管理状态，避免状态管理混乱和循环依赖
 */
import { useOnlineStatusStore } from '../stores'

// 事件名称常量
export const CONNECTION_EVENTS = {
  CONNECTED: 'connection-status-connected',
  DISCONNECTED: 'connection-status-disconnected',
  CONNECTING: 'connection-status-connecting'
}

/**
 * 设置连接状态
 * @param {boolean} isConnected 是否已连接
 * @param {boolean} isConnecting 是否正在连接
 */
export function setConnectionStatus(isConnected, isConnecting = false) {
  const store = useOnlineStatusStore()

  // 获取当前状态
  const oldConnected = store.isConnected
  const oldConnecting = store.isConnecting

  // 更新状态
  store.updateConnectionStatus(isConnected, isConnecting)

  // 只有状态变化时才触发事件
  if (oldConnected !== isConnected || oldConnecting !== isConnecting) {
    let eventName = ''
    if (isConnected) {
      eventName = CONNECTION_EVENTS.CONNECTED
    } else if (isConnecting) {
      eventName = CONNECTION_EVENTS.CONNECTING
    } else {
      eventName = CONNECTION_EVENTS.DISCONNECTED
    }

    // 创建自定义事件，包含更多信息
    const event = new CustomEvent(eventName, {
      detail: {
        connected: isConnected,
        connecting: isConnecting,
        event: eventName,
        timestamp: new Date().getTime()
      }
    })

    window.dispatchEvent(event)
  }
}

/**
 * 获取当前连接状态
 * @returns {Object} 连接状态对象
 */
export function getConnectionStatus() {
  const store = useOnlineStatusStore()
  return {
    connected: store.isConnected,
    connecting: store.isConnecting
  }
}

/**
 * 检查是否已连接
 * @returns {boolean} 是否已连接
 */
export function isConnected() {
  const store = useOnlineStatusStore()
  return store.isConnected
}

/**
 * 检查是否正在连接
 * @returns {boolean} 是否正在连接
 */
export function isConnecting() {
  const store = useOnlineStatusStore()
  return store.isConnecting
}

/**
 * 添加连接状态变化监听器
 * @param {Function} callback 回调函数
 */
export function addConnectionListener(callback) {
  window.addEventListener(CONNECTION_EVENTS.CONNECTED, callback)
  window.addEventListener(CONNECTION_EVENTS.DISCONNECTED, callback)
  window.addEventListener(CONNECTION_EVENTS.CONNECTING, callback)
}

/**
 * 移除连接状态变化监听器
 * @param {Function} callback 回调函数
 */
export function removeConnectionListener(callback) {
  window.removeEventListener(CONNECTION_EVENTS.CONNECTED, callback)
  window.removeEventListener(CONNECTION_EVENTS.DISCONNECTED, callback)
  window.removeEventListener(CONNECTION_EVENTS.CONNECTING, callback)
}

// 导出默认对象
export default {
  setConnectionStatus,
  getConnectionStatus,
  isConnected,
  isConnecting,
  addConnectionListener,
  removeConnectionListener,
  CONNECTION_EVENTS
}
