package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.MatchHistory;
import com.tomato.lostfoundsystem.service.IntelligentMatchService;
import com.tomato.lostfoundsystem.utils.JWTUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 智能匹配控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/match")
public class IntelligentMatchController {

    @Autowired
    private IntelligentMatchService intelligentMatchService;

    /**
     * 使用图片进行智能匹配
     *
     * @param file 图片文件
     * @param itemType 物品类型（LOST表示查找拾物，FOUND表示查找失物）
     * @param request HTTP请求
     * @return 匹配结果
     */
    @PostMapping("/image")
    public Result<Map<String, Object>> matchByImage(
            @RequestParam("file") MultipartFile file,
            @RequestParam("itemType") String itemType,
            HttpServletRequest request) {
        try {
            log.info("接收到图片匹配请求，物品类型: {}", itemType);
            Long userId = JWTUtil.getUserId(request);

            if (userId == null) {
                return Result.fail("用户未登录");
            }

            return intelligentMatchService.matchByImage(userId, file, itemType);
        } catch (Exception e) {
            log.error("图片匹配请求处理失败", e);
            return Result.fail("匹配失败: " + e.getMessage());
        }
    }

    /**
     * 使用文本描述进行智能匹配
     *
     * @param params 请求参数
     * @param request HTTP请求
     * @return 匹配结果
     */
    @PostMapping("/text")
    public Result<Map<String, Object>> matchByText(
            @RequestBody Map<String, String> params,
            HttpServletRequest request) {
        try {
            String itemName = params.get("itemName");
            String itemDescription = params.get("itemDescription");
            String itemType = params.get("itemType");

            // 构建结构化描述
            StringBuilder description = new StringBuilder();
            if (itemName != null && !itemName.isEmpty()) {
                description.append(itemName).append(" ");
            }
            if (itemDescription != null && !itemDescription.isEmpty()) {
                description.append(itemDescription);
            }

            String finalDescription = description.toString().trim();

            log.info("接收到文本匹配请求，物品类型: {}, 物品名称: {}, 物品描述: {}, 最终描述: {}",
                    itemType, itemName, itemDescription, finalDescription);

            Long userId = JWTUtil.getUserId(request);

            if (userId == null) {
                return Result.fail("用户未登录");
            }

            return intelligentMatchService.matchByText(userId, finalDescription, itemType);
        } catch (Exception e) {
            log.error("文本匹配请求处理失败", e);
            return Result.fail("匹配失败: " + e.getMessage());
        }
    }

    /**
     * 使用图片和文本混合进行智能匹配
     *
     * @param file 图片文件
     * @param itemName 物品名称
     * @param itemDescription 物品描述
     * @param itemType 物品类型（LOST表示查找拾物，FOUND表示查找失物）
     * @param request HTTP请求
     * @return 匹配结果
     */
    @PostMapping("/mixed")
    public Result<Map<String, Object>> matchByMixed(
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam(value = "itemName", required = false) String itemName,
            @RequestParam(value = "itemDescription", required = false) String itemDescription,
            @RequestParam("itemType") String itemType,
            HttpServletRequest request) {
        try {
            // 构建结构化描述
            StringBuilder description = new StringBuilder();
            if (itemName != null && !itemName.isEmpty()) {
                description.append(itemName).append(" ");
            }
            if (itemDescription != null && !itemDescription.isEmpty()) {
                description.append(itemDescription);
            }

            String finalDescription = description.toString().trim();

            log.info("接收到混合匹配请求，物品类型: {}, 物品名称: {}, 物品描述: {}, 最终描述: {}",
                    itemType, itemName, itemDescription, finalDescription);

            Long userId = JWTUtil.getUserId(request);

            if (userId == null) {
                return Result.fail("用户未登录");
            }

            return intelligentMatchService.matchByMixed(userId, file, finalDescription, itemType);
        } catch (Exception e) {
            log.error("混合匹配请求处理失败", e);
            return Result.fail("匹配失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的匹配历史记录
     *
     * @param request HTTP请求
     * @return 匹配历史记录列表
     */
    @GetMapping("/history")
    public Result<List<MatchHistory>> getMatchHistory(HttpServletRequest request) {
        try {
            Long userId = JWTUtil.getUserId(request);

            if (userId == null) {
                return Result.fail("用户未登录");
            }

            return intelligentMatchService.getMatchHistory(userId);
        } catch (Exception e) {
            log.error("获取匹配历史记录失败", e);
            return Result.fail("获取匹配历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取匹配历史详情
     *
     * @param id 匹配历史ID
     * @return 匹配历史详情
     */
    @GetMapping("/history/{id}")
    public Result<MatchHistory> getMatchHistoryDetail(@PathVariable("id") Long id) {
        try {
            return intelligentMatchService.getMatchHistoryDetail(id);
        } catch (Exception e) {
            log.error("获取匹配历史详情失败", e);
            return Result.fail("获取匹配历史详情失败: " + e.getMessage());
        }
    }

    /**
     * 为所有物品生成特征向量（仅管理员可用）
     *
     * @return 处理结果
     */
    @PostMapping("/admin/generate-vectors")
    public Result<String> generateFeatureVectors(HttpServletRequest request) {
        try {
            // 验证管理员权限
            String role = JWTUtil.getUserRole(request);
            if (!"ADMIN".equals(role) && !"SUPER_ADMIN".equals(role)) {
                return Result.fail("权限不足");
            }

            return intelligentMatchService.generateFeatureVectorsForAllItems();
        } catch (Exception e) {
            log.error("生成特征向量失败", e);
            return Result.fail("生成特征向量失败: " + e.getMessage());
        }
    }

    /**
     * 触发物品匹配
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @param request HTTP请求
     * @return 匹配结果
     */
    @PostMapping("/trigger")
    public Result<Map<String, Object>> triggerItemMatch(
            @RequestParam("itemId") Long itemId,
            @RequestParam("itemType") String itemType,
            HttpServletRequest request) {
        try {
            log.info("接收到触发匹配请求，物品ID: {}, 物品类型: {}", itemId, itemType);

            // 获取当前用户ID
            Long userId = JWTUtil.getUserId(request);
            if (userId == null) {
                return Result.fail("用户未登录");
            }

            // 调用服务层方法执行匹配
            return intelligentMatchService.performAutoMatch(userId, itemId, itemType);
        } catch (Exception e) {
            log.error("触发物品匹配失败", e);
            return Result.fail("触发匹配失败: " + e.getMessage());
        }
    }
}
