-- 使用lost_found数据库
USE lost_found;

-- 显示所有表
SHOW TABLES;

-- 检查match_notifications表是否已存在
SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'lost_found' AND table_name = 'match_notifications';

-- 创建system_configs表（如果不存在）
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 添加配置项
INSERT IGNORE INTO system_configs (config_key, config_value, description, created_at, updated_at)
VALUES 
('match.notification.similarity-threshold', '0.7', '触发匹配通知的相似度阈值', NOW(), NOW()),
('match.notification.auto-notify', 'true', '是否启用自动匹配通知功能', NOW(), NOW());

-- 创建match_history表（如果不存在）
CREATE TABLE IF NOT EXISTS match_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    query_type VARCHAR(20) NOT NULL COMMENT '查询类型（TEXT/IMAGE/MIXED）',
    query_image_url VARCHAR(255) COMMENT '查询图片URL',
    query_text TEXT COMMENT '查询文本',
    item_type VARCHAR(10) NOT NULL COMMENT '物品类型（LOST/FOUND）',
    result_count INT DEFAULT 0 COMMENT '结果数量',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配历史表';

-- 创建match_results表（如果不存在）
CREATE TABLE IF NOT EXISTS match_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    match_history_id BIGINT NOT NULL COMMENT '匹配历史ID',
    item_id BIGINT NOT NULL COMMENT '物品ID',
    item_type VARCHAR(10) NOT NULL COMMENT '物品类型（LOST/FOUND）',
    similarity_score DECIMAL(10,4) NOT NULL COMMENT '相似度分数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_match_history_id (match_history_id),
    INDEX idx_item_id_type (item_id, item_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配结果表';

-- 创建item_feature_vectors表（如果不存在）
CREATE TABLE IF NOT EXISTS item_feature_vectors (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    item_id BIGINT NOT NULL COMMENT '物品ID',
    item_type VARCHAR(10) NOT NULL COMMENT '物品类型（LOST/FOUND）',
    text_vector BLOB COMMENT '文本特征向量',
    image_vector BLOB COMMENT '图像特征向量',
    vector_version VARCHAR(20) COMMENT '向量版本',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_item_id_type (item_id, item_type),
    INDEX idx_item_type (item_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物品特征向量表';

-- 创建match_notifications表（如果不存在）
CREATE TABLE IF NOT EXISTS match_notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '接收通知的用户ID',
    match_history_id BIGINT COMMENT '匹配历史ID',
    item_id BIGINT NOT NULL COMMENT '匹配物品ID',
    item_type VARCHAR(10) NOT NULL COMMENT '匹配物品类型（LOST/FOUND）',
    similarity FLOAT NOT NULL COMMENT '匹配相似度',
    title VARCHAR(100) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_match_history_id (match_history_id),
    INDEX idx_item_id_type (item_id, item_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配通知表';

-- 显示所有表（确认更改）
SHOW TABLES;
