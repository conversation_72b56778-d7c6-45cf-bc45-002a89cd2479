<template>
  <div>
    <!-- 图表容器 -->
    <div ref="chartContainer" class="radar-chart-container" :style="{ width: width, height: height }"></div>

    <!-- 简洁模式下只显示图标，点击后显示弹出框 -->
    <div v-if="compact" class="radar-chart-icon">
      <el-popover
        placement="top"
        :width="320"
        trigger="click"
        popper-class="radar-chart-popover"
        @show="handlePopoverShow"
        :teleported="true"
      >
        <template #reference>
          <el-button type="primary" circle size="small" class="chart-icon-btn">
            <el-icon><DataAnalysis /></el-icon>
          </el-button>
        </template>
        <div class="popover-chart-container">
          <div class="popover-title">{{ title }}</div>
          <div ref="popoverChartContainer" class="popover-chart" style="width: 100%; height: 220px;"></div>
          <div class="similarity-details">
            <div class="similarity-item">
              <span class="label">图像-图像:</span>
              <span class="value">{{ (parseFloat(similarities.image_to_image) * 100).toFixed(0) }}%</span>
            </div>
            <div class="similarity-item">
              <span class="label">图像-文本:</span>
              <span class="value">{{ (parseFloat(similarities.image_to_text) * 100).toFixed(0) }}%</span>
            </div>
            <div class="similarity-item">
              <span class="label">文本-图像:</span>
              <span class="value">{{ (parseFloat(similarities.text_to_image) * 100).toFixed(0) }}%</span>
            </div>
            <div class="similarity-item">
              <span class="label">文本-文本:</span>
              <span class="value">{{ (parseFloat(similarities.text_to_text) * 100).toFixed(0) }}%</span>
            </div>
          </div>
        </div>
      </el-popover>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount, nextTick } from 'vue'
import * as echarts from 'echarts/core'
import { RadarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { DataAnalysis } from '@element-plus/icons-vue'

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  RadarChart,
  CanvasRenderer
])

const props = defineProps({
  // 相似度数据
  similarities: {
    type: Object,
    required: true,
    default: () => ({
      image_to_image: 0,
      image_to_text: 0,
      text_to_image: 0,
      text_to_text: 0
    })
  },
  // 图表宽度
  width: {
    type: String,
    default: '100%'
  },
  // 图表高度
  height: {
    type: String,
    default: '200px'
  },
  // 图表标题
  title: {
    type: String,
    default: '匹配度分析'
  },
  // 是否使用紧凑模式（只显示图标）
  compact: {
    type: Boolean,
    default: false
  }
})

// 图表容器引用
const chartContainer = ref(null)
const popoverChartContainer = ref(null)
// 图表实例
let chartInstance = null
let popoverChartInstance = null

// 初始化图表
const initChart = () => {
  // 如果不是紧凑模式，初始化主图表
  if (!props.compact && chartContainer.value) {
    // 创建图表实例
    chartInstance = echarts.init(chartContainer.value)
    // 更新图表
    updateChart(chartInstance)
  }
}

// 初始化弹出框图表
const initPopoverChart = () => {
  try {
    // 检查容器是否存在
    if (!popoverChartContainer.value) {
      console.warn('弹出框图表容器不存在，无法初始化图表');
      return;
    }

    // 检查容器尺寸
    const containerWidth = popoverChartContainer.value.clientWidth;
    const containerHeight = popoverChartContainer.value.clientHeight;

    if (containerWidth <= 0 || containerHeight <= 0) {
      console.warn(`弹出框图表容器尺寸异常: ${containerWidth}x${containerHeight}，无法初始化图表`);
      return;
    }

    // 如果已经有实例，先销毁
    if (popoverChartInstance) {
      popoverChartInstance.dispose();
      popoverChartInstance = null;
    }

    // 创建图表实例
    console.log(`创建弹出框图表实例，容器尺寸: ${containerWidth}x${containerHeight}`);
    popoverChartInstance = echarts.init(popoverChartContainer.value);

    // 更新图表
    updateChart(popoverChartInstance);
  } catch (error) {
    console.error('初始化弹出框图表失败:', error);
  }
}

// 更新图表数据
const updateChart = (chart) => {
  if (!chart) return

  // 准备数据
  const { image_to_image, image_to_text, text_to_image, text_to_text } = props.similarities

  // 图表配置
  const option = {
    title: props.compact ? null : {
      text: props.title,
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#303133'
      },
      left: 'center',
      top: 5,
      padding: [0, 0, 5, 0]
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return '相似度详情<br/>' +
               '图像-图像: ' + (parseFloat(params.value[0]) * 100).toFixed(0) + '%<br/>' +
               '图像-文本: ' + (parseFloat(params.value[1]) * 100).toFixed(0) + '%<br/>' +
               '文本-图像: ' + (parseFloat(params.value[2]) * 100).toFixed(0) + '%<br/>' +
               '文本-文本: ' + (parseFloat(params.value[3]) * 100).toFixed(0) + '%'
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'rgba(50, 50, 50, 0.9)',
      textStyle: {
        color: '#fff'
      }
    },
    radar: {
      indicator: [
        { name: '图像-图像', max: 1 },
        { name: '图像-文本', max: 1 },
        { name: '文本-图像', max: 1 },
        { name: '文本-文本', max: 1 }
      ],
      radius: '65%',
      center: ['50%', '55%'],
      splitNumber: 4,
      nameGap: 6,
      name: {
        textStyle: {
          color: '#606266',
          fontSize: 11
        }
      },
      splitArea: {
        areaStyle: {
          color: ['rgba(64, 158, 255, 0.03)', 'rgba(64, 158, 255, 0.07)', 'rgba(64, 158, 255, 0.1)', 'rgba(64, 158, 255, 0.15)'],
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowBlur: 5
        }
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(64, 158, 255, 0.4)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(64, 158, 255, 0.2)'
        }
      }
    },
    series: [
      {
        name: '相似度',
        type: 'radar',
        data: [
          {
            value: [
              parseFloat(image_to_image) || 0,
              parseFloat(image_to_text) || 0,
              parseFloat(text_to_image) || 0,
              parseFloat(text_to_text) || 0
            ],
            name: '相似度分数',
            areaStyle: {
              color: 'rgba(64, 158, 255, 0.5)'
            },
            lineStyle: {
              color: 'rgba(64, 158, 255, 0.8)',
              width: 2
            },
            itemStyle: {
              color: 'rgba(64, 158, 255, 1)'
            }
          }
        ]
      }
    ]
  }

  // 设置图表配置
  chart.setOption(option)
}

// 监听相似度数据变化
watch(() => props.similarities, () => {
  if (chartInstance) {
    updateChart(chartInstance)
  }
  if (popoverChartInstance) {
    updateChart(popoverChartInstance)
  }
}, { deep: true })

// 组件挂载后初始化图表
onMounted(() => {
  initChart()

  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
    if (popoverChartInstance) {
      popoverChartInstance.resize()
    }
  })
})

// 组件卸载前销毁图表实例
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }

  if (popoverChartInstance) {
    popoverChartInstance.dispose()
    popoverChartInstance = null
  }

  // 移除事件监听
  window.removeEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
    if (popoverChartInstance) {
      popoverChartInstance.resize()
    }
  })
})

// 监听弹出框的显示状态
const handlePopoverShow = () => {
  // 使用多个nextTick和延迟确保DOM已完全渲染
  nextTick(() => {
    // 添加一个更长的延迟，确保DOM已完全渲染
    setTimeout(() => {
      try {
        if (popoverChartContainer.value) {
          console.log('初始化弹出框图表');
          initPopoverChart();
        } else {
          console.warn('弹出框图表容器未找到，延迟重试');
          // 如果容器还没准备好，再次延迟尝试
          setTimeout(() => {
            if (popoverChartContainer.value) {
              console.log('延迟后初始化弹出框图表');
              initPopoverChart();
            } else {
              console.error('弹出框图表容器仍未找到');
            }
          }, 200);
        }
      } catch (error) {
        console.error('初始化弹出框图表时出错:', error);
      }
    }, 100);
  });
}
</script>

<style scoped>
.radar-chart-container {
  width: 100%;
  height: 200px;
  padding-top: 5px;
  position: relative;
}

.radar-chart-icon {
  display: inline-block;
  margin-left: 4px;
  vertical-align: middle;
}

.chart-icon-btn {
  padding: 4px;
  font-size: 12px;
  background-color: #409EFF;
  border-color: #409EFF;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-icon-btn:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.popover-chart-container {
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
}

.popover-title {
  text-align: center;
  margin-top: 0;
  margin-bottom: 8px;
  color: #303133;
  font-weight: bold;
  font-size: 14px;
}

.similarity-details {
  margin-top: 8px;
  border-top: 1px solid #ebeef5;
  padding-top: 8px;
}

.similarity-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
  padding: 2px 0;
}

.similarity-item .label {
  color: #606266;
}

.similarity-item .value {
  font-weight: bold;
  color: #409EFF;
}

/* 自定义弹出框样式 */
:deep(.radar-chart-popover) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
}
</style>
