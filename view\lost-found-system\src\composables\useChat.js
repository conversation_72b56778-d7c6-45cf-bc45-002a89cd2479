/**
 * 聊天功能相关的组合式函数
 * 提供聊天消息处理、发送和接收等功能
 */
import { ref, computed, nextTick } from 'vue'
import { useUserStore } from '@/stores'
import { ElMessage } from 'element-plus'
import logger from '@/utils/logger'
import { debounce } from 'lodash-es'

// 创建聊天日志记录器
const chatLogger = logger.createLogger('Chat')

/**
 * 聊天功能组合式函数
 * @param {Object} options 配置选项
 * @returns {Object} 聊天相关的状态和方法
 */
export function useChat(options = {}) {
  // 当前用户信息
  const userStore = useUserStore()
  const userInfo = computed(() => userStore.userInfo || {})

  // 聊天状态
  const messages = ref([])
  const currentContact = ref(null)
  const sending = ref(false)
  const loading = ref(false)
  const loadingMore = ref(false)
  const noMoreMessages = ref(false)
  const inputMessage = ref('')
  const messagesContainer = ref(null)

  // 网络状态
  const networkStatus = ref({
    connected: navigator.onLine,
    reconnecting: false,
    attempts: 0,
    lastConnected: null
  })

  /**
   * 发送文本消息
   * @param {Object} options 消息选项
   * @returns {Promise<Object>} 发送结果
   */
  const sendTextMessage = async (options = {}) => {
    if (!currentContact.value || !userInfo.value.id) {
      ElMessage.error('无法发送消息，请检查联系人和用户信息')
      return { success: false }
    }

    if (sending.value) {
      chatLogger.warn('消息正在发送中，请稍候再试')
      return { success: false }
    }

    const message = options.message || inputMessage.value
    if (!message.trim()) {
      chatLogger.warn('消息内容不能为空')
      return { success: false }
    }

    try {
      sending.value = true

      // 创建临时消息ID
      const tempId = `temp-${Date.now()}`

      // 添加临时消息到聊天窗口
      messages.value.push({
        senderId: userInfo.value.id,
        receiverId: currentContact.value.id,
        message: message.trim(),
        content: message.trim(), // 兼容前端显示
        messageType: 'TEXT',
        time: new Date().toISOString(),
        timestamp: new Date().toISOString(),
        isSelf: true,
        id: tempId,
        status: 'SENDING',
        isRead: false,
        clientMessageId: tempId
      })

      // 清空输入框
      if (!options.keepInput) {
        inputMessage.value = ''
      }

      // 滚动到底部
      await scrollToBottom()

      // 检查网络连接状态
      try {
        const { isWebSocketConnected } = await import('@/utils/websocket/index')
        const connected = await isWebSocketConnected()
        if (!connected) {
          ElMessage.error('网络连接已断开，无法发送消息')
          return { success: false }
        }
      } catch (error) {
        chatLogger.error('检查WebSocket连接状态失败:', error)
        ElMessage.error('网络连接状态检查失败，无法发送消息')
        return { success: false }
      }

      // 准备消息数据
      const messageDTO = {
        senderId: userInfo.value.id,
        receiverId: currentContact.value.id,
        message: message.trim(),
        messageType: 'TEXT',
        clientMessageId: tempId,
        timestamp: new Date().getTime()
      }

      // 发送消息
      try {
        const { sendChatMessage } = await import('@/utils/websocket/index')
        const result = await sendChatMessage(messageDTO)

        if (result && result.code === 200) {
          // 更新消息状态
          updateMessageStatus(tempId, {
            id: result.data?.id || tempId,
            time: result.data?.timestamp || new Date().toISOString()
          })

          return { success: true, data: result.data }
        } else {
          // 更新消息状态为发送失败
          updateMessageStatus(tempId, {
            errorMessage: result?.message || '发送失败'
          })

          return { success: false, error: result?.message || '发送失败' }
        }
      } catch (error) {
        chatLogger.error('发送消息失败:', error)

        // 更新消息状态为发送失败
        updateMessageStatus(tempId, {
          errorMessage: error.message || '发送失败'
        })

        return { success: false, error: error.message || '发送失败' }
      }
    } finally {
      sending.value = false
    }
  }

  /**
   * 更新消息状态
   * @param {string} messageId 消息ID
   * @param {Object} updates 更新内容
   */
  const updateMessageStatus = (messageId, updates = {}) => {
    const index = messages.value.findIndex(m => m.id === messageId)
    if (index === -1) return

    const message = messages.value[index]

    // 更新消息
    messages.value[index] = {
      ...message,
      ...updates,
      status: updates.errorMessage ? 'ERROR' : (updates.status || message.status || 'SENT')
    }
  }

  /**
   * 滚动到底部
   */
  const scrollToBottom = async () => {
    await nextTick()
    if (messagesContainer.value) {
      const scrollbar = messagesContainer.value.$el?.querySelector('.el-scrollbar__wrap')
      if (scrollbar) {
        scrollbar.scrollTop = scrollbar.scrollHeight
      }
    }
  }

  /**
   * 加载聊天历史
   * @param {string|number} contactId 联系人ID
   * @param {Object} options 加载选项
   */
  const loadChatHistory = async (contactId, options = {}) => {
    if (!contactId) return

    try {
      const isLoadMore = options.loadMore === true

      if (!isLoadMore) {
        loading.value = true
      } else {
        loadingMore.value = true
      }

      chatLogger.info(`开始加载聊天历史, 联系人ID: ${contactId}, 加载更多: ${isLoadMore}`)

      // 获取当前用户ID
      const userId = userInfo.value.id
      if (!userId) {
        throw new Error('用户未登录')
      }

      // 获取分页参数
      const page = isLoadMore && messages.value.length > 0 ? Math.floor(messages.value.length / 20) + 1 : 0
      const size = 20

      // 导入API函数
      const { getChatHistory } = await import('@/api/chat')

      // 调用API获取聊天历史
      const data = await getChatHistory(userId, contactId, page, size)

      if (data.code === 200 && Array.isArray(data.data)) {
        // 格式化消息
        const historyMessages = data.data.map(msg => formatMessage(msg))

        // 如果是加载更多，添加到消息列表前面
        if (isLoadMore) {
          messages.value = [...historyMessages, ...messages.value]

          // 如果返回的消息数量小于请求的数量，说明没有更多消息了
          noMoreMessages.value = historyMessages.length < size
        } else {
          // 否则替换消息列表
          messages.value = historyMessages
          noMoreMessages.value = historyMessages.length < size
        }

        chatLogger.info(`聊天历史加载成功, 消息数量: ${historyMessages.length}`)

        // 如果不是加载更多，滚动到底部
        if (!isLoadMore) {
          await scrollToBottom()
        }

        return historyMessages
      } else {
        chatLogger.warn('加载聊天历史返回异常:', data)
        if (!isLoadMore) {
          // 如果不是加载更多，清空消息列表
          messages.value = []
        }
        return []
      }
    } catch (error) {
      chatLogger.error('加载聊天历史失败:', error)
      ElMessage.error('加载聊天历史失败: ' + error.message)
      return []
    } finally {
      loading.value = false
      loadingMore.value = false
    }
  }

  /**
   * 格式化消息
   * @param {Object} message 原始消息
   * @returns {Object} 格式化后的消息
   */
  const formatMessage = (message) => {
    try {
      if (!message) {
        return {
          id: `temp-${Date.now()}`,
          content: '[无效消息]',
          message: '[无效消息]',
          time: new Date().toISOString(),
          timestamp: new Date().toISOString(),
          isSelf: false,
          messageType: 'TEXT'
        }
      }

      // 处理时间戳
      let formattedTime = message.time || message.timestamp || new Date().toISOString()

      // 确保消息有所有必要的属性
      return {
        ...message,
        id: message.id || message.messageId || `temp-${Date.now()}`,
        // 同时保存content和message字段，兼容不同组件的显示需求
        content: message.content || message.message || '[空消息]',
        message: message.message || message.content || '[空消息]',
        senderId: message.senderId || message.sender || userInfo.value.id,
        receiverId: message.receiverId || message.receiver || currentContact.value?.id,
        messageType: message.messageType || 'TEXT',
        // 同时保存time和timestamp字段，兼容不同组件的显示需求
        time: formattedTime,
        timestamp: formattedTime,
        isSelf: message.isSelf !== undefined ? message.isSelf :
                (String(message.senderId) === String(userInfo.value.id) ||
                 String(message.sender) === String(userInfo.value.id)),
        // 处理文件相关字段
        fileUrl: message.fileUrl || (message.attachment ? message.attachment.fileUrl : null),
        fileSize: message.fileSize || (message.attachment ? message.attachment.fileSize : null),
        // 保留客户端消息ID
        clientMessageId: message.clientMessageId || message.id
      }
    } catch (error) {
      chatLogger.error('格式化消息时出错:', error)
      return {
        id: `error-${Date.now()}`,
        content: '[消息格式错误]',
        message: '[消息格式错误]',
        time: new Date().toISOString(),
        timestamp: new Date().toISOString(),
        isSelf: false,
        messageType: 'TEXT'
      }
    }
  }

  // 创建防抖版本的函数
  const debouncedScrollToBottom = debounce(scrollToBottom, 100)

  return {
    // 状态
    messages,
    currentContact,
    sending,
    loading,
    loadingMore,
    noMoreMessages,
    inputMessage,
    messagesContainer,
    networkStatus,

    // 方法
    sendTextMessage,
    updateMessageStatus,
    scrollToBottom,
    debouncedScrollToBottom,
    loadChatHistory,
    formatMessage,

    // 日志
    chatLogger
  }
}

export default useChat
