-- 创建系统配置表
CREATE TABLE IF NOT EXISTS `system_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` varchar(500) DEFAULT NULL COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入初始配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `description`) VALUES
('autodl.clip.api.url', 'http://localhost:8000', '智能匹配服务API地址'),
('autodl.clip.service.check-enabled', 'true', '是否启用智能匹配服务检查'),
('autodl.clip.service.connection-timeout', '3000', '智能匹配服务连接超时时间(毫秒)'),
('autodl.clip.service.script-path', './clip_faiss_service/start_clip_service.sh', '智能匹配服务启动脚本路径');
