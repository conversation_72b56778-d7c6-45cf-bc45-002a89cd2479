package com.tomato.lostfoundsystem.interceptors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.annotation.RequireToken;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.utils.JWTUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Method;

@Slf4j
@Component
public class TokenInterceptor implements HandlerInterceptor {

    @Autowired
    private JWTUtil jwtUtil;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 1. 如果不是映射到方法（静态资源等），直接放行
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        // 2. 检查方法上是否有 @RequireToken 注解，且 required 为 true
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        RequireToken requireToken = method.getAnnotation(RequireToken.class);

        if (requireToken == null || !requireToken.required()) {
            return true; // 没有注解或不要求校验，放行
        }

        // 3. 获取 Authorization 头部，校验 Bearer Token
        String authHeader = request.getHeader("Authorization");
        log.info("拦截器拦截的请求头{}",authHeader);
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            log.warn("缺少或格式错误的 Authorization 头部");
            handleError(response, "未授权的请求，请先登录");
            return false;
        }

        String token = authHeader.substring(7).trim(); // 去除 Bearer 前缀
        // 4. 尝试解析 token
        Claims claims = jwtUtil.parseToken(token);
        if (claims == null) {
            log.error("JWT 解析失败，非法或过期的 token");
            handleError(response, "无效的 token 或已过期，请重新登录");
            return false;
        }

        // 5. 获取用户 ID 并注入到 request 中
        Long userId = claims.get("userId", Long.class);
        if (userId == null) {
            log.warn("Token 中未包含 userId");
            handleError(response, "token 无效，缺少用户信息");
            return false;
        }

        // ✅ 将 userId 存入 request 供后续使用
        request.setAttribute("userId", userId);
        return true;
    }

    /**
     * 拦截时返回统一错误响应
     */
    private void handleError(HttpServletResponse response, String message) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED); // 401
        response.getWriter().write(new ObjectMapper().writeValueAsString(Result.fail(message)));
    }
}
