package com.tomato.lostfoundsystem.controller;

import jakarta.mail.MessagingException;
import lombok.RequiredArgsConstructor;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.web.bind.annotation.*;

import jakarta.mail.internet.MimeMessage;

@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
public class EmailTestController {

    private final JavaMailSender javaMailSender;

    @GetMapping("/send-email")
    public String sendEmail(@RequestParam String to) {
        try {
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setFrom("<EMAIL>"); // 替换为你的邮箱
            helper.setTo(to);
            helper.setSubject("测试邮件");
            helper.setText("这是一个来自校园失物招领系统的测试邮件", true);

            javaMailSender.send(message);
            return "发送成功！请检查收件箱或垃圾箱";
        } catch (MessagingException e) {
            e.printStackTrace();
            return "发送失败：" + e.getMessage();
        }
    }
}

