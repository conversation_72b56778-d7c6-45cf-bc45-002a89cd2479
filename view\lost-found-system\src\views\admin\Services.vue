<template>
  <div class="admin-services">
    <div class="page-header">
      <h2>系统服务管理</h2>
      <p class="description">
        在这里您可以监控和管理系统中的各项服务，包括智能匹配服务、消息队列等。
      </p>
    </div>
    
    <ServiceStatus />
    
    <el-card class="service-docs" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>服务管理指南</h3>
        </div>
      </template>
      
      <div class="docs-content">
        <h4>智能匹配服务</h4>
        <p>
          智能匹配服务基于CLIP+FAISS技术，用于图像和文本的特征提取和相似度匹配。该服务需要在AutoDL服务器上运行，
          通过API与主系统进行通信。
        </p>
        
        <h4>服务状态说明</h4>
        <ul>
          <li><span class="status-running">●</span> <strong>运行中</strong>：服务正常运行，可以接受请求</li>
          <li><span class="status-stopped">●</span> <strong>已停止</strong>：服务未运行，相关功能将不可用</li>
        </ul>
        
        <h4>常见问题</h4>
        <p>
          <strong>Q: 为什么需要启动智能匹配服务？</strong><br>
          A: 智能匹配服务是系统的核心功能之一，用于实现图像和文本的智能匹配，帮助用户更快地找到失物或认领拾物。
        </p>
        
        <p>
          <strong>Q: 服务启动失败怎么办？</strong><br>
          A: 请检查AutoDL服务器是否正常运行，以及CLIP+FAISS环境是否正确配置。如果问题持续，请查看日志文件获取详细错误信息。
        </p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import ServiceStatus from '@/components/admin/ServiceStatus.vue'
</script>

<style scoped>
.admin-services {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 12px 0;
  font-size: 22px;
  font-weight: 500;
  color: #303133;
}

.description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.service-docs {
  margin-top: 24px;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.docs-content {
  color: #606266;
  line-height: 1.6;
}

.docs-content h4 {
  margin: 16px 0 8px;
  font-size: 16px;
  color: #303133;
}

.docs-content p {
  margin: 8px 0 16px;
}

.docs-content ul {
  padding-left: 20px;
  margin: 8px 0 16px;
}

.status-running {
  color: #67c23a;
  font-size: 18px;
}

.status-stopped {
  color: #f56c6c;
  font-size: 18px;
}
</style>
