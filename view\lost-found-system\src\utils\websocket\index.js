/**
 * WebSocket 服务统一入口
 *
 * 该文件作为WebSocket相关功能的统一入口，提供简单清晰的API。
 * 内部管理WebSocket连接状态和初始化流程，对外提供稳定的接口。
 */
import connectionManager from './connection-manager';
import messageHandler, { MESSAGE_TYPES } from './message-handler';
import logger from '../logger';

// 创建WebSocket专用日志记录器
const wsLogger = logger.createLogger('WebSocket');

// 初始化状态
let initialized = false;
let initializing = false;
let initPromise = null;

/**
 * 初始化WebSocket服务
 * @returns {Promise<boolean>} 初始化是否成功
 */
export async function initializeWebSocketService() {
  // 如果已经初始化，直接返回成功
  if (initialized) {
    return true;
  }

  // 如果正在初始化，返回初始化Promise
  if (initializing && initPromise) {
    return initPromise;
  }

  // 开始初始化
  initializing = true;
  wsLogger.info('开始初始化WebSocket服务...');

  // 创建初始化Promise
  initPromise = new Promise(async (resolve) => {
    try {
      // 连接WebSocket
      const connected = await connectionManager.connect();
      if (!connected) {
        wsLogger.error('WebSocket连接失败，无法初始化服务');
        initializing = false;
        resolve(false);
        return;
      }

      // 初始化消息处理器
      const handlerInitialized = await messageHandler.initialize();
      if (!handlerInitialized) {
        wsLogger.error('消息处理器初始化失败');
        initializing = false;
        resolve(false);
        return;
      }

      // 初始化成功
      initialized = true;
      initializing = false;
      wsLogger.info('WebSocket服务初始化成功');
      resolve(true);
    } catch (error) {
      wsLogger.error('初始化WebSocket服务失败:', error);
      initializing = false;
      resolve(false);
    }
  });

  return initPromise;
}

/**
 * 重新初始化WebSocket服务
 * @returns {Promise<boolean>} 初始化是否成功
 */
export async function reinitializeWebSocketService() {
  // 重置初始化状态
  initialized = false;
  initializing = false;
  initPromise = null;

  // 断开现有连接
  connectionManager.disconnect();

  // 重新初始化
  return initializeWebSocketService();
}

/**
 * 检查WebSocket是否已连接
 * @returns {boolean} 是否已连接
 */
export function isWebSocketConnected() {
  return connectionManager.isConnected();
}

/**
 * 获取WebSocket连接状态
 * @returns {string} 连接状态
 */
export function getWebSocketState() {
  return connectionManager.getState();
}

/**
 * 添加连接状态监听器
 * @param {Function} listener 监听器函数
 */
export function addConnectionListener(listener) {
  connectionManager.addConnectionListener(listener);
}

/**
 * 移除连接状态监听器
 * @param {Function} listener 监听器函数
 */
export function removeConnectionListener(listener) {
  connectionManager.removeConnectionListener(listener);
}

/**
 * 添加消息监听器
 * @param {string} type 消息类型
 * @param {Function} listener 监听器函数
 */
export function addMessageListener(type, listener) {
  messageHandler.addMessageListener(type, listener);
}

/**
 * 移除消息监听器
 * @param {string} type 消息类型
 * @param {Function} listener 监听器函数
 */
export function removeMessageListener(type, listener) {
  messageHandler.removeMessageListener(type, listener);
}

/**
 * 发送聊天消息
 * @param {Object} message 消息对象
 * @param {File} file 文件对象（可选）
 * @returns {Promise<Object>} 发送结果
 */
export async function sendChatMessage(message, file = null) {
  // 确保WebSocket服务已初始化
  if (!initialized) {
    const success = await initializeWebSocketService();
    if (!success) {
      wsLogger.error('WebSocket服务未初始化，无法发送消息');
      return { code: 500, message: 'WebSocket服务未初始化，无法发送消息' };
    }
  }

  // 检查WebSocket连接状态
  if (!connectionManager.isConnected()) {
    wsLogger.error('WebSocket未连接，无法发送消息');
    return {
      code: 500,
      message: 'WebSocket未连接，无法发送消息',
      data: null
    };
  }

  try {
    // 如果有文件，使用FormData上传
    if (file) {
      // 检查WebSocket连接状态
      if (!connectionManager.isConnected()) {
        wsLogger.error('WebSocket未连接，无法上传文件');
        return {
          code: 500,
          message: 'WebSocket未连接，无法上传文件',
          data: null
        };
      }

      // 添加标记，表示这是文件上传，避免创建重复的离线消息
      window._fileMessageSent = true;
      wsLogger.info('设置文件消息已发送标记');

      // 延迟清除标记
      setTimeout(() => {
        window._fileMessageSent = false;
        wsLogger.info('清除文件消息已发送标记');
      }, 2000);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('senderId', message.senderId);
      formData.append('receiverId', message.receiverId);
      formData.append('message', message.message || '');
      formData.append('messageType', message.messageType);

      if (message.clientMessageId) {
        formData.append('clientMessageId', message.clientMessageId);
      }

      // 使用chat.js中的sendPrivateMessage函数
      wsLogger.info('使用chat.js中的sendPrivateMessage函数发送文件消息');

      // 导入sendPrivateMessage函数
      const { sendPrivateMessage } = await import('@/api/chat');
      const result = await sendPrivateMessage(formData);
      return result;
    } else {
      // 普通文本消息，使用WebSocket发送
      const result = await messageHandler.sendChatMessage(message);

      if (result) {
        return {
          code: 200,
          message: '消息已发送',
          data: {
            id: message.clientMessageId || `ws-${Date.now()}`,
            timestamp: new Date().toISOString(),
            ...message
          }
        };
      } else {
        return {
          code: 500,
          message: '消息发送失败',
          data: null
        };
      }
    }
  } catch (error) {
    wsLogger.error('发送消息失败:', error);
    return {
      code: 500,
      message: error.message || '发送消息失败',
      data: null
    };
  }
}

/**
 * 发送已读回执
 * @param {string|number|Array} messageId 消息ID或消息ID数组
 * @param {string|number} senderId 发送者ID
 * @param {string|number} readerId 读者ID（谁读了消息）
 * @returns {Promise<Object>} 发送结果
 */
export async function sendReadReceipt(messageId, senderId, readerId) {
  // 确保WebSocket服务已初始化
  if (!initialized) {
    const success = await initializeWebSocketService();
    if (!success) {
      wsLogger.error('WebSocket服务未初始化，无法发送已读回执');
      return { code: 500, message: 'WebSocket服务未初始化，无法发送已读回执' };
    }
  }

  try {
    // 确保messageId是数组
    const messageIds = Array.isArray(messageId) ? messageId : [messageId];

    wsLogger.info(`准备发送已读回执 - 消息数量: ${messageIds.length}, 发送者ID: ${senderId}, 读者ID: ${readerId}`);

    const result = await messageHandler.sendReadReceipt(messageIds, senderId, readerId);

    if (result) {
      return {
        code: 200,
        message: '已读回执已发送',
        data: {
          messageIds,
          senderId,
          readerId,
          timestamp: new Date().toISOString()
        }
      };
    } else {
      return {
        code: 500,
        message: '已读回执发送失败',
        data: null
      };
    }
  } catch (error) {
    wsLogger.error('发送已读回执失败:', error);
    return {
      code: 500,
      message: error.message || '发送已读回执失败',
      data: null
    };
  }
}

/**
 * 断开WebSocket连接
 */
export function disconnect() {
  connectionManager.disconnect();
  initialized = false;
}

// 联系人在线状态相关函数已移至 onlineStatusService.js

// 兼容性导出 - 为了支持旧代码
export const initWebSocketClient = initializeWebSocketService;
export const wsClient = {
  connect: initializeWebSocketService,
  disconnect,
  isConnected: isWebSocketConnected,
  sendChatMessage,
  sendReadReceipt
};
export const getWebSocketClient = () => wsClient;

// 导出消息类型
export { MESSAGE_TYPES };

// 默认导出
export default {
  initializeWebSocketService,
  reinitializeWebSocketService,
  isWebSocketConnected,
  getWebSocketState,
  addConnectionListener,
  removeConnectionListener,
  addMessageListener,
  removeMessageListener,
  sendChatMessage,
  sendReadReceipt,
  disconnect,
  MESSAGE_TYPES,
  // 兼容性导出
  initWebSocketClient,
  wsClient,
  getWebSocketClient
};
