
package com.tomato.lostfoundsystem.entity;

import com.tomato.lostfoundsystem.enums.AuditStatusEnum;
import lombok.Data;
import java.time.LocalDateTime;

@Data
public class FoundItem {
    private Long id;
    private Long userId;
    private String itemName;
    private String description;
    private String foundTime;
    private String foundLocation;
    private String imageUrl;
    private String status;
    private LocalDateTime createdAt;
    private String username;
    private String avatar;    //发布人头像
    private AuditStatusEnum auditStatus = AuditStatusEnum.PENDING;  //审核状态
}
