package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.UserAnnouncementRead;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户公告阅读状态Mapper接口
 */
@Mapper
public interface UserAnnouncementReadMapper {
    /**
     * 插入阅读记录
     * 
     * @param read 阅读记录对象
     * @return 影响的行数
     */
    int insertRead(UserAnnouncementRead read);
    
    /**
     * 查询用户的阅读记录
     * 
     * @param userId 用户ID
     * @return 阅读记录列表
     */
    List<UserAnnouncementRead> selectByUserId(Long userId);
    
    /**
     * 查询特定公告的阅读记录
     * 
     * @param announcementId 公告ID
     * @return 阅读记录列表
     */
    List<UserAnnouncementRead> selectByAnnouncementId(Long announcementId);
    
    /**
     * 查询用户是否已读特定公告
     * 
     * @param userId 用户ID
     * @param announcementId 公告ID
     * @return 阅读记录对象，如果不存在则返回null
     */
    UserAnnouncementRead selectByUserAndAnnouncement(@Param("userId") Long userId, @Param("announcementId") Long announcementId);
    
    /**
     * 获取未读公告数量
     * 
     * @param userId 用户ID
     * @return 未读公告数量
     */
    int countUnreadAnnouncements(Long userId);
}
