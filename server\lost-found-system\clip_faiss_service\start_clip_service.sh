#!/bin/bash

# 智能匹配服务启动脚本
# 用法: ./start_clip_service.sh [start|stop|restart|status]

# 配置
SERVICE_NAME="CLIP+FAISS Service"
PYTHON_ENV="./clip_faiss_env/bin/activate"
SCRIPT_PATH="./clip_faiss_api.py"
PID_FILE="./clip_faiss_service.pid"
LOG_FILE="./clip_faiss_service.log"

# 检查是否在Windows环境下运行
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows环境
    PYTHON_ENV="./clip_faiss_env/Scripts/activate"
fi

# 检查服务状态
check_status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "$SERVICE_NAME is running with PID $PID"
            return 0
        else
            echo "$SERVICE_NAME is not running (stale PID file)"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        echo "$SERVICE_NAME is not running"
        return 1
    fi
}

# 启动服务
start_service() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "$SERVICE_NAME is already running with PID $PID"
            return 0
        else
            echo "Removing stale PID file"
            rm -f "$PID_FILE"
        fi
    fi
    
    echo "Starting $SERVICE_NAME..."
    
    # 激活Python虚拟环境并启动服务
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        # Windows环境
        cmd //c "call $PYTHON_ENV && python $SCRIPT_PATH > $LOG_FILE 2>&1 &"
        # 获取PID（Windows环境下可能需要其他方法）
        PID=$!
    else
        # Linux/Mac环境
        source "$PYTHON_ENV" && python "$SCRIPT_PATH" > "$LOG_FILE" 2>&1 &
        PID=$!
    fi
    
    echo $PID > "$PID_FILE"
    echo "$SERVICE_NAME started with PID $PID"
    return 0
}

# 停止服务
stop_service() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            echo "Stopping $SERVICE_NAME with PID $PID..."
            kill "$PID"
            sleep 2
            
            # 检查是否成功停止
            if ps -p "$PID" > /dev/null 2>&1; then
                echo "Service did not stop gracefully, forcing kill..."
                kill -9 "$PID"
                sleep 1
            fi
            
            # 再次检查
            if ps -p "$PID" > /dev/null 2>&1; then
                echo "Failed to stop $SERVICE_NAME"
                return 1
            else
                echo "$SERVICE_NAME stopped"
                rm -f "$PID_FILE"
                return 0
            fi
        else
            echo "$SERVICE_NAME is not running (stale PID file)"
            rm -f "$PID_FILE"
            return 0
        fi
    else
        echo "$SERVICE_NAME is not running"
        return 0
    fi
}

# 重启服务
restart_service() {
    stop_service
    sleep 2
    start_service
}

# 主逻辑
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        check_status
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac

exit 0
