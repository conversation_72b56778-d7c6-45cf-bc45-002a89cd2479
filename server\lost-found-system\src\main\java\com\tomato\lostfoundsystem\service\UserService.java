package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.LoginRequestDTO;
import com.tomato.lostfoundsystem.dto.RegisterRequestDTO;
import com.tomato.lostfoundsystem.dto.UserProfileDTO;
import com.tomato.lostfoundsystem.entity.User;

public interface UserService {

    /**
     * 用户注册（手机号或邮箱注册二选一）
     * @param registerDTO 注册请求数据
     * @return 注册结果，包含token和用户信息
     */
    Result<?> register(RegisterRequestDTO registerDTO);

    /**
     * 用户登录
     * @param loginDTO 登录请求数据
     * @return 登录结果，包含token和用户信息
     */
    Result<?> login(LoginRequestDTO loginDTO);

    /**
     * 用户退出登录
     * @param token 用户token
     * @return 是否成功退出
     */
    boolean logout(String token);

    /**
     * 根据ID获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    User getUserById(Long userId);

    /**
     * 更新用户个人资料
     * @param userProfileDTO 用户资料
     * @return 是否更新成功
     */
    boolean updateUserProfile(UserProfileDTO userProfileDTO);

    /**
     * 用户注销账号
     * @param userId 用户ID
     * @param password 用户密码（用于验证）
     * @return 注销结果
     */
    Result<String> deactivateAccount(Long userId, String password);

    /**
     * 上传用户头像
     * @param userId 用户ID
     * @param avatarFile 头像文件
     * @return 上传结果
     */
    Result<String> uploadAvatar(Long userId, org.springframework.web.multipart.MultipartFile avatarFile);
}


