@echo off
echo ===================================================
echo Starting Lost and Found System Development Environment...
echo ===================================================

REM Set ngrok path
set NGROK_PATH=D:\ngrok\ngrok.exe

REM Start backend server (in background)
echo [1/3] Starting backend server...
start cmd /k "cd D:\augment\server\lost-found-system && mvn spring-boot:run"

REM Wait for backend server to start
echo Waiting for backend server to start...
timeout /t 15

REM Start frontend dev server (in background)
echo [2/3] Starting frontend dev server...
start cmd /k "cd D:\augment\view\lost-found-system && npm run dev -- --host"

REM Wait for frontend server to start
echo Waiting for frontend server to start...
timeout /t 10

REM Start ngrok (using config file)
echo [3/3] Starting ngrok tunnel...
echo.
echo Note: Only need to start frontend tunnel, backend is accessed through frontend proxy
echo.
set CONFIG_PATH=C:\Users\<USER>\AppData\Local\ngrok\ngrok.yml
start cmd /k "%NGROK_PATH% start --config="%CONFIG_PATH%" lost-found-frontend"

echo ===================================================
echo All services started!
echo.
echo Frontend local access: http://localhost:5173
echo Frontend LAN access: http://***********:5173
echo Backend local access: http://localhost:8081
echo.
echo ngrok tunnel information will be displayed in the ngrok window
echo Please note the URL provided by ngrok for remote access
echo ===================================================
