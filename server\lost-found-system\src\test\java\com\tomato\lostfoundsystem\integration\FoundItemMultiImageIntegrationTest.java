package com.tomato.lostfoundsystem.integration;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.controller.FoundItemController;
import com.tomato.lostfoundsystem.dto.FoundItemDTO;
import com.tomato.lostfoundsystem.entity.FoundItem;
import com.tomato.lostfoundsystem.entity.ItemImage;
import com.tomato.lostfoundsystem.mapper.FoundItemMapper;
import com.tomato.lostfoundsystem.mapper.ItemImageMapper;
import com.tomato.lostfoundsystem.service.FoundItemService;
import com.tomato.lostfoundsystem.service.ItemImageService;
import com.tomato.lostfoundsystem.utils.AliyunOSSUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 拾物多图发布集成测试
 * 注意：此测试类使用了@MockBean来模拟AliyunOSSUtil，避免实际上传图片到OSS
 */
@SpringBootTest
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@Transactional
public class FoundItemMultiImageIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private FoundItemService foundItemService;

    @Autowired
    private FoundItemMapper foundItemMapper;

    @Autowired
    private ItemImageMapper itemImageMapper;

    @MockBean
    private AliyunOSSUtil aliyunOSSUtil;

    private MockMvc mockMvc;
    private MockMultipartFile image1;
    private MockMultipartFile image2;
    private MockMultipartFile image3;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 创建测试图片
        image1 = new MockMultipartFile(
                "images",
                "test1.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test image content 1".getBytes()
        );

        image2 = new MockMultipartFile(
                "images",
                "test2.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test image content 2".getBytes()
        );

        image3 = new MockMultipartFile(
                "images",
                "test3.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test image content 3".getBytes()
        );

        // 模拟AliyunOSSUtil.uploadFoundImage方法
        when(aliyunOSSUtil.uploadFoundImage(any(MultipartFile.class)))
                .thenReturn("http://example.com/test.jpg");
    }

    /**
     * 测试多图发布拾物的完整流程
     * 1. 准备多张图片和拾物信息
     * 2. 调用服务层方法发布拾物
     * 3. 验证拾物信息和图片是否正确保存
     */
    @Test
    void testPublishFoundItemWithMultipleImages() {
        // 准备测试数据
        FoundItemDTO foundItemDTO = new FoundItemDTO();
        foundItemDTO.setUserId(1L);
        foundItemDTO.setItemName("测试多图拾物");
        foundItemDTO.setDescription("这是一个详细的拾物描述，包含了物品的颜色、形状和特征，长度超过20个字符。这是一个集成测试。");
        foundItemDTO.setFoundTime("2023-05-01T10:00:00");
        foundItemDTO.setFoundLocation("图书馆");
        foundItemDTO.setImages(Arrays.asList(image1, image2, image3));

        // 调用服务层方法
        Result<Object> result = foundItemService.publishFoundItem(foundItemDTO);

        // 验证结果
        assertEquals(200, result.getCode());
        assertTrue(result.getMessage().contains("发布成功"));

        // 从结果中获取拾物ID
        Long foundItemId = null;
        if (result.getData() != null && result.getData() instanceof FoundItem) {
            foundItemId = ((FoundItem) result.getData()).getId();
        }

        // 验证拾物信息是否正确保存
        assertNotNull(foundItemId, "拾物ID不应为空");
        FoundItem savedFoundItem = foundItemMapper.selectById(foundItemId);
        assertNotNull(savedFoundItem, "保存的拾物不应为空");
        assertEquals("测试多图拾物", savedFoundItem.getItemName());
        assertEquals("图书馆", savedFoundItem.getFoundLocation());

        // 验证图片是否正确保存
        List<ItemImage> savedImages = itemImageMapper.getImagesByItemId(foundItemId, "FOUND");
        assertNotNull(savedImages, "保存的图片不应为空");
        assertEquals(3, savedImages.size(), "应该保存了3张图片");

        // 验证图片URL是否正确
        for (ItemImage image : savedImages) {
            assertEquals("http://example.com/test.jpg", image.getImageUrl());
            assertEquals("FOUND", image.getItemType());
            assertEquals(foundItemId, image.getItemId());
        }
    }

    /**
     * 测试通过HTTP请求发布多图拾物
     */
    @Test
    void testPublishFoundItemWithMultipleImagesViaHttp() throws Exception {
        // 准备测试数据
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setAttribute("userId", 1L);

        // 执行测试
        mockMvc.perform(multipart("/api/found-items/publish")
                        .file(image1)
                        .file(image2)
                        .file(image3)
                        .param("itemName", "测试HTTP多图拾物")
                        .param("description", "这是一个通过HTTP请求发布的多图拾物测试，包含了物品的颜色、形状和特征，长度超过20个字符。")
                        .param("foundTime", "2023-05-01T10:00:00")
                        .param("foundLocation", "图书馆")
                        .requestAttr("userId", 1L)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andDo(print());

        // 验证AliyunOSSUtil.uploadFoundImage方法被调用了多次
        verify(aliyunOSSUtil, atLeast(3)).uploadFoundImage(any(MultipartFile.class));
    }
}
