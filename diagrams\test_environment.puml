@startuml 测试环境架构图

skinparam backgroundColor white
skinparam handwritten false
skinparam defaultFontName Arial
skinparam roundcorner 8
skinparam shadowing false

skinparam rectangle {
  BackgroundColor #F0F8FF
  BorderColor #2C3E50
  FontColor #2C3E50
}

skinparam database {
  BackgroundColor #F5EEF8
  BorderColor #8E44AD
  FontColor #2C3E50
}

skinparam cloud {
  BackgroundColor #E8F8F5
  BorderColor #16A085
  FontColor #2C3E50
}

title 失物招领系统测试环境架构

rectangle "测试环境" as TestEnv {
  rectangle "前端测试环境" as FrontendTest {
    rectangle "Nginx" as TestNginx
    rectangle "Vue 3应用" as TestVue
    rectangle "Jest测试框架" as Jest
    rectangle "Cypress E2E测试" as Cypress
  }
  
  rectangle "后端测试环境" as BackendTest {
    rectangle "Spring Boot应用" as TestSpringBoot {
      rectangle "JUnit 5" as JUnit
      rectangle "MockMvc" as MockMvc
      rectangle "TestRestTemplate" as TestRestTemplate
    }
    rectangle "Tomcat" as TestTomcat
    rectangle "Java环境(JDK 17)" as TestJava
  }
  
  rectangle "数据库测试环境" as DBTest {
    database "MySQL测试库" as TestMySQL
    database "Redis测试实例" as TestRedis
    rectangle "H2内存数据库" as H2DB
  }
  
  rectangle "消息队列测试" as MQTest {
    rectangle "Kafka" as TestKafka
    rectangle "Zookeeper" as TestZookeeper
  }
  
  rectangle "CLIP+FAISS测试环境" as ClipTest {
    rectangle "Python 3.9+" as TestPython
    rectangle "PyTorch" as TestPyTorch
    rectangle "FAISS-CPU" as TestFAISS
    rectangle "FastAPI" as TestFastAPI
    rectangle "测试数据集" as TestDataset
  }
  
  rectangle "测试工具" as TestTools {
    rectangle "Postman" as Postman
    rectangle "JMeter" as JMeter
    rectangle "SonarQube" as SonarQube
    rectangle "Jacoco" as Jacoco
  }
}

cloud "模拟外部服务" as MockServices {
  rectangle "Mock OSS服务" as MockOSS
  rectangle "Mock 短信服务" as MockSMS
  rectangle "Mock 邮件服务" as MockEmail
}

' 连接关系
TestVue -- TestNginx
Jest -- TestVue
Cypress -- TestVue

TestNginx -- TestTomcat
TestTomcat -- TestSpringBoot
TestSpringBoot -- JUnit
TestSpringBoot -- MockMvc
TestSpringBoot -- TestRestTemplate

TestSpringBoot -- TestMySQL
TestSpringBoot -- TestRedis
TestSpringBoot -- H2DB
TestSpringBoot -- TestKafka
TestKafka -- TestZookeeper

TestSpringBoot -- TestFastAPI
TestFastAPI -- TestPython
TestPython -- TestPyTorch
TestPython -- TestFAISS
TestFAISS -- TestDataset

Postman -- TestSpringBoot
JMeter -- TestSpringBoot
SonarQube -- TestSpringBoot
Jacoco -- TestSpringBoot

TestSpringBoot -- MockOSS
TestSpringBoot -- MockSMS
TestSpringBoot -- MockEmail

note right of TestDataset
  测试数据集包含:
  - 标准测试图像集
  - 预生成的特征向量
  - 模拟的物品数据
end note

note bottom of MockServices
  模拟外部服务使用WireMock
  实现，避免测试依赖真实
  外部服务
end note

note right of H2DB
  单元测试使用H2内存数据库
  集成测试使用实际MySQL
end note

@enduml