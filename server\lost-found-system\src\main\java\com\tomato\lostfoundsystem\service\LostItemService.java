package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.ItemAuditDTO;
import com.tomato.lostfoundsystem.dto.LostItemDTO;
import com.tomato.lostfoundsystem.dto.LostItemDetailsDTO;
import com.tomato.lostfoundsystem.entity.LostItem;

import java.util.Map;

public interface LostItemService {
    Result<Object> publishLostItem(LostItemDTO lostItemDTO);

    Result<Map<String, Object>> searchLostItems(String keyword, String lostLocation, String status, String timeRange, String timeFilterType, String startDate, String endDate, int page, int size);

    LostItemDetailsDTO getLostItemDetails(Long id);

    /**
     * 根据ID获取失物信息
     *
     * @param id 失物ID
     * @return 失物信息
     */
    LostItem getLostItemById(Long id);

    Result<Object> updateLostItem(Long id, LostItemDTO lostItemUpdateDTO);

    Result<Object> deleteLostItem(Long id, Long userId);

    Result<Object> getMyPublishedItems(Long userId);

    /**
     * 更新失物状态（未找回/已找回）
     *
     * @param id 失物ID
     * @param status 状态值（LOST/FOUND）
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Object> updateLostItemStatus(Long id, String status, Long userId);
}