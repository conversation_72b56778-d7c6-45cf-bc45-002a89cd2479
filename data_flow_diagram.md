```mermaid
flowchart TD
    %% 标题
    title[校园失物招领系统数据流图]
    style title fill:none,stroke:none
    
    %% 外部实体
    User[用户]
    Admin[管理员]
    style User fill:#D6EAF8,stroke:#2E86C1
    style Admin fill:#D6EAF8,stroke:#2E86C1
    
    %% 处理过程
    Auth[用户认证]
    LostItem[失物管理]
    FoundItem[招领管理]
    Match[智能匹配]
    Chat[即时通讯]
    SysManage[系统管理]
    style Auth fill:#F9E79F,stroke:#F1C40F
    style LostItem fill:#F9E79F,stroke:#F1C40F
    style FoundItem fill:#F9E79F,stroke:#F1C40F
    style Match fill:#F9E79F,stroke:#F1C40F
    style Chat fill:#F9E79F,stroke:#F1C40F
    style SysManage fill:#F9E79F,stroke:#F1C40F
    
    %% 数据存储
    UserDB[(用户数据)]
    LostDB[(失物数据)]
    FoundDB[(招领数据)]
    MatchDB[(匹配数据)]
    ChatDB[(聊天数据)]
    SysDB[(系统数据)]
    style UserDB fill:#F5B7B1,stroke:#E74C3C
    style LostDB fill:#F5B7B1,stroke:#E74C3C
    style FoundDB fill:#F5B7B1,stroke:#E74C3C
    style MatchDB fill:#F5B7B1,stroke:#E74C3C
    style ChatDB fill:#F5B7B1,stroke:#E74C3C
    style SysDB fill:#F5B7B1,stroke:#E74C3C
    
    %% 外部系统
    OSS[阿里云OSS]
    CLIP[CLIP+FAISS服务]
    Kafka[(Kafka消息队列)]
    style OSS fill:#D6DBDF,stroke:#2C3E50
    style CLIP fill:#D6DBDF,stroke:#2C3E50
    style Kafka fill:#FADBD8,stroke:#E74C3C
    
    %% 数据流
    %% 用户认证流程
    User -- 1.1 提交登录信息 --> Auth
    Auth -- 1.2 验证用户信息 --> UserDB
    Auth -- 1.3 返回认证结果 --> User
    
    %% 失物管理流程
    User -- 2.1 发布失物信息 --> LostItem
    LostItem -- 2.2 上传图片 --> OSS
    LostItem -- 2.3 保存失物信息 --> LostDB
    LostItem -- 2.4 返回发布结果 --> User
    
    %% 招领管理流程
    User -- 3.1 发布招领信息 --> FoundItem
    FoundItem -- 3.2 上传图片 --> OSS
    FoundItem -- 3.3 保存招领信息 --> FoundDB
    FoundItem -- 3.4 返回发布结果 --> User
    
    %% 智能匹配流程
    User -- 4.1 提交匹配请求 --> Match
    Match -- 4.2 提取特征向量 --> CLIP
    CLIP -- 4.3 返回特征向量 --> Match
    Match -- 4.4a 查询失物数据 --> LostDB
    Match -- 4.4b 查询招领数据 --> FoundDB
    Match -- 4.5 保存匹配结果 --> MatchDB
    Match -- 4.6 返回匹配结果 --> User
    
    %% 即时通讯流程
    User -- 5.1 发送消息 --> Chat
    Chat -- 5.2 上传媒体文件 --> OSS
    Chat -- 5.3 保存消息记录 --> ChatDB
    Chat -- 5.4 发布消息事件 --> Kafka
    Kafka -- 5.5 消费消息事件 --> Chat
    Chat -- 5.6 推送消息 --> User
    
    %% 系统管理流程
    Admin -- 6.1 提交管理操作 --> SysManage
    SysManage -- 6.2 执行管理操作 --> SysDB
    SysManage -- 6.3 返回操作结果 --> Admin
```
