package com.tomato.lostfoundsystem.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 系统公告实体类
 */
@Data
public class SystemAnnouncement {
    /**
     * 公告ID
     */
    private Long id;
    
    /**
     * 公告标题
     */
    private String title;
    
    /**
     * 公告内容
     */
    private String content;
    
    /**
     * 重要程度：NORMAL(普通), IMPORTANT(重要), URGENT(紧急)
     */
    private String importance;
    
    /**
     * 生效时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间（可为空，表示长期有效）
     */
    private LocalDateTime endTime;
    
    /**
     * 创建者ID
     */
    private Long createdBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 状态：DRAFT(草稿), PUBLISHED(已发布), EXPIRED(已过期)
     */
    private String status;
}
