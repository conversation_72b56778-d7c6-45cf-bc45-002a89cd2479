<template>
  <div class="publish-found-container">
    <AuthCheck title="需要登录" message="发布拾物信息需要登录后才能使用，请先登录">
      <!-- 主要内容区域 - 单列布局 -->
      <div class="main-content">
        <!-- 表单区域 -->
        <div class="content-form">
          <!-- 页面标题 - 移到表单内部 -->
          <div class="page-header">
            <h2 class="page-title">{{ isEdit ? '编辑拾物信息' : '发布拾物信息' }}</h2>
            <div class="page-subtitle">{{ isEdit ? '请修改拾物信息，帮助失主尽快找回物品' : '请填写拾物信息，帮助失主尽快找回物品' }}</div>
          </div>


        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-position="top"
          class="publish-form"
        >
          <!-- 混合布局容器 -->
          <div class="mixed-layout">
            <!-- 顶部双栏布局 - 基本信息和轮播图 -->
            <div class="two-column-section">
              <!-- 左侧 - 基本信息 -->
              <div class="left-column">
                <div class="form-section">
                  <div class="section-title" style="margin-bottom: 20px;">基本信息</div>

                  <el-form-item label="物品名称" prop="itemName" required>
                    <el-input
                      v-model="form.itemName"
                      placeholder="请输入物品名称"
                      maxlength="50"
                      show-word-limit
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="拾获地点" prop="foundLocation" required>
                    <el-input
                      v-model="form.foundLocation"
                      placeholder="请输入拾获地点"
                      maxlength="100"
                      show-word-limit
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="拾获时间" prop="foundTime" required>
                    <el-date-picker
                      v-model="form.foundTime"
                      type="datetime"
                      placeholder="请选择拾获时间"
                      value-format="YYYY-MM-DDTHH:mm:ss"
                      :default-time="new Date(2000, 1, 1, 12, 0, 0)"
                      style="width: 100%"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="物品描述" prop="description" required>
                    <el-input
                      v-model="form.description"
                      type="textarea"
                      :rows="4"
                      placeholder="请详细描述物品特征（颜色、尺寸、品牌、材质、新旧程度等），至少20个字符"
                      maxlength="500"
                      show-word-limit
                    >
                      <template #prefix>
                        <el-tooltip
                          content="描述越详细，失主找到物品的几率越大。请包含物品外观、颜色、品牌、材质等特征，至少20个字符"
                          placement="top-start"
                        >
                          <el-icon><i class="el-icon-info"></i></el-icon>
                        </el-tooltip>
                      </template>
                    </el-input>

                  </el-form-item>
                </div>
              </div>

              <!-- 右侧 - 图片轮播预览 -->
              <div class="right-column">
                <div class="form-section">
                  <div class="section-title" style="margin-top: 40px;">物品图片</div>

                  <div class="image-preview-area">
                    <div class="image-carousel-container">
                      <!-- 无图片时显示系统logo作为占位图 -->
                      <div v-if="fileList.length === 0" class="empty-image-placeholder">
                        <img src="/images/logo_text.png" alt="校园失物招领系统" class="placeholder-logo" />
                        <div class="placeholder-text">请上传物品图片</div>
                      </div>
                      <el-carousel
                        v-if="fileList.length > 0"
                        :interval="4000"
                        type="card"
                        height="280px"
                        indicator-position="outside"
                        arrow="always"
                        @change="handleCarouselChange"
                      >
                        <el-carousel-item
                          v-for="(item, index) in fileList"
                          :key="item.uid"
                          :class="{ 'is-main-slide': index === mainImageIndex }"
                        >
                          <div class="carousel-item-content">
                            <!-- 主图标识 - 左上角星标 -->
                            <div class="carousel-main-badge" v-if="index === mainImageIndex">
                              <el-icon><i class="el-icon-star-on"></i></el-icon>
                              <span>主图</span>
                            </div>
                            <el-image
                              :src="getImageUrl(item)"
                              fit="contain"
                              class="carousel-image"
                              @click="previewImage(index)"
                            />
                            <div class="carousel-actions">
                              <el-button
                                type="primary"
                                size="small"
                                @click.stop="setAsMainImage(index)"
                                :class="{ 'is-active': index === mainImageIndex }"
                              >
                                <el-icon><i class="el-icon-star-on"></i></el-icon>
                                {{ index === mainImageIndex ? '当前主图' : '设为主图' }}
                              </el-button>
                              <el-button
                                type="danger"
                                size="small"
                                @click.stop="removeImage(index)"
                              >
                                <el-icon><i class="el-icon-delete"></i></el-icon>
                                删除
                              </el-button>
                            </div>
                          </div>
                        </el-carousel-item>
                      </el-carousel>

                      <div class="carousel-indicator">
                        <span v-if="currentCarouselIndex === mainImageIndex" class="main-image-indicator">主图</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 底部单栏 - 图片上传区域 -->
            <div class="form-section">
              <div class="section-title" style="margin-bottom: 20px;">上传物品图片</div>

              <el-form-item prop="images">
                <div class="upload-area">
                  <div class="upload-button-area">
                    <el-upload
                      class="upload-button"
                      :auto-upload="false"
                      :on-change="handleImageChange"
                      :before-upload="beforeImageUpload"
                      :limit="5"
                      :on-exceed="handleExceed"
                      :on-remove="handleRemove"
                      :file-list="fileList"
                      multiple
                      :show-file-list="false"
                    >
                      <div class="upload-button-content">
                        <el-icon class="upload-icon"><i class="el-icon-plus"></i></el-icon>
                        <div class="upload-text">
                          <div class="upload-primary-text">点击选择拾获物品图片</div>
                          <div class="upload-secondary-text">
                            <span>支持jpg、png格式，每张不超过2MB，最多5张</span>
                          </div>
                        </div>
                      </div>
                    </el-upload>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 表单底部按钮 -->
          <div class="form-actions">
            <el-button @click="handleCancel" :disabled="submitting" size="large">
              取消
            </el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitting" size="large">
              <span>{{ submitting ? (isEdit ? '保存中...' : '发布中...') : (isEdit ? '保存修改' : '发布拾物') }}</span>
            </el-button>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 上传进度条 -->
    <el-dialog
      v-model="showProgress"
      title="上传进度"
      width="30%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <div class="upload-progress">
        <span class="progress-text">上传进度: {{ uploadProgress }}%</span>
        <el-progress :percentage="uploadProgress" :stroke-width="15" status="success" />
      </div>
    </el-dialog>

    <!-- 图片预览组件 -->
    <el-image-viewer
      v-if="previewVisible"
      :url-list="previewImages"
      :initial-index="previewIndex"
      :teleported="true"
      :z-index="9999"
      @close="previewVisible = false"
    />

    <!-- 提交成功对话框 -->
    <el-dialog
      v-model="submitSuccess"
      title="提交成功"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <el-result
        icon="success"
        :title="isEdit ? '修改成功' : '提交成功'"
        :sub-title="isEdit ? '您的拾物信息已更新' : '您的拾物信息已提交，正在等待管理员审核，审核通过后将对所有用户可见'"
      >
        <template #extra>
          <el-alert
            v-if="!isEdit"
            title="审核通常在24小时内完成"
            type="info"
            description="审核结果将通过系统通知告知您，请留意右上角的通知图标"
            show-icon
            :closable="false"
            style="margin-bottom: 20px;"
          />
          <div class="result-actions">
            <el-button type="primary" @click="goToMyPosts">
              查看我的发布
            </el-button>
            <el-button @click="resetForm">继续发布</el-button>
            <el-button @click="goToList">返回列表</el-button>
          </div>
        </template>
      </el-result>
    </el-dialog>
    </AuthCheck>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, defineProps } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { publishFoundItem, getFoundItemDetail, updateFoundItem, updateFoundItemImages } from '@/api/found'
import AuthCheck from '@/components/AuthCheck.vue'

// 定义组件属性
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  itemId: {
    type: [String, Number],
    default: ''
  }
})

const router = useRouter()
const formRef = ref(null)
const submitting = ref(false)
const submitSuccess = ref(false)
const showProgress = ref(false)
const uploadProgress = ref(0)

// 表单数据
const form = reactive({
  itemName: '',
  foundLocation: '',
  foundTime: '',
  description: ''
})

// 上传相关
const fileList = ref([])
const mainImageIndex = ref(0) // 默认选择第一张图片作为主图

// 表单验证规则
const rules = {
  itemName: [
    { required: true, message: '请输入物品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '物品名称长度应在2-50个字符之间', trigger: 'blur' }
  ],
  foundLocation: [
    { required: true, message: '请输入拾获地点', trigger: 'blur' },
    { min: 2, max: 100, message: '拾获地点长度应在2-100个字符之间', trigger: 'blur' }
  ],
  foundTime: [
    { required: true, message: '请选择拾获时间', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入物品描述', trigger: 'blur' },
    { min: 20, max: 500, message: '物品描述长度应在20-500个字符之间，请提供详细描述，包括物品特征、颜色、品牌等信息', trigger: 'blur' }
  ]
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  // 验证文件类型
  const isJPG = file.type === 'image/jpeg'
  const isPNG = file.type === 'image/png'
  if (!isJPG && !isPNG) {
    ElMessage.error('只能上传JPG或PNG格式的图片!')
    return false
  }

  // 验证文件大小
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB!')
    return false
  }
  return true
}

// 处理图片选择
const handleImageChange = (file, uploadFileList) => {
  console.log('文件变化:', file, uploadFileList)

  // 更新图片列表
  fileList.value = uploadFileList

  // 如果是第一张图片，自动设为主图
  if (uploadFileList.length === 1) {
    mainImageIndex.value = 0
  }

  // 如果删除了所有图片，重置主图索引
  if (uploadFileList.length === 0) {
    mainImageIndex.value = 0
  }

  // 确保主图索引不超出范围
  if (mainImageIndex.value >= uploadFileList.length && uploadFileList.length > 0) {
    mainImageIndex.value = 0
  }
}

// 处理超出限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传5张图片')
}

// 处理移除图片
const handleRemove = (_, uploadFiles) => {
  // 如果删除的是主图，则将第一张图片设为主图
  if (mainImageIndex.value >= uploadFiles.length && uploadFiles.length > 0) {
    mainImageIndex.value = 0
  }
}

// 移除图片
const removeImage = (index) => {
  console.log('删除图片:', index, fileList.value)

  // 记录删除操作
  console.log('要删除的图片索引:', index)

  // 从列表中移除图片
  fileList.value.splice(index, 1)

  console.log('删除后的图片列表:', fileList.value)

  // 如果删除的是主图，则将第一张图片设为主图
  if (mainImageIndex.value === index && fileList.value.length > 0) {
    mainImageIndex.value = 0
    console.log('删除的是主图，新的主图索引:', mainImageIndex.value)
  } else if (mainImageIndex.value > index) {
    // 如果删除的图片在主图之前，则主图索引减1
    mainImageIndex.value--
    console.log('删除的图片在主图之前，新的主图索引:', mainImageIndex.value)
  }

  // 确保主图索引不超出范围
  if (mainImageIndex.value >= fileList.value.length && fileList.value.length > 0) {
    mainImageIndex.value = fileList.value.length - 1
    console.log('主图索引超出范围，调整为:', mainImageIndex.value)
  }

  ElMessage.success('已删除图片')
}

// 获取图片URL
const getImageUrl = (item) => {
  if (item.url) return item.url
  if (item.raw) return URL.createObjectURL(item.raw)
  return ''
}

// 设置为主图
const setAsMainImage = (index) => {
  mainImageIndex.value = index
  ElMessage.success('已设置为主图')
}

// 预览图片相关
const previewVisible = ref(false)
const previewImages = ref([])
const previewIndex = ref(0)
const currentCarouselIndex = ref(0)

// 轮播切换处理
const handleCarouselChange = (index) => {
  currentCarouselIndex.value = index
}

// 预览单张图片
const previewImage = (index) => {
  if (fileList.value.length === 0) return

  previewImages.value = fileList.value.map(img => getImageUrl(img))
  previewIndex.value = index
  previewVisible.value = true
}



// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    // 先验证基本信息
    await formRef.value.validate()

    // 检查图片是否上传
    if (fileList.value.length === 0) {
      ElMessage.error({
        message: '请上传至少一张物品图片',
        duration: 2000
      })
      return
    }

    // 检查描述长度是否符合后端要求
    if (form.description.length < 20) {
      ElMessage.error({
        message: '物品描述长度必须在20-500个字符之间，请提供详细描述，包括物品特征、颜色、品牌等信息',
        duration: 3000
      })
      return
    }

    submitting.value = true
    showProgress.value = true

    // 创建FormData对象
    const formData = new FormData()
    formData.append('itemName', form.itemName)
    formData.append('foundLocation', form.foundLocation)
    formData.append('foundTime', form.foundTime)
    formData.append('description', form.description)

    // 添加主图索引 - 确保转换为字符串
    formData.append('mainImageIndex', String(mainImageIndex.value))
    console.log('发送主图索引:', mainImageIndex.value)

    // 添加图片文件 - 统一只使用images参数
    fileList.value.forEach((item, index) => {
      if (item.raw) {
        // 添加图片
        formData.append('images', item.raw)
        console.log(`添加图片 ${index}:`, item.raw.name, index === mainImageIndex.value ? '(主图)' : '')
      }
    })

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 300)

    // 检查是否有新上传的图片
    const hasNewImages = fileList.value.some(file => file.raw);
    console.log('是否有新上传的图片:', hasNewImages);

    if (!hasNewImages && props.isEdit) {
      // 在编辑模式下，如果没有新上传的图片，添加一个标志表示保留原有图片
      formData.append('keepExistingImages', 'true')
      console.log('保留原有图片')
    }

    // 根据模式选择不同的API
    let response
    if (props.isEdit) {
      // 编辑模式 - 更新拾物信息
      response = await updateFoundItem(props.itemId, formData)
    } else {
      // 发布模式 - 创建新的拾物信息
      response = await publishFoundItem(formData)
    }

    // 清除进度条定时器
    clearInterval(progressInterval)
    uploadProgress.value = 100

    // 处理响应
    if (response.code === 200) {
      setTimeout(() => {
        showProgress.value = false
        submitSuccess.value = true
        submitting.value = false
      }, 500)
    } else {
      showProgress.value = false
      submitting.value = false
      ElMessage.error(response.message || '发布失败，请重试')
    }
  } catch (error) {
    submitting.value = false
    showProgress.value = false
    console.error('表单验证失败', error)
    ElMessage.error('表单验证失败，请检查输入')
  }
}

// 加载拾物详情
const fetchItemDetail = async () => {
  if (!props.isEdit || !props.itemId) return

  try {
    const response = await getFoundItemDetail(props.itemId)
    if (response.code === 200) {
      const itemData = response.data

      // 填充表单数据
      form.itemName = itemData.itemName
      form.foundLocation = itemData.foundLocation
      form.foundTime = itemData.foundTime
      form.description = itemData.description

      // 处理图片数据
      if (itemData.imageUrls && itemData.imageUrls.length > 0) {
        fileList.value = itemData.imageUrls.map((url, index) => {
          return {
            name: `image-${index}.jpg`,
            url: url,
            uid: `existing-${index}`,
            status: 'success'
          }
        })

        // 找到主图的索引
        if (itemData.imageUrl) {
          const mainIndex = itemData.imageUrls.findIndex(url => url === itemData.imageUrl)
          if (mainIndex !== -1) {
            mainImageIndex.value = mainIndex
          }
        }
      } else if (itemData.imageUrl) {
        // 如果只有主图，没有额外图片
        fileList.value = [{
          name: 'main-image.jpg',
          url: itemData.imageUrl,
          uid: 'existing-0',
          status: 'success'
        }]
        mainImageIndex.value = 0
      }

      console.log('加载的图片列表:', fileList.value)
      console.log('主图索引:', mainImageIndex.value)
    } else {
      ElMessage.error(response.message || '获取拾物详情失败')
    }
  } catch (error) {
    console.error('获取拾物详情失败：', error)
    ElMessage.error('获取拾物详情失败')
  }
}

// 取消操作
const handleCancel = () => {
  if (props.isEdit) {
    ElMessage.info('已取消修改')
  } else {
    ElMessage.info('已取消发布')
  }
  router.push('/found-items')
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  fileList.value = []
  mainImageIndex.value = 0
  submitSuccess.value = false
}

// 查看我的发布
const goToMyPosts = () => {
  router.push('/profile/my-posts')
  submitSuccess.value = false
}

// 返回列表
const goToList = () => {
  router.push('/found-items')
  submitSuccess.value = false
}

// 组件挂载时加载数据
onMounted(() => {
  if (props.isEdit && props.itemId) {
    fetchItemDetail()
  }
})
</script>

<style scoped>
/* 页面容器 */
.publish-found-container {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --border-color: #EBEEF5;
  --border-radius: 4px;
  --box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  margin-top: 60px; /* 为顶部导航栏留出空间 */
  display: flex;
  flex-direction: column;
}

/* 页面标题 */
.page-header {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  text-align: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 10px 0;
}

.page-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* 主内容区域 - 单列布局 */
.main-content {
  margin-bottom: 20px;
}

.content-form {
  max-width: 1000px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--box-shadow);
}

/* 表单区域 */
.form-header {
  margin-bottom: 20px;
}

.form-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.form-section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 15px;
}

/* 表单样式 */
.publish-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-primary);
  padding-bottom: 8px;
}

.publish-form :deep(.el-form-item) {
  margin-bottom: 18px;
}

.publish-form :deep(.el-input__wrapper),
.publish-form :deep(.el-textarea__inner) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.publish-form :deep(.el-input__wrapper:hover),
.publish-form :deep(.el-textarea__inner:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.publish-form :deep(.el-input__wrapper.is-focus),
.publish-form :deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 1px var(--primary-color) inset;
}

/* 混合布局 */
.mixed-layout {
  display: flex;
  flex-direction: column;
  gap: 30px;
  margin-bottom: 20px;
}

/* 顶部双栏布局 */
.two-column-section {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.left-column {
  flex: 1;
  min-width: 320px;
}

.right-column {
  flex: 1;
  min-width: 320px;
}

/* 图片上传区域 */
.upload-area {
  margin-bottom: 15px;
}

/* 图片上传容器 */
.image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  background-color: #fff;
  border-radius: 8px;
  height: 100%;
}

.upload-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

@media (min-width: 768px) {
  .upload-content-wrapper {
    flex-direction: row;
    align-items: flex-start;
  }

  .upload-button-area {
    flex: 1;
    min-width: 300px;
    max-width: 40%;
  }

  .image-preview-area {
    flex: 2;
  }
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.upload-title-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.upload-title {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
}

.upload-counter {
  font-size: 13px;
  color: var(--text-secondary);
  background-color: #f5f7fa;
  padding: 2px 8px;
  border-radius: 10px;
  transition: all 0.3s;
}

.upload-counter.has-images {
  background-color: #ecf5ff;
  color: var(--primary-color);
  font-weight: 500;
}

/* 上传按钮区域 */
.upload-button-area {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.upload-button {
  width: 100%;
}

.upload-button :deep(.el-upload) {
  width: 100%;
  display: block;
}

.upload-button-content {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  background-color: #f8f9fb;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
  min-height: 100px;
  width: 100%;
}

.upload-button-content:hover {
  border-color: var(--primary-color);
  background-color: #ecf5ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.upload-icon {
  font-size: 28px;
  color: var(--primary-color);
  margin-right: 15px;
}

.upload-text {
  flex: 1;
}

.upload-primary-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 6px;
}

.upload-secondary-text {
  font-size: 13px;
  color: var(--text-secondary);
  opacity: 0.9;
  display: flex;
  align-items: center;
  white-space: normal;
  line-height: 1.5;
}

/* 图片预览区域 */
.image-preview-area {
  width: 100%;
}

.thumbnails-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 15px;
  justify-content: flex-start;
}

.thumbnail-item {
  width: 120px;
  height: 120px;
  position: relative;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.thumbnail-wrapper {
  width: 120px;
  height: 120px;
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 2px solid transparent;
  position: relative;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.thumbnail-item:hover .thumbnail-wrapper {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 主图标识 - 缩略图 */
.main-image-badge {
  position: absolute;
  top: 5px;
  left: 5px;
  background-color: #E6A23C; /* 黄色 */
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

/* 轮播图主图标识 */
.carousel-main-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: #E6A23C; /* 黄色 */
  color: white;
  padding: 2px 8px 2px 6px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.carousel-main-badge .el-icon {
  font-size: 14px;
}

/* 主图边框样式 */
.thumbnail-item.is-main .thumbnail-wrapper {
  border-color: #E6A23C; /* 黄色边框 */
  box-shadow: 0 0 0 1px #E6A23C, 0 2px 6px rgba(0, 0, 0, 0.1);
}

.thumbnail-actions {
  position: absolute;
  top: 5px;
  right: 5px;
  opacity: 0;
  transition: opacity 0.2s;
}

.thumbnail-item:hover .thumbnail-actions {
  opacity: 1;
}

.thumbnail-label {
  margin-top: 5px;
  font-size: 12px;
  color: var(--primary-color);
  font-weight: 500;
}



/* 图片轮播样式 */
.image-carousel-container {
  margin-bottom: 15px;
  position: relative;
  padding: 15px;
  background-color: #f8f9fb;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.carousel-item-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.carousel-image {
  height: 100%;
  width: 100%;
  object-fit: contain;
  cursor: pointer;
}

.carousel-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 10px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.6);
  transition: opacity 0.3s;
}

.carousel-indicator {
  margin-top: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.indicator-text {
  font-size: 14px;
  color: var(--text-secondary);
  background-color: #f5f7fa;
  padding: 3px 12px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  font-weight: 500;
}

.main-image-indicator {
  font-size: 12px;
  color: #fff;
  background-color: #E6A23C;
  padding: 3px 10px;
  border-radius: 10px;
  font-weight: 500;
}

.is-main-slide {
  border: 2px solid #E6A23C;
}

/* 占位图样式 */
.empty-image-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 280px;
  background-color: #f8f9fb;
  border-radius: 8px;
  border: 2px dashed #dcdfe6;
  padding: 20px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.03);
}

.placeholder-logo {
  max-width: 220px;
  max-height: 180px;
  opacity: 0.85;
  margin-bottom: 20px;
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.placeholder-text {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 15px;
  background-color: rgba(64, 158, 255, 0.1);
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: 500;
}





.mr-5 {
  margin-right: 5px;
}

/* 表单底部按钮 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}



/* 上传进度条样式 */
.upload-progress {
  padding: 20px;
}

.progress-text {
  display: block;
  margin-bottom: 10px;
  font-size: 14px;
  color: var(--text-regular);
}

/* 提交成功状态 */
.result-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
}



/* 响应式调整 */
@media screen and (max-width: 900px) {
  .content-form {
    max-width: 100%;
  }
}

@media screen and (max-width: 768px) {
  .publish-found-container {
    padding: 15px;
  }

  .page-header {
    margin-bottom: 15px;
  }

  .content-form {
    padding: 20px;
  }



  .image-carousel-container {
    margin-bottom: 20px;
  }

  .carousel-actions {
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 5px;
  }

  .carousel-actions .el-button {
    font-size: 12px;
    padding: 6px 10px;
  }
}

@media screen and (max-width: 480px) {
  .publish-found-container {
    padding: 10px;
  }

  .content-form {
    padding: 15px;
  }

  .form-header h3 {
    font-size: 15px;
  }

  .section-title {
    font-size: 14px;
  }



  .el-carousel {
    height: 180px;
  }

  .carousel-actions {
    display: none;
  }

  .carousel-indicator {
    margin-top: 5px;
  }




}
</style>

<style>
@import '@/styles/publish-found-item.css';
</style>
