package com.tomato.lostfoundsystem.service.Impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.MatchNotification;
import com.tomato.lostfoundsystem.mapper.MatchNotificationMapper;
import com.tomato.lostfoundsystem.service.MatchNotificationService;
import com.tomato.lostfoundsystem.service.WebSocketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 匹配通知服务实现类
 */
@Slf4j
@Service
public class MatchNotificationServiceImpl implements MatchNotificationService {

    @Autowired
    private MatchNotificationMapper matchNotificationMapper;

    @Autowired
    private WebSocketService webSocketService;

    @Value("${match.notification.similarity-threshold:0.7}")
    private Float defaultSimilarityThreshold;

    @Override
    @Transactional
    public Result createMatchNotification(Long userId, Long matchHistoryId, Long itemId, String itemType,
                                         Float similarity, Map<String, Object> itemInfo) {
        try {
            log.info("【智能匹配】创建匹配通知，用户ID: {}, 物品ID: {}, 物品类型: {}, 相似度: {}", userId, itemId, itemType, similarity);

            // 检查是否已经为该匹配结果发送过通知 - 使用match_notifications表
            if (matchNotificationMapper.checkNotificationExists(userId, itemId, itemType) > 0) {
                log.info("【数据库表使用】在match_notifications表中发现已存在的通知记录，用户ID: {}, 物品ID: {}", userId, itemId);
                return Result.success("通知已存在");
            }

            // 创建通知对象 - 将存储到match_notifications表
            MatchNotification notification = new MatchNotification();
            notification.setUserId(userId);
            notification.setMatchHistoryId(matchHistoryId);
            notification.setItemId(itemId);
            notification.setItemType(itemType);
            notification.setSimilarity(similarity);
            notification.setIsRead(false);

            // 构建统一的元数据对象
            Map<String, Object> metadataMap = new HashMap<>();

            // 添加匹配类型
            if (itemInfo.containsKey("match_type")) {
                String matchType = (String) itemInfo.get("match_type");
                metadataMap.put("matchType", matchType);
                notification.setMatchType(matchType); // 保持向后兼容
            }

            // 添加相似度详情
            if (itemInfo.containsKey("match_details")) {
                Map<String, Object> matchDetails = (Map<String, Object>) itemInfo.get("match_details");
                metadataMap.put("similarityDetails", matchDetails);

                // 保持向后兼容，同时设置单独的相似度字段
                try {
                    if (matchDetails.containsKey("TEXT_TO_TEXT")) {
                        notification.setTextToTextSimilarity(parseFloat(matchDetails.get("TEXT_TO_TEXT")));
                    }
                    if (matchDetails.containsKey("TEXT_TO_IMAGE")) {
                        notification.setTextToImageSimilarity(parseFloat(matchDetails.get("TEXT_TO_IMAGE")));
                    }
                    if (matchDetails.containsKey("IMAGE_TO_TEXT")) {
                        notification.setImageToTextSimilarity(parseFloat(matchDetails.get("IMAGE_TO_TEXT")));
                    }
                    if (matchDetails.containsKey("IMAGE_TO_IMAGE")) {
                        notification.setImageToImageSimilarity(parseFloat(matchDetails.get("IMAGE_TO_IMAGE")));
                    }
                } catch (Exception e) {
                    log.warn("【智能匹配】设置相似度详情失败: {}", e.getMessage());
                }
            }

            // 添加匹配时间
            metadataMap.put("matchTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 如果itemInfo中有metadata，合并到我们的元数据中
            if (itemInfo.containsKey("metadata") && itemInfo.get("metadata") instanceof Map) {
                Map<String, Object> extraMetadata = (Map<String, Object>) itemInfo.get("metadata");
                metadataMap.putAll(extraMetadata);
            }

            // 序列化元数据
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                String metadataJson = objectMapper.writeValueAsString(metadataMap);
                notification.setMetadata(metadataJson);
            } catch (Exception e) {
                log.warn("【智能匹配】序列化元数据失败: {}", e.getMessage());
            }

            log.info("【数据库表使用】准备向match_notifications表插入记录");

            // 生成通知标题和内容
            String itemName = (String) itemInfo.get("name"); // 使用"name"键获取物品名称
            if (itemName == null) {
                // 兼容旧版本，尝试使用"itemName"键
                itemName = (String) itemInfo.get("itemName");
            }

            // 如果物品名称仍然为null，使用默认值
            if (itemName == null) {
                itemName = "未知物品";
                log.warn("【智能匹配】物品名称为null，使用默认值: {}", itemName);
            }

            // 获取地点信息
            String location = null;
            if ("LOST".equals(itemType)) {
                location = (String) itemInfo.get("location"); // 失物的地点字段
                if (location == null) {
                    location = (String) itemInfo.get("lostLocation"); // 尝试使用lostLocation字段
                }
            } else {
                location = (String) itemInfo.get("location"); // 拾物的地点字段
                if (location == null) {
                    location = (String) itemInfo.get("foundLocation"); // 尝试使用foundLocation字段
                }
            }

            // 获取时间信息
            String time = null;
            if ("LOST".equals(itemType)) {
                time = (String) itemInfo.get("time"); // 失物的时间字段
                if (time == null) {
                    time = (String) itemInfo.get("lostTime"); // 尝试使用lostTime字段
                }
            } else {
                time = (String) itemInfo.get("time"); // 拾物的时间字段
                if (time == null) {
                    time = (String) itemInfo.get("foundTime"); // 尝试使用foundTime字段
                }
            }

            // 记录获取到的信息
            log.info("【智能匹配】通知信息: 物品名称={}, 地点={}, 时间={}", itemName, location, time);

            String title = "发现高度匹配的" + ("LOST".equals(itemType) ? "失物" : "拾物") + "信息";

            StringBuilder contentBuilder = new StringBuilder();
            contentBuilder.append("系统发现一条与您相关的")
                         .append("LOST".equals(itemType) ? "失物" : "拾物")
                         .append("信息，匹配度为")
                         .append(String.format("%.0f%%", similarity * 100))
                         .append("。\n");

            contentBuilder.append("物品名称：").append(itemName).append("\n");
            if (location != null && !location.isEmpty()) {
                contentBuilder.append("地点：").append(location).append("\n");
            }
            if (time != null && !time.isEmpty()) {
                contentBuilder.append("时间：").append(time).append("\n");
            }

            // 添加匹配类型信息（如果有）
            if (itemInfo.containsKey("match_type")) {
                String matchType = (String) itemInfo.get("match_type");
                String matchTypeLabel = getMatchTypeLabel(matchType);
                if (matchTypeLabel != null) {
                    contentBuilder.append("匹配类型：").append(matchTypeLabel).append("\n");
                }
            }

            contentBuilder.append("点击查看详情并联系对方。");

            notification.setTitle(title);
            notification.setContent(contentBuilder.toString());

            // 保存通知 - 使用match_notifications表
            matchNotificationMapper.insertNotification(notification);
            log.info("【数据库表使用】向match_notifications表插入记录成功，通知ID: {}", notification.getId());

            // 通过WebSocket推送通知
            Map<String, Object> wsMessage = new HashMap<>();
            wsMessage.put("type", "MATCH_NOTIFICATION");
            wsMessage.put("notificationId", notification.getId());
            wsMessage.put("title", notification.getTitle());
            wsMessage.put("content", notification.getContent());
            wsMessage.put("itemId", itemId);
            wsMessage.put("itemType", itemType);
            wsMessage.put("similarity", similarity);
            wsMessage.put("time", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 添加元数据到WebSocket消息
            if (itemInfo.containsKey("metadata")) {
                wsMessage.put("metadata", itemInfo.get("metadata"));
            }

            // 添加匹配详情到WebSocket消息
            if (itemInfo.containsKey("match_details")) {
                wsMessage.put("match_details", itemInfo.get("match_details"));
            }

            webSocketService.sendMessage(userId, wsMessage);
            log.info("【智能匹配】WebSocket通知推送成功，用户ID: {}", userId);

            return Result.success("通知创建成功");
        } catch (Exception e) {
            log.error("创建匹配通知失败", e);
            return Result.fail("创建通知失败: " + e.getMessage());
        }
    }

    /**
     * 获取匹配类型的友好标签
     *
     * @param matchType 匹配类型代码
     * @return 友好标签
     */
    private String getMatchTypeLabel(String matchType) {
        if (matchType == null) {
            return null;
        }

        switch (matchType) {
            case "IMAGE_TO_IMAGE":
                return "外观匹配";
            case "IMAGE_TO_TEXT":
                return "图像-描述匹配";
            case "TEXT_TO_IMAGE":
                return "描述-图像匹配";
            case "TEXT_TO_TEXT":
                return "描述匹配";
            default:
                return "综合匹配";
        }
    }

    /**
     * 将对象解析为Float
     *
     * @param value 要解析的对象
     * @return 解析后的Float值，如果解析失败则返回null
     */
    private Float parseFloat(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Number) {
            return ((Number) value).floatValue();
        }

        try {
            return Float.parseFloat(value.toString());
        } catch (NumberFormatException e) {
            log.warn("解析Float失败: {}", value);
            return null;
        }
    }

    @Override
    public Result<List<MatchNotification>> getUserNotifications(Long userId) {
        try {
            log.info("【智能匹配】获取用户通知列表，用户ID: {}", userId);
            // 从match_notifications表获取通知列表
            List<MatchNotification> notifications = matchNotificationMapper.getNotificationsByUserId(userId);
            log.info("【数据库表使用】从match_notifications表查询用户ID: {}的通知列表，共{}条记录", userId, notifications.size());
            return Result.success(notifications);
        } catch (Exception e) {
            log.error("获取用户通知列表失败", e);
            return Result.fail("获取通知列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result markNotificationAsRead(Long notificationId, Long userId) {
        try {
            log.info("【智能匹配】标记通知为已读，通知ID: {}, 用户ID: {}", notificationId, userId);
            // 更新match_notifications表中的记录
            matchNotificationMapper.markAsRead(notificationId);
            log.info("【数据库表使用】更新match_notifications表中通知ID: {}的已读状态", notificationId);
            return Result.success("标记已读成功");
        } catch (Exception e) {
            log.error("标记通知为已读失败", e);
            return Result.fail("标记已读失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> getUnreadNotificationCount(Long userId) {
        try {
            log.info("【智能匹配】获取用户未读通知数量，用户ID: {}", userId);
            // 从match_notifications表统计未读通知数量
            int count = matchNotificationMapper.getUnreadCount(userId);
            log.info("【数据库表使用】从match_notifications表统计用户ID: {}的未读通知数量: {}", userId, count);
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取用户未读通知数量失败", e);
            return Result.fail("获取未读通知数量失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result processHighSimilarityMatches(List<Map<String, Object>> matchResults, Long matchHistoryId,
                                              Long userId, Float similarityThreshold) {
        try {
            log.info("【智能匹配】处理高相似度匹配结果，用户ID: {}, 匹配历史ID: {}, 阈值: {}", userId, matchHistoryId, similarityThreshold);

            // 如果未指定阈值，使用默认阈值
            if (similarityThreshold == null) {
                similarityThreshold = defaultSimilarityThreshold;
                log.info("【智能匹配】使用默认相似度阈值: {}", similarityThreshold);
            }

            // 如果没有匹配结果，直接返回
            if (matchResults == null || matchResults.isEmpty()) {
                log.info("【智能匹配】没有匹配结果，不发送通知");
                return Result.success("没有匹配结果，不发送通知");
            }

            // 找出相似度最高的结果
            Map<String, Object> highestSimilarityResult = null;
            Float highestSimilarity = 0f;

            for (Map<String, Object> result : matchResults) {
                Float similarity = (Float) result.get("similarity");

                // 只考虑高于阈值的结果
                if (similarity >= similarityThreshold) {
                    // 如果当前结果的相似度高于之前找到的最高相似度，则更新
                    if (highestSimilarityResult == null || similarity > highestSimilarity) {
                        highestSimilarityResult = result;
                        highestSimilarity = similarity;
                    }
                }
            }

            // 如果找到了高相似度的结果，则发送通知
            if (highestSimilarityResult != null) {
                Long itemId = Long.valueOf(highestSimilarityResult.get("id").toString());
                String itemType = (String) highestSimilarityResult.get("itemType");
                Float similarity = (Float) highestSimilarityResult.get("similarity");

                log.info("【智能匹配】发现最高相似度匹配结果，物品ID: {}, 类型: {}, 相似度: {}", itemId, itemType, similarity);

                // 添加匹配详情到元数据
                Map<String, Object> metadata = new HashMap<>();

                // 保存四种相似度的详细信息（如果有）
                if (highestSimilarityResult.containsKey("match_details")) {
                    metadata.put("similarityDetails", highestSimilarityResult.get("match_details"));
                }

                // 保存匹配类型（如果有）
                if (highestSimilarityResult.containsKey("match_type")) {
                    metadata.put("matchType", highestSimilarityResult.get("match_type"));
                }

                // 添加匹配时间
                metadata.put("matchTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                // 添加元数据到结果中
                highestSimilarityResult.put("metadata", metadata);

                // 创建通知 - 使用match_notifications表
                Result notificationResult = createMatchNotification(userId, matchHistoryId, itemId, itemType,
                                                                  similarity, highestSimilarityResult);

                if (notificationResult.getCode() == 200) {
                    log.info("【数据库表使用】成功向match_notifications表插入最高相似度匹配通知");
                    return Result.success("处理完成，发送了1条最高相似度通知");
                } else {
                    log.warn("【智能匹配】创建通知失败: {}", notificationResult.getMessage());
                    return Result.fail("创建通知失败: " + notificationResult.getMessage());
                }
            } else {
                log.info("【智能匹配】没有找到高于阈值的匹配结果，不发送通知");
                return Result.success("没有找到高于阈值的匹配结果，不发送通知");
            }
        } catch (Exception e) {
            log.error("处理高相似度匹配结果失败", e);
            return Result.fail("处理失败: " + e.getMessage());
        }
    }
}
