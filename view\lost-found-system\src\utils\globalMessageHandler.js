/**
 * 全局消息处理器
 *
 * 该模块负责：
 * 1. 全局范围内处理WebSocket消息
 * 2. 播放通知音效
 * 3. 更新未读消息计数
 * 4. 触发全局事件
 */

import { ElNotification } from 'element-plus'
import { useUserStore } from '@/stores'
import router from '@/router'

// 音频缓存
const audioCache = {
  chat: null,     // 聊天消息音频
  system: null,   // 系统通知音频
  contact: null   // 联系人上线音频
}

// 音频加载状态
const audioLoadingState = {
  isLoading: false,
  isLoaded: false,
  error: null
}

// 联系人列表缓存
let contactsCache = []

// 联系人在线状态缓存
const onlineStatusCache = new Map()

// 初始化音频
function initAudio() {
  if (audioLoadingState.isLoading || audioLoadingState.isLoaded) {
    return
  }

  console.log('初始化全局音频...')
  audioLoadingState.isLoading = true

  // 生成时间戳参数避免缓存问题
  const timestamp = Date.now()

  // 聊天消息音频
  loadAudio('chat', [
    `/sounds/chat-notification.mp3?t=${timestamp}`,
    `./sounds/chat-notification.mp3?t=${timestamp}`,
    `../sounds/chat-notification.mp3?t=${timestamp}`,
    `/notification.mp3?t=${timestamp}`,
    `./notification.mp3?t=${timestamp}`
  ])

  // 系统通知音频
  loadAudio('system', [
    `/sounds/system-notification.mp3?t=${timestamp}`,
    `./sounds/system-notification.mp3?t=${timestamp}`,
    `../sounds/system-notification.mp3?t=${timestamp}`,
    `/system-notification.mp3?t=${timestamp}`,
    `./system-notification.mp3?t=${timestamp}`
  ])

  // 联系人上线音频
  loadAudio('contact', [
    `/sounds/contact-online.mp3?t=${timestamp}`,
    `./sounds/contact-online.mp3?t=${timestamp}`,
    `../sounds/contact-online.mp3?t=${timestamp}`,
    `/contact-online.mp3?t=${timestamp}`,
    `./contact-online.mp3?t=${timestamp}`
  ])
}

// 加载音频
function loadAudio(type, paths) {
  tryLoadAudio(type, paths, 0)
}

// 尝试加载音频
function tryLoadAudio(type, paths, index) {
  if (index >= paths.length) {
    console.error(`所有${type}类型音频路径都加载失败`)
    audioLoadingState.error = new Error(`所有${type}类型音频路径都加载失败`)
    return
  }

  try {
    const path = paths[index]
    console.log(`尝试加载${type}类型音频(路径${index+1}): ${path}`)

    const audio = new Audio(path)

    audio.addEventListener('canplaythrough', () => {
      console.log(`${type}类型音频加载成功: ${path}`)
      audioCache[type] = audio

      // 检查是否所有音频都已加载完成
      if (audioCache.chat && audioCache.system && audioCache.contact) {
        audioLoadingState.isLoaded = true
        audioLoadingState.isLoading = false
      }
    })

    audio.addEventListener('error', (error) => {
      console.error(`${type}类型音频加载失败(路径${index+1}): ${path}`, error)
      // 尝试下一个路径
      tryLoadAudio(type, paths, index + 1)
    })
  } catch (error) {
    console.error(`创建${type}类型音频对象失败(路径${index+1}):`, error)
    // 尝试下一个路径
    tryLoadAudio(type, paths, index + 1)
  }
}

// 播放通知声音
function playNotificationSound(type = 'chat') {
  try {
    // 确保音频已初始化
    if (!audioLoadingState.isLoaded) {
      console.log('通知音频尚未加载，初始化中...')
      initAudio()
      // 由于异步加载，可能需要等待一段时间
      setTimeout(() => {
        if (audioCache[type]) {
          playSound(type)
        } else {
          console.warn(`${type}类型通知音频仍未加载完成，尝试播放其他可用音频`)
          // 尝试播放任何可用的音频
          if (audioCache.chat) playSound('chat')
          else if (audioCache.system) playSound('system')
          else if (audioCache.contact) playSound('contact')
          else {
            console.warn('所有通知音频都未加载完成，尝试直接创建音频对象')
            // 尝试直接创建音频对象
            try {
              const audio = new Audio('/notification.mp3')
              audio.play().catch(err => {
                console.warn('直接播放通知音频失败:', err)
              })
            } catch (error) {
              console.warn('创建直接音频对象失败:', error)
            }
          }
        }
      }, 1000)
      return
    }

    // 播放指定类型的音频
    playSound(type)
  } catch (error) {
    console.warn('播放通知音效失败:', error)
  }
}

// 播放音频
function playSound(soundType) {
  try {
    // 获取指定类型的音频
    const audio = audioCache[soundType]

    // 如果指定类型的音频不可用，尝试使用其他类型
    if (!audio) {
      console.warn(`${soundType}类型音频不可用，尝试使用其他类型`)
      if (soundType !== 'chat' && audioCache.chat) return playSound('chat')
      if (soundType !== 'system' && audioCache.system) return playSound('system')
      if (soundType !== 'contact' && audioCache.contact) return playSound('contact')
      console.warn('没有可用的音频')
      return
    }

    console.log(`播放${soundType}类型音频...`)

    // 重置音频到开始位置（如果之前播放过）
    audio.currentTime = 0

    // 检查用户是否已与页面交互
    const hasInteracted = document.documentElement.hasAttribute('data-user-interacted') ||
                          document.visibilityState === 'visible';

    if (!hasInteracted) {
      console.warn(`用户尚未与页面交互，无法播放${soundType}类型音频`);
      // 存储音频播放请求，等待用户交互后播放
      window.pendingAudioPlay = window.pendingAudioPlay || [];
      window.pendingAudioPlay.push(soundType);
      return;
    }

    // 播放音频
    audio.play().then(() => {
      console.log(`${soundType}类型音频播放成功`)
    }).catch(error => {
      console.error(`播放${soundType}类型音频失败:`, error)

      // 如果是因为用户未交互导致的错误，存储音频播放请求
      if (error.name === 'NotAllowedError') {
        console.warn('由于浏览器策略限制，需要用户交互后才能播放音频');
        window.pendingAudioPlay = window.pendingAudioPlay || [];
        window.pendingAudioPlay.push(soundType);
      }
    })
  } catch (error) {
    console.error(`播放${soundType}类型音频时出错:`, error)
  }
}

// 处理新消息
function handleNewMessage(message) {
  try {
    console.log('全局消息处理器收到新消息:', message)

    // 获取当前用户ID
    const userStore = useUserStore()
    const currentUserId = userStore.userInfo?.id

    // 确保消息有必要的字段
    if (!message.senderId && !message.sender) {
      console.warn('消息缺少发送者ID:', message)
      return
    }

    if (!message.receiverId && !message.receiver) {
      console.warn('消息缺少接收者ID:', message)
      return
    }

    // 规范化消息对象
    const normalizedMessage = {
      ...message,
      senderId: message.senderId || message.sender,
      receiverId: message.receiverId || message.receiver,
      content: message.message || message.content,
      messageType: message.messageType || 'TEXT',
      time: message.time || message.timestamp || new Date().toISOString(),
      isSelf: (message.senderId || message.sender) == currentUserId
    }

    console.log('规范化后的消息:', normalizedMessage)

    // 检查消息是否与当前用户相关
    if (normalizedMessage.senderId != currentUserId && normalizedMessage.receiverId != currentUserId) {
      console.log('消息与当前用户无关，忽略消息')
      return
    }

    // 触发全局事件
    window.dispatchEvent(new CustomEvent('chat-message', {
      detail: normalizedMessage
    }))
    console.log('已触发chat-message全局事件')

    // 触发刷新联系人列表事件 - 使用防抖版本
    window.dispatchEvent(new CustomEvent('refresh-contacts', {
      detail: { message: normalizedMessage }
    }))
    console.log('已触发refresh-contacts全局事件')

    // 触发全局刷新联系人列表事件 - 使用防抖版本
    window.dispatchEvent(new CustomEvent('global-refresh-contacts', {
      detail: { timestamp: Date.now() }
    }))
    console.log('已触发global-refresh-contacts全局事件')

    // 注意：移除了force-refresh-chat事件，避免页面闪烁

    // 如果消息不是自己发送的，播放通知音效并显示通知
    if (!normalizedMessage.isSelf) {
      // 播放通知音效
      playNotificationSound('chat')

      // 显示通知
      showNotification(normalizedMessage)
    }
  } catch (error) {
    console.error('处理新消息时出错:', error)
  }
}

// 显示通知
function showNotification(message) {
  try {
    // 获取发送者名称
    const senderName = message.senderName || `用户${message.senderId}`

    // 获取消息内容
    let content = message.content || '收到一条新消息'

    // 如果消息内容过长，截断显示
    if (content.length > 50) {
      content = content.substring(0, 50) + '...'
    }

    // 显示通知
    ElNotification({
      title: `来自 ${senderName} 的新消息`,
      message: content,
      type: 'info',
      duration: 5000,
      onClick: () => {
        // 点击通知跳转到聊天页面
        router.push({
          path: '/chat',
          query: { contactId: message.senderId }
        })
      }
    })
  } catch (error) {
    console.error('显示通知时出错:', error)
  }
}

// 处理用户上线通知
function handleUserOnline(event) {
  try {
    // 从事件中获取数据
    const { userId, timestamp, isSelf, username, status } = event.detail
    console.log('全局消息处理器收到用户上线事件:', event.detail)

    // 如果事件中没有isSelf标志，则手动检查
    let isCurrentUser = isSelf
    if (isCurrentUser === undefined) {
      // 获取当前用户ID
      const userStore = useUserStore()
      const currentUserId = userStore.userInfo?.id

      // 检查是否是当前用户自己
      isCurrentUser = String(userId) === String(currentUserId)
    }

    console.log(`全局消息处理器: 上线用户ID: ${userId}, 是否是自己: ${isCurrentUser}`)

    // 只有当其他用户上线时才播放提示音
    if (!isCurrentUser) {
      console.log('全局消息处理器: 其他用户上线，播放联系人上线音效')
      playNotificationSound('contact')

      // 可以在这里添加桌面通知
      try {
        // 显示通知
        ElNotification({
          title: '用户上线通知',
          message: `用户 ${username || userId} 已上线`,
          type: 'success',
          duration: 3000
        })
      } catch (error) {
        console.warn('显示用户上线通知失败:', error)
      }
    } else {
      console.log('全局消息处理器: 自己上线，不播放联系人上线音效')
    }

    // 触发用户在线状态变更事件
    // 使用setTimeout延迟执行，避免递归更新
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('user-online-status', {
        detail: {
          userId,
          status: status || 'online',
          isOnline: true,
          timestamp: timestamp || Date.now(),
          isSelf: isCurrentUser
        }
      }))
      console.log('全局消息处理器: 已触发user-online-status事件')
    }, 0)

    // 触发全局刷新联系人列表事件
    window.dispatchEvent(new CustomEvent('global-refresh-contacts', {
      detail: { timestamp: timestamp || Date.now() }
    }))
    console.log('全局消息处理器: 已触发global-refresh-contacts事件')

    // 如果是其他用户上线，还可以触发一个特定的事件，用于更新UI
    if (!isCurrentUser) {
      window.dispatchEvent(new CustomEvent('other-user-online', {
        detail: {
          userId,
          username: username || userId,
          timestamp: timestamp || Date.now()
        }
      }))
      console.log('全局消息处理器: 已触发other-user-online事件')
    }
  } catch (error) {
    console.error('全局消息处理器: 处理用户上线通知时出错:', error)
  }
}

// 更新联系人列表
function updateContacts(contacts) {
  if (!contacts || !Array.isArray(contacts)) {
    console.warn('更新联系人列表失败: 无效的联系人列表', contacts)
    return
  }

  console.log('更新联系人列表:', contacts.length, '个联系人')

  // 更新缓存
  contactsCache = [...contacts]

  // 更新在线状态缓存
  contacts.forEach(contact => {
    if (contact.id && contact.online !== undefined) {
      onlineStatusCache.set(String(contact.id), !!contact.online)
    }
  })

  // 触发联系人列表更新事件
  window.dispatchEvent(new CustomEvent('contacts-updated', {
    detail: { contacts: contactsCache, timestamp: Date.now() }
  }))

  console.log('联系人列表已更新，已触发contacts-updated事件')
}

// 更新联系人在线状态
function updateContactOnlineStatus(userId, isOnline) {
  if (!userId) return

  console.log(`更新联系人 ${userId} 在线状态:`, isOnline)

  // 更新在线状态缓存
  onlineStatusCache.set(String(userId), !!isOnline)

  // 更新联系人列表中的在线状态
  const updatedContacts = contactsCache.map(contact => {
    if (String(contact.id) === String(userId)) {
      return { ...contact, online: !!isOnline }
    }
    return contact
  })

  // 更新缓存
  contactsCache = updatedContacts

  // 触发联系人列表更新事件
  // 使用setTimeout延迟执行，避免递归更新
  setTimeout(() => {
    window.dispatchEvent(new CustomEvent('contacts-updated', {
      detail: { contacts: contactsCache, timestamp: Date.now() }
    }))
    console.log('联系人在线状态已更新，已触发contacts-updated事件')
  }, 0)

  // 如果是上线状态，检查是否需要显示通知
  if (isOnline) {
    // 获取当前用户ID
    const userStore = useUserStore()
    const currentUserId = userStore.userInfo?.id

    // 如果不是当前用户自己上线，显示通知
    if (String(userId) !== String(currentUserId)) {
      // 查找联系人信息
      const contact = contactsCache.find(c => String(c.id) === String(userId))
      const contactName = contact ? (contact.username || contact.nickname || contact.name) : userId

      // 播放联系人上线音效
      playNotificationSound('contact')

      // 显示通知
      try {
        ElNotification({
          title: '联系人上线',
          message: `${contactName} 已上线`,
          type: 'success',
          duration: 3000
        })
      } catch (error) {
        console.warn('显示联系人上线通知失败:', error)
      }
    }
  }
}

// 设置用户交互事件监听器
function setupUserInteractionListeners() {
  // 用户交互事件列表
  const interactionEvents = [
    'mousedown', 'keydown', 'touchstart', 'scroll', 'click'
  ];

  // 处理用户交互事件
  const handleUserInteraction = () => {
    // 标记用户已交互
    document.documentElement.setAttribute('data-user-interacted', 'true');

    // 播放待播放的音频
    if (window.pendingAudioPlay && window.pendingAudioPlay.length > 0) {
      console.log('用户已交互，播放待播放的音频:', window.pendingAudioPlay);

      // 复制待播放列表，避免在播放过程中修改原列表
      const pendingAudios = [...window.pendingAudioPlay];

      // 清空待播放列表
      window.pendingAudioPlay = [];

      // 播放第一个待播放的音频
      if (pendingAudios.length > 0) {
        playSound(pendingAudios[0]);
      }
    }
  };

  // 添加所有交互事件监听器
  interactionEvents.forEach(eventType => {
    document.addEventListener(eventType, handleUserInteraction, { once: false, passive: true });
  });

  console.log('用户交互事件监听器已设置');
}

// 初始化
function init() {
  console.log('初始化全局消息处理器...')

  // 初始化音频
  initAudio()

  // 添加全局事件监听
  window.addEventListener('websocket-message', (event) => {
    handleNewMessage(event.detail)
  })

  // 监听用户上线事件 - 传递整个事件对象
  window.addEventListener('user-online', (event) => {
    handleUserOnline(event)
  })

  // 监听联系人列表更新事件
  window.addEventListener('contacts-list-updated', (event) => {
    if (event.detail && event.detail.contacts) {
      updateContacts(event.detail.contacts)
    }
  })

  // 监听用户在线状态变更事件
  window.addEventListener('user-online-status', (event) => {
    if (event.detail && event.detail.userId) {
      const { userId, isOnline, status } = event.detail
      const online = isOnline !== undefined ? isOnline : (status === 'online')
      updateContactOnlineStatus(userId, online)
    }
  })

  // 设置用户交互事件监听器
  setupUserInteractionListeners()

  console.log('全局消息处理器初始化完成')
}

// 导出
export default {
  init,
  playNotificationSound,
  handleNewMessage,
  handleUserOnline,
  updateContacts,
  updateContactOnlineStatus,
  // 获取联系人列表
  getContacts: () => contactsCache,
  // 获取联系人在线状态
  getContactOnlineStatus: (userId) => onlineStatusCache.get(String(userId)) || false
}
