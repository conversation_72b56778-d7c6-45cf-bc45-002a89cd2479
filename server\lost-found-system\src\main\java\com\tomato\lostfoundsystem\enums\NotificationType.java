package com.tomato.lostfoundsystem.enums;

/**
 * 通知类型枚举
 * 用于区分不同类型的通知
 */
public enum NotificationType {
    // 系统通知
    SYSTEM("SYSTEM", "系统通知"),
    
    // 审核通知
    AUDIT_APPROVED("AUDIT_APPROVED", "审核通过"),
    AUDIT_REJECTED("AUDIT_REJECTED", "审核拒绝"),
    
    // 匹配通知
    MATCH("MATCH", "匹配通知"),
    
    // 认领通知
    CLAIM("CLAIM", "认领通知"),
    CLAIM_APPROVED("CLAIM_APPROVED", "认领通过"),
    CLAIM_REJECTED("CLAIM_REJECTED", "认领拒绝"),
    
    // 管理员通知
    ADMIN("ADMIN", "管理员通知"),
    
    // 公告通知
    ANNOUNCEMENT("ANNOUNCEMENT", "系统公告");
    
    private final String code;
    private final String description;
    
    NotificationType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举值
     * @param code 代码
     * @return 枚举值，如果不存在则返回SYSTEM
     */
    public static NotificationType fromCode(String code) {
        if (code == null) {
            return SYSTEM;
        }
        
        for (NotificationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        
        return SYSTEM;
    }
    
    /**
     * 根据通知标题推断通知类型
     * @param title 通知标题
     * @return 推断的通知类型
     */
    public static NotificationType inferFromTitle(String title) {
        if (title == null) {
            return SYSTEM;
        }
        
        if (title.contains("通过审核")) {
            return AUDIT_APPROVED;
        } else if (title.contains("未通过审核")) {
            return AUDIT_REJECTED;
        } else if (title.contains("已认领") || title.contains("认领申请")) {
            return CLAIM;
        } else if (title.contains("系统公告") || title.contains("新公告")) {
            return ANNOUNCEMENT;
        } else if (title.contains("匹配") || title.contains("相似")) {
            return MATCH;
        } else {
            return SYSTEM;
        }
    }
}
