<template>
  <div class="statistics-card">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.activeUsers }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon">
            <el-icon><Goods /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalItems }}</div>
            <div class="stat-label">总物品数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.returnedItems }}</div>
            <div class="stat-label">已归还物品</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-icon">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.successMatches }}</div>
            <div class="stat-label">已找回物品</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { User, Goods, Check, Connection } from '@element-plus/icons-vue'
import { getHomePageStatistics } from '@/api/statistics'
import { ElMessage } from 'element-plus'

// 统计数据
const stats = ref({
  activeUsers: 0,
  totalItems: 0,
  returnedItems: 0,
  successMatches: 0
})

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const res = await getHomePageStatistics()
    if (res.code === 200 && res.data) {
      stats.value = res.data
      console.log('获取到的统计数据:', stats.value)
    } else {
      ElMessage.warning(res.message || '获取统计数据失败')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败，请稍后重试')
  }
}

// 生命周期钩子
onMounted(() => {
  // 获取统计数据
  fetchStatistics()
})
</script>

<style scoped>
.statistics-card {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}
</style>
