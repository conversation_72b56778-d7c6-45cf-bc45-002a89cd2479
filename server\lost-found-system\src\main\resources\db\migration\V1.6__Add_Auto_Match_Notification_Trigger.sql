-- 创建触发器，在发布新的失物信息时自动触发智能匹配
DELIMITER //
CREATE TRIGGER IF NOT EXISTS after_lost_item_insert
AFTER INSERT ON lost_items
FOR EACH ROW
BEGIN
    -- 如果审核状态为已通过，则触发智能匹配
    IF NEW.audit_status = 'APPROVED' THEN
        -- 在这里我们只记录一个日志，实际的匹配操作由应用程序完成
        INSERT INTO system_logs (log_type, operation, entity_type, entity_id, user_id, details, created_at)
        VALUES ('AUTO_MATCH', 'INSERT', 'LOST_ITEM', NEW.id, NEW.user_id, 
                CONCAT('自动触发智能匹配: 失物ID=', NEW.id, ', 名称=', NEW.item_name), NOW());
    END IF;
END //
DELIMITER ;

-- 创建触发器，在发布新的拾物信息时自动触发智能匹配
DELIMITER //
CREATE TRIGGER IF NOT EXISTS after_found_item_insert
AFTER INSERT ON found_items
FOR EACH ROW
BEGIN
    -- 如果审核状态为已通过，则触发智能匹配
    IF NEW.audit_status = 'APPROVED' THEN
        -- 在这里我们只记录一个日志，实际的匹配操作由应用程序完成
        INSERT INTO system_logs (log_type, operation, entity_type, entity_id, user_id, details, created_at)
        VALUES ('AUTO_MATCH', 'INSERT', 'FOUND_ITEM', NEW.id, NEW.user_id, 
                CONCAT('自动触发智能匹配: 拾物ID=', NEW.id, ', 名称=', NEW.item_name), NOW());
    END IF;
END //
DELIMITER ;

-- 创建触发器，在审核通过失物信息时自动触发智能匹配
DELIMITER //
CREATE TRIGGER IF NOT EXISTS after_lost_item_audit_approved
AFTER UPDATE ON lost_items
FOR EACH ROW
BEGIN
    -- 如果审核状态从非APPROVED变为APPROVED，则触发智能匹配
    IF OLD.audit_status != 'APPROVED' AND NEW.audit_status = 'APPROVED' THEN
        -- 在这里我们只记录一个日志，实际的匹配操作由应用程序完成
        INSERT INTO system_logs (log_type, operation, entity_type, entity_id, user_id, details, created_at)
        VALUES ('AUTO_MATCH', 'UPDATE', 'LOST_ITEM', NEW.id, NEW.user_id, 
                CONCAT('审核通过后自动触发智能匹配: 失物ID=', NEW.id, ', 名称=', NEW.item_name), NOW());
    END IF;
END //
DELIMITER ;

-- 创建触发器，在审核通过拾物信息时自动触发智能匹配
DELIMITER //
CREATE TRIGGER IF NOT EXISTS after_found_item_audit_approved
AFTER UPDATE ON found_items
FOR EACH ROW
BEGIN
    -- 如果审核状态从非APPROVED变为APPROVED，则触发智能匹配
    IF OLD.audit_status != 'APPROVED' AND NEW.audit_status = 'APPROVED' THEN
        -- 在这里我们只记录一个日志，实际的匹配操作由应用程序完成
        INSERT INTO system_logs (log_type, operation, entity_type, entity_id, user_id, details, created_at)
        VALUES ('AUTO_MATCH', 'UPDATE', 'FOUND_ITEM', NEW.id, NEW.user_id, 
                CONCAT('审核通过后自动触发智能匹配: 拾物ID=', NEW.id, ', 名称=', NEW.item_name), NOW());
    END IF;
END //
DELIMITER ;
