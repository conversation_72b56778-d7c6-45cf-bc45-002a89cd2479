@startuml 校园失物招领系统E-R图

' 设置样式
skinparam linetype ortho
skinparam shadowing false
skinparam defaultFontName Arial
skinparam defaultFontSize 12
skinparam handwritten false
skinparam monochrome false

' 颜色设置
skinparam class {
  BackgroundColor<<User>> LightBlue
  BackgroundColor<<Item>> LightGreen
  BackgroundColor<<Chat>> LightPink
  BackgroundColor<<Match>> LightYellow
  BackgroundColor<<System>> LightGray
  BackgroundColor<<Audit>> Lavender
  BorderColor DarkGray
  ArrowColor DarkGray
}

skinparam entity {
  BackgroundColor White
  BorderColor Black
}

' 实体定义
entity "用户(users)" as users <<User>> {
  *user_id : 整型 <<PK>>
  --
  username : 字符串
  password : 字符串
  email : 字符串
  phone : 字符串
  avatar : 字符串
  create_time : 日期时间
  status : 整型
}

entity "失物(lost_items)" as lost_items <<Item>> {
  *item_id : 整型 <<PK>>
  --
  *user_id : 整型 <<FK>>
  title : 字符串
  description : 文本
  lost_time : 日期时间
  lost_location : 字符串
  category : 字符串
  status : 整型
  create_time : 日期时间
}

entity "招领(found_items)" as found_items <<Item>> {
  *item_id : 整型 <<PK>>
  --
  *user_id : 整型 <<FK>>
  title : 字符串
  description : 文本
  found_time : 日期时间
  found_location : 字符串
  category : 字符串
  status : 整型
  create_time : 日期时间
}

entity "物品图片(item_images)" as item_images <<Item>> {
  *image_id : 整型 <<PK>>
  --
  *item_id : 整型 <<FK>>
  item_type : 字符串
  image_url : 字符串
  upload_time : 日期时间
}

entity "特征向量(item_feature_vectors)" as item_feature_vectors <<Match>> {
  *vector_id : 整型 <<PK>>
  --
  *item_id : 整型 <<FK>>
  item_type : 字符串
  feature_vector : 二进制
  vector_type : 字符串
}

entity "聊天会话(chat_sessions)" as chat_sessions <<Chat>> {
  *session_id : 整型 <<PK>>
  --
  *user_id_1 : 整型 <<FK>>
  *user_id_2 : 整型 <<FK>>
  create_time : 日期时间
  update_time : 日期时间
  status : 整型
}

entity "聊天消息(chat_messages)" as chat_messages <<Chat>> {
  *message_id : 整型 <<PK>>
  --
  *session_id : 整型 <<FK>>
  *sender_id : 整型 <<FK>>
  content : 文本
  send_time : 日期时间
  message_type : 整型
}

entity "匹配结果(match_results)" as match_results <<Match>> {
  *lost_item_id : 整型 <<PK>>
  *found_item_id : 整型 <<PK>>
  --
  similarity_score : 浮点数
  match_time : 日期时间
}

entity "匹配通知(match_notifications)" as match_notifications <<Match>> {
  *notification_id : 整型 <<PK>>
  --
  *user_id : 整型 <<FK>>
  *lost_item_id : 整型 <<FK>>
  *found_item_id : 整型 <<FK>>
  notification_time : 日期时间
  read_status : 整型
}

entity "系统公告(system_announcements)" as system_announcements <<System>> {
  *announcement_id : 整型 <<PK>>
  --
  title : 字符串
  content : 文本
  publish_time : 日期时间
  importance : 整型
}

entity "公告阅读状态(user_announcement_reads)" as user_announcement_reads <<System>> {
  *user_id : 整型 <<PK>>
  *announcement_id : 整型 <<PK>>
  --
  read_time : 日期时间
}

entity "物品附件(item_attachments)" as item_attachments <<Item>> {
  *attachment_id : 整型 <<PK>>
  --
  *item_id : 整型 <<FK>>
  item_type : 字符串
  file_url : 字符串
  file_type : 字符串
  upload_time : 日期时间
}

entity "消息附件(message_attachments)" as message_attachments <<Chat>> {
  *attachment_id : 整型 <<PK>>
  --
  *message_id : 整型 <<FK>>
  file_url : 字符串
  file_type : 字符串
  upload_time : 日期时间
}

entity "消息读取状态(message_read_status)" as message_read_status <<Chat>> {
  *message_id : 整型 <<PK>>
  *user_id : 整型 <<PK>>
  --
  read_time : 日期时间
}

entity "系统配置(system_configs)" as system_configs <<System>> {
  *config_id : 整型 <<PK>>
  --
  config_key : 字符串
  config_value : 字符串
  description : 文本
  update_time : 日期时间
}

entity "用户通知(user_notifications)" as user_notifications <<User>> {
  *notification_id : 整型 <<PK>>
  --
  *user_id : 整型 <<FK>>
  notification_type : 整型
  content : 文本
  create_time : 日期时间
  read_status : 整型
}

entity "匹配历史(match_history)" as match_history <<Match>> {
  *history_id : 整型 <<PK>>
  --
  *user_id : 整型 <<FK>>
  *item_id : 整型 <<FK>>
  item_type : 字符串
  search_time : 日期时间
  search_params : 文本
}

entity "失物审计(lost_item_audit)" as lost_item_audit <<Audit>> {
  *audit_id : 整型 <<PK>>
  --
  *item_id : 整型 <<FK>>
  action : 字符串
  action_time : 日期时间
  action_user_id : 整型
  details : 文本
}

entity "招领审计(found_item_audit)" as found_item_audit <<Audit>> {
  *audit_id : 整型 <<PK>>
  --
  *item_id : 整型 <<FK>>
  action : 字符串
  action_time : 日期时间
  action_user_id : 整型
  details : 文本
}

' 关系定义
users ||--o{ lost_items : 发布
users ||--o{ found_items : 发布
users ||--o{ user_notifications : 接收
users ||--o{ match_history : 查询
users }|--o{ user_announcement_reads : 阅读
users }|--o{ message_read_status : 阅读

lost_items ||--o{ item_images : 包含
lost_items ||--o{ item_feature_vectors : 生成
lost_items ||--o{ item_attachments : 附加
lost_items ||--o{ lost_item_audit : 记录

found_items ||--o{ item_images : 包含
found_items ||--o{ item_feature_vectors : 生成
found_items ||--o{ item_attachments : 附加
found_items ||--o{ found_item_audit : 记录

lost_items }o--o{ found_items : 匹配 > match_results

chat_sessions }|--|| users : 参与者1
chat_sessions }|--|| users : 参与者2
chat_sessions ||--o{ chat_messages : 包含

chat_messages ||--o{ message_attachments : 附加
chat_messages }|--o{ message_read_status : 状态

system_announcements ||--o{ user_announcement_reads : 阅读状态

match_results ||--o{ match_notifications : 通知

' 图例
legend right
  |= 颜色 |= 模块 |
  |<#LightBlue>| 用户模块 |
  |<#LightGreen>| 物品模块 |
  |<#LightPink>| 聊天模块 |
  |<#LightYellow>| 匹配模块 |
  |<#LightGray>| 系统模块 |
  |<#Lavender>| 审计模块 |
endlegend

@enduml