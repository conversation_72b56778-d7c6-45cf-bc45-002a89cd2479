package com.tomato.lostfoundsystem.service;

/**
 * 通知事件服务接口
 * 
 * 该接口作为通知系统的中间层，负责处理通知事件，解耦通知服务和WebSocket处理器
 */
public interface NotificationEventService {
    
    /**
     * 发送WebSocket通知
     * 
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param notificationId 通知ID，可为null
     */
    void sendWebSocketNotification(Long userId, String title, String message, Long notificationId);
    
    /**
     * 检查通知是否已发送
     * 
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @return 是否已发送
     */
    boolean isNotificationSent(Long userId, Long notificationId);
    
    /**
     * 标记通知为已发送
     * 
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @param expirationSeconds 过期时间（秒）
     */
    void markNotificationAsSent(Long userId, Long notificationId, int expirationSeconds);
}
