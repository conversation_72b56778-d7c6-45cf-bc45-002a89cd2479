/**
 * 认证状态管理 Store
 * 用于管理登录对话框的显示状态和登录流程
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useUserStore } from './userStore'

export const useAuthStore = defineStore('auth', () => {
  // 用户存储
  const userStore = useUserStore()
  
  // 登录对话框状态
  const loginDialogVisible = ref(false)
  const defaultLoginTab = ref('login') // 'login' 或 'register'
  
  // 登录后的回调函数
  const loginSuccessCallback = ref(null)
  const loginCancelCallback = ref(null)
  
  /**
   * 显示登录对话框
   * @param {Object} options 配置选项
   * @param {string} options.tab 默认显示的标签页，'login' 或 'register'
   * @param {Function} options.onSuccess 登录成功的回调函数
   * @param {Function} options.onCancel 取消登录的回调函数
   */
  function showLoginDialog(options = {}) {
    defaultLoginTab.value = options.tab || 'login'
    loginSuccessCallback.value = options.onSuccess || null
    loginCancelCallback.value = options.onCancel || null
    loginDialogVisible.value = true
  }
  
  /**
   * 隐藏登录对话框
   */
  function hideLoginDialog() {
    loginDialogVisible.value = false
  }
  
  /**
   * 处理登录成功
   * @param {Object} userInfo 用户信息
   */
  function handleLoginSuccess(userInfo) {
    // 隐藏登录对话框
    hideLoginDialog()
    
    // 执行成功回调
    if (typeof loginSuccessCallback.value === 'function') {
      loginSuccessCallback.value(userInfo)
    }
  }
  
  /**
   * 处理登录取消
   */
  function handleLoginCancel() {
    // 隐藏登录对话框
    hideLoginDialog()
    
    // 执行取消回调
    if (typeof loginCancelCallback.value === 'function') {
      loginCancelCallback.value()
    }
  }
  
  /**
   * 检查用户是否已登录，如果未登录则显示登录对话框
   * @param {Object} options 配置选项
   * @param {Function} options.onSuccess 登录成功的回调函数
   * @param {Function} options.onCancel 取消登录的回调函数
   * @returns {boolean} 是否已登录
   */
  function checkLogin(options = {}) {
    // 检查用户是否已登录
    if (userStore.isAuthenticated) {
      // 如果已登录，则直接执行成功回调
      if (typeof options.onSuccess === 'function') {
        options.onSuccess(userStore.userInfo)
      }
      return true
    }
    
    // 如果未登录，则显示登录对话框
    showLoginDialog({
      tab: 'login',
      onSuccess: options.onSuccess,
      onCancel: options.onCancel
    })
    
    return false
  }
  
  /**
   * 登出
   */
  function logout() {
    userStore.clearUser()
  }
  
  return {
    loginDialogVisible,
    defaultLoginTab,
    showLoginDialog,
    hideLoginDialog,
    handleLoginSuccess,
    handleLoginCancel,
    checkLogin,
    logout
  }
})
