package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.NotificationDTO;
import com.tomato.lostfoundsystem.service.NotificationService;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;

@Slf4j
@RestController
@RequestMapping("/api/notifications")
public class NotificationController {
        @Autowired
        private NotificationService notificationService;

        @Autowired
        private SecurityUtil securityUtil;
    /**
     * 管理员发送通知
     * @param notificationDTO 通知数据
     * @return 发送结果
     */
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")  // 仅管理员或超级管理员可以发送通知
    @PostMapping("/send")
    public Result<String> sendNotification(@RequestBody NotificationDTO notificationDTO) {
        try {
            // 检查通知标题和内容是否为空
            if (notificationDTO.getTitle() == null || notificationDTO.getMessage() == null) {
                return Result.fail("通知标题和内容不能为空");
            }

            // 根据 role 字段选择发送通知给哪个用户类型
            switch (notificationDTO.getRole()) {
                case "SPECIFIC":
                    // 发送给特定用户
                    if (notificationDTO.getUserId() == null) {
                        return Result.fail("特定用户ID不能为空");
                    }
                    notificationService.sendNotificationToUser(notificationDTO.getUserId(), notificationDTO.getTitle(), notificationDTO.getMessage());
                    break;

                case "NORMAL":
                    // 发送给所有普通用户
                    notificationService.sendNotificationToUsersByRole(notificationDTO);
                    break;

                case "ADMIN":
                    // 发送给所有管理员（包括普通管理员和超级管理员）
                    notificationService.sendNotificationToAdmins(notificationDTO);
                    break;

                case "ALL":
                    // 发送给所有用户
                    notificationService.sendNotificationToAllUsers(notificationDTO);
                    break;

                default:
                    return Result.fail("无效的用户类型");
            }
            return Result.success("通知发送成功");
        } catch (Exception e) {
            return Result.fail("通知发送失败：" + e.getMessage());
        }
    }
    // 获取用户的通知列表
    @GetMapping
    public Result<List<NotificationDTO>> getNotifications(
            @RequestParam(required = false) String status,
            @RequestParam(required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        try {
            // 从安全上下文中获取用户ID
            Long userId = securityUtil.getCurrentUserId();
            if (userId == null) {
                return Result.fail("用户未登录或登录已过期");
            }

            log.info("通知状态：{}, 页码：{}, 每页大小：{}", status, pageNum, pageSize);

            // 获取 DTO 数据
            List<NotificationDTO> notifications = notificationService.getNotifications(userId, status);

            // 手动实现分页
            int total = notifications.size();
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            // 检查起始索引是否有效
            if (startIndex >= total) {
                // 如果起始索引超出范围，返回空列表
                return Result.success(new ArrayList<>());
            }

            // 提取当前页的数据
            List<NotificationDTO> pagedNotifications = notifications.subList(startIndex, endIndex);

            log.info("总通知数：{}, 当前页通知数：{}", total, pagedNotifications.size());
            return Result.success(pagedNotifications);
        } catch (Exception e) {
            log.error("获取通知失败", e);
            return Result.fail("获取通知失败：" + e.getMessage());
        }
    }

    //获取未读消息通知数量
    @GetMapping("/unread/count")
    public Result<Integer> getUnreadCount() {
        try {
            // 从安全上下文中获取用户ID
            Long userId = securityUtil.getCurrentUserId();
            if (userId == null) {
                return Result.fail("用户未登录或登录已过期");
            }

            // 调用服务层方法获取未读通知数量
            Integer count = notificationService.getUnreadCount(userId);

            // 返回未读通知数量
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取未读通知数量失败", e);
            return Result.fail("获取未读通知数量失败: " + e.getMessage());
        }
    }

    // 标记通知为已读
    @PostMapping("/mark-as-read")
    public Result<String> markNotificationAsRead(@RequestParam Long notificationId) {
        try {
            notificationService.markNotificationAsRead(notificationId);
            return Result.success("通知已标记为已读", null);
        } catch (Exception e) {
            return Result.fail("标记通知为已读失败：" + e.getMessage());
        }
    }

    // 删除通知
    @DeleteMapping("/delete")
    public Result<String> deleteNotification(@RequestParam Long notificationId) {
        try {
            notificationService.deleteNotification(notificationId);
            return Result.success("通知已删除", null);
        } catch (Exception e) {
            return Result.fail("删除通知失败：" + e.getMessage());
        }
    }

}