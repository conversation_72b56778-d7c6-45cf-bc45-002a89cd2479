<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomato.lostfoundsystem.mapper.ItemImageMapper">

    <!-- 结果映射 -->
    <resultMap id="ItemImageMap" type="com.tomato.lostfoundsystem.entity.ItemImage">
        <id property="id" column="id"/>
        <result property="itemId" column="item_id"/>
        <result property="itemType" column="item_type"/>
        <result property="imageUrl" column="image_url"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createdAt" column="created_at"/>
    </resultMap>

    <!-- 插入物品图片 -->
    <insert id="insertItemImage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO item_images (
            item_id,
            item_type,
            image_url,
            sort_order,
            created_at
        ) VALUES (
            #{itemId},
            #{itemType},
            #{imageUrl},
            #{sortOrder},
            NOW()
        )
    </insert>

    <!-- 批量插入物品图片 -->
    <insert id="batchInsertItemImages" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO item_images (
            item_id,
            item_type,
            image_url,
            sort_order,
            created_at
        ) VALUES
        <foreach collection="itemImages" item="image" separator=",">
            (
                #{image.itemId},
                #{image.itemType},
                #{image.imageUrl},
                #{image.sortOrder},
                NOW()
            )
        </foreach>
    </insert>

    <!-- 根据物品ID和类型获取图片列表 -->
    <select id="getImagesByItemId" resultMap="ItemImageMap">
        SELECT
            id,
            item_id,
            item_type,
            image_url,
            sort_order,
            created_at
        FROM
            item_images
        WHERE
            item_id = #{itemId}
            AND item_type = #{itemType}
        ORDER BY
            sort_order ASC
    </select>

    <!-- 根据物品ID和类型删除图片 -->
    <delete id="deleteImagesByItemId">
        DELETE FROM
            item_images
        WHERE
            item_id = #{itemId}
            AND item_type = #{itemType}
    </delete>

    <!-- 根据ID删除图片 -->
    <delete id="deleteImageById">
        DELETE FROM
            item_images
        WHERE
            id = #{id}
    </delete>

    <!-- 根据ID列表批量删除图片 -->
    <delete id="deleteImagesByIds">
        DELETE FROM
            item_images
        WHERE
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
    </delete>

    <!-- 更新图片排序 -->
    <update id="updateImageSortOrder">
        UPDATE
            item_images
        SET
            sort_order = #{sortOrder}
        WHERE
            id = #{id}
    </update>

    <!-- 根据ID获取图片 -->
    <select id="getImageById" resultMap="ItemImageMap">
        SELECT
            id,
            item_id,
            item_type,
            image_url,
            sort_order,
            created_at
        FROM
            item_images
        WHERE
            id = #{id}
    </select>
</mapper>
