/**
 * 离线消息处理器
 *
 * 用于管理离线消息的存储和发送
 */

import IndexedDBManager from './IndexedDB'
import { sendBatchMessages } from '@/api/chat'
import { v4 as uuidv4 } from 'uuid'

export default class OfflineMessageHandler {
  constructor(options = {}) {
    this.dbManager = options.dbManager || new IndexedDBManager('chat_db', 1)
    this.sendFunction = options.sendFunction
    this.onSuccess = options.onSuccess || (() => {})
    this.onError = options.onError || (() => {})
    this.onComplete = options.onComplete || (() => {})
    this.isOnline = navigator.onLine
    this.processingQueue = false
    this.deviceId = this.getDeviceId()

    // 监听网络状态变化
    window.addEventListener('online', this.handleNetworkChange.bind(this))
    window.addEventListener('offline', this.handleNetworkChange.bind(this))
  }

  // 获取或生成设备ID
  getDeviceId() {
    let deviceId = localStorage.getItem('device_id')

    if (!deviceId) {
      deviceId = uuidv4()
      localStorage.setItem('device_id', deviceId)
    }

    return deviceId
  }

  // 处理网络状态变化
  handleNetworkChange() {
    const wasOffline = !this.isOnline
    this.isOnline = navigator.onLine

    if (wasOffline && this.isOnline) {
      console.log('网络恢复，处理离线消息')
      this.processOfflineMessages()
    }
  }

  // 添加离线消息
  async addOfflineMessage(message) {
    try {
      // 标记为离线消息
      message.status = 'OFFLINE'

      // 保存到数据库
      await this.dbManager.saveMessage(message)

      console.log('已添加离线消息:', message.id)

      // 如果已经在线，立即处理
      if (this.isOnline) {
        this.processOfflineMessages()
      }

      return message
    } catch (error) {
      console.error('添加离线消息失败:', error)
      throw error
    }
  }

  // 处理离线消息
  async processOfflineMessages() {
    if (!this.isOnline || this.processingQueue || !this.sendFunction) {
      return
    }

    this.processingQueue = true

    try {
      // 获取所有离线消息
      const offlineMessages = await this.getOfflineMessages()

      if (offlineMessages.length === 0) {
        console.log('没有离线消息需要处理')
        this.processingQueue = false
        return
      }

      console.log(`开始处理 ${offlineMessages.length} 条离线消息`)

      // 尝试使用批量API发送
      if (offlineMessages.length > 0) {
        try {
          // 准备批量发送数据
          const batchData = {
            messages: offlineMessages,
            deviceId: this.deviceId,
            batchId: uuidv4()
          }

          // 调用API批量发送
          const response = await sendBatchMessages(batchData)

          if (response && response.code === 200) {
            console.log('批量发送离线消息成功:', response)

            // 更新消息状态
            for (let i = 0; i < offlineMessages.length; i++) {
              const message = offlineMessages[i]
              const result = response.data[i]

              if (result) {
                // 更新消息状态为已发送
                await this.dbManager.updateMessageStatus(message.id, 'SENT', {
                  id: result.id || message.id,
                  timestamp: result.timestamp || message.timestamp
                })

                // 触发消息发送成功事件，用于更新UI
                window.dispatchEvent(new CustomEvent('message-sent', {
                  detail: {
                    message: {
                      ...message,
                      id: result.id || message.id,
                      timestamp: result.timestamp || message.timestamp
                    },
                    status: 'SENT'
                  }
                }));

                // 调用成功回调
                this.onSuccess(message, { data: result })
              }
            }

            // 调用完成回调
            this.onComplete()
            this.processingQueue = false
            return
          } else {
            console.error('批量发送离线消息失败:', response)
            throw new Error(response?.message || '批量发送失败')
          }
        } catch (error) {
          console.error('批量发送离线消息出错，尝试单条发送:', error)
        }
      }

      // 如果批量发送失败，按会话分组单独处理
      const messagesByConversation = this.groupMessagesByConversation(offlineMessages)

      // 处理每个会话的消息
      for (const [conversationId, messages] of Object.entries(messagesByConversation)) {
        try {
          // 更新消息状态为发送中
          for (const message of messages) {
            await this.dbManager.updateMessageStatus(message.id, 'SENDING')
          }

          // 批量发送消息
          const results = await this.sendFunction(messages)

          // 处理结果
          if (Array.isArray(results)) {
            for (let i = 0; i < messages.length; i++) {
              const message = messages[i]
              const result = results[i]

              if (result && (result.success || result.code === 200)) {
                // 更新消息状态为已发送
                await this.dbManager.updateMessageStatus(message.id, 'SENT', {
                  id: result.data?.id || message.id,
                  time: result.data?.timestamp || new Date().toISOString(),
                  fileUrl: result.data?.fileUrl || null
                })

                this.onSuccess(message, result)
              } else {
                // 更新消息状态为发送失败
                await this.dbManager.updateMessageStatus(message.id, 'ERROR', {
                  errorMessage: result?.message || '发送失败'
                })

                this.onError(message, new Error(result?.message || '发送失败'))
              }
            }
          }
        } catch (error) {
          console.error(`处理会话 ${conversationId} 的离线消息失败:`, error)

          // 更新所有消息状态为发送失败
          for (const message of messages) {
            await this.dbManager.updateMessageStatus(message.id, 'ERROR', {
              errorMessage: error.message || '发送失败'
            })

            this.onError(message, error)
          }
        }
      }

      console.log('离线消息处理完成')
      this.onComplete()
    } catch (error) {
      console.error('处理离线消息失败:', error)
    } finally {
      this.processingQueue = false
    }
  }

  // 获取所有离线消息
  async getOfflineMessages() {
    try {
      const messages = await this.dbManager.getOfflineMessages()

      // 过滤掉标记为 _isPostRequest 或 _isWebSocketSending 或 _isFileUpload 的消息
      // 这些消息可能是通过 POST 请求或 WebSocket 发送的，不应该被当作离线消息处理
      const filteredMessages = messages.filter(message => {
        if (message._isPostRequest || message._isWebSocketSending || message._isFileUpload) {
          console.log('跳过已通过其他方式发送的消息:', message.id, {
            _isPostRequest: message._isPostRequest,
            _isWebSocketSending: message._isWebSocketSending,
            _isFileUpload: message._isFileUpload
          })
          return false
        }
        return true
      })

      console.log(`过滤前离线消息数量: ${messages.length}, 过滤后: ${filteredMessages.length}`)
      return filteredMessages
    } catch (error) {
      console.error('获取离线消息失败:', error)
      return []
    }
  }

  // 按会话分组消息
  groupMessagesByConversation(messages) {
    const groups = {}

    messages.forEach(message => {
      const conversationId = message.conversationId || 'unknown'

      if (!groups[conversationId]) {
        groups[conversationId] = []
      }

      groups[conversationId].push(message)
    })

    return groups
  }

  // 清理资源
  destroy() {
    window.removeEventListener('online', this.handleNetworkChange.bind(this))
    window.removeEventListener('offline', this.handleNetworkChange.bind(this))
  }
}
