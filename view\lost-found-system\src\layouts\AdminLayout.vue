<template>
  <div class="admin-layout">
    <el-container>
      <el-aside width="200px">
        <div class="logo">
          后台管理系统
        </div>
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          :router="true"
        >
          <el-menu-item index="/admin/dashboard">
            <el-icon><Odometer /></el-icon>
            <span>控制台</span>
          </el-menu-item>

          <el-menu-item index="/admin/lost-items">
            <el-icon><Files /></el-icon>
            <span>失物管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/found-items">
            <el-icon><Folder /></el-icon>
            <span>拾物管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/users">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/notifications">
            <el-icon><Bell /></el-icon>
            <span>系统通知管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/announcements">
            <el-icon><Notification /></el-icon>
            <span>系统公告管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/services">
            <el-icon><Monitor /></el-icon>
            <span>服务管理</span>
          </el-menu-item>

          <el-menu-item index="/admin/config">
            <el-icon><Setting /></el-icon>
            <span>系统配置</span>
          </el-menu-item>

          <el-sub-menu index="statistics">
            <template #title>
              <el-icon><DataLine /></el-icon>
              <span>统计分析</span>
            </template>
            <el-menu-item index="/admin/statistics">
              <el-icon><PieChart /></el-icon>
              <span>基础统计</span>
            </el-menu-item>
            <el-menu-item index="/admin/match-statistics">
              <el-icon><Connection /></el-icon>
              <span>匹配统计</span>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>

      <el-container>
        <el-header>
          <div class="header-content">
            <el-breadcrumb>
              <el-breadcrumb-item :to="{ path: '/admin/dashboard' }">
                后台管理
              </el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentRoute.meta.title }}</el-breadcrumb-item>
            </el-breadcrumb>

            <div class="header-right">
              <el-button type="primary" link @click="router.push('/')">
                返回前台
              </el-button>
            </div>
          </div>
        </el-header>

        <el-main>
          <router-view></router-view>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  Odometer,
  Files,
  Folder,
  User,
  Bell,
  Notification,
  Monitor,
  Setting,
  DataLine,
  PieChart,
  Connection
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const activeMenu = computed(() => route.path)
const currentRoute = computed(() => route)
</script>

<style scoped>
.admin-layout {
  min-height: 100vh;
}

.logo {
  height: 60px;
  line-height: 60px;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
  border-bottom: 1px solid #e6e6e6;
}

.el-aside {
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
}

.el-menu-vertical {
  border-right: none;
}

.el-header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0 20px;
}

.header-content {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-right {
  display: flex;
  align-items: center;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
}
</style>