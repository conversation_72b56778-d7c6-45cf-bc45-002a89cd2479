package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.entity.MessageReadStatus;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.data.repository.query.Param;

import java.util.List;

@Mapper
public interface MessageReadStatusMapper {

    //插入消息
    void insert(ChatMessage chatMessage);

    // 插入已读状态
    void insert(MessageReadStatus messageReadStatus);

    // 更新已读状态
    int updateReadStatus(Long messageId, Long userId, Boolean isRead);

    // 获取未读消息数量
    int countUnreadMessages(@Param("userId") Long userId, @Param("contactId") Long contactId);

    // 获取消息的已读状态
    Integer getMessageReadStatus(@Param("messageId") Long messageId, @Param("userId") Long userId);

    // 获取用户与联系人之间的未读消息ID列表
    List<Long> getUnreadMessageIds(@Param("userId") Long userId, @Param("contactId") Long contactId);

    // 批量更新消息已读状态
    void batchUpdateReadStatus(@Param("messageIds") List<Long> messageIds, @Param("userId") Long userId, @Param("isRead") Boolean isRead);
}
