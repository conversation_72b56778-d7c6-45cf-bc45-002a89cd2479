package com.tomato.lostfoundsystem.service.Impl;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.cdn.model.v20141111.RefreshObjectCachesRequest;
import com.aliyuncs.cdn.model.v20141111.RefreshObjectCachesResponse;
import com.aliyuncs.cdn.model.v20141111.PushObjectCacheRequest;
import com.aliyuncs.cdn.model.v20141111.PushObjectCacheResponse;
import com.aliyuncs.cdn.model.v20141111.DescribeRefreshQuotaRequest;
import com.aliyuncs.cdn.model.v20141111.DescribeRefreshQuotaResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.tomato.lostfoundsystem.service.CDNCacheService;
import com.tomato.lostfoundsystem.service.CDNConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * CDN缓存服务实现类
 */
@Slf4j
@Service
public class CDNCacheServiceImpl implements CDNCacheService {

    @Value("${aliyun.oss.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.oss.access-key-secret}")
    private String accessKeySecret;

    @Resource
    private CDNConfigService cdnConfigService;

    /**
     * 获取阿里云CDN客户端
     */
    private IAcsClient getClient() {
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
        return new DefaultAcsClient(profile);
    }

    @Override
    public boolean refreshUrl(String url) {
        try {
            IAcsClient client = getClient();
            RefreshObjectCachesRequest request = new RefreshObjectCachesRequest();
            request.setObjectPath(url);
            request.setObjectType("File");

            RefreshObjectCachesResponse response = client.getAcsResponse(request);
            log.info("刷新CDN缓存成功: {}, 请求ID: {}", url, response.getRequestId());
            return true;
        } catch (ClientException e) {
            log.error("刷新CDN缓存失败: {}, 错误: {}", url, e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("刷新CDN缓存失败: {}, 错误: {}", url, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int refreshUrls(List<String> urls) {
        if (urls == null || urls.isEmpty()) {
            return 0;
        }

        try {
            IAcsClient client = getClient();
            RefreshObjectCachesRequest request = new RefreshObjectCachesRequest();

            // 将URL列表转换为以换行符分隔的字符串
            String objectPaths = String.join("\\n", urls);
            request.setObjectPath(objectPaths);
            request.setObjectType("File");

            RefreshObjectCachesResponse response = client.getAcsResponse(request);
            log.info("批量刷新CDN缓存成功, 请求ID: {}, URL数量: {}", response.getRequestId(), urls.size());
            return urls.size();
        } catch (ClientException e) {
            log.error("批量刷新CDN缓存失败, 错误: {}", e.getMessage(), e);

            // 如果批量刷新失败，尝试逐个刷新
            int successCount = 0;
            for (String url : urls) {
                if (refreshUrl(url)) {
                    successCount++;
                }
            }
            return successCount;
        } catch (Exception e) {
            log.error("批量刷新CDN缓存失败, 错误: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public boolean refreshDirectory(String directory) {
        try {
            IAcsClient client = getClient();
            RefreshObjectCachesRequest request = new RefreshObjectCachesRequest();
            request.setObjectPath(directory);
            request.setObjectType("Directory");

            RefreshObjectCachesResponse response = client.getAcsResponse(request);
            log.info("刷新CDN目录缓存成功: {}, 请求ID: {}", directory, response.getRequestId());
            return true;
        } catch (ClientException e) {
            log.error("刷新CDN目录缓存失败: {}, 错误: {}", directory, e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("刷新CDN目录缓存失败: {}, 错误: {}", directory, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean preloadUrl(String url) {
        try {
            IAcsClient client = getClient();
            PushObjectCacheRequest request = new PushObjectCacheRequest();
            request.setObjectPath(url);

            PushObjectCacheResponse response = client.getAcsResponse(request);
            log.info("预热CDN缓存成功: {}, 请求ID: {}", url, response.getRequestId());
            return true;
        } catch (ClientException e) {
            log.error("预热CDN缓存失败: {}, 错误: {}", url, e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("预热CDN缓存失败: {}, 错误: {}", url, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int preloadUrls(List<String> urls) {
        if (urls == null || urls.isEmpty()) {
            return 0;
        }

        try {
            IAcsClient client = getClient();
            PushObjectCacheRequest request = new PushObjectCacheRequest();

            // 将URL列表转换为以换行符分隔的字符串
            String objectPaths = String.join("\\n", urls);
            request.setObjectPath(objectPaths);

            PushObjectCacheResponse response = client.getAcsResponse(request);
            log.info("批量预热CDN缓存成功, 请求ID: {}, URL数量: {}", response.getRequestId(), urls.size());
            return urls.size();
        } catch (ClientException e) {
            log.error("批量预热CDN缓存失败, 错误: {}", e.getMessage(), e);

            // 如果批量预热失败，尝试逐个预热
            int successCount = 0;
            for (String url : urls) {
                if (preloadUrl(url)) {
                    successCount++;
                }
            }
            return successCount;
        } catch (Exception e) {
            log.error("批量预热CDN缓存失败, 错误: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public CDNQuotaInfo getQuotaInfo() {
        try {
            IAcsClient client = getClient();
            DescribeRefreshQuotaRequest request = new DescribeRefreshQuotaRequest();

            DescribeRefreshQuotaResponse response = client.getAcsResponse(request);

            // 将String类型转换为int类型
            return new CDNQuotaInfo(
                Integer.parseInt(response.getUrlRemain()),
                Integer.parseInt(response.getDirRemain()),
                Integer.parseInt(response.getPreloadRemain()),
                Integer.parseInt(response.getUrlQuota()),
                Integer.parseInt(response.getDirQuota()),
                Integer.parseInt(response.getPreloadQuota())
            );
        } catch (ClientException e) {
            log.error("获取CDN配额信息失败, 错误: {}", e.getMessage(), e);
            // 返回默认配额信息
            return new CDNQuotaInfo(100, 100, 100, 2000, 500, 500);
        } catch (NumberFormatException e) {
            log.error("解析CDN配额信息失败, 错误: {}", e.getMessage(), e);
            // 返回默认配额信息
            return new CDNQuotaInfo(100, 100, 100, 2000, 500, 500);
        } catch (Exception e) {
            log.error("获取CDN配额信息失败, 错误: {}", e.getMessage(), e);
            // 返回默认配额信息
            return new CDNQuotaInfo(0, 0, 0, 0, 0, 0);
        }
    }
}
