@startuml 物品状态流转综合图

skinparam StateBackgroundColor LightYellow
skinparam StateBorderColor Black
skinparam StateStartColor Green
skinparam StateEndColor Red
skinparam ArrowColor Black

title 失物招领系统物品状态流转图

[*] --> 用户发布物品

state 用户发布物品 {
  [*] --> 失物发布 : 用户发布失物
  [*] --> 拾物发布 : 用户发布拾物
  
  失物发布 --> 失物待审核 : 提交
  拾物发布 --> 拾物待审核 : 提交
}

state 审核流程 {
  state 失物待审核 as "失物待审核\n(PENDING)" #LightBlue
  state 失物已通过 as "失物已通过\n(APPROVED)" #LightGreen
  state 失物已拒绝 as "失物已拒绝\n(REJECTED)" #Pink
  
  state 拾物待审核 as "拾物待审核\n(PENDING)" #LightBlue
  state 拾物已通过 as "拾物已通过\n(APPROVED)" #LightGreen
  state 拾物已拒绝 as "拾物已拒绝\n(REJECTED)" #Pink
  
  失物待审核 --> 失物已通过 : 管理员审核通过
  失物待审核 --> 失物已拒绝 : 管理员审核拒绝
  
  拾物待审核 --> 拾物已通过 : 管理员审核通过
  拾物待审核 --> 拾物已拒绝 : 管理员审核拒绝
  
  失物已拒绝 --> 失物待审核 : 修改后重新提交
  拾物已拒绝 --> 拾物待审核 : 修改后重新提交
}

state 物品状态 {
  state 未找回 as "未找回\n(LOST)" #Orange
  state 已找回 as "已找回\n(FOUND)" #Green
  
  state 未认领 as "未认领\n(UNCLAIMED)" #Orange
  state 已归还 as "已归还\n(RETURNED)" #Green
  
  失物已通过 --> 未找回 : 自动转换
  拾物已通过 --> 未认领 : 自动转换
  
  未找回 --> 已找回 : 用户标记为已找回\n或管理员更新状态
  未认领 --> 已归还 : 用户认领\n或管理员更新状态
}

state 智能匹配 {
  state 匹配触发 as "触发匹配" #LightCyan
  state 匹配结果 as "匹配结果" #LightCyan
  
  失物已通过 --> 匹配触发 : 自动触发
  拾物已通过 --> 匹配触发 : 自动触发
  
  匹配触发 --> 匹配结果 : 执行匹配算法
  匹配结果 --> 未找回 : 通知失主
  匹配结果 --> 未认领 : 通知拾主
}

已找回 --> [*] : 流程结束
已归还 --> [*] : 流程结束

@enduml
