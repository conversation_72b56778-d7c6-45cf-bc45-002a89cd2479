import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import {
  getNotifications,
  markNotificationAsRead,
  getUnreadCount,
  deleteNotification,
  getMatchNotifications,
  markMatchNotificationAsRead,
  getUnreadMatchNotificationCount
} from '@/api/notification';
import {
  getValidAnnouncements,
  markAnnouncementAsRead,
  getUnreadAnnouncementCount
} from '@/api/announcement';

export const useNotificationStore = defineStore('notifications', {
  state: () => ({
    // 通知数据
    systemNotifications: [],
    matchNotifications: [],
    announcements: [],

    // 未读数量
    unreadSystemCount: 0,
    unreadMatchCount: 0,
    unreadAnnouncementCount: 0,

    // 数据加载状态
    loading: false,

    // 用于防止重复请求
    lastFetchTime: {
      notifications: 0,
      unreadCount: 0
    },

    // 请求状态
    fetchInProgress: {
      notifications: false,
      unreadCount: false
    },

    // 请求间隔限制 (毫秒)
    minFetchInterval: 5000,
  }),

  getters: {
    // 总未读数量
    totalUnreadCount: (state) => {
      return state.unreadSystemCount + state.unreadMatchCount + state.unreadAnnouncementCount;
    },

    // 是否有未读通知
    hasUnreadNotifications: (state) => {
      return state.totalUnreadCount > 0;
    },

    // 获取所有通知（合并后按时间排序）
    allNotifications: (state) => {
      const all = [
        ...state.systemNotifications.map(n => ({ ...n, type: 'system' })),
        ...state.matchNotifications.map(n => ({ ...n, type: 'match' })),
        ...state.announcements.map(a => ({ ...a, type: 'announcement' }))
      ];

      // 按时间排序，最新的在前面
      return all.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    }
  },

  actions: {
    // 获取所有通知数据
    async fetchAllNotifications(forceRefresh = false) {
      const now = Date.now();

      // 防抖：如果距离上次请求不足指定时间且不是强制刷新，则跳过
      if (
        !forceRefresh && (
          this.fetchInProgress.notifications ||
          (now - this.lastFetchTime.notifications < this.minFetchInterval)
        )
      ) {
        console.log('【通知优化】距离上次获取通知不足5秒，跳过请求');
        return;
      }

      this.fetchInProgress.notifications = true;
      this.loading = true;

      try {
        console.log('【通知】开始获取所有通知数据');

        // 并行请求三种通知数据
        const [sysRes, matchRes, announcementRes] = await Promise.all([
          getNotifications(),
          getMatchNotifications(),
          getValidAnnouncements()
        ]);

        // 更新状态
        if (sysRes && sysRes.code === 200) {
          // 处理系统通知的已读状态
          this.systemNotifications = (sysRes.data || []).map(notification => {
            console.log(`【通知调试】系统通知ID: ${notification.id}, 标题: ${notification.title}, 后端返回的状态:`,
              notification.status,
              `(类型: ${typeof notification.status}, 值: ${JSON.stringify(notification.status)})`
            );

            // 确保isRead属性存在且为布尔值
            let isRead = false;
            if (notification.status === 'READ' ||
                notification.isRead === true ||
                notification.isRead === 1 ||
                notification.isRead === "1" ||
                notification.isRead === "true") {
              isRead = true;
            }

            console.log(`【通知调试】系统通知ID: ${notification.id}, 最终已读状态: ${isRead} (类型: ${typeof isRead})`);

            return {
              ...notification,
              isRead: isRead,  // 添加或覆盖isRead属性
              status: isRead ? 'READ' : 'UNREAD'  // 确保status属性一致
            };
          });

          console.log('【通知调试】系统通知列表更新完成，总数:', this.systemNotifications.length,
                      '已读:', this.systemNotifications.filter(n => n.isRead).length,
                      '未读:', this.systemNotifications.filter(n => !n.isRead).length);
        }

        if (matchRes && matchRes.code === 200) {
          // 处理匹配通知的已读状态
          this.matchNotifications = (matchRes.data || []).map(notification => {
            console.log(`【通知调试】匹配通知ID: ${notification.id}, 标题: ${notification.title}, 后端返回的已读状态:`,
              notification.isRead,
              `(类型: ${typeof notification.isRead}, 值: ${JSON.stringify(notification.isRead)})`
            );

            // 确保isRead属性为布尔值
            let isRead = false;
            if (notification.isRead === true ||
                notification.isRead === 1 ||
                notification.isRead === "1" ||
                notification.isRead === "true") {
              isRead = true;
            }

            console.log(`【通知调试】匹配通知ID: ${notification.id}, 最终已读状态: ${isRead} (类型: ${typeof isRead})`);

            return {
              ...notification,
              isRead: isRead,  // 确保isRead属性是真正的布尔值
              status: isRead ? 'READ' : 'UNREAD'  // 添加统一的status属性
            };
          });

          console.log('【通知调试】匹配通知列表更新完成，总数:', this.matchNotifications.length,
                      '已读:', this.matchNotifications.filter(n => n.isRead).length,
                      '未读:', this.matchNotifications.filter(n => !n.isRead).length);
        }

        if (announcementRes && announcementRes.code === 200) {
          // 处理新获取的公告数据
          // 打印原始数据类型
          console.log('【通知调试】原始公告数据:', announcementRes.data);

          this.announcements = (announcementRes.data || []).map(announcement => {
            // 打印详细调试信息，包括类型信息
            console.log(`【通知调试】公告ID: ${announcement.id}, 标题: ${announcement.title}, 后端返回的已读状态:`,
              announcement.isRead,
              `(类型: ${typeof announcement.isRead}, 值: ${JSON.stringify(announcement.isRead)})`
            );

            // 严格处理已读状态
            // 后端返回的isRead是整数类型(1/0)
            let isRead = false;
            if (announcement.isRead === 1) {
              isRead = true;
            }

            // 更新后的状态
            console.log(`【通知调试】公告ID: ${announcement.id}, 最终已读状态: ${isRead} (类型: ${typeof isRead})`);

            return {
              ...announcement,
              isRead: isRead,         // 确保isRead属性是真正的布尔值
              status: isRead ? 'READ' : 'UNREAD'  // 统一status属性
            };
          });

          console.log('【通知调试】公告列表更新完成，总数:', this.announcements.length,
                      '已读:', this.announcements.filter(a => a.isRead).length,
                      '未读:', this.announcements.filter(a => !a.isRead).length);
        }

        // 记录请求时间
        this.lastFetchTime.notifications = Date.now();

        console.log('【通知】获取所有通知数据完成');
      } catch (error) {
        console.error('【通知错误】获取通知列表失败:', error);
      } finally {
        this.fetchInProgress.notifications = false;
        this.loading = false;
      }
    },

    // 获取所有未读数量
    async fetchAllUnreadCounts(forceRefresh = false) {
      const now = Date.now();

      // 防抖：如果距离上次请求不足指定时间且不是强制刷新，则跳过
      if (
        !forceRefresh && (
          this.fetchInProgress.unreadCount ||
          (now - this.lastFetchTime.unreadCount < this.minFetchInterval)
        )
      ) {
        console.log('【通知优化】距离上次获取未读数量不足5秒，跳过请求');
        return;
      }

      this.fetchInProgress.unreadCount = true;

      try {
        console.log('【通知】开始获取所有未读数量');

        // 并行请求三种未读数量
        const [sysCount, matchCount, announcementCount] = await Promise.all([
          getUnreadCount(),
          getUnreadMatchNotificationCount(),
          getUnreadAnnouncementCount()
        ]);

        // 更新状态
        if (sysCount && sysCount.code === 200) {
          this.unreadSystemCount = sysCount.data || 0;
          console.log('【通知调试】未读系统通知数量:', this.unreadSystemCount);
        }

        if (matchCount && matchCount.code === 200) {
          this.unreadMatchCount = matchCount.data || 0;
          console.log('【通知调试】未读匹配通知数量:', this.unreadMatchCount);
        }

        if (announcementCount && announcementCount.code === 200) {
          this.unreadAnnouncementCount = announcementCount.data || 0;
          console.log('【通知调试】未读公告数量:', this.unreadAnnouncementCount);
        }

        // 记录请求时间
        this.lastFetchTime.unreadCount = Date.now();

        console.log('【通知】获取所有未读数量完成，总未读:', this.totalUnreadCount);
      } catch (error) {
        console.error('【通知错误】获取未读数量失败:', error);
      } finally {
        this.fetchInProgress.unreadCount = false;
      }
    },

    // 标记系统通知为已读
    async markSystemAsRead(notificationId) {
      try {
        const res = await markNotificationAsRead(notificationId);
        if (res) {
          // 更新本地通知状态
          const notification = this.systemNotifications.find(n => n.id === notificationId);
          if (notification) {
            notification.isRead = true;
            notification.status = 'READ';
          }
          // 更新未读数量
          if (this.unreadSystemCount > 0) this.unreadSystemCount--;
        }
        return res;
      } catch (error) {
        console.error('【通知错误】标记系统通知已读失败:', error);
        throw error;
      }
    },

    // 标记匹配通知为已读
    async markMatchAsRead(notificationId) {
      try {
        const res = await markMatchNotificationAsRead(notificationId);
        if (res && res.code === 200) {
          // 更新本地通知状态
          const notification = this.matchNotifications.find(n => n.id === notificationId);
          if (notification) {
            notification.isRead = true;
            notification.status = 'READ';
          }
          // 更新未读数量
          if (this.unreadMatchCount > 0) this.unreadMatchCount--;
        }
        return res;
      } catch (error) {
        console.error('【通知错误】标记匹配通知已读失败:', error);
        throw error;
      }
    },

    // 手动减少未读公告计数
    decrementUnreadAnnouncementCount() {
      if (this.unreadAnnouncementCount > 0) {
        this.unreadAnnouncementCount--;
        console.log('【通知调试】手动更新未读公告数量为:', this.unreadAnnouncementCount);
      }
    },

    // 标记公告为已读
    async markAnnouncementRead(announcementId) {
      try {
        console.log('【通知调试】开始标记公告已读，ID:', announcementId);
        const res = await markAnnouncementAsRead(announcementId);
        console.log('【通知调试】标记公告已读响应:', res);

        if (res && res.code === 200) {
          // 更新本地公告状态
          const index = this.announcements.findIndex(a => a.id === announcementId);
          if (index !== -1) {
            const announcement = this.announcements[index];
            console.log('【通知调试】找到公告，标记前状态:',
                        '标题=', announcement.title,
                        'isRead=', announcement.isRead,
                        'status=', announcement.status);

            // 创建新对象以确保响应式更新
            const updatedAnnouncement = {
              ...announcement,
              isRead: true,  // 强制设置为布尔值true
              status: 'READ'
            };

            // 使用响应式API更新
            this.$patch(state => {
              state.announcements[index] = updatedAnnouncement;
            });

            // 为确保视图更新，还创建一个新数组
            this.announcements = [...this.announcements];

            console.log('【通知调试】更新后状态:',
                        'isRead=', this.announcements[index].isRead,
                        '(类型:', typeof this.announcements[index].isRead, ')',
                        'status=', this.announcements[index].status);
          } else {
            console.log('【通知调试】未找到对应公告，ID:', announcementId);
          }

          // 更新未读数量
          if (this.unreadAnnouncementCount > 0) {
            this.unreadAnnouncementCount--;
            console.log('【通知调试】更新未读数量为:', this.unreadAnnouncementCount);
          }

          // 只更新未读计数，不重新获取通知列表
          setTimeout(() => {
            this.fetchAllUnreadCounts(true);
          }, 500);
        } else {
          console.error('【通知错误】标记公告已读API返回错误:', res);
        }
        return res;
      } catch (error) {
        console.error('【通知错误】标记公告已读失败:', error);
        throw error;
      }
    },

    // 删除系统通知
    async deleteSystemNotification(notificationId) {
      try {
        const res = await deleteNotification(notificationId);
        if (res && res.code === 200) {
          // 更新本地通知状态
          const index = this.systemNotifications.findIndex(n => n.id === notificationId);
          if (index !== -1) {
            // 如果是未读通知，更新未读计数
            if (!this.systemNotifications[index].isRead ||
                this.systemNotifications[index].status === 'UNREAD') {
              if (this.unreadSystemCount > 0) this.unreadSystemCount--;
            }
            // 从数组中移除
            this.systemNotifications.splice(index, 1);
          }
        }
        return res;
      } catch (error) {
        console.error('【通知错误】删除系统通知失败:', error);
        throw error;
      }
    },

    // 删除匹配通知
    async deleteMatchNotification(notificationId) {
      try {
        // 使用相同的API删除通知
        const res = await deleteNotification(notificationId);
        if (res && res.code === 200) {
          // 更新本地通知状态
          const index = this.matchNotifications.findIndex(n => n.id === notificationId);
          if (index !== -1) {
            // 如果是未读通知，更新未读计数
            if (!this.matchNotifications[index].isRead ||
                this.matchNotifications[index].status === 'UNREAD') {
              if (this.unreadMatchCount > 0) this.unreadMatchCount--;
            }
            // 从数组中移除
            this.matchNotifications.splice(index, 1);
          }
        }
        return res;
      } catch (error) {
        console.error('【通知错误】删除匹配通知失败:', error);
        throw error;
      }
    },

    // 从列表中移除匹配通知（不调用API）
    removeMatchNotification(notificationId) {
      console.log('【通知】从列表中移除匹配通知:', notificationId);

      // 更新本地通知状态
      const index = this.matchNotifications.findIndex(n => n.id === notificationId);
      if (index !== -1) {
        // 如果是未读通知，更新未读计数
        if (!this.matchNotifications[index].isRead ||
            this.matchNotifications[index].status === 'UNREAD') {
          if (this.unreadMatchCount > 0) this.unreadMatchCount--;
        }
        // 从数组中移除
        this.matchNotifications.splice(index, 1);
        return true;
      }
      return false;
    },

    // 删除公告通知
    async deleteAnnouncementNotification(notificationId) {
      try {
        // 使用相同的API删除通知
        const res = await deleteNotification(notificationId);
        if (res && res.code === 200) {
          // 更新本地通知状态
          const index = this.announcements.findIndex(a => a.id === notificationId);
          if (index !== -1) {
            // 如果是未读通知，更新未读计数
            if (!this.announcements[index].isRead) {
              if (this.unreadAnnouncementCount > 0) this.unreadAnnouncementCount--;
            }
            // 从数组中移除
            this.announcements.splice(index, 1);
          }
        }
        return res;
      } catch (error) {
        console.error('【通知错误】删除公告通知失败:', error);
        throw error;
      }
    },

    // 处理接收到的新通知
    handleNewNotification(notification) {
      if (!notification) return;

      console.log('【通知】处理新通知:', notification);

      // 根据通知类型分类处理
      if (notification.type === 'match' || notification.similarity) {
        // 检查是否已存在
        const exists = this.matchNotifications.some(n => n.id === notification.id);
        if (!exists) {
          this.matchNotifications.unshift(notification);
          this.unreadMatchCount++;
        }
      } else if (notification.type === 'announcement' || notification.importance) {
        // 检查是否已存在
        const exists = this.announcements.some(a => a.id === notification.id);
        if (!exists) {
          this.announcements.unshift(notification);
          this.unreadAnnouncementCount++;
        }
      } else {
        // 默认为系统通知
        // 检查是否已存在
        const exists = this.systemNotifications.some(n => n.id === notification.id);
        if (!exists) {
          this.systemNotifications.unshift(notification);
          this.unreadSystemCount++;
        }
      }
    },

    // 批量标记系统通知为已读
    async markAllSystemNotificationsAsRead() {
      console.log('【通知】批量标记系统通知为已读');
      try {
        // 获取所有未读系统通知
        const unreadNotifications = this.systemNotifications.filter(n => !n.isRead || n.status === 'UNREAD');

        if (unreadNotifications.length === 0) {
          console.log('【通知】没有未读系统通知');
          return;
        }

        // 循环标记每个通知为已读
        const promises = unreadNotifications.map(notification =>
          this.markSystemAsRead(notification.id)
        );

        await Promise.all(promises);

        // 更新状态
        this.unreadSystemCount = 0;

        // 更新所有通知状态
        this.systemNotifications.forEach(n => {
          n.isRead = true;
          n.status = 'READ';
        });

        // 显示成功消息
        ElMessage.success({
          message: `已将 ${unreadNotifications.length} 条系统通知标记为已读`,
          duration: 2000
        });

        return true;
      } catch (error) {
        console.error('【通知错误】批量标记系统通知已读失败:', error);
        ElMessage.error('操作失败，请稍后重试');
        return false;
      }
    },

    // 批量标记匹配通知为已读
    async markAllMatchNotificationsAsRead() {
      console.log('【通知】批量标记匹配通知为已读');
      try {
        // 获取所有未读匹配通知
        const unreadNotifications = this.matchNotifications.filter(n => !n.isRead || n.status === 'UNREAD');

        if (unreadNotifications.length === 0) {
          console.log('【通知】没有未读匹配通知');
          return;
        }

        // 循环标记每个通知为已读
        const promises = unreadNotifications.map(notification =>
          this.markMatchAsRead(notification.id)
        );

        await Promise.all(promises);

        // 更新状态
        this.unreadMatchCount = 0;

        // 更新所有通知状态
        this.matchNotifications.forEach(n => {
          n.isRead = true;
          n.status = 'READ';
        });

        // 显示成功消息
        ElMessage.success({
          message: `已将 ${unreadNotifications.length} 条匹配通知标记为已读`,
          duration: 2000
        });

        return true;
      } catch (error) {
        console.error('【通知错误】批量标记匹配通知已读失败:', error);
        ElMessage.error('操作失败，请稍后重试');
        return false;
      }
    },

    // 批量标记公告为已读
    async markAllAnnouncementsAsRead() {
      console.log('【通知】批量标记公告为已读');
      try {
        // 获取所有未读公告
        const unreadAnnouncements = this.announcements.filter(a => !a.isRead);

        if (unreadAnnouncements.length === 0) {
          console.log('【通知】没有未读公告');
          return;
        }

        console.log(`【通知调试】开始批量标记 ${unreadAnnouncements.length} 条公告为已读`);

        // 循环标记每个公告为已读
        const promises = unreadAnnouncements.map(announcement =>
          this.markAnnouncementRead(announcement.id)
        );

        await Promise.all(promises);

        // 更新状态
        this.unreadAnnouncementCount = 0;

        // 更新所有公告状态 - 创建全新数组以确保响应式更新
        const updatedAnnouncements = this.announcements.map(a => {
          // 创建新对象
          return {
            ...a,
            isRead: true,
            status: 'READ'
          };
        });

        // 完全替换数组
        this.announcements = updatedAnnouncements;

        console.log('【通知调试】批量标记已读后的公告状态:',
                    '总数:', this.announcements.length,
                    '已读:', this.announcements.filter(a => a.isRead).length,
                    '未读:', this.announcements.filter(a => !a.isRead).length);

        // 显示成功消息
        ElMessage.success({
          message: `已将 ${unreadAnnouncements.length} 条公告标记为已读`,
          duration: 2000
        });

        return true;
      } catch (error) {
        console.error('【通知错误】批量标记公告已读失败:', error);
        ElMessage.error('操作失败，请稍后重试');
        return false;
      }
    },

    // 全部标记为已读
    async markAllAsRead() {
      console.log('【通知】标记所有通知为已读');
      try {
        // 并行执行所有批量标记操作
        await Promise.all([
          this.markAllSystemNotificationsAsRead(),
          this.markAllMatchNotificationsAsRead(),
          this.markAllAnnouncementsAsRead()
        ]);

        // 强制刷新数据
        setTimeout(() => {
          this.fetchAllNotifications(true);
          this.fetchAllUnreadCounts(true);
        }, 500);

        return true;
      } catch (error) {
        console.error('【通知错误】标记所有通知已读失败:', error);
        ElMessage.error('操作失败，请稍后重试');
        return false;
      }
    },

    // 批量标记选中的通知为已读
    async markSelectedNotificationsAsRead(selectedIds, type) {
      console.log(`【通知】批量标记选中的${type}通知为已读:`, selectedIds);

      if (!selectedIds || selectedIds.length === 0) {
        console.log('【通知】没有选中任何通知');
        return;
      }

      try {
        let markMethod;
        let notifications;

        // 根据类型选择不同的标记方法和通知列表
        if (type === 'system') {
          markMethod = this.markSystemAsRead;
          notifications = this.systemNotifications;
        } else if (type === 'match') {
          markMethod = this.markMatchAsRead;
          notifications = this.matchNotifications;
        } else if (type === 'announcement') {
          markMethod = this.markAnnouncementRead;
          notifications = this.announcements;
        } else {
          console.error('【通知错误】未知的通知类型:', type);
          return;
        }

        // 循环标记每个选中的通知为已读
        const promises = selectedIds.map(id => markMethod.call(this, id));

        await Promise.all(promises);

        // 更新选中通知的状态
        selectedIds.forEach(id => {
          const notification = notifications.find(n => n.id === id);
          if (notification) {
            notification.isRead = true;
            notification.status = 'READ';
          }
        });

        // 更新未读计数
        this.fetchAllUnreadCounts(true);

        // 显示成功消息
        ElMessage.success({
          message: `已将 ${selectedIds.length} 条通知标记为已读`,
          duration: 2000
        });

        return true;
      } catch (error) {
        console.error('【通知错误】批量标记选中通知已读失败:', error);
        ElMessage.error('操作失败，请稍后重试');
        return false;
      }
    },

    // 清空所有通知（保留公告）
    async clearAllNotifications() {
      console.log('【通知】清空所有通知（保留公告）');
      try {
        // 清空系统通知
        for (const notification of [...this.systemNotifications]) {
          await this.deleteSystemNotification(notification.id);
        }

        // 清空匹配通知
        for (const notification of [...this.matchNotifications]) {
          await this.deleteMatchNotification(notification.id);
        }

        // 保留公告通知，不进行删除操作
        // 公告只能由管理员删除

        // 重置状态（保留公告）
        this.systemNotifications = [];
        this.matchNotifications = [];
        // this.announcements 保持不变
        this.unreadSystemCount = 0;
        this.unreadMatchCount = 0;
        // this.unreadAnnouncementCount 保持不变

        // 显示成功消息
        ElMessage.success({
          message: '已清空通知（系统公告已保留）',
          duration: 2000
        });

        return true;
      } catch (error) {
        console.error('【通知错误】清空所有通知失败:', error);
        ElMessage.error('操作失败，请稍后重试');
        return false;
      }
    }
  }
});