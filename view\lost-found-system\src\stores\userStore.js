/**
 * 用户状态管理 Store
 * 集中管理用户登录状态、用户信息和认证相关功能
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getUserInfo } from '../api/user'
import { parseJwt } from '../utils/jwt'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const lastValidationTime = ref(0)  // 上次验证时间戳
  const loading = ref(false)
  const error = ref(null)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const isLoggedIn = computed(() => !!token.value)
  const userInfo = computed(() => user.value || {})

  // 是否需要重新验证token（超过2小时才重新验证）
  const shouldValidateToken = computed(() => {
    const now = Date.now()
    const validationInterval = 2 * 60 * 60 * 1000 // 2小时
    return now - lastValidationTime.value > validationInterval
  })

  // 方法
  /**
   * 设置用户信息
   * @param {Object} userData 用户数据
   */
  function setUser(userData) {
    user.value = userData
    // 更新验证时间
    lastValidationTime.value = Date.now()
    // 触发全局用户登录事件
    window.dispatchEvent(new CustomEvent('user-login', {
      detail: userData
    }))
    // 保存到本地存储
    localStorage.setItem('userInfo', JSON.stringify(userData))
  }

  /**
   * 设置token
   * @param {string} newToken JWT token
   */
  function setToken(newToken) {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  /**
   * 清除用户信息和token
   */
  function clearUser() {
    user.value = null
    token.value = ''
    lastValidationTime.value = 0
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }

  /**
   * 验证token并更新用户信息
   * @param {boolean} force 是否强制验证
   * @returns {Promise<boolean>} 验证结果
   */
  async function validateTokenAndUpdateUser(force = false) {
    // 如果没有token，直接返回false
    if (!token.value) {
      return false
    }

    // 如果不是强制验证且不需要重新验证，直接返回true
    if (!force && !shouldValidateToken.value) {
      return true
    }

    try {
      const res = await getUserInfo()
      if (res.success) {
        setUser(res.data)
        return true
      }
      throw new Error('获取用户信息失败')
    } catch (error) {
      console.error('Token验证失败:', error)
      clearUser()
      return false
    }
  }

  /**
   * 从本地存储恢复用户会话
   * @returns {Promise<Object|null>} 用户信息
   */
  async function restoreSession() {
    if (token.value) {
      // 如果已经有用户信息，直接返回
      if (user.value) {
        return user.value
      }

      // 尝试从本地存储恢复用户信息
      const storedUserInfo = localStorage.getItem('userInfo')
      if (storedUserInfo) {
        try {
          const parsedUserInfo = JSON.parse(storedUserInfo)
          setUser(parsedUserInfo)
          return parsedUserInfo
        } catch (e) {
          console.error('解析存储的用户信息失败:', e)
        }
      }

      // 如果本地没有用户信息，尝试从服务器获取
      try {
        const res = await getUserInfo()
        if (res.success) {
          setUser(res.data)
          return res.data
        }
      } catch (error) {
        console.error('从服务器获取用户信息失败:', error)
        clearUser()
      }
    }
    return null
  }

  /**
   * 处理登录成功
   * @param {string} tokenData JWT token
   * @returns {Object} 用户信息
   */
  function handleLoginSuccess(tokenData) {
    // 解析 JWT token
    const decodedToken = parseJwt(tokenData)

    if (!decodedToken || !decodedToken.userId || !decodedToken.username) {
      throw new Error('解析用户信息失败：token格式不正确')
    }

    // 确保角色信息正确处理
    let roles = []
    if (decodedToken.roles) {
      // 如果roles是字符串，转换为数组
      if (typeof decodedToken.roles === 'string') {
        roles = [decodedToken.roles]
      } else if (Array.isArray(decodedToken.roles)) {
        roles = decodedToken.roles
      }
    }

    // 构建用户信息对象
    const userInfo = {
      id: decodedToken.userId,
      userId: decodedToken.userId,
      username: decodedToken.username,
      roles: roles,
      avatar: decodedToken.avatar || '',
      email: decodedToken.email || '',
      phone: decodedToken.phone || ''
    }

    // 设置token和用户信息
    setToken(tokenData)
    setUser(userInfo)

    return userInfo
  }

  /**
   * 更新用户头像
   * @param {string} newAvatarUrl 新的头像URL
   */
  function updateAvatar(newAvatarUrl) {
    if (user.value) {
      // 创建一个新的用户对象，避免直接修改引用
      const updatedUser = { ...user.value }
      updatedUser.avatar = newAvatarUrl

      // 更新用户信息
      setUser(updatedUser)

      console.log('用户头像已更新:', newAvatarUrl)

      // 触发全局头像更新事件
      window.dispatchEvent(new CustomEvent('avatar-updated', {
        detail: { avatar: newAvatarUrl }
      }))
    }
  }

  return {
    // 状态
    user,
    token,
    loading,
    error,
    lastValidationTime,

    // 计算属性
    isAuthenticated,
    isLoggedIn,
    userInfo,
    shouldValidateToken,

    // 方法
    setUser,
    setToken,
    clearUser,
    validateTokenAndUpdateUser,
    restoreSession,
    handleLoginSuccess,
    updateAvatar
  }
})
