/**
 * Vue日志插件
 * 将日志工具集成到Vue应用中，使其可以通过this.$log访问
 */

import logger, { LogLevel } from '@/utils/logger'

export default {
  install(app, options = {}) {
    // 应用配置选项
    if (options) {
      logger.setConfig(options)
    }

    // 添加实例属性，使日志工具可以通过this.$log访问
    app.config.globalProperties.$log = logger

    // 添加全局mixin，为每个组件创建一个独立的日志记录器
    app.mixin({
      beforeCreate() {
        // 为每个组件创建一个独立的日志记录器，使用组件名作为上下文
        const componentName = this.$options.name || 'Anonymous'
        this.$log = logger.createLogger(componentName)
      },
      beforeUnmount() {
        // 清理日志上下文
        if (this.$log && this.$log !== logger) {
          this.$log.clearContext()
        }
      }
    })

    // 添加全局错误处理，记录未捕获的错误
    app.config.errorHandler = (err, vm, info) => {
      // 记录错误
      const componentName = vm?.$options?.name || 'Unknown'
      logger.pushContext('ErrorHandler').pushContext(componentName)
      logger.error('Uncaught error:', err)
      logger.error('Error info:', info)
      logger.popContext().popContext()

      // 在开发环境中，将错误传递给控制台
      if (process.env.NODE_ENV !== 'production') {
        console.error(err)
      }
    }

    // 将日志工具暴露为全局属性，使其可以在组件外部使用
    app.provide('logger', logger)
  }
}

// 导出日志级别，方便使用
export { LogLevel }
