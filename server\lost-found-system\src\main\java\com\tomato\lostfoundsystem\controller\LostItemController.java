package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.annotation.RequireToken;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.LostItemDTO;
import com.tomato.lostfoundsystem.dto.LostItemDetailsDTO;
import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.service.ItemImageService;
import com.tomato.lostfoundsystem.service.LostItemService;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 失物招领控制器
 * 处理失物招领相关的HTTP请求
 */
@Slf4j
@RestController
@RequestMapping("/api/lost-items")
public class LostItemController {

    private static final Logger logger = LoggerFactory.getLogger(LostItemController.class);

    @Autowired
    private LostItemService lostItemService;
    @Autowired
    private ItemImageService itemImageService;

    /**
     * 发布失物信息
     */
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @PostMapping(value = "/publish", consumes = "multipart/form-data")
    public Result<Object> publishLostItem(
            @RequestParam("itemName") String itemName,
            @RequestParam("description") String description,
            @RequestParam("lostTime") String lostTime,
            @RequestParam("lostLocation") String lostLocation,
            @RequestParam(value = "image", required = false) MultipartFile image,
            @RequestParam(value = "images", required = false) List<MultipartFile> images,
            @RequestParam(value = "mainImageIndex", required = false, defaultValue = "0") Integer mainImageIndex,
            HttpServletRequest request) {

        logger.info("收到发布失物信息请求 - 物品名称: {}, 描述: {}, 时间: {}, 地点: {}", itemName, description, lostTime, lostLocation);
        logger.info("图片数量: 单图={}, 多图={}, 主图索引={}", (image != null ? "有" : "无"), (images != null ? images.size() : 0), mainImageIndex);

        try {
            // 获取用户ID（由TokenInterceptor设置）
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                logger.error("发布失败：无法获取用户ID");
                return Result.fail("用户未登录或登录已过期");
            }

            logger.info("获取到的用户ID: {}", userId);

            // 构建 LostItemDTO
            LostItemDTO lostItemDTO = new LostItemDTO();
            lostItemDTO.setItemName(itemName);
            lostItemDTO.setDescription(description);
            lostItemDTO.setLostTime(lostTime);
            lostItemDTO.setLostLocation(lostLocation);
            lostItemDTO.setImage(image);
            lostItemDTO.setImages(images);
            lostItemDTO.setMainImageIndex(mainImageIndex);
            lostItemDTO.setUserId(userId);

            // 日志输出 LostItemDTO 数据
            logger.info("发布的失物信息DTO: {}", lostItemDTO);

            // 手动验证描述长度
            if (description.length() < 20) {
                return Result.fail("物品描述长度必须在20-500个字符之间，请提供详细描述，包括物品特征、颜色、品牌等信息");
            }

            // 调用 Service 层处理
            return lostItemService.publishLostItem(lostItemDTO);
        } catch (Exception e) {
            logger.error("发布失物信息时发生错误: {}", e.getMessage(), e);
            return Result.fail("发布失败，请稍后重试");
        }
    }

    /**
     * 查询失物信息（默认展示未认领的失物，默认展示过去一周的失物）
     */
    @GetMapping("/search")
    public Result<Map<String, Object>> searchLostItems(@RequestParam(required = false) String keyword, // 关键字
                                  @RequestParam(required = false) String lostLocation, // 丢失地点
                                  @RequestParam(required = false) String status, // 物品状态
                                  @RequestParam(required = false) String timeRange, // 时间范围（今天、昨天、三天内、一周内、一个月内）
                                  @RequestParam(required = false) String timeFilterType, // 筛选时间类型（丢失时间或发布时间）
                                  @RequestParam(required = false) String startDate, // 自定义开始时间
                                  @RequestParam(required = false) String endDate,   // 自定义结束时间
                                  @RequestParam(defaultValue = "1") int page, // 页码
                                  @RequestParam(defaultValue = "12") int size) { // 每页条数

        // 默认展示过去一周内丢失的物品
        if (timeRange == null || timeRange.isEmpty()) {
            timeRange = "lastWeek";  // 默认展示过去一周内丢失的物品
        }
        logger.info("查询的时间范围{}",timeRange);
        log.info("page:{},size:{}",page,size);

        // 调用业务层查询
        return lostItemService.searchLostItems(keyword, lostLocation, status, timeRange, timeFilterType, startDate, endDate, page, size);
    }

    // 获取失物详情
    @GetMapping("/detail/{id}")
    public Result<LostItemDetailsDTO> getLostItemDetails(@PathVariable Long id) {
        // 调用服务层方法获取失物详情
        LostItemDetailsDTO lostItemDetails = lostItemService.getLostItemDetails(id);

        // 如果失物信息不存在，返回404错误
        if (lostItemDetails == null) {
            return Result.fail("失物信息不存在");
        }

        // 返回获取到的失物详情
        return Result.success(lostItemDetails);
    }


    // 更新失物详情
    /**
     * 更新失物信息
     * @param id 失物ID
     * @return 更新结果
     */
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @PutMapping(value = "/update/{id}", consumes = "multipart/form-data")
    public Result<Object> updateLostItem(@PathVariable("id") Long id,
                                 @RequestParam("itemName") String itemName,
                                 @RequestParam("description") String description,
                                 @RequestParam("lostTime") String lostTime,
                                 @RequestParam("lostLocation") String lostLocation,
                                 @RequestParam(value = "image", required = false) MultipartFile image,
                                 @RequestParam(value = "images", required = false) List<MultipartFile> images,
                                 @RequestParam(value = "mainImageIndex", required = false, defaultValue = "0") Integer mainImageIndex,
                                 @RequestParam(value = "keepExistingImages", required = false, defaultValue = "false") String keepExistingImages,
                                 @RequestParam(value = "mainImageChanged", required = false, defaultValue = "false") String mainImageChanged,
                                 HttpServletRequest request) {

        logger.info("更新失物信息请求 - ID: {}, 物品名称: {}, 时间: {}, 地点: {}", id, itemName, lostTime, lostLocation);
        logger.info("图片数量: 单图={}, 多图={}, 主图索引={}, 保留原有图片={}, 主图索引是否变化={}",
                   (image != null ? "有" : "无"),
                   (images != null ? images.size() : 0),
                   mainImageIndex,
                   "true".equalsIgnoreCase(keepExistingImages) ? "是" : "否",
                   "true".equalsIgnoreCase(mainImageChanged) ? "是" : "否");

        // 构建 LostItemDTO
        LostItemDTO lostItemDTO = new LostItemDTO();
        lostItemDTO.setItemName(itemName);
        lostItemDTO.setDescription(description);
        lostItemDTO.setLostTime(lostTime);
        lostItemDTO.setLostLocation(lostLocation);
        lostItemDTO.setImage(image);
        lostItemDTO.setImages(images);
        lostItemDTO.setMainImageIndex(mainImageIndex);
        lostItemDTO.setKeepExistingImages("true".equalsIgnoreCase(keepExistingImages));
        lostItemDTO.setMainImageChanged("true".equalsIgnoreCase(mainImageChanged));

        return lostItemService.updateLostItem(id, lostItemDTO);
    }


    /**
     * 删除失物信息
     */
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @DeleteMapping("/delete/{id}")
    public Result<Object> deleteLostItem(@PathVariable Long id, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.fail("用户未登录或登录已过期");
        }

        return lostItemService.deleteLostItem(id, userId);
    }

    /**
     * 更新失物图片
     */
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @PostMapping("/update-images/{id}")
    public Result<Object> updateLostItemImages(
            @PathVariable Long id,
            @RequestParam(required = false) MultipartFile mainImage,
            @RequestParam(required = false) List<MultipartFile> additionalImages,
            @RequestParam(required = false) List<Long> retainImageIds,
            HttpServletRequest request) {

        // 获取当前用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.fail("用户未登录或登录已过期");
        }

        // 验证用户是否有权限更新该失物信息
        LostItem lostItem = lostItemService.getLostItemById(id);
        if (lostItem == null) {
            return Result.fail("失物信息不存在");
        }

        if (!lostItem.getUserId().equals(userId)) {
            return Result.fail("您无权更新此失物信息");
        }

        // 调用服务更新图片
        return itemImageService.updateItemImages(id, "LOST", mainImage, additionalImages, retainImageIds);
    }

    /**
     * 获取我的发布失物信息
     */
    @Autowired
    private SecurityUtil securityUtil;
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @GetMapping("/my-published/lost")
    public Result<Object> getMyPublishedLostItems() {
        Long userId = securityUtil.getCurrentUserId();
        if (userId == null) {
            return Result.fail("用户未登录或登录已过期");
        }
        return lostItemService.getMyPublishedItems(userId);
    }

    /**
     * 更新失物状态（未找回/已找回）
     */
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @PutMapping("/{id}/status")
    public Result<Object> updateLostItemStatus(@PathVariable Long id, @RequestBody Map<String, String> statusMap) {
        // 获取当前用户ID
        Long userId = securityUtil.getCurrentUserId();
        if (userId == null) {
            return Result.fail("用户未登录或登录已过期");
        }

        // 获取状态参数
        String status = statusMap.get("status");
        if (status == null || (!status.equals("LOST") && !status.equals("FOUND"))) {
            return Result.fail("无效的状态值，必须为 LOST 或 FOUND");
        }

        return lostItemService.updateLostItemStatus(id, status, userId);
    }
}