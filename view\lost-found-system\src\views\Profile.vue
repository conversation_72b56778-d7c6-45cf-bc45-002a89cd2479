<!-- Profile.vue -->
<template>
  <div class="profile-container">
    <!-- 移动端菜单按钮 -->
    <div class="mobile-menu-button" @click="toggleSidebar">
      <el-icon><Menu /></el-icon>
    </div>

    <!-- 左侧导航栏 -->
    <div class="sidebar" :class="{ 'sidebar-mobile-open': isSidebarOpen }">
      <div class="sidebar-header">
        <span class="sidebar-title">个人中心</span>
        <el-icon class="close-sidebar" @click="toggleSidebar" v-show="isSidebarOpen"><Close /></el-icon>
      </div>
      <el-menu
        class="sidebar-menu"
        :default-active="activeMenu"
        @select="handleSelect"
      >
        <el-menu-item index="profile">
          <el-icon><UserFilled /></el-icon>
          <span>个人资料</span>
        </el-menu-item>

        <el-menu-item index="my-posts">
          <el-icon><List /></el-icon>
          <span>我的发布</span>
        </el-menu-item>

        <el-menu-item index="matches">
          <el-icon><Link /></el-icon>
          <span>匹配状态</span>
        </el-menu-item>

        <el-menu-item index="notifications">
          <el-icon><Message /></el-icon>
          <span>系统通知</span>
        </el-menu-item>

        <el-menu-item index="settings">
          <el-icon><Tools /></el-icon>
          <span>设置</span>
        </el-menu-item>

        <el-menu-item index="logout" @click="handleLogout">
          <el-icon><SwitchButton /></el-icon>
          <span>退出登录</span>
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 遮罩层 -->
    <div class="sidebar-overlay" v-show="isSidebarOpen" @click="toggleSidebar"></div>

    <!-- 右侧内容区域 -->
    <div class="content">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores'
import { ElMessageBox } from 'element-plus'
import {
  UserFilled,
  List,
  Link,
  Message,
  Tools,
  SwitchButton,
  Menu,
  Close
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const activeMenu = ref('profile')
const isSidebarOpen = ref(false)

const handleSelect = (key) => {
  if (key !== 'logout') {
    router.push(`/profile/${key}`)
    // 在移动端选择菜单项后自动关闭侧边栏
    if (window.innerWidth <= 768) {
      isSidebarOpen.value = false
    }
  }
}

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 清除用户状态
    userStore.clearUser()
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')

    // 跳转到首页
    router.push('/')
  } catch {
    // 用户取消退出
  }
}
</script>

<style scoped>
.profile-container {
  display: flex;
  min-height: calc(100vh - 60px);
  position: relative;
  width: 100%;
}

.mobile-menu-button {
  display: none;
  position: fixed;
  top: 70px;
  left: 10px;
  z-index: 100;
  padding: 12px;
  background-color: var(--el-color-primary);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s;
}

.mobile-menu-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.sidebar {
  width: 220px;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  position: fixed;
  left: 0;
  height: calc(100vh - 60px);
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.close-sidebar {
  cursor: pointer;
  font-size: 20px;
  color: #909399;
  transition: color 0.3s;
}

.close-sidebar:hover {
  color: #409EFF;
}

.sidebar-menu {
  border-right: none;
  height: calc(100% - 60px);
  overflow-y: auto;
  padding: 10px 0;
}

.content {
  flex: 1;
  margin-left: 220px;
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.el-menu-item {
  display: flex;
  align-items: center;
  padding-left: 25px !important;
  height: 50px;
  margin: 5px 0;
  border-radius: 0 25px 25px 0;
  transition: all 0.3s;
}

.el-menu-item .el-icon {
  margin-right: 15px;
  font-size: 18px;
  transition: all 0.3s;
}

.el-menu-item.is-active {
  background-color: #ecf5ff !important;
  color: var(--el-color-primary);
  font-weight: 500;
}

.el-menu-item:hover {
  background-color: #f5f7fa !important;
  padding-left: 30px !important;
}

.el-menu-item.is-active .el-icon {
  color: var(--el-color-primary);
  transform: scale(1.1);
}

/* 遮罩层样式 */
.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(2px);
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .mobile-menu-button {
    display: block;
  }

  .sidebar {
    transform: translateX(-100%);
    width: 80%;
    max-width: 300px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  }

  .sidebar-mobile-open {
    transform: translateX(0);
  }

  .sidebar-header {
    display: flex;
  }

  .content {
    margin-left: 0;
    padding: 20px;
    border-radius: 8px;
  }

  .sidebar-overlay {
    display: block;
  }
}

/* 平板设备适配 */
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .sidebar {
    width: 200px;
  }

  .content {
    margin-left: 200px;
    padding: 25px;
  }

  .el-menu-item {
    padding-left: 20px !important;
  }

  .el-menu-item:hover {
    padding-left: 25px !important;
  }
}

/* 确保内容区域在小屏幕上也能完整显示 */
@media screen and (max-width: 480px) {
  .content {
    padding: 15px;
  }

  .profile-container {
    padding: 0 10px;
  }
}
</style>