-- 扩展 chat_sessions 表结构
ALTER TABLE chat_sessions 
ADD COLUMN last_message_id BIGINT COMMENT '最后一条消息ID',
ADD COLUMN last_message_content VARCHAR(255) COMMENT '最后一条消息内容摘要',
ADD COLUMN last_message_type VARCHAR(20) COMMENT '最后一条消息类型',
ADD COLUMN unread_count INT DEFAULT 0 COMMENT '未读消息计数',
ADD COLUMN status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '会话状态：ACTIVE, ARCHIVED, DELETED',
ADD COLUMN is_pinned BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
ADD COLUMN is_muted BOOLEAN DEFAULT FALSE COMMENT '是否静音',
ADD COLUMN created_at DATETIME COMMENT '创建时间',
ADD COLUMN updated_at DATETIME COMMENT '更新时间';

-- 更新现有记录的创建时间和更新时间
UPDATE chat_sessions 
SET created_at = last_message_time, 
    updated_at = last_message_time 
WHER<PERSON> created_at IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_chat_sessions_user1_id ON chat_sessions(user1_id);
CREATE INDEX idx_chat_sessions_user2_id ON chat_sessions(user2_id);
CREATE INDEX idx_chat_sessions_status ON chat_sessions(status);
CREATE INDEX idx_chat_sessions_last_message_time ON chat_sessions(last_message_time);
CREATE INDEX idx_chat_sessions_is_pinned ON chat_sessions(is_pinned);
