/**
 * 联系人相关的组合式函数
 * 提供联系人列表加载、状态管理等功能
 */
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores'
import { ElMessage } from 'element-plus'
import { debounce } from 'lodash-es'
import request from '@/utils/request'
import logger from '@/utils/logger'

// 创建联系人日志记录器
const contactLogger = logger.createLogger('Contacts')

/**
 * 联系人组合式函数
 * @param {Object} options 配置选项
 * @returns {Object} 联系人相关的状态和方法
 */
export function useContacts(options = {}) {
  // 当前用户信息
  const userStore = useUserStore()
  const userInfo = computed(() => userStore.userInfo || {})

  // 联系人状态
  const contacts = ref([])
  const currentContact = ref(null)
  const loading = ref(false)

  /**
   * 加载联系人列表
   * @param {Object} options 加载选项
   */
  const loadContacts = async (options = {}) => {
    if (!userInfo.value.id) {
      contactLogger.warn('用户未登录，无法加载联系人列表')
      return
    }

    try {
      loading.value = true
      contactLogger.info('开始加载联系人列表')

      // 发送请求获取联系人列表 - 使用正确的API路径，包含用户ID
      const userId = userInfo.value.id
      contactLogger.info(`请求联系人列表，用户ID: ${userId}`)
      const response = await request({
        url: `/chat/contacts/${userId}`,
        method: 'get'
      })

      if (response.code === 200 && Array.isArray(response.data)) {
        // 更新联系人列表
        contacts.value = response.data.map(contact => ({
          ...contact,
          // 确保联系人有必要的属性
          id: contact.id,
          name: contact.name || contact.username || '未知用户',
          avatar: contact.avatar || null,
          online: contact.online || false,
          unreadCount: contact.unreadCount || 0,
          lastMessage: contact.lastMessage || '',
          lastTime: contact.lastTime || null
        }))

        // 获取联系人在线状态
        updateContactsOnlineStatus()

        // 按最后消息时间排序
        contacts.value.sort((a, b) => {
          const timeA = a.lastTime ? new Date(a.lastTime).getTime() : 0
          const timeB = b.lastTime ? new Date(b.lastTime).getTime() : 0
          return timeB - timeA
        })

        contactLogger.info('联系人列表加载成功，数量:', contacts.value.length)

        // 如果有当前联系人，更新其信息
        if (currentContact.value) {
          const updatedContact = contacts.value.find(c =>
            String(c.id) === String(currentContact.value.id)
          )

          if (updatedContact) {
            currentContact.value = { ...updatedContact }
            contactLogger.info('已更新当前联系人信息:', currentContact.value.name)
          }
        }

        return contacts.value
      } else {
        contactLogger.error('加载联系人列表失败:', response.message)
        ElMessage.error('加载联系人列表失败')
        return []
      }
    } catch (error) {
      contactLogger.error('加载联系人列表异常:', error)
      ElMessage.error('加载联系人列表失败，请稍后重试')
      return []
    } finally {
      loading.value = false
    }
  }

  /**
   * 选择联系人
   * @param {Object} contact 联系人对象
   */
  const selectContact = (contact) => {
    if (!contact) return

    // 更新当前联系人
    currentContact.value = { ...contact }

    // 重置未读消息计数
    const index = contacts.value.findIndex(c => String(c.id) === String(contact.id))
    if (index !== -1) {
      contacts.value[index].unreadCount = 0
    }

    contactLogger.info('已选择联系人:', contact.name)

    // 更新URL参数
    updateUrlWithContactId(contact.id)
  }

  /**
   * 更新URL参数
   * @param {string|number} contactId 联系人ID
   */
  const updateUrlWithContactId = (contactId) => {
    if (!contactId) return

    const url = new URL(window.location.href)
    url.searchParams.set('contactId', contactId)
    window.history.replaceState({}, '', url.toString())
  }

  /**
   * 从URL参数获取联系人ID
   */
  const getContactIdFromUrl = () => {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('contactId')
  }

  /**
   * 从URL参数打开联系人
   */
  const openContactFromUrl = () => {
    const contactId = getContactIdFromUrl()

    if (contactId && contacts.value.length > 0) {
      const contact = contacts.value.find(c => String(c.id) === String(contactId))
      if (contact) {
        selectContact(contact)
        return true
      }
    }

    return false
  }

  /**
   * 更新联系人最新消息
   * @param {Object} message 消息对象
   */
  const updateContactLastMessage = (message) => {
    if (!message || !message.senderId || !message.receiverId) {
      contactLogger.warn('消息缺少必要的发送者或接收者ID')
      return
    }

    // 确定消息是否是自己发送的
    const isSelfMessage = String(message.senderId) === String(userInfo.value.id)

    // 找到相关联系人 - 如果是自己发送的消息，联系人是接收者；否则联系人是发送者
    const contactId = isSelfMessage ? message.receiverId : message.senderId

    if (!contactId) {
      contactLogger.warn('无法确定联系人ID')
      return
    }

    // 查找联系人
    const index = contacts.value.findIndex(c => String(c.id) === String(contactId))

    if (index === -1) {
      contactLogger.warn('未找到消息对应的联系人:', contactId)
      // 可以在这里添加刷新联系人列表的逻辑
      return
    }

    // 更新联系人的最新消息
    const contact = contacts.value[index]
    contact.lastMessage = message.content || message.message || '[消息]'
    contact.lastTime = message.time || message.timestamp || new Date().toISOString()
    contact.messageType = message.messageType || 'TEXT'

    // 如果不是当前聊天且不是自己发的消息，增加未读计数
    if ((!currentContact.value || String(currentContact.value.id) !== String(contactId)) && !isSelfMessage) {
      contact.unreadCount = (contact.unreadCount || 0) + 1
      contactLogger.info(`更新联系人 ${contact.name} 的未读消息计数: ${contact.unreadCount}`)
    }

    // 将联系人移到列表顶部
    if (index > 0) {
      contacts.value.splice(index, 1)
      contacts.value.unshift(contact)
      contactLogger.info('联系人已移至列表顶部:', contact.name)
    }
  }

  /**
   * 检查联系人是否在线
   * @param {string|number} contactId 联系人ID
   * @returns {boolean} 是否在线
   */
  const isContactOnline = async (contactId) => {
    if (!contactId) return false

    try {
      const response = await request({
        url: `/user/status/${contactId}`,
        method: 'get'
      })

      if (response.code === 200) {
        return response.data?.online || false
      }

      return false
    } catch (error) {
      contactLogger.error('获取联系人在线状态失败:', error)
      return false
    }
  }

  /**
   * 更新所有联系人的在线状态
   */
  const updateContactsOnlineStatus = async () => {
    if (!contacts.value.length) return

    try {
      // 导入在线状态服务
      const { getContactsStatus } = await import('@/utils/contact-status-new')

      // 获取所有联系人ID
      const contactIds = contacts.value.map(contact => String(contact.id))

      // 获取联系人在线状态
      const statuses = await getContactsStatus(contactIds)

      // 更新联系人在线状态
      contacts.value.forEach(contact => {
        const status = statuses.get(String(contact.id))
        if (status) {
          contact.online = status.online
        }
      })

      contactLogger.info('联系人在线状态已更新')
    } catch (error) {
      contactLogger.error('更新联系人在线状态失败:', error)
    }
  }

  /**
   * 订阅联系人状态
   * @param {string|number} contactId 联系人ID
   */
  const subscribeContactStatus = async (contactId) => {
    if (!contactId) return

    try {
      const { isWebSocketConnected } = await import('@/utils/websocket/index')
      const connected = await isWebSocketConnected()

      if (!connected) {
        contactLogger.warn('WebSocket未连接，无法订阅联系人状态')
        return
      }

      // 导入联系人状态服务
      const { subscribeContactStatus: subscribe } = await import('@/utils/contact-status-new')

      // 订阅联系人状态
      await subscribe(contactId)

      contactLogger.info('已订阅联系人状态:', contactId)
    } catch (error) {
      contactLogger.error('订阅联系人状态失败:', error)
    }
  }

  // 创建防抖版本的函数
  const debouncedLoadContacts = debounce(loadContacts, 300)

  return {
    // 状态
    contacts,
    currentContact,
    loading,

    // 方法
    loadContacts,
    debouncedLoadContacts,
    selectContact,
    openContactFromUrl,
    updateContactLastMessage,
    isContactOnline,
    subscribeContactStatus,
    updateContactsOnlineStatus
  }
}

export default useContacts
