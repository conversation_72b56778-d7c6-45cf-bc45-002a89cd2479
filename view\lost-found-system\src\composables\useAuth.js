import { useUserStore, useAuthStore } from '../stores'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

export function useAuth() {
  const userStore = useUserStore()
  const authStore = useAuthStore()
  const router = useRouter()

  // 检查是否已登录，如果未登录则提示并显示登录对话框
  const checkAuth = () => {
    console.log('检查登录状态:', { isAuthenticated: userStore.isAuthenticated })
    if (!userStore.isAuthenticated) {
      console.log('用户未登录，显示登录对话框')
      ElMessage.warning('请先登录后再执行此操作')

      // 使用登录对话框
      authStore.showLoginDialog({
        tab: 'login',
        onSuccess: (userInfo) => {
          console.log('登录成功，用户信息:', userInfo)
        }
      })

      return false
    }
    return true
  }

  // 检查是否是当前用户的内容
  const isOwner = (userId) => {
    const currentUserId = userStore.userInfo?.id
    console.log('检查内容所有权:', { userId, currentUserId })
    return currentUserId === userId
  }

  // 检查是否有权限执行操作
  const canPerformAction = (userId = null) => {
    console.log('检查操作权限:', {
      isAuthenticated: userStore.isAuthenticated,
      requestedUserId: userId,
      currentUserId: userStore.userInfo?.id
    })

    if (!userStore.isAuthenticated) {
      console.log('用户未登录，显示登录对话框')
      ElMessage.warning('请先登录后再执行此操作')

      // 使用登录对话框
      authStore.showLoginDialog({
        tab: 'login',
        onSuccess: (userInfo) => {
          console.log('登录成功，用户信息:', userInfo)
        }
      })

      return false
    }

    // 如果提供了userId，检查是否是内容所有者
    if (userId !== null && !isOwner(userId)) {
      console.log('用户无权限执行此操作')
      ElMessage.warning('您没有权限执行此操作')
      return false
    }

    console.log('权限检查通过')
    return true
  }

  return {
    checkAuth,
    isOwner,
    canPerformAction
  }
}