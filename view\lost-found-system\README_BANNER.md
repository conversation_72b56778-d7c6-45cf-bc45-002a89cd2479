# 首页轮播图替换说明

为了将首页轮播图替换为您自己的图片，请按照以下步骤操作：

## 1. 创建图片目录

已经为您创建了图片存储目录：
```
view\lost-found-system\public\images\banner
```

## 2. 复制图片到项目

请将以下图片复制到上述目录中：

- `D:\论文\设计流程\首页轮播图1.jpg` → 重命名为 `banner1.jpg`
- `D:\论文\设计流程\首页轮播图2.jpeg` → 重命名为 `banner2.jpg`
- `D:\论文\设计流程\首页轮播图3.jpg` → 重命名为 `banner3.jpg`

## 3. 代码修改

已经修改了 `Home.vue` 文件中的轮播图配置，使用新的图片路径：

```javascript
// 轮播图数据
const bannerItems = [
  {
    title: '校园失物招领平台',
    description: '快速发布、查找和匹配失物与拾物信息',
    image: '/images/banner/banner1.jpg',
    // ...其他配置
  },
  {
    title: '智能匹配技术',
    description: '使用CLIP+FAISS技术，通过图片或文字描述快速匹配失物与拾物',
    image: '/images/banner/banner2.jpg',
    // ...其他配置
  },
  {
    title: '高效便捷的沟通',
    description: '内置即时通讯系统，方便失主与拾主快速联系',
    image: '/images/banner/banner3.jpg',
    // ...其他配置
  }
]
```

## 4. 查看效果

完成上述步骤后，重新启动项目或刷新页面，即可看到新的轮播图效果。

## 注意事项

- 确保图片尺寸适合轮播图显示（建议宽度至少 1200px，高度约 400px）
- 如果图片太大，可能会影响页面加载速度，建议进行适当压缩
- 如果需要调整轮播图的高度，可以修改 `Home.vue` 文件中的 `el-carousel` 组件的 `height` 属性
