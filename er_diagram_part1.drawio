<mxfile host="app.diagrams.net" modified="2023-06-15T12:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64)" etag="your-etag" version="14.7.7" type="device">
  <diagram id="lost-found-er-diagram" name="失物招领系统ER图">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 实体：用户 -->
        <mxCell id="user" value="用户" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="200" y="200" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：失物信息 -->
        <mxCell id="lost_item" value="失物信息" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="400" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：招领信息 -->
        <mxCell id="found_item" value="招领信息" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="400" y="400" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：物品图片 -->
        <mxCell id="item_image" value="物品图片" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="550" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：特征向量 -->
        <mxCell id="feature_vector" value="特征向量" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="650" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：匹配记录 -->
        <mxCell id="match_record" value="匹配记录" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="450" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：聊天会话 -->
        <mxCell id="chat_session" value="聊天会话" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="500" y="200" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：聊天消息 -->
        <mxCell id="chat_message" value="聊天消息" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="500" y="300" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：系统公告 -->
        <mxCell id="system_announcement" value="系统公告" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="350" y="100" width="100" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
