<template>
  <div class="unified-notifications">
    <el-popover
      placement="bottom-end"
      :width="380"
      trigger="click"
      popper-class="notification-popover"
      v-model:visible="popoverVisible"
      :show-arrow="false"
      :teleported="true"
    >
      <template #reference>
        <el-badge :value="totalUnreadCount > 0 ? totalUnreadCount : ''" :hidden="totalUnreadCount <= 0" class="notification-badge" type="danger">
          <el-button class="notification-button" :icon="Bell" circle />
        </el-badge>
      </template>

      <div class="notification-header-container">
        <div class="notification-header-title">
          <h3>通知中心</h3>
          <span class="notification-count" v-if="totalUnreadCount > 0">{{ totalUnreadCount }}条未读</span>
        </div>
        <div class="notification-header-actions">
          <el-button v-if="hasUnreadNotifications" type="primary" size="small" link @click="markAllAsRead">
            全部标为已读
          </el-button>
        </div>
      </div>

      <!-- 通知类型选项卡 -->
      <el-tabs v-model="activeTab" class="notification-tabs" type="card">
        <el-tab-pane label="全部" name="all">
          <div v-if="loading" class="notification-loading">
            <el-skeleton :rows="3" animated />
          </div>

          <div v-else-if="allNotifications.length === 0" class="empty-notifications">
            <el-empty description="暂无通知" :image-size="80" />
          </div>

          <el-scrollbar max-height="450px" v-else>
            <div
              v-for="notification in allNotifications"
              :key="notification.id"
              class="notification-item"
              :class="{ 'unread': !notification.isRead }"
              @click="handleNotificationClick(notification, $event)"
            >
              <div class="notification-dot" v-if="!notification.isRead"></div>
              <div class="notification-icon">
                <el-avatar :icon="getNotificationIcon(notification.type)" :size="36"
                  :style="{ backgroundColor: getNotificationColor(notification) }" />
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-text">{{ formatNotificationContent(notification.content || notification.message) }}</div>
                <div class="notification-meta">
                  <span class="notification-time">{{ formatTime(notification.createdAt) }}</span>
                  <el-tag v-if="notification.type === 'match'" size="small" type="success" effect="plain" class="notification-tag">
                    {{ notification.similarity ? (notification.similarity * 100).toFixed(0) + '%相似' : '匹配' }}
                  </el-tag>
                  <el-tag v-else size="small" :type="getNotificationTagType(notification.type)" effect="plain" class="notification-tag">
                    {{ getNotificationTypeText(notification.type) }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <el-tab-pane label="系统" name="system">
          <div v-if="loading" class="notification-loading">
            <el-skeleton :rows="3" animated />
          </div>

          <div v-else-if="systemNotifications.length === 0" class="empty-notifications">
            <el-empty description="暂无系统通知" :image-size="80" />
          </div>

          <el-scrollbar max-height="450px" v-else>
            <div
              v-for="notification in systemNotifications"
              :key="notification.id"
              class="notification-item"
              :class="{ 'unread': !notification.isRead }"
              @click="handleSystemNotificationClick(notification)"
            >
              <div class="notification-dot" v-if="!notification.isRead"></div>
              <div class="notification-icon">
                <el-avatar :icon="Bell" :size="36" style="background-color: #409EFF;" />
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-text">{{ formatNotificationContent(notification.content || notification.message) }}</div>
                <div class="notification-meta">
                  <span class="notification-time">{{ formatTime(notification.createdAt) }}</span>
                  <el-tag size="small" type="info" effect="plain" class="notification-tag">系统</el-tag>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <el-tab-pane label="匹配" name="match">
          <div v-if="loading" class="notification-loading">
            <el-skeleton :rows="3" animated />
          </div>

          <div v-else-if="matchNotifications.length === 0" class="empty-notifications">
            <el-empty description="暂无匹配通知" :image-size="80" />
          </div>

          <el-scrollbar max-height="450px" v-else>
            <div
              v-for="notification in matchNotifications"
              :key="notification.id"
              class="notification-item"
              :class="{ 'unread': !notification.isRead }"
              @click="handleMatchNotificationClick(notification, $event)"
            >
              <div class="notification-dot" v-if="!notification.isRead"></div>
              <div class="notification-icon">
                <el-avatar :icon="Link" :size="36" :style="{ backgroundColor: getSimilarityColor(notification.similarity) }" />
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-text">{{ formatNotificationContent(notification.content || notification.message) }}</div>
                <div class="notification-meta">
                  <span class="notification-time">{{ formatTime(notification.createdAt) }}</span>
                  <el-tag size="small" type="success" effect="plain" class="notification-tag">
                    {{ (notification.similarity * 100).toFixed(0) }}%相似
                  </el-tag>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <el-tab-pane label="公告" name="announcement">
          <div v-if="loading" class="notification-loading">
            <el-skeleton :rows="3" animated />
          </div>

          <div v-else-if="announcements.length === 0" class="empty-notifications">
            <el-empty description="暂无系统公告" :image-size="80" />
          </div>

          <el-scrollbar max-height="450px" v-else>
            <div
              v-for="announcement in announcements"
              :key="announcement.id"
              class="notification-item"
              :class="{ 'unread': !announcement.isRead }"
              @click="handleAnnouncementClick(announcement)"
            >
              <div class="notification-dot" v-if="!announcement.isRead"></div>
              <div class="notification-icon">
                <el-avatar :icon="Notification" :size="36" :style="{ backgroundColor: getImportanceColor(announcement.importance) }" />
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ announcement.title }}</div>
                <div class="notification-text">{{ formatNotificationContent(announcement.content || announcement.message) }}</div>
                <div class="notification-meta">
                  <span class="notification-time">{{ formatTime(announcement.createdAt) }}</span>
                  <el-tag size="small" type="warning" effect="plain" class="notification-tag">公告</el-tag>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>

      <div class="notification-footer">
        <el-button type="primary" link @click="viewAllNotifications">
          查看全部通知
        </el-button>
      </div>
    </el-popover>

    <!-- 匹配通知详情对话框 - 直接实现，不使用组件 -->
    <el-dialog
      v-model="matchNotificationDetailVisible"
      :title="currentMatchNotification?.title || '匹配通知详情'"
      width="600px"
      destroy-on-close
      append-to-body
      :modal-append-to-body="false"
      :z-index="9999"
    >
      <div v-if="currentMatchNotification" class="match-notification-detail">
        <!-- 匹配类型标签 -->
        <div class="notification-type">
          <el-tag :type="getMatchTypeTagType(currentMatchNotification.matchType)">
            {{ getMatchTypeLabel(currentMatchNotification.matchType) }}
          </el-tag>
          <el-tag type="success" effect="plain" class="similarity-tag">
            匹配度: {{ (currentMatchNotification.similarity * 100).toFixed(0) }}%
          </el-tag>
        </div>

        <!-- 通知内容 -->
        <div class="notification-content">
          {{ currentMatchNotification.content }}
        </div>

        <!-- 相似度雷达图 -->
        <div v-if="hasSimilarityDetails" class="similarity-radar">
          <h4>匹配度详情分析</h4>
          <similarity-radar-chart
            :similarities="similarityDetails"
            height="250px"
            title=""
          />
        </div>

        <!-- 匹配物品信息 -->
        <div class="related-item">
          <el-alert
            :title="relatedItemTitle"
            type="info"
            :closable="false"
          >
            <template #default>
              <div class="item-info-content">
                <p>您可以点击下方按钮查看相关物品的详细信息</p>
                <div class="detail-actions">
                  <el-button
                    type="primary"
                    @click="viewRelatedItem"
                  >
                    <el-icon><ArrowRight /></el-icon>
                    查看物品详情
                  </el-button>
                  <el-button
                    type="success"
                    @click="contactOwner"
                  >
                    <el-icon><ChatLineRound /></el-icon>
                    联系{{ currentMatchNotification.itemType === 'LOST' ? '失主' : '拾主' }}
                  </el-button>
                </div>
              </div>
            </template>
          </el-alert>
        </div>

        <!-- 通知时间 -->
        <div class="notification-time">
          <span class="label">通知时间：</span>
          <span class="value">{{ formatDateTime(currentMatchNotification.createdAt) }}</span>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeMatchDetailDialog">关闭</el-button>
          <el-button type="danger" @click="handleDeleteMatchNotification">删除通知</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Bell, Link, Notification, ChatLineRound, InfoFilled, SuccessFilled, Warning } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { wsClient } from '@/utils/websocket'
import { useNotificationStore } from '@/stores/notificationStore'
import SimilarityRadarChart from '@/components/SimilarityRadarChart.vue'
import { deleteMatchNotification } from '@/api/notification'

// 实例化通知状态管理器
const notificationStore = useNotificationStore()
const router = useRouter()
const popoverVisible = ref(false)
const activeTab = ref('all')
const loading = computed(() => notificationStore.loading)

// 匹配通知详情对话框
const matchNotificationDetailVisible = ref(false)
const currentMatchNotification = ref(null)

// 计算属性：相似度详情
const similarityDetails = computed(() => {
  if (!currentMatchNotification.value) return {};
  return {
    image_to_image: currentMatchNotification.value.imageToImageSimilarity || 0,
    image_to_text: currentMatchNotification.value.imageToTextSimilarity || 0,
    text_to_image: currentMatchNotification.value.textToImageSimilarity || 0,
    text_to_text: currentMatchNotification.value.textToTextSimilarity || 0
  };
});

// 计算属性：是否有相似度详情
const hasSimilarityDetails = computed(() => {
  if (!currentMatchNotification.value) return false;
  return currentMatchNotification.value.imageToImageSimilarity !== null ||
         currentMatchNotification.value.imageToTextSimilarity !== null ||
         currentMatchNotification.value.textToImageSimilarity !== null ||
         currentMatchNotification.value.textToTextSimilarity !== null;
});

// 计算属性：关联物品标题
const relatedItemTitle = computed(() => {
  if (!currentMatchNotification.value) return '';
  const itemType = currentMatchNotification.value.itemType === 'LOST' ? '失物' : '拾物';
  return `相关${itemType}信息（ID: ${currentMatchNotification.value.itemId}）`;
});

// 使用store中的数据
const matchNotifications = computed(() => notificationStore.matchNotifications)
const systemNotifications = computed(() => notificationStore.systemNotifications)
const announcements = computed(() => notificationStore.announcements)

// 使用store中的未读数量
const unreadMatchCount = computed(() => notificationStore.unreadMatchCount)
const unreadSystemCount = computed(() => notificationStore.unreadSystemCount)
const unreadAnnouncementCount = computed(() => notificationStore.unreadAnnouncementCount)

// 计算总未读数量 - 使用store的getter
const totalUnreadCount = computed(() => notificationStore.totalUnreadCount)

// 计算是否有未读通知
const hasUnreadMatchNotifications = computed(() => {
  return matchNotifications.value.some(n => !n.isRead)
})

const hasUnreadSystemNotifications = computed(() => {
  return systemNotifications.value.some(n => !n.isRead)
})

const hasUnreadAnnouncements = computed(() => {
  return announcements.value.some(a => !a.isRead)
})

const hasUnreadNotifications = computed(() => {
  return hasUnreadMatchNotifications.value ||
         hasUnreadSystemNotifications.value ||
         hasUnreadAnnouncements.value
})

// 合并所有通知 - 使用store的getter
const allNotifications = computed(() => notificationStore.allNotifications)

// 标记匹配通知为已读
const markMatchAsRead = async (notificationId) => {
  try {
    console.log('🔔🔔🔔 标记匹配通知为已读:', notificationId)
    await notificationStore.markMatchAsRead(notificationId)
  } catch (error) {
    console.error('🔔🔔🔔 标记匹配通知为已读失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 标记系统通知为已读
const markSystemAsRead = async (notificationId) => {
  try {
    console.log('🔔🔔🔔 标记系统通知为已读:', notificationId)
    await notificationStore.markSystemAsRead(notificationId)
  } catch (error) {
    console.error('🔔🔔🔔 标记系统通知为已读失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 全部匹配通知标为已读
const markAllMatchAsRead = async () => {
  try {
    console.log('🔔🔔🔔 标记所有匹配通知为已读')
    await notificationStore.markAllMatchNotificationsAsRead()
    ElMessage.success('已将所有匹配通知标记为已读')
  } catch (error) {
    console.error('🔔🔔🔔 标记所有匹配通知为已读失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 全部系统通知标为已读
const markAllSystemAsRead = async () => {
  try {
    console.log('🔔🔔🔔 标记所有系统通知为已读')
    await notificationStore.markAllSystemNotificationsAsRead()
    ElMessage.success('已将所有系统通知标记为已读')
  } catch (error) {
    console.error('🔔🔔🔔 标记所有系统通知为已读失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 全部公告标为已读
const markAllAnnouncementsAsRead = async () => {
  try {
    console.log('🔔🔔🔔 标记所有公告为已读')
    await notificationStore.markAllAnnouncementsAsRead()
    ElMessage.success('已将所有公告标记为已读')
  } catch (error) {
    console.error('🔔🔔🔔 标记所有公告为已读失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 全部标为已读
const markAllAsRead = async () => {
  try {
    console.log('🔔🔔🔔 标记所有通知为已读')
    await notificationStore.markAllAsRead()
    ElMessage.success('已将所有通知标记为已读')

    // 强制刷新数据
    setTimeout(() => {
      notificationStore.fetchAllNotifications(true)
      notificationStore.fetchAllUnreadCounts(true)
    }, 500)
  } catch (error) {
    console.error('🔔🔔🔔 标记所有通知为已读失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 处理匹配通知点击
const handleMatchNotificationClick = async (notification, event) => {
  try {
    console.log('【匹配通知调试】处理匹配通知点击:', notification);
    console.log('【匹配通知调试】通知ID:', notification.id);
    console.log('【匹配通知调试】物品ID:', notification.itemId);
    console.log('【匹配通知调试】物品类型:', notification.itemType);

    // 标记为已读
    if (!notification.isRead) {
      console.log('【匹配通知调试】匹配通知未读，标记为已读');
      await notificationStore.markMatchAsRead(notification.id);

      // 手动更新本地状态
      notification.isRead = true;

      // 显示成功消息
      ElMessage.success({
        message: '已标记为已读',
        duration: 2000
      });
    }

    // 检查物品ID和类型是否存在
    if (!notification.itemId) {
      console.error('【匹配通知调试】错误: 物品ID不存在');
      ElMessage.error('无法查看物品详情：物品ID不存在');
      return;
    }

    if (!notification.itemType) {
      console.error('【匹配通知调试】错误: 物品类型不存在');
      ElMessage.error('无法查看物品详情：物品类型不存在');
      return;
    }

    // 关闭弹窗
    popoverVisible.value = false;

    // 创建一个完整的通知对象副本，确保所有必要的字段都存在
    const completeNotification = {
      ...notification,
      // 确保这些关键字段存在
      id: notification.id,
      itemId: notification.itemId,
      itemType: notification.itemType,
      similarity: notification.similarity || 0,
      matchType: notification.matchType || 'UNKNOWN',
      // 如果有相似度详情，也包含它们
      imageToImageSimilarity: notification.imageToImageSimilarity,
      imageToTextSimilarity: notification.imageToTextSimilarity,
      textToImageSimilarity: notification.textToImageSimilarity,
      textToTextSimilarity: notification.textToTextSimilarity
    };

    console.log('【匹配通知调试】创建完整通知对象:', completeNotification);

    // 阻止事件冒泡和默认行为
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    // 直接显示详情对话框
    currentMatchNotification.value = completeNotification;
    console.log('【匹配通知调试】已设置当前匹配通知:', currentMatchNotification.value);

    // 确保在DOM更新后再显示对话框
    setTimeout(() => {
      // 直接设置对话框可见性
      matchNotificationDetailVisible.value = true;
      console.log('【匹配通知调试】已设置对话框可见性为true:', matchNotificationDetailVisible.value);

      // 强制重新渲染对话框
      document.body.style.overflow = 'hidden';
      setTimeout(() => {
        document.body.style.overflow = '';
      }, 10);

      // 记录对话框状态
      console.log('【匹配通知调试】对话框状态:', {
        visible: matchNotificationDetailVisible.value,
        notification: currentMatchNotification.value,
        bodyHasDialog: document.querySelector('.el-dialog__wrapper') !== null
      });
    }, 50);
  } catch (error) {
    console.error('【匹配通知调试】处理匹配通知点击失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
}

// 处理系统通知点击
const handleSystemNotificationClick = async (notification) => {
  try {
    console.log('🔔🔔🔔 处理系统通知点击:', notification.id)

    // 标记为已读
    if (!notification.isRead) {
      console.log('🔔🔔🔔 系统通知未读，标记为已读')
      await notificationStore.markSystemAsRead(notification.id)

      // 手动更新本地状态
      notification.isRead = true

      // 显示成功消息
      ElMessage.success({
        message: '已标记为已读',
        duration: 2000
      })
    }

    // 关闭弹窗
    popoverVisible.value = false

    // 显示通知详情
    ElMessageBox.alert(notification.content || notification.message, notification.title, {
      confirmButtonText: '确定',
      callback: () => {
        // 对话框关闭后强制刷新数据
        setTimeout(() => {
          notificationStore.fetchAllNotifications(true)
          notificationStore.fetchAllUnreadCounts(true)
        }, 300)
      }
    })
  } catch (error) {
    console.error('🔔🔔🔔 处理系统通知点击失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 处理公告点击
const handleAnnouncementClick = async (announcement) => {
  console.log('🔔🔔🔔 处理公告点击:', announcement)

  try {
    // 标记为已读
    if (!announcement.isRead) {
      console.log('🔔🔔🔔 公告未读，标记为已读')
      const result = await notificationStore.markAnnouncementRead(announcement.id)
      console.log('🔔🔔🔔 标记公告已读结果:', result)

      if (result && result.code === 200) {
        // 手动更新本地状态
        announcement.isRead = true
        announcement.status = 'READ'

        // 立即更新未读计数
        notificationStore.decrementUnreadAnnouncementCount()

        // 显示成功消息
        ElMessage.success({
          message: '已标记为已读',
          duration: 2000
        })
      }
    } else {
      console.log('🔔🔔🔔 公告已读，无需标记')
    }

    // 获取公告详情
    popoverVisible.value = false

    ElMessageBox.alert(announcement.content, announcement.title, {
      confirmButtonText: '确定',
      dangerouslyUseHTMLString: true,
      callback: () => {
        // 对话框关闭后强制刷新数据
        setTimeout(() => {
          notificationStore.fetchAllNotifications(true)
          notificationStore.fetchAllUnreadCounts(true)
        }, 300)
      }
    })
  } catch (error) {
    console.error('🔔🔔🔔 处理公告点击失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 处理通知点击
const handleNotificationClick = (notification, event) => {
  if (notification.type === 'match') {
    handleMatchNotificationClick(notification, event)
  } else if (notification.type === 'system') {
    handleSystemNotificationClick(notification)
  } else if (notification.type === 'announcement') {
    handleAnnouncementClick(notification)
  }
}

// 处理匹配通知删除
const handleMatchNotificationDeleted = (notificationId) => {
  console.log('🔔🔔🔔 匹配通知已删除:', notificationId)

  // 从列表中移除该通知
  notificationStore.removeMatchNotification(notificationId)

  // 刷新通知数量
  notificationStore.fetchAllUnreadCounts()

  // 显示成功消息
  ElMessage.success({
    message: '通知已删除',
    duration: 2000
  })
}

// 查看全部通知
const viewAllNotifications = () => {
  popoverVisible.value = false
  router.push('/profile/notifications')
}



// 关闭匹配通知详情对话框
const closeMatchDetailDialog = () => {
  console.log('【匹配通知调试】关闭匹配通知详情对话框');
  matchNotificationDetailVisible.value = false;
}

// 查看关联物品
const viewRelatedItem = () => {
  try {
    console.log('【匹配通知调试】查看关联物品，通知对象:', currentMatchNotification.value);

    if (!currentMatchNotification.value) {
      console.error('【匹配通知调试】错误: 当前匹配通知为空');
      ElMessage.error('无法查看物品详情：通知数据不存在');
      return;
    }

    // 确保物品ID和类型存在
    if (!currentMatchNotification.value.itemId) {
      console.error('【匹配通知调试】错误: 物品ID不存在');
      ElMessage.error('无法查看物品详情：物品ID不存在');
      return;
    }

    if (!currentMatchNotification.value.itemType) {
      console.error('【匹配通知调试】错误: 物品类型不存在');
      ElMessage.error('无法查看物品详情：物品类型不存在');
      return;
    }

    // 构建路由路径
    const path = currentMatchNotification.value.itemType === 'LOST'
      ? `/lost-items/detail/${currentMatchNotification.value.itemId}`
      : `/found-items/detail/${currentMatchNotification.value.itemId}`;

    console.log('【匹配通知调试】跳转路径:', path);

    // 关闭对话框
    closeMatchDetailDialog();

    // 使用setTimeout确保对话框完全关闭后再跳转
    setTimeout(() => {
      // 使用try-catch包装路由跳转
      try {
        console.log('【匹配通知调试】开始路由跳转到:', path);
        router.push(path);
        console.log('【匹配通知调试】路由跳转成功');
      } catch (error) {
        console.error('【匹配通知调试】路由跳转失败:', error);
        // 尝试使用window.location作为备选方案
        console.log('【匹配通知调试】尝试使用window.location跳转');
        window.location.href = path;
      }
    }, 200);
  } catch (error) {
    console.error('【匹配通知调试】查看关联物品失败:', error);
    ElMessage.error('查看物品详情失败，请稍后重试');
  }
}

// 联系物主
const contactOwner = () => {
  // 这里可以添加跳转到聊天页面的逻辑
  ElMessage.success(`即将跳转到与物品发布者的聊天页面`);
  closeMatchDetailDialog();
  router.push('/chat');
}

// 处理删除匹配通知
const handleDeleteMatchNotification = () => {
  if (!currentMatchNotification.value) {
    ElMessage.error('通知数据不存在');
    return;
  }

  ElMessageBox.confirm('确定要删除这条通知吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteMatchNotification(currentMatchNotification.value.id);
      ElMessage.success('删除成功');
      closeMatchDetailDialog();

      // 从列表中移除该通知
      notificationStore.removeMatchNotification(currentMatchNotification.value.id);

      // 刷新通知数量
      notificationStore.fetchAllUnreadCounts();
    } catch (error) {
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    // 用户取消操作
  });
}

// 格式化通知内容（只显示前30个字符）
const formatNotificationContent = (content) => {
  if (!content) return ''
  const maxLength = 30
  return content.length > maxLength ? content.substring(0, maxLength) + '...' : content
}

// 格式化时间为"几分钟前"的形式
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';

  try {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (e) {
    return dateTimeStr;
  }
}

// 根据相似度获取颜色
const getSimilarityColor = (similarity) => {
  if (!similarity) return '#909399'
  if (similarity >= 0.9) return '#67C23A' // 高相似度 - 绿色
  if (similarity >= 0.7) return '#E6A23C' // 中等相似度 - 黄色
  return '#909399' // 低相似度 - 灰色
}

// 根据重要性获取颜色
const getImportanceColor = (importance) => {
  if (!importance) return '#409EFF'
  if (importance === 'HIGH') return '#F56C6C' // 高重要性 - 红色
  if (importance === 'MEDIUM') return '#E6A23C' // 中等重要性 - 黄色
  return '#409EFF' // 低重要性 - 蓝色
}

// 获取通知图标
const getNotificationIcon = (type) => {
  switch (type) {
    case 'match':
      return Link
    case 'system':
      return Bell
    case 'announcement':
      return Notification
    default:
      return Bell
  }
}

// 获取通知颜色
const getNotificationColor = (notification) => {
  if (notification.type === 'match') {
    return getSimilarityColor(notification.similarity)
  } else if (notification.type === 'announcement') {
    return getImportanceColor(notification.importance)
  } else {
    return '#409EFF' // 默认蓝色
  }
}

// 获取通知类型文本
const getNotificationTypeText = (type) => {
  switch (type) {
    case 'match':
      return '匹配'
    case 'system':
      return '系统'
    case 'announcement':
      return '公告'
    case 'AUDIT_APPROVED':
    case 'AUDIT_REJECTED':
      return '审核'
    case 'CLAIM_APPROVED':
    case 'CLAIM_REJECTED':
      return '认领'
    default:
      return '通知'
  }
}

// 获取匹配类型的标签类型
const getMatchTypeTagType = (matchType) => {
  if (!matchType) return 'info';

  const types = {
    'IMAGE_TO_IMAGE': 'success',
    'IMAGE_TO_TEXT': 'warning',
    'TEXT_TO_IMAGE': 'warning',
    'TEXT_TO_TEXT': 'primary'
  };
  return types[matchType] || 'info';
}

// 获取匹配类型的友好标签
const getMatchTypeLabel = (matchType) => {
  if (!matchType) return '综合匹配';

  const labels = {
    'IMAGE_TO_IMAGE': '外观匹配',
    'IMAGE_TO_TEXT': '图像-描述匹配',
    'TEXT_TO_IMAGE': '描述-图像匹配',
    'TEXT_TO_TEXT': '描述匹配'
  };
  return labels[matchType] || '综合匹配';
}

// 获取通知标签类型
const getNotificationTagType = (type) => {
  switch (type) {
    case 'match':
      return 'success'
    case 'system':
      return 'info'
    case 'announcement':
      return 'warning'
    case 'AUDIT_APPROVED':
      return 'success'
    case 'AUDIT_REJECTED':
      return 'danger'
    case 'CLAIM_APPROVED':
      return 'success'
    case 'CLAIM_REJECTED':
      return 'danger'
    default:
      return 'info'
  }
}

// 定时刷新通知
let refreshInterval = null

// 用于跟踪已处理的通知ID，防止重复显示
const processedNotifications = new Set()

// 处理WebSocket通知
const handleWebSocketNotification = (notification) => {
  console.log('🔔🔔🔔 收到WebSocket通知:', notification)

  // 如果没有通知数据，直接返回
  if (!notification) return

  // 检查通知是否有ID
  const notificationId = notification?.id || notification?.notificationId

  // 如果通知没有ID或者这个ID之前没有处理过
  if (!notificationId || !processedNotifications.has(notificationId)) {
    // 如果有ID，标记为已处理
    if (notificationId) {
      processedNotifications.add(notificationId)

      // 设置过期时间，5秒后从集合中移除，避免集合无限增长
      setTimeout(() => {
        processedNotifications.delete(notificationId)
      }, 5000)
    }

    // 使用Store处理新通知
    notificationStore.handleNewNotification(notification)

    // 显示通知提示
    ElMessage({
      message: notification.title || '收到新通知',
      type: 'success',
      duration: 3000
    })
  } else {
    console.log(`🔔🔔🔔 通知ID ${notificationId} 已处理，跳过重复显示`)
  }
}

onMounted(() => {
  console.log('🔔🔔🔔 UnifiedNotifications组件挂载')

  // 初始加载数据
  notificationStore.fetchAllNotifications()
  notificationStore.fetchAllUnreadCounts()

  // 每分钟刷新一次未读通知数量（使用Store的节流版本）
  refreshInterval = setInterval(() => {
    console.log('🔔🔔🔔 定时刷新通知数量')
    notificationStore.fetchAllUnreadCounts()
  }, 60000)

  // 只使用全局WebSocket通知事件监听
  window.addEventListener('websocket-notification', (event) => {
    console.log('🔔🔔🔔 收到全局WebSocket通知事件:', event.detail)
    handleWebSocketNotification(event.detail)
  })

  // 注册通知计数刷新事件监听
  window.addEventListener('refresh-notification-count', () => {
    console.log('🔔🔔🔔 收到通知计数刷新事件')
    notificationStore.fetchAllUnreadCounts()
  })

  // 注册通知WebSocket连接成功事件监听
  window.addEventListener('notification-websocket-connected', (event) => {
    console.log('🔔🔔🔔 通知WebSocket连接成功:', event.detail)
    // 连接成功后刷新通知
    notificationStore.fetchAllNotifications()
    notificationStore.fetchAllUnreadCounts()
  })

  // 注册通知订阅成功事件监听
  window.addEventListener('notification-subscribed', (event) => {
    console.log('🔔🔔🔔 通知订阅成功:', event.detail)
    // 订阅成功后刷新通知
    notificationStore.fetchAllNotifications()
    notificationStore.fetchAllUnreadCounts()
  })

  // 注册全局刷新通知事件
  window.addEventListener('global-refresh-notifications', () => {
    console.log('🔔🔔🔔 收到全局刷新通知事件')
    notificationStore.fetchAllNotifications()
    notificationStore.fetchAllUnreadCounts()
  })
})

onBeforeUnmount(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }

  // 由于我们使用了匿名函数作为事件处理器，无法直接移除
  // 这里只是为了代码完整性，实际上这些事件监听器会在组件销毁时自动被清理
  console.log('组件卸载，事件监听器将被清理')

  // 清空已处理通知集合
  processedNotifications.clear()
})
</script>

<style scoped>
.unified-notifications {
  display: inline-block;
}

.notification-badge {
  margin-right: 10px;
}

.notification-button {
  font-size: 18px;
  padding: 0.6rem;
  transition: all 0.3s;
  background-color: #f5f7fa;
  border: none;
  color: #606266;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.notification-button:hover {
  transform: scale(1.05);
  color: #409EFF;
  background-color: #ecf5ff;
  box-shadow: 0 3px 6px rgba(64, 158, 255, 0.15);
}

/* 通知面板头部 */
.notification-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 8px;
}

.notification-header-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification-header-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.notification-count {
  font-size: 13px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 2px 8px;
  border-radius: 10px;
}

.notification-header-actions {
  display: flex;
  align-items: center;
}

/* 通知选项卡 */
.notification-tabs {
  margin-bottom: 0;
}

.notification-tabs :deep(.el-tabs__header) {
  margin-bottom: 10px;
  padding: 0 16px;
}

.notification-tabs :deep(.el-tabs__nav) {
  border-radius: 4px;
}

.notification-tabs :deep(.el-tabs__item) {
  height: 32px;
  line-height: 32px;
  font-size: 13px;
}

/* 通知列表项 */
.notification-item {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #f0f9ff;
}

.notification-dot {
  position: absolute;
  top: 16px;
  left: 6px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #f56c6c;
}

.notification-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 6px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.notification-text {
  font-size: 13px;
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-tag {
  font-size: 11px;
  padding: 0 6px;
  height: 20px;
  line-height: 18px;
}

/* 空状态 */
.empty-notifications {
  padding: 20px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 加载状态 */
.notification-loading {
  padding: 16px;
}

/* 底部 */
.notification-footer {
  text-align: center;
  padding: 10px 0;
  border-top: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

/* 弹出层样式 */
:deep(.notification-popover) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .notification-header-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .notification-header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 匹配通知详情对话框样式 */
.match-notification-detail {
  padding: 0 10px;
}

/* 确保对话框显示在最顶层 */
:deep(.el-dialog) {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin-top: 0 !important;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

:deep(.el-dialog__wrapper) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  z-index: 9999 !important;
}

:deep(.v-modal) {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  background: #000;
  z-index: 9998 !important;
}

.notification-type {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.similarity-tag {
  margin-left: auto;
}

.notification-content {
  margin-bottom: 20px;
  white-space: pre-line;
  line-height: 1.6;
  color: #303133;
}

.similarity-radar {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.similarity-radar h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
  font-size: 16px;
}

.related-item {
  margin: 20px 0;
}

.item-info-content {
  margin-top: 10px;
}

.detail-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.notification-time {
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
}

.notification-time .label {
  font-weight: bold;
  margin-right: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
}
</style>
