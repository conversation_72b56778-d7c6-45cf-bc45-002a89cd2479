<template>
  <div class="similarity-radar">
    <div ref="radarChart" class="radar-chart"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted } from 'vue'
import * as echarts from 'echarts/core'
import { RadarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  RadarChart,
  CanvasRenderer
])

const props = defineProps({
  matchDetails: {
    type: Object,
    default: () => ({
      TEXT_TO_TEXT: 0,
      TEXT_TO_IMAGE: 0,
      IMAGE_TO_TEXT: 0,
      IMAGE_TO_IMAGE: 0
    })
  }
})

const radarChart = ref(null)
let chart = null

// 初始化图表
const initChart = () => {
  if (!radarChart.value) return
  
  // 创建图表实例
  chart = echarts.init(radarChart.value)
  
  // 更新图表
  updateChart()
  
  // 响应窗口大小变化
  const handleResize = () => {
    chart && chart.resize()
  }
  
  window.addEventListener('resize', handleResize)
  
  // 在组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    chart && chart.dispose()
  })
}

// 更新图表数据
const updateChart = () => {
  if (!chart) return
  
  // 获取相似度数据
  const textToText = props.matchDetails.TEXT_TO_TEXT || 0
  const textToImage = props.matchDetails.TEXT_TO_IMAGE || 0
  const imageToText = props.matchDetails.IMAGE_TO_TEXT || 0
  const imageToImage = props.matchDetails.IMAGE_TO_IMAGE || 0
  
  // 设置图表选项
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return '匹配度分析<br/>' + 
               '外观相似度: ' + (imageToImage * 100).toFixed(0) + '%<br/>' +
               '描述相似度: ' + (textToText * 100).toFixed(0) + '%<br/>' +
               '图像-描述相似度: ' + (imageToText * 100).toFixed(0) + '%<br/>' +
               '描述-图像相似度: ' + (textToImage * 100).toFixed(0) + '%'
      }
    },
    radar: {
      indicator: [
        { name: '外观相似度', max: 1 },
        { name: '描述相似度', max: 1 },
        { name: '图像-描述相似度', max: 1 },
        { name: '描述-图像相似度', max: 1 }
      ],
      radius: '65%',
      shape: 'circle',
      splitNumber: 5,
      axisName: {
        color: '#606266',
        fontSize: 12
      },
      splitArea: {
        areaStyle: {
          color: ['rgba(255, 255, 255, 0.5)'],
          shadowColor: 'rgba(0, 0, 0, 0.05)',
          shadowBlur: 10
        }
      }
    },
    series: [
      {
        name: '匹配度分析',
        type: 'radar',
        data: [
          {
            value: [
              imageToImage,
              textToText,
              imageToText,
              textToImage
            ],
            name: '相似度',
            areaStyle: {
              color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                {
                  color: 'rgba(64, 158, 255, 0.7)',
                  offset: 0
                },
                {
                  color: 'rgba(64, 158, 255, 0.3)',
                  offset: 1
                }
              ])
            },
            lineStyle: {
              width: 2,
              color: '#409EFF'
            },
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }
    ]
  }
  
  // 应用选项
  chart.setOption(option)
}

// 监听匹配详情变化
watch(() => props.matchDetails, () => {
  updateChart()
}, { deep: true })

// 组件挂载后初始化图表
onMounted(() => {
  initChart()
})
</script>

<style scoped>
.similarity-radar {
  width: 100%;
  height: 100%;
}

.radar-chart {
  width: 100%;
  height: 250px;
}
</style>
