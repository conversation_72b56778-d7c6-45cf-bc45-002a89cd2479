<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//MyBatis//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomato.lostfoundsystem.mapper.ItemAuditMapper">

    <!-- 插入拾物审核记录 -->
    <insert id="insertFoundItemAudit" parameterType="com.tomato.lostfoundsystem.entity.ItemAudit">
        INSERT INTO found_item_audit (item_id, audit_status, audit_time, auditor_id, remarks)
        VALUES (#{itemId}, #{auditStatus, typeHandler=org.apache.ibatis.type.EnumTypeHandler}, #{auditTime}, #{auditorId}, #{remarks})
    </insert>

<!-- 插入失物审核记录 -->
<insert id="insertLostItemAudit" parameterType="com.tomato.lostfoundsystem.entity.ItemAudit">
    INSERT INTO lost_item_audit (item_id, audit_status, audit_time, auditor_id, remarks)
    VALUES (#{itemId}, #{auditStatus}, #{auditTime}, #{auditorId}, #{remarks})
</insert>

</mapper>