package com.tomato.lostfoundsystem.service;

import java.util.List;

/**
 * CDN缓存服务接口
 * 用于管理CDN缓存刷新和预热
 */
public interface CDNCacheService {
    
    /**
     * 刷新单个URL的CDN缓存
     * 
     * @param url 需要刷新的URL
     * @return 是否成功提交刷新请求
     */
    boolean refreshUrl(String url);
    
    /**
     * 批量刷新URL的CDN缓存
     * 
     * @param urls 需要刷新的URL列表
     * @return 成功提交刷新请求的URL数量
     */
    int refreshUrls(List<String> urls);
    
    /**
     * 刷新单个目录的CDN缓存
     * 
     * @param directory 需要刷新的目录路径
     * @return 是否成功提交刷新请求
     */
    boolean refreshDirectory(String directory);
    
    /**
     * 预热单个URL的CDN缓存
     * 
     * @param url 需要预热的URL
     * @return 是否成功提交预热请求
     */
    boolean preloadUrl(String url);
    
    /**
     * 批量预热URL的CDN缓存
     * 
     * @param urls 需要预热的URL列表
     * @return 成功提交预热请求的URL数量
     */
    int preloadUrls(List<String> urls);
    
    /**
     * 查询CDN刷新配额和余量
     * 
     * @return 配额信息
     */
    CDNQuotaInfo getQuotaInfo();
    
    /**
     * CDN配额信息类
     */
    class CDNQuotaInfo {
        private int urlRefreshRemaining;      // URL刷新剩余配额
        private int dirRefreshRemaining;      // 目录刷新剩余配额
        private int preloadRemaining;         // 预热剩余配额
        private int urlRefreshQuota;          // URL刷新总配额
        private int dirRefreshQuota;          // 目录刷新总配额
        private int preloadQuota;             // 预热总配额
        
        public CDNQuotaInfo(int urlRefreshRemaining, int dirRefreshRemaining, int preloadRemaining,
                           int urlRefreshQuota, int dirRefreshQuota, int preloadQuota) {
            this.urlRefreshRemaining = urlRefreshRemaining;
            this.dirRefreshRemaining = dirRefreshRemaining;
            this.preloadRemaining = preloadRemaining;
            this.urlRefreshQuota = urlRefreshQuota;
            this.dirRefreshQuota = dirRefreshQuota;
            this.preloadQuota = preloadQuota;
        }
        
        // Getters and Setters
        public int getUrlRefreshRemaining() {
            return urlRefreshRemaining;
        }
        
        public void setUrlRefreshRemaining(int urlRefreshRemaining) {
            this.urlRefreshRemaining = urlRefreshRemaining;
        }
        
        public int getDirRefreshRemaining() {
            return dirRefreshRemaining;
        }
        
        public void setDirRefreshRemaining(int dirRefreshRemaining) {
            this.dirRefreshRemaining = dirRefreshRemaining;
        }
        
        public int getPreloadRemaining() {
            return preloadRemaining;
        }
        
        public void setPreloadRemaining(int preloadRemaining) {
            this.preloadRemaining = preloadRemaining;
        }
        
        public int getUrlRefreshQuota() {
            return urlRefreshQuota;
        }
        
        public void setUrlRefreshQuota(int urlRefreshQuota) {
            this.urlRefreshQuota = urlRefreshQuota;
        }
        
        public int getDirRefreshQuota() {
            return dirRefreshQuota;
        }
        
        public void setDirRefreshQuota(int dirRefreshQuota) {
            this.dirRefreshQuota = dirRefreshQuota;
        }
        
        public int getPreloadQuota() {
            return preloadQuota;
        }
        
        public void setPreloadQuota(int preloadQuota) {
            this.preloadQuota = preloadQuota;
        }
    }
}
