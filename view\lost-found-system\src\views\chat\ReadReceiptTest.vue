<template>
  <div class="read-receipt-test">
    <h1>已读回执测试</h1>
    
    <div class="status-section">
      <h2>WebSocket 状态</h2>
      <div class="status-info">
        <div>连接状态: <span :class="['status', connected ? 'connected' : 'disconnected']">{{ connected ? '已连接' : '未连接' }}</span></div>
        <div>初始化状态: <span :class="['status', initialized ? 'initialized' : 'not-initialized']">{{ initialized ? '已初始化' : '未初始化' }}</span></div>
      </div>
      
      <div class="button-group">
        <el-button type="primary" @click="initializeWebSocket" :loading="connecting">
          初始化 WebSocket
        </el-button>
        <el-button type="danger" @click="disconnectWebSocket" :disabled="!connected">
          断开 WebSocket
        </el-button>
        <el-button type="info" @click="checkStatus">
          检查状态
        </el-button>
      </div>
    </div>
    
    <div class="test-section">
      <h2>发送已读回执测试</h2>
      <div class="form-section">
        <el-form :model="readReceiptForm" label-width="120px">
          <el-form-item label="消息ID">
            <el-input v-model="readReceiptForm.messageId" placeholder="请输入消息ID" />
          </el-form-item>
          <el-form-item label="发送者ID">
            <el-input v-model="readReceiptForm.senderId" placeholder="请输入发送者ID" />
          </el-form-item>
          <el-form-item label="读者ID">
            <el-input v-model="readReceiptForm.readerId" placeholder="请输入读者ID（当前用户ID）" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="sendReadReceipt" :disabled="!canSendReceipt" :loading="sending">
              发送已读回执
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    
    <div class="test-section">
      <h2>测试结果</h2>
      <div class="result-box">
        <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'

// 状态
const connected = ref(false)
const initialized = ref(false)
const connecting = ref(false)
const sending = ref(false)
const testResult = ref(null)

// 已读回执表单
const readReceiptForm = ref({
  messageId: '',
  senderId: '',
  readerId: ''
})

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo || {})

// 是否可以发送已读回执
const canSendReceipt = computed(() => {
  if (!connected.value || !initialized.value) return false
  if (!readReceiptForm.value.messageId || !readReceiptForm.value.senderId || !readReceiptForm.value.readerId) return false
  return true
})

// 初始化WebSocket
const initializeWebSocket = async () => {
  try {
    connecting.value = true
    testResult.value = { action: '初始化WebSocket', status: 'pending' }
    
    // 导入WebSocket模块
    const { initializeWebSocketService, isWebSocketConnected } = await import('@/utils/websocket/index')
    
    // 初始化WebSocket
    const result = await initializeWebSocketService()
    
    // 更新状态
    initialized.value = result
    connected.value = await isWebSocketConnected()
    
    // 更新测试结果
    testResult.value = {
      action: '初始化WebSocket',
      status: result ? 'success' : 'error',
      result,
      connected: connected.value
    }
    
    if (result) {
      ElMessage.success('WebSocket初始化成功')
      
      // 自动填充当前用户ID
      if (userInfo.value && userInfo.value.id) {
        readReceiptForm.value.readerId = userInfo.value.id
      }
    } else {
      ElMessage.error('WebSocket初始化失败')
    }
  } catch (error) {
    console.error('初始化WebSocket失败:', error)
    testResult.value = {
      action: '初始化WebSocket',
      status: 'error',
      error: error.message
    }
    ElMessage.error('初始化WebSocket失败: ' + error.message)
  } finally {
    connecting.value = false
  }
}

// 断开WebSocket
const disconnectWebSocket = async () => {
  try {
    testResult.value = { action: '断开WebSocket', status: 'pending' }
    
    // 导入WebSocket模块
    const { disconnect } = await import('@/utils/websocket/index')
    
    // 断开WebSocket
    disconnect()
    
    // 更新状态
    initialized.value = false
    connected.value = false
    
    // 更新测试结果
    testResult.value = {
      action: '断开WebSocket',
      status: 'success'
    }
    
    ElMessage.success('WebSocket已断开')
  } catch (error) {
    console.error('断开WebSocket失败:', error)
    testResult.value = {
      action: '断开WebSocket',
      status: 'error',
      error: error.message
    }
    ElMessage.error('断开WebSocket失败: ' + error.message)
  }
}

// 检查状态
const checkStatus = async () => {
  try {
    testResult.value = { action: '检查状态', status: 'pending' }
    
    // 导入WebSocket模块
    const { isWebSocketConnected, getWebSocketState } = await import('@/utils/websocket/index')
    
    // 检查状态
    const isConnected = await isWebSocketConnected()
    const state = getWebSocketState()
    
    // 更新状态
    connected.value = isConnected
    initialized.value = isConnected
    
    // 更新测试结果
    testResult.value = {
      action: '检查状态',
      status: 'success',
      connected: isConnected,
      state
    }
    
    ElMessage.info(`WebSocket状态: ${isConnected ? '已连接' : '未连接'}, 状态: ${state}`)
  } catch (error) {
    console.error('检查状态失败:', error)
    testResult.value = {
      action: '检查状态',
      status: 'error',
      error: error.message
    }
    ElMessage.error('检查状态失败: ' + error.message)
  }
}

// 发送已读回执
const sendReadReceipt = async () => {
  if (!canSendReceipt.value) return
  
  try {
    sending.value = true
    testResult.value = { action: '发送已读回执', status: 'pending' }
    
    // 导入WebSocket模块
    const { sendReadReceipt } = await import('@/utils/websocket/index')
    
    // 发送已读回执
    const result = await sendReadReceipt(
      readReceiptForm.value.messageId,
      readReceiptForm.value.senderId,
      readReceiptForm.value.readerId
    )
    
    // 更新测试结果
    testResult.value = {
      action: '发送已读回执',
      status: result.code === 200 ? 'success' : 'error',
      result
    }
    
    if (result.code === 200) {
      ElMessage.success('已读回执发送成功')
    } else {
      ElMessage.error('已读回执发送失败: ' + result.message)
    }
  } catch (error) {
    console.error('发送已读回执失败:', error)
    testResult.value = {
      action: '发送已读回执',
      status: 'error',
      error: error.message
    }
    ElMessage.error('发送已读回执失败: ' + error.message)
  } finally {
    sending.value = false
  }
}

// 组件挂载时检查状态
onMounted(async () => {
  await checkStatus()
  
  // 自动填充当前用户ID
  if (userInfo.value && userInfo.value.id) {
    readReceiptForm.value.readerId = userInfo.value.id
  }
})
</script>

<style scoped>
.read-receipt-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  margin-bottom: 30px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.status-info {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.status {
  font-weight: bold;
}

.connected {
  color: #67c23a;
}

.disconnected {
  color: #f56c6c;
}

.initialized {
  color: #67c23a;
}

.not-initialized {
  color: #f56c6c;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.form-section {
  max-width: 600px;
}

.result-box {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow: auto;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
