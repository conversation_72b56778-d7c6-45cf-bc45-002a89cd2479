<template>
  <div class="chat-container">
    <!-- 左侧联系人列表 -->
    <div class="chat-sidebar">
      <ContactList
        :contacts="contacts"
        :current-contact="currentContact"
        :loading="contactsLoading"
        @select="handleSelectContact"
        @refresh="loadContacts"
      />
    </div>

    <!-- 右侧聊天区域 -->
    <div class="chat-main">
      <template v-if="currentContact">
        <!-- 聊天头部 -->
        <ChatHeader
          :contact="currentContact"
          :loading="loading"
          @refresh="handleRefreshChat"
          @diagnose="runWebSocketDiagnostics"
        />

        <!-- 聊天消息列表 -->
        <ChatMessageList
          ref="messageListRef"
          :messages="messages"
          :user="userInfo"
          :contact="currentContact"
          :loading="loading"
          :loading-more="loadingMore"
          :no-more-messages="noMoreMessages"
          @load-more="loadMoreMessages"
          @retry="handleResendMessage"
          @preview-image="handlePreviewImage"
        />

        <!-- 聊天输入框 -->
        <ChatInput
          v-model="inputMessage"
          :sending="sending"
          :disabled="!currentContact"
          @send="sendTextMessage"
          @image-select="handleImageSelect"
          @file-select="handleFileSelect"
        />
      </template>

      <!-- 未选择联系人时的占位内容 -->
      <div v-else class="chat-placeholder">
        <div class="placeholder-content">
          <img src="@/assets/chat-placeholder.svg" alt="选择联系人开始聊天" class="placeholder-image">
          <h3>选择一个联系人开始聊天</h3>
          <p>或者点击左侧的"+"按钮创建新的会话</p>

          <!-- 调试信息 -->
          <div class="debug-info" v-if="showDebugInfo">
            <h4>调试信息</h4>
            <p>联系人数量: {{ contacts.length }}</p>
            <p>当前联系人: {{ currentContact ? currentContact.name : '无' }}</p>
            <p>消息数量: {{ messages.length }}</p>
            <p>WebSocket状态: {{ wsStatus }}</p>
            <el-button @click="refreshDebugInfo" size="small">刷新</el-button>
          </div>
          <el-button @click="showDebugInfo = !showDebugInfo" size="small" style="margin-top: 20px;">
            {{ showDebugInfo ? '隐藏调试信息' : '显示调试信息' }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 文件上传对话框 -->
    <FileUploadDialog
      v-if="fileUploadVisible"
      v-model="inputMessage"
      :file="selectedFile"
      :visible="fileUploadVisible"
      :sending="sending"
      @close="fileUploadVisible = false"
      @send="sendFileMessage"
    />

    <!-- 图片上传对话框 -->
    <FileUploadDialog
      v-if="imageUploadVisible"
      v-model="inputMessage"
      :file="selectedImage"
      :visible="imageUploadVisible"
      :sending="sending"
      @close="imageUploadVisible = false"
      @send="sendImageMessage"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 导入组件
import ContactList from '@/components/chat/ContactList.vue'
import ChatHeader from '@/components/chat/ChatHeader.vue'
import ChatMessageList from '@/components/chat/ChatMessageList.vue'
import ChatInput from '@/components/chat/ChatInput.vue'
import FileUploadDialog from '@/components/chat/FileUploadDialog.vue'

// 导入组合式函数
import { useChat } from '@/composables/useChat'
import { useContacts } from '@/composables/useContacts'
import { useWebSocket } from '@/composables/useWebSocket'

// 导入工具函数
import logger from '@/utils/logger'

// 导入状态管理
import { useUserStore } from '@/stores'

// 创建聊天日志记录器
const chatLogger = logger.createLogger('ChatNew')

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo || {})

// 使用组合式函数
const {
  messages, currentContact, sending, loading, loadingMore, noMoreMessages,
  inputMessage, sendTextMessage, updateMessageStatus, scrollToBottom,
  loadChatHistory, formatMessage, messagesContainer
} = useChat()

const {
  contacts, contactsLoading, loadContacts, selectContact, openContactFromUrl,
  updateContactLastMessage
} = useContacts()

const {
  initializeWebSocket, diagnoseWebSocketIssues,
  sendChatMessage, sendReadReceipt, addEventListener, removeEventListener
} = useWebSocket()

// 引用
const messageListRef = ref(null)

// 消息容器引用会在组件挂载后设置

// 文件上传状态
const selectedFile = ref(null)
const selectedImage = ref(null)
const fileUploadVisible = ref(false)
const imageUploadVisible = ref(false)

// 调试状态
const showDebugInfo = ref(false)
const wsStatus = ref('未知')

// 刷新调试信息
const refreshDebugInfo = async () => {
  try {
    const { isWebSocketConnected } = await import('@/utils/websocket/index')
    const connected = await isWebSocketConnected()
    wsStatus.value = connected ? '已连接' : '未连接'
    chatLogger.info('WebSocket状态:', wsStatus.value)
    chatLogger.info('联系人数量:', contacts.value.length)
    chatLogger.info('当前联系人:', currentContact.value)
    chatLogger.info('消息数量:', messages.value.length)
  } catch (error) {
    chatLogger.error('获取WebSocket状态失败:', error)
    wsStatus.value = '检查失败'
  }
}

// 初始化WebSocket
const initChat = async () => {
  chatLogger.info('开始初始化聊天组件')
  chatLogger.info('当前用户信息:', userInfo.value)

  try {
    // 初始化WebSocket
    await initializeWebSocket()

    // 加载联系人列表
    await loadContacts()

    // 从URL参数获取要打开的联系人ID
    openContactFromUrl()

    chatLogger.info('聊天组件初始化完成')
  } catch (error) {
    chatLogger.error('初始化聊天组件失败:', error)
    ElMessage.error('聊天组件初始化失败，请刷新页面重试')
  }
}

// 加载更多消息
const loadMoreMessages = async () => {
  if (loadingMore.value || !currentContact.value) return

  try {
    // 调用组合式函数中的加载更多消息方法
    await loadChatHistory(currentContact.value.id, { loadMore: true })
  } catch (error) {
    chatLogger.error('加载更多消息失败:', error)
    ElMessage.error('加载更多消息失败')
  }
}

// 处理选择联系人
const handleSelectContact = async (contact) => {
  if (!contact) return

  // 如果已经选中该联系人，不做任何操作
  if (currentContact.value && String(currentContact.value.id) === String(contact.id)) {
    return
  }

  chatLogger.info('选择联系人:', contact)

  try {
    // 更新当前联系人
    selectContact(contact)

    // 清空消息列表
    messages.value = []

    // 加载聊天历史
    await loadChatHistory(contact.id)

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    // 刷新调试信息
    refreshDebugInfo()
  } catch (error) {
    chatLogger.error('选择联系人失败:', error)
    ElMessage.error('加载聊天历史失败: ' + error.message)
  }
}

// 处理刷新聊天
const handleRefreshChat = async () => {
  if (!currentContact.value) return

  // 重新加载联系人列表
  await loadContacts()

  // 重新加载聊天历史
  await loadChatHistory(currentContact.value.id)

  // 滚动到底部
  await nextTick()
  scrollToBottom()
}

// 处理图片选择
const handleImageSelect = (event) => {
  const files = event.target.files
  if (!files || !files.length) return

  const file = files[0]
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  selectedImage.value = file
  imageUploadVisible.value = true
}

// 处理文件选择
const handleFileSelect = (event) => {
  const files = event.target.files
  if (!files || !files.length) return

  const file = files[0]
  selectedFile.value = file
  fileUploadVisible.value = true
}

// 发送图片消息
const sendImageMessage = async (data) => {
  if (!currentContact.value || !selectedImage.value) return

  try {
    sending.value = true

    // 创建临时消息ID
    const tempId = `temp-${Date.now()}`

    // 添加临时消息到聊天窗口
    messages.value.push({
      id: tempId,
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      content: data.message || '图片',
      messageType: 'IMAGE',
      time: new Date().toISOString(),
      isSelf: true,
      status: 'SENDING',
      isRead: false
    })

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    // 准备消息数据
    const messageDTO = {
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      message: data.message || '图片',
      messageType: 'IMAGE',
      clientMessageId: tempId
    }

    // 发送消息
    const result = await sendChatMessage(messageDTO, selectedImage.value)

    if (result && result.code === 200) {
      // 更新消息状态
      updateMessageStatus(tempId, {
        id: result.data?.id || tempId,
        time: result.data?.timestamp || new Date().toISOString(),
        fileUrl: result.data?.fileUrl || null,
        status: 'SENT'
      })

      // 更新联系人最新消息
      updateContactLastMessage({
        ...messageDTO,
        id: result.data?.id || tempId,
        content: data.message || '图片',
        time: result.data?.timestamp || new Date().toISOString()
      })

      // 关闭对话框
      imageUploadVisible.value = false
      selectedImage.value = null
    } else {
      // 更新消息状态为发送失败
      updateMessageStatus(tempId, {
        errorMessage: result?.message || '发送失败',
        status: 'ERROR'
      })

      ElMessage.error('发送图片失败: ' + (result?.message || '未知错误'))
    }
  } catch (error) {
    chatLogger.error('发送图片失败:', error)
    ElMessage.error('发送图片失败: ' + error.message)
  } finally {
    sending.value = false
  }
}

// 发送文件消息
const sendFileMessage = async (data) => {
  if (!currentContact.value || !selectedFile.value) return

  try {
    sending.value = true

    // 创建临时消息ID
    const tempId = `temp-${Date.now()}`

    // 添加临时消息到聊天窗口
    messages.value.push({
      id: tempId,
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      content: selectedFile.value.name,
      messageType: 'DOCUMENT',
      time: new Date().toISOString(),
      isSelf: true,
      status: 'SENDING',
      isRead: false,
      fileSize: selectedFile.value.size
    })

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    // 准备消息数据
    const messageDTO = {
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      message: data.message || selectedFile.value.name,
      messageType: 'DOCUMENT',
      clientMessageId: tempId
    }

    // 发送消息
    const result = await sendChatMessage(messageDTO, selectedFile.value)

    if (result && result.code === 200) {
      // 更新消息状态
      updateMessageStatus(tempId, {
        id: result.data?.id || tempId,
        time: result.data?.timestamp || new Date().toISOString(),
        fileUrl: result.data?.fileUrl || null,
        status: 'SENT'
      })

      // 更新联系人最新消息
      updateContactLastMessage({
        ...messageDTO,
        id: result.data?.id || tempId,
        content: selectedFile.value.name,
        time: result.data?.timestamp || new Date().toISOString()
      })

      // 关闭对话框
      fileUploadVisible.value = false
      selectedFile.value = null
    } else {
      // 更新消息状态为发送失败
      updateMessageStatus(tempId, {
        errorMessage: result?.message || '发送失败',
        status: 'ERROR'
      })

      ElMessage.error('发送文件失败: ' + (result?.message || '未知错误'))
    }
  } catch (error) {
    chatLogger.error('发送文件失败:', error)
    ElMessage.error('发送文件失败: ' + error.message)
  } finally {
    sending.value = false
  }
}

// 处理重发消息
const handleResendMessage = async (messageId) => {
  const message = messages.value.find(m => m.id === messageId)
  if (!message) return

  // 确认重发
  try {
    await ElMessageBox.confirm('确定要重新发送此消息吗？', '重发消息', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 更新消息状态为发送中
    updateMessageStatus(messageId, {
      status: 'SENDING'
    })

    // 准备消息数据
    const messageDTO = {
      senderId: message.senderId || userInfo.value.id,
      receiverId: message.receiverId || currentContact.value.id,
      message: message.content,
      messageType: message.messageType || 'TEXT',
      clientMessageId: messageId
    }

    // 发送消息
    const result = await sendChatMessage(messageDTO)

    if (result && result.code === 200) {
      // 更新消息状态
      updateMessageStatus(messageId, {
        id: result.data?.id || messageId,
        time: result.data?.timestamp || new Date().toISOString(),
        status: 'SENT'
      })

      ElMessage.success('消息已重新发送')
    } else {
      // 更新消息状态为发送失败
      updateMessageStatus(messageId, {
        errorMessage: result?.message || '发送失败',
        status: 'ERROR'
      })

      ElMessage.error('重发消息失败: ' + (result?.message || '未知错误'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      chatLogger.error('重发消息失败:', error)
      ElMessage.error('重发消息失败: ' + error.message)
    }
  }
}

// 处理预览图片
const handlePreviewImage = (url) => {
  if (!url) return

  // 使用Element Plus的图片预览功能
  const imgElement = document.createElement('img')
  imgElement.src = url
  imgElement.style.display = 'none'
  document.body.appendChild(imgElement)

  // 创建图片预览
  const previewInstance = ElMessageBox.alert('', {
    title: '图片预览',
    dangerouslyUseHTMLString: true,
    message: `<img src="${url}" style="max-width: 100%; max-height: 70vh;" />`,
    showConfirmButton: false,
    callback: () => {
      document.body.removeChild(imgElement)
    }
  })

  return previewInstance
}

// 运行WebSocket诊断
const runWebSocketDiagnostics = async () => {
  try {
    const result = await diagnoseWebSocketIssues()
    ElMessageBox.alert(result.join('<br>'), 'WebSocket诊断结果', {
      dangerouslyUseHTMLString: true
    })
  } catch (error) {
    chatLogger.error('WebSocket诊断失败:', error)
    ElMessage.error('WebSocket诊断失败')
  }
}

// 组件挂载时初始化
onMounted(() => {
  initChat()

  // 添加WebSocket事件监听
  addEventListener('chat-message', handleChatMessage)
  addEventListener('message-sent', handleMessageSent)

  // 设置消息容器引用
  nextTick(() => {
    if (messageListRef.value) {
      messagesContainer.value = messageListRef.value
      console.log('消息容器引用已设置:', messageListRef.value)

      // 监听消息列表变化，自动滚动到底部
      watch(messages, () => {
        nextTick(() => scrollToBottom())
      }, { deep: true })
    } else {
      console.warn('消息容器引用未找到')
    }

    // 刷新调试信息
    refreshDebugInfo()
  })
})

// 组件卸载时清理
onUnmounted(() => {
  // 移除WebSocket事件监听
  removeEventListener('chat-message', handleChatMessage)
  removeEventListener('message-sent', handleMessageSent)
})

// 处理接收到的聊天消息
const handleChatMessage = (event) => {
  try {
    chatLogger.info('处理实时聊天消息:', event.detail);
    
    const message = event.detail;
    if (!message || !message.receiverId || !message.senderId) {
      chatLogger.error('无效的消息格式:', message);
      return;
    }

    // 确保ID是字符串类型，便于比较
    const messageReceiverId = String(message.receiverId);
    const messageSenderId = String(message.senderId);
    const currentUserId = String(userInfo.value.id);

    // 判断消息是发给当前用户的还是当前用户发出的
    const isSelfMessage = messageSenderId === currentUserId;
    const isReceivedMessage = messageReceiverId === currentUserId;

    chatLogger.info('消息分析 - 发送者:', messageSenderId, '接收者:', messageReceiverId,
                   '当前用户:', currentUserId, '是自己发送的:', isSelfMessage,
                   '是接收到的:', isReceivedMessage);

    // 检查消息是否与当前用户相关
    if (!isSelfMessage && !isReceivedMessage) {
      chatLogger.warn('消息与当前用户无关，忽略消息');
      return;
    }

    // 格式化消息
    const formattedMessage = formatMessage(message);

    // 检查是否是重复消息
    if (isMessageDuplicate(formattedMessage, messages.value)) {
      chatLogger.info('忽略重复消息:', formattedMessage.id);
      return;
    }

    // 如果是当前联系人的消息，添加到消息列表
    if (currentContact.value &&
        (String(message.senderId) === String(currentContact.value.id) ||
         String(message.receiverId) === String(currentContact.value.id))) {

      // 添加到消息列表
      messages.value.push(formattedMessage);

      // 滚动到底部
      nextTick(() => scrollToBottom());

      // 如果是接收到的消息，发送已读回执
      if (!formattedMessage.isSelf) {
        // 发送已读回执
        sendReadReceipt(formattedMessage.id, formattedMessage.senderId, userInfo.value.id);

        // 播放通知声音
        playNotificationSound();
      }
    } else {
      chatLogger.info('消息不属于当前聊天，不添加到聊天窗口');
      
      // 如果不是当前聊天且不是自己发送的消息，播放通知声音
      if (!formattedMessage.isSelf) {
        playNotificationSound();
      }
    }

    // 更新联系人最新消息
    updateContactLastMessage(formattedMessage);
  } catch (error) {
    chatLogger.error('处理聊天消息错误:', error);
    console.error('错误详情:', error);
  }
};

// 处理消息发送确认
const handleMessageSent = (event) => {
  const message = event.detail

  // 查找临时消息并更新状态
  const tempMessage = messages.value.find(m =>
    m.clientMessageId === message.clientMessageId ||
    m.id === message.clientMessageId
  )

  if (tempMessage) {
    updateMessageStatus(tempMessage.id, {
      id: message.id,
      time: message.timestamp,
      status: 'SENT'
    })
  }
}

// 格式化消息
const formatMessage = (message) => {
  try {
    // 基本消息格式验证
    if (!message) {
      chatLogger.warn('formatMessage: 消息对象为空');
      return {
        id: `temp-${Date.now()}`,
        content: '[无效消息]',
        time: new Date().toISOString(),
        isSelf: false,
        messageType: 'TEXT',
        status: 'ERROR'
      };
    }

    // 处理时间戳格式
    let formattedTime = message.time || message.timestamp || new Date().toISOString();

    // 检查时间是否为字符串格式，例如: "Mon Apr 28 22:51:54 CST 2025"
    if (typeof formattedTime === 'string' && formattedTime.includes('CST')) {
      try {
        // 解析非标准时间格式
        const date = new Date(formattedTime);
        if (!isNaN(date.getTime())) {
          formattedTime = date.toISOString();
        }
      } catch (timeError) {
        chatLogger.warn('时间戳格式解析失败:', timeError);
      }
    }

    // 确保消息有所有必要的属性
    return {
      ...message,
      id: message.id || message.messageId || `temp-${Date.now()}`,
      content: message.content || message.message || '[空消息]',
      senderId: message.senderId || message.sender || userInfo.value.id,
      receiverId: message.receiverId || message.receiver || currentContact.value?.id,
      messageType: message.messageType || 'TEXT',
      time: formattedTime,
      isSelf: message.isSelf !== undefined ? message.isSelf :
              (String(message.senderId) === String(userInfo.value.id) || String(message.sender) === String(userInfo.value.id)),
      status: message.status || (message.isRead ? 'READ' : 'SENT'),
      clientMessageId: message.clientMessageId,
      conversationId: message.conversationId
    };
  } catch (error) {
    console.error('格式化消息时出错:', error);
    // 返回安全的默认消息
    return {
      id: `error-${Date.now()}`,
      content: '[消息格式错误]',
      time: new Date().toISOString(),
      isSelf: false,
      messageType: 'TEXT'
    };
  }
};

// 增强的消息重复检测函数
const isMessageDuplicate = (newMsg, existingMsgs) => {
  if (!newMsg || !existingMsgs || !Array.isArray(existingMsgs)) {
    return false;
  }

  return existingMsgs.some(m => {
    // 1. 精确匹配ID (非临时ID)
    if (m.id === newMsg.id && !m.id.toString().startsWith('temp-') && !m.id.toString().startsWith('error-')) {
      chatLogger.info('消息重复检测: ID精确匹配', m.id);
      return true;
    }

    // 2. 客户端消息ID匹配
    if (m.clientMessageId && newMsg.clientMessageId && m.clientMessageId === newMsg.clientMessageId) {
      chatLogger.info('消息重复检测: 客户端消息ID匹配', m.clientMessageId);
      return true;
    }

    // 3. 临时ID匹配（处理发送中的消息）
    if (m.id.toString().startsWith('temp-') && newMsg.id.toString().startsWith('temp-') &&
        m.content === newMsg.content &&
        String(m.senderId) === String(newMsg.senderId) &&
        Math.abs(new Date(m.time) - new Date(newMsg.time)) < 2000) {
      chatLogger.info('消息重复检测: 临时ID匹配', m.id, newMsg.id);
      return true;
    }

    // 4. 内容+发送者+接收者+时间匹配（处理没有ID或ID不同的消息）
    if (m.content === newMsg.content &&
        String(m.senderId) === String(newMsg.senderId) &&
        String(m.receiverId) === String(newMsg.receiverId) &&
        Math.abs(new Date(m.time) - new Date(newMsg.time)) < 5000) {
      chatLogger.info('消息重复检测: 内容+发送者+接收者+时间匹配');
      return true;
    }

    return false;
  });
};

// 播放通知声音
const playNotificationSound = () => {
  // 实现播放通知声音的逻辑
  console.log('播放通知声音');
};

</script>

<style scoped>
.chat-container {
  display: flex;
  height: calc(100vh - 60px);
  background-color: #f0f2f5;
  overflow: hidden;
}

.chat-sidebar {
  width: 320px;
  min-width: 280px;
  max-width: 420px;
  height: 100%;
  border-right: 1px solid #e9edef;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  resize: horizontal;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #efeae2;
  background-image: linear-gradient(rgba(0, 0, 0, 0.02) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
  background-size: 20px 20px;
  position: relative;
  overflow: hidden;
}

.chat-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f2f5;
}

.placeholder-content {
  text-align: center;
  padding: 20px;
  max-width: 400px;
}

.placeholder-image {
  width: 200px;
  height: 200px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.placeholder-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: #41464b;
  margin-bottom: 10px;
}

.placeholder-content p {
  font-size: 14px;
  color: #6c757d;
}

.debug-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #dee2e6;
  text-align: left;
}

.debug-info h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #495057;
}

.debug-info p {
  margin: 5px 0;
  font-family: monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-sidebar {
    width: 100%;
    max-width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .chat-sidebar.active {
    transform: translateX(0);
  }

  .chat-main {
    width: 100%;
  }

  .placeholder-image {
    width: 150px;
    height: 150px;
  }
}
</style>
