package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.entity.FoundItem;
import com.tomato.lostfoundsystem.entity.ItemFeatureVector;
import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.mapper.FoundItemMapper;
import com.tomato.lostfoundsystem.mapper.ItemFeatureVectorMapper;
import com.tomato.lostfoundsystem.mapper.LostItemMapper;
import com.tomato.lostfoundsystem.service.FeatureExtractionService;
import com.tomato.lostfoundsystem.utils.ClipFaissClientRefactored;
import com.tomato.lostfoundsystem.utils.KeywordExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 特征提取服务实现类
 */
@Slf4j
@Service
public class FeatureExtractionServiceImpl implements FeatureExtractionService {

    private static final String VECTOR_VERSION = "v1";

    @Autowired
    private ClipFaissClientRefactored clipFaissClient;

    @Autowired
    private ItemFeatureVectorMapper itemFeatureVectorMapper;

    @Autowired
    private LostItemMapper lostItemMapper;

    @Autowired
    private FoundItemMapper foundItemMapper;

    @Override
    @Transactional
    public boolean extractAndStoreFeatures(Long itemId, String itemType) {
        try {
            log.info("【特征提取】开始提取并存储特征向量: 物品ID={}, 类型={}", itemId, itemType);

            // 获取物品信息
            String itemName = null;
            String description = null;
            String imageUrl = null;

            if ("LOST".equals(itemType)) {
                LostItem lostItem = lostItemMapper.selectById(itemId);
                if (lostItem == null) {
                    log.error("【特征提取】失物信息不存在: {}", itemId);
                    return false;
                }
                itemName = lostItem.getItemName();
                description = lostItem.getDescription();
                imageUrl = lostItem.getImageUrl();
                log.info("【特征提取】获取到失物信息 - ID: {}, 名称: {}, 图片URL: {}",
                        itemId, itemName, imageUrl);
            } else if ("FOUND".equals(itemType)) {
                FoundItem foundItem = foundItemMapper.selectById(itemId);
                if (foundItem == null) {
                    log.error("【特征提取】拾物信息不存在: {}", itemId);
                    return false;
                }
                itemName = foundItem.getItemName();
                description = foundItem.getDescription();
                imageUrl = foundItem.getImageUrl();
                log.info("【特征提取】获取到拾物信息 - ID: {}, 名称: {}, 图片URL: {}",
                        itemId, itemName, imageUrl);
            } else {
                log.error("【特征提取】无效的物品类型: {}", itemType);
                return false;
            }

            // 使用HanLP提取关键词
            String keywords = KeywordExtractor.extractKeywords(itemName, description);
            log.info("【特征提取】从描述中提取的关键词: {}", keywords);

            // 提取文本特征向量
            byte[] textFeatures = null;
            if (keywords != null && !keywords.isEmpty()) {
                log.info("【特征提取】开始提取文本特征向量 - 物品ID: {}, 关键词: {}", itemId, keywords);
                long startTime = System.currentTimeMillis();
                textFeatures = clipFaissClient.extractTextFeatures(keywords);
                long endTime = System.currentTimeMillis();

                if (textFeatures != null) {
                    log.info("【特征提取】成功提取文本特征向量 - 物品ID: {}, 向量大小: {} 字节, 耗时: {} 毫秒",
                            itemId, textFeatures.length, (endTime - startTime));
                } else {
                    log.error("【特征提取】提取文本特征向量失败 - 物品ID: {}, 耗时: {} 毫秒",
                            itemId, (endTime - startTime));
                }
            } else {
                log.warn("【特征提取】提取的关键词为空，跳过文本特征提取 - 物品ID: {}", itemId);
            }

            // 提取图像特征向量
            byte[] imageFeatures = null;
            if (imageUrl != null && !imageUrl.isEmpty()) {
                log.info("【特征提取】开始提取图像特征向量 - 物品ID: {}, 图片URL: {}", itemId, imageUrl);
                long startTime = System.currentTimeMillis();
                imageFeatures = clipFaissClient.extractImageFeaturesFromUrl(imageUrl);
                long endTime = System.currentTimeMillis();

                if (imageFeatures != null) {
                    log.info("【特征提取】成功提取图像特征向量 - 物品ID: {}, 向量大小: {} 字节, 耗时: {} 毫秒",
                            itemId, imageFeatures.length, (endTime - startTime));
                } else {
                    log.error("【特征提取】提取图像特征向量失败 - 物品ID: {}, 耗时: {} 毫秒",
                            itemId, (endTime - startTime));
                }
            } else {
                log.warn("【特征提取】物品图片URL为空，跳过图像特征提取 - 物品ID: {}", itemId);
            }

            // 如果两种特征都提取失败，则返回失败
            if (textFeatures == null && imageFeatures == null) {
                log.error("【特征提取】文本和图像特征向量均提取失败 - 物品ID: {}", itemId);
                return false;
            }

            // 存储特征向量到数据库
            boolean dbResult = storeFeatureVectors(itemId, itemType, textFeatures, imageFeatures);
            if (!dbResult) {
                log.error("【特征提取】存储特征向量到数据库失败: {}", itemId);
                return false;
            }

            // 添加特征向量到索引
            boolean indexResult = addVectorsToIndex(itemId, itemType, textFeatures, imageFeatures);
            if (!indexResult) {
                log.error("【特征提取】添加特征向量到索引失败: {}", itemId);
                return false;
            }

            log.info("【特征提取】成功提取并存储特征向量: 物品ID={}, 类型={}", itemId, itemType);
            return true;
        } catch (Exception e) {
            log.error("【特征提取】提取并存储特征向量时发生异常", e);
            return false;
        }
    }

    /**
     * 存储特征向量到数据库
     */
    private boolean storeFeatureVectors(Long itemId, String itemType, byte[] textFeatures, byte[] imageFeatures) {
        try {
            log.info("【特征提取】开始存储特征向量到数据库 - 物品ID: {}, 类型: {}", itemId, itemType);

            // 检查是否已存在特征向量
            int exists = itemFeatureVectorMapper.checkFeatureVectorExists(itemId, itemType);
            log.info("【特征提取】检查特征向量是否存在 - 物品ID: {}, 类型: {}, 结果: {}",
                    itemId, itemType, exists > 0 ? "已存在" : "不存在");

            ItemFeatureVector vector = new ItemFeatureVector();
            vector.setItemId(itemId);
            vector.setItemType(itemType);
            vector.setTextVector(textFeatures);
            vector.setImageVector(imageFeatures);
            vector.setVectorVersion(VECTOR_VERSION);

            // 记录向量信息
            String textVectorInfo = textFeatures != null ? textFeatures.length + " 字节" : "null";
            String imageVectorInfo = imageFeatures != null ? imageFeatures.length + " 字节" : "null";
            log.info("【特征提取】准备存储特征向量 - 物品ID: {}, 类型: {}, 文本向量: {}, 图像向量: {}, 版本: {}",
                    itemId, itemType, textVectorInfo, imageVectorInfo, VECTOR_VERSION);

            int result;
            long startTime = System.currentTimeMillis();
            if (exists > 0) {
                // 更新现有特征向量
                log.info("【特征提取】更新现有特征向量 - 物品ID: {}, 类型: {}", itemId, itemType);
                result = itemFeatureVectorMapper.updateFeatureVector(vector);
            } else {
                // 插入新特征向量
                log.info("【特征提取】插入新特征向量 - 物品ID: {}, 类型: {}", itemId, itemType);
                result = itemFeatureVectorMapper.insertFeatureVector(vector);
            }
            long endTime = System.currentTimeMillis();

            if (result > 0) {
                log.info("【特征提取】成功存储特征向量到数据库 - 物品ID: {}, 类型: {}, 耗时: {} 毫秒",
                        itemId, itemType, (endTime - startTime));
            } else {
                log.error("【特征提取】存储特征向量到数据库失败 - 物品ID: {}, 类型: {}, 耗时: {} 毫秒",
                        itemId, itemType, (endTime - startTime));
            }

            return result > 0;
        } catch (Exception e) {
            log.error("【特征提取】存储特征向量到数据库时发生异常 - 物品ID: {}, 类型: {}", itemId, itemType, e);
            return false;
        }
    }

    /**
     * 添加特征向量到索引
     */
    private boolean addVectorsToIndex(Long itemId, String itemType, byte[] textFeatures, byte[] imageFeatures) {
        boolean success = true;

        try {
            log.info("【特征提取】开始添加特征向量到索引 - 物品ID: {}, 类型: {}", itemId, itemType);

            // 先从索引中删除该物品的所有向量，避免重复
            log.info("【特征提取】从索引中删除现有向量 - 物品ID: {}, 类型: {}", itemId, itemType);
            long startRemove = System.currentTimeMillis();
            boolean removed = clipFaissClient.removeVectorFromIndex(itemId, itemType, null);
            long endRemove = System.currentTimeMillis();
            log.info("【特征提取】从索引中删除向量 - 物品ID: {}, 类型: {}, 结果: {}, 耗时: {} 毫秒",
                    itemId, itemType, removed ? "成功" : "失败", (endRemove - startRemove));

            // 添加文本特征向量到索引
            if (textFeatures != null) {
                log.info("【特征提取】开始添加文本特征向量到索引 - 物品ID: {}, 类型: {}, 向量大小: {} 字节",
                        itemId, itemType, textFeatures.length);
                long startAddText = System.currentTimeMillis();
                Long textVectorId = clipFaissClient.addVectorToIndex(itemId, itemType, textFeatures, "TEXT");
                long endAddText = System.currentTimeMillis();

                if (textVectorId != null) {
                    log.info("【特征提取】成功添加文本特征向量到索引 - 物品ID: {}, 类型: {}, 向量ID: {}, 耗时: {} 毫秒",
                            itemId, itemType, textVectorId, (endAddText - startAddText));
                    success = true;
                } else {
                    log.error("【特征提取】添加文本特征向量到索引失败 - 物品ID: {}, 类型: {}, 耗时: {} 毫秒",
                            itemId, itemType, (endAddText - startAddText));
                    success = false;
                }
            } else {
                log.warn("【特征提取】文本特征向量为空，跳过添加到索引 - 物品ID: {}, 类型: {}", itemId, itemType);
            }

            // 添加图像特征向量到索引
            if (imageFeatures != null) {
                log.info("【特征提取】开始添加图像特征向量到索引 - 物品ID: {}, 类型: {}, 向量大小: {} 字节",
                        itemId, itemType, imageFeatures.length);
                long startAddImage = System.currentTimeMillis();
                Long imageVectorId = clipFaissClient.addVectorToIndex(itemId, itemType, imageFeatures, "IMAGE");
                long endAddImage = System.currentTimeMillis();

                if (imageVectorId != null) {
                    log.info("【特征提取】成功添加图像特征向量到索引 - 物品ID: {}, 类型: {}, 向量ID: {}, 耗时: {} 毫秒",
                            itemId, itemType, imageVectorId, (endAddImage - startAddImage));
                    success = success && true;
                } else {
                    log.error("【特征提取】添加图像特征向量到索引失败 - 物品ID: {}, 类型: {}, 耗时: {} 毫秒",
                            itemId, itemType, (endAddImage - startAddImage));
                    success = false;
                }
            } else {
                log.warn("【特征提取】图像特征向量为空，跳过添加到索引 - 物品ID: {}, 类型: {}", itemId, itemType);
            }

            // 保存索引
            if (success) {
                log.info("【特征提取】开始保存索引 - 物品ID: {}, 类型: {}", itemId, itemType);
                long startSave = System.currentTimeMillis();
                boolean saveResult = clipFaissClient.saveIndices();
                long endSave = System.currentTimeMillis();

                if (saveResult) {
                    log.info("【特征提取】成功保存索引 - 物品ID: {}, 类型: {}, 耗时: {} 毫秒",
                            itemId, itemType, (endSave - startSave));
                } else {
                    log.error("【特征提取】保存索引失败 - 物品ID: {}, 类型: {}, 耗时: {} 毫秒",
                            itemId, itemType, (endSave - startSave));
                }

                success = saveResult;
            } else {
                log.warn("【特征提取】添加向量失败，跳过保存索引 - 物品ID: {}, 类型: {}", itemId, itemType);
            }

            if (success) {
                log.info("【特征提取】成功完成添加特征向量到索引 - 物品ID: {}, 类型: {}", itemId, itemType);
            } else {
                log.error("【特征提取】添加特征向量到索引失败 - 物品ID: {}, 类型: {}", itemId, itemType);
            }

            return success;
        } catch (Exception e) {
            log.error("【特征提取】添加特征向量到索引时发生异常 - 物品ID: {}, 类型: {}", itemId, itemType, e);
            return false;
        }
    }
}
