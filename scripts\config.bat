@echo off
:: ===================================
:: 失物招领系统服务配置文件
:: ===================================
::
:: 重要提示：此配置文件中的设置应与数据库中的system_config表保持一致
:: 特别是智能匹配服务的路径、端口等参数应与后台管理界面中的配置一致
:: 否则可能导致后台管理界面无法正确检测和控制服务
:: ===================================

:: Redis配置
set REDIS_PATH=D:\redis
set REDIS_CONFIG=redis.windows.conf
set REDIS_PORT=6379

:: Kafka配置
set KAFKA_PATH=D:\kafka
set ZOOKEEPER_CONFIG=config\zookeeper.properties
set KAFKA_CONFIG=config\server.properties
set ZOOKEEPER_PORT=2181
set KAFKA_PORT=9092

:: 智能匹配服务配置
:: 注意：以下配置必须与数据库中的system_config表中的配置保持一致
:: 对应的数据库配置项：
:: - autodl.clip.api.url (应为 http://服务器IP:CLIP_API_PORT)
:: - autodl.clip.service.script-path (应包含CLIP_SERVICE_PATH和CLIP_API_SCRIPT)
set CLIP_SERVICE_PATH=D:\clip_faiss_service
set CLIP_API_SCRIPT=clip_faiss_api.py
set CLIP_ENV_PATH=clip_faiss_env
set CLIP_API_PORT=8000

:: 系统配置
set SYSTEM_NAME=失物招领系统
set LOG_PATH=logs
set WAIT_TIME=10

:: 不要修改以下内容
if not exist "%LOG_PATH%" mkdir "%LOG_PATH%"
