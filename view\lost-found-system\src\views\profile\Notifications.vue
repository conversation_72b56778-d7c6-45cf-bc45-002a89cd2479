<template>
  <div class="notifications-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h2 class="title">通知中心</h2>
          <div class="header-actions">
            <el-dropdown @command="handleBatchCommand" split-button type="primary" size="small">
              批量操作
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="markAllAsRead" :disabled="!unreadCount || loading">
                    <el-icon><Check /></el-icon> 全部标记为已读
                  </el-dropdown-item>
                  <el-dropdown-item command="markSelectedAsRead" :disabled="!selectedUnreadCount || loading">
                    <el-icon><Check /></el-icon> 标记选中为已读 ({{ selectedUnreadCount }})
                  </el-dropdown-item>
                  <el-dropdown-item divided command="deleteSelected" :disabled="!selectedNotifications.length || loading">
                    <el-icon><Delete /></el-icon> 删除选中 ({{ selectedNotifications.length }})
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-button
              type="primary"
              plain
              @click="refreshNotifications"
              :loading="loading"
              size="small"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 状态标签页 -->
      <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="custom-tabs">
        <el-tab-pane name="all">
          <template #label>
            全部
          </template>
        </el-tab-pane>
        <el-tab-pane name="unread">
          <template #label>
            <el-badge :value="unreadCount" :hidden="unreadCount === 0">未读</el-badge>
          </template>
        </el-tab-pane>
        <el-tab-pane label="已读" name="read" />
      </el-tabs>

      <!-- 类型标签页 -->
      <el-tabs v-model="activeType" @tab-click="handleTypeClick" class="custom-tabs type-tabs">
        <el-tab-pane name="all">
          <template #label>
            所有类型
          </template>
        </el-tab-pane>
        <el-tab-pane name="audit">
          <template #label>
            审核通知
          </template>
        </el-tab-pane>
        <el-tab-pane name="match">
          <template #label>
            匹配通知
          </template>
        </el-tab-pane>
        <el-tab-pane name="claim">
          <template #label>
            认领通知
          </template>
        </el-tab-pane>
        <el-tab-pane name="system">
          <template #label>
            系统公告
          </template>
        </el-tab-pane>
      </el-tabs>

      <!-- 提示信息 -->
      <el-alert
        v-if="!loading && notifications.length > 0"
        title="提示：您可以使用上方的标签页切换不同类型的通知"
        type="info"
        :closable="false"
        show-icon
        style="margin-bottom: 16px;"
      />

      <el-empty v-if="!loading && (!notifications || notifications.length === 0)" description="暂无通知" />

      <div v-else class="notification-list">
        <el-checkbox
          v-model="selectAll"
          :indeterminate="isIndeterminate"
          @change="handleSelectAllChange"
          class="select-all-checkbox"
        >
          全选
        </el-checkbox>

        <el-skeleton :rows="3" animated v-if="loading" />
        <template v-else>
          <div
            v-for="item in notifications"
            :key="item.id"
            class="notification-item"
            :class="{ 'unread': item.status === 'UNREAD' }"
          >
            <div class="notification-checkbox">
              <el-checkbox v-model="item.selected" @change="handleItemSelectChange" />
            </div>
            <div class="notification-content" @click="handleView(item)">
              <div class="notification-header">
                <div class="notification-title">
                  <el-tag
                    :type="getNotificationTagType(item)"
                    size="small"
                    effect="light"
                  >
                    {{ getNotificationTypeText(item) }}
                  </el-tag>

                  <h3>{{ item.title }}</h3>

                  <el-tag
                    v-if="item.status === 'UNREAD'"
                    type="danger"
                    effect="plain"
                    size="small"
                    class="unread-tag"
                  >
                    未读
                  </el-tag>
                </div>
                <div class="notification-meta">
                  <span class="time">{{ formatDate(item.createdAt) }}</span>
                </div>
              </div>
              <p class="message">{{ item.message }}</p>
            </div>
          </div>
        </template>

        <!-- 通知详情组件 -->
        <notification-detail
          v-if="currentNotification"
          :notification="currentNotification"
          v-model:visible="dialogVisible"
          @deleted="handleNotificationDeleted"
        />
      </div>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :disabled="loading"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Delete, Refresh } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'
import { useNotificationStore } from '@/stores/notificationStore'
import { formatDate } from '@/utils/format'
import { wsClient } from '@/utils/websocket'
import { useRouter } from 'vue-router'
import { getNotificationTagType, getNotificationTypeText } from '@/utils/notificationUtils'
import NotificationDetail from '@/components/NotificationDetail.vue'

const userStore = useUserStore()
const notificationStore = useNotificationStore()
const notifications = ref([])
const activeTab = ref('all')
const activeType = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = computed(() => notificationStore.loading)
const markingAll = ref(false)
const router = useRouter()
const dialogVisible = ref(false)
const currentNotification = ref(null)
const selectAll = ref(false)
const isIndeterminate = ref(false)
const selectedNotifications = computed(() => {
  return notifications.value.filter(item => item.selected)
})

// 计算未读通知数量
const unreadCount = computed(() => {
  const count = notifications.value?.filter(item => item.status === 'UNREAD')?.length || 0
  console.log('未读通知数量:', count)
  return count
})

// 计算选中的未读通知数量
const selectedUnreadCount = computed(() => {
  return notifications.value?.filter(item => item.selected && item.status === 'UNREAD')?.length || 0
})

// 刷新通知列表
const refreshNotifications = async () => {
  await fetchNotifications()
  ElMessage.success('通知列表已刷新')
}

// 处理批量操作命令
const handleBatchCommand = async (command) => {
  switch (command) {
    case 'markAllAsRead':
      await markAllAsRead()
      break
    case 'markSelectedAsRead':
      await markSelectedAsRead()
      break
    case 'deleteSelected':
      await handleDeleteSelected()
      break
    default:
      console.warn('未知的批量操作命令:', command)
  }
}

// 标记选中的通知为已读
const markSelectedAsRead = async () => {
  const selectedUnread = notifications.value.filter(item => item.selected && item.status === 'UNREAD')

  if (selectedUnread.length === 0) {
    ElMessage.info('没有选中未读通知')
    return
  }

  try {
    loading.value = true

    // 按类型分组标记已读
    for (const notification of selectedUnread) {
      // 根据通知类型调用相应的标记已读方法
      if (notification.type === 'MATCH') {
        await notificationStore.markMatchAsRead(notification.id)
      } else if (notification.type === 'ANNOUNCEMENT') {
        await notificationStore.markAnnouncementRead(notification.id)
      } else {
        await notificationStore.markSystemAsRead(notification.id)
      }

      // 更新本地通知状态
      notification.status = 'READ'
      notification.isRead = true
    }

    ElMessage.success(`已将 ${selectedUnread.length} 条通知标记为已读`)
  } catch (error) {
    console.error('标记选中通知为已读失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 获取通知列表
const fetchNotifications = async () => {
  console.log('🔔🔔🔔 fetchNotifications 函数被调用 🔔🔔🔔')

  try {
    // 设置加载状态
    loading.value = true

    // 记录当前活动标签和类型
    const status = activeTab.value === 'all' ? null : activeTab.value.toUpperCase()

    // 首先从store获取所有通知数据
    await notificationStore.fetchAllNotifications()

    // 根据当前选项卡和类型过滤通知
    let filteredNotifications = []

    // 合并所有类型的通知并扁平化
    const allNotificationData = [
      ...notificationStore.systemNotifications.map(n => ({ ...n, type: n.type || 'SYSTEM' })),
      ...notificationStore.matchNotifications.map(n => ({ ...n, type: 'MATCH' })),
      ...notificationStore.announcements.map(a => ({ ...a, type: 'ANNOUNCEMENT' }))
    ]

    // 根据状态过滤
    if (status) {
      filteredNotifications = allNotificationData.filter(
        n => (n.status === status) || (status === 'READ' && n.isRead) || (status === 'UNREAD' && !n.isRead)
      )
    } else {
      filteredNotifications = allNotificationData
    }

    // 根据类型过滤
    if (activeType.value !== 'all') {
      const typeFilter = getNotificationTypeByCategory(activeType.value).split(',')
      filteredNotifications = filteredNotifications.filter(item => typeFilter.includes(item.type))
    }

    // 统一通知格式
    filteredNotifications = filteredNotifications.map(item => {
      return {
        ...item,
        // 确保状态字段统一
        status: item.status || (item.isRead ? 'READ' : 'UNREAD'),
        // 确保消息字段统一
        message: item.message || item.content || '',
        // 确保时间格式统一
        createdAt: item.createdAt || item.createTime || new Date().toISOString(),
        // 添加选中状态
        selected: false
      }
    })

    // 按时间排序
    filteredNotifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))

    // 更新视图数据
    notifications.value = filteredNotifications
    total.value = filteredNotifications.length

    console.log('🔔🔔🔔 通知获取完成，总数:', filteredNotifications.length)
  } catch (error) {
    console.error('【通知错误】获取通知列表失败:', error)
    ElMessage.error('获取通知失败，请稍后重试')
    notifications.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 根据分类获取通知类型
const getNotificationTypeByCategory = (category) => {
  switch (category) {
    case 'audit':
      return 'AUDIT_APPROVED,AUDIT_REJECTED'
    case 'match':
      return 'MATCH'
    case 'claim':
      return 'CLAIM,CLAIM_APPROVED,CLAIM_REJECTED'
    case 'system':
      return 'SYSTEM,ADMIN,ANNOUNCEMENT'
    default:
      return null
  }
}

// 全部标记为已读
const markAllAsRead = async () => {
  markingAll.value = true
  try {
    // 获取未读通知
    const unreadNotifications = notifications.value.filter(item => item.status === 'UNREAD')
    if (unreadNotifications.length === 0) {
      ElMessage.info('没有未读通知')
      return
    }

    // 按类型分组标记已读
    for (const notification of unreadNotifications) {
      // 根据通知类型调用相应的标记已读方法
      if (notification.type === 'MATCH') {
        await notificationStore.markMatchAsRead(notification.id)
      } else if (notification.type === 'ANNOUNCEMENT') {
        await notificationStore.markAnnouncementRead(notification.id)
      } else {
        await notificationStore.markSystemAsRead(notification.id)
      }

      // 更新本地通知状态
      notification.status = 'READ'
      notification.isRead = true
    }

    ElMessage.success('已将所有通知标记为已读')
  } catch (error) {
    console.error('标记所有通知为已读失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    markingAll.value = false
  }
}

// 删除选中的通知
const handleDeleteSelected = async () => {
  if (selectedNotifications.value.length === 0) {
    ElMessage.warning('请先选择要删除的通知')
    return
  }

  try {
    const confirmResult = await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedNotifications.value.length} 条通知吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (confirmResult === 'confirm') {
      loading.value = true

      // 逐个删除选中的通知
      for (const notification of selectedNotifications.value) {
        // 根据通知类型调用相应的删除方法
        if (notification.type === 'MATCH') {
          // 假设store中有删除匹配通知的方法
          // await notificationStore.deleteMatchNotification(notification.id)
          console.log('删除匹配通知:', notification.id)
        } else if (notification.type === 'ANNOUNCEMENT') {
          // 假设store中有删除公告通知的方法
          // await notificationStore.deleteAnnouncement(notification.id)
          console.log('删除公告通知:', notification.id)
        } else {
          await notificationStore.deleteSystemNotification(notification.id)
        }
      }

      // 重新获取通知列表
      await fetchNotifications()

      ElMessage.success('已删除选中的通知')
    }
  } catch (error) {
    console.error('删除通知失败:', error)
    ElMessage.error('删除失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 查看通知详情
const handleView = (notification) => {
  // 如果是未读通知，标记为已读
  if (notification.status === 'UNREAD') {
    // 根据通知类型调用相应的标记已读方法
    if (notification.type === 'MATCH') {
      notificationStore.markMatchAsRead(notification.id)
    } else if (notification.type === 'ANNOUNCEMENT') {
      notificationStore.markAnnouncementRead(notification.id)
    } else {
      notificationStore.markSystemAsRead(notification.id)
    }

    // 更新本地通知状态
    notification.status = 'READ'
    notification.isRead = true
  }

  // 设置当前通知并显示详情对话框
  currentNotification.value = notification
  dialogVisible.value = true
}

// 处理通知被删除的事件
const handleNotificationDeleted = (notificationId) => {
  // 从列表中移除该通知
  const index = notifications.value.findIndex(n => n.id === notificationId)
  if (index !== -1) {
    notifications.value.splice(index, 1)
    total.value--
  }
}

// 处理标签页切换
const handleTabClick = () => {
  // 重置分页并获取通知
  currentPage.value = 1
  fetchNotifications()
}

// 处理类型标签页切换
const handleTypeClick = () => {
  // 重置分页并获取通知
  currentPage.value = 1
  fetchNotifications()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchNotifications()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchNotifications()
}

// 处理全选变化
const handleSelectAllChange = (val) => {
  // 设置所有通知的选中状态
  notifications.value.forEach(item => {
    item.selected = val
  })
  isIndeterminate.value = false
}

// 处理单个通知选择变化
const handleItemSelectChange = () => {
  const selectedCount = selectedNotifications.value.length

  // 更新全选状态
  if (selectedCount === 0) {
    selectAll.value = false
    isIndeterminate.value = false
  } else if (selectedCount === notifications.value.length) {
    selectAll.value = true
    isIndeterminate.value = false
  } else {
    selectAll.value = false
    isIndeterminate.value = true
  }
}

// 处理新通知
const handleNewNotification = () => {
  // 当收到新通知时，刷新通知列表
  fetchNotifications()
}

// 组件挂载时获取通知
onMounted(() => {
  console.log('🔔🔔🔔 Notifications组件挂载')

  // 注册WebSocket事件监听
  wsClient.on('notification', handleNewNotification)

  // 获取通知数据
  fetchNotifications()

  // 监听全局刷新通知事件
  window.addEventListener('global-refresh-notifications', fetchNotifications)
})

// 组件卸载时清理
onUnmounted(() => {
  console.log('🔔🔔🔔 Notifications组件卸载')

  // 移除WebSocket事件监听
  wsClient.off('notification', handleNewNotification)

  // 移除全局事件监听
  window.removeEventListener('global-refresh-notifications', fetchNotifications)
})
</script>

<style scoped>
.notifications-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.custom-tabs :deep(.el-tabs__nav) {
  border-radius: 4px;
}

.type-tabs {
  margin-top: 16px;
  margin-bottom: 16px;
}

.type-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

.type-tabs :deep(.el-tabs__nav-wrap::after) {
  height: 1px;
  background-color: var(--el-border-color-light);
}

.type-tabs :deep(.el-tabs__item) {
  font-size: 13px;
  padding: 0 16px;
}

.type-tabs :deep(.el-tabs__item.is-active) {
  font-weight: 600;
}

.notification-list {
  margin-top: 16px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  transition: all 0.3s ease;
  margin-bottom: 12px;
  border: 1px solid var(--el-border-color-lighter);
  position: relative;
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--el-border-color);
}

.notification-item.unread {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-5);
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--el-color-primary);
  border-radius: 4px 0 0 4px;
}

.notification-checkbox {
  padding-top: 4px;
}

.select-all-checkbox {
  margin-bottom: 16px;
  padding: 0 16px;
  height: 48px;
  display: flex;
  align-items: center;
  background-color: var(--el-fill-color-light);
  border-radius: 8px 8px 0 0;
  font-weight: 500;
}

.notification-content {
  flex: 1;
  cursor: pointer;
}

.notification-header {
  margin-bottom: 12px;
}

.notification-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.notification-title h3 {
  margin: 0;
  font-size: 16px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.sender {
  color: var(--el-text-color-primary);
  font-size: 13px;
  font-weight: 500;
}

.time {
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.message {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.related-item-info {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px dashed var(--el-border-color-lighter);
}

.importance-tag {
  margin-right: 4px;
}

.notification-actions {
  display: flex;
  gap: 12px;
}

.notification-actions .el-button {
  padding: 4px 8px;
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

:deep(.el-badge__content) {
  background-color: var(--el-color-danger);
}

.notification-item.unread :deep(.el-tag) {
  border-color: transparent;
}

.unread-tag {
  margin-left: auto;
}

.notification-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.detail-time {
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.detail-content {
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  margin-bottom: 16px;
}

.detail-message {
  margin: 0;
  font-size: 14px;
  line-height: 1.6;
  color: var(--el-text-color-regular);
  white-space: pre-wrap;
}

.detail-item-info {
  margin-top: 16px;
  border-top: 1px dashed var(--el-border-color);
  padding-top: 16px;
}

.item-info-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.item-info-content p {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.detail-actions {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

.detail-reject-reason {
  margin-top: 16px;
}

.reject-reason-text {
  color: var(--el-color-danger);
  font-size: 14px;
  margin: 8px 0 0;
  padding: 8px;
  background-color: var(--el-color-danger-light-9);
  border-radius: 4px;
  border-left: 3px solid var(--el-color-danger);
}

:deep(.el-alert__title) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-alert__content) {
  padding: 8px 0 0;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .notification-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .notification-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .notification-item {
    padding: 12px;
  }

  .message {
    -webkit-line-clamp: 3;
    line-clamp: 3;
  }
}
</style>