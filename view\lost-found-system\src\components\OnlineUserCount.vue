<template>
  <div class="online-user-count" :class="{ 'connected': connected, 'connecting': connecting, 'disconnected': !connected && !connecting }">
    <el-tooltip
      :content="getConnectionStatusText()"
      placement="bottom"
    >
      <el-badge :value="count" :type="getBadgeType()" class="online-badge">
        <el-icon><User /></el-icon>
      </el-badge>
    </el-tooltip>
    <span class="count-text">在线用户: {{ count }}</span>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { User } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
// 使用统一的WebSocket功能入口
import { requestOnlineUsers } from '../utils/online-status'
// 使用统一的连接状态管理
import { addConnectionListener, removeConnectionListener, CONNECTION_EVENTS } from '../utils/connection-status'
// 导入WebSocket客户端
import { getWebSocketClient } from '../utils/websocket'
// 导入Pinia Store
import { useOnlineStatusStore } from '../stores'

// 使用Pinia Store
const store = useOnlineStatusStore()

// 使用计算属性从Store获取状态
const count = computed(() => store.onlineUserCount)
const connected = computed(() => store.isConnected)
const connecting = computed(() => store.isConnecting)
const reconnectAttempts = computed(() => store.connectionStatus.reconnectAttempts)

// 获取连接状态文本
function getConnectionStatusText() {
  if (connected.value) {
    return '在线状态监控已连接'
  } else if (connecting.value) {
    return '正在连接中...'
  } else {
    return '在线状态监控未连接'
  }
}

// 获取徽章类型
function getBadgeType() {
  if (connected.value) {
    return 'success'
  } else if (connecting.value) {
    return 'warning'
  } else {
    return 'danger'
  }
}

// 自动重连由online-status.js处理

// 处理在线用户数量更新事件
const handleOnlineUserCount = (event) => {
  console.log('收到在线用户数量更新事件:', event.detail)
  // 不需要手动更新count，因为它是一个计算属性，会自动从store获取最新值
  console.log('在线用户数量已更新为:', store.onlineUserCount)
}

// 处理在线用户列表更新事件
const handleOnlineUsersUpdated = (event) => {
  console.log('收到在线用户列表更新事件:', event.detail)
  // 不需要手动更新count，因为它是一个计算属性，会自动从store获取最新值
  console.log('在线用户列表更新:', event.detail.users, '数量已更新为:', store.onlineUserCount)
}

// 处理WebSocket连接状态变化
const handleConnectionStatus = (event) => {
  // 检查事件是否包含detail属性（自定义事件）
  if (event.detail) {
    // 如果连接成功，自动刷新在线用户数量
    if (event.detail.connected && event.detail.event === CONNECTION_EVENTS.CONNECTED) {
      console.log('WebSocket连接成功，自动刷新在线用户数量')
      store.connectionStatus.reconnectAttempts = 0
      setTimeout(() => {
        refreshOnlineCount()
      }, 1000)
    }
  } else {
    // 兼容旧版事件（不包含detail属性）
    if (event.type === CONNECTION_EVENTS.CONNECTED) {
      console.log('WebSocket连接成功')
      setTimeout(() => {
        refreshOnlineCount()
      }, 1000)
    }
  }
}

// 初始化在线用户数量
const initOnlineUserCount = () => {
  console.log('初始化在线用户数量')

  // 请求最新的在线用户列表
  refreshOnlineCount()
}

// 自动刷新在线用户数量
const refreshOnlineCount = async () => {
  console.log('自动刷新在线用户数量')

  // 检查WebSocket连接状态
  try {
    // 使用已导入的函数
    let connected = false

    // 使用动态导入
    try {
      const module = await import('@/utils/websocket/index')
      connected = module.isWebSocketConnected()
    } catch (importError) {
      console.error('导入WebSocket模块失败:', importError)
    }

    // 更新连接状态
    store.setConnectionStatus(connected, false)

    if (!connected) {
      console.warn('WebSocket未连接，无法刷新在线用户数量')
      return
    }
  } catch (e) {
    console.error('检查WebSocket连接状态失败:', e)
  }

  try {
    // 请求最新的在线用户列表
    requestOnlineUsers()
    console.log('已请求刷新在线用户数量')
  } catch (error) {
    console.error('刷新在线用户数量失败:', error)
  }
}

// 全局更新函数，供其他模块调用
const updateCount = (newCount) => {
  if (newCount > 0) {
    // 使用Store更新在线用户数量
    const tempUsers = Array.from({ length: newCount }, (_, i) => `temp-${i}`)
    store.updateOnlineUsers(tempUsers)
    console.log('通过全局函数更新在线用户数量:', store.onlineUserCount)
  }
}

// 将更新函数暴露给全局
window._updateOnlineUserCount = updateCount

// 定期刷新的定时器
let refreshInterval = null

onMounted(() => {
  console.log('OnlineUserCount组件挂载')

  // 检查WebSocket连接状态
  try {
    // 使用导入的函数而不是require
    import('@/utils/websocket/index').then(module => {
      const connected = module.isWebSocketConnected()

      // 更新连接状态
      store.setConnectionStatus(connected, false)
      console.log('初始WebSocket连接状态:', connected ? '已连接' : '未连接')
    }).catch(e => {
      console.error('导入WebSocket模块失败:', e)
    })
  } catch (e) {
    console.error('检查WebSocket连接状态失败:', e)
  }

  // 初始化在线用户数量
  initOnlineUserCount()

  // 监听在线用户数量更新事件
  window.addEventListener('online-user-count', handleOnlineUserCount)

  // 监听在线用户列表更新事件
  window.addEventListener('online-users-updated', handleOnlineUsersUpdated)

  // 使用统一的连接状态管理
  addConnectionListener(handleConnectionStatus)

  // 设置定期刷新（每5分钟刷新一次）
  refreshInterval = setInterval(() => {
    refreshOnlineCount() // 无论连接状态如何，都尝试刷新，内部会检查连接状态
  }, 300000) // 5分钟 = 300000毫秒

  // 立即刷新一次
  setTimeout(() => {
    refreshOnlineCount()
  }, 2000)

  // 再次尝试刷新，确保数据一致性
  setTimeout(() => {
    console.log('再次尝试刷新在线用户数量，确保数据一致性')
    refreshOnlineCount()
  }, 5000)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('online-user-count', handleOnlineUserCount)
  window.removeEventListener('online-users-updated', handleOnlineUsersUpdated)
  // 使用统一的连接状态管理
  removeConnectionListener(handleConnectionStatus)

  // 清除定期刷新定时器
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
})
</script>

<style scoped>
.online-user-count {
  display: flex;
  align-items: center;
  margin-left: 16px;
  position: relative;
}

.online-badge {
  margin-right: 8px;
  transition: all 0.3s;
}

.count-text {
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
}

.connected .count-text {
  color: #67c23a;
}

.connected .el-icon {
  color: #67c23a;
}

.connecting .count-text {
  color: #e6a23c;
}

.connecting .el-icon {
  color: #e6a23c;
  animation: pulse 1.5s infinite;
}

.disconnected .count-text {
  color: #f56c6c;
}

.disconnected .el-icon {
  color: #f56c6c;
}

/* 重连按钮已移除 */

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
