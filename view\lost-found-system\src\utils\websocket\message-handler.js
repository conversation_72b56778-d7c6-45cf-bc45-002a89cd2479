/**
 * WebSocket消息处理器
 * 统一处理所有WebSocket消息，包括订阅、发送和接收
 */
import connectionManager from './connection-manager';
import { useUserStore } from '@/stores';
import { ElMessage } from 'element-plus';

// 消息类型
export const MESSAGE_TYPES = {
  CHAT: 'CHAT',
  SYSTEM: 'SYSTEM',
  READ_RECEIPT: 'READ_RECEIPT',
  TYPING: 'TYPING',
  ONLINE_STATUS: 'ONLINE_STATUS'
};

class MessageHandler {
  constructor() {
    this.subscriptions = {};
    this.messageListeners = {};
    this.processedMessages = new Set(); // 用于消息去重
    this.maxProcessedMessages = 1000; // 最多保存1000条消息ID
  }

  /**
   * 初始化消息处理器
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize() {
    try {
      console.log('开始初始化消息处理器...');

      // 获取用户信息
      const userStore = useUserStore();
      console.log('用户存储状态:', {
        isLoggedIn: userStore.isLoggedIn,
        hasToken: !!userStore.token,
        userInfo: userStore.userInfo
      });

      const userId = userStore.userInfo?.id;

      if (!userId) {
        console.error('未找到用户ID，无法初始化消息处理器');
        console.log('用户信息详情:', userStore.userInfo);

        // 尝试从localStorage获取用户信息
        try {
          const storedUserInfo = localStorage.getItem('userInfo');
          if (storedUserInfo) {
            const parsedUserInfo = JSON.parse(storedUserInfo);
            console.log('从localStorage获取的用户信息:', parsedUserInfo);
            if (parsedUserInfo && parsedUserInfo.id) {
              console.log('使用localStorage中的用户ID:', parsedUserInfo.id);
              // 不自动设置用户信息，只记录日志
            }
          } else {
            console.log('localStorage中没有用户信息');
          }
        } catch (e) {
          console.error('解析localStorage中的用户信息失败:', e);
        }

        return false;
      }

      console.log('找到用户ID:', userId);

      // 确保WebSocket已连接
      const connected = await connectionManager.connect();
      if (!connected) {
        console.error('WebSocket连接失败，无法初始化消息处理器');
        return false;
      }

      // 订阅私人消息队列
      await this.subscribeToPrivateMessages(userId);

      // 订阅发送确认队列
      await this.subscribeToSentConfirmation(userId);

      // 订阅已读回执队列
      await this.subscribeToReadReceipts(userId);

      // 订阅在线状态更新
      await this.subscribeToOnlineStatus();

      console.log('消息处理器初始化成功');
      return true;
    } catch (error) {
      console.error('初始化消息处理器失败:', error);
      return false;
    }
  }

  /**
   * 订阅私人消息队列
   * @param {string|number} userId 用户ID
   * @returns {Promise<boolean>} 订阅是否成功
   */
  async subscribeToPrivateMessages(userId) {
    if (!connectionManager.isConnected()) {
      console.error('WebSocket未连接，无法订阅私人消息');
      return false;
    }

    try {
      // 取消之前的订阅
      if (this.subscriptions.privateMessages) {
        this.subscriptions.privateMessages.unsubscribe();
      }

      // 创建新订阅 - 使用标准路径 /user/{receiverId}/queue/private
      // Spring的convertAndSendToUser会自动添加/user前缀，所以后端实际发送到 /user/{userId}/queue/private
      const destination = `/user/queue/private`;
      this.subscriptions.privateMessages = connectionManager.stompClient.subscribe(
        destination,
        this._handlePrivateMessage.bind(this)
      );

      console.log(`已订阅私人消息队列: ${destination}`);
      return true;
    } catch (error) {
      console.error('订阅私人消息队列失败:', error);
      return false;
    }
  }

  /**
   * 订阅发送确认队列
   * @param {string|number} userId 用户ID
   * @returns {Promise<boolean>} 订阅是否成功
   */
  async subscribeToSentConfirmation(userId) {
    if (!connectionManager.isConnected()) {
      console.error('WebSocket未连接，无法订阅发送确认');
      return false;
    }

    try {
      // 取消之前的订阅
      if (this.subscriptions.sentConfirmation) {
        this.subscriptions.sentConfirmation.unsubscribe();
      }

      // 创建新订阅 - 使用标准路径 /user/queue/sent
      // Spring的convertAndSendToUser会自动添加/user前缀，所以后端实际发送到 /user/{userId}/queue/sent
      const destination = `/user/queue/sent`;
      this.subscriptions.sentConfirmation = connectionManager.stompClient.subscribe(
        destination,
        this._handleSentConfirmation.bind(this)
      );

      console.log(`已订阅发送确认队列: ${destination}`);
      return true;
    } catch (error) {
      console.error('订阅发送确认队列失败:', error);
      return false;
    }
  }

  /**
   * 订阅已读回执队列
   * @param {string|number} userId 用户ID
   * @returns {Promise<boolean>} 订阅是否成功
   */
  async subscribeToReadReceipts(userId) {
    if (!connectionManager.isConnected()) {
      console.error('WebSocket未连接，无法订阅已读回执');
      return false;
    }

    try {
      // 取消之前的订阅
      if (this.subscriptions.readReceipts) {
        this.subscriptions.readReceipts.unsubscribe();
      }

      // 创建新订阅 - 使用标准路径 /user/queue/read-receipts
      // Spring的convertAndSendToUser会自动添加/user前缀，所以后端实际发送到 /user/{userId}/queue/read-receipts
      const destination = `/user/queue/read-receipts`;
      this.subscriptions.readReceipts = connectionManager.stompClient.subscribe(
        destination,
        this._handleReadReceipt.bind(this)
      );

      console.log(`已订阅已读回执队列: ${destination}`);
      return true;
    } catch (error) {
      console.error('订阅已读回执队列失败:', error);
      return false;
    }
  }

  /**
   * 订阅在线状态更新
   * @returns {Promise<boolean>} 订阅是否成功
   */
  async subscribeToOnlineStatus() {
    if (!connectionManager.isConnected()) {
      console.error('WebSocket未连接，无法订阅在线状态');
      return false;
    }

    try {
      // 取消之前的订阅
      if (this.subscriptions.onlineStatus) {
        try {
          this.subscriptions.onlineStatus.unsubscribe();
          console.log('已取消在线状态订阅');
        } catch (e) {
          console.warn('取消在线状态订阅失败:', e);
        }
      }

      if (this.subscriptions.contactOnlineStatus) {
        try {
          this.subscriptions.contactOnlineStatus.unsubscribe();
          console.log('已取消联系人在线状态订阅');
        } catch (e) {
          console.warn('取消联系人在线状态订阅失败:', e);
        }
      }

      if (this.subscriptions.globalOnlineUsers) {
        try {
          this.subscriptions.globalOnlineUsers.unsubscribe();
          console.log('已取消全局在线用户列表订阅');
        } catch (e) {
          console.warn('取消全局在线用户列表订阅失败:', e);
        }
      }

      if (this.subscriptions.onlineCount) {
        try {
          this.subscriptions.onlineCount.unsubscribe();
          console.log('已取消在线用户数量订阅');
        } catch (e) {
          console.warn('取消在线用户数量订阅失败:', e);
        }
      }

      // 创建新订阅 - 使用标准路径 /topic/onlineStatus
      const destination = '/topic/onlineStatus';
      this.subscriptions.onlineStatus = connectionManager.stompClient.subscribe(
        destination,
        this._handleOnlineStatus.bind(this)
      );

      console.log(`已订阅在线状态更新: ${destination}`);

      // 订阅个人联系人在线状态 - 使用标准路径 /user/queue/onlineUsers
      // 这个主要用于联系人列表中显示联系人是否在线
      const userDestination = '/user/queue/onlineUsers';
      this.subscriptions.contactOnlineStatus = connectionManager.stompClient.subscribe(
        userDestination,
        this._handleContactOnlineStatus.bind(this)
      );
      console.log(`已订阅联系人在线状态: ${userDestination}`);

      // 订阅广播在线用户列表 - 使用标准路径 /topic/onlineUsers
      // 这个主要用于显示全局在线用户数量
      const broadcastDestination = '/topic/onlineUsers';
      this.subscriptions.globalOnlineUsers = connectionManager.stompClient.subscribe(
        broadcastDestination,
        this._handleGlobalOnlineUsers.bind(this)
      );
      console.log(`已订阅全局在线用户列表: ${broadcastDestination}`);

      // 订阅在线用户数量 - 使用标准路径 /topic/onlineCount
      const countDestination = '/topic/onlineCount';
      this.subscriptions.onlineCount = connectionManager.stompClient.subscribe(
        countDestination,
        this._handleOnlineCount.bind(this)
      );
      console.log(`已订阅在线用户数量: ${countDestination}`);

      return true;
    } catch (error) {
      console.error('订阅在线状态更新失败:', error);
      return false;
    }
  }

  /**
   * 发送聊天消息
   * @param {Object} message 消息对象
   * @param {File} file 可选的文件对象
   * @returns {Promise<Object>} 发送结果
   */
  async sendChatMessage(message, file = null) {
    if (!connectionManager.isConnected()) {
      console.error('WebSocket未连接，无法发送消息');
      return { code: 500, message: 'WebSocket未连接' };
    }

    try {
      // 添加时间戳和客户端消息ID（如果没有）
      const messageToSend = {
        ...message,
        timestamp: message.timestamp || new Date().toISOString(),
        clientMessageId: message.clientMessageId || `temp-${Date.now()}`
      };

      console.log('准备发送消息:', messageToSend);

      // 如果有文件，使用FormData上传
      if (file) {
        console.log('检测到文件，使用FormData上传');

        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);

        // 添加消息数据
        Object.keys(messageToSend).forEach(key => {
          formData.append(key, messageToSend[key]);
        });

        // 使用fetch API上传文件
        const response = await fetch('/api/chat/privateMessage', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: formData
        });

        const result = await response.json();
        console.log('文件上传响应:', result);

        return result;
      } else {
        // 纯文本消息通过WebSocket发送
        console.log('使用WebSocket发送文本消息');

        // 发送到privateMessage端点
        connectionManager.stompClient.publish({
          destination: '/app/privateMessage',
          headers: {
            'content-type': 'application/json'
          },
          body: JSON.stringify(messageToSend)
        });

        console.log('文本消息已发送');

        // 返回模拟的成功响应
        // 实际的消息ID和时间戳将通过/user/queue/sent回调获取
        return {
          code: 200,
          message: '消息已发送',
          data: {
            clientMessageId: messageToSend.clientMessageId,
            timestamp: messageToSend.timestamp
          }
        };
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      return {
        code: 500,
        message: `发送失败: ${error.message || '未知错误'}`
      };
    }
  }

  /**
   * 发送已读回执
   * @param {string|number|Array} messageId 消息ID或消息ID数组
   * @param {string|number} senderId 发送者ID
   * @param {string|number} readerId 读者ID（谁读了消息）
   * @returns {Promise<boolean>} 发送是否成功
   */
  async sendReadReceipt(messageId, senderId, readerId) {
    if (!connectionManager.isConnected()) {
      console.error('WebSocket未连接，无法发送已读回执');
      return false;
    }

    try {
      // 确保messageId是数组
      const messageIds = Array.isArray(messageId) ? messageId : [messageId];

      // 创建已读回执对象
      const receipt = {
        messageIds,
        senderId,
        readerId,
        timestamp: new Date().toISOString()
      };

      console.log('准备发送已读回执:', receipt);

      // 发送到已读回执端点
      connectionManager.stompClient.publish({
        destination: '/app/read-receipt',
        headers: {
          'content-type': 'application/json'
        },
        body: JSON.stringify(receipt)
      });

      console.log('已读回执已发送:', receipt);
      return true;
    } catch (error) {
      console.error('发送已读回执失败:', error);
      return false;
    }
  }

  /**
   * 添加消息监听器
   * @param {string} type 消息类型
   * @param {Function} listener 监听器函数
   */
  addMessageListener(type, listener) {
    if (!this.messageListeners[type]) {
      this.messageListeners[type] = [];
    }

    if (typeof listener === 'function' &&
        !this.messageListeners[type].includes(listener)) {
      this.messageListeners[type].push(listener);
    }
  }

  /**
   * 移除消息监听器
   * @param {string} type 消息类型
   * @param {Function} listener 监听器函数
   */
  removeMessageListener(type, listener) {
    if (!this.messageListeners[type]) {
      return;
    }

    const index = this.messageListeners[type].indexOf(listener);
    if (index !== -1) {
      this.messageListeners[type].splice(index, 1);
    }
  }

  /**
   * 处理私人消息
   * @param {Object} message STOMP消息对象
   * @private
   */
  _handlePrivateMessage(message) {
    try {
      console.log('【调试】收到私人消息原始数据:', message);
      const data = JSON.parse(message.body);
      console.log('【调试】解析后的私人消息数据:', data);
      console.log('【调试】消息字段:', Object.keys(data).join(', '));

      // 消息去重
      if (data.id && this.processedMessages.has(data.id)) {
        console.log(`【调试】跳过重复消息: ${data.id}`);
        return;
      }

      // 添加到已处理集合
      if (data.id) {
        this.processedMessages.add(data.id);
        console.log(`【调试】已将消息ID ${data.id} 添加到已处理集合，当前大小:`, this.processedMessages.size);

        // 限制集合大小
        if (this.processedMessages.size > this.maxProcessedMessages) {
          const iterator = this.processedMessages.values();
          const removed = iterator.next().value;
          this.processedMessages.delete(removed);
          console.log(`【调试】已从已处理集合中移除最旧的消息ID: ${removed}`);
        }
      }

      console.log('收到私人消息:', data);

      // 确保消息有正确的格式
      const processedMessage = {
        ...data,
        id: data.id || data.messageId,
        senderId: data.senderId,
        receiverId: data.receiverId,
        content: data.content || data.message,
        messageType: data.messageType || 'TEXT',
        timestamp: data.timestamp || data.time || new Date().toISOString(),
        status: data.status || 'RECEIVED'
      };

      console.log('处理后的消息:', processedMessage);
      console.log('【调试】处理后的消息字段:', Object.keys(processedMessage).join(', '));
      console.log('【调试】senderId:', processedMessage.senderId, '类型:', typeof processedMessage.senderId);
      console.log('【调试】receiverId:', processedMessage.receiverId, '类型:', typeof processedMessage.receiverId);

      // 获取当前用户ID
      const userStore = useUserStore();
      const currentUserId = userStore.userInfo?.id;
      console.log('【调试】当前用户ID:', currentUserId, '类型:', typeof currentUserId);

      // 检查消息是否与当前用户相关
      const isSelfMessage = String(processedMessage.senderId) === String(currentUserId);
      const isReceivedMessage = String(processedMessage.receiverId) === String(currentUserId);
      console.log('【调试】是自己发送的消息:', isSelfMessage);
      console.log('【调试】是发给当前用户的消息:', isReceivedMessage);

      if (!isSelfMessage && !isReceivedMessage) {
        console.log('【调试】消息与当前用户无关，但仍然触发事件');
      }

      // 触发全局事件
      console.log('【调试】准备触发 chat-message 全局事件');
      window.dispatchEvent(new CustomEvent('chat-message', {
        detail: processedMessage
      }));
      console.log('【调试】已触发 chat-message 全局事件');

      // 通知消息监听器
      this._notifyMessageListeners(MESSAGE_TYPES.CHAT, processedMessage);
      console.log('【调试】已通知消息监听器');

      // 额外触发一个备用事件，以防主事件有问题
      console.log('【调试】准备触发备用 chat-message-backup 全局事件');
      window.dispatchEvent(new CustomEvent('chat-message-backup', {
        detail: processedMessage
      }));
      console.log('【调试】已触发备用 chat-message-backup 全局事件');
    } catch (error) {
      console.error('处理私人消息失败:', error, message);
      console.error('【调试】错误详情:', error.stack);

      // 尝试直接解析消息体
      try {
        console.log('【调试】尝试直接处理原始消息体:', message.body);
        // 触发原始消息事件
        window.dispatchEvent(new CustomEvent('chat-message-raw', {
          detail: { body: message.body }
        }));
        console.log('【调试】已触发原始消息事件');
      } catch (e) {
        console.error('【调试】处理原始消息失败:', e);
      }
    }
  }

  /**
   * 处理发送确认
   * @param {Object} message STOMP消息对象
   * @private
   */
  _handleSentConfirmation(message) {
    try {
      const data = JSON.parse(message.body);
      console.log('收到发送确认:', data);

      // 确保确认消息有正确的格式
      const processedConfirmation = {
        ...data,
        id: data.id || data.messageId,
        clientMessageId: data.clientMessageId,
        senderId: data.senderId,
        receiverId: data.receiverId,
        timestamp: data.timestamp || data.time || new Date().toISOString(),
        status: 'SENT'
      };

      console.log('处理后的发送确认:', processedConfirmation);

      // 触发全局事件
      window.dispatchEvent(new CustomEvent('message-sent', {
        detail: processedConfirmation
      }));

      // 通知消息监听器
      this._notifyMessageListeners(MESSAGE_TYPES.CHAT, processedConfirmation);
    } catch (error) {
      console.error('处理发送确认失败:', error, message);
    }
  }

  /**
   * 处理已读回执
   * @param {Object} message STOMP消息对象
   * @private
   */
  _handleReadReceipt(message) {
    try {
      console.log('收到已读回执原始消息:', message);
      const data = JSON.parse(message.body);
      console.log('解析后的已读回执数据:', data);

      // 确保已读回执有正确的格式
      const processedReceipt = {
        ...data,
        readerId: data.readerId,
        senderId: data.senderId,
        messageIds: data.messageIds || [data.messageId],
        timestamp: data.timestamp || new Date().toISOString()
      };

      console.log('处理后的已读回执:', processedReceipt);

      // 触发全局事件
      window.dispatchEvent(new CustomEvent('read-receipt', {
        detail: processedReceipt
      }));

      // 通知消息监听器
      this._notifyMessageListeners(MESSAGE_TYPES.READ_RECEIPT, processedReceipt);
    } catch (error) {
      console.error('处理已读回执失败:', error, message);
    }
  }

  /**
   * 处理在线状态更新
   * @param {Object} message STOMP消息对象
   * @private
   */
  _handleOnlineStatus(message) {
    try {
      console.log('收到在线状态更新原始消息:', message);

      let data;
      let processedStatus;

      // 尝试解析JSON
      try {
        data = JSON.parse(message.body);
        console.log('成功解析JSON格式的在线状态更新:', data);

        // 确保状态消息有正确的格式
        processedStatus = {
          ...data,
          userId: data.userId,
          status: data.status,
          timestamp: data.timestamp || new Date().toISOString()
        };
      } catch (parseError) {
        // 如果不是JSON格式，尝试解析文本格式
        console.log('消息不是JSON格式，尝试解析文本格式:', message.body);

        // 尝试从文本中提取用户ID和状态
        const textBody = message.body.toString();
        const userIdMatch = textBody.match(/User (\d+) is (online|offline|back online)/i);

        if (userIdMatch) {
          const userId = userIdMatch[1];
          const status = userIdMatch[2].toLowerCase() === 'online' || userIdMatch[2].toLowerCase() === 'back online';

          console.log(`从文本中提取到用户ID: ${userId}, 状态: ${status ? 'online' : 'offline'}`);

          // 创建处理后的状态对象
          processedStatus = {
            userId: userId,
            isOnline: status, // 使用isOnline字段，与onlineStatusService.js中的处理保持一致
            status: status ? 'ONLINE' : 'OFFLINE',
            timestamp: new Date().toISOString(),
            fromTextFormat: true
          };
        } else {
          console.warn('无法从文本中提取用户ID和状态:', textBody);
          return; // 无法处理的消息格式，直接返回
        }
      }

      console.log('处理后的在线状态:', processedStatus);

      // 使用防抖处理，避免短时间内多次触发同一用户的状态更新
      if (!this._statusUpdateDebounce) {
        this._statusUpdateDebounce = new Map();
      }

      const userId = processedStatus.userId;

      // 如果已经有相同用户的状态更新在处理中，取消它
      if (this._statusUpdateDebounce.has(userId)) {
        clearTimeout(this._statusUpdateDebounce.get(userId));
      }

      // 设置新的延迟处理
      this._statusUpdateDebounce.set(userId, setTimeout(() => {
        // 触发全局事件
        window.dispatchEvent(new CustomEvent('user-online-status', {
          detail: processedStatus
        }));

        // 通知消息监听器
        this._notifyMessageListeners(MESSAGE_TYPES.ONLINE_STATUS, processedStatus);

        // 清理防抖Map
        this._statusUpdateDebounce.delete(userId);
      }, 300)); // 300ms防抖
    } catch (error) {
      console.error('处理在线状态更新失败:', error, message);
    }
  }

  /**
   * 处理联系人在线状态
   * @param {Object} message STOMP消息对象
   * @private
   */
  _handleContactOnlineStatus(message) {
    try {
      console.log('收到联系人在线状态消息:', message);
      const data = JSON.parse(message.body);
      console.log('解析后的联系人在线状态数据:', data);

      // 检查数据格式
      if (data && data.onlineUsers) {
        // 获取在线用户列表
        const onlineUsers = data.onlineUsers;
        const userIds = Object.keys(onlineUsers);
        console.log(`收到${userIds.length}个在线联系人:`, userIds);

        // 使用Pinia Store更新联系人在线状态
        try {
          // 使用动态导入替代require
          import('@/stores').then(({ useOnlineStatusStore }) => {
            const store = useOnlineStatusStore();

            // 更新联系人在线状态
            const result = store.updateContactOnlineStatus(userIds);
            console.log('更新联系人在线状态结果:', result);

            // 触发全局事件
            window.dispatchEvent(new CustomEvent('contact-online-status-updated', {
              detail: {
                users: userIds,
                timestamp: data.timestamp || Date.now()
              }
            }));
          }).catch(e => {
            console.error('导入Store模块失败:', e);
          });
        } catch (e) {
          console.error('更新联系人在线状态时出错:', e);
        }
      } else {
        console.warn('收到的联系人在线状态数据格式不正确:', data);
      }
    } catch (error) {
      console.error('处理联系人在线状态失败:', error, message);
    }
  }

  /**
   * 处理全局在线用户列表
   * @param {Object} message STOMP消息对象
   * @private
   */
  _handleGlobalOnlineUsers(message) {
    try {
      console.log('收到全局在线用户列表消息:', message);
      const data = JSON.parse(message.body);
      console.log('解析后的全局在线用户列表数据:', data);

      // 检查数据格式
      if (data && data.onlineUsers) {
        // 获取在线用户列表
        const onlineUsers = data.onlineUsers;
        const userIds = Object.keys(onlineUsers);
        console.log(`收到${userIds.length}个全局在线用户:`, userIds);

        // 使用Pinia Store更新全局在线用户列表
        try {
          // 使用动态导入替代require
          import('@/stores').then(({ useOnlineStatusStore }) => {
            const store = useOnlineStatusStore();

            // 更新全局在线用户列表
            const result = store.updateOnlineUsers(userIds);
            console.log('更新全局在线用户列表结果:', result);

            // 触发全局事件
            window.dispatchEvent(new CustomEvent('online-users-updated', {
              detail: {
                users: userIds,
                count: userIds.length,
                timestamp: data.timestamp || Date.now()
              }
            }));

            // 更新连接状态
            store.setConnectionStatus(true, false);
          }).catch(e => {
            console.error('导入Store模块失败:', e);
          });
        } catch (e) {
          console.error('更新全局在线用户列表时出错:', e);
        }
      } else {
        console.warn('收到的全局在线用户列表数据格式不正确:', data);
      }
    } catch (error) {
      console.error('处理全局在线用户列表失败:', error, message);
    }
  }

  /**
   * 处理在线用户数量
   * @param {Object} message STOMP消息对象
   * @private
   */
  _handleOnlineCount(message) {
    try {
      console.log('收到在线用户数量消息:', message);
      const data = JSON.parse(message.body);
      console.log('解析后的在线用户数量数据:', data);

      // 检查数据格式
      if (data && typeof data.onlineCount === 'number') {
        const count = data.onlineCount;
        console.log(`收到在线用户数量: ${count}`);

        // 使用Pinia Store更新在线用户数量
        try {
          // 使用动态导入替代require
          import('@/stores').then(({ useOnlineStatusStore }) => {
            const store = useOnlineStatusStore();

            // 更新在线用户数量
            store.setOnlineCount(count);

            // 触发全局事件
            window.dispatchEvent(new CustomEvent('online-count-updated', {
              detail: {
                count: count,
                timestamp: data.timestamp || Date.now()
              }
            }));
          }).catch(e => {
            console.error('导入Store模块失败:', e);
          });
        } catch (e) {
          console.error('更新在线用户数量时出错:', e);
        }
      } else {
        console.warn('收到的在线用户数量数据格式不正确:', data);
      }
    } catch (error) {
      console.error('处理在线用户数量失败:', error, message);
    }
  }

  /**
   * 通知消息监听器
   * @param {string} type 消息类型
   * @param {Object} data 消息数据
   * @private
   */
  _notifyMessageListeners(type, data) {
    if (!this.messageListeners[type]) {
      return;
    }

    this.messageListeners[type].forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error(`执行${type}消息监听器时出错:`, error);
      }
    });
  }
}

// 创建单例实例
const messageHandler = new MessageHandler();

export default messageHandler;
