package com.tomato.lostfoundsystem.entity;

import com.tomato.lostfoundsystem.enums.NotificationType;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class Notification {
    private Long id;
    private Long userId;
    private String message;
    private String status;  // "UNREAD" or "READ"
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String title;

    // 新增字段
    private String type;  // 通知类型，对应NotificationType枚举的code
    private Long relatedItemId;  // 关联物品ID
    private String relatedItemType;  // 关联物品类型(LOST/FOUND)
    private String metadata;  // 额外元数据(JSON格式)
    private Long auditorId;  // 审核员ID(仅审核通知)

    /**
     * 获取通知类型枚举
     * @return 通知类型枚举
     */
    public NotificationType getNotificationType() {
        return NotificationType.fromCode(type);
    }

    /**
     * 设置通知类型
     * @param notificationType 通知类型枚举
     */
    public void setNotificationType(NotificationType notificationType) {
        this.type = notificationType.getCode();
    }

    // 手动添加 getter 和 setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getRelatedItemId() {
        return relatedItemId;
    }

    public void setRelatedItemId(Long relatedItemId) {
        this.relatedItemId = relatedItemId;
    }

    public String getRelatedItemType() {
        return relatedItemType;
    }

    public void setRelatedItemType(String relatedItemType) {
        this.relatedItemType = relatedItemType;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Long getAuditorId() {
        return auditorId;
    }

    public void setAuditorId(Long auditorId) {
        this.auditorId = auditorId;
    }
}
