package com.tomato.lostfoundsystem.service.Impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.dto.NotificationDTO;
import com.tomato.lostfoundsystem.entity.ItemAudit;
import com.tomato.lostfoundsystem.entity.Notification;
import com.tomato.lostfoundsystem.entity.User;
import com.tomato.lostfoundsystem.enums.NotificationType;
import com.tomato.lostfoundsystem.mapper.ItemAuditMapper;
import com.tomato.lostfoundsystem.mapper.UserMapper;
import com.tomato.lostfoundsystem.mapper.UserNotificationMapper;
import com.tomato.lostfoundsystem.service.KafkaProducerService;
import com.tomato.lostfoundsystem.service.NotificationDispatchService;
import com.tomato.lostfoundsystem.service.NotificationEventService;
import com.tomato.lostfoundsystem.service.NotificationService;
import com.tomato.lostfoundsystem.service.RedisService;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NotificationServiceImpl implements NotificationService {

    private final UserNotificationMapper userNotificationMapper;
    private final UserMapper userMapper;
    private final SecurityUtil securityUtil;
    private final RedisService redisService;
    private final KafkaProducerService kafkaProducerService;
    private final ObjectMapper objectMapper;
    private final ItemAuditMapper itemAuditMapper;
    private final NotificationEventService notificationEventService;
    private final NotificationDispatchService notificationDispatchService;

    @Autowired
    public NotificationServiceImpl(
            UserNotificationMapper userNotificationMapper,
            UserMapper userMapper,
            SecurityUtil securityUtil,
            RedisService redisService,
            KafkaProducerService kafkaProducerService,
            ItemAuditMapper itemAuditMapper,
            NotificationEventService notificationEventService,
            NotificationDispatchService notificationDispatchService) {
        this.userNotificationMapper = userNotificationMapper;
        this.userMapper = userMapper;
        this.securityUtil = securityUtil;
        this.redisService = redisService;
        this.kafkaProducerService = kafkaProducerService;
        this.itemAuditMapper = itemAuditMapper;
        this.notificationEventService = notificationEventService;
        this.notificationDispatchService = notificationDispatchService;
        this.objectMapper = new ObjectMapper();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sendNotificationToUser(Long userId, String title, String message) {
        // 查询用户是否存在
        User user = userMapper.findById(userId);

        // 如果用户不存在，返回错误信息
        if (user == null) {
            throw new RuntimeException("用户ID " + userId + " 不存在");
        }

        // 用户存在，继续发送通知
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setStatus("UNREAD");

        // 设置通知类型为管理员通知
        notification.setType(NotificationType.ADMIN.getCode());

        // 获取当前管理员ID
        Long currentAdminId = securityUtil.getCurrentUserId();
        if (currentAdminId != null) {
            notification.setAuditorId(currentAdminId);

            // 设置元数据
            try {
                // 获取管理员信息
                User admin = userMapper.findById(currentAdminId);

                // 构建元数据
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("sendTime", LocalDateTime.now().toString());
                if (admin != null) {
                    metadata.put("adminName", admin.getUsername());
                    metadata.put("adminRole", admin.getRole());
                }

                notification.setMetadata(objectMapper.writeValueAsString(metadata));
            } catch (Exception e) {
                log.error("设置元数据失败", e);
            }
        }

        // 保存通知到数据库
        userNotificationMapper.insertNotification(notification);

        // 检查用户是否在线
        boolean isUserOnline = redisService.isUserOnline(userId);

        if (isUserOnline) {
            // 用户在线，通过WebSocket推送通知
            log.info("用户在线，通过WebSocket推送通知，用户ID: {}", userId);
            // 使用通知事件服务发送WebSocket通知
            notificationEventService.sendWebSocketNotification(userId, title, message, notification.getId());
        } else {
            // 用户离线，通过Kafka存储离线通知
            log.info("用户离线，通过Kafka存储离线通知，用户ID: {}", userId);
            try {
                // 构建通知消息
                Map<String, Object> notificationMessage = new HashMap<>();
                notificationMessage.put("type", "NOTIFICATION");
                notificationMessage.put("receiverId", userId);
                notificationMessage.put("title", title);
                notificationMessage.put("message", message);
                notificationMessage.put("notificationType", notification.getType());
                notificationMessage.put("notificationId", notification.getId());

                // 添加元数据
                if (notification.getMetadata() != null) {
                    notificationMessage.put("metadata", notification.getMetadata());
                }

                // 转换为JSON并发送到Kafka
                String jsonMessage = objectMapper.writeValueAsString(notificationMessage);
                kafkaProducerService.sendNotification(jsonMessage);

                log.info("离线通知已发送到Kafka，用户ID: {}, 通知ID: {}", userId, notification.getId());
            } catch (Exception e) {
                log.error("发送离线通知到Kafka失败", e);
            }
        }
    }


    /**
     * 向所有管理员发送通知（包括普通管理员和超级管理员）
     * @param notificationDTO 通知内容
     */
    @Override
    public void sendNotificationToAdmins(NotificationDTO notificationDTO) {
        // 查询所有管理员和超级管理员的ID列表（通过角色查询）
        List<Long> adminIds = userMapper.getUserIdsByRole("ADMIN");  // 获取所有管理员的ID列表

        // 将超级管理员也包含在管理员列表中
        List<Long> superAdminIds = userMapper.getUserIdsByRole("SUPER_ADMIN");  // 获取超级管理员的ID列表
        adminIds.addAll(superAdminIds);  // 合并普通管理员和超级管理员的ID列表

        // 获取当前管理员ID
        Long currentAdminId = securityUtil.getCurrentUserId();
        User currentAdmin = null;
        if (currentAdminId != null) {
            currentAdmin = userMapper.findById(currentAdminId);
        }

        // 构建元数据
        String metadataJson = null;
        try {
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("sendTime", LocalDateTime.now().toString());
            if (currentAdmin != null) {
                metadata.put("adminName", currentAdmin.getUsername());
                metadata.put("adminRole", currentAdmin.getRole());
            }
            metadataJson = objectMapper.writeValueAsString(metadata);
        } catch (Exception e) {
            log.error("构建元数据失败", e);
        }

        // 遍历所有管理员ID，进行通知推送
        for (Long userId : adminIds) {
            // 将通知数据传递给实体类
            Notification notification = new Notification();
            notification.setUserId(userId);
            notification.setTitle(notificationDTO.getTitle());
            notification.setMessage(notificationDTO.getMessage());
            notification.setStatus("UNREAD");  // 默认通知为未读
            notification.setCreatedAt(LocalDateTime.now());
            notification.setUpdatedAt(LocalDateTime.now());

            // 设置通知类型
            notification.setType(NotificationType.ADMIN.getCode());

            // 设置发送者ID和元数据
            if (currentAdminId != null) {
                notification.setAuditorId(currentAdminId);
                notification.setMetadata(metadataJson);
            }

            // 保存通知到数据库
            userNotificationMapper.insertNotification(notification);

            // 检查用户是否在线
            boolean isUserOnline = redisService.isUserOnline(userId);

            if (isUserOnline) {
                // 用户在线，通过WebSocket推送通知
                log.info("管理员在线，通过WebSocket推送通知，用户ID: {}", userId);
                notificationEventService.sendWebSocketNotification(userId, notification.getTitle(), notification.getMessage(), notification.getId());
            } else {
                // 用户离线，通过Kafka存储离线通知
                log.info("管理员离线，通过Kafka存储离线通知，用户ID: {}", userId);
                try {
                    // 构建通知消息
                    Map<String, Object> notificationMessage = new HashMap<>();
                    notificationMessage.put("type", "NOTIFICATION");
                    notificationMessage.put("receiverId", userId);
                    notificationMessage.put("title", notification.getTitle());
                    notificationMessage.put("message", notification.getMessage());
                    notificationMessage.put("notificationType", notification.getType());
                    notificationMessage.put("notificationId", notification.getId());

                    // 添加元数据
                    if (notification.getMetadata() != null) {
                        notificationMessage.put("metadata", notification.getMetadata());
                    }

                    // 转换为JSON并发送到Kafka
                    String jsonMessage = objectMapper.writeValueAsString(notificationMessage);
                    kafkaProducerService.sendNotification(jsonMessage);

                    log.info("离线通知已发送到Kafka，管理员ID: {}", userId);
                } catch (Exception e) {
                    log.error("发送离线通知到Kafka失败", e);
                }
            }
        }
    }

    @Override
    public void sendNotificationToUsersByRole(NotificationDTO notificationDTO) {
        // 查询所有用户的ID列表
        List<Long> allUserIds = userMapper.getUserIdsByRole("USER");  // 获取所有用户的ID列表

        // 获取当前管理员ID
        Long currentAdminId = securityUtil.getCurrentUserId();
        User currentAdmin = null;
        if (currentAdminId != null) {
            currentAdmin = userMapper.findById(currentAdminId);
        }

        // 构建元数据
        String metadataJson = null;
        try {
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("sendTime", LocalDateTime.now().toString());
            if (currentAdmin != null) {
                metadata.put("adminName", currentAdmin.getUsername());
                metadata.put("adminRole", currentAdmin.getRole());
            }
            metadataJson = objectMapper.writeValueAsString(metadata);
        } catch (Exception e) {
            log.error("构建元数据失败", e);
        }

        // 遍历所有用户ID，进行通知推送
        for (Long userId : allUserIds) {
            // 将通知数据传递给实体类
            Notification notification = new Notification();
            notification.setUserId(userId);
            notification.setTitle(notificationDTO.getTitle());
            notification.setMessage(notificationDTO.getMessage());
            notification.setStatus("UNREAD");  // 默认通知为未读
            notification.setCreatedAt(LocalDateTime.now());
            notification.setUpdatedAt(LocalDateTime.now());

            // 设置通知类型
            notification.setType(NotificationType.ADMIN.getCode());

            // 设置发送者ID和元数据
            if (currentAdminId != null) {
                notification.setAuditorId(currentAdminId);
                notification.setMetadata(metadataJson);
            }

            // 保存通知到数据库
            userNotificationMapper.insertNotification(notification);

            // 检查用户是否在线
            boolean isUserOnline = redisService.isUserOnline(userId);

            if (isUserOnline) {
                // 用户在线，通过WebSocket推送通知
                log.info("用户在线，通过WebSocket推送通知，用户ID: {}", userId);
                notificationEventService.sendWebSocketNotification(userId, notification.getTitle(), notification.getMessage(), notification.getId());
            } else {
                // 用户离线，通过Kafka存储离线通知
                log.info("用户离线，通过Kafka存储离线通知，用户ID: {}", userId);
                try {
                    // 构建通知消息
                    Map<String, Object> notificationMessage = new HashMap<>();
                    notificationMessage.put("type", "NOTIFICATION");
                    notificationMessage.put("receiverId", userId);
                    notificationMessage.put("title", notification.getTitle());
                    notificationMessage.put("message", notification.getMessage());
                    notificationMessage.put("notificationType", notification.getType());
                    notificationMessage.put("notificationId", notification.getId());

                    // 添加元数据
                    if (notification.getMetadata() != null) {
                        notificationMessage.put("metadata", notification.getMetadata());
                    }

                    // 转换为JSON并发送到Kafka
                    String jsonMessage = objectMapper.writeValueAsString(notificationMessage);
                    kafkaProducerService.sendNotification(jsonMessage);

                    log.info("离线通知已发送到Kafka，用户ID: {}", userId);
                } catch (Exception e) {
                    log.error("发送离线通知到Kafka失败", e);
                }
            }
        }
    }

    @Override
    public Integer getUnreadCount(Long userId) {
        return userNotificationMapper.getUnreadCount(userId);
    }

    @Override
    public void sendNotificationToAllUsers(NotificationDTO notificationDTO) {
        // 查询所有用户的ID列表
        List<Long> allUserIds = userMapper.getAllUserIds();  // 获取所有用户的ID列表

        // 获取当前管理员ID
        Long currentAdminId = securityUtil.getCurrentUserId();
        User currentAdmin = null;
        if (currentAdminId != null) {
            currentAdmin = userMapper.findById(currentAdminId);
        }

        // 构建元数据
        String metadataJson = null;
        try {
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("sendTime", LocalDateTime.now().toString());
            if (currentAdmin != null) {
                metadata.put("adminName", currentAdmin.getUsername());
                metadata.put("adminRole", currentAdmin.getRole());
            }
            metadataJson = objectMapper.writeValueAsString(metadata);
        } catch (Exception e) {
            log.error("构建元数据失败", e);
        }

        // 遍历所有用户ID，进行通知推送
        for (Long userId : allUserIds) {
            // 将通知数据传递给实体类
            Notification notification = new Notification();
            notification.setUserId(userId);
            notification.setTitle(notificationDTO.getTitle());
            notification.setMessage(notificationDTO.getMessage());
            notification.setStatus("UNREAD");  // 默认通知为未读
            notification.setCreatedAt(LocalDateTime.now());
            notification.setUpdatedAt(LocalDateTime.now());

            // 设置通知类型
            notification.setType(NotificationType.ADMIN.getCode());

            // 设置发送者ID和元数据
            if (currentAdminId != null) {
                notification.setAuditorId(currentAdminId);
                notification.setMetadata(metadataJson);
            }

            // 保存通知到数据库
            userNotificationMapper.insertNotification(notification);

            // 检查用户是否在线
            boolean isUserOnline = redisService.isUserOnline(userId);

            if (isUserOnline) {
                // 用户在线，通过WebSocket推送通知
                log.info("用户在线，通过WebSocket推送通知，用户ID: {}", userId);
                notificationEventService.sendWebSocketNotification(userId, notification.getTitle(), notification.getMessage(), notification.getId());
            } else {
                // 用户离线，通过Kafka存储离线通知
                log.info("用户离线，通过Kafka存储离线通知，用户ID: {}", userId);
                try {
                    // 构建通知消息
                    Map<String, Object> notificationMessage = new HashMap<>();
                    notificationMessage.put("type", "NOTIFICATION");
                    notificationMessage.put("receiverId", userId);
                    notificationMessage.put("title", notification.getTitle());
                    notificationMessage.put("message", notification.getMessage());
                    notificationMessage.put("notificationType", notification.getType());
                    notificationMessage.put("notificationId", notification.getId());

                    // 添加元数据
                    if (notification.getMetadata() != null) {
                        notificationMessage.put("metadata", notification.getMetadata());
                    }

                    // 转换为JSON并发送到Kafka
                    String jsonMessage = objectMapper.writeValueAsString(notificationMessage);
                    kafkaProducerService.sendNotification(jsonMessage);

                    log.info("离线通知已发送到Kafka，用户ID: {}", userId);
                } catch (Exception e) {
                    log.error("发送离线通知到Kafka失败", e);
                }
            }
        }
    }


    /**
     * 创建并存储审核通过通知
     *
     * @param userId 用户ID
     * @param itemType 物品类型
     * @param itemId 物品ID
     * @return 通知ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createApprovalNotification(Long userId, String itemType, Long itemId) {
        // 创建通知内容
        String title = "您的发布已通过审核";
        // 动态构建消息
        String message = "您的" + (itemType.equals("lost") ? "失物" : "拾物") +
                "信息（ID: " + itemId + "）已经审核通过，并成功发布。";

        // 获取当前管理员ID（审核员）
        Long auditorId = securityUtil.getCurrentUserId();

        // 如果当前没有登录管理员，尝试从审核记录中获取
        if (auditorId == null) {
            try {
                // 从审核记录中获取审核员ID
                if (itemAuditMapper != null) {
                    if (itemType.equals("lost")) {
                        // 获取失物审核记录
                        ItemAudit audit = itemAuditMapper.getLatestLostItemAudit(itemId);
                        if (audit != null) {
                            auditorId = audit.getAuditorId();
                        }
                    } else {
                        // 获取拾物审核记录
                        ItemAudit audit = itemAuditMapper.getLatestFoundItemAudit(itemId);
                        if (audit != null) {
                            auditorId = audit.getAuditorId();
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取审核记录失败", e);
            }
        }

        // 插入通知到数据库
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setMessage(message);
        notification.setStatus("UNREAD");
        notification.setTitle(title);

        // 设置通知类型和关联物品
        notification.setType(NotificationType.AUDIT_APPROVED.getCode());
        notification.setRelatedItemId(itemId);
        notification.setRelatedItemType(itemType.equals("lost") ? "LOST" : "FOUND");

        // 设置审核员ID和元数据
        if (auditorId != null) {
            notification.setAuditorId(auditorId);

            try {
                // 获取审核员信息
                User auditor = userMapper.findById(auditorId);

                // 构建元数据
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("auditTime", LocalDateTime.now().toString());
                if (auditor != null) {
                    metadata.put("auditorName", auditor.getUsername());
                    metadata.put("auditorRole", auditor.getRole());
                }

                notification.setMetadata(objectMapper.writeValueAsString(metadata));
            } catch (Exception e) {
                log.error("设置元数据失败", e);
            }
        }

        userNotificationMapper.insertNotification(notification);
        log.info("【通知】审核通过通知已创建并存储到数据库 - 用户ID: {}, 通知ID: {}", userId, notification.getId());

        return notification.getId();
    }

    /**
     * 兼容旧版本的方法，使用新的方法实现
     */
    @Override
    public void sendApprovalNotification(Long userId, String itemType, Long itemId) {
        log.info("【通知】使用兼容方法发送审核通过通知 - 用户ID: {}, 物品类型: {}, 物品ID: {}", userId, itemType, itemId);

        // 创建并存储通知
        Long notificationId = createApprovalNotification(userId, itemType, itemId);

        // 分发通知
        String title = "您的发布已通过审核";
        String message = "您的" + (itemType.equals("lost") ? "失物" : "拾物") +
                "信息（ID: " + itemId + "）已经审核通过，并成功发布。";

        notificationDispatchService.dispatchNotification(
                userId,
                title,
                message,
                notificationId,
                NotificationType.AUDIT_APPROVED.getCode(),
                itemId,
                itemType.equals("lost") ? "LOST" : "FOUND"
        );
    }


    /**
     * 创建并存储审核拒绝通知
     *
     * @param userId 用户ID
     * @param itemType 物品类型
     * @param itemId 物品ID
     * @param remarks 拒绝原因
     * @return 通知ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRejectionNotification(Long userId, String itemType, Long itemId, String remarks) {
        String title = "您的发布未通过审核";
        String message = "您的" +  (itemType.equals("lost") ? "失物" : "拾物") + "信息（ID: " + itemId + "）:"+ remarks +"未通过审核，发布失败。";

        // 获取当前管理员ID（审核员）
        Long auditorId = securityUtil.getCurrentUserId();

        // 如果当前没有登录管理员，尝试从审核记录中获取
        if (auditorId == null) {
            try {
                // 从审核记录中获取审核员ID
                if (itemAuditMapper != null) {
                    if (itemType.equals("lost")) {
                        // 获取失物审核记录
                        ItemAudit audit = itemAuditMapper.getLatestLostItemAudit(itemId);
                        if (audit != null) {
                            auditorId = audit.getAuditorId();
                        }
                    } else {
                        // 获取拾物审核记录
                        ItemAudit audit = itemAuditMapper.getLatestFoundItemAudit(itemId);
                        if (audit != null) {
                            auditorId = audit.getAuditorId();
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取审核记录失败", e);
            }
        }

        // 插入通知到数据库
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setMessage(message);
        notification.setStatus("UNREAD");
        notification.setTitle(title);

        // 设置通知类型和关联物品
        notification.setType(NotificationType.AUDIT_REJECTED.getCode());
        notification.setRelatedItemId(itemId);
        notification.setRelatedItemType(itemType.equals("lost") ? "LOST" : "FOUND");

        // 设置审核员ID和元数据
        if (auditorId != null) {
            notification.setAuditorId(auditorId);

            try {
                // 获取审核员信息
                User auditor = userMapper.findById(auditorId);

                // 构建元数据
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("rejectReason", remarks);
                metadata.put("auditTime", LocalDateTime.now().toString());
                if (auditor != null) {
                    metadata.put("auditorName", auditor.getUsername());
                    metadata.put("auditorRole", auditor.getRole());
                }

                notification.setMetadata(objectMapper.writeValueAsString(metadata));
            } catch (Exception e) {
                log.error("设置元数据失败", e);
            }
        }

        userNotificationMapper.insertNotification(notification);
        log.info("【通知】审核拒绝通知已创建并存储到数据库 - 用户ID: {}, 通知ID: {}", userId, notification.getId());

        return notification.getId();
    }

    /**
     * 兼容旧版本的方法，使用新的方法实现
     */
    @Override
    public void sendRejectionNotification(Long userId, String itemType, Long itemId, String remarks) {
        log.info("【通知】使用兼容方法发送审核拒绝通知 - 用户ID: {}, 物品类型: {}, 物品ID: {}", userId, itemType, itemId);

        // 创建并存储通知
        Long notificationId = createRejectionNotification(userId, itemType, itemId, remarks);

        // 分发通知
        String title = "您的发布未通过审核";
        String message = "您的" +  (itemType.equals("lost") ? "失物" : "拾物") + "信息（ID: " + itemId + "）:"+ remarks +"未通过审核，发布失败。";

        notificationDispatchService.dispatchNotification(
                userId,
                title,
                message,
                notificationId,
                NotificationType.AUDIT_REJECTED.getCode(),
                itemId,
                itemType.equals("lost") ? "LOST" : "FOUND"
        );
    }

    /**
     * 创建并存储通知
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param type 通知类型
     * @return 通知ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createNotification(Long userId, String title, String message, String type) {
        return createNotification(userId, title, message, type, null);
    }

    /**
     * 创建并存储通知（带有元数据）
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param type 通知类型
     * @param metadata 元数据JSON字符串
     * @return 通知ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createNotification(Long userId, String title, String message, String type, String metadata) {
        // 查询用户是否存在
        User user = userMapper.findById(userId);

        // 如果用户不存在，返回错误信息
        if (user == null) {
            throw new RuntimeException("用户ID " + userId + " 不存在");
        }

        // 用户存在，创建通知
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setStatus("UNREAD");
        notification.setType(type);

        // 获取当前管理员ID
        Long currentAdminId = securityUtil.getCurrentUserId();
        if (currentAdminId != null) {
            notification.setAuditorId(currentAdminId);
        }

        // 设置元数据
        if (metadata != null) {
            notification.setMetadata(metadata);
        } else {
            try {
                // 获取管理员信息
                User admin = null;
                if (currentAdminId != null) {
                    admin = userMapper.findById(currentAdminId);
                }

                // 构建元数据
                Map<String, Object> metadataMap = new HashMap<>();
                metadataMap.put("sendTime", LocalDateTime.now().toString());
                if (admin != null) {
                    metadataMap.put("adminName", admin.getUsername());
                    metadataMap.put("adminRole", admin.getRole());
                }

                notification.setMetadata(objectMapper.writeValueAsString(metadataMap));
            } catch (Exception e) {
                log.error("设置元数据失败", e);
            }
        }

        // 保存通知到数据库
        userNotificationMapper.insertNotification(notification);
        log.info("【通知】通知已创建并存储到数据库 - 用户ID: {}, 通知ID: {}, 类型: {}", userId, notification.getId(), type);

        return notification.getId();
    }

    /**
     * 创建并存储完整通知
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param type 通知类型
     * @param relatedItemId 关联物品ID
     * @param relatedItemType 关联物品类型
     * @param metadata 元数据JSON字符串
     * @param auditorId 审核员ID
     * @return 通知ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createNotification(Long userId, String title, String message, String type,
                                  Long relatedItemId, String relatedItemType,
                                  String metadata, Long auditorId) {
        // 查询用户是否存在
        User user = userMapper.findById(userId);

        // 如果用户不存在，返回错误信息
        if (user == null) {
            throw new RuntimeException("用户ID " + userId + " 不存在");
        }

        // 用户存在，创建通知
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setStatus("UNREAD");
        notification.setType(type);

        // 设置关联物品信息
        if (relatedItemId != null) {
            notification.setRelatedItemId(relatedItemId);
        }

        if (relatedItemType != null) {
            notification.setRelatedItemType(relatedItemType);
        }

        // 设置审核员ID
        if (auditorId != null) {
            notification.setAuditorId(auditorId);
        } else {
            // 获取当前管理员ID
            Long currentAdminId = securityUtil.getCurrentUserId();
            if (currentAdminId != null) {
                notification.setAuditorId(currentAdminId);
            }
        }

        // 设置元数据
        if (metadata != null) {
            notification.setMetadata(metadata);
        } else {
            try {
                // 获取管理员信息
                User admin = null;
                Long adminId = auditorId != null ? auditorId : securityUtil.getCurrentUserId();
                if (adminId != null) {
                    admin = userMapper.findById(adminId);
                }

                // 构建元数据
                Map<String, Object> metadataMap = new HashMap<>();
                metadataMap.put("sendTime", LocalDateTime.now().toString());
                if (admin != null) {
                    metadataMap.put("adminName", admin.getUsername());
                    metadataMap.put("adminRole", admin.getRole());
                }

                notification.setMetadata(objectMapper.writeValueAsString(metadataMap));
            } catch (Exception e) {
                log.error("设置元数据失败", e);
            }
        }

        // 保存通知到数据库
        userNotificationMapper.insertNotification(notification);
        log.info("【通知】完整通知已创建并存储到数据库 - 用户ID: {}, 通知ID: {}, 类型: {}, 关联物品ID: {}, 关联物品类型: {}",
                userId, notification.getId(), type, relatedItemId, relatedItemType);

        return notification.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    // 获取用户的通知（根据状态过滤）
    public List<NotificationDTO> getNotifications(Long userId, String status) {
        List<Notification> notifications = userNotificationMapper.getNotificationsByStatus(userId, status);
        return notifications.stream()
                .map(notification -> convertToDTO(notification))
                .collect(Collectors.toList());
    }

    // 将 Notification 实体转换为 NotificationDTO
    private NotificationDTO convertToDTO(Notification notification) {
        NotificationDTO dto = new NotificationDTO();
        dto.setId(notification.getId());
        dto.setUserId(notification.getUserId());
        dto.setTitle(notification.getTitle());
        dto.setMessage(notification.getMessage());
        dto.setStatus(notification.getStatus());
        dto.setCreatedAt(notification.getCreatedAt());
        dto.setUpdatedAt(notification.getUpdatedAt());

        // 设置新增字段
        dto.setType(notification.getType());
        dto.setRelatedItemId(notification.getRelatedItemId());
        dto.setRelatedItemType(notification.getRelatedItemType());
        dto.setMetadata(notification.getMetadata());
        dto.setAuditorId(notification.getAuditorId());

        return dto;
    }

    // 标记通知为已读
    @Transactional(rollbackFor = Exception.class)
    public void markNotificationAsRead(Long notificationId) {
        userNotificationMapper.updateNotificationStatus(notificationId, "READ");
    }

    // 删除通知
    @Transactional(rollbackFor = Exception.class)
    public void deleteNotification(Long notificationId) {
        userNotificationMapper.deleteNotification(notificationId);
    }



    /**
     * 检查通知是否已经发送过
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @return 如果通知已经发送过，返回true；否则返回false
     */
    @Override
    public boolean isNotificationSent(Long userId, Long notificationId) {
        // 委托给通知事件服务处理
        return notificationEventService.isNotificationSent(userId, notificationId);
    }

    /**
     * 标记通知为已发送
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @param expirationSeconds 过期时间（秒）
     */
    @Override
    public void markNotificationAsSent(Long userId, Long notificationId, int expirationSeconds) {
        // 委托给通知事件服务处理
        notificationEventService.markNotificationAsSent(userId, notificationId, expirationSeconds);
    }

    /**
     * 定时清理过期的通知记录，每小时执行一次
     * Redis会自动清理过期的键，这里只是为了记录日志
     */
    @Scheduled(fixedRate = 3600000)
    @Override
    public void cleanExpiredNotifications() {
        try {
            log.info("开始清理过期的通知记录");

            // Redis会自动清理过期的键，这里只是为了记录日志
            Set<String> keys = redisService.getKeysByPattern("notification:sent:*");

            log.info("当前通知记录数量: {}", keys.size());
        } catch (Exception e) {
            log.error("清理过期通知记录时出错: {}", e.getMessage(), e);
        }
    }
}

