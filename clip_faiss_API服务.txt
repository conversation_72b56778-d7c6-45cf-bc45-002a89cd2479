#提取文本特征
import requests

text = "一个黑色的背包，里面有笔记本电脑和书"
response = requests.post(
    "http://localhost:8000/extract_text_features",
    json={"text": text}
)
text_features = response.json()["features"]
print(f"文本特征长度: {len(text_features)}")


提取图像特征
import requests
from PIL import Image
import io

# 加载图像
image_path = "path/to/your/image.jpg"
image = Image.open(image_path)

# 转换为字节流
img_byte_arr = io.BytesIO()
image.save(img_byte_arr, format='JPEG')
img_byte_arr = img_byte_arr.getvalue()

# 发送请求
files = {'file': ('image.jpg', img_byte_arr, 'image/jpeg')}
response = requests.post("http://localhost:8000/extract_image_features", files=files)
image_features = response.json()["features"]
print(f"图像特征长度: {len(image_features)}")


添加向量到索引
import requests

# 假设我们已经有了特征向量
text_features = "..."  # 从提取文本特征的API获取
image_features = "..."  # 从提取图像特征的API获取

# 添加到失物索引
response = requests.post(
    "http://localhost:8000/add_vector",
    json={
        "item_id": 123,  # 物品ID
        "item_type": "LOST",  # 物品类型：LOST或FOUND
        "text_features": text_features,
        "image_features": image_features
    }
)
print(response.json())

搜索相似物品

import requests

# 假设我们已经有了特征向量
features = "..."  # 从提取特征的API获取

# 搜索相似的拾物
response = requests.post(
    "http://localhost:8000/search_similar",
    json={
        "features": features,
        "item_type": "FOUND",  # 搜索拾物
        "index_type": "text",  # 使用文本特征
        "limit": 10  # 返回前10个结果
    }
)
results = response.json()["results"]
print(f"找到 {len(results)} 个相似物品")
for item in results:
    print(f"物品ID: {item['item_id']}, 相似度: {item['similarity']}")


