<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tomato.lostfoundsystem.mapper.UserMapper">

    <!-- 插入用户 -->
    <insert id="insertUser" parameterType="com.tomato.lostfoundsystem.entity.User">
        INSERT INTO users (username, password, email, phone, role, create_time)
        VALUES (#{username}, #{password}, #{email}, #{phone}, #{role}, #{createTime})
    </insert>

    <!-- 通过用户名查找用户 -->
    <select id="findByUsername" parameterType="string" resultType="com.tomato.lostfoundsystem.entity.User">
        SELECT * FROM users WHERE username = #{username}
    </select>

    <!-- 通过手机号查找用户 -->
    <select id="findByPhone" parameterType="string" resultType="com.tomato.lostfoundsystem.entity.User">
        SELECT * FROM users WHERE phone = #{phone}
    </select>

    <!-- 通过邮箱查找用户 -->
    <select id="findByEmail" parameterType="string" resultType="com.tomato.lostfoundsystem.entity.User">
        SELECT * FROM users WHERE email = #{email}
    </select>

    <!-- 查询手机号绑定的邮箱 -->
    <select id="getEmailByPhone" resultType="java.lang.String">
        SELECT email FROM users
        WHERE phone = #{phone}
        LIMIT 1
    </select>

    <!-- 查询用户信息 -->
    <select id="findById" resultType="com.tomato.lostfoundsystem.entity.User">
        SELECT * FROM users WHERE id = #{userId}
    </select>

    <!-- 更新用户信息 -->
    <update id="updateUser">
        UPDATE users
        SET email = #{email},
            phone = #{phone}
        WHERE id = #{id}
    </update>


    <!--关键词查询用户-->
    <select id="searchUsers" resultType="com.tomato.lostfoundsystem.entity.User">
        SELECT * FROM users
        <where>
            <if test="keyword != null and keyword != ''">
                AND (username LIKE CONCAT('%', #{keyword}, '%')
                OR email LIKE CONCAT('%', #{keyword}, '%')
                OR phone LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="isActive != null">
                AND is_active = #{isActive}
            </if>
            <if test="role != null and role != ''">
                AND role = #{role}
            </if>
            <if test="deleted != null">
                AND deleted = #{deleted}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
   <!--更新用户角色-->
    <update id="updateUserRole">
        UPDATE users SET role = #{role}, updated_at = NOW() WHERE id = #{id}
    </update>
   <!--启用和禁用用户角色-->
    <update id="updateUserStatus">
        UPDATE users SET is_active = #{isActive}, updated_at = NOW() WHERE id = #{id}
    </update>
    <!--重置密码-->
    <update id="updateUserPassword">
        UPDATE users
        SET password = #{password},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!--逻辑删除用户-->
    <update id="logicalDeleteUser">
        UPDATE users
        SET deleted = TRUE,
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据角色查询用户ID -->
    <select id="getUserIdsByRole" resultType="Long">
        SELECT id
        FROM users
        WHERE role = #{role}
    </select>

    <!-- 获取所有用户ID -->
    <select id="getAllUserIds" resultType="Long">
        SELECT id
        FROM users
    </select>

    <!-- 更新用户头像 -->
    <update id="updateUserAvatar">
        UPDATE users
        SET avatar = #{avatar},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 统计总用户数（未删除的） -->
    <select id="countTotalUsers" resultType="int">
        SELECT COUNT(*)
        FROM users
        WHERE deleted = false
    </select>

    <!-- 统计指定日期注册的用户数 -->
    <select id="countUsersByRegistrationDate" resultType="int">
        SELECT COUNT(*)
        FROM users
        WHERE DATE(create_time) = #{date}
        AND deleted = false
    </select>

    <!-- 统计指定月份注册的用户数 -->
    <select id="countUsersByRegistrationMonth" resultType="int">
        SELECT COUNT(*)
        FROM users
        WHERE YEAR(create_time) = #{year}
        AND MONTH(create_time) = #{month}
        AND deleted = false
    </select>

</mapper>

