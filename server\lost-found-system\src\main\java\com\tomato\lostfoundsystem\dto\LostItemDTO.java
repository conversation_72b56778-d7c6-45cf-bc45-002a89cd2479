package com.tomato.lostfoundsystem.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class LostItemDTO {
    private Long lostItemId;
    @NotNull(message = "用户ID不能为空")
    private Long userId;           // 用户ID
    @NotBlank(message = "物品名称不能为空")
    private String itemName;       // 物品名称
    @NotBlank(message = "物品描述不能为空")
    @jakarta.validation.constraints.Size(min = 20, max = 500, message = "物品描述长度必须在20-500个字符之间，请提供详细描述，包括物品特征、颜色、品牌等信息")
    private String description;    // 物品描述

    @NotBlank(message = "丢失时间不能为空")
    private String lostTime;       // 丢失时间
    @NotBlank(message = "丢失地点不能为空")
    private String lostLocation;   // 丢失地点
    private MultipartFile image;   // 主图片文件（可选，向后兼容）
    private List<MultipartFile> images; // 多图片文件列表（可选）
    private Integer mainImageIndex = 0; // 主图索引，默认为0
    private boolean keepExistingImages = false; // 是否保留原有图片
    private boolean mainImageChanged = false; // 主图索引是否变化

    private String username;   //发布者的用户名

    private String createdAt; //发布时间

    private String Status;
}