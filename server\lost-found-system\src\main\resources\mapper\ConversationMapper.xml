<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tomato.lostfoundsystem.mapper.ConversationMapper">

    <!-- 获取两个用户之间的会话 -->
    <select id="getConversation" resultType="com.tomato.lostfoundsystem.entity.Conversation">
        SELECT * FROM chat_sessions
        WHERE (user1_id = #{user1Id} AND user2_id = #{user2Id})
           OR (user1_id = #{user2Id} AND user2_id = #{user1Id})
        LIMIT 1
    </select>

    <!-- 插入新的会话记录 -->
    <insert id="insert" parameterType="com.tomato.lostfoundsystem.entity.Conversation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chat_sessions (
            user1_id,
            user2_id,
            last_message_id,
            last_message_content,
            last_message_type,
            last_message_time,
            unread_count,
            status,
            is_pinned,
            is_muted,
            created_at,
            updated_at
        )
        VALUES (
            #{user1Id},
            #{user2Id},
            #{lastMessageId},
            #{lastMessageContent},
            #{lastMessageType},
            #{lastMessageTime},
            #{unreadCount},
            #{status},
            #{isPinned},
            #{isMuted},
            #{createdAt},
            #{updatedAt}
        )
    </insert>

    <!-- 更新会话信息 -->
    <update id="update" parameterType="com.tomato.lostfoundsystem.entity.Conversation">
        UPDATE chat_sessions
        SET
            last_message_id = #{lastMessageId},
            last_message_content = #{lastMessageContent},
            last_message_type = #{lastMessageType},
            last_message_time = #{lastMessageTime},
            unread_count = #{unreadCount},
            status = #{status},
            is_pinned = #{isPinned},
            is_muted = #{isMuted},
            updated_at = #{updatedAt}
        WHERE id = #{id}
    </update>

    <!-- 更新会话中的最后一条消息时间 -->
    <update id="updateLastMessageTime">
        UPDATE chat_sessions
        SET last_message_time = #{lastMessageTime},
            updated_at = NOW()
        WHERE id = #{sessionId}
    </update>

    <!-- 更新会话的最后一条消息信息 -->
    <update id="updateLastMessage">
        UPDATE chat_sessions
        SET
            last_message_id = #{lastMessageId},
            last_message_content = #{lastMessageContent},
            last_message_type = #{lastMessageType},
            last_message_time = #{lastMessageTime},
            updated_at = NOW()
        WHERE id = #{sessionId}
    </update>

    <!-- 更新会话未读计数 -->
    <update id="updateUnreadCount">
        UPDATE chat_sessions
        SET unread_count = #{unreadCount},
            updated_at = NOW()
        WHERE id = #{sessionId}
    </update>

    <!-- 更新会话状态 -->
    <update id="updateStatus">
        UPDATE chat_sessions
        SET status = #{status},
            updated_at = NOW()
        WHERE id = #{sessionId}
    </update>

    <!-- 更新会话置顶状态 -->
    <update id="updatePinned">
        UPDATE chat_sessions
        SET is_pinned = #{isPinned},
            updated_at = NOW()
        WHERE id = #{sessionId}
    </update>

    <!-- 更新会话静音状态 -->
    <update id="updateMuted">
        UPDATE chat_sessions
        SET is_muted = #{isMuted},
            updated_at = NOW()
        WHERE id = #{sessionId}
    </update>

    <!-- 重置会话未读计数 -->
    <update id="resetUnreadCount">
        UPDATE chat_sessions
        SET unread_count = 0,
            updated_at = NOW()
        WHERE (user1_id = #{userId} AND user2_id = #{contactId})
           OR (user1_id = #{contactId} AND user2_id = #{userId})
    </update>

    <!-- 增加会话未读计数 -->
    <update id="incrementUnreadCount">
        UPDATE chat_sessions
        SET unread_count = unread_count + 1,
            updated_at = NOW()
        WHERE (user1_id = #{userId} AND user2_id = #{contactId})
           OR (user1_id = #{contactId} AND user2_id = #{userId})
    </update>

    <!-- 获取用户的所有联系人（即与用户有过聊天记录的其他用户） -->
    <select id="getContacts" resultType="Long">
        SELECT user1_id AS contact_id FROM chat_sessions
        WHERE user2_id = #{userId} AND status != 'DELETED'
        UNION
        SELECT user2_id AS contact_id FROM chat_sessions
        WHERE user1_id = #{userId} AND status != 'DELETED'
    </select>

    <!-- 获取用户的所有会话 -->
    <select id="getConversationsByUserId" resultType="com.tomato.lostfoundsystem.entity.Conversation">
        SELECT * FROM chat_sessions
        WHERE (user1_id = #{userId} OR user2_id = #{userId})
        AND status != 'DELETED'
        ORDER BY is_pinned DESC, last_message_time DESC
    </select>

    <!-- 获取最后一次聊天时间 -->
    <select id="getLastMessageTime" resultType="String">
        SELECT last_message_time
        FROM chat_sessions
        WHERE (user1_id = #{userId} AND user2_id = #{contactId})
           OR (user1_id = #{contactId} AND user2_id = #{userId})
        LIMIT 1
    </select>
</mapper>
