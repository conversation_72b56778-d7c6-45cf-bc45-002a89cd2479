package com.tomato.lostfoundsystem.utils;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

@Slf4j
@Component
public class AliyunSmsUtil {

    private final IAcsClient client;

    @Value("${aliyun.sms.signName}")
    private String signName;

    @Value("${aliyun.sms.templateCode}")
    private String templateCode;

    private final String accessKeyId;
    private final String accessKeySecret;

    public AliyunSmsUtil(
            @Value("${aliyun.sms.accessKeyId}") String accessKeyId,
            @Value("${aliyun.sms.accessKeySecret}") String accessKeySecret) {
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
        this.client = new DefaultAcsClient(profile);
    }

    @PostConstruct
    public void init() {
        log.info("阿里云短信服务配置初始化");
        log.info("AccessKey ID: {}", accessKeyId.substring(0, 3) + "..." + accessKeyId.substring(accessKeyId.length() - 3));
        log.info("签名名称: {}", signName);
        log.info("模板编号: {}", templateCode);
    }

    /**
     * 发送验证码短信，返回阿里云响应结果
     */
    public SendSmsResponse sendCode(String phone, String code) throws ClientException {
        log.info("开始发送短信 - 手机号: {}, 验证码: {}", phone, code);
        log.info("短信配置 - 签名: {}, 模板: {}", signName, templateCode);

        try {
            SendSmsRequest request = new SendSmsRequest();
            request.setPhoneNumbers(phone);
            request.setSignName(signName);
            request.setTemplateCode(templateCode);
            String templateParam = "{\"code\":\"" + code + "\"}";
            request.setTemplateParam(templateParam);

            log.info("短信请求参数 - 手机号: {}, 签名: {}, 模板: {}, 参数: {}",
                    phone, signName, templateCode, templateParam);

            log.info("准备调用阿里云短信API...");
            SendSmsResponse response = client.getAcsResponse(request);

            log.info("短信API响应 - 状态码: {}, 消息: {}, 请求ID: {}, 业务ID: {}",
                    response.getCode(), response.getMessage(), response.getRequestId(), response.getBizId());

            if (!"OK".equals(response.getCode())) {
                log.error("短信发送失败 - 状态码: {}, 消息: {}", response.getCode(), response.getMessage());
            } else {
                log.info("短信发送成功 - 手机号: {}", phone);
            }

            return response;
        } catch (ClientException e) {
            log.error("阿里云短信客户端异常 - 错误代码: {}, 错误消息: {}, 请求ID: {}",
                    e.getErrCode(), e.getErrMsg(), e.getRequestId(), e);
            throw e;
        } catch (Exception e) {
            log.error("发送短信时发生未知异常", e);
            throw new ClientException("UnknownError", "发送短信时发生未知异常", e);
        }
    }
}
