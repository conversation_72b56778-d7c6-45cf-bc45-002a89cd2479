/**
 * 会话管理 Store
 *
 * 该 Store 负责管理聊天会话的状态，包括：
 * - 会话列表的获取和更新
 * - 会话的置顶、静音、归档、删除等操作
 * - 会话未读计数的管理
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
  getContacts,
  updateConversationStatus,
  updateConversationPinned,
  updateConversationMuted,
  resetUnreadCount,
  incrementUnreadCount
} from '@/api/chat'
import { useUserStore } from './userStore'
import { ElMessage } from 'element-plus'
import logger from '@/utils/logger'

// 创建会话管理专用日志记录器
const convLogger = logger.createLogger('Conversation')

export const useConversationStore = defineStore('conversation', () => {
  // 状态
  const conversations = ref([])
  const loading = ref(false)
  const error = ref(null)
  const lastFetchTime = ref(0)

  // 用户 Store
  const userStore = useUserStore()

  // 计算属性
  const sortedConversations = computed(() => {
    return [...conversations.value].sort((a, b) => {
      // 先按置顶状态排序
      if (a.isPinned && !b.isPinned) return -1
      if (!a.isPinned && b.isPinned) return 1

      // 再按最后消息时间排序
      const timeA = new Date(a.lastTime || 0).getTime()
      const timeB = new Date(b.lastTime || 0).getTime()
      return timeB - timeA
    })
  })

  const totalUnreadCount = computed(() => {
    return conversations.value.reduce((total, conv) => total + (conv.unreadCount || 0), 0)
  })

  // 方法
  /**
   * 获取会话列表
   * @param {boolean} force 是否强制刷新
   * @returns {Promise<Array>} 会话列表
   */
  async function fetchConversations(force = false) {
    // 如果没有登录，不获取会话列表
    if (!userStore.isLoggedIn) {
      convLogger.warn('用户未登录，无法获取会话列表')
      return []
    }

    // 如果正在加载，不重复获取
    if (loading.value && !force) {
      convLogger.warn('正在加载会话列表，跳过重复请求')
      return conversations.value
    }

    // 如果距离上次获取时间不足5秒，且不是强制刷新，直接返回缓存
    const now = Date.now()
    if (!force && now - lastFetchTime.value < 5000) {
      convLogger.info('使用缓存的会话列表')
      return conversations.value
    }

    loading.value = true
    error.value = null

    try {
      convLogger.info('开始获取会话列表')
      const response = await getContacts(userStore.userInfo.id)

      if (response.code === 200 && Array.isArray(response.data)) {
        // 处理会话数据，确保字段一致性
        const processedConversations = response.data.map(conv => ({
          ...conv,
          // 确保会话ID字段存在
          conversationId: conv.conversationId || conv.id,
          // 确保置顶和静音字段存在
          isPinned: conv.isPinned || false,
          isMuted: conv.isMuted || false,
          // 确保状态字段存在
          status: conv.status || 'ACTIVE',
          // 确保未读计数字段存在
          unreadCount: conv.unreadCount || 0
        }))

        conversations.value = processedConversations
        lastFetchTime.value = now
        convLogger.info(`成功获取会话列表，共 ${processedConversations.length} 个会话`)
        return processedConversations
      } else {
        error.value = response.message || '获取会话列表失败'
        convLogger.error('获取会话列表失败:', error.value)
        return []
      }
    } catch (err) {
      error.value = err.message || '获取会话列表失败'
      convLogger.error('获取会话列表异常:', err)
      return []
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新会话状态（归档、删除等）
   * @param {number} conversationId 会话ID
   * @param {string} status 状态：ACTIVE, ARCHIVED, DELETED
   * @returns {Promise<boolean>} 是否成功
   */
  async function updateStatus(conversationId, status) {
    try {
      convLogger.info('更新会话状态:', { conversationId, status })
      const response = await updateConversationStatus(conversationId, status)

      if (response.code === 200) {
        // 如果是删除操作，从列表中移除
        if (status === 'DELETED') {
          conversations.value = conversations.value.filter(conv => conv.conversationId !== conversationId)
          convLogger.info('会话已删除:', conversationId)
        }
        // 如果是归档操作，更新状态
        else if (status === 'ARCHIVED') {
          const conversation = conversations.value.find(conv => conv.conversationId === conversationId)
          if (conversation) {
            conversation.status = status
            convLogger.info('会话已归档:', conversationId)
          }
        }

        return true
      }

      convLogger.error('更新会话状态失败:', response.message)
      return false
    } catch (err) {
      convLogger.error(`更新会话状态失败 (${status}):`, err)
      ElMessage.error(`更新会话状态失败: ${err.message}`)
      return false
    }
  }

  /**
   * 更新会话置顶状态
   * @param {number} conversationId 会话ID
   * @param {boolean} isPinned 是否置顶
   * @returns {Promise<boolean>} 是否成功
   */
  async function updatePinned(conversationId, isPinned) {
    try {
      convLogger.info('更新会话置顶状态:', { conversationId, isPinned })
      const response = await updateConversationPinned(conversationId, isPinned)

      if (response.code === 200) {
        const conversation = conversations.value.find(conv => conv.conversationId === conversationId)
        if (conversation) {
          conversation.isPinned = isPinned
          convLogger.info(`会话 ${conversationId} 置顶状态已更新为 ${isPinned}`)
        }
        return true
      }

      convLogger.error('更新会话置顶状态失败:', response.message)
      return false
    } catch (err) {
      convLogger.error('更新会话置顶状态失败:', err)
      ElMessage.error(`更新会话置顶状态失败: ${err.message}`)
      return false
    }
  }

  /**
   * 更新会话静音状态
   * @param {number} conversationId 会话ID
   * @param {boolean} isMuted 是否静音
   * @returns {Promise<boolean>} 是否成功
   */
  async function updateMuted(conversationId, isMuted) {
    try {
      convLogger.info('更新会话静音状态:', { conversationId, isMuted })
      const response = await updateConversationMuted(conversationId, isMuted)

      if (response.code === 200) {
        const conversation = conversations.value.find(conv => conv.conversationId === conversationId)
        if (conversation) {
          conversation.isMuted = isMuted
          convLogger.info(`会话 ${conversationId} 静音状态已更新为 ${isMuted}`)
        }
        return true
      }

      convLogger.error('更新会话静音状态失败:', response.message)
      return false
    } catch (err) {
      convLogger.error('更新会话静音状态失败:', err)
      ElMessage.error(`更新会话静音状态失败: ${err.message}`)
      return false
    }
  }

  /**
   * 重置会话未读计数
   * @param {number} userId 用户ID
   * @param {number} contactId 联系人ID
   * @returns {Promise<boolean>} 是否成功
   */
  async function clearUnreadCount(userId, contactId) {
    try {
      convLogger.info('重置会话未读计数:', { userId, contactId })

      // 先更新本地状态，确保UI立即响应
      // 查找会话并更新未读计数
      const conversation = conversations.value.find(conv =>
        conv.contactId === contactId ||
        String(conv.contactId) === String(contactId) ||
        conv.id === contactId ||
        String(conv.id) === String(contactId)
      )

      if (conversation) {
        // 记录旧的未读计数，用于日志
        const oldUnreadCount = conversation.unreadCount || 0;

        // 重置未读计数
        conversation.unreadCount = 0
        convLogger.info(`会话未读计数已在本地重置 - 联系人ID: ${contactId}, 旧计数: ${oldUnreadCount} -> 0`)
      }

      // 然后发送请求到服务器
      const response = await resetUnreadCount(userId, contactId)

      if (response.code === 200) {
        convLogger.info(`会话未读计数已在服务器重置 - 联系人ID: ${contactId}`)
        return true
      }

      convLogger.error('重置会话未读计数失败:', response.message)
      return false
    } catch (err) {
      convLogger.error('重置会话未读计数失败:', err)
      return false
    }
  }

  /**
   * 增加会话未读计数
   * @param {number} userId 用户ID
   * @param {number} contactId 联系人ID
   * @returns {Promise<boolean>} 是否成功
   */
  async function increaseUnreadCount(userId, contactId) {
    try {
      convLogger.info('增加会话未读计数:', { userId, contactId })
      const response = await incrementUnreadCount(userId, contactId)

      if (response.code === 200) {
        // 查找会话并更新未读计数
        const conversation = conversations.value.find(conv => conv.contactId === contactId)
        if (conversation) {
          conversation.unreadCount = (conversation.unreadCount || 0) + 1
          convLogger.info(`会话未读计数已增加 - 联系人ID: ${contactId}, 新计数: ${conversation.unreadCount}`)
        }
        return true
      }

      convLogger.error('增加会话未读计数失败:', response.message)
      return false
    } catch (err) {
      convLogger.error('增加会话未读计数失败:', err)
      return false
    }
  }

  /**
   * 使用消息更新会话
   * @param {Object} message 消息对象
   */
  function updateConversationWithMessage(message) {
    if (!message) return

    convLogger.info('使用消息更新会话:', {
      messageId: message.id,
      senderId: message.senderId,
      receiverId: message.receiverId
    })

    const userId = userStore.userInfo.id
    const isCurrentUser = message.senderId === userId
    const contactId = isCurrentUser ? message.receiverId : message.senderId

    // 查找现有会话
    let conversation = conversations.value.find(conv => conv.contactId === contactId)

    if (conversation) {
      // 更新现有会话
      conversation.lastMessage = message.message
      conversation.lastTime = new Date(message.timestamp || message.time || Date.now()).toISOString()
      conversation.messageType = message.messageType
      conversation.lastMessageId = message.id

      // 如果不是当前用户发送的消息，增加未读计数
      if (!isCurrentUser) {
        conversation.unreadCount = (conversation.unreadCount || 0) + 1
        convLogger.info(`增加会话未读计数 - 联系人ID: ${contactId}, 新计数: ${conversation.unreadCount}`)
      }

      convLogger.info(`会话已更新 - 联系人ID: ${contactId}`)
    } else {
      // 创建新会话
      convLogger.info(`创建新会话 - 联系人ID: ${contactId}`)

      // 获取联系人信息
      const contactName = message.senderName || `用户${contactId}`
      const contactAvatar = message.senderAvatar || ''

      const newConversation = {
        contactId,
        conversationId: message.conversationId,
        name: contactName,
        avatar: contactAvatar,
        avatarText: contactName.charAt(0).toUpperCase(),
        lastMessage: message.message,
        lastTime: new Date(message.timestamp || message.time || Date.now()).toISOString(),
        messageType: message.messageType,
        lastMessageId: message.id,
        unreadCount: isCurrentUser ? 0 : 1,
        isPinned: false,
        isMuted: false,
        status: 'ACTIVE'
      }

      conversations.value.push(newConversation)
      convLogger.info(`新会话已创建 - 联系人ID: ${contactId}`)
    }
  }

  return {
    // 状态
    conversations,
    loading,
    error,

    // 计算属性
    sortedConversations,
    totalUnreadCount,

    // 方法
    fetchConversations,
    updateStatus,
    updatePinned,
    updateMuted,
    clearUnreadCount,
    increaseUnreadCount,
    updateConversationWithMessage
  }
})
