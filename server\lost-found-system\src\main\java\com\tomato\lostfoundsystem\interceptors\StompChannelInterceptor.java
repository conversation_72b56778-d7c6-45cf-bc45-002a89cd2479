package com.tomato.lostfoundsystem.interceptors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.stereotype.Component;

import java.security.Principal;

/**
 * STOMP通道拦截器
 * 负责将WebSocket握手阶段获取的用户信息设置到STOMP会话中
 */
@Component
public class StompChannelInterceptor implements ChannelInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(StompChannelInterceptor.class);

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);

        if (accessor != null) {
            StompCommand command = accessor.getCommand();
            String sessionId = accessor.getSessionId();

            // 记录所有STOMP命令
            if (command != null) {
                logger.info("【STOMP拦截器】收到STOMP命令: {} - 会话ID: {}", command, sessionId);

                // 记录订阅和发送消息的目标地址
                if (StompCommand.SUBSCRIBE.equals(command)) {
                    String destination = accessor.getDestination();
                    logger.info("【STOMP拦截器】用户订阅: {} - 会话ID: {}", destination, sessionId);

                    // 如果有用户信息，记录用户名
                    if (accessor.getUser() != null) {
                        logger.info("【STOMP拦截器】订阅用户: {} - 目标: {}", accessor.getUser().getName(), destination);
                    }
                } else if (StompCommand.SEND.equals(command)) {
                    String destination = accessor.getDestination();
                    logger.info("【STOMP拦截器】用户发送消息: {} - 会话ID: {}", destination, sessionId);

                    // 如果有用户信息，记录用户名
                    if (accessor.getUser() != null) {
                        logger.info("【STOMP拦截器】发送用户: {} - 目标: {}", accessor.getUser().getName(), destination);
                    }
                }
            }

            // 处理连接请求
            if (StompCommand.CONNECT.equals(command)) {
                logger.info("【STOMP拦截器】STOMP连接请求 - 会话ID: {}", sessionId);
                logger.info("【STOMP拦截器】连接头信息: {}", accessor.getMessageHeaders());

                // 从会话属性中获取用户信息
                if (accessor.getSessionAttributes() != null) {
                    Object userIdObj = accessor.getSessionAttributes().get("userId");
                    Object usernameObj = accessor.getSessionAttributes().get("username");

                    if (userIdObj != null && usernameObj != null) {
                        final String username = usernameObj.toString();
                        final Long userId = userIdObj instanceof Long ?
                            (Long) userIdObj : Long.valueOf(userIdObj.toString());

                        logger.info("【STOMP拦截器】为STOMP会话设置用户信息 - 用户名: {}, 用户ID: {}", username, userId);

                        // 创建自定义Principal对象
                        accessor.setUser(new Principal() {
                            @Override
                            public String getName() {
                                return username;
                            }

                            @Override
                            public String toString() {
                                return "User(username=" + username + ", userId=" + userId + ")";
                            }
                        });

                        logger.info("【STOMP拦截器】用户Principal已设置 - getName()将返回: {}", username);
                    } else {
                        logger.warn("【STOMP拦截器】STOMP连接请求中未找到用户信息 - 会话ID: {}", sessionId);
                        logger.warn("【STOMP拦截器】会话属性: {}", accessor.getSessionAttributes());
                    }
                } else {
                    logger.warn("【STOMP拦截器】STOMP连接请求中没有会话属性 - 会话ID: {}", sessionId);
                }
            }
        }

        return message;
    }
}
