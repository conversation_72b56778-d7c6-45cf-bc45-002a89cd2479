/**
 * WebSocket 客户端工具类
 */
class WebSocketClient {
    constructor(userId) {
        this.userId = userId;
        this.stompClient = null;
        this.connected = false;
        this.heartbeatInterval = null;
        this.reconnectTimeout = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000; // 重连延迟，初始5秒
    }

    /**
     * 连接到WebSocket服务器
     */
    connect() {
        const socket = new SockJS('/ws');
        this.stompClient = Stomp.over(socket);

        // 禁用调试日志
        this.stompClient.debug = null;

        const that = this;

        this.stompClient.connect({}, function (frame) {
            console.log('WebSocket连接成功: ' + frame);
            that.connected = true;
            that.reconnectAttempts = 0;

            // 发送上线消息
            that.sendOnlineStatus();

            // 订阅在线状态更新
            that.stompClient.subscribe('/topic/onlineStatus', function (message) {
                console.log('收到在线状态更新: ' + message.body);
                // 这里可以更新UI显示
            });

            // 订阅私人消息 - 使用标准路径 /user/queue/private
            that.stompClient.subscribe('/user/queue/private', function (message) {
                console.log('收到私人消息: ' + message.body);
                // 处理接收到的消息
                const receivedMessage = JSON.parse(message.body);
                // 您可以在这里调用函数来处理和显示消息
            });

            // 启动心跳机制
            that.startHeartbeat();

            // 触发连接成功回调（如果需要）
            if (typeof that.onConnected === 'function') {
                that.onConnected();
            }
        }, function (error) {
            console.error('WebSocket连接错误: ', error);
            that.connected = false;

            // 尝试重新连接
            that.scheduleReconnect();

            // 触发连接错误回调（如果需要）
            if (typeof that.onError === 'function') {
                that.onError(error);
            }
        });
    }

    /**
     * 断开WebSocket连接
     */
    disconnect() {
        if (this.stompClient !== null && this.connected) {
            // 发送下线消息
            this.sendOfflineStatus();

            // 停止心跳
            this.stopHeartbeat();

            // 断开连接
            this.stompClient.disconnect(function() {
                console.log('WebSocket已断开连接');
            });

            this.connected = false;
        }
    }

    /**
     * 发送用户上线状态
     */
    sendOnlineStatus() {
        if (this.stompClient && this.connected) {
            this.stompClient.send('/app/user/online', {}, this.userId);
            console.log('发送上线状态: ' + this.userId);
        }
    }

    /**
     * 发送用户下线状态
     */
    sendOfflineStatus() {
        if (this.stompClient && this.connected) {
            this.stompClient.send('/app/user/offline', {}, this.userId);
            console.log('发送下线状态: ' + this.userId);
        }
    }

    /**
     * 启动心跳机制
     */
    startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }

        const that = this;
        // 每25秒发送一次心跳
        this.heartbeatInterval = setInterval(function() {
            that.sendHeartbeat();
        }, 25000);
    }

    /**
     * 停止心跳机制
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * 发送心跳消息
     */
    sendHeartbeat() {
        if (this.stompClient && this.connected) {
            try {
                this.stompClient.send('/app/user/heartbeat', {}, '');
                console.log('发送心跳...');
            } catch (error) {
                console.error('发送心跳失败:', error);
                this.connected = false;
                this.scheduleReconnect();
            }
        }
    }

    /**
     * 安排重新连接
     */
    scheduleReconnect() {
        // 清除已有的重连计划
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
        }

        // 停止心跳
        this.stopHeartbeat();

        const that = this;
        this.reconnectAttempts++;

        // 检查是否超过最大重试次数
        if (this.reconnectAttempts <= this.maxReconnectAttempts) {
            // 指数退避重连策略
            const delay = Math.min(30000, this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1));

            console.log(`尝试在 ${delay/1000} 秒后重新连接... (尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

            this.reconnectTimeout = setTimeout(function() {
                console.log('正在重新连接...');
                that.connect();
            }, delay);
        } else {
            console.error('已达到最大重连尝试次数，请刷新页面手动重连');
            // 触发重连失败回调（如果需要）
            if (typeof that.onReconnectFailed === 'function') {
                that.onReconnectFailed();
            }
        }
    }

    /**
     * 发送私人消息
     */
    sendPrivateMessage(receiverId, message) {
        if (this.stompClient && this.connected) {
            const messageObj = {
                senderId: this.userId,
                receiverId: receiverId,
                message: message,
                timestamp: new Date().getTime()
            };

            this.stompClient.send('/app/privateMessage', {}, JSON.stringify(messageObj));
            console.log('发送私人消息：', messageObj);
            return true;
        } else {
            console.error('无法发送消息，WebSocket未连接');
            return false;
        }
    }

    /**
     * 检查用户在线状态
     */
    checkUserOnlineStatus(targetUserId) {
        if (this.stompClient && this.connected) {
            this.stompClient.send(`/app/checkOnlineStatus/${targetUserId}`, {}, '');
            console.log('检查用户在线状态: ' + targetUserId);
        }
    }
}

// 导出客户端
export default WebSocketClient;