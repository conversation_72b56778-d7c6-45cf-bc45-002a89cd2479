<mxfile host="65bd71144e">
    <diagram id="C5RBs43oDa-KdzZeNtuy" name="普通用户用例图">
        <mxGraphModel dx="1223" dy="871" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
                <mxCell id="0" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;普通用户功能&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="364" y="40" width="100" height="30" as="geometry" />
                </mxCell>
                <mxCell id="1" value="普通用户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="120" y="280" width="30" height="60" as="geometry" />
                </mxCell>
                <mxCell id="2" value="发布物品信息" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="240" y="120" width="120" height="40" as="geometry" />
                </mxCell>
                <mxCell id="3" value="查询物品信息" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="240" y="170" width="120" height="40" as="geometry" />
                </mxCell>
                <mxCell id="4" value="相似物品匹配" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="240" y="220" width="120" height="40" as="geometry" />
                </mxCell>
                <mxCell id="5" value="即时沟通" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="240" y="270" width="120" height="40" as="geometry" />
                </mxCell>
                <mxCell id="6" value="接收通知" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="240" y="320" width="120" height="40" as="geometry" />
                </mxCell>
                <mxCell id="7" value="管理发布信息" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="240" y="370" width="120" height="40" as="geometry" />
                </mxCell>
                <mxCell id="8" value="查看历史记录" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="240" y="420" width="120" height="40" as="geometry" />
                </mxCell>
                <mxCell id="9" value="" style="endArrow=none;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="1" target="2">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="" style="endArrow=none;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="1" target="3">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="170" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="" style="endArrow=none;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="1" target="4">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="220" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="" style="endArrow=none;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="1" target="5">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="270" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="" style="endArrow=none;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="1" target="6">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="320" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="" style="endArrow=none;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="1" target="7">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="370" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="" style="endArrow=none;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="1" target="8">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="420" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;图3-5 普通用户用例图&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="364" y="500" width="160" height="30" as="geometry" />
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile> 