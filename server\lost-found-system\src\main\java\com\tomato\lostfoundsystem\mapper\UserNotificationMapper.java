package com.tomato.lostfoundsystem.mapper;


import com.tomato.lostfoundsystem.entity.Notification;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.data.repository.query.Param;
import org.springframework.http.ResponseEntity;

import java.util.List;

@Mapper
public interface UserNotificationMapper {

    void insertNotification(Notification notification);

    List<Notification> getNotificationsByStatus(Long userId, String status);

    void updateNotificationStatus(@Param("notificationId") Long notificationId, @Param("status") String status);

    void deleteNotification(Long notificationId);

    Integer getUnreadCount(Long userId);
}
