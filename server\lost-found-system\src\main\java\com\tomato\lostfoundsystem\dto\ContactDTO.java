package com.tomato.lostfoundsystem.dto;


import com.tomato.lostfoundsystem.entity.MessageAttachment;
import com.tomato.lostfoundsystem.enums.MessageType;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ContactDTO {
    private Long contactId;               // 联系人ID
    private String name;           // 联系人姓名
    private String avatarText;     // 头像首字母
    private String avatar;         // 头像URL
    private String lastMessage;    // 最后一条消息的内容
    private String lastTime;       // 最后一条消息的时间
    private int unreadCount;       // 未读消息数量
    private MessageType messageType; // 最后一条消息类型（枚举：TEXT, IMAGE, AUDIO, VIDEO 等）
    private Integer audioDuration; // 音频时长（秒）
    private Integer videoDuration; // 视频时长（秒）
    private MessageAttachmentDTO messageAttachmentDTO; // 单个附件（向后兼容）
    private List<MessageAttachmentDTO> messageAttachments; // 多个附件
}

