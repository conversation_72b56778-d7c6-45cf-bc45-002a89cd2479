```mermaid
graph TD
    %% 定义主要模块
    subgraph 查询入口模块["查询入口模块<br>(Query Entry)"]
        A1[用户界面 totalPages]
        A2[查询参数 params]
    end

    subgraph 用户查询模块["用户查询模块<br>(User Query)"]
        B1[失物查询入口]
        B2[拾物查询入口]
        B3[我的发布查询]
        B4[匹配历史查询]
    end

    subgraph 高级查询策略模块["高级查询策略模块<br>(Advanced Query Strategy)"]
        C1[关键词查询策略]
        C2[位置查询策略]
        C3[时间查询策略]
        C4[状态查询策略]
        C5[分页查询策略]
    end

    subgraph 查询处理模块["查询处理模块<br>(Query Processing)"]
        D1[构建查询SQL]
        D2[参数预处理]
        D3[执行数据库查询]
        D4[结果分页处理]
    end

    subgraph 结果处理模块["结果处理模块<br>(Result Processing)"]
        E1[数据转换]
        E2[结果过滤]
        E3[结果排序]
        E4[结果聚合]
    end

    subgraph 数据库模块["数据库模块<br>(Database)"]
        F1[lost_items表]
        F2[found_items表]
        F3[match_history表]
    end

    %% 定义连接关系
    A1 --> B1
    A1 --> B2
    A1 --> B3
    A1 --> B4
    A2 --> C1
    A2 --> C2
    A2 --> C3
    A2 --> C4
    A2 --> C5

    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> C4
    B1 --> C5
    B2 --> C1
    B2 --> C2
    B2 --> C3
    B2 --> C4
    B2 --> C5
    B3 --> C1
    B3 --> C4
    B3 --> C5
    B4 --> C5

    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C5 --> D1
    C1 --> D2
    C2 --> D2
    C3 --> D2
    C4 --> D2
    C5 --> D2

    D1 --> D3
    D2 --> D3
    D3 --> D4
    D3 --> E1

    D4 --> E3
    E1 --> E2
    E2 --> E3
    E3 --> E4

    D3 --> F1
    D3 --> F2
    D3 --> F3

    %% 样式设置
    classDef queryEntry fill:#f9d5e5,stroke:#333,stroke-width:1px;
    classDef userQuery fill:#eeeeee,stroke:#333,stroke-width:1px;
    classDef queryStrategy fill:#d0f0c0,stroke:#333,stroke-width:1px;
    classDef queryProcessing fill:#b5dcf5,stroke:#333,stroke-width:1px;
    classDef resultProcessing fill:#f5e6b5,stroke:#333,stroke-width:1px;
    classDef database fill:#d8bfd8,stroke:#333,stroke-width:1px;

    class A1,A2 queryEntry;
    class B1,B2,B3,B4 userQuery;
    class C1,C2,C3,C4,C5 queryStrategy;
    class D1,D2,D3,D4 queryProcessing;
    class E1,E2,E3,E4 resultProcessing;
    class F1,F2,F3 database;
```

# 失物招领系统多维查询组合策略说明

## 1. 查询入口模块 (Query Entry)

- **用户界面 (totalPages)**: 提供用户交互界面，包括失物列表、拾物列表、我的发布和匹配历史等页面
- **查询参数 (params)**: 收集和处理用户输入的查询参数，如关键词、位置、时间范围等

## 2. 用户查询模块 (User Query)

- **失物查询入口**: 处理用户对失物信息的查询请求
- **拾物查询入口**: 处理用户对拾物信息的查询请求
- **我的发布查询**: 查询当前用户发布的失物和拾物信息
- **匹配历史查询**: 查询用户的匹配历史记录

## 3. 高级查询策略模块 (Advanced Query Strategy)

- **关键词查询策略**: 基于物品名称和描述的模糊查询
- **位置查询策略**: 基于丢失/拾取地点的模糊查询
- **时间查询策略**: 支持多种时间范围查询（今天、昨天、三天内、一周内、一个月内、自定义时间段）
- **状态查询策略**: 基于物品状态的精确查询（失物：未找回/已找回；拾物：未认领/已归还）
- **分页查询策略**: 支持分页查询，提高查询效率和用户体验

## 4. 查询处理模块 (Query Processing)

- **构建查询SQL**: 根据查询策略动态构建SQL语句
- **参数预处理**: 对查询参数进行验证、转换和格式化
- **执行数据库查询**: 调用MyBatis执行SQL查询
- **结果分页处理**: 使用PageHelper处理分页逻辑

## 5. 结果处理模块 (Result Processing)

- **数据转换**: 将数据库实体转换为前端所需的DTO对象
- **结果过滤**: 根据业务规则过滤查询结果
- **结果排序**: 对查询结果进行排序（如按时间降序）
- **结果聚合**: 聚合查询结果，添加额外信息（如用户名、头像等）

## 6. 数据库模块 (Database)

- **lost_items表**: 存储失物信息
- **found_items表**: 存储拾物信息
- **match_history表**: 存储匹配历史记录

## 查询流程

1. 用户通过界面选择查询类型（失物/拾物/我的发布/匹配历史）
2. 用户输入查询参数（关键词、位置、时间范围、状态等）
3. 系统根据查询类型和参数组合相应的查询策略
4. 查询处理模块构建SQL语句并执行查询
5. 结果处理模块对查询结果进行处理和转换
6. 将处理后的结果返回给用户界面显示

## 特点

1. **多维度查询**: 支持多种查询条件的组合，满足不同场景的查询需求
2. **灵活的时间查询**: 支持多种时间范围选择，方便用户快速筛选
3. **高效的分页机制**: 使用PageHelper实现高效分页，提升查询性能
4. **统一的结果格式**: 使用统一的Result对象封装查询结果，便于前端处理
5. **良好的扩展性**: 模块化设计使系统易于扩展新的查询功能
