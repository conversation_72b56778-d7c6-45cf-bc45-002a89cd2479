-- 重置所有拾物的审核状态为待审核
UPDATE found_items SET audit_status = 'PENDING' WHERE audit_status = 'APPROVED';

-- 重置所有失物的审核状态为待审核
UPDATE lost_items SET audit_status = 'PENDING' WHERE audit_status = 'APPROVED';

-- 查看重置后的状态
SELECT 'FOUND' as type, audit_status, COUNT(*) as count FROM found_items GROUP BY audit_status
UNION ALL
SELECT 'LOST' as type, audit_status, COUNT(*) as count FROM lost_items GROUP BY audit_status;
