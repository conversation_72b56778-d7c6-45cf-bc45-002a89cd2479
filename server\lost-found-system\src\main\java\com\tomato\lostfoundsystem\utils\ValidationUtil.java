package com.tomato.lostfoundsystem.utils;

import java.util.regex.Pattern;

public class ValidationUtil {

    // 邮箱格式正则
    private static final String EMAIL_REGEX = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";

    // 手机号格式正则（以中国手机号为例）
    private static final String PHONE_REGEX = "^1[3-9]\\d{9}$";

    // 验证邮箱格式
    public static boolean isValidEmail(String email) {
        return email != null && Pattern.matches(EMAIL_REGEX, email);
    }

    // 验证手机号格式
    public static boolean isValidPhone(String phone) {
        return phone != null && Pattern.matches(PHONE_REGEX, phone);
    }
}
