<template>
  <div class="chat-input">
    <div class="toolbar-top">
      <el-tooltip content="表情" placement="top">
        <el-button circle @click="toggleEmojiPicker">
          <el-icon><ChatRound /></el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip content="图片" placement="top">
        <el-button circle @click="triggerImageUpload">
          <el-icon><Picture /></el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip content="文件" placement="top">
        <el-button circle @click="triggerFileUpload">
          <el-icon><Document /></el-icon>
        </el-button>
      </el-tooltip>
    </div>

    <div class="input-row">
      <div class="input-box" ref="inputBoxRef">
        <el-input
          v-model="inputValue"
          type="textarea"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 4 }"
          placeholder="输入消息..."
          resize="none"
          @keydown.enter.prevent="handleEnterPress"
          class="wechat-textarea"
        />

        <!-- 表情选择器 -->
        <div v-if="showEmojiPicker" class="emoji-picker" ref="emojiPickerRef">
          <div class="emoji-grid">
            <div
              v-for="emoji in emojiList"
              :key="emoji"
              class="emoji-item"
              @click="insertEmoji(emoji)"
            >
              {{ emoji }}
            </div>
          </div>
        </div>
      </div>

      <el-button
        class="send-btn"
        :disabled="!canSend || sending"
        :loading="sending"
        @click="sendMessage"
      >
        <el-icon><Position /></el-icon>
      </el-button>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="imageInputRef"
      type="file"
      accept="image/jpeg,image/png,image/gif,image/webp"
      style="display: none"
      @change="handleImageSelect"
    />
    <input
      ref="fileInputRef"
      type="file"
      style="display: none"
      @change="handleFileSelect"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ChatRound, Picture, Document, Position } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  sending: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'send', 'image-select', 'file-select'])

// 输入值
const inputValue = ref(props.modelValue)

// 监听输入值变化
watch(() => props.modelValue, (newValue) => {
  inputValue.value = newValue
})

// 监听本地输入值变化
watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 表情选择器
const showEmojiPicker = ref(false)
const emojiPickerRef = ref(null)
const inputBoxRef = ref(null)
const imageInputRef = ref(null)
const fileInputRef = ref(null)

// 表情列表
const emojiList = [
  '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
  '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
  '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
  '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
  '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
  '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
  '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯',
  '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
  '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈',
  '👿', '👹', '👺', '🤡', '💩', '👻', '💀', '☠️', '👽', '👾'
]

// 切换表情选择器
const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
}

// 插入表情
const insertEmoji = (emoji) => {
  inputValue.value += emoji
  emit('update:modelValue', inputValue.value)
}

// 处理点击外部关闭表情选择器
const handleClickOutside = (event) => {
  if (
    showEmojiPicker.value &&
    emojiPickerRef.value &&
    !emojiPickerRef.value.contains(event.target) &&
    !inputBoxRef.value.contains(event.target)
  ) {
    showEmojiPicker.value = false
  }
}

// 触发图片上传
const triggerImageUpload = () => {
  imageInputRef.value.click()
}

// 触发文件上传
const triggerFileUpload = () => {
  fileInputRef.value.click()
}

// 处理图片选择
const handleImageSelect = (event) => {
  emit('image-select', event)
}

// 处理文件选择
const handleFileSelect = (event) => {
  emit('file-select', event)
}

// 处理回车键发送消息
const handleEnterPress = (event) => {
  // 如果按下Shift+Enter，则插入换行符
  if (event.shiftKey) {
    return
  }

  // 否则发送消息
  sendMessage()
}

// 发送消息
const sendMessage = () => {
  if (!canSend.value || props.sending) return

  emit('send')
}

// 是否可以发送消息
const canSend = computed(() => {
  return !props.disabled && inputValue.value.trim().length > 0
})

// 组件挂载时添加事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.chat-input {
  background: #f0f2f5;
  padding: 8px 16px 12px;
  margin: 0;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #e9edef;
}

.toolbar-top {
  display: flex;
  flex-direction: row;
  gap: 6px;
  align-items: center;
  margin-bottom: 6px;
  padding: 0 4px;
}

.toolbar-top :deep(.el-button) {
  color: #54656f;
  border: none;
  background: transparent;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s;
  font-size: 18px;
}

.toolbar-top :deep(.el-button:hover) {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}

.toolbar-top :deep(.el-button:active) {
  transform: scale(0.95);
}

.input-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-box {
  flex: 1;
  position: relative;
  background: #ffffff;
  border-radius: 24px;
  min-height: 42px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  transition: all 0.2s;
}

.wechat-textarea {
  width: 100%;
}

.wechat-textarea :deep(.el-textarea__inner) {
  border: none;
  background: transparent;
  padding: 10px 16px;
  font-size: 15px;
  resize: none;
  box-shadow: none;
}

.wechat-textarea :deep(.el-textarea__inner:focus) {
  box-shadow: none;
}

.send-btn {
  min-width: 42px;
  height: 42px;
  border-radius: 50%;
  font-size: 20px;
  background: #00a884;
  color: #fff;
  border: none;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn:hover {
  background: #008f72;
  transform: scale(1.05);
}

.send-btn:active {
  transform: scale(0.95);
}

.send-btn:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

.emoji-picker {
  position: absolute;
  bottom: 100%;
  left: 0;
  width: 320px;
  max-height: 200px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  padding: 10px;
  margin-bottom: 8px;
  z-index: 10;
  overflow-y: auto;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 5px;
}

.emoji-item {
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.emoji-item:hover {
  background-color: #f0f2f5;
}
</style>
