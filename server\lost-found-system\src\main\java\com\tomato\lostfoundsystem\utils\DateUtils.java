package com.tomato.lostfoundsystem.utils;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;


@Component
public class DateUtils {

    /**
     * 根据时间范围（今天、昨天、三天内、一周内、一个月内）或自定义时间，获取起始和结束时间
     */
    public LocalDateTime[] getDateRange(String timeRange, String startDate, String endDate) {
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = LocalDateTime.now();

        // 根据用户选择的时间范围处理日期
        if (timeRange != null) {
            switch (timeRange) {
                case "today":
                    startDateTime = LocalDateTime.now().toLocalDate().atStartOfDay();
                    break;
                case "yesterday":
                    startDateTime = LocalDateTime.now().minusDays(1).toLocalDate().atStartOfDay(); // 2025-04-06 00:00
                    endDateTime = startDateTime.plusDays(1).minusNanos(1); // 2025-04-06 23:59:59.999999999
                    break;
                case "lastThreeDays":
                    startDateTime = endDateTime.minusDays(3).toLocalDate().atStartOfDay(); // 3天前的00:00
                    endDateTime = endDateTime.minusNanos(1); // 当前时间的23:59:59.999999999
                    break;
                case "lastWeek":
                    startDateTime = endDateTime.minusWeeks(1).toLocalDate().atStartOfDay(); // 一周前的00:00
                    endDateTime = endDateTime.minusNanos(1); // 当前时间的23:59:59.999999999
                    break;
                case "lastMonth":
                    startDateTime = endDateTime.minusMonths(1).toLocalDate().atStartOfDay(); // 一个月前的00:00
                    endDateTime = endDateTime.minusNanos(1); // 当前时间的23:59:59.999999999
                    break;
                default:
                    // 自定义时间段
                    if (startDate != null && !startDate.isEmpty()) {
                        startDateTime = LocalDateTime.parse(startDate);
                    }
                    if (endDate != null && !endDate.isEmpty()) {
                        endDateTime = LocalDateTime.parse(endDate);
                    }
                    break;
            }
        }

        return new LocalDateTime[]{startDateTime, endDateTime};
    }

}
