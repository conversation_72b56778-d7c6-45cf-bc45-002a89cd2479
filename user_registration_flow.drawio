<mxfile host="app.diagrams.net" modified="2023-06-15T12:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64)" etag="your-etag" version="14.7.7" type="device">
  <diagram id="registration-flow-diagram" name="用户注册处理流程图">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 开始节点 -->
        <mxCell id="start" value="开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="360" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 前端表单提交 -->
        <mxCell id="form_submit" value="前端表单提交&#xa;(用户名、密码、手机号/邮箱、验证码)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="360" y="140" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 请求到达后端 -->
        <mxCell id="request_backend" value="请求到达后端&#xa;/api/user/register" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="360" y="240" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 验证码拦截器 -->
        <mxCell id="verify_interceptor" value="验证码拦截器&#xa;VerifyCodeInterceptor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="360" y="340" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 验证码校验 -->
        <mxCell id="code_validation" value="验证码校验" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="360" y="440" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- 返回验证码错误 -->
        <mxCell id="return_code_error" value="返回验证码错误" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="160" y="450" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 请求到达控制器 -->
        <mxCell id="request_controller" value="请求到达控制器&#xa;UserController" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="360" y="560" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 参数校验 -->
        <mxCell id="param_validation" value="参数校验&#xa;(用户名、密码、手机号/邮箱)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="360" y="660" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 参数校验结果 -->
        <mxCell id="param_validation_result" value="参数校验通过?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="360" y="760" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- 返回参数错误 -->
        <mxCell id="return_param_error" value="返回参数错误" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="160" y="770" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 创建用户对象 -->
        <mxCell id="create_user" value="创建用户对象" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="360" y="880" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 密码加密 -->
        <mxCell id="password_encrypt" value="密码加密&#xa;(BCrypt算法)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="360" y="980" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 写入数据库 -->
        <mxCell id="save_to_db" value="写入数据库" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="360" y="1080" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 写入结果 -->
        <mxCell id="save_result" value="写入成功?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="360" y="1180" width="120" height="80" as="geometry" />
        </mxCell>
        
        <!-- 返回注册失败 -->
        <mxCell id="return_save_error" value="返回注册失败" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="160" y="1190" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 返回注册成功 -->
        <mxCell id="return_success" value="返回注册成功" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="360" y="1300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 结束节点 -->
        <mxCell id="end" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="360" y="1400" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="start_to_form" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="start" target="form_submit">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="form_to_backend" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="form_submit" target="request_backend">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="backend_to_interceptor" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="request_backend" target="verify_interceptor">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="interceptor_to_validation" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="verify_interceptor" target="code_validation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="validation_to_error" value="验证码无效" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="code_validation" target="return_code_error">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="validation_to_controller" value="验证码有效" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="code_validation" target="request_controller">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="controller_to_param" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="request_controller" target="param_validation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="param_to_result" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="param_validation" target="param_validation_result">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="result_to_param_error" value="否" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="param_validation_result" target="return_param_error">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="result_to_create" value="是" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="param_validation_result" target="create_user">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="create_to_encrypt" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="create_user" target="password_encrypt">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="encrypt_to_save" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="password_encrypt" target="save_to_db">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="save_to_result" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="save_to_db" target="save_result">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="result_to_save_error" value="否" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="save_result" target="return_save_error">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="result_to_success" value="是" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="save_result" target="return_success">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="success_to_end" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="return_success" target="end">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="error_to_end1" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="return_code_error" target="end">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="220" y="1430" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <mxCell id="error_to_end2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="return_param_error">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="220" y="1430" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="error_to_end3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="return_save_error">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="220" y="1430" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 注释框 -->
        <mxCell id="note1" value="1. 前端表单提交用户注册信息" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;align=left;size=20;" vertex="1" parent="1">
          <mxGeometry x="520" y="140" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="note2" value="2. 验证码拦截器拦截请求，&#xa;   从Redis获取验证码进行校验" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;align=left;size=20;" vertex="1" parent="1">
          <mxGeometry x="520" y="340" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="note3" value="3. 参数校验：&#xa;   - 用户名是否已存在&#xa;   - 手机号/邮箱是否已注册&#xa;   - 密码是否符合要求" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;align=left;size=20;" vertex="1" parent="1">
          <mxGeometry x="520" y="660" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="note4" value="4. 使用BCrypt算法加密密码，&#xa;   确保密码安全存储" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;align=left;size=20;" vertex="1" parent="1">
          <mxGeometry x="520" y="980" width="200" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="note5" value="5. 将用户信息写入数据库，&#xa;   设置默认角色为普通用户" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;align=left;size=20;" vertex="1" parent="1">
          <mxGeometry x="520" y="1080" width="200" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="note1_to_form" value="" style="endArrow=none;dashed=1;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="note1" target="form_submit">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="note2_to_interceptor" value="" style="endArrow=none;dashed=1;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="note2" target="verify_interceptor">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="note3_to_param" value="" style="endArrow=none;dashed=1;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="note3" target="param_validation">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="note4_to_encrypt" value="" style="endArrow=none;dashed=1;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="note4" target="password_encrypt">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="note5_to_save" value="" style="endArrow=none;dashed=1;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="note5" target="save_to_db">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
