/**
 * 格式化日期时间
 * @param {string | number | Date} date 日期时间
 * @param {string} format 格式化模式，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化为相对时间
 * @param {string | number | Date} date 日期时间
 * @returns {string} 相对时间描述
 */
function formatRelativeTime(date) {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  
  // 转换为秒
  const seconds = Math.floor(diff / 1000)
  
  // 小于1分钟
  if (seconds < 60) {
    return '刚刚'
  }
  
  // 小于1小时
  const minutes = Math.floor(seconds / 60)
  if (minutes < 60) {
    return `${minutes}分钟前`
  }
  
  // 小于1天
  const hours = Math.floor(minutes / 60)
  if (hours < 24) {
    return `${hours}小时前`
  }
  
  // 小于30天
  const days = Math.floor(hours / 24)
  if (days < 30) {
    return `${days}天前`
  }
  
  // 小于12个月
  const months = Math.floor(days / 30)
  if (months < 12) {
    return `${months}个月前`
  }
  
  // 大于等于12个月
  const years = Math.floor(months / 12)
  return `${years}年前`
}

/**
 * 格式化为友好的日期时间
 * 如果是今天，显示"今天 HH:mm"
 * 如果是昨天，显示"昨天 HH:mm"
 * 如果是前天，显示"前天 HH:mm"
 * 如果是今年，显示"MM-DD HH:mm"
 * 其他显示完整的"YYYY-MM-DD HH:mm"
 * @param {string | number | Date} date 日期时间
 * @returns {string} 友好的日期时间描述
 */
function formatFriendlyDate(date) {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const beforeYesterday = new Date(today.getTime() - 48 * 60 * 60 * 1000)
  
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const time = `${hours}:${minutes}`
  
  if (d >= today) {
    return `今天 ${time}`
  }
  
  if (d >= yesterday) {
    return `昨天 ${time}`
  }
  
  if (d >= beforeYesterday) {
    return `前天 ${time}`
  }
  
  if (d.getFullYear() === now.getFullYear()) {
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    return `${month}-${day} ${time}`
  }
  
  return formatDate(date, 'YYYY-MM-DD HH:mm')
}

export {
  formatDate,
  formatRelativeTime,
  formatFriendlyDate
} 