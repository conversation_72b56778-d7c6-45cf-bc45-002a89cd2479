package com.tomato.lostfoundsystem.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * OSS工具类，提供共享的OSS操作方法
 * 用于减少AliyunOSSUtil和AliyunOSSAvatarUtil之间的代码重复
 */
@Slf4j
public class OSSUtils {

    /**
     * 创建OSS客户端
     *
     * @param endpoint        OSS端点
     * @param accessKeyId     访问密钥ID
     * @param accessKeySecret 访问密钥密码
     * @return OSS客户端实例
     */
    public static OSS createOSSClient(String endpoint, String accessKeyId, String accessKeySecret) {
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }

    /**
     * 获取文件URL
     *
     * @param bucketName OSS存储空间名称
     * @param endpoint   OSS端点
     * @param objectKey  对象键
     * @param cdnDomain  CDN域名（可选，已禁用）
     * @return 文件访问URL
     */
    public static String getFileUrl(String bucketName, String endpoint, String objectKey, String cdnDomain) {
        // 直接使用OSS域名，不再使用CDN
        return "https://" + bucketName + "." + endpoint + "/" + objectKey;
    }

    /**
     * 从URL中提取OSS对象键
     *
     * @param url        文件URL
     * @param bucketName OSS存储空间名称
     * @param endpoint   OSS端点
     * @param cdnDomain  CDN域名（可选）
     * @return OSS对象键
     */
    public static String extractObjectKeyFromUrl(String url, String bucketName, String endpoint, String cdnDomain) {
        if (url == null || url.isEmpty()) {
            return null;
        }

        // 处理CDN URL
        if (cdnDomain != null && !cdnDomain.isEmpty()) {
            // 标准化CDN域名（移除尾部斜杠）
            String normalizedCdnDomain = cdnDomain.endsWith("/")
                ? cdnDomain.substring(0, cdnDomain.length() - 1)
                : cdnDomain;

            if (url.startsWith(normalizedCdnDomain)) {
                // 提取对象键（确保以斜杠开头）
                String path = url.substring(normalizedCdnDomain.length());
                return path.startsWith("/") ? path.substring(1) : path;
            }
        }

        // 处理OSS URL
        String ossPrefix = "https://" + bucketName + "." + endpoint + "/";
        if (url.startsWith(ossPrefix)) {
            return url.substring(ossPrefix.length());
        }

        return null;
    }

    /**
     * 上传文件到OSS
     *
     * @param ossClient    OSS客户端
     * @param bucketName   存储空间名称
     * @param objectKey    对象键
     * @param inputStream  输入流
     * @param contentType  内容类型
     * @param contentLength 内容长度
     * @throws IOException 如果上传过程中发生错误
     */
    public static void putObject(OSS ossClient, String bucketName, String objectKey,
                                InputStream inputStream, String contentType, long contentLength) throws IOException {
        try {
            ObjectMetadata metadata = new ObjectMetadata();
            if (contentType != null) {
                metadata.setContentType(contentType);
            }
            metadata.setContentLength(contentLength);
            ossClient.putObject(bucketName, objectKey, inputStream, metadata);
            log.info("文件上传成功: {}", objectKey);
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new IOException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证图像文件
     *
     * @param file    图像文件
     * @param maxSize 最大文件大小（字节）
     * @throws IOException 如果文件无效
     */
    public static void validateImageFile(MultipartFile file, long maxSize) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IOException("文件为空");
        }

        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new IOException("文件不是图像类型");
        }

        // 验证文件大小
        if (file.getSize() > maxSize) {
            throw new IOException("图像文件大小不能超过" + (maxSize / 1024 / 1024) + "MB");
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 文件扩展名（包含点，如".jpg"）
     */
    public static String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty() || !filename.contains(".")) {
            return ".jpg"; // 默认扩展名
        }
        return filename.substring(filename.lastIndexOf("."));
    }

    /**
     * 删除OSS对象
     *
     * @param ossClient  OSS客户端
     * @param bucketName 存储空间名称
     * @param objectKey  对象键
     * @return 是否删除成功
     */
    public static boolean deleteObject(OSS ossClient, String bucketName, String objectKey) {
        try {
            ossClient.deleteObject(bucketName, objectKey);
            log.info("文件删除成功: {}", objectKey);
            return true;
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
