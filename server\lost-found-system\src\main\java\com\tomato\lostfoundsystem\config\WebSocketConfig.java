package com.tomato.lostfoundsystem.config;

import com.tomato.lostfoundsystem.interceptors.StompChannelInterceptor;
import com.tomato.lostfoundsystem.interceptors.WebSocketHandshakeInterceptor;
import com.tomato.lostfoundsystem.websocket.OnlineWebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

@Configuration
@EnableWebSocketMessageBroker
@Slf4j

public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    private final WebSocketHandshakeInterceptor webSocketHandshakeInterceptor;
    private final StompChannelInterceptor stompChannelInterceptor;

    public WebSocketConfig(WebSocketHandshakeInterceptor webSocketHandshakeInterceptor,
                          StompChannelInterceptor stompChannelInterceptor) {
        this.webSocketHandshakeInterceptor = webSocketHandshakeInterceptor;
        this.stompChannelInterceptor = stompChannelInterceptor;
    }

    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        // 添加STOMP通道拦截器，用于处理用户认证
        registration.interceptors(stompChannelInterceptor);
    }

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // 配置消息代理
        config.enableSimpleBroker("/topic", "/queue")  // 消息代理前缀
              .setHeartbeatValue(new long[]{25000, 25000})  // 设置心跳间隔为25秒，与application.yml和前端保持一致
              .setTaskScheduler(taskScheduler());

        config.setApplicationDestinationPrefixes("/app");  // 消息处理请求前缀
        config.setUserDestinationPrefix("/user");  // 用户目标前缀

        log.info("【WebSocket配置】消息代理已配置:");
        log.info("【WebSocket配置】- 广播前缀: /topic");
        log.info("【WebSocket配置】- 点对点前缀: /queue");
        log.info("【WebSocket配置】- 应用前缀: /app");
        log.info("【WebSocket配置】- 用户前缀: /user");
        log.info("【WebSocket配置】- 心跳间隔: 25秒");
    }

    @Bean
    public ThreadPoolTaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(2);
        scheduler.setThreadNamePrefix("ws-heartbeat-");
        scheduler.initialize();
        return scheduler;
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // 注册统一的WebSocket端点 - 只使用/ws作为唯一端点
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*")  // 允许所有来源，简化开发调试
                .withSockJS()
                .setInterceptors(webSocketHandshakeInterceptor);  // 将拦截器注册到 WebSocket 握手处理器

        // 添加日志
        log.info("【WebSocket配置】WebSocket端点已注册:");
        log.info("【WebSocket配置】- 统一端点: /ws");
        log.info("【WebSocket配置】- 允许的来源: *");
        log.info("【WebSocket配置】- SockJS支持: 已启用");
        log.info("【WebSocket配置】- 握手拦截器: {}", webSocketHandshakeInterceptor.getClass().getSimpleName());
    }
}
