package com.tomato.lostfoundsystem.service;

import com.github.pagehelper.PageInfo;
import com.tomato.lostfoundsystem.dto.ChatMessageDTO;
import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.entity.ChatMessage;
import org.springframework.data.repository.query.Param;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ChatMessageService {

    // 保存聊天消息
    MessageDTO saveChatMessage(MessageDTO message, MultipartFile file);

    // 获取用户和目标用户之间的聊天记录
    PageInfo<ChatMessageDTO> getChatHistory(Long userId, Long otherUserId, int page, int size);

    // 标记消息为已读
    void markAsRead(Long messageId, Long userId);

    // 获取消息的已读状态
    Integer getMessageReadStatus(Long messageId, Long userId);

    // 批量标记用户与联系人之间的所有未读消息为已读
    void markAllAsRead(Long contactId, Long userId);
}
