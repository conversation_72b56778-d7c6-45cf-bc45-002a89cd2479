package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.SystemAnnouncementDTO;
import com.tomato.lostfoundsystem.entity.SystemAnnouncement;
import com.tomato.lostfoundsystem.entity.UserAnnouncementRead;
import com.tomato.lostfoundsystem.entity.User;
import com.tomato.lostfoundsystem.mapper.SystemAnnouncementMapper;
import com.tomato.lostfoundsystem.mapper.UserAnnouncementReadMapper;
import com.tomato.lostfoundsystem.mapper.UserMapper;
import com.tomato.lostfoundsystem.dto.NotificationDTO;
import com.tomato.lostfoundsystem.service.NotificationEventService;
import com.tomato.lostfoundsystem.service.SystemAnnouncementService;
import com.tomato.lostfoundsystem.websocket.NotificationWebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统公告服务实现类
 */
@Slf4j
@Service
public class SystemAnnouncementServiceImpl implements SystemAnnouncementService {

    @Autowired
    private SystemAnnouncementMapper systemAnnouncementMapper;

    @Autowired
    private UserAnnouncementReadMapper userAnnouncementReadMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private NotificationWebSocketHandler notificationWebSocketHandler;

    @Autowired
    private NotificationEventService notificationEventService;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    @Transactional
    public Result<Long> createAnnouncement(SystemAnnouncementDTO announcementDTO, Long adminId) {
        try {
            // 检查管理员权限
            User admin = userMapper.findById(adminId);
            if (admin == null || (!admin.getRole().equals("ADMIN") && !admin.getRole().equals("SUPER_ADMIN"))) {
                return Result.fail("无权创建系统公告");
            }

            // 转换DTO为实体
            SystemAnnouncement announcement = new SystemAnnouncement();
            announcement.setTitle(announcementDTO.getTitle());
            announcement.setContent(announcementDTO.getContent());
            announcement.setImportance(announcementDTO.getImportance());
            announcement.setStartTime(LocalDateTime.parse(announcementDTO.getStartTime(), formatter));
            if (announcementDTO.getEndTime() != null && !announcementDTO.getEndTime().isEmpty()) {
                announcement.setEndTime(LocalDateTime.parse(announcementDTO.getEndTime(), formatter));
            }
            announcement.setCreatedBy(adminId);
            announcement.setStatus("DRAFT"); // 默认为草稿状态

            // 保存到数据库
            systemAnnouncementMapper.insertAnnouncement(announcement);

            return Result.success("系统公告创建成功", announcement.getId());
        } catch (Exception e) {
            log.error("创建系统公告失败", e);
            return Result.fail("创建系统公告失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<String> updateAnnouncement(SystemAnnouncementDTO announcementDTO, Long adminId) {
        try {
            // 检查公告是否存在
            SystemAnnouncement existingAnnouncement = systemAnnouncementMapper.selectById(announcementDTO.getId());
            if (existingAnnouncement == null) {
                return Result.fail("系统公告不存在");
            }

            // 检查管理员权限
            User admin = userMapper.findById(adminId);
            if (admin == null || (!admin.getRole().equals("ADMIN") && !admin.getRole().equals("SUPER_ADMIN"))) {
                return Result.fail("无权更新系统公告");
            }

            // 更新公告信息
            existingAnnouncement.setTitle(announcementDTO.getTitle());
            existingAnnouncement.setContent(announcementDTO.getContent());
            existingAnnouncement.setImportance(announcementDTO.getImportance());
            existingAnnouncement.setStartTime(LocalDateTime.parse(announcementDTO.getStartTime(), formatter));
            if (announcementDTO.getEndTime() != null && !announcementDTO.getEndTime().isEmpty()) {
                existingAnnouncement.setEndTime(LocalDateTime.parse(announcementDTO.getEndTime(), formatter));
            } else {
                existingAnnouncement.setEndTime(null);
            }

            // 保存更新
            systemAnnouncementMapper.updateAnnouncement(existingAnnouncement);

            return Result.success("系统公告更新成功");
        } catch (Exception e) {
            log.error("更新系统公告失败", e);
            return Result.fail("更新系统公告失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<String> deleteAnnouncement(Long id, Long adminId) {
        try {
            // 检查公告是否存在
            SystemAnnouncement announcement = systemAnnouncementMapper.selectById(id);
            if (announcement == null) {
                return Result.fail("系统公告不存在");
            }

            // 检查管理员权限
            User admin = userMapper.findById(adminId);
            if (admin == null || (!admin.getRole().equals("ADMIN") && !admin.getRole().equals("SUPER_ADMIN"))) {
                return Result.fail("无权删除系统公告");
            }

            // 删除公告
            systemAnnouncementMapper.deleteAnnouncement(id);

            return Result.success("系统公告删除成功");
        } catch (Exception e) {
            log.error("删除系统公告失败", e);
            return Result.fail("删除系统公告失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SystemAnnouncementDTO> getAnnouncementDetail(Long id, Long userId) {
        try {
            // 获取公告详情
            SystemAnnouncement announcement = systemAnnouncementMapper.selectById(id);
            if (announcement == null) {
                return Result.fail("系统公告不存在");
            }

            // 转换为DTO
            SystemAnnouncementDTO dto = convertToDTO(announcement);

            // 获取创建者信息
            User creator = userMapper.findById(announcement.getCreatedBy());
            if (creator != null) {
                dto.setCreatedByUsername(creator.getUsername());
            }

            // 检查用户是否已读
            if (userId != null) {
                UserAnnouncementRead read = userAnnouncementReadMapper.selectByUserAndAnnouncement(userId, id);
                dto.setIsRead(read != null ? 1 : 0);
            } else {
                dto.setIsRead(0);
            }

            return Result.success(dto);
        } catch (Exception e) {
            log.error("获取系统公告详情失败", e);
            return Result.fail("获取系统公告详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<SystemAnnouncementDTO>> getValidAnnouncements(Long userId) {
        try {
            log.info("获取有效系统公告，用户ID: {}", userId);

            List<SystemAnnouncementDTO> dtoList;

            if (userId != null) {
                // 使用联表查询直接获取已读状态
                dtoList = systemAnnouncementMapper.selectValidAnnouncementsWithReadStatus(LocalDateTime.now(), userId);
                log.info("使用联表查询获取公告列表，数量: {}", dtoList.size());

                // 格式化日期时间
                dtoList.forEach(dto -> {
                    if (dto.getCreatedAt() != null) {
                        dto.setCreatedAt(LocalDateTime.parse(dto.getCreatedAt()).format(formatter));
                    }
                    if (dto.getUpdatedAt() != null) {
                        dto.setUpdatedAt(LocalDateTime.parse(dto.getUpdatedAt()).format(formatter));
                    }
                    if (dto.getStartTime() != null) {
                        dto.setStartTime(LocalDateTime.parse(dto.getStartTime()).format(formatter));
                    }
                    if (dto.getEndTime() != null) {
                        dto.setEndTime(LocalDateTime.parse(dto.getEndTime()).format(formatter));
                    }
                });

                // 打印详细调试信息
                for (SystemAnnouncementDTO dto : dtoList) {
                    log.info("【公告详情】公告ID: {}, 标题: {}, 已读状态: {}, 已读时间: {}, 重要性: {}, 开始时间: {}, 结束时间: {}",
                        dto.getId(), dto.getTitle(), dto.getIsRead(), dto.getReadAt(), dto.getImportance(),
                        dto.getStartTime(), dto.getEndTime());
                }
            } else {
                // 用户未登录，使用原来的方法
                log.info("用户未登录，使用原方法获取公告列表");
                List<SystemAnnouncement> announcements = systemAnnouncementMapper.selectValidAnnouncements(LocalDateTime.now());

                // 转换为DTO并设置已读状态为false（未登录用户都显示为未读）
                dtoList = announcements.stream()
                    .map(announcement -> {
                        SystemAnnouncementDTO dto = convertToDTO(announcement);
                        dto.setIsRead(0);  // 0表示未读
                        return dto;
                    })
                    .collect(Collectors.toList());
            }

            return Result.success(dtoList);
        } catch (Exception e) {
            log.error("获取有效系统公告失败", e);
            return Result.fail("获取有效系统公告失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<SystemAnnouncementDTO>> getAllAnnouncements() {
        try {
            // 获取所有公告（管理员视图）
            List<SystemAnnouncement> announcements = systemAnnouncementMapper.selectAllAnnouncements();

            // 转换为DTO
            List<SystemAnnouncementDTO> dtoList = announcements.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

            return Result.success(dtoList);
        } catch (Exception e) {
            log.error("获取所有系统公告失败", e);
            return Result.fail("获取所有系统公告失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<String> publishAnnouncement(Long id, Long adminId) {
        try {
            // 检查公告是否存在
            SystemAnnouncement announcement = systemAnnouncementMapper.selectById(id);
            if (announcement == null) {
                return Result.fail("系统公告不存在");
            }

            // 检查管理员权限
            User admin = userMapper.findById(adminId);
            if (admin == null || (!admin.getRole().equals("ADMIN") && !admin.getRole().equals("SUPER_ADMIN"))) {
                return Result.fail("无权发布系统公告");
            }

            // 更新状态为已发布
            systemAnnouncementMapper.updateStatus(id, "PUBLISHED");

            // 如果是紧急或重要公告，通过WebSocket推送通知
            if ("URGENT".equals(announcement.getImportance()) || "IMPORTANT".equals(announcement.getImportance())) {
                // 获取所有用户
                List<Long> allUserIds = userMapper.getAllUserIds();

                // 推送通知
                for (Long userId : allUserIds) {
                    // 创建通知对象
                    NotificationDTO notification = new NotificationDTO();
                    notification.setUserId(userId);
                    notification.setTitle("新系统公告: " + announcement.getTitle());
                    notification.setMessage("管理员发布了一条" +
                        ("URGENT".equals(announcement.getImportance()) ? "紧急" : "重要") +
                        "公告，请及时查看。");
                    notification.setStatus("UNREAD");

                    // 使用WebSocket处理器发送通知
                    notificationWebSocketHandler.sendNotification(userId, notification);
                }
            }

            return Result.success("系统公告发布成功");
        } catch (Exception e) {
            log.error("发布系统公告失败", e);
            return Result.fail("发布系统公告失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<String> markAnnouncementAsRead(Long announcementId, Long userId) {
        try {
            log.info("开始处理标记公告已读，公告ID: {}, 用户ID: {}", announcementId, userId);

            // 检查公告是否存在
            SystemAnnouncement announcement = systemAnnouncementMapper.selectById(announcementId);
            if (announcement == null) {
                log.warn("标记公告已读失败：公告不存在，ID: {}", announcementId);
                return Result.fail("系统公告不存在");
            }

            log.info("找到公告，标题: {}, 状态: {}", announcement.getTitle(), announcement.getStatus());

            // 检查用户是否已读过该公告
            UserAnnouncementRead existingRead = userAnnouncementReadMapper.selectByUserAndAnnouncement(userId, announcementId);
            if (existingRead != null) {
                log.info("用户已读过该公告，更新已读时间，用户ID: {}, 公告ID: {}", userId, announcementId);
                // 如果已读过，只更新已读时间
                existingRead.setReadAt(LocalDateTime.now());
                userAnnouncementReadMapper.insertRead(existingRead); // 使用ON DUPLICATE KEY UPDATE
            } else {
                // 标记为已读
                log.info("用户首次阅读该公告，创建已读记录，用户ID: {}, 公告ID: {}", userId, announcementId);
                UserAnnouncementRead read = new UserAnnouncementRead();
                read.setUserId(userId);
                read.setAnnouncementId(announcementId);
                read.setReadAt(LocalDateTime.now());

                int result = userAnnouncementReadMapper.insertRead(read);
                log.info("插入已读记录结果: {}", result);
            }

            log.info("标记公告已读成功，公告ID: {}, 用户ID: {}", announcementId, userId);
            return Result.success("已标记为已读");
        } catch (Exception e) {
            log.error("标记公告已读失败，公告ID: {}, 用户ID: {}, 错误: {}", announcementId, userId, e.getMessage(), e);
            return Result.fail("标记公告已读失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> getUnreadCount(Long userId) {
        try {
            int count = userAnnouncementReadMapper.countUnreadAnnouncements(userId);
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取未读公告数量失败", e);
            return Result.fail("获取未读公告数量失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SystemAnnouncementDTO> getLatestAnnouncement(Long userId) {
        try {
            // 获取最新的有效公告
            SystemAnnouncement announcement = systemAnnouncementMapper.selectLatestAnnouncement(LocalDateTime.now());
            if (announcement == null) {
                return Result.success(null); // 没有有效公告
            }

            // 转换为DTO
            SystemAnnouncementDTO dto = convertToDTO(announcement);

            // 检查用户是否已读
            if (userId != null) {
                UserAnnouncementRead read = userAnnouncementReadMapper.selectByUserAndAnnouncement(userId, announcement.getId());
                dto.setIsRead(read != null ? 1 : 0);
            } else {
                dto.setIsRead(0);
            }

            return Result.success(dto);
        } catch (Exception e) {
            log.error("获取最新系统公告失败", e);
            return Result.fail("获取最新系统公告失败: " + e.getMessage());
        }
    }

    // 辅助方法：将实体转换为DTO
    private SystemAnnouncementDTO convertToDTO(SystemAnnouncement announcement) {
        SystemAnnouncementDTO dto = new SystemAnnouncementDTO();
        dto.setId(announcement.getId());
        dto.setTitle(announcement.getTitle());
        dto.setContent(announcement.getContent());
        dto.setImportance(announcement.getImportance());
        dto.setStartTime(announcement.getStartTime().format(formatter));
        if (announcement.getEndTime() != null) {
            dto.setEndTime(announcement.getEndTime().format(formatter));
        }
        dto.setCreatedBy(announcement.getCreatedBy());
        dto.setCreatedAt(announcement.getCreatedAt().format(formatter));
        dto.setUpdatedAt(announcement.getUpdatedAt().format(formatter));
        dto.setStatus(announcement.getStatus());
        dto.setIsRead(0); // 默认为未读，0表示未读
        return dto;
    }
}
