
package com.tomato.lostfoundsystem.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class FoundItemDTO {
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    @NotBlank(message = "物品名称不能为空")
    private String itemName;
    @NotBlank(message = "物品描述不能为空")
    @jakarta.validation.constraints.Size(min = 20, max = 500, message = "物品描述长度必须在20-500个字符之间，请提供详细描述，包括物品特征、颜色、品牌等信息")
    private String description;
    @NotBlank(message = "拾取时间不能为空")
    private String foundTime;
    @NotBlank(message = "拾取地点不能为空")
    private String foundLocation;
    private MultipartFile image;   // 主图片文件（可选，向后兼容）
    private List<MultipartFile> images; // 多图片文件列表（可选）
    private Integer mainImageIndex; // 主图索引，指定哪张图片作为主图
    private boolean keepExistingImages = false; // 是否保留原有图片
    private boolean mainImageChanged = false; // 主图索引是否变化
    private String username;   //发布者的用户
    private String createdAt; //发布时间
    private String Status;

}
