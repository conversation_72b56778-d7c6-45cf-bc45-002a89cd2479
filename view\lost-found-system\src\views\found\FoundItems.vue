<template>
    <div class="found-items-container">
      <!-- 页面标题 -->
      <div class="page-title">
        <h2>拾物信息</h2>
        <div class="title-line"></div>
      </div>

      <div class="page-header">
        <div class="search-area">
          <div class="search-form">
            <div class="input-group">
              <el-input
                v-model="searchForm.keyword"
                placeholder="物品名称/描述"
                clearable
                @keyup.enter="handleSearch"
              />
              <el-input
                v-model="searchForm.foundLocation"
                placeholder="拾取地点"
                clearable
                @keyup.enter="handleSearch"
              />
              <el-select v-model="searchForm.status" placeholder="状态" clearable>
                <el-option label="未认领" value="UNCLAIMED" />
                <el-option label="已归还" value="RETURNED" />
              </el-select>
            </div>

            <div class="time-group">
              <el-select v-model="searchForm.timeFilterType" class="time-type">
                <el-option label="拾取时间" value="foundTime" />
                <el-option label="发布时间" value="createdAt" />
              </el-select>
              <el-select v-model="timeRange" class="time-range" @change="handleTimeRangeChange">
                <el-option label="请选择" value="" />
                <el-option label="今天" value="today" />
                <el-option label="昨天" value="yesterday" />
                <el-option label="最近三天" value="lastThreeDays" />
                <el-option label="最近一周" value="lastWeek" />
                <el-option label="最近一月" value="lastMonth" />
                <el-option label="自定义" value="custom" />
              </el-select>
              <el-date-picker
                v-if="timeRange === 'custom'"
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DDTHH:mm:ss"
                :default-time="[
                  new Date(2000, 1, 1, 0, 0, 0),
                  new Date(2000, 1, 1, 23, 59, 59)
                ]"
                @change="handleDateRangeChange"
              />
            </div>
          </div>

          <div class="button-group">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button type="success" @click="router.push('/found-items/publish')">
              <el-icon><Plus /></el-icon>
              发布拾物
            </el-button>
          </div>
        </div>

        <div class="view-controls">
          <div class="active-filters" v-if="hasActiveFilters">
            <span class="filter-label">当前筛选：</span>
            <el-tag
              v-if="searchForm.keyword"
              closable
              @close="clearKeyword"
            >关键词: {{ searchForm.keyword }}</el-tag>
            <el-tag
              v-if="searchForm.foundLocation"
              closable
              @close="clearLocation"
            >地点: {{ searchForm.foundLocation }}</el-tag>
            <el-tag
              v-if="searchForm.status"
              closable
              @close="clearStatus"
            >状态: {{ searchForm.status === 'UNCLAIMED' ? '未认领' : '已归还' }}</el-tag>
            <el-tag
              v-if="activeTimeFilter"
              closable
              @close="clearTimeFilter"
            >{{ activeTimeFilter }}</el-tag>
          </div>

          <div class="view-switcher">
            <el-radio-group v-model="viewMode" size="default">
              <el-radio-button value="list">
                <el-icon><List /></el-icon>
                列表视图
              </el-radio-button>
              <el-radio-button value="card">
                <el-icon><Grid /></el-icon>
                卡片视图
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>

      <!-- 空状态提示 -->
      <el-empty
        v-if="!loading && (!foundItems || foundItems.length === 0)"
        :description="getEmptyDescription"
      >
        <template #image>
          <el-icon :size="48"><Box /></el-icon>
        </template>
      </el-empty>

      <!-- 列表内容保持不变 -->
      <div v-else>
        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="list-view" v-loading="loading">
          <el-table :data="foundItems" border style="width: 100%">
            <el-table-column label="图片" width="120">
              <template #default="{ row }">
                <el-image
                  class="found-item-image"
                  :src="row.imageUrl || 'default-image.jpg'"
                  fit="cover"
                  :preview-src-list="row.imageUrl ? [row.imageUrl] : []"
                  :initial-index="0"
                  preview-teleported
                  @click="row.imageUrl && $event.stopPropagation()"
                >
                  <template #error>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column prop="itemName" label="物品名称" min-width="120" />
            <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
            <el-table-column prop="foundLocation" label="拾取地点" min-width="120" />
            <el-table-column prop="foundTime" label="拾取时间" min-width="160" />
            <el-table-column prop="username" label="发布人" min-width="100">
              <template #default="{ row }">
                {{ row.username || '匿名用户' }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">{{ row.status === 'UNCLAIMED' ? '未认领' : '已归还' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  link
                  @click="router.push(`/found-items/detail/${row.id}`)"
                >
                  <el-tooltip content="查看详情" placement="top">
                    <el-icon><View /></el-icon>
                  </el-tooltip>
                </el-button>
                <el-button
                  v-if="row.userId === currentUserId && row.status === 'UNCLAIMED'"
                  type="primary"
                  link
                  @click="router.push(`/found-items/edit/${row.id}`)"
                >
                  <el-tooltip content="编辑" placement="top">
                    <el-icon><Edit /></el-icon>
                  </el-tooltip>
                </el-button>
                <el-button
                  v-if="row.userId === currentUserId && row.status === 'UNCLAIMED'"
                  type="danger"
                  link
                  @click="handleDelete(row)"
                >
                  <el-tooltip content="删除" placement="top">
                    <el-icon><Delete /></el-icon>
                  </el-tooltip>
                </el-button>

              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 卡片视图 -->
        <div v-else class="card-view" v-loading="loading">
          <el-row :gutter="16">
            <el-col v-for="item in foundItems" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
              <el-card class="found-item-card" :body-style="{ padding: '0px' }">
                <div class="card-image">
                  <el-image
                    :src="item.imageUrl || 'default-image.jpg'"
                    fit="contain"
                    :preview-src-list="item.imageUrl ? [item.imageUrl] : []"
                    :initial-index="0"
                    preview-teleported
                    @click="item.imageUrl && $event.stopPropagation()"
                  >
                    <template #error>
                      <div class="image-placeholder">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                  <div class="card-status">
                    <el-tag :type="getStatusType(item.status)" size="small">{{ item.status === 'UNCLAIMED' ? '未认领' : '已归还' }}</el-tag>
                  </div>
                </div>
                <div class="card-content">
                  <h3 class="card-title text-ellipsis">{{ item.itemName }}</h3>
                  <p class="card-desc">{{ item.description }}</p>
                  <div class="card-info">
                    <span class="info-item text-ellipsis">
                      <el-icon><Location /></el-icon>
                      {{ item.foundLocation }}
                    </span>
                    <span class="info-item text-ellipsis">
                      <el-icon><Timer /></el-icon>
                      {{ item.foundTime }}
                    </span>
                    <span class="info-item text-ellipsis user-info">
                      <el-avatar :size="20" :src="item.avatar" class="user-avatar">
                        {{ item.username ? item.username.charAt(0).toUpperCase() : 'U' }}
                      </el-avatar>
                      {{ item.username || '匿名用户' }}
                    </span>
                  </div>
                  <div class="card-actions">
                    <el-button
                      type="primary"
                      link
                      @click="router.push(`/found-items/detail/${item.id}`)"
                    >
                      <el-tooltip content="查看详情" placement="top">
                        <el-icon><View /></el-icon>
                      </el-tooltip>
                    </el-button>
                    <el-button
                      v-if="item.userId === currentUserId && item.status === 'UNCLAIMED'"
                      type="primary"
                      link
                      @click="router.push(`/found-items/edit/${item.id}`)"
                    >
                      <el-tooltip content="编辑" placement="top">
                        <el-icon><Edit /></el-icon>
                      </el-tooltip>
                    </el-button>
                    <el-button
                      v-if="item.userId === currentUserId && item.status === 'UNCLAIMED'"
                      type="danger"
                      link
                      @click="handleDelete(item)"
                    >
                      <el-tooltip content="删除" placement="top">
                        <el-icon><Delete /></el-icon>
                      </el-tooltip>
                    </el-button>

                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[4, 8, 12, 16]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>


    </div>
  </template>

  <script setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Plus, List, Grid, Location, Timer, Search, Refresh, Box, Picture, View, Edit, Delete, User } from '@element-plus/icons-vue';
  import { getFoundItems, deleteFoundItem } from '../../api/found';
  import { useUserStore } from '../../stores';
  import { useRouter } from 'vue-router';

  const userStore = useUserStore();
  const currentUserId = computed(() => userStore.userInfo?.id);

  // 视图模式
  const viewMode = ref('list');

  // 数据状态
  const loading = ref(false);
  const foundItems = ref([]);
  const total = ref(0);
  const totalPages = ref(0);
  const currentPage = ref(1);
  const pageSize = ref(4);

  // 搜索表单
  const searchForm = reactive({
    keyword: '',
    foundLocation: '',
    status: '',
    timeFilterType: 'foundTime',
    startDate: '',
    endDate: ''
  });

  // 时间范围
  const timeRange = ref('lastWeek');
  const dateRange = ref(null);

  // 计算属性：是否有激活的筛选条件
  const hasActiveFilters = computed(() => {
    return searchForm.keyword ||
           searchForm.foundLocation ||
           searchForm.status ||
           timeRange.value;
  });

  // 计算属性：当前激活的时间筛选器显示文本
  const activeTimeFilter = computed(() => {
    if (!timeRange.value) return '';
    const timeRangeMap = {
      today: '今天',
      yesterday: '昨天',
      threeDays: '最近三天',
      lastWeek: '最近一周',
      lastMonth: '最近一月',
      custom: `${searchForm.startDate} 至 ${searchForm.endDate}`
    };
    return `时间: ${timeRangeMap[timeRange.value] || ''}`;
  });

  // 清除筛选条件的方法
  const clearKeyword = () => searchForm.keyword = '';
  const clearLocation = () => searchForm.foundLocation = '';
  const clearStatus = () => searchForm.status = '';
  const clearTimeFilter = () => {
    timeRange.value = '';
    dateRange.value = null;
    searchForm.startDate = '';
    searchForm.endDate = '';
  };

  // 处理日期范围变化
  const handleDateRangeChange = (value) => {
    if (value) {
      searchForm.timeRange = 'custom';
      searchForm.startDate = value[0];
      searchForm.endDate = value[1];
    } else {
      searchForm.startDate = '';
      searchForm.endDate = '';
    }
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (value) => {
    if (value === 'custom') return;

    if (value) {
      searchForm.timeRange = value;
      const startDate = new Date();
      const endDate = new Date();

      switch (value) {
        case 'today':
          startDate.setHours(0, 0, 0, 0);
          endDate.setHours(23, 59, 59, 999);
          break;
        case 'yesterday':
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(0, 0, 0, 0);
          endDate.setDate(endDate.getDate() - 1);
          endDate.setHours(23, 59, 59, 999);
          break;
        case 'lastThreeDays':
          startDate.setDate(startDate.getDate() - 3);
          startDate.setHours(0, 0, 0, 0);
          endDate.setHours(23, 59, 59, 999);
          break;
        case 'lastWeek':
          startDate.setDate(startDate.getDate() - 7);
          startDate.setHours(0, 0, 0, 0);
          endDate.setHours(23, 59, 59, 999);
          break;
        case 'lastMonth':
          startDate.setMonth(startDate.getMonth() - 1);
          startDate.setHours(0, 0, 0, 0);
          endDate.setHours(23, 59, 59, 999);
          break;
      }

      searchForm.startDate = startDate.toISOString().split('.')[0];
      searchForm.endDate = endDate.toISOString().split('.')[0];
    } else {
      searchForm.startDate = '';
      searchForm.endDate = '';
    }
  };

  // 搜索方法
  const handleSearch = async () => {
    try {
      currentPage.value = 1;
      loading.value = true;

      const params = {
        ...searchForm,
        page: currentPage.value,
        size: pageSize.value
      };

      const res = await getFoundItems(params);

      if (res.code === 200) {
        const { data, total: totalCount, totalPages: totalPagesCount } = res.data;
        foundItems.value = data || [];
        total.value = totalCount || 0;
        totalPages.value = totalPagesCount || 0;
        ElMessage.success({ message: `搜索成功，共找到 ${foundItems.value.length} 条记录`, duration: 2000 });
      } else {
        ElMessage.info({ message: res.message || '暂无符合条件的数据', duration: 2000 });
      }
    } catch (error) {
      console.error('搜索失败：', error);
      ElMessage.error({ message: '搜索失败，请稍后重试', duration: 2000 });
      foundItems.value = [];
      total.value = 0;
    } finally {
      loading.value = false;
    }
  };

  // 重置方法
  const handleReset = () => {
    searchForm.keyword = '';
    searchForm.foundLocation = '';
    searchForm.status = '';
    searchForm.timeFilterType = 'foundTime';
    timeRange.value = 'lastWeek';
    dateRange.value = null;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date();
    endDate.setHours(23, 59, 59, 999);

    searchForm.startDate = startDate.toISOString().split('.')[0];
    searchForm.endDate = endDate.toISOString().split('.')[0];

    currentPage.value = 1;
    handleSearch();
  };

  // 获取拾物列表
  const fetchFoundItems = async () => {
    try {
      loading.value = true;

      const params = {
        ...searchForm,
        page: currentPage.value,
        size: pageSize.value
      };

      const res = await getFoundItems(params);

      if (res.code === 200) {
        const { data, total: totalCount, totalPages: totalPagesCount } = res.data;
        foundItems.value = data || [];
        total.value = totalCount || 0;
        totalPages.value = totalPagesCount || 0;
      } else {
        ElMessage.error({ message: res.message || '获取拾物列表失败', duration: 2000 });
      }
    } catch (error) {
      console.error('获取拾物列表失败：', error);
      ElMessage.error({ message: '获取拾物列表失败，请稍后重试', duration: 2000 });
    } finally {
      loading.value = false;
    }
  };

  // 处理分页大小变化
  const handleSizeChange = (val) => {
    pageSize.value = val;
    fetchFoundItems();
  };

  // 处理页码变化
  const handleCurrentChange = (val) => {
    currentPage.value = val;
    fetchFoundItems();
  };

  // 查看详情
  const showDetail = (item) => {
    router.push(`/found-items/detail/${item.id}`);
  };

  // 编辑拾物
  const showEditDialog = (item) => {
    router.push(`/found-items/edit/${item.id}`);
  };

  // 处理删除
  const handleDelete = async (item) => {
    try {
      await ElMessageBox.confirm('确定要删除这条拾物信息吗？此操作不可恢复', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });

      await deleteFoundItem(item.id);
      ElMessage.success({ message: '删除成功', duration: 2000 });
      fetchFoundItems();
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error({ message: '删除失败：' + (error.message || '未知错误'), duration: 2000 });
      }
    }
  };



  // 获取状态类型
  const getStatusType = (status) => {
    switch (status) {
      case 'UNCLAIMED':
        return 'warning';
      case 'RETURNED':
        return 'success';
      default:
        return 'info';
    }
  };

  // 获取空状态描述
  const getEmptyDescription = computed(() => {
    if (hasActiveFilters.value) {
      const timeDesc = searchForm.timeRange === 'custom'
        ? `${searchForm.startDate} 至 ${searchForm.endDate}`
        : {
            today: '今天',
            yesterday: '昨天',
            threeDays: '最近三天',
            lastWeek: '最近一周',
            lastMonth: '最近一个月'
          }[searchForm.timeRange];

      return `在${timeDesc}内没有找到相关的拾物信息`;
    }
    return '暂无拾物信息';
  });

  // 初始加载
  onMounted(() => {
    handleTimeRangeChange('lastWeek');
    fetchFoundItems();
  });

  const router = useRouter();




  </script>

<style scoped>
.found-items-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面标题样式 - 与首页一致 */
.page-title {
  text-align: center;
  margin-bottom: 2rem;
}

.page-title h2 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 8px;
}

.title-line {
  width: 60px;
  height: 3px;
  background-color: #1890ff; /* 拾物页面使用蓝色 */
  margin: 0 auto;
  margin-bottom: 1rem;
}

.page-header {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  overflow: hidden;
}

.search-area {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.search-form {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.input-group {
  display: flex;
  gap: 15px;
  flex: 2;
}

.input-group :deep(.el-input),
.input-group :deep(.el-select) {
  width: 180px;
}

.time-group {
  display: flex;
  gap: 15px;
  flex: 2;
}

.time-type,
.time-range {
  width: 120px;
}

.time-group :deep(.el-date-editor) {
  width: 260px;
}

.button-group {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding-top: 5px;
}

.button-group .el-button {
  transition: all 0.3s;
}

.button-group .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.view-controls {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.active-filters {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
}

.filter-label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.view-switcher {
  white-space: nowrap;
  margin-left: auto;
}

:deep(.el-tag) {
  margin: 3px;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.el-tag:hover) {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media screen and (max-width: 1200px) {
  .search-form {
    flex-direction: column;
  }

  .input-group,
  .time-group {
    flex-wrap: wrap;
  }

  .input-group :deep(.el-input),
  .input-group :deep(.el-select),
  .time-group :deep(.el-select),
  .time-group :deep(.el-date-editor) {
    width: 100%;
  }

  .button-group {
    justify-content: flex-start;
  }
}

@media screen and (max-width: 768px) {
  .view-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .view-switcher {
    display: flex;
    justify-content: flex-end;
  }
}

.list-view {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  padding: 20px;
  overflow: hidden;
}

.card-view {
  margin: 0 -10px 20px;
}

.found-item-card {
  margin-bottom: 20px;
  transition: all 0.3s;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.found-item-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-image {
  position: relative;
  height: 180px;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.card-image .el-image {
  width: 100%;
  height: 100%;
}

.card-image :deep(.el-image__inner) {
  object-fit: cover !important;
  transition: transform 0.5s;
}

.card-image:hover :deep(.el-image__inner) {
  transform: scale(1.05);
}

.image-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
}

.image-placeholder .el-icon {
  font-size: 36px;
}

.card-status {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1;
}

.card-content {
  padding: 15px;
}

.card-title {
  margin: 0 0 10px;
  font-size: 16px;
  color: #303133;
  line-height: 1.4;
  font-weight: 500;
}

.card-desc {
  margin: 0 0 10px;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 42px;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 13px;
  line-height: 1.4;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  margin-right: 4px;
  border: 1px solid #eee;
  background-color: #f5f7fa;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

.card-actions .el-button {
  padding: 4px 8px;
}

.card-actions .el-button .el-icon {
  margin-right: 4px;
}

.pagination {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: flex-end;
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 500;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
  margin-right: 0;
}

:deep(.el-card) {
  border: none;
  overflow: hidden;
  transition: all 0.3s;
}

.found-item-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  display: block;
  margin: 0 auto;
  transition: transform 0.3s;
}

.found-item-image:hover {
  transform: scale(1.05);
}

.image-placeholder {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 4px;
  margin: 0 auto;
}

.image-placeholder .el-icon {
  font-size: 24px;
  color: #909399;
}
</style>