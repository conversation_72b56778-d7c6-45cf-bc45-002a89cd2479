package com.tomato.lostfoundsystem.interceptors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.mapper.UserMapper;
import com.tomato.lostfoundsystem.utils.CustomHttpServletRequestWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@Slf4j
@Component
public class VerifyCodeInterceptor implements HandlerInterceptor {

    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;

    @Autowired
    private UserMapper userMapper;

    public VerifyCodeInterceptor(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String uri = request.getRequestURI();
        // 只处理注册相关的请求
        if (!uri.contains("/register")) {
            return true;
        }

        log.info("拦截注册请求路径：{}", uri);

        try {
            // 处理请求体
            CustomHttpServletRequestWrapper customRequest = new CustomHttpServletRequestWrapper(request);
            String requestBody = customRequest.getBody();
            log.debug("请求体内容：{}", requestBody);

            @SuppressWarnings("unchecked")
            Map<String, String> bodyMap = objectMapper.readValue(requestBody, Map.class);

            // 验证码校验
            return validateVerificationCode(bodyMap, response);
        } catch (IOException e) {
            log.error("处理注册请求时发生错误", e);
            respondWithJson(response, Result.fail("处理请求时发生错误").toString());
            return false;
        }
    }

    private boolean validateVerificationCode(Map<String, String> bodyMap, HttpServletResponse response) throws IOException {
        String phone = bodyMap.get("phone");
        String email = bodyMap.get("email");
        String code = bodyMap.get("code");
        String username = bodyMap.get("username");

        log.debug("验证码校验参数 - username: {}, phone: {}, email: {}, code: {}", username, phone, email, code);

        // 验证必要参数
        if (code == null || (phone == null && email == null) || username == null) {
            log.error("注册请求缺少必要参数");
            respondWithJson(response, Result.fail("注册必须提供用户名、手机号/邮箱和验证码").toString());
            return false;
        }

        // 检查用户是否已存在
        if (userMapper.findByUsername(username) != null) {
            log.error("用户名已存在 - username: {}", username);
            respondWithJson(response, Result.fail("用户名已存在").toString());
            return false;
        }

        if (email != null && userMapper.findByEmail(email) != null) {
            log.error("邮箱已被注册 - email: {}", email);
            respondWithJson(response, Result.fail("该邮箱已被注册").toString());
            return false;
        }

        if (phone != null && userMapper.findByPhone(phone) != null) {
            log.error("手机号已被注册 - phone: {}", phone);
            respondWithJson(response, Result.fail("该手机号已被注册").toString());
            return false;
        }

        // 获取Redis中存储的验证码
        String redisKey = getRedisKey(email, phone);
        String storedCode = redisTemplate.opsForValue().get(redisKey);
        log.debug("Redis中存储的验证码：{}", storedCode);

        // 验证码校验
        if (storedCode == null || !storedCode.equals(code)) {
            log.error("验证码无效或已过期 - phone/email: {}, code: {}", email != null ? email : phone, code);
            respondWithJson(response, Result.fail("注册验证码无效或已过期").toString());
            return false;
        }

        // 验证通过，删除验证码
        redisTemplate.delete(redisKey);
        log.info("验证码校验通过，已从Redis删除 - key: {}", redisKey);
        return true;
    }

    private String getRedisKey(String email, String phone) {
        return email != null ? "register:email:code:" + email : "register:phone:code:" + phone;
    }

    private void respondWithJson(HttpServletResponse response, String jsonStr) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.getWriter().write(jsonStr);
    }
}