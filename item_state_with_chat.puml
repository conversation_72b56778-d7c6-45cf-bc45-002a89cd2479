@startuml 物品状态流转与即时通讯集成图

skinparam StateBackgroundColor LightYellow
skinparam StateBorderColor Black
skinparam StateStartColor Green
skinparam StateEndColor Red
skinparam ArrowColor Black

title 物品状态流转与即时通讯集成图

[*] --> 物品发布

state 物品发布 {
  [*] --> 失物发布 : 用户发布失物
  [*] --> 拾物发布 : 用户发布拾物
  
  失物发布 --> 失物待审核 : 提交
  拾物发布 --> 拾物待审核 : 提交
}

state 审核流程 {
  state 失物待审核 as "失物待审核\n(PENDING)" #LightBlue
  state 失物已通过 as "失物已通过\n(APPROVED)" #LightGreen
  state 失物已拒绝 as "失物已拒绝\n(REJECTED)" #Pink
  
  state 拾物待审核 as "拾物待审核\n(PENDING)" #LightBlue
  state 拾物已通过 as "拾物已通过\n(APPROVED)" #LightGreen
  state 拾物已拒绝 as "拾物已拒绝\n(REJECTED)" #Pink
  
  失物待审核 --> 失物已通过 : 管理员审核通过
  失物待审核 --> 失物已拒绝 : 管理员审核拒绝
  
  拾物待审核 --> 拾物已通过 : 管理员审核通过
  拾物待审核 --> 拾物已拒绝 : 管理员审核拒绝
}

state 物品状态 {
  state 未找回 as "未找回\n(LOST)" #Orange
  state 已找回 as "已找回\n(FOUND)" #Green
  
  state 未认领 as "未认领\n(UNCLAIMED)" #Orange
  state 已归还 as "已归还\n(RETURNED)" #Green
  
  失物已通过 --> 未找回 : 自动转换
  拾物已通过 --> 未认领 : 自动转换
}

state 即时通讯 {
  state 创建会话 as "创建聊天会话" #LightCyan
  state 消息交流 as "消息交流" #LightCyan
  state 线下交接 as "线下交接" #LightCyan
  state 确认完成 as "确认完成" #LightCyan
  
  创建会话 --> 消息交流 : 发送消息
  消息交流 --> 线下交接 : 约定交接
  线下交接 --> 确认完成 : 完成交接
}

state 智能匹配 {
  state 匹配触发 as "触发匹配" #LightCyan
  state 匹配结果 as "匹配结果" #LightCyan
  
  失物已通过 --> 匹配触发 : 自动触发
  拾物已通过 --> 匹配触发 : 自动触发
  
  匹配触发 --> 匹配结果 : 执行匹配算法
  匹配结果 --> 创建会话 : 高相似度匹配\n自动创建会话
}

未找回 --> 创建会话 : 用户主动联系
未认领 --> 创建会话 : 用户主动联系

未认领 --> 已归还 : 用户认领
note right of 已归还 : 自动创建与发布者的聊天会话

确认完成 --> 未找回 : 未达成一致
确认完成 --> 已找回 : 确认找回
确认完成 --> 已归还 : 确认归还

已找回 --> [*] : 流程结束
已归还 --> [*] : 流程结束

@enduml
