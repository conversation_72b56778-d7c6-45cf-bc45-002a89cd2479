@startuml 校园失物招领系统用例图

skinparam backgroundColor white
skinparam usecase {
  BackgroundColor white
  BorderColor black
  ArrowColor black
  FontSize 16
}
skinparam actor {
  BackgroundColor white
  BorderColor black
  FontSize 16
}
skinparam defaultTextAlignment center
skinparam linetype polyline

' 定义布局方向
left to right direction

' 左侧角色
actor "普通用户" as User

' 中间功能块
rectangle "校园失物招领系统" {
  ' 上部功能（用户相关）
  usecase "用户注册登录" as UserAuth
  usecase "失物信息管理" as LostManage
  usecase "拾物信息管理" as FoundManage
  usecase "智能匹配" as IntelligentMatch
  usecase "即时通讯" as Communication
  
  ' 下部功能（管理相关）
  usecase "物品信息审核" as ReviewItems
  usecase "用户管理" as ManageUsers
  usecase "系统公告管理" as ManageAnnouncement
}

' 右侧角色
actor "管理员" as Admin
actor "超级管理员" as SuperAdmin

' 普通用户关系
User --> UserAuth
User --> LostManage
User --> FoundManage
User --> IntelligentMatch
User --> Communication

' 管理员关系
Admin --> ReviewItems
Admin --> ManageUsers
Admin --> ManageAnnouncement

' 超级管理员继承管理员
SuperAdmin --|> Admin

' 包含和扩展关系
LostManage ..> IntelligentMatch : <<include>>
FoundManage ..> IntelligentMatch : <<include>>
IntelligentMatch ..> Communication : <<extend>>
UserAuth ..> ManageUsers : <<include>>

@enduml
