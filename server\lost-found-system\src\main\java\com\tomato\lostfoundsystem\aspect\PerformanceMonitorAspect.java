package com.tomato.lostfoundsystem.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 性能监控切面
 * 用于监控Controller和Service方法的执行时间
 */
@Slf4j
@Aspect
@Component
public class PerformanceMonitorAspect {

    /**
     * 定义切点 - Controller层
     */
    @Pointcut("execution(* com.tomato.lostfoundsystem.controller.*.*(..))")
    public void controllerPointcut() {
    }

    /**
     * 定义切点 - Service层
     */
    @Pointcut("execution(* com.tomato.lostfoundsystem.service.*.*(..))")
    public void servicePointcut() {
    }

    /**
     * 环绕通知 - 监控Controller方法执行时间
     */
    @Around("controllerPointcut()")
    public Object aroundController(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorPerformance(joinPoint, true);
    }

    /**
     * 环绕通知 - 监控Service方法执行时间
     */
    @Around("servicePointcut()")
    public Object aroundService(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorPerformance(joinPoint, false);
    }

    /**
     * 监控方法执行性能
     *
     * @param joinPoint 连接点
     * @param isController 是否为Controller方法
     * @return 方法执行结果
     * @throws Throwable 异常
     */
    private Object monitorPerformance(ProceedingJoinPoint joinPoint, boolean isController) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取类名和方法名
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = method.getName();
        
        // 创建计时器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        
        // 记录请求信息（仅Controller层）
        if (isController) {
            logRequestInfo(className, methodName, joinPoint.getArgs());
        }
        
        try {
            // 执行目标方法
            Object result = joinPoint.proceed();
            stopWatch.stop();
            
            // 记录执行时间
            long executionTime = stopWatch.getTotalTimeMillis();
            
            // 根据执行时间记录不同级别的日志
            if (executionTime > 1000) {
                log.warn("性能监控 - {}#{} 执行时间: {}ms", className, methodName, executionTime);
            } else if (executionTime > 500) {
                log.info("性能监控 - {}#{} 执行时间: {}ms", className, methodName, executionTime);
            } else if (log.isDebugEnabled()) {
                log.debug("性能监控 - {}#{} 执行时间: {}ms", className, methodName, executionTime);
            }
            
            return result;
        } catch (Throwable e) {
            stopWatch.stop();
            log.error("性能监控 - {}#{} 执行异常，耗时: {}ms, 异常: {}", 
                    className, methodName, stopWatch.getTotalTimeMillis(), e.getMessage());
            throw e;
        }
    }

    /**
     * 记录请求信息
     */
    private void logRequestInfo(String className, String methodName, Object[] args) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                log.info("请求监控 - {}#{} - URL: {}, 方法: {}, IP: {}, 参数: {}", 
                        className, methodName, 
                        request.getRequestURL().toString(), 
                        request.getMethod(), 
                        request.getRemoteAddr(), 
                        Arrays.toString(args));
            }
        } catch (Exception e) {
            log.warn("记录请求信息失败", e);
        }
    }
}
