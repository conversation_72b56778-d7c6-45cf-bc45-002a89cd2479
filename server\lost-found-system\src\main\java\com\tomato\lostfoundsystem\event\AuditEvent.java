package com.tomato.lostfoundsystem.event;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 审核事件
 * 用于在审核通过后触发自动匹配
 */
@Data
public class AuditEvent {
    /**
     * 事件ID
     */
    private String eventId;
    
    /**
     * 事件类型
     * APPROVED - 审核通过
     * REJECTED - 审核拒绝
     */
    private String eventType;
    
    /**
     * 物品ID
     */
    private Long itemId;
    
    /**
     * 物品类型
     * LOST - 失物
     * FOUND - 拾物
     */
    private String itemType;
    
    /**
     * 审核人ID
     */
    private String auditorId;
    
    /**
     * 事件时间
     */
    private LocalDateTime timestamp;
}
