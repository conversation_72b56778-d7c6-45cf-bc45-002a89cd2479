<template>
  <div class="message-item" :class="{ 'message-self': isSelf }" :data-anchor="props.isAnchor ? 'true' : 'false'">
    <el-avatar
      :size="36"
      :src="avatar"
      class="message-avatar"
      :style="avatarStyle"
    >
      <span>{{ senderInitial }}</span>
    </el-avatar>

    <div class="message-content">
      <div class="message-bubble" :class="{ 'message-self': isSelf }">
        <!-- 文本消息 -->
        <div v-if="messageType === 'TEXT'" class="message-text">
          {{ message.message || content }}
        </div>

        <!-- 图片消息 -->
        <div v-else-if="messageType === 'IMAGE'" class="message-image">
          <!-- 图片加载中状态 -->
          <template v-if="props.message._imageLoading || props.message.status === 'SENDING'">
            <div class="image-loading-container">
              <el-skeleton animated :rows="3" />
              <div class="image-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>图片上传中...</span>
              </div>

              <!-- 即使图片加载中，也显示文字内容 -->
              <div v-if="message.message || content" class="image-caption">
                {{ message.message || content }}
              </div>
            </div>
          </template>

          <!-- 单张图片显示 -->
          <template v-else-if="!hasMultipleAttachments">
            <!-- 有URL时显示图片 -->
            <template v-if="processedImageUrl">
              <div class="image-container">
                <el-image
                  :src="processedImageUrl"
                  :preview-src-list="[processedImageUrl]"
                  fit="cover"
                  class="image-preview"
                  :initial-index="0"
                  @load="handleImageLoad"
                  @error="handleImageError"
                  preview-teleported
                >
                  <template #placeholder>
                    <div class="image-loading">
                      <el-icon class="is-loading"><Loading /></el-icon>
                      <span>加载中...</span>
                    </div>
                  </template>
                  <template #error>
                    <div class="image-error" @click="retryLoadImage">
                      <el-icon><Picture /></el-icon>
                      <span>图片加载失败</span>
                      <el-button type="text" size="small">点击重试</el-button>
                    </div>
                  </template>
                </el-image>

                <!-- 图片下方显示文字内容 -->
                <div v-if="message.message || content" class="image-caption">
                  {{ message.message || content }}
                </div>
              </div>

              <!-- 调试信息 -->
              <div v-if="showDebugInfo" class="debug-info">
                <small>URL: {{ processedImageUrl }}</small>
              </div>
            </template>

            <!-- 没有URL时显示错误提示 -->
            <template v-else>
              <!-- 如果消息状态是发送中，显示加载状态 -->
              <template v-if="props.message.status === 'SENDING'">
                <div class="image-loading">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  <span>图片上传中...</span>
                </div>
              </template>
              <!-- 否则显示错误提示 -->
              <template v-else>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>图片URL缺失</span>
                  <small>消息ID: {{ props.message.messageId || props.message.id }}</small>
                </div>
              </template>

              <!-- 即使图片加载失败，也显示文字内容 -->
              <div v-if="message.message || content" class="image-caption error-caption">
                {{ message.message || content }}
              </div>
            </template>
          </template>

          <!-- 多张图片显示 -->
          <template v-else-if="attachments.length > 0">
            <div class="multi-image-container">
              <el-image
                v-for="(attachment, index) in attachments"
                :key="index"
                :src="processAttachmentUrl(attachment)"
                :preview-src-list="attachmentUrls"
                :initial-index="index"
                fit="cover"
                class="multi-image-preview"
                @load="handleImageLoad"
                @error="(e) => handleImageError(e, index)"
                preview-teleported
              >
                <template #placeholder>
                  <div class="image-loading">
                    <el-icon class="is-loading"><Loading /></el-icon>
                  </div>
                </template>
                <template #error>
                  <div class="image-error" @click="() => retryLoadImage(index)">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>

            <!-- 多图片下方显示文字内容 -->
            <div v-if="message.message || content" class="image-caption multi-image-caption">
              {{ message.message || content }}
            </div>

            <!-- 调试信息 -->
            <div v-if="showDebugInfo" class="debug-info">
              <small>附件数量: {{ attachments.length }}</small>
            </div>
          </template>

          <!-- 没有附件时显示错误提示 -->
          <template v-else>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
              <span>图片附件缺失</span>
              <small>消息ID: {{ props.message.messageId || props.message.id }}</small>
            </div>

            <!-- 即使没有附件，也显示文字内容 -->
            <div v-if="message.message || content" class="image-caption error-caption">
              {{ message.message || content }}
            </div>
          </template>
        </div>

        <!-- 音频消息 -->
        <div v-else-if="messageType === 'AUDIO'" class="message-audio">
          <div class="audio-player">
            <el-button
              circle
              :icon="isPlaying ? VideoPlay : CaretRight"
              @click="toggleAudio"
              :disabled="!canPlay"
              class="play-button"
            />
            <div class="audio-info">
              <div class="audio-waveform">
                <div class="waveform-bg"></div>
                <div
                  class="waveform-progress"
                  :style="{ width: `${audioProgress}%` }"
                ></div>
              </div>
              <div class="audio-time">{{ formatAudioTime(audioDurationRef) }}</div>
            </div>
          </div>
          <audio
            ref="audioElement"
            :src="getAttachmentInfo.value?.fileUrl"
            preload="metadata"
            @timeupdate="updateAudioProgress"
            @ended="audioEnded"
            @loadedmetadata="audioLoaded"
            @error="audioError"
          ></audio>
        </div>

        <!-- 视频消息 -->
        <div v-else-if="messageType === 'VIDEO'" class="message-video">
          <div class="video-container">
            <video
              ref="videoElement"
              controls
              :src="getAttachmentInfo.value?.fileUrl"
              preload="metadata"
              class="video-player"
            >
            您的浏览器不支持视频播放
          </video>
            <div class="video-overlay" v-if="!videoPlaying" @click="playVideo">
              <el-icon class="play-icon"><VideoPlay /></el-icon>
            </div>
          </div>
        </div>

        <!-- 文件消息 -->
        <div v-else-if="messageType === 'FILE' || messageType === 'DOCUMENT'" class="message-file">
          <div class="file-container">
            <el-icon class="file-icon"><Document /></el-icon>
            <div class="file-info">
              <div class="file-name">{{ fileName || message.fileName || getAttachmentInfo.value?.fileId || '文件' }}</div>
              <div class="file-size">{{ formatFileSize(fileSize || message.fileSize || getAttachmentInfo.value?.fileSize) }}</div>
            </div>
            <el-button
              link
              :icon="Download"
              @click="downloadFile"
              class="download-button"
            />
          </div>
        </div>
      </div>

      <div class="message-meta" :class="{ 'message-meta-self': isSelf }">
        <div v-if="isSelf" class="message-status">
          <!-- 只显示已读状态 -->
          <template v-if="isReadStatus">
            <el-icon class="status-icon read"><CircleCheck /></el-icon>
            <span class="status-text">已读</span>
          </template>

          <!-- 显示POST请求状态 -->
          <template v-else-if="props.message._isPostRequest">
            <el-icon class="status-icon sending"><Loading /></el-icon>
            <span class="status-text">发送中...</span>
          </template>

          <!-- 显示错误信息（如果有） -->
          <template v-else-if="props.message.errorMessage">
            <el-icon class="status-icon failed"><Warning /></el-icon>
            <span class="status-text error">
              {{ props.message.errorMessage }}
              <el-button type="text" size="small" @click="$emit('resend', props.message.id)">重试</el-button>
            </span>
          </template>
        </div>
        <span class="message-time">{{ formatTime() }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  CaretRight, // 用于音频播放
  VideoPlay,  // 用于视频播放
  Loading,    // 用于加载状态
  Picture,    // 用于图片错误状态
  Document,   // 用于文档消息
  Download    // 用于下载按钮
} from '@element-plus/icons-vue'
import { getUserColor, getContactColor, getContactAvatarColor } from '@/utils/avatar-utils'
import { useUserStore } from '@/stores'

const userStore = useUserStore()

// 定义事件
const emit = defineEmits(['image-loaded', 'resend'])

// 调试模式
const showDebugInfo = ref(false)

// 图片加载状态
const imageLoadAttempts = ref(0)
const imageLoadError = ref(null)

const props = defineProps({
  message: {
    type: Object,
    required: true
  },
  isSelf: {
    type: Boolean,
    default: false
  },
  avatar: {
    type: String,
    default: ''
  },
  avatarText: {
    type: String,
    default: ''
  },
  contactId: {
    type: [Number, String],
    default: null
  },
  isAnchor: {
    type: Boolean,
    default: false
  }
})

// 解构消息内容
const {
  content,
  messageType = 'TEXT',
  imageUrl,
  audioUrl,
  videoUrl,
  fileUrl,
  fileName,
  fileSize,
  time,
  status, // 不设置默认值，使用原始状态
  isRead, // 新增: 后端返回的已读状态
  audioDuration,
  videoDuration,
  messageAttachmentDTO,
  attachments: messageAttachments // 新增: 多个附件
} = props.message

// 获取附件信息
const getAttachmentInfo = computed(() => {
  if (messageAttachmentDTO) {
    return {
      fileUrl: messageAttachmentDTO.fileUrl,
      fileType: messageAttachmentDTO.fileType,
      fileSize: messageAttachmentDTO.fileSize,
      fileId: messageAttachmentDTO.fileId
    }
  }

  // 兼容旧格式
  return {
    fileUrl: fileUrl || imageUrl || audioUrl || videoUrl,
    fileType: messageType,
    fileSize: fileSize || 0,
    fileId: null
  }
})

// 检查是否有多个附件
const hasMultipleAttachments = computed(() => {
  return messageAttachments && Array.isArray(messageAttachments) && messageAttachments.length > 0;
});

// 获取所有附件
const attachments = computed(() => {
  if (hasMultipleAttachments.value) {
    return messageAttachments;
  }

  // 如果没有多个附件，但有单个附件，创建一个只包含单个附件的数组
  if (messageAttachmentDTO) {
    return [messageAttachmentDTO];
  }

  // 如果有图片URL但没有附件对象，创建一个虚拟附件
  if (imageUrl || props.message.fileUrl || props.message.imageUrl) {
    return [{
      fileUrl: imageUrl || props.message.fileUrl || props.message.imageUrl,
      fileType: 'IMAGE',
      fileSize: fileSize || 0
    }];
  }

  // 如果是图片消息但没有URL，记录详细信息并创建一个空附件
  if (messageType === 'IMAGE') {
    console.warn('图片消息缺少URL，消息ID:', props.message.messageId || props.message.id);
    console.warn('消息完整内容:', JSON.stringify(props.message));

    // 返回一个空数组，避免前端报错
    return [];
  }

  return [];
});

// 获取所有附件URL列表（用于预览）
const attachmentUrls = computed(() => {
  return attachments.value.map(attachment => processAttachmentUrl(attachment));
});

// 处理单个附件URL
const processAttachmentUrl = (attachment) => {
  if (!attachment || !attachment.fileUrl) {
    return '';
  }

  const originalUrl = attachment.fileUrl;

  // 如果URL已经是完整的HTTP(S)URL，直接返回
  if (originalUrl.startsWith('http://') || originalUrl.startsWith('https://')) {
    return originalUrl;
  }

  // 如果是相对路径，添加基础URL
  if (originalUrl.startsWith('/')) {
    const baseUrl = import.meta.env.VITE_API_BASE_URL || window.location.origin;
    return `${baseUrl}${originalUrl}`;
  }

  // 其他情况直接返回原始URL
  return originalUrl;
};

// 处理图片URL（向后兼容）
const processedImageUrl = computed(() => {
  // 如果有多个附件，使用第一个附件的URL
  if (hasMultipleAttachments.value && messageAttachments.length > 0) {
    return processAttachmentUrl(messageAttachments[0]);
  }

  // 获取原始URL
  const originalUrl = imageUrl || props.message.fileUrl || props.message.imageUrl || getAttachmentInfo.value?.fileUrl;

  if (!originalUrl) {
    // 记录更详细的信息，帮助调试
    console.warn('图片消息缺少URL，消息ID:', props.message.messageId || props.message.id);

    // 检查是否有attachment对象但没有fileUrl
    if (props.message.attachment) {
      console.warn('消息有attachment对象但fileUrl为空:', props.message.attachment);
    }

    // 检查是否有messageAttachmentDTO对象但没有fileUrl
    if (messageAttachmentDTO) {
      console.warn('消息有messageAttachmentDTO对象但fileUrl为空:', messageAttachmentDTO);
    }

    // 返回一个默认的图片URL或空字符串
    return '';
  }

  // 如果URL已经是完整的HTTP(S)URL，直接返回
  if (originalUrl.startsWith('http://') || originalUrl.startsWith('https://')) {
    return originalUrl;
  }

  // 如果是相对路径，添加基础URL
  if (originalUrl.startsWith('/')) {
    const baseUrl = import.meta.env.VITE_API_BASE_URL || window.location.origin;
    return `${baseUrl}${originalUrl}`;
  }

  // 其他情况直接返回原始URL
  return originalUrl;
})

// 图片加载成功处理
const handleImageLoad = () => {
  imageLoadError.value = null;
  emit('image-loaded');

  if (hasMultipleAttachments.value) {
    console.log('图片加载成功 (多附件模式)');
  } else {
    console.log('图片加载成功:', processedImageUrl.value);
  }
}

// 图片加载失败处理
const handleImageError = (e, index) => {
  imageLoadAttempts.value++;
  imageLoadError.value = e;

  if (index !== undefined) {
    // 多附件模式
    console.error('图片加载失败 (多附件模式):',
      index >= 0 && index < attachments.value.length ?
      processAttachmentUrl(attachments.value[index]) :
      '未知附件',
      '尝试次数:', imageLoadAttempts.value);
  } else {
    // 单附件模式
    console.error('图片加载失败:', processedImageUrl.value, '尝试次数:', imageLoadAttempts.value);
  }

  // 显示调试信息
  showDebugInfo.value = true;
}

// 重试加载图片
const retryLoadImage = (index) => {
  // 重置错误状态
  imageLoadError.value = null;

  let url;

  if (index !== undefined && index >= 0 && index < attachments.value.length) {
    // 多附件模式
    const originalUrl = processAttachmentUrl(attachments.value[index]);
    if (!originalUrl) {
      console.error('无法重试加载图片：URL为空');
      return;
    }

    try {
      url = new URL(originalUrl, window.location.origin);
    } catch (e) {
      console.error('无效的URL格式:', originalUrl, e);
      return;
    }
  } else {
    // 单附件模式
    if (!processedImageUrl.value) {
      console.error('无法重试加载图片：URL为空');
      return;
    }

    try {
      url = new URL(processedImageUrl.value, window.location.origin);
    } catch (e) {
      console.error('无效的URL格式:', processedImageUrl.value, e);
      return;
    }
  }

  // 添加时间戳参数强制刷新
  url.searchParams.set('t', Date.now());

  // 创建一个新的Image对象预加载图片
  const img = new Image();
  img.onload = () => {
    console.log('图片预加载成功:', url.toString());
    // 强制组件重新渲染
    imageLoadAttempts.value++;
  };
  img.onerror = (err) => {
    console.error('图片预加载失败:', url.toString(), err);
    imageLoadError.value = err;
  };
  img.src = url.toString();
}

// 简单但健壮的时间格式化函数
const formatTime = () => {
  try {
    let date;
    let timeStr = '';

    // 1. 获取日期对象
    if (props.message.timestamp) {
      // 针对"Mon Apr 28 22:51:54 CST 2025"格式
      if (typeof props.message.timestamp === 'string') {
        if (props.message.timestamp.includes('CST')) {
          // 正则提取年月日和时分
          const regex = /(\w+)\s+(\w+)\s+(\d+)\s+(\d+):(\d+):(\d+)\s+\w+\s+(\d+)/;
          const matches = props.message.timestamp.match(regex);

          if (matches) {
            // eslint-disable-next-line no-unused-vars
            const [_, dayName, monthName, day, hours, minutes, seconds, year] = matches;

            // 月份名称映射
            const monthMap = {
              'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
              'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
            };

            date = new Date(
              parseInt(year),
              monthMap[monthName] || 0,
              parseInt(day),
              parseInt(hours),
              parseInt(minutes),
              parseInt(seconds)
            );
          } else {
            // 降级处理
            date = new Date(props.message.timestamp);
          }
        } else if (props.message.timestamp.includes('T') && props.message.timestamp.includes('Z')) {
          // ISO格式 "2023-05-15T14:30:00Z"
          date = new Date(props.message.timestamp);
        } else if (props.message.timestamp.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
          // 标准日期格式 "2023-05-15 14:30:00"
          const [dateStr, timeStr] = props.message.timestamp.split(' ');
          const [year, month, day] = dateStr.split('-').map(Number);
          const [hours, minutes, seconds] = timeStr.split(':').map(Number);
          date = new Date(year, month - 1, day, hours, minutes, seconds);
        } else {
          // 其他格式尝试
          date = new Date(props.message.timestamp);
        }
      } else if (typeof props.message.timestamp === 'number') {
        // 时间戳格式
        date = new Date(props.message.timestamp);
      }
    } else if (time) {
      date = new Date(time);
    } else {
      return '';
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的日期对象，使用当前时间');
      date = new Date(); // 使用当前时间作为备选
    }

    // 2. 获取时间部分 (HH:MM)
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    timeStr = `${hours}:${minutes}`;

    // 3. 根据日期与当前日期的关系，确定前缀
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // 3.1 如果是今天的消息
    if (date >= today) {
      return timeStr;
    }

    // 3.2 如果是昨天的消息
    if (date >= yesterday) {
      return `昨天 ${timeStr}`;
    }

    // 3.3 如果是今年的消息
    if (date.getFullYear() === now.getFullYear()) {
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${month}-${day} ${timeStr}`;
    }

    // 3.4 如果是更早的消息
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day} ${timeStr}`;
  } catch (e) {
    console.warn('时间格式化失败:', e);
    return new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }
}

// 获取发送者首字母
const senderInitial = computed(() => {
  // 首先使用传入的avatarText prop
  if (props.avatarText) {
    return props.avatarText
  }
  // 其次使用消息中的avatarText
  if (props.message.avatarText) {
    return props.message.avatarText
  }
  // 再次使用senderName的首字母
  if (props.message.senderName) {
    return props.message.senderName.substring(0, 1)
  }
  // 使用sender的首字母(如果是对象)
  if (typeof props.message.sender === 'object' && props.message.sender?.name) {
    return props.message.sender.name.substring(0, 1)
  }
  // 默认使用'用'字
  return '用'
})

// 获取用户头像背景色
const avatarStyle = computed(() => {
  // 如果有头像URL，不需要设置背景色
  if (props.avatar) {
    return {}
  }

  if (props.isSelf) {
    // 如果是自己的消息，使用与登录头像相同的颜色生成逻辑
    const username = userStore.userInfo?.username
    return {
      backgroundColor: getUserColor(username),
      color: 'white',
      fontWeight: '500',
      fontSize: '16px',
      textTransform: 'uppercase'
    }
  }

  // 如果是对方的消息，优先使用传入的contactId
  if (props.contactId) {
    return {
      backgroundColor: getContactAvatarColor(props.contactId),
      color: 'white',
      fontWeight: '500',
      fontSize: '16px',
      textTransform: 'uppercase'
    }
  }

  // 其次使用消息中的senderId
  const senderId = props.message.senderId || props.message.sender?.id
  if (senderId) {
    return {
      backgroundColor: getContactColor(senderId),
      color: 'white',
      fontWeight: '500',
      fontSize: '16px',
      textTransform: 'uppercase'
    }
  }

  // 如果没有发送者ID，使用默认样式
  return {}
})

// 音频相关
const audioElement = ref(null)
const isPlaying = ref(false)
const audioProgress = ref(0)
const audioDurationRef = ref(0)
const canPlay = ref(false)

// 格式化音频时间
const formatAudioTime = (seconds) => {
  if (!seconds) return '0:00'
  const minutes = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${minutes}:${secs < 10 ? '0' : ''}${secs}`
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  if (bytes < 1024) return bytes + ' B'
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB'
  if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB'
  return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
}

// 播放/暂停音频
const toggleAudio = () => {
  if (!audioElement.value) return

  if (isPlaying.value) {
    audioElement.value.pause()
  } else {
    audioElement.value.play()
  }

  isPlaying.value = !isPlaying.value
}

// 更新音频进度
const updateAudioProgress = () => {
  if (!audioElement.value) return

  const currentTime = audioElement.value.currentTime
  const duration = audioElement.value.duration

  if (duration) {
    audioProgress.value = (currentTime / duration) * 100
  }
}

// 音频播放结束
const audioEnded = () => {
  isPlaying.value = false
  audioProgress.value = 0
}

// 音频加载完成
const audioLoaded = () => {
  if (!audioElement.value) return

  canPlay.value = true
  audioDurationRef.value = audioElement.value.duration || audioDuration || 0
}

// 音频加载错误
const audioError = () => {
  canPlay.value = false
}

// 视频相关
const videoElement = ref(null)
const videoPlaying = ref(false)

// 播放视频
const playVideo = () => {
  if (!videoElement.value) return

  videoElement.value.play()
  videoPlaying.value = true
}

// 下载文件
const downloadFile = () => {
  const fileUrl = getAttachmentInfo.value?.fileUrl
  if (!fileUrl) return

  // 创建下载链接
  const link = document.createElement('a')
  link.href = fileUrl
  link.download = fileName || `文件${getAttachmentInfo.value?.fileId || Date.now()}`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 计算消息的已读状态
const isReadStatus = computed(() => {
  // 使用多种可能的已读状态标识
  return props.message.isRead === 1 ||
         isRead === 1 ||
         props.message.isRead === true ||
         isRead === true ||
         props.message.status === 'READ' ||
         status === 'READ';
});

onMounted(() => {
  // 控制台输出消息状态，用于调试
  console.log(`消息ID: ${props.message.id}, 发送者: ${props.message.senderId}, 状态: ${props.message.status || status}, isRead: ${props.message.isRead || isRead}`);

  // 监听视频播放状态
  if (videoElement.value) {
    videoElement.value.addEventListener('play', () => {
      videoPlaying.value = true
    })

    videoElement.value.addEventListener('pause', () => {
      videoPlaying.value = false
    })

    videoElement.value.addEventListener('ended', () => {
      videoPlaying.value = false
    })
  }
})
</script>

<style scoped>
.message-item {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
  animation: fadeIn 0.2s ease-out;
  position: relative;
  padding: 0 4px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-self {
  flex-direction: row-reverse;
}

/* 添加连续消息样式 */
.message-item + .message-item {
  margin-top: 2px;
}

/* 同一发送者的连续消息 */
.message-item.consecutive .message-avatar {
  opacity: 0;
  visibility: hidden;
}

.message-avatar {
  margin-top: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 36px;
  height: 36px;
  flex-shrink: 0;
}

.message-avatar :deep(.el-avatar) {
  border: 2px solid #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  transition: all 0.2s;
  width: 36px !important;
  height: 36px !important;
  font-size: 14px !important;
}

.message-avatar :deep(.el-avatar:hover) {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

/* 添加在线状态指示器 */
.message-avatar::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4dc247;
  border: 1.5px solid #fff;
  display: none; /* 默认隐藏，只在需要时显示 */
}

.message-avatar.online::after {
  display: block;
}

.message-content {
  margin: 0 8px;
  max-width: 65%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 为自己发送的消息添加尾巴 */
.message-self .message-bubble::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: -8px;
  width: 8px;
  height: 13px;
  background-color: #e1ffc7;
  clip-path: polygon(0 0, 0% 100%, 100% 100%);
  border-bottom-right-radius: 16px;
  border-right: 1px solid rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid rgba(0, 0, 0, 0.02);
}

/* 为接收的消息添加尾巴 */
.message-bubble::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -8px;
  width: 8px;
  height: 13px;
  background-color: #ffffff;
  clip-path: polygon(100% 0, 0 100%, 100% 100%);
  border-bottom-left-radius: 16px;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.message-bubble {
  padding: 10px 14px;
  border-radius: 16px 16px 16px 0;
  background-color: #ffffff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
  word-break: break-word;
  position: relative;
  transition: all 0.2s;
  max-width: 100%;
  font-size: 15px;
  line-height: 1.4;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.message-bubble:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.06);
}

.message-self .message-bubble {
  background-color: #e1ffc7;
  border-radius: 16px 16px 0 16px;
  color: #303030;
  border: 1px solid rgba(0, 0, 0, 0.02);
}

.message-text {
  white-space: pre-wrap;
  line-height: 1.5;
}

.message-image {
  max-width: 300px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.image-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.image-preview {
  width: 100%;
  border-radius: 12px 12px 0 0; /* 顶部圆角，底部直角 */
  cursor: pointer;
  transition: transform 0.3s;
  min-height: 100px;
  max-height: 300px;
  object-fit: contain;
}

.image-preview:hover {
  transform: scale(1.02);
}

/* 图片说明文字 */
.image-caption {
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 14px;
  border-radius: 0 0 12px 12px; /* 底部圆角，顶部直角 */
  word-break: break-word;
  max-width: 100%;
  box-sizing: border-box;
}

/* 错误状态下的说明文字 */
.error-caption {
  border-radius: 12px;
  margin-top: 8px;
  background-color: rgba(245, 245, 245, 0.9);
}

/* 多图片容器 */
.multi-image-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 4px;
  width: 100%;
  max-width: 300px;
  border-radius: 12px 12px 0 0; /* 顶部圆角，底部直角 */
  overflow: hidden;
}

/* 多图片说明文字 */
.multi-image-caption {
  border-radius: 0 0 12px 12px; /* 底部圆角，顶部直角 */
  margin-top: 0;
  width: 100%;
  max-width: 300px;
}

/* 多图片预览 */
.multi-image-preview {
  width: 100%;
  height: 120px;
  cursor: pointer;
  transition: transform 0.3s;
  object-fit: cover;
}

.multi-image-preview:hover {
  transform: scale(1.05);
}

.image-loading-container {
  display: flex;
  flex-direction: column;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 8px;
  min-width: 200px;
  min-height: 150px;
  margin-bottom: 8px;
}

.image-loading, .image-error {
  width: 100%;
  height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #909399;
  gap: 8px;
  border-radius: 8px;
}

.image-error {
  background-color: #fef0f0;
  color: #f56c6c;
  cursor: pointer;
}

.image-error:hover {
  background-color: #fde2e2;
}

.image-error .el-icon {
  font-size: 32px;
}

.debug-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  word-break: break-all;
  max-height: 40px;
  overflow: auto;
}

.message-audio {
  width: 240px;
}

.audio-player {
  display: flex;
  align-items: center;
  gap: 12px;
}

.play-button {
  flex-shrink: 0;
}

.audio-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.audio-waveform {
  height: 24px;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
}

.waveform-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(200,200,200,0.5) 1px, transparent 1px);
  background-size: 5px 100%;
}

.waveform-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, rgba(24,144,255,0.5) 1px, rgba(24,144,255,0.2) 1px);
  background-size: 5px 100%;
  transition: width 0.1s linear;
}

.message-self .waveform-progress {
  background: linear-gradient(90deg, rgba(82,196,26,0.5) 1px, rgba(82,196,26,0.2) 1px);
  background-size: 5px 100%;
}

.audio-time {
  font-size: 12px;
  color: #909399;
  text-align: right;
}

.message-video {
  max-width: 320px;
}

.video-container {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
}

.video-player {
  width: 100%;
  border-radius: 12px;
  background-color: #000;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.play-icon {
  font-size: 48px;
  color: #fff;
  opacity: 0.8;
  transition: opacity 0.3s, transform 0.3s;
}

.video-overlay:hover .play-icon {
  opacity: 1;
  transform: scale(1.1);
}

.message-file {
  width: 240px;
}

.file-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 32px;
  color: #1890ff;
}

.file-info {
  flex: 1;
  overflow: hidden;
}

.file-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.download-button {
  flex-shrink: 0;
}

.message-meta {
  display: flex;
  align-items: center;
  margin-top: 2px;
  padding-left: 2px;
  font-size: 10px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.message-item:hover .message-meta {
  opacity: 1;
}

.message-meta-self {
  justify-content: flex-end;
  padding-right: 2px;
}

.message-time {
  font-size: 10px;
  color: #8696a0;
  margin-right: auto;
}

.message-meta-self .message-time {
  margin-left: auto;
  margin-right: 0;
}

.message-status {
  display: flex;
  align-items: center;
  margin-left: 4px;
}

.status-icon {
  font-size: 13px;
  color: #8696a0;
}

.status-icon.read {
  color: #53bdeb;
  font-size: 14px;
}

.status-icon.sending {
  color: #909399;
  font-size: 14px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-icon.failed {
  color: #f15c6d;
}

.status-text {
  margin-left: 4px;
  font-size: 11px;
  color: #8696a0;
}

.status-text.error {
  color: #f15c6d;
}

/* 添加消息时间悬浮样式 */
.message-bubble .message-time-hover {
  position: absolute;
  top: -18px;
  font-size: 10px;
  color: #8696a0;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 1px 6px;
  border-radius: 10px;
  opacity: 0;
  transition: opacity 0.2s;
  white-space: nowrap;
}

.message-self .message-time-hover {
  right: 8px;
}

.message-bubble:hover .message-time-hover {
  opacity: 1;
}
</style>