package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.HomeStatisticsDTO;

import java.time.LocalDate;
import java.util.Map;

/**
 * 统计服务接口
 */
public interface StatisticsService {
    
    /**
     * 记录用户活动
     * @param userId 用户ID
     */
    void recordUserActivity(Long userId);
    
    /**
     * 获取首页统计数据
     * @return 首页统计数据
     */
    Result<HomeStatisticsDTO> getHomePageStatistics();
    
    /**
     * 获取活跃用户统计数据
     * @return 活跃用户统计数据
     */
    Result<Map<String, Object>> getActiveUsersStatistics();
    
    /**
     * 获取统计数据趋势
     * @param statType 统计类型
     * @param days 天数
     * @return 统计数据趋势
     */
    Result<Map<String, Object>> getStatisticsTrend(String statType, int days);
    
    /**
     * 更新统计数据缓存
     */
    void updateStatisticsCache();
    
    /**
     * 保存统计数据到数据库
     */
    void saveStatisticsToDatabase();
}
