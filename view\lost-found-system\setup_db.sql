-- 显示数据库
SHOW DATABASES;

-- 使用lost_found数据库
USE lost_found;

-- 显示所有表
SHOW TABLES;

-- 检查是否存在system_logs表（用于触发器记录）
SHOW TABLES LIKE 'system_logs';

-- 检查是否存在触发器
SHOW TRIGGERS;

-- 创建匹配通知表
CREATE TABLE IF NOT EXISTS match_notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '接收通知的用户ID',
    match_history_id BIGINT COMMENT '匹配历史ID',
    item_id BIGINT NOT NULL COMMENT '匹配物品ID',
    item_type VARCHAR(10) NOT NULL COMMENT '匹配物品类型（LOST/FOUND）',
    similarity FLOAT NOT NULL COMMENT '匹配相似度',
    title VARCHAR(100) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_match_history_id (match_history_id),
    INDEX idx_item_id_type (item_id, item_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='匹配通知表';

-- 检查system_configs表是否存在
SHOW TABLES LIKE 'system_configs';

-- 如果system_configs表存在，添加配置项
-- 注意：如果表不存在，这条语句会失败，但不会影响后续语句
INSERT IGNORE INTO system_configs (config_key, config_value, description, created_at, updated_at)
VALUES 
('match.notification.similarity-threshold', '0.7', '触发匹配通知的相似度阈值', NOW(), NOW()),
('match.notification.auto-notify', 'true', '是否启用自动匹配通知功能', NOW(), NOW());

-- 删除触发器
DROP TRIGGER IF EXISTS after_lost_item_insert;
DROP TRIGGER IF EXISTS after_found_item_insert;
DROP TRIGGER IF EXISTS after_lost_item_audit_approved;
DROP TRIGGER IF EXISTS after_found_item_audit_approved;

-- 检查是否存在system_logs表，如果存在且不再需要，可以删除
-- DROP TABLE IF EXISTS system_logs;

-- 显示所有表（确认更改）
SHOW TABLES;
