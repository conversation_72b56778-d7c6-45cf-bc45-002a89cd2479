<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tomato.lostfoundsystem.mapper.FoundItemMapper">
    <insert id="insertFoundItem" parameterType="com.tomato.lostfoundsystem.entity.FoundItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO found_items (
            user_id,
            item_name,
            description,
            found_time,
            found_location,
            image_url,
            status,
            created_at,
            audit_status
        ) VALUES (
            #{userId},
            #{itemName},
            #{description},
            #{foundTime},
            #{foundLocation},
            #{imageUrl},
            #{status},
            #{createdAt},
            #{auditStatus}
        )
    </insert>

    <select id="selectFoundItems" resultType="com.tomato.lostfoundsystem.entity.FoundItem">
        SELECT
        fi.id AS id,
        fi.user_id,
        fi.item_name,
        fi.description,
        fi.found_time,
        fi.found_location,
        fi.image_url,
        fi.status,
        fi.created_at,
        users.username,
        users.avatar
        FROM found_items AS fi
        JOIN
        users ON fi.user_id = users.id
        <where>
            <!-- 只显示已审核通过的拾物信息 -->
            AND fi.audit_status = 'APPROVED'
            <if test="keyword != null and keyword != ''">
                AND fi.item_name LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="foundLocation != null and foundLocation != ''">
                AND fi.found_location LIKE CONCAT('%', #{foundLocation}, '%')
            </if>
            <if test="status != null and status != ''">
                AND fi.status = #{status}
            </if>
            <if test="startDateTime != null and endDateTime != null">
                <choose>
                    <when test="timeFilterType == 'foundTime'">
                        AND fi.found_time BETWEEN #{startDateTime} AND #{endDateTime}
                    </when>
                    <when test="timeFilterType == 'createdAt'">
                        AND fi.created_at BETWEEN #{startDateTime} AND #{endDateTime}
                    </when>
                    <otherwise>
                        AND fi.created_at BETWEEN #{startDateTime} AND #{endDateTime}
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY fi.found_time DESC
    </select>

    <!-- 根据拾物ID查询拾物详情 -->
    <select id="selectFoundItemDetailsById" resultType="com.tomato.lostfoundsystem.dto.FoundItemDetailDTO">
        SELECT
            fi.id AS id,
            fi.user_id AS userId,
            fi.item_name AS itemName,
            fi.description AS description,
            fi.found_time AS foundTime,
            fi.found_location AS foundLocation,
            fi.image_url AS imageUrl,
            fi.status AS status,
            fi.created_at As createdAt,
            fi.audit_status AS auditStatus,
            CASE
                WHEN fi.audit_status = 'PENDING' THEN '待审核'
                WHEN fi.audit_status = 'APPROVED' THEN '已通过'
                WHEN fi.audit_status = 'REJECTED' THEN '已拒绝'
                ELSE fi.audit_status
            END AS auditStatusDescription,
            u.username AS username
        FROM found_items fi
                 JOIN users u ON fi.user_id = u.id
        WHERE fi.id = #{id}
    </select>

    <!-- 更新拾物信息 -->
    <update id="updateById" parameterType="com.tomato.lostfoundsystem.entity.FoundItem">
        UPDATE found_items
        <set>
            <if test="itemName != null">item_name = #{itemName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="foundTime != null">found_time = #{foundTime},</if>
            <if test="foundLocation != null">found_location = #{foundLocation},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
        </set>
        WHERE id = #{id} AND user_id = #{userId}
    </update>

    <!-- 更新拾物审核状态 -->
    <update id="updateAuditStatus" parameterType="com.tomato.lostfoundsystem.entity.FoundItem">
        UPDATE found_items
        SET audit_status = #{auditStatus, typeHandler=org.apache.ibatis.type.EnumTypeHandler}
        WHERE id = #{id}
    </update>

    <!-- 更新拾物状态 -->
    <update id="updateFoundItemStatus">
        UPDATE found_items
        SET status = #{status}
        WHERE id = #{id}
    </update>

    <!-- 根据拾物ID查询拾物信息 -->
    <select id="selectById" resultType="com.tomato.lostfoundsystem.entity.FoundItem">
        SELECT * FROM found_items WHERE id = #{id}
    </select>

    <!-- 删除拾物信息 -->
    <delete id="deleteById">
        DELETE FROM found_items WHERE id = #{id}
    </delete>

    <select id="findFoundItemsByUserId" resultType="com.tomato.lostfoundsystem.entity.FoundItem">
        SELECT
        fi.id AS id,
        fi.user_id AS userId,
        fi.item_name AS itemName,
        fi.description AS description,
        fi.found_time AS foundTime,
        fi.found_location AS foundLocation,
        fi.image_url AS imageUrl,
        fi.status AS status,
        fi.created_at AS createdAt,
        fi.audit_status AS auditStatus
        FROM
        found_items AS fi
        WHERE
        fi.user_id = #{userId}  <!-- 根据 userId 查询拾物信息 -->
    </select>

    <!-- 查询拾物列表（管理员） -->
    <select id="selectFoundItemList" resultType="com.tomato.lostfoundsystem.dto.FoundItemDetailDTO">
        SELECT
            fi.id AS id,
            fi.user_id AS userId,
            fi.item_name AS itemName,
            fi.description AS description,
            fi.found_time AS foundTime,
            fi.found_location AS foundLocation,
            fi.image_url AS imageUrl,
            fi.status AS status,
            fi.audit_status AS auditStatus,
            CASE
                WHEN fi.audit_status = 'PENDING' THEN '待审核'
                WHEN fi.audit_status = 'APPROVED' THEN '已通过'
                WHEN fi.audit_status = 'REJECTED' THEN '已拒绝'
                ELSE fi.audit_status
            END AS auditStatusDescription,
            fi.created_at AS createdAt,
            fi.created_at AS updatedAt,
            u.username AS username
        FROM
            found_items fi
        JOIN
            users u ON fi.user_id = u.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (fi.item_name LIKE CONCAT('%', #{keyword}, '%')
                OR fi.found_location LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                AND fi.audit_status = #{auditStatus}
            </if>
            <if test="status != null and status != ''">
                AND fi.status = #{status}
            </if>
            <if test="startDate != null and endDate != null">
                AND fi.created_at BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="userId != null">
                AND fi.user_id = #{userId}
            </if>
        </where>
        ORDER BY fi.created_at DESC
    </select>

    <!-- 根据审核状态统计拾物数量 -->
    <select id="countByAuditStatus" resultType="int">
        SELECT COUNT(*)
        FROM found_items
        WHERE audit_status = #{auditStatus}
    </select>

    <!-- 根据状态和审核状态统计拾物数量 -->
    <select id="countByStatusAndAuditStatus" resultType="int">
        SELECT COUNT(*)
        FROM found_items
        WHERE status = #{status} AND audit_status = #{auditStatus}
    </select>

</mapper>
