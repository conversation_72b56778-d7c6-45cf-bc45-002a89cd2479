package com.tomato.lostfoundsystem.service.Impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tomato.lostfoundsystem.dto.ChatMessageDTO;
import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.dto.ReadReceiptDTO;
import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.entity.Conversation;
import com.tomato.lostfoundsystem.entity.MessageAttachment;
import com.tomato.lostfoundsystem.entity.MessageReadStatus;
import com.tomato.lostfoundsystem.enums.FileType;
import com.tomato.lostfoundsystem.enums.MessageType;
import com.tomato.lostfoundsystem.mapper.ChatMessageMapper;
import com.tomato.lostfoundsystem.mapper.ConversationMapper;
import com.tomato.lostfoundsystem.mapper.MessageAttachmentMapper;
import com.tomato.lostfoundsystem.mapper.MessageReadStatusMapper;
import com.tomato.lostfoundsystem.service.AsyncTaskService;
import com.tomato.lostfoundsystem.service.ChatMessageService;
import com.tomato.lostfoundsystem.service.KafkaProducerService;
import com.tomato.lostfoundsystem.service.RedisService;
import com.tomato.lostfoundsystem.utils.AliyunOSSUtil;
import com.tomato.lostfoundsystem.utils.FFmpegUtils;
import com.tomato.lostfoundsystem.utils.FileValidationUtil;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.tomato.lostfoundsystem.enums.FileType.*;

@Transactional
@Service
@Slf4j
public class ChatMessageServiceImpl implements ChatMessageService {

    @Autowired
    private ChatMessageMapper chatMessageMapper;

    @Autowired
    private MessageReadStatusMapper messageReadStatusMapper;

    @Autowired
    private RedisService redisService;  // 使用 RedisService 来管理会话和已读消息

    @Autowired
    private KafkaProducerService kafkaProducerService;  // 使用 Kafka 生产者服务

    @Autowired
    private SimpMessagingTemplate messagingTemplate;  // 用于推送消息

    @Autowired
    private MessageAttachmentMapper messageAttachmentMapper;

    @Autowired
    private ConversationMapper conversationMapper;  // 用于更新会话的最后消息时间

    @Autowired
    private ConversationServiceImpl conversationService; // 用于更新会话信息

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private FileValidationUtil fileValidationUtil;


    @Transactional
    @Override
    public MessageDTO saveChatMessage(MessageDTO messageDTO, MultipartFile file) {
        log.info("开始保存聊天消息: {}", messageDTO);

        try {
            // 1. 创建聊天消息对象
            ChatMessage chatMessage = new ChatMessage();
            chatMessage.setSenderId(messageDTO.getSenderId());
            chatMessage.setReceiverId(messageDTO.getReceiverId());
            chatMessage.setMessage(messageDTO.getMessage());
            chatMessage.setMessageType(messageDTO.getMessageType());
            chatMessage.setTimestamp(new Date());

            // 2. 插入聊天消息并获取生成的 message_id
            chatMessageMapper.insertChatMessage(chatMessage);
            Long messageId = chatMessage.getId();
            log.info("聊天消息已保存，ID: {}", messageId);

            // 3. 初始化接收方的已读状态为未读
            MessageReadStatus readStatus = new MessageReadStatus();
            readStatus.setMessageId(messageId);
            readStatus.setUserId(chatMessage.getReceiverId());
            readStatus.setIsRead(false);
            messageReadStatusMapper.insert(readStatus);
            log.info("已初始化消息已读状态");

            // 4. 处理附件（如果有文件）
            String fileUrl = null;
            Long fileSize = null;

            if (file != null && !file.isEmpty()) {
                log.info("处理附件: {}, 大小: {} 字节", file.getOriginalFilename(), file.getSize());

                // 获取文件类型
                FileType fileType = messageDTO.getMessageType().getFileType();

                // 验证文件大小
                if (!fileValidationUtil.validateFileSize(file)) {
                    double fileSizeMB = file.getSize() / (1024.0 * 1024.0);
                    log.error("文件大小超过限制: {} MB, 最大允许: {}",
                            String.format("%.2f", fileSizeMB), fileValidationUtil.getReadableMaxFileSize());
                    throw new RuntimeException(String.format("文件大小 (%.2f MB) 超过限制: %s",
                            fileSizeMB, fileValidationUtil.getReadableMaxFileSize()));
                }

                // 验证文件类型
                if (!fileValidationUtil.validateFileType(file, fileType)) {
                    log.error("文件类型不允许: {}, 期望类型: {}",
                            file.getOriginalFilename(), fileType);
                    throw new RuntimeException("不支持的文件类型");
                }

                // 创建附件记录
                MessageAttachment messageAttachment = new MessageAttachment();
                messageAttachment.setMessageId(messageId);
                messageAttachment.setFileSize(file.getSize());
                messageAttachment.setFileType(fileType);

                // 同步上传文件，确保返回URL
                fileUrl = AliyunOSSUtil.uploadFile(file, fileType);
                messageAttachment.setFileUrl(fileUrl);
                fileSize = file.getSize();

                // 保存附件基本信息
                messageAttachmentMapper.insertMessageAttachment(messageAttachment);
                Long attachmentId = messageAttachment.getId();
                log.info("附件基本信息已保存，ID: {}, URL: {}", attachmentId, fileUrl);

                // 异步处理媒体时长（如果是音频或视频）
                if (fileType == FileType.AUDIO || fileType == FileType.VIDEO) {
                    asyncTaskService.processFileAsync(file, attachmentId, messageId);
                    log.info("已启动异步文件处理任务");
                }
            } else {
                // 没有附件，设置默认时长
                chatMessage.setAudioDuration(0);
                chatMessage.setVideoDuration(0);
                chatMessageMapper.updateChatMessageWithDuration(chatMessage);
                log.info("没有附件，已设置默认时长");
            }

            // 5. 设置消息ID和时间戳并推送消息
            messageDTO.setId(messageId); // 设置数据库生成的消息ID
            messageDTO.setTimestamp(chatMessage.getTimestamp().getTime()); // 设置时间戳
            messageDTO.setFileUrl(fileUrl); // 设置文件URL
            messageDTO.setFileSize(fileSize); // 设置文件大小
            messageDTO.setIsRead(false); // 设置为未读

            // 添加详细日志，记录文件URL
            if (fileUrl != null) {
                log.info("设置文件URL: {}", fileUrl);
            } else {
                log.warn("文件URL为空");
            }

            // 保持clientMessageId不变，用于前端消息ID映射
            if (messageDTO.getClientMessageId() != null) {
                log.info("保留clientMessageId: {}", messageDTO.getClientMessageId());
            }

            // 检查接收者是否在线
            boolean isReceiverOnline = redisService.isUserOnline(chatMessage.getReceiverId());

            // 记录详细的在线状态日志
            log.info("接收者 {} 在线状态检查结果: {}", chatMessage.getReceiverId(), isReceiverOnline ? "在线" : "离线");

            // 不再设置离线状态，只记录日志
            log.info("接收者离线状态: {}", !isReceiverOnline ? "离线" : "在线");

            // 使用事务同步器，确保只有在事务成功提交后才发送消息到Kafka和更新会话信息
            final Long receiverId = chatMessage.getReceiverId();
            final MessageDTO finalMessageDTO = messageDTO;
            final ChatMessage finalChatMessage = chatMessage;

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    log.info("事务已提交，消息ID: {}，准备发送消息到Kafka", finalMessageDTO.getId());

                    // 无论接收者是否在线，都将消息发送到Kafka
                    try {
                        // 使用Kafka生产者服务发送消息
                        kafkaProducerService.sendChatMessage(finalMessageDTO);
                        log.info("消息已发送到Kafka: {}", finalMessageDTO.getId());
                    } catch (Exception e) {
                        log.error("发送消息到Kafka失败: {}", e.getMessage(), e);

                        // 发送失败时，使用异步任务服务重试
                        try {
                            asyncTaskService.storeOfflineMessageObjectAsync(finalMessageDTO);
                            log.info("消息已通过异步任务服务重新发送到Kafka");
                        } catch (Exception retryError) {
                            log.error("重试发送消息到Kafka失败: {}", retryError.getMessage(), retryError);
                        }
                    }

                    // 更新会话信息
                    try {
                        // 使用会话服务更新会话的最后一条消息信息
                        conversationService.updateConversationWithMessage(finalChatMessage);

                        // 同时更新Redis中的最后一条消息信息
                        // 为发送者存储一份
                        redisService.storeLastMessage(
                            finalChatMessage.getSenderId(),
                            finalChatMessage.getReceiverId(),
                            finalChatMessage.getId(),
                            finalChatMessage.getMessage(),
                            finalChatMessage.getMessageType().toString(),
                            finalChatMessage.getTimestamp().getTime(),
                            finalMessageDTO.getFileUrl()
                        );

                        // 为接收者存储一份
                        redisService.storeLastMessage(
                            finalChatMessage.getReceiverId(),
                            finalChatMessage.getSenderId(),
                            finalChatMessage.getId(),
                            finalChatMessage.getMessage(),
                            finalChatMessage.getMessageType().toString(),
                            finalChatMessage.getTimestamp().getTime(),
                            finalMessageDTO.getFileUrl()
                        );

                        log.info("Redis中的最后一条消息信息已更新");

                        // 如果接收者不在线，增加未读计数
                        if (!redisService.isUserOnline(receiverId)) {
                            // 更新数据库中的未读计数
                            conversationService.incrementUnreadCount(receiverId, finalChatMessage.getSenderId());

                            // 同时更新Redis中的未读计数
                            int newCount = redisService.incrementUnreadCount(receiverId, finalChatMessage.getSenderId());
                            log.info("接收者不在线，已增加未读计数 - 数据库和Redis都已更新，新计数: {}", newCount);
                        }

                        log.info("会话信息已更新");
                    } catch (Exception e) {
                        log.error("更新会话信息失败: {}", e.getMessage(), e);
                    }
                }

                // 实现其他必要的接口方法（空实现）
                @Override
                public void afterCompletion(int status) {
                    // 空实现
                }

                @Override
                public void beforeCommit(boolean readOnly) {
                    // 空实现
                }

                @Override
                public void beforeCompletion() {
                    // 空实现
                }

                @Override
                public void flush() {
                    // 空实现
                }
            });

            log.info("聊天消息处理完成: {}", messageId);
            return messageDTO;
        } catch (Exception e) {
            log.error("保存聊天消息失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存聊天消息和附件失败", e);
        }
    }


    //分页获取聊天记录和附件
    @Override
    public PageInfo<ChatMessageDTO> getChatHistory(Long userId, Long otherUserId, int page, int size) {
        // 使用 PageHelper 来进行分页查询
        PageHelper.startPage(page, size);

        // 执行查询
        List<ChatMessage> chatMessages = chatMessageMapper.getChatHistory(userId, otherUserId);

        // 将 ChatMessage 转换为 ChatMessageDTO
        List<ChatMessageDTO> chatMessageDTOs = convertToDTO(chatMessages, otherUserId);

        // 将转换后的列表包装成 PageInfo
        PageInfo<ChatMessageDTO> pageInfo = new PageInfo<>(chatMessageDTOs);

        return pageInfo;
    }

    // 将 ChatMessage 转换为 ChatMessageDTO
    private List<ChatMessageDTO> convertToDTO(List<ChatMessage> chatMessages) {
        // 将 ChatMessage 和附件封装到 ChatMessageDTO
        return chatMessages.stream()
                .map(chatMessage -> {
                    // 输出 ChatMessage 和附件的信息
                    log.info("转换消息：{}, 单个附件：{}, 多个附件：{}",
                            chatMessage,
                            chatMessage.getAttachment(),
                            chatMessage.getAttachments());

                    ChatMessageDTO dto;

                    // 优先使用多个附件
                    if (chatMessage.getAttachments() != null && !chatMessage.getAttachments().isEmpty()) {
                        dto = new ChatMessageDTO(chatMessage, chatMessage.getAttachments());
                        log.info("使用多个附件创建DTO，附件数量: {}", chatMessage.getAttachments().size());
                    } else if (chatMessage.getAttachment() != null) {
                        // 使用单个附件
                        dto = new ChatMessageDTO(chatMessage, chatMessage.getAttachment());
                        log.info("使用单个附件创建DTO，附件URL: {}", chatMessage.getAttachment().getFileUrl());
                    } else {
                        // 没有附件
                        dto = new ChatMessageDTO(chatMessage, (MessageAttachment)null);

                        // 如果是图片消息但没有附件，尝试从消息类型推断
                        if (chatMessage.getMessageType() == MessageType.IMAGE) {
                            log.warn("图片消息没有附件: {}", chatMessage.getId());

                            // 查询附件
                            List<MessageAttachment> attachments = messageAttachmentMapper.getAllAttachmentsByMessageId(chatMessage.getId());
                            if (attachments != null && !attachments.isEmpty()) {
                                dto = new ChatMessageDTO(chatMessage, attachments);
                                log.info("通过查询找到附件，附件数量: {}", attachments.size());
                            }
                        }
                    }

                    // 输出转换后的 ChatMessageDTO
                    log.info("转换后的 ChatMessageDTO: {}", dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    // 将 ChatMessage 转换为 ChatMessageDTO，并包含已读状态
    private List<ChatMessageDTO> convertToDTO(List<ChatMessage> chatMessages, Long receiverId) {
        // 将 ChatMessage 和附件封装到 ChatMessageDTO
        return chatMessages.stream()
                .map(chatMessage -> {
                    // 输出 ChatMessage 和附件的信息
                    log.info("转换消息：{}, 单个附件：{}, 多个附件：{}",
                            chatMessage,
                            chatMessage.getAttachment(),
                            chatMessage.getAttachments());

                    // 创建 ChatMessageDTO
                    ChatMessageDTO dto;

                    // 优先使用多个附件
                    if (chatMessage.getAttachments() != null && !chatMessage.getAttachments().isEmpty()) {
                        dto = new ChatMessageDTO(chatMessage, chatMessage.getAttachments());
                        log.info("使用多个附件创建DTO，附件数量: {}", chatMessage.getAttachments().size());
                    } else if (chatMessage.getAttachment() != null) {
                        // 使用单个附件
                        dto = new ChatMessageDTO(chatMessage, chatMessage.getAttachment());
                        log.info("使用单个附件创建DTO，附件URL: {}", chatMessage.getAttachment().getFileUrl());
                    } else {
                        // 没有附件
                        dto = new ChatMessageDTO(chatMessage, (MessageAttachment)null);

                        // 如果是图片消息但没有附件，尝试从消息类型推断
                        if (chatMessage.getMessageType() == MessageType.IMAGE) {
                            log.warn("图片消息没有附件: {}", chatMessage.getId());

                            // 查询附件
                            List<MessageAttachment> attachments = messageAttachmentMapper.getAllAttachmentsByMessageId(chatMessage.getId());
                            if (attachments != null && !attachments.isEmpty()) {
                                dto = new ChatMessageDTO(chatMessage, attachments);
                                log.info("通过查询找到附件，附件数量: {}", attachments.size());
                            }
                        }
                    }

                    // 如果消息是发给接收者的，则查询接收者是否已读
                    if (chatMessage.getReceiverId().equals(receiverId)) {
                        // 获取消息已读状态
                        Integer isRead = getMessageReadStatus(chatMessage.getId(), receiverId);
                        dto.setIsRead(isRead != null ? isRead : 0); // 默认未读
                    } else {
                        // 如果消息是接收者发送的，则默认为已读
                        dto.setIsRead(1); // 已读
                    }

                    // 输出转换后的 ChatMessageDTO
                    log.info("转换后的 ChatMessageDTO: {}", dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取消息的已读状态
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 0表示未读，1表示已读，null表示没有记录
     */
    public Integer getMessageReadStatus(Long messageId, Long userId) {
        try {
            Integer isRead = messageReadStatusMapper.getMessageReadStatus(messageId, userId);
            log.debug("消息ID {}，用户ID {} 的已读状态为 {}", messageId, userId, isRead);
            return isRead;
        } catch (Exception e) {
            log.error("获取消息已读状态失败: {}", e.getMessage(), e);
            return null;
        }
    }

    // 标记消息为已读
    @Override
    public void markAsRead(Long messageId, Long userId) {
        log.info("开始标记消息为已读 - 消息ID: {}, 用户ID: {}", messageId, userId);
        try {
            // 检查消息是否存在
            ChatMessage chatMessage = chatMessageMapper.getMessageById(messageId);
            if (chatMessage == null) {
                log.warn("标记消息为已读 - 消息不存在，消息ID: {}，可能是临时消息或事务尚未提交", messageId);

                // 检查是否是临时消息ID（以"temp-"开头）
                if (messageId != null && messageId.toString().startsWith("temp-")) {
                    log.info("这是一个临时消息ID ({}), 忽略此警告并继续处理", messageId);
                    // 创建一个虚拟消息对象，以便后续处理
                    chatMessage = new ChatMessage();
                    chatMessage.setId(messageId);
                    chatMessage.setSenderId(userId); // 假设当前用户是发送者
                    chatMessage.setReceiverId(userId); // 假设当前用户也是接收者
                } else {
                    // 非临时消息ID不存在，可能是事务尚未提交，尝试延迟重试
                    log.warn("非临时消息ID不存在: {}，可能是事务尚未提交，将进行延迟重试", messageId);

                    // 使用异步任务服务进行延迟重试
                    retryMarkAsRead(messageId, userId, 0);
                    return; // 不抛出异常，让异步重试处理
                }
            }
            log.info("找到消息 - 消息ID: {}, 发送者ID: {}, 接收者ID: {}",
                    messageId, chatMessage.getSenderId(), chatMessage.getReceiverId());

            // 检查当前已读状态
            Integer currentReadStatus = messageReadStatusMapper.getMessageReadStatus(messageId, userId);
            log.info("当前已读状态 - 消息ID: {}, 用户ID: {}, 已读状态: {}",
                    messageId, userId, currentReadStatus);

            // 更新数据库中的已读状态
            log.info("更新数据库中的已读状态 - 消息ID: {}, 用户ID: {}", messageId, userId);
            int updatedRows = messageReadStatusMapper.updateReadStatus(messageId, userId, true);
            log.info("数据库已读状态更新结果 - 更新行数: {}", updatedRows);

            if (updatedRows == 0) {
                log.warn("没有记录被更新，可能是消息读取状态记录不存在 - 消息ID: {}, 用户ID: {}", messageId, userId);
                // 如果没有更新任何行，可能是因为记录不存在，尝试插入一条新记录
                MessageReadStatus readStatus = new MessageReadStatus();
                readStatus.setMessageId(messageId);
                readStatus.setUserId(userId);
                readStatus.setIsRead(true);
                readStatus.setReadAt(new Date());
                messageReadStatusMapper.insert(readStatus);
                log.info("已插入新的消息读取状态记录 - 消息ID: {}, 用户ID: {}", messageId, userId);
            } else {
                log.info("数据库已读状态更新成功 - 消息ID: {}, 用户ID: {}", messageId, userId);
            }

            // 将已读消息存储到 Redis 中
            log.info("将已读消息存储到Redis - 用户ID: {}, 消息ID: {}", userId, messageId);
            redisService.storeReadMessage(userId, messageId);

            // 如果是接收者标记消息为已读，可能需要更新未读计数
            if (chatMessage.getReceiverId().equals(userId)) {
                // 获取发送者ID
                Long senderId = chatMessage.getSenderId();

                // 检查Redis中的未读计数，如果大于0则减少1
                int currentUnreadCount = redisService.getUnreadCount(userId, senderId);
                if (currentUnreadCount > 0) {
                    // 设置新的未读计数
                    redisService.setUnreadCount(userId, senderId, currentUnreadCount - 1);
                    log.info("已更新Redis中的未读计数 - 用户ID: {}, 联系人ID: {}, 新计数: {}",
                            userId, senderId, currentUnreadCount - 1);
                }
            }

            log.info("Redis已读状态更新成功");

            // 获取消息发送者ID
            if (!chatMessage.getSenderId().equals(userId)) {
                Long senderId = chatMessage.getSenderId();
                log.info("准备向发送者发送已读回执 - 发送者ID: {}", senderId);

                // 创建已读回执DTO
                List<Long> messageIds = new ArrayList<>();
                messageIds.add(messageId);
                ReadReceiptDTO readReceipt = new ReadReceiptDTO(userId, senderId, messageIds);
                log.info("已创建已读回执DTO: {}", readReceipt);

                // 使用AsyncTaskService异步推送已读回执
                asyncTaskService.pushReadReceiptToWebSocket(senderId, readReceipt);
                log.info("已异步推送消息 {} 的已读回执到用户 {}", messageId, senderId);
            } else {
                log.info("用户是消息发送者，不需要发送已读回执 - 用户ID: {}", userId);
            }

            // 再次检查更新后的已读状态
            Integer updatedReadStatus = messageReadStatusMapper.getMessageReadStatus(messageId, userId);
            log.info("更新后的已读状态 - 消息ID: {}, 用户ID: {}, 已读状态: {}",
                    messageId, userId, updatedReadStatus);

            log.info("标记消息为已读完成 - 消息ID: {}, 用户ID: {}", messageId, userId);
        } catch (Exception e) {
            log.error("标记消息 {} 为已读失败: {}", messageId, e.getMessage(), e);
            throw new RuntimeException("标记消息为已读失败: " + e.getMessage(), e);
        }
    }

    // 重试标记消息为已读
    private void retryMarkAsRead(Long messageId, Long userId, int retryCount) {
        if (retryCount >= 3) {
            log.error("标记消息为已读重试次数已达上限，放弃重试 - 消息ID: {}, 用户ID: {}", messageId, userId);
            throw new RuntimeException("消息不存在");
        }

        // 使用异步任务服务进行延迟重试
        final int nextRetryCount = retryCount + 1;
        final long delayMillis = 300 * (retryCount + 1); // 300ms, 600ms, 900ms 的延迟

        log.info("安排延迟重试标记消息为已读 - 消息ID: {}, 用户ID: {}, 重试次数: {}, 延迟: {}ms",
                messageId, userId, nextRetryCount, delayMillis);

        // 使用线程延迟执行，模拟异步调度任务
        new Thread(() -> {
            try {
                // 延迟指定时间
                Thread.sleep(delayMillis);

                log.info("开始重试标记消息为已读 (#{}) - 消息ID: {}, 用户ID: {}",
                        nextRetryCount, messageId, userId);

                // 再次检查消息是否存在
                ChatMessage chatMessage = chatMessageMapper.getMessageById(messageId);
                if (chatMessage == null) {
                    log.warn("重试标记消息为已读 - 消息仍不存在，将再次重试");
                    retryMarkAsRead(messageId, userId, nextRetryCount);
                    return;
                }

                log.info("重试成功：找到消息 - 消息ID: {}, 发送者ID: {}, 接收者ID: {}",
                        messageId, chatMessage.getSenderId(), chatMessage.getReceiverId());

                // 执行标记为已读的逻辑
                // 更新消息读取状态
                int updatedRows = messageReadStatusMapper.updateReadStatus(messageId, userId, true);

                if (updatedRows == 0) {
                    // 如果没有更新任何行，可能是因为记录不存在，尝试插入一条新记录
                    MessageReadStatus readStatus = new MessageReadStatus();
                    readStatus.setMessageId(messageId);
                    readStatus.setUserId(userId);
                    readStatus.setIsRead(true);
                    readStatus.setReadAt(new Date());
                    messageReadStatusMapper.insert(readStatus);
                }

                // 将已读消息存储到 Redis 中
                redisService.storeReadMessage(userId, messageId);

                // 发送已读回执
                if (!chatMessage.getSenderId().equals(userId)) {
                    Long senderId = chatMessage.getSenderId();
                    List<Long> messageIds = new ArrayList<>();
                    messageIds.add(messageId);
                    ReadReceiptDTO readReceipt = new ReadReceiptDTO(userId, senderId, messageIds);
                    asyncTaskService.pushReadReceiptToWebSocket(senderId, readReceipt);
                }

                log.info("重试成功：消息已标记为已读 - 消息ID: {}, 用户ID: {}", messageId, userId);
            } catch (Exception e) {
                log.error("重试标记消息为已读失败: {}", e.getMessage(), e);
                if (!(e instanceof RuntimeException && e.getMessage().equals("消息不存在"))) {
                    retryMarkAsRead(messageId, userId, nextRetryCount);
                }
            }
        }).start();
    }

    // 批量标记用户与联系人之间的所有未读消息为已读
    @Override
    @Transactional
    public void markAllAsRead(Long contactId, Long userId) {
        try {
            log.info("开始批量标记已读 - 用户ID: {}, 联系人ID: {}", userId, contactId);

            // 获取所有未读消息ID
            List<Long> unreadMessageIds = messageReadStatusMapper.getUnreadMessageIds(userId, contactId);

            // 更新消息已读状态
            if (unreadMessageIds != null && !unreadMessageIds.isEmpty()) {
                log.info("发现未读消息 {} 条", unreadMessageIds.size());

                // 批量更新已读状态
                messageReadStatusMapper.batchUpdateReadStatus(unreadMessageIds, userId, true);

                // 更新Redis缓存
                for (Long messageId : unreadMessageIds) {
                    redisService.storeReadMessage(userId, messageId);
                }

                // 向消息发送者发送已读回执
                try {
                    ReadReceiptDTO readReceipt = new ReadReceiptDTO(userId, contactId, unreadMessageIds);
                    // 使用AsyncTaskService异步推送已读回执
                    asyncTaskService.pushReadReceiptToWebSocket(contactId, readReceipt);
                    log.info("已异步推送批量已读回执到用户 {}, 消息数量: {}", contactId, unreadMessageIds.size());
                } catch (Exception e) {
                    log.error("发送批量已读回执失败: {}", e.getMessage());
                    // 不抛出异常，因为这不应该影响主要功能
                }
            } else {
                log.info("没有未读消息需要标记");
            }

            // 更新会话信息
            try {
                log.info("更新会话信息 - 用户ID: {}, 联系人ID: {}", userId, contactId);

                // 获取用户和联系人之间的会话
                Conversation conversation = conversationMapper.getConversation(userId, contactId);

                if (conversation != null) {
                    // 重置会话未读计数
                    conversation.setUnreadCount(0);
                    conversation.setUpdatedAt(new Date());
                    conversationMapper.update(conversation);

                    // 同时重置Redis中的未读计数
                    redisService.resetUnreadCount(userId, contactId);
                    log.info("会话未读计数已重置 - 会话ID: {}, 数据库和Redis都已更新", conversation.getId());
                } else {
                    log.warn("未找到用户 {} 和联系人 {} 之间的会话", userId, contactId);
                }
            } catch (Exception e) {
                log.error("更新会话信息失败: {}", e.getMessage(), e);
                // 不抛出异常，因为这不应该影响主要功能
            }

            log.info("批量标记已读完成 - 用户ID: {}, 联系人ID: {}", userId, contactId);
        } catch (Exception e) {
            log.error("批量标记已读失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量标记已读失败", e);
        }
    }
}
