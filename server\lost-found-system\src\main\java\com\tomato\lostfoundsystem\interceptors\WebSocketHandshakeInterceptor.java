package com.tomato.lostfoundsystem.interceptors;

import com.tomato.lostfoundsystem.utils.JWTUtil;
import com.tomato.lostfoundsystem.utils.RedisUtil;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import io.jsonwebtoken.Claims;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Component
public class WebSocketHandshakeInterceptor implements HandshakeInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketHandshakeInterceptor.class);
    private final SecurityUtil securityUtil;
    private final JWTUtil jwtUtil;
    private final RedisUtil redisUtil;

    // 构造器注入
    public WebSocketHandshakeInterceptor(SecurityUtil securityUtil, JWTUtil jwtUtil, RedisUtil redisUtil) {
        this.securityUtil = securityUtil;
        this.jwtUtil = jwtUtil;
        this.redisUtil = redisUtil;
    }

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        try {
            // 获取请求URL和查询参数
            String uri = request.getURI().toString();
            logger.info("WebSocket握手开始 - URI: {}", uri);

            // 尝试从URL参数中获取token
            String token = null;
            if (uri.contains("token=")) {
                token = uri.substring(uri.indexOf("token=") + 6);
                if (token.contains("&")) {
                    token = token.substring(0, token.indexOf("&"));
                }
                logger.info("从URL中提取的token: {}", token.substring(0, Math.min(10, token.length())) + "...");
            } else {
                logger.warn("URL中未找到token参数");
            }

            // 首先尝试从SecurityContext获取用户信息
            String username = securityUtil.getCurrentUsername();
            Long userId = securityUtil.getCurrentUserId();
            String userRole = securityUtil.getCurrentUserRole();

            logger.info("SecurityContext认证信息 - 用户名: {}, 用户ID: {}, 角色: {}",
                        username != null ? username : "null",
                        userId != null ? userId : "null",
                        userRole != null ? userRole : "null");

            // 如果SecurityContext中没有用户信息，但有token，则直接验证token
            if ((username == null || userId == null) && token != null) {
                logger.info("从SecurityContext未获取到用户信息，尝试直接验证token");

                // 解析token
                Claims claims = jwtUtil.parseToken(token);
                if (claims != null && !jwtUtil.isTokenExpired(token)) {
                    // 从token中获取用户信息
                    userId = claims.get("userId", Long.class);
                    username = claims.getSubject();
                    userRole = claims.get("roles", String.class);

                    // 验证Redis中是否存在该token
                    String redisKey = "jwt:user:" + userId;
                    String storedToken = redisUtil.get(redisKey);

                    if (storedToken != null && storedToken.equals(token)) {
                        logger.info("Token验证成功 - 用户名: {}, 用户ID: {}, 角色: {}",
                                   username, userId, userRole);
                    } else {
                        logger.warn("Redis中不存在token或已被替换");
                        return false;
                    }
                } else {
                    logger.warn("Token无效或已过期");
                    return false;
                }
            }

            // 如果有用户信息，则允许连接
            if (username != null && userId != null) {
                // 将用户信息存储到 WebSocket 会话中
                attributes.put("username", username);
                attributes.put("userId", userId);
                attributes.put("userRole", userRole);

                // 添加设备信息（可选）
                String userAgent = request.getHeaders().getFirst("User-Agent");
                if (userAgent != null) {
                    attributes.put("userAgent", userAgent);
                    logger.debug("用户代理: {}", userAgent);
                }

                // 添加IP地址（可选）
                String remoteAddress = request.getRemoteAddress().toString();
                if (remoteAddress != null) {
                    attributes.put("remoteAddress", remoteAddress);
                    logger.debug("远程地址: {}", remoteAddress);
                }

                logger.info("WebSocket握手成功 - 用户: {}, ID: {}", username, userId);
                return true;  // 用户已认证，允许连接
            } else {
                logger.warn("WebSocket握手失败: 未找到认证信息 - URI: {}", uri);
                return false;  // 如果没有认证信息，则拒绝 WebSocket 连接
            }
        } catch (Exception e) {
            logger.error("WebSocket握手过程中发生错误: {}", e.getMessage(), e);
            return false;  // 捕获异常并拒绝连接
        }
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        // 这里可以记录日志或做其他处理
        if (exception != null) {
            logger.error("WebSocket握手失败: {}", exception.getMessage());
        } else {
            // 获取请求URI
            String uri = request.getURI().toString();
            // 获取远程地址
            String remoteAddress = request.getRemoteAddress().toString();
            // 获取用户代理
            String userAgent = request.getHeaders().getFirst("User-Agent");

            logger.info("WebSocket握手成功完成 - URI: {}, 远程地址: {}", uri, remoteAddress);
            logger.debug("WebSocket握手成功 - 用户代理: {}", userAgent);
        }
    }
}
