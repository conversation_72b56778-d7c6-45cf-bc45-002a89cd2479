import request from '@/utils/request'

// 获取所有系统配置
export function getAllConfigs() {
  return request({
    url: '/api/admin/config',
    method: 'get'
  })
}

// 根据配置键获取配置
export function getConfigByKey(configKey) {
  return request({
    url: `/api/admin/config/${configKey}`,
    method: 'get'
  })
}

// 更新配置值
export function updateConfigValue(configKey, configValue) {
  return request({
    url: `/api/admin/config/${configKey}`,
    method: 'put',
    data: { configValue }
  })
}

// 批量更新配置
export function batchUpdateConfig(configs) {
  return request({
    url: '/api/admin/config/batch',
    method: 'put',
    data: configs
  })
}

// 添加配置
export function addConfig(config) {
  return request({
    url: '/api/admin/config',
    method: 'post',
    data: config
  })
}

// 删除配置
export function deleteConfig(configKey) {
  return request({
    url: `/api/admin/config/${configKey}`,
    method: 'delete'
  })
}

// 刷新配置缓存
export function refreshConfigCache() {
  return request({
    url: '/api/admin/config/refresh',
    method: 'post'
  })
}
