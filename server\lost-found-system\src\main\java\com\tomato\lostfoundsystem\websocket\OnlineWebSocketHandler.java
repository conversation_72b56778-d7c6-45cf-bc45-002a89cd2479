package com.tomato.lostfoundsystem.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.socket.messaging.SessionConnectEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Controller
public class OnlineWebSocketHandler {

    @Autowired
    private RedisService redisService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private com.tomato.lostfoundsystem.mapper.UserMapper userMapper;

    // 存储连接信息（用户ID -> 设备信息）
    private final Map<String, String> connectionInfo = new HashMap<>();

    @MessageMapping("/user/online")
    public void handleUserOnlineStatus(String userId, SimpMessageHeaderAccessor headerAccessor) {
        if (userId != null && !userId.isEmpty()) {
            try {
                String sessionId = headerAccessor.getSessionId();  // 获取 WebSocket 会话的唯一标识

                // 获取设备信息
                String userAgent = "unknown";
                if (headerAccessor.getSessionAttributes() != null) {
                    Object userAgentObj = headerAccessor.getSessionAttributes().get("userAgent");
                    if (userAgentObj != null) {
                        userAgent = userAgentObj.toString();
                    }
                }
                String deviceInfo = getDeviceInfo(userAgent);

                // 获取IP地址
                String remoteAddress = "unknown";
                if (headerAccessor.getSessionAttributes() != null) {
                    Object remoteAddressObj = headerAccessor.getSessionAttributes().get("remoteAddress");
                    if (remoteAddressObj != null) {
                        remoteAddress = remoteAddressObj.toString();
                    }
                }

                // 存储会话关联信息
                connectionInfo.put(sessionId, userId);

                // 检查用户是否已经在线
                boolean wasAlreadyOnline = redisService.isUserOnline(Long.valueOf(userId));

                // 存储到Redis（单设备登录，会自动断开旧会话）
                redisService.addUserSession(Long.valueOf(userId), sessionId, deviceInfo);

                log.info("用户上线 - ID: {}, 会话: {}, 设备: {}, IP: {}, 之前状态: {} (单设备登录)",
                         userId, sessionId, deviceInfo, remoteAddress, wasAlreadyOnline ? "在线" : "离线");

                // 无论用户之前是否在线，都广播上线状态（因为单设备登录会断开旧会话）
                // 创建状态消息
                Map<String, Object> statusData = new HashMap<>();
                statusData.put("userId", userId);
                statusData.put("online", true);
                statusData.put("timestamp", System.currentTimeMillis());
                statusData.put("lastActive", System.currentTimeMillis());

                // 转换为JSON
                String json = new ObjectMapper().writeValueAsString(statusData);

                // 推送用户上线状态到全局主题
                messagingTemplate.convertAndSend("/topic/onlineStatus", json);

                // 推送到用户特定的主题，供订阅该用户状态的客户端接收
                messagingTemplate.convertAndSend("/topic/onlineStatus/" + userId, json);

                log.info("已广播用户 {} 的上线状态", userId);

                // 广播在线用户数量
                broadcastOnlineUserCount();
            } catch (Exception e) {
                log.error("处理用户 {} 上线状态时出错: {}", userId, e.getMessage(), e);
            }
        } else {
            log.warn("收到空的用户ID上线请求");
        }
    }

    // 处理用户下线
    @MessageMapping("/user/offline")
    public void handleUserOfflineStatus(String userId, SimpMessageHeaderAccessor headerAccessor) {
        if (userId != null && !userId.isEmpty()) {
            try {
                String sessionId = headerAccessor.getSessionId();

                log.info("用户 {} 请求下线, 会话: {} (单设备登录)", userId, sessionId);

                // 从Redis中删除会话
                redisService.removeUserSession(Long.valueOf(userId), sessionId);

                // 移除本地连接信息
                connectionInfo.remove(sessionId);

                // 在单设备登录模式下，直接广播下线状态
                // 创建状态消息
                Map<String, Object> statusData = new HashMap<>();
                statusData.put("userId", userId);
                statusData.put("online", false);
                statusData.put("timestamp", System.currentTimeMillis());

                // 转换为JSON
                String json = new ObjectMapper().writeValueAsString(statusData);

                // 推送用户下线状态到全局主题
                messagingTemplate.convertAndSend("/topic/onlineStatus", json);

                // 推送到用户特定的主题，供订阅该用户状态的客户端接收
                messagingTemplate.convertAndSend("/topic/onlineStatus/" + userId, json);

                log.info("已广播用户 {} 的下线状态", userId);

                // 广播在线用户数量
                broadcastOnlineUserCount();
            } catch (Exception e) {
                log.error("处理用户 {} 下线状态时出错: {}", userId, e.getMessage(), e);
            }
        } else {
            log.warn("收到空的用户ID下线请求");
        }
    }

    // 处理心跳消息，更新用户在线状态
    @MessageMapping("/user/heartbeat")
    public void handleHeartbeat(SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();

        // 从本地映射或会话属性中获取用户ID
        Long userId = getUserIdFromSession(headerAccessor);

        if (userId != null && userId != 0) {
            // 更新心跳时间
            redisService.updateHeartbeat(userId, sessionId);
            log.debug("收到用户 {} 的心跳, 会话: {}", userId, sessionId);
        } else {
            log.warn("收到心跳但无法识别用户, 会话: {}", sessionId);
        }
    }

    /**
     * 处理临时离线消息（页面刷新或关闭）
     */
    @MessageMapping("/user/tempOffline")
    public void handleTempOffline(TempOfflineMessage message, SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();

        // 从消息中获取用户ID，如果没有则从会话中获取
        String userId = message.getUserId();
        if (userId == null || userId.isEmpty()) {
            Long userIdFromSession = getUserIdFromSession(headerAccessor);
            if (userIdFromSession != null) {
                userId = userIdFromSession.toString();
            } else {
                // 尝试从本地映射获取
                userId = connectionInfo.get(sessionId);
            }
        }

        if (userId != null && !userId.isEmpty()) {
            try {
                log.info("收到用户 {} 的临时离线消息, 会话: {}, 原因: {}",
                         userId, sessionId, message.getReason());

                // 标记会话为临时离线状态，但不立即删除
                // 延长会话过期时间，给用户一定时间重新连接
                redisService.markSessionAsTemporaryOffline(Long.valueOf(userId), sessionId);

                // 不广播用户离线状态，也不更新在线用户数量
                log.info("用户 {} 临时离线，保留会话状态", userId);
            } catch (Exception e) {
                log.error("处理用户 {} 临时离线消息时出错: {}", userId, e.getMessage(), e);
            }
        } else {
            log.warn("收到临时离线消息但无法识别用户, 会话: {}", sessionId);
        }
    }

    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();

        // 从会话属性获取用户ID
        Long userId = getUserIdFromSession(headerAccessor);

        // 如果会话属性中无法获取，则从本地映射中获取
        if (userId == null || userId == 0) {
            String userIdStr = connectionInfo.get(sessionId);
            if (userIdStr != null) {
                userId = Long.valueOf(userIdStr);
            }
        }

        if (userId != null && userId != 0) {
            try {
                // 在单设备登录模式下，直接删除会话并广播下线状态
                // 从Redis中删除会话
                redisService.removeUserSession(userId, sessionId);

                // 移除本地连接信息
                connectionInfo.remove(sessionId);

                // 广播下线状态
                // 创建状态消息
                Map<String, Object> statusData = new HashMap<>();
                statusData.put("userId", userId.toString());
                statusData.put("online", false);
                statusData.put("timestamp", System.currentTimeMillis());

                // 转换为JSON
                String json = new ObjectMapper().writeValueAsString(statusData);

                // 推送用户下线状态到全局主题
                messagingTemplate.convertAndSend("/topic/onlineStatus", json);

                // 推送到用户特定的主题，供订阅该用户状态的客户端接收
                messagingTemplate.convertAndSend("/topic/onlineStatus/" + userId, json);

                log.info("已广播用户 {} 的下线状态", userId);

                // 广播在线用户数量
                broadcastOnlineUserCount();

                log.info("User {} 连接断开, 会话: {}", userId, sessionId);
            } catch (Exception e) {
                log.error("Failed to handle disconnect for user {}: {}", userId, e.getMessage());
            }
        }
    }

    @EventListener
    public void handleWebSocketReconnectListener(SessionConnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        Long userId = getUserIdFromSession(headerAccessor);
        String sessionId = headerAccessor.getSessionId();

        // 记录连接事件，无论是否有用户ID
        log.info("WebSocket STOMP连接事件 - 会话ID: {}, 消息类型: {}",
                sessionId, event.getMessage().getHeaders().get("simpMessageType"));

        // 获取连接详情
        String destination = headerAccessor.getDestination();
        String userAgent = "unknown";
        String remoteAddress = "unknown";

        if (headerAccessor.getSessionAttributes() != null) {
            Object userAgentObj = headerAccessor.getSessionAttributes().get("userAgent");
            if (userAgentObj != null) {
                userAgent = userAgentObj.toString();
            }

            Object remoteAddressObj = headerAccessor.getSessionAttributes().get("remoteAddress");
            if (remoteAddressObj != null) {
                remoteAddress = remoteAddressObj.toString();
            }
        }

        log.info("WebSocket连接详情 - 目标: {}, 远程地址: {}",
                destination != null ? destination : "未指定", remoteAddress);
        log.debug("WebSocket连接用户代理: {}", userAgent);

        if (userId != null && userId != 0) {
            try {
                // 获取设备信息
                String deviceInfo = getDeviceInfo(userAgent);

                // 存储会话关联信息
                connectionInfo.put(sessionId, userId.toString());

                // 存储到Redis（单设备登录，会自动断开旧会话）
                redisService.addUserSession(userId, sessionId, deviceInfo);

                log.info("WebSocket连接成功 - 用户: {}, 会话: {}, 设备: {}", userId, sessionId, deviceInfo);

                // 创建状态消息
                Map<String, Object> statusData = new HashMap<>();
                statusData.put("userId", userId.toString());
                statusData.put("online", true);
                statusData.put("timestamp", System.currentTimeMillis());
                statusData.put("lastActive", System.currentTimeMillis());

                // 转换为JSON
                String json = new ObjectMapper().writeValueAsString(statusData);

                // 推送用户上线状态到全局主题
                messagingTemplate.convertAndSend("/topic/onlineStatus", json);

                // 推送到用户特定的主题，供订阅该用户状态的客户端接收
                messagingTemplate.convertAndSend("/topic/onlineStatus/" + userId, json);

                log.info("已广播用户 {} 的重新上线状态", userId);

                // 广播在线用户数量
                broadcastOnlineUserCount();
            } catch (Exception e) {
                log.error("处理用户 {} 连接时出错: {}", userId, e.getMessage(), e);
            }
        } else {
            log.warn("WebSocket连接 - 无法识别用户, 会话ID: {}", sessionId);
        }
    }

    // 检查用户在线状态
    @MessageMapping("/checkOnlineStatus/{userId}")
    public void checkUserOnlineStatus(@DestinationVariable String userId, SimpMessageHeaderAccessor headerAccessor) {
        boolean isOnline = redisService.isUserOnline(Long.valueOf(userId));
        String status = isOnline ? "online" : "offline";
        log.info("检查用户是否在线: {}, 状态: {}", userId, status);

        // 发送给请求检查的用户 - 使用标准路径 /queue/status
        if (headerAccessor.getUser() != null) {
            messagingTemplate.convertAndSendToUser(
                headerAccessor.getUser().getName(),
                "/queue/status",
                "User " + userId + " is " + status
            );
        } else {
            log.warn("无法获取用户信息，只进行广播发送");
            // 如果无法获取用户信息，只进行广播发送
        }

        // 广播用户状态（可选）
        messagingTemplate.convertAndSend("/topic/onlineStatus", "User " + userId + " is " + status);
    }

    /**
     * 获取所有在线用户
     * 前端通过 /app/getAllOnlineUsers 请求获取所有在线用户
     * 增强版：添加了更多的错误处理和降级策略
     */
    @MessageMapping("/getAllOnlineUsers")
    public void getAllOnlineUsers(SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor != null ? headerAccessor.getSessionId() : "unknown";
        try {
            log.info("收到获取所有在线用户请求，会话ID: {}", sessionId);

            // 安全检查：确保headerAccessor不为null
            if (headerAccessor == null) {
                log.error("headerAccessor为null，无法处理请求");
                return;
            }

            // 获取当前用户ID - 使用增强的方法，支持多种获取方式
            Long currentUserId = getUserIdFromSession(headerAccessor);

            // 即使无法识别用户，也继续处理请求，但记录警告
            if (currentUserId == null) {
                log.warn("无法识别请求用户，会话ID: {}，将使用降级策略", sessionId);
                // 继续处理，但不记录用户特定信息
            } else {
                log.info("识别到用户ID: {}, 会话ID: {}", currentUserId, sessionId);
            }

            // 获取所有在线用户会话
            Map<String, Object> onlineUsersMap = new HashMap<>();

            // 遍历Redis中的所有用户会话
            Map<Long, Boolean> onlineStatusMap = new HashMap<>();

            try {
                // 获取所有用户ID
                Set<Long> userIds = redisService.getAllUserIds();
                log.info("从Redis获取到 {} 个用户ID", userIds.size());

                // 检查每个用户是否在线
                for (Long userId : userIds) {
                    try {
                        boolean isOnline = redisService.isUserOnline(userId);
                        if (isOnline) {
                            onlineStatusMap.put(userId, true);
                        }
                    } catch (Exception e) {
                        log.warn("检查用户 {} 在线状态时出错: {}", userId, e.getMessage());
                        // 继续处理其他用户
                    }
                }
            } catch (Exception e) {
                log.error("获取用户列表时出错: {}", e.getMessage());
                // 降级：返回空列表
                onlineStatusMap = new HashMap<>();
            }

            // 转换为前端期望的格式
            Map<String, Boolean> result = onlineStatusMap.entrySet().stream()
                .collect(Collectors.toMap(
                    entry -> entry.getKey().toString(),
                    Map.Entry::getValue
                ));

            // 添加到响应中
            onlineUsersMap.put("onlineUsers", result);
            onlineUsersMap.put("timestamp", System.currentTimeMillis());
            onlineUsersMap.put("total", result.size());

            // 转换为JSON
            String json;
            try {
                json = new ObjectMapper().writeValueAsString(onlineUsersMap);
            } catch (Exception e) {
                log.error("序列化在线用户数据时出错: {}", e.getMessage());
                // 降级：创建简单的JSON字符串
                json = "{\"onlineUsers\":{},\"timestamp\":" + System.currentTimeMillis() + ",\"total\":0,\"error\":\"数据序列化失败\"}";
            }

            log.info("获取到 {} 个在线用户", result.size());

            // 发送给请求的用户 - 增强的错误处理
            boolean sentToUser = false;
            if (headerAccessor.getUser() != null) {
                try {
                    String username = headerAccessor.getUser().getName();
                    if (username != null && !username.isEmpty()) {
                        messagingTemplate.convertAndSendToUser(
                            username,
                            "/queue/onlineUsers",
                            json
                        );
                        sentToUser = true;
                        log.info("已发送在线用户列表给用户: {}", username);
                    } else {
                        log.warn("用户名为空，无法发送私人消息");
                    }
                } catch (Exception e) {
                    log.error("发送私人消息时出错: {}", e.getMessage());
                }
            }

            // 无论是否发送了私人消息，都广播到公共主题，确保所有客户端都能收到
            try {
                messagingTemplate.convertAndSend("/topic/onlineUsers", json);
                log.info("已广播在线用户列表到公共主题");
            } catch (Exception e) {
                log.error("广播在线用户列表时出错: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.error("获取所有在线用户时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 订阅特定联系人的在线状态
     * 前端通过 /app/subscribeContactStatus/{contactId} 订阅特定联系人的在线状态
     */
    @MessageMapping("/subscribeContactStatus/{contactId}")
    public void subscribeContactStatus(@DestinationVariable String contactId, SimpMessageHeaderAccessor headerAccessor) {
        try {
            // 获取当前用户ID
            Long currentUserId = getUserIdFromSession(headerAccessor);
            if (currentUserId == null) {
                log.warn("无法识别请求用户，会话ID: {}", headerAccessor.getSessionId());
                return;
            }

            log.info("用户 {} 订阅联系人 {} 的在线状态", currentUserId, contactId);

            // 检查联系人是否在线
            boolean isOnline = redisService.isUserOnline(Long.valueOf(contactId));

            // 创建状态消息
            Map<String, Object> statusData = new HashMap<>();
            statusData.put("contactId", contactId);
            statusData.put("online", isOnline);
            statusData.put("timestamp", System.currentTimeMillis());

            // 如果联系人在线，获取最后活跃时间
            if (isOnline) {
                long lastActive = redisService.getLastActiveTime(Long.valueOf(contactId));
                statusData.put("lastActive", lastActive);
            }

            // 转换为JSON
            String json = new ObjectMapper().writeValueAsString(statusData);

            // 发送给请求的用户 - 使用标准路径 /queue/contactStatus
            if (headerAccessor.getUser() != null) {
                // 使用用户名发送消息
                String username = headerAccessor.getUser().getName();
                messagingTemplate.convertAndSendToUser(
                    username,
                    "/queue/contactStatus/" + contactId,
                    json
                );
                log.info("使用用户名发送联系人状态: 用户名={}", username);
            } else if (currentUserId != null) {
                // 如果无法从Principal获取用户名，尝试通过用户ID获取用户名
                String username = getUsernameById(currentUserId);
                if (username != null) {
                    messagingTemplate.convertAndSendToUser(
                        username,
                        "/queue/contactStatus/" + contactId,
                        json
                    );
                    log.info("使用查询的用户名发送联系人状态: 用户名={}, 用户ID={}", username, currentUserId);
                } else {
                    log.warn("无法获取用户名，使用ID发送联系人状态: {}", currentUserId);
                    messagingTemplate.convertAndSendToUser(
                        currentUserId.toString(),
                        "/queue/contactStatus/" + contactId,
                        json
                    );
                }
            } else {
                log.warn("无法获取用户信息，只进行广播发送");
                // 如果无法获取用户信息，只进行广播发送
            }

            // 同时发送到特定联系人的主题
            messagingTemplate.convertAndSend("/topic/onlineStatus/" + contactId, json);

            log.info("已发送联系人 {} 的在线状态给用户 {}", contactId, currentUserId);
        } catch (Exception e) {
            log.error("订阅联系人在线状态时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 批量获取联系人在线状态
     * 前端通过 /app/getContactsStatus 请求获取多个联系人的在线状态
     */
    @MessageMapping("/getContactsStatus")
    public void getContactsStatus(ContactStatusRequest request, SimpMessageHeaderAccessor headerAccessor) {
        try {
            // 获取当前用户ID
            Long currentUserId = getUserIdFromSession(headerAccessor);
            if (currentUserId == null) {
                log.warn("无法识别请求用户，会话ID: {}", headerAccessor.getSessionId());
                return;
            }

            List<String> contactIds = request.getContactIds();
            log.info("用户 {} 请求获取 {} 个联系人的在线状态", currentUserId, contactIds.size());

            // 创建响应数据
            Map<String, Object> responseData = new HashMap<>();
            Map<String, Object> contactsStatus = new HashMap<>();

            // 获取每个联系人的在线状态
            for (String contactId : contactIds) {
                try {
                    boolean isOnline = redisService.isUserOnline(Long.valueOf(contactId));
                    Map<String, Object> statusData = new HashMap<>();
                    statusData.put("online", isOnline);

                    // 如果联系人在线，获取最后活跃时间
                    if (isOnline) {
                        long lastActive = redisService.getLastActiveTime(Long.valueOf(contactId));
                        statusData.put("lastActive", lastActive);
                    }

                    contactsStatus.put(contactId, statusData);
                } catch (NumberFormatException e) {
                    log.warn("无效的联系人ID: {}", contactId);
                }
            }

            responseData.put("contactsStatus", contactsStatus);
            responseData.put("timestamp", System.currentTimeMillis());

            // 转换为JSON
            String json = new ObjectMapper().writeValueAsString(responseData);

            // 发送给请求的用户 - 使用标准路径 /queue/contactsStatus
            if (headerAccessor.getUser() != null) {
                // 使用用户名发送消息
                String username = headerAccessor.getUser().getName();
                messagingTemplate.convertAndSendToUser(
                    username,
                    "/queue/contactsStatus",
                    json
                );
                log.info("使用用户名发送联系人状态列表: 用户名={}", username);
            } else if (currentUserId != null) {
                // 如果无法从Principal获取用户名，尝试通过用户ID获取用户名
                String username = getUsernameById(currentUserId);
                if (username != null) {
                    messagingTemplate.convertAndSendToUser(
                        username,
                        "/queue/contactsStatus",
                        json
                    );
                    log.info("使用查询的用户名发送联系人状态列表: 用户名={}, 用户ID={}", username, currentUserId);
                } else {
                    log.warn("无法获取用户名，使用ID发送联系人状态列表: {}", currentUserId);
                    messagingTemplate.convertAndSendToUser(
                        currentUserId.toString(),
                        "/queue/contactsStatus",
                        json
                    );
                }
            } else {
                log.warn("无法获取用户信息，无法发送私人消息");
                // 如果无法获取用户信息，无法发送私人消息
            }

            log.info("已发送 {} 个联系人的在线状态给用户 {}", contactIds.size(), currentUserId);
        } catch (Exception e) {
            log.error("获取联系人在线状态时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 从会话属性中获取用户ID
     */
    private Long getUserIdFromSession(SimpMessageHeaderAccessor headerAccessor) {
        // 首先尝试从会话属性中获取用户ID
        if (headerAccessor.getSessionAttributes() != null) {
            Object userIdObj = headerAccessor.getSessionAttributes().get("userId");
            if (userIdObj instanceof Long) {
                return (Long) userIdObj;
            } else if (userIdObj instanceof String) {
                try {
                    return Long.valueOf((String) userIdObj);
                } catch (NumberFormatException e) {
                    log.warn("无法将会话中的userId转换为Long: {}", userIdObj);
                }
            }
        }

        // 如果会话属性中没有用户ID，尝试从Principal中获取
        if (headerAccessor.getUser() != null) {
            String username = headerAccessor.getUser().getName();
            log.info("从Principal中获取到用户名: {}", username);

            // 如果需要，可以通过用户名查询用户ID
            // 这里假设用户名就是用户ID的字符串表示
            try {
                return Long.valueOf(username);
            } catch (NumberFormatException e) {
                log.warn("无法将Principal中的用户名转换为用户ID: {}", username);
            }
        }

        // 如果都没有获取到，返回null
        return null;
    }

    /**
     * 根据用户ID获取用户名
     * @param userId 用户ID
     * @return 用户名，如果未找到则返回null
     */
    private String getUsernameById(Long userId) {
        try {
            if (userId == null) {
                log.warn("尝试获取用户名时收到空的用户ID");
                return null;
            }

            // 从数据库中查询用户
            com.tomato.lostfoundsystem.entity.User user = userMapper.findById(userId);
            if (user != null) {
                return user.getUsername();
            } else {
                log.warn("未找到用户ID对应的用户: {}", userId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取用户名时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从User-Agent获取设备信息
     */
    private String getDeviceInfo(String userAgent) {
        if (userAgent == null) {
            return "unknown";
        }

        String deviceInfo = "unknown";
        if (userAgent.contains("Mobile") || userAgent.contains("Android")) {
            deviceInfo = "mobile";
        } else if (userAgent.contains("Windows") || userAgent.contains("Macintosh") || userAgent.contains("Linux")) {
            deviceInfo = "desktop";
        } else if (userAgent.contains("iPad") || userAgent.contains("Tablet")) {
            deviceInfo = "tablet";
        }

        return deviceInfo;
    }

    /**
     * 广播在线用户数量
     */
    private void broadcastOnlineUserCount() {
        try {
            // 获取所有用户ID
            Set<Long> userIds = redisService.getAllUserIds();
            log.info("广播在线用户数量 - 获取到 {} 个用户ID", userIds.size());

            // 计算在线用户数量
            int onlineCount = 0;
            java.util.List<Long> onlineUserIds = new java.util.ArrayList<>();
            for (Long userId : userIds) {
                boolean isOnline = redisService.isUserOnline(userId);
                if (isOnline) {
                    onlineCount++;
                    onlineUserIds.add(userId);
                }
            }

            log.info("广播在线用户数量 - 在线用户: {}, 数量: {}", onlineUserIds, onlineCount);

            // 创建包含在线用户数量的JSON对象
            Map<String, Object> countData = new HashMap<>();
            countData.put("onlineCount", onlineCount);
            countData.put("timestamp", System.currentTimeMillis());
            countData.put("onlineUserIds", onlineUserIds); // 添加在线用户ID列表

            // 转换为JSON字符串
            String countJson = new ObjectMapper().writeValueAsString(countData);

            // 广播在线用户数量
            messagingTemplate.convertAndSend("/topic/onlineCount", countJson);
            log.info("已广播在线用户数量: {}", onlineCount);

            // 同时广播在线用户列表
            broadcastOnlineUsersList();
        } catch (Exception e) {
            log.error("广播在线用户数量时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 广播在线用户列表
     */
    private void broadcastOnlineUsersList() {
        try {
            // 获取所有用户ID
            Set<Long> userIds = redisService.getAllUserIds();
            log.info("广播在线用户列表 - 获取到 {} 个用户ID", userIds.size());

            // 检查每个用户是否在线
            Map<String, Object> onlineUsers = new HashMap<>();
            java.util.List<Long> onlineUserIds = new java.util.ArrayList<>();

            for (Long userId : userIds) {
                boolean isOnline = redisService.isUserOnline(userId);
                if (isOnline) {
                    onlineUsers.put(userId.toString(), true);
                    onlineUserIds.add(userId);
                }
            }

            log.info("广播在线用户列表 - 在线用户: {}, 数量: {}", onlineUserIds, onlineUserIds.size());

            // 创建包含在线用户列表的JSON对象
            Map<String, Object> usersData = new HashMap<>();
            usersData.put("onlineUsers", onlineUsers);
            usersData.put("timestamp", System.currentTimeMillis());
            usersData.put("onlineCount", onlineUserIds.size());

            // 转换为JSON字符串
            String usersJson = new ObjectMapper().writeValueAsString(usersData);

            // 广播在线用户列表
            messagingTemplate.convertAndSend("/topic/onlineUsers", usersJson);
            log.info("已广播在线用户列表: {} 个用户", onlineUserIds.size());
        } catch (Exception e) {
            log.error("广播在线用户列表时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理广播在线用户列表请求
     */
    @MessageMapping("/broadcastOnlineUsers")
    public void broadcastOnlineUsers(SimpMessageHeaderAccessor headerAccessor) {
        log.info("收到广播在线用户列表请求");

        // 获取用户信息
        String username = "unknown";
        if (headerAccessor.getUser() != null) {
            username = headerAccessor.getUser().getName();
        }

        log.info("用户 {} 请求广播在线用户列表", username);

        // 广播在线用户列表
        broadcastOnlineUsersList();
    }
}
