<template>
  <div class="contact-status" :class="{ 'online': isOnline, 'offline': !isOnline }">
    <div class="status-indicator"></div>
    <span class="status-text">{{ statusText }}</span>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { subscribeContactStatus, unsubscribeContactStatus, isContactOnline, getContactLastActiveTime } from '../utils/contact-status';

const props = defineProps({
  contactId: {
    type: [String, Number],
    required: true
  },
  showText: {
    type: Boolean,
    default: true
  },
  showLastActive: {
    type: Boolean,
    default: false
  }
});

const isOnline = ref(false);
const lastActive = ref(null);
const statusText = ref('离线');

// 更新状态显示
function updateStatusDisplay() {
  isOnline.value = isContactOnline(props.contactId.toString());
  lastActive.value = getContactLastActiveTime(props.contactId.toString());
  
  if (isOnline.value) {
    statusText.value = '在线';
  } else if (lastActive.value && props.showLastActive) {
    // 计算最后活跃时间
    const now = new Date().getTime();
    const diff = now - lastActive.value;
    
    if (diff < 60000) { // 小于1分钟
      statusText.value = '刚刚在线';
    } else if (diff < 3600000) { // 小于1小时
      const minutes = Math.floor(diff / 60000);
      statusText.value = `${minutes}分钟前在线`;
    } else if (diff < 86400000) { // 小于1天
      const hours = Math.floor(diff / 3600000);
      statusText.value = `${hours}小时前在线`;
    } else {
      const days = Math.floor(diff / 86400000);
      statusText.value = `${days}天前在线`;
    }
  } else {
    statusText.value = '离线';
  }
}

// 处理联系人状态更新事件
function handleContactStatusUpdate(event) {
  const { contactId, status } = event.detail;
  
  if (contactId === props.contactId.toString()) {
    console.log(`联系人 ${contactId} 状态更新:`, status);
    updateStatusDisplay();
  }
}

// 监听contactId变化
watch(() => props.contactId, (newId, oldId) => {
  if (oldId) {
    unsubscribeContactStatus(oldId.toString());
  }
  if (newId) {
    subscribeContactStatus(newId.toString());
    updateStatusDisplay();
  }
});

onMounted(() => {
  // 订阅联系人状态
  subscribeContactStatus(props.contactId.toString());
  
  // 初始化状态显示
  updateStatusDisplay();
  
  // 监听状态更新事件
  window.addEventListener('contact-status-updated', handleContactStatusUpdate);
});

onUnmounted(() => {
  // 取消订阅
  unsubscribeContactStatus(props.contactId.toString());
  
  // 移除事件监听
  window.removeEventListener('contact-status-updated', handleContactStatusUpdate);
});
</script>

<style scoped>
.contact-status {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.online .status-indicator {
  background-color: #67c23a;
  box-shadow: 0 0 4px rgba(103, 194, 58, 0.6);
}

.offline .status-indicator {
  background-color: #909399;
}

.status-text {
  color: #606266;
}

.online .status-text {
  color: #67c23a;
}
</style>
