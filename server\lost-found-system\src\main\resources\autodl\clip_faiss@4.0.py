import base64
import io
import logging
import os
import pickle
from datetime import datetime
from typing import Dict, List, Optional, Union, Any
import faiss
import numpy as np
import torch
from fastapi import FastAPI, File, Form, HTTPException, UploadFile, Query, Request
from fastapi.middleware.cors import CORSMiddleware
from PIL import Image
from pydantic import BaseModel
import clip  # 导入CLIP模型

# 配置日志
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("clip_faiss_api")

# 创建FastAPI应用
app = FastAPI(
    title="CLIP+FAISS API",
    description="用于校园失物招领系统的CLIP+FAISS API",
    version="2.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 常量定义
CLIP_MODEL_NAME = "ViT-B/32"  # CLIP模型名称
FEATURE_DIM = 512  # CLIP特征向量维度
ITEM_TYPES = ["LOST", "FOUND"]  # 物品类型
INDEX_TYPES = ["TEXT", "IMAGE"]  # 索引类型

# 归一化策略说明
# 本系统只在CLIP模型提取特征时进行一次归一化处理
# 不在添加向量到索引或搜索时重复归一化，避免不必要的计算开销
# CLIP模型提取的特征向量已经经过L2归一化，范数为1

# 设备配置
device = "cuda" if torch.cuda.is_available() else "cpu"

# 索引和映射字典
indexes = {}
mappings = {}

# 索引文件目录
INDEX_DIR = os.environ.get("INDEX_DIR", "/root/autodl-tmp/data/indices")
os.makedirs(INDEX_DIR, exist_ok=True)

# 加载CLIP模型
logger.info(f"正在加载CLIP模型 {CLIP_MODEL_NAME} 到 {device} 设备...")
model, preprocess = clip.load(CLIP_MODEL_NAME, device=device)
logger.info(f"CLIP模型加载完成")

# 请求模型定义
class TextFeatureRequest(BaseModel):
    text: str

class AddVectorRequest(BaseModel):
    item_id: int
    item_type: str
    index_type: str
    features: str  # Base64编码的特征向量

class SearchRequest(BaseModel):
    features: str  # Base64编码的特征向量
    item_type: str
    index_type: str
    limit: int = 20

class MixedSearchRequest(BaseModel):
    text_features: Optional[str] = None  # Base64编码的文本特征向量
    image_features: Optional[str] = None  # Base64编码的图像特征向量
    target_type: str  # 目标物品类型
    limit: int = 20

# 辅助函数
def get_index_filename(item_type, index_type):
    """获取索引文件名"""
    return os.path.join(INDEX_DIR, f"{item_type.lower()}_{index_type.lower()}_hnsw_index.bin")

def get_mapping_filename(item_type, index_type):
    """获取映射文件名"""
    return os.path.join(INDEX_DIR, f"{item_type.lower()}_{index_type.lower()}_mapping.pkl")

def create_hnsw_index(dimension=FEATURE_DIM):
    """创建HNSW索引"""
    # 使用HNSW索引，内积度量（对于归一化向量等同于余弦相似度）
    index = faiss.IndexHNSWFlat(dimension, 32, faiss.METRIC_INNER_PRODUCT)
    # 设置构建参数
    index.hnsw.efConstruction = 100  # 构建时的探索因子
    index.hnsw.efSearch = 64  # 搜索时的探索因子
    return index

def load_indices():
    """加载所有索引"""
    for item_type in ITEM_TYPES:
        for index_type in INDEX_TYPES:
            index_name = f"{item_type}_{index_type}"
            index_file = get_index_filename(item_type, index_type)
            mapping_file = get_mapping_filename(item_type, index_type)

            # 创建默认索引
            indexes[index_name] = create_hnsw_index()
            mappings[index_name] = {}

            # 尝试加载现有索引
            if os.path.exists(index_file) and os.path.exists(mapping_file):
                try:
                    # 加载索引
                    indexes[index_name] = faiss.read_index(index_file)

                    # 加载映射
                    with open(mapping_file, 'rb') as f:
                        mappings[index_name] = pickle.load(f)

                    logger.info(f"已加载索引: {index_name}, 包含 {indexes[index_name].ntotal} 个向量")
                except Exception as e:
                    logger.error(f"加载索引 {index_name} 失败: {e}")
                    # 创建新索引
                    indexes[index_name] = create_hnsw_index()
                    mappings[index_name] = {}
            else:
                logger.info(f"索引文件不存在，创建新索引: {index_name}")

def save_all_indices():
    """保存所有索引"""
    for item_type in ITEM_TYPES:
        for index_type in INDEX_TYPES:
            save_index(item_type, index_type)

def save_index(item_type, index_type):
    """保存索引和映射"""
    index_name = f"{item_type}_{index_type}"
    index_file = get_index_filename(item_type, index_type)
    mapping_file = get_mapping_filename(item_type, index_type)

    try:
        # 保存索引
        faiss.write_index(indexes[index_name], index_file)

        # 保存映射
        with open(mapping_file, 'wb') as f:
            pickle.dump(mappings[index_name], f)

        logger.info(f"已保存索引: {index_name}")
        return True
    except Exception as e:
        logger.error(f"保存索引 {index_name} 失败: {e}")
        return False

def extract_text_features(text):
    """从文本中提取特征向量"""
    try:
        # 预处理：确保文本不为空
        if text is None or text.strip() == "":
            logger.warning("输入文本为空，使用默认文本")
            text = "空文本"

        # 预处理：限制文本长度（保守估计）
        MAX_CHARS = 40  # 非常保守的字符限制
        if len(text) > MAX_CHARS:
            original_text = text
            text = text[:MAX_CHARS]
            logger.warning(f"预处理阶段裁剪文本: 原长度={len(original_text)}字符，裁剪后={len(text)}字符")

        logger.info(f"提取文本特征，处理后的文本: '{text}' (长度: {len(text)}字符)")

        try:
            # 检查文本的token长度
            tokens = clip.tokenize([text])
            token_count = (tokens[0] != 0).sum().item()  # 计算非零token的数量
            logger.info(f"文本token数量: {token_count}")

            # CLIP模型的token限制为77（包括开始和结束token）
            MAX_TOKENS = 77

            # 如果token数量超过限制，需要裁剪文本
            if token_count >= MAX_TOKENS:
                logger.warning(f"文本token数量({token_count})超过CLIP模型限制(77)，将进行裁剪")

                # 二分查找合适的文本长度
                left, right = 1, len(text)
                truncated_text = text[:20]  # 默认非常保守的裁剪

                while left <= right:
                    mid = (left + right) // 2
                    test_text = text[:mid]
                    try:
                        test_tokens = clip.tokenize([test_text])
                        test_token_count = (test_tokens[0] != 0).sum().item()

                        if test_token_count < MAX_TOKENS - 1:  # 留一个位置给结束token
                            left = mid + 1
                            truncated_text = test_text
                        else:
                            right = mid - 1
                    except Exception as e:
                        logger.error(f"二分查找过程中检查token数量时发生错误: {e}")
                        right = mid - 1  # 出错时减小搜索范围

                logger.info(f"文本已裁剪: 原长度={len(text)}字符，裁剪后={len(truncated_text)}字符")
                text = truncated_text
        except Exception as e:
            logger.error(f"检查token数量时发生错误: {e}")
            # 如果token检查失败，使用简单的字符长度限制
            if len(text) > 20:  # 设置一个非常保守的字符长度限制
                original_text = text
                text = text[:20]
                logger.warning(f"Token检查失败，使用简单字符长度限制裁剪文本: 原长度={len(original_text)}字符，裁剪后={len(text)}字符")

        # 使用CLIP模型提取文本特征
        try:
            with torch.no_grad():
                logger.info(f"开始提取特征，最终文本: '{text}' (长度: {len(text)}字符)")
                text_tokens = clip.tokenize([text]).to(device)
                text_features = model.encode_text(text_tokens)

            # 归一化特征向量
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)

            # 转换为numpy数组
            result = text_features.cpu().numpy().astype(np.float32)
            logger.info(f"成功提取文本特征，特征向量形状: {result.shape}")
            return result
        except Exception as e:
            logger.error(f"提取文本特征时发生错误: {e}")
            raise
    except Exception as e:
        logger.error(f"提取文本特征过程中发生未处理的错误: {e}")
        raise

def extract_image_features(image):
    """从图像中提取特征向量"""
    try:
        # 预处理图像
        image_input = preprocess(image).unsqueeze(0).to(device)

        # 使用CLIP模型提取图像特征
        with torch.no_grad():
            image_features = model.encode_image(image_input)

        # 归一化特征向量
        image_features = image_features / image_features.norm(dim=-1, keepdim=True)

        # 转换为numpy数组
        return image_features.cpu().numpy().astype(np.float32)
    except Exception as e:
        logger.error(f"提取图像特征时发生错误: {e}")
        raise

def add_vector_to_index(item_id, item_type, features, index_type):
    """添加向量到索引"""
    index_name = f"{item_type}_{index_type}"

    # 确保向量是正确的形状和类型
    features = np.array(features).reshape(1, -1).astype(np.float32)
    # 注意：不再进行归一化，因为CLIP模型提取特征时已经归一化
    # 添加到索引
    vector_id = indexes[index_name].ntotal
    indexes[index_name].add(features)
    # 更新映射 - 简化为直接映射item_id到vector_id
    mappings[index_name][str(item_id)] = vector_id
    return vector_id

def search_similar_vectors(features, item_type, index_type, limit=20):
    """搜索相似向量"""
    index_name = f"{item_type}_{index_type}"

    logger.info(f"【搜索详情】开始 {index_name} 索引搜索")

    # 确保向量是正确的形状和类型
    features = np.array(features).reshape(1, -1).astype(np.float32)

    # 记录向量信息
    logger.info(f"【搜索详情】特征向量形状: {features.shape}, 类型: {features.dtype}")
    logger.info(f"【搜索详情】特征向量范数: {np.linalg.norm(features)}")

    # 注意：不再进行归一化，因为CLIP模型提取特征时已经归一化
    # 只记录日志，确认向量范数接近1
    if abs(np.linalg.norm(features) - 1.0) > 0.01:
        logger.warning(f"【搜索详情】警告：特征向量范数 ({np.linalg.norm(features)}) 与预期的1.0相差较大")

    # 搜索相似向量
    if indexes[index_name].ntotal == 0:
        logger.warning(f"【搜索详情】{index_name} 索引为空，无法搜索")
        return []

    # 记录索引信息
    logger.info(f"【搜索详情】{index_name} 索引包含 {indexes[index_name].ntotal} 个向量")
    logger.info(f"【搜索详情】{index_name} 映射包含 {len(mappings[index_name])} 个物品ID")

    limit = min(limit, indexes[index_name].ntotal)
    logger.info(f"【搜索详情】实际搜索限制: {limit}")

    distances, indices = indexes[index_name].search(features, limit)

    # 记录原始搜索结果
    logger.info(f"【搜索详情】原始搜索结果: 找到 {len(indices[0])} 个结果")
    if len(indices[0]) > 0:
        logger.info(f"【搜索详情】距离范围: 最小={np.min(distances[0])}, 最大={np.max(distances[0])}")

    # 构建结果
    results = []
    for i in range(len(indices[0])):
        idx = int(indices[0][i])
        raw_distance = float(distances[0][i])
        similarity = float((raw_distance + 1) / 2)  # 将[-1,1]范围转换为[0,1]

        # 查找对应的物品ID
        item_id = None
        for id_str, vector_id in mappings[index_name].items():
            if vector_id == idx:
                item_id = int(id_str)
                break

        if item_id is not None:
            results.append({
                "item_id": item_id,
                "similarity": similarity
            })

            # 记录每个结果的详细信息
            logger.info(f"【搜索详情】{index_name} 结果 #{i+1}: item_id={item_id}, 原始距离={raw_distance}, 相似度={similarity}")
        else:
            logger.warning(f"【搜索详情】找不到向量ID {idx} 对应的物品ID")

    logger.info(f"【搜索详情】{index_name} 搜索完成，返回 {len(results)} 个结果")

    return results

def search_mixed_vectors(text_vector, image_vector, target_type, limit=20):
    """
    混合搜索，同时使用文本和图像特征，按权重计算综合相似度
    """
    logger.info(f"【混合搜索】开始混合搜索: target_type={target_type}, limit={limit}")
    logger.info(f"【混合搜索】输入向量: text_vector={'有' if text_vector is not None else '无'}, image_vector={'有' if image_vector is not None else '无'}")

    # 权重配置 - 使用原始权重设置
    TEXT_TO_TEXT_WEIGHT = 1.0    # 文本到文本搜索权重
    IMAGE_TO_IMAGE_WEIGHT = 1.0  # 图像到图像搜索权重
    IMAGE_TO_TEXT_WEIGHT = 0.5   # 图像到文本搜索权重
    TEXT_TO_IMAGE_WEIGHT = 0.5   # 文本到图像搜索权重

    logger.info(f"【混合搜索】权重配置: TEXT_TO_TEXT={TEXT_TO_TEXT_WEIGHT}, IMAGE_TO_IMAGE={IMAGE_TO_IMAGE_WEIGHT}, IMAGE_TO_TEXT={IMAGE_TO_TEXT_WEIGHT}, TEXT_TO_IMAGE={TEXT_TO_IMAGE_WEIGHT}")

    # 初始化结果容器
    all_items = {}  # 存储所有物品的原始相似度
    match_details = {}  # 存储匹配详情

    # 初始化结果变量，避免未定义错误
    text_to_text_results = []
    image_to_image_results = []
    text_to_image_results = []
    image_to_text_results = []

    # 文本到文本搜索
    if text_vector is not None:
        logger.info(f"【混合搜索】执行文本到文本搜索")
        text_to_text_results = search_similar_vectors(text_vector, target_type, "TEXT", limit)
        logger.info(f"【混合搜索】文本到文本搜索结果: {len(text_to_text_results)} 个")

        for result in text_to_text_results:
            item_id = result["item_id"]

            # 初始化物品数据
            if item_id not in all_items:
                all_items[item_id] = {
                    "TEXT_TO_TEXT": 0.0,
                    "TEXT_TO_IMAGE": 0.0,
                    "IMAGE_TO_TEXT": 0.0,
                    "IMAGE_TO_IMAGE": 0.0
                }

            # 初始化match_details
            if item_id not in match_details:
                match_details[item_id] = {
                    "item_id": item_id,
                    "match_details": {
                        "TEXT_TO_TEXT": 0.0,
                        "TEXT_TO_IMAGE": 0.0,
                        "IMAGE_TO_TEXT": 0.0,
                        "IMAGE_TO_IMAGE": 0.0
                    }
                }

            # 保存原始相似度
            original_similarity = result["similarity"]

            # 对text-to-text匹配分数进行条件平方压缩
            if original_similarity < 0.75:
                compressed_similarity = original_similarity  # 低分不动
            else:
                compressed_similarity = min(original_similarity * original_similarity, 0.85)  # 平方压缩 + clip 上限

            # 保存压缩后的相似度
            all_items[item_id]["TEXT_TO_TEXT"] = compressed_similarity
            match_details[item_id]["match_details"]["TEXT_TO_TEXT"] = compressed_similarity

            logger.info(f"【混合搜索】文本到文本: item_id={item_id}, 原始相似度={original_similarity}, 压缩后相似度={compressed_similarity}")

    # 图像到图像搜索
    if image_vector is not None:
        logger.info(f"【混合搜索】执行图像到图像搜索")
        image_to_image_results = search_similar_vectors(image_vector, target_type, "IMAGE", limit)
        logger.info(f"【混合搜索】图像到图像搜索结果: {len(image_to_image_results)} 个")

        for result in image_to_image_results:
            item_id = result["item_id"]

            # 初始化物品数据
            if item_id not in all_items:
                all_items[item_id] = {
                    "TEXT_TO_TEXT": 0.0,
                    "TEXT_TO_IMAGE": 0.0,
                    "IMAGE_TO_TEXT": 0.0,
                    "IMAGE_TO_IMAGE": 0.0
                }

            # 初始化match_details
            if item_id not in match_details:
                match_details[item_id] = {
                    "item_id": item_id,
                    "match_details": {
                        "TEXT_TO_TEXT": 0.0,
                        "TEXT_TO_IMAGE": 0.0,
                        "IMAGE_TO_TEXT": 0.0,
                        "IMAGE_TO_IMAGE": 0.0
                    }
                }

            # 保存原始相似度
            all_items[item_id]["IMAGE_TO_IMAGE"] = result["similarity"]
            match_details[item_id]["match_details"]["IMAGE_TO_IMAGE"] = result["similarity"]
            logger.info(f"【混合搜索】图像到图像: item_id={item_id}, similarity={result['similarity']}")

    # 文本到图像搜索（跨模态）
    if text_vector is not None:
        logger.info(f"【混合搜索】执行文本到图像搜索")
        text_to_image_results = search_similar_vectors(text_vector, target_type, "IMAGE", limit)
        logger.info(f"【混合搜索】文本到图像搜索结果: {len(text_to_image_results)} 个")

        for result in text_to_image_results:
            item_id = result["item_id"]

            # 初始化物品数据
            if item_id not in all_items:
                all_items[item_id] = {
                    "TEXT_TO_TEXT": 0.0,
                    "TEXT_TO_IMAGE": 0.0,
                    "IMAGE_TO_TEXT": 0.0,
                    "IMAGE_TO_IMAGE": 0.0
                }

            # 初始化match_details
            if item_id not in match_details:
                match_details[item_id] = {
                    "item_id": item_id,
                    "match_details": {
                        "TEXT_TO_TEXT": 0.0,
                        "TEXT_TO_IMAGE": 0.0,
                        "IMAGE_TO_TEXT": 0.0,
                        "IMAGE_TO_IMAGE": 0.0
                    }
                }

            # 保存原始相似度
            all_items[item_id]["TEXT_TO_IMAGE"] = result["similarity"]
            match_details[item_id]["match_details"]["TEXT_TO_IMAGE"] = result["similarity"]
            logger.info(f"【混合搜索】文本到图像: item_id={item_id}, similarity={result['similarity']}")

    # 图像到文本搜索（跨模态）
    if image_vector is not None:
        logger.info(f"【混合搜索】执行图像到文本搜索")
        image_to_text_results = search_similar_vectors(image_vector, target_type, "TEXT", limit)
        logger.info(f"【混合搜索】图像到文本搜索结果: {len(image_to_text_results)} 个")

        # 详细记录图像到文本的搜索结果
        if len(image_to_text_results) > 0:
            for i, result in enumerate(image_to_text_results):
                logger.info(f"【混合搜索】图像到文本结果 #{i+1}: item_id={result['item_id']}, similarity={result['similarity']}")
        else:
            logger.warning(f"【混合搜索】图像到文本搜索没有找到结果")

        for result in image_to_text_results:
            item_id = result["item_id"]

            # 初始化物品数据
            if item_id not in all_items:
                all_items[item_id] = {
                    "TEXT_TO_TEXT": 0.0,
                    "TEXT_TO_IMAGE": 0.0,
                    "IMAGE_TO_TEXT": 0.0,
                    "IMAGE_TO_IMAGE": 0.0
                }

            # 初始化match_details
            if item_id not in match_details:
                match_details[item_id] = {
                    "item_id": item_id,
                    "match_details": {
                        "TEXT_TO_TEXT": 0.0,
                        "TEXT_TO_IMAGE": 0.0,
                        "IMAGE_TO_TEXT": 0.0,
                        "IMAGE_TO_IMAGE": 0.0
                    }
                }

            # 保存原始相似度
            all_items[item_id]["IMAGE_TO_TEXT"] = result["similarity"]
            match_details[item_id]["match_details"]["IMAGE_TO_TEXT"] = result["similarity"]
            logger.info(f"【混合搜索】图像到文本: item_id={item_id}, similarity={result['similarity']}")

    # 添加混合搜索统计日志
    logger.info(f"混合搜索统计: 模式 TEXT→TEXT 命中 {len(text_to_text_results) if text_vector is not None else 0}")
    logger.info(f"混合搜索统计: 模式 IMAGE→IMAGE 命中 {len(image_to_image_results) if image_vector is not None else 0}")
    logger.info(f"混合搜索统计: 模式 TEXT→IMAGE 命中 {len(text_to_image_results) if text_vector is not None else 0}")
    logger.info(f"混合搜索统计: 模式 IMAGE→TEXT 命中 {len(image_to_text_results) if image_vector is not None else 0}")

    # 计算综合相似度并创建结果列表
    combined_results = []
    logger.info(f"【混合搜索】开始计算综合相似度，共有 {len(all_items)} 个物品")

    for item_id, similarities in all_items.items():
        # 计算加权综合相似度
        # 注意：TEXT_TO_TEXT已经在前面进行了压缩处理，这里直接使用
        weighted_sum = (
            similarities["TEXT_TO_TEXT"] * TEXT_TO_TEXT_WEIGHT +  # 已经是压缩后的分数
            similarities["IMAGE_TO_IMAGE"] * IMAGE_TO_IMAGE_WEIGHT +
            similarities["TEXT_TO_IMAGE"] * TEXT_TO_IMAGE_WEIGHT +
            similarities["IMAGE_TO_TEXT"] * IMAGE_TO_TEXT_WEIGHT
        )

        # 计算有效模式数量（非零相似度的模式）
        active_modes = sum(1 for sim in similarities.values() if sim > 0)

        # 记录日志
        logger.info(f"【混合搜索】物品 {item_id} 的相似度: TEXT_TO_TEXT={similarities['TEXT_TO_TEXT']}(已压缩), IMAGE_TO_IMAGE={similarities['IMAGE_TO_IMAGE']}, TEXT_TO_IMAGE={similarities['TEXT_TO_IMAGE']}, IMAGE_TO_TEXT={similarities['IMAGE_TO_TEXT']}, 加权和={weighted_sum}, 有效模式={active_modes}")

        # 计算最终综合相似度（使用加权平均方式）
        if active_modes > 0:
            # 确定主要匹配类型（相似度最高的模式）
            weighted_similarities = {
                "TEXT_TO_TEXT": similarities["TEXT_TO_TEXT"] * TEXT_TO_TEXT_WEIGHT,  # 已经是压缩后的分数
                "IMAGE_TO_IMAGE": similarities["IMAGE_TO_IMAGE"] * IMAGE_TO_IMAGE_WEIGHT,
                "TEXT_TO_IMAGE": similarities["TEXT_TO_IMAGE"] * TEXT_TO_IMAGE_WEIGHT,
                "IMAGE_TO_TEXT": similarities["IMAGE_TO_TEXT"] * IMAGE_TO_TEXT_WEIGHT
            }

            best_match_type = max(weighted_similarities.items(), key=lambda x: x[1])[0]

            # 改为使用加权平均方式计算综合相似度
            # 加权平均 = 加权总和 / 权重总和
            # 计算有效模式的权重总和
            active_weights_sum = 0
            if similarities["TEXT_TO_TEXT"] > 0:
                active_weights_sum += TEXT_TO_TEXT_WEIGHT
            if similarities["IMAGE_TO_IMAGE"] > 0:
                active_weights_sum += IMAGE_TO_IMAGE_WEIGHT
            if similarities["TEXT_TO_IMAGE"] > 0:
                active_weights_sum += TEXT_TO_IMAGE_WEIGHT
            if similarities["IMAGE_TO_TEXT"] > 0:
                active_weights_sum += IMAGE_TO_TEXT_WEIGHT

            # 计算加权平均（如果没有有效模式，则相似度为0）
            if active_weights_sum > 0:
                final_similarity = weighted_sum / active_weights_sum
            else:
                final_similarity = 0.0

            # 确保相似度在0-1范围内
            final_similarity = min(final_similarity, 1.0)  # text-to-text已在压缩函数中设置了0.85上限

            # 记录加权平均的计算过程
            logger.info(f"【混合搜索】物品 {item_id} 的相似度计算: 加权总和={weighted_sum}, 有效权重总和={active_weights_sum}, 加权平均={final_similarity}, 最佳匹配类型: {best_match_type}")
            logger.info(f"【混合搜索】物品 {item_id} 的最终相似度: {final_similarity}, 最佳匹配类型: {best_match_type}")


            combined_results.append(result_item)

    # 按相似度排序
    combined_results.sort(key=lambda x: x["similarity"], reverse=True)

    # 记录排序后的结果
    logger.info(f"【混合搜索】排序后的结果: {len(combined_results)} 个")
    for i, result in enumerate(combined_results[:min(5, len(combined_results))]):
        logger.info(f"【混合搜索】Top {i+1}: item_id={result['item_id']}, similarity={result['similarity']}, match_type={result['match_type']}")

    # 限制结果数量
    limited_results = combined_results[:limit]
    logger.info(f"【混合搜索】限制后的结果: {len(limited_results)} 个")

    return limited_results

# API端点实现
@app.post("/extract_text_features")
async def api_extract_text_features(request: TextFeatureRequest):
    """从文本中提取特征向量"""
    try:
        # 记录原始文本
        original_text = request.text
        original_length = len(original_text)
        logger.info(f"【API】提取文本特征，原始文本长度: {original_length} 字符")
        logger.info(f"【API】原始文本: '{original_text}'")

        # 预处理：确保文本不为空
        if request.text is None or request.text.strip() == "":
            logger.warning("【API】输入文本为空，使用默认文本")
            request.text = "空文本"

        # 预处理：限制文本长度（非常保守的估计）
        MAX_CHARS = 40  # 非常保守的字符限制
        if len(request.text) > MAX_CHARS:
            request.text = request.text[:MAX_CHARS]
            logger.warning(f"【API】预处理阶段裁剪文本: 原长度={original_length}字符，裁剪后={len(request.text)}字符")

        logger.info(f"【API】预处理后的文本: '{request.text}' (长度: {len(request.text)}字符)")

        # 检查预处理后文本的token数量
        try:
            tokens = clip.tokenize([request.text])
            token_count = (tokens[0] != 0).sum().item()
            logger.info(f"【API】预处理后文本token数量: {token_count}")

            # 检查token数量是否超过限制
            MAX_TOKENS = 77
            if token_count >= MAX_TOKENS:
                logger.warning(f"【API】预处理后文本token数量({token_count})仍超过CLIP模型限制(77)，将进行二次裁剪")

                # 二分查找合适的文本长度
                left, right = 1, len(request.text)
                truncated_text = request.text[:20]  # 默认非常保守的裁剪

                while left <= right:
                    mid = (left + right) // 2
                    test_text = request.text[:mid]
                    try:
                        test_tokens = clip.tokenize([test_text])
                        test_token_count = (test_tokens[0] != 0).sum().item()

                        if test_token_count < MAX_TOKENS - 1:  # 留一个位置给结束token
                            left = mid + 1
                            truncated_text = test_text
                        else:
                            right = mid - 1
                    except Exception as e:
                        logger.error(f"【API】二分查找过程中检查token数量时发生错误: {e}")
                        right = mid - 1  # 出错时减小搜索范围

                logger.info(f"【API】文本已二次裁剪: 预处理后长度={len(request.text)}字符，二次裁剪后={len(truncated_text)}字符")
                request.text = truncated_text
        except Exception as e:
            logger.error(f"【API】检查token数量时发生错误: {str(e)}")
            # 如果token检查失败，使用简单的字符长度限制
            if len(request.text) > 20:  # 设置一个非常保守的字符长度限制
                request.text = request.text[:20]
                logger.warning(f"【API】Token检查失败，使用简单字符长度限制裁剪文本: 裁剪后={len(request.text)}字符")

        logger.info(f"【API】最终处理后的文本: '{request.text}' (长度: {len(request.text)}字符)")

        # 提取特征（内部会处理token长度限制）
        logger.info(f"【API】调用extract_text_features提取特征")
        features = extract_text_features(request.text)
        logger.info(f"【API】特征提取成功，特征向量形状: {features.shape}")

        # 编码特征向量为Base64
        features_bytes = pickle.dumps(features)
        features_base64 = base64.b64encode(features_bytes).decode('utf-8')
        logger.info(f"【API】特征向量已编码为Base64，长度: {len(features_base64)} 字符")

        return {"features": features_base64}
    except Exception as e:
        logger.error(f"【API】提取文本特征时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/extract_image_features")
async def api_extract_image_features(file: UploadFile = File(...)):
    """从图像中提取特征向量"""
    try:
        # 读取上传的图像
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))

        # 提取特征
        features = extract_image_features(image)

        # 编码特征向量为Base64
        features_bytes = pickle.dumps(features)
        features_base64 = base64.b64encode(features_bytes).decode('utf-8')

        return {"features": features_base64}
    except Exception as e:
        logger.error(f"提取图像特征时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/add_vector")
async def api_add_vector(request: AddVectorRequest):
    """将特征向量添加到索引"""
    try:
        # 验证参数
        if request.item_type not in ITEM_TYPES:
            raise HTTPException(status_code=400, detail=f"无效的物品类型: {request.item_type}")
        if request.index_type not in INDEX_TYPES:
            raise HTTPException(status_code=400, detail=f"无效的索引类型: {request.index_type}")

        # 解码Base64特征向量
        features_bytes = base64.b64decode(request.features)
        features = pickle.loads(features_bytes)

        # 添加向量到索引
        vector_id = add_vector_to_index(request.item_id, request.item_type, features, request.index_type)

        return {"vector_id": vector_id}
    except Exception as e:
        logger.error(f"添加向量到索引时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search_similar")
async def api_search_similar(request: SearchRequest):
    """搜索相似向量"""
    try:
        # 验证参数
        if request.item_type not in ITEM_TYPES:
            raise HTTPException(status_code=400, detail=f"无效的物品类型: {request.item_type}")
        if request.index_type not in INDEX_TYPES:
            raise HTTPException(status_code=400, detail=f"无效的索引类型: {request.index_type}")

        # 解码Base64特征向量
        features_bytes = base64.b64decode(request.features)
        features = pickle.loads(features_bytes)

        # 搜索相似向量
        results = search_similar_vectors(features, request.item_type, request.index_type, request.limit)

        return {"results": results}
    except Exception as e:
        logger.error(f"搜索相似向量时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search_mixed")
async def api_search_mixed(request: MixedSearchRequest):
    """混合搜索，同时使用文本和图像特征"""
    try:
        # 验证参数
        if request.target_type not in ITEM_TYPES:
            raise HTTPException(status_code=400, detail=f"无效的目标物品类型: {request.target_type}")

        if not request.text_features and not request.image_features:
            raise HTTPException(status_code=400, detail="必须提供至少一种特征向量（文本或图像）")

        # 解码特征向量
        text_vector = None
        if request.text_features:
            text_bytes = base64.b64decode(request.text_features)
            text_vector = pickle.loads(text_bytes)

        image_vector = None
        if request.image_features:
            image_bytes = base64.b64decode(request.image_features)
            image_vector = pickle.loads(image_bytes)

        # 执行混合搜索
        results = search_mixed_vectors(text_vector, image_vector, request.target_type, request.limit)

        return {"results": results}
    except Exception as e:
        logger.error(f"混合搜索时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/upload_and_extract")
async def upload_and_extract(
        file: UploadFile = File(...),
        item_type: str = Form(...)
):
    """上传图像并提取特征向量"""
    try:
        # 验证参数
        if item_type not in ITEM_TYPES:
            raise HTTPException(status_code=400, detail=f"无效的物品类型: {item_type}")

        # 读取上传的图像
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))

        # 提取特征
        features = extract_image_features(image)

        # 编码特征向量为Base64
        features_bytes = pickle.dumps(features)
        features_base64 = base64.b64encode(features_bytes).decode('utf-8')

        return {"features": features_base64}
    except Exception as e:
        logger.error(f"上传并提取特征时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/image_search")
async def image_search(
        file: UploadFile = File(...),
        target_type: str = Query(..., description="目标物品类型 (LOST/FOUND)"),
        limit: int = Query(20, description="返回结果数量限制")
):
    """
    图像搜索接口 - 上传图片并搜索相似物品

    此接口同时返回图像到图像和图像到文本的搜索结果
    """
    try:
        # 记录请求信息
        logger.info(f"【图像搜索】接收到图像搜索请求: 文件名={file.filename}, 目标类型={target_type}, 限制={limit}")

        # 验证参数
        if target_type not in ITEM_TYPES:
            logger.error(f"【图像搜索】无效的目标物品类型: {target_type}")
            raise HTTPException(status_code=400, detail=f"无效的目标物品类型: {target_type}")

        # 读取上传的图像
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        logger.info(f"【图像搜索】成功读取图像: 尺寸={image.size}, 模式={image.mode}")

        # 提取图像特征
        logger.info(f"【图像搜索】开始提取图像特征")
        image_features = extract_image_features(image)
        logger.info(f"【图像搜索】图像特征提取完成: 形状={image_features.shape}")

        # 图像到图像搜索
        logger.info(f"【图像搜索】开始图像到图像搜索")
        image_to_image_results = search_similar_vectors(image_features, target_type, "IMAGE", limit)
        logger.info(f"【图像搜索】图像到图像搜索完成: 找到 {len(image_to_image_results)} 个结果")

        # 图像到文本搜索
        logger.info(f"【图像搜索】开始图像到文本搜索")
        image_to_text_results = search_similar_vectors(image_features, target_type, "TEXT", limit)
        logger.info(f"【图像搜索】图像到文本搜索完成: 找到 {len(image_to_text_results)} 个结果")

        # 对图像到文本的相似度进行压缩处理
        for result in image_to_text_results:
            original_similarity = result["similarity"]

            # 对image-to-text匹配分数进行条件平方压缩
            if original_similarity < 0.75:
                compressed_similarity = original_similarity  # 低分不动
            else:
                compressed_similarity = min(original_similarity * original_similarity, 0.85)  # 平方压缩 + clip 上限

            # 记录压缩信息
            logger.info(f"【图像搜索】图像-文本结果压缩: item_id={result['item_id']}, 原始相似度={original_similarity}, 压缩后相似度={compressed_similarity}")

            # 更新相似度为压缩后的值
            result["similarity"] = compressed_similarity

        # 为结果添加匹配类型
        for result in image_to_image_results:
            result["match_type"] = "IMAGE_TO_IMAGE"

        for result in image_to_text_results:
            result["match_type"] = "IMAGE_TO_TEXT"

        # 记录最终结果
        logger.info(f"【图像搜索】搜索结果统计: 图像-图像={len(image_to_image_results)}, 图像-文本={len(image_to_text_results)}")

        # 添加详细的统计日志
        logger.info(f"图像搜索统计: 模式 IMAGE→IMAGE 命中 {len(image_to_image_results)}")
        logger.info(f"图像搜索统计: 模式 IMAGE→TEXT 命中 {len(image_to_text_results)}")

        # 记录图像-文本结果的详细信息
        if len(image_to_text_results) > 0:
            for i, result in enumerate(image_to_text_results):
                logger.info(f"【图像搜索】图像-文本结果 #{i+1}: item_id={result['item_id']}, similarity={result['similarity']}")
        else:
            logger.warning(f"【图像搜索】图像-文本搜索没有找到结果")

        return {
            "image_to_image": image_to_image_results,
            "image_to_text": image_to_text_results
        }
    except Exception as e:
        logger.error(f"【图像搜索】图像搜索时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/text_search")
async def text_search(
        request: Request,
        target_type: str = Query(..., description="目标物品类型 (LOST/FOUND)"),
        limit: int = Query(20, description="返回结果数量限制")
):
    """
    文本搜索接口 - 输入文本或文本特征向量并搜索相似物品

    此接口同时返回文本到文本和文本到图像的搜索结果
    """
    try:
        # 记录请求信息
        logger.info(f"【文本搜索】接收到文本搜索请求: 目标类型={target_type}, 限制={limit}")

        # 检查内容类型
        content_type = request.headers.get("Content-Type", "")
        logger.info(f"【文本搜索】请求的Content-Type: {content_type}")

        # 初始化参数
        text = None
        text_features = None

        # 如果是表单数据
        if "form-data" in content_type or "x-www-form-urlencoded" in content_type:
            form_data = await request.form()
            text = form_data.get("text")
            text_features = form_data.get("text_features")
            logger.info(f"【文本搜索】从表单数据获取参数: text={text is not None}, text_features={text_features is not None}")
        # 如果是JSON数据
        elif "application/json" in content_type:
            try:
                json_data = await request.json()
                text = json_data.get("text")
                text_features = json_data.get("text_features")
                logger.info(f"【文本搜索】从JSON数据获取参数: text={text is not None}, text_features={text_features is not None}")
            except Exception as e:
                logger.error(f"【文本搜索】解析JSON数据时发生错误: {str(e)}")
                # 如果JSON解析失败，尝试读取原始请求体
                body = await request.body()
                logger.info(f"【文本搜索】原始请求体: {body[:100]}...")

        # 如果都没有，尝试从查询参数获取
        if text is None:
            text = request.query_params.get("text")
        if text_features is None:
            text_features = request.query_params.get("text_features")

        logger.info(f"【文本搜索】最终参数: text={text is not None}, text_features={text_features is not None}, target_type={target_type}, limit={limit}")

        # 验证参数
        if target_type not in ITEM_TYPES:
            raise HTTPException(status_code=400, detail=f"无效的目标物品类型: {target_type}")

        if not text and not text_features:
            raise HTTPException(status_code=400, detail="必须提供文本或文本特征向量")

        # 提取或解码文本特征
        if text_features:
            # 解码Base64编码的特征向量
            try:
                features_bytes = base64.b64decode(text_features)
                features = pickle.loads(features_bytes)
            except Exception as e:
                logger.error(f"解码特征向量时发生错误: {str(e)}")
                raise HTTPException(status_code=400, detail=f"无效的特征向量格式: {str(e)}")
        else:
            # 记录原始文本长度
            original_length = len(text)
            logger.info(f"文本搜索，原始文本长度: {original_length} 字符")

            # 检查原始文本的token数量
            tokens = clip.tokenize([text])
            token_count = (tokens[0] != 0).sum().item()
            logger.info(f"原始文本token数量: {token_count}")

            # 从文本提取特征（内部会处理token长度限制）
            features = extract_text_features(text)

        # 文本到文本搜索
        text_to_text_results = search_similar_vectors(features, target_type, "TEXT", limit)

        # 对文本到文本的相似度进行压缩处理
        for result in text_to_text_results:
            original_similarity = result["similarity"]

            # 对text-to-text匹配分数进行条件平方压缩
            if original_similarity < 0.75:
                compressed_similarity = original_similarity  # 低分不动
            else:
                compressed_similarity = min(original_similarity * original_similarity, 0.85)  # 平方压缩 + clip 上限

            # 记录压缩信息
            logger.info(f"【文本搜索】文本-文本结果压缩: item_id={result['item_id']}, 原始相似度={original_similarity}, 压缩后相似度={compressed_similarity}")

            # 更新相似度为压缩后的值
            result["similarity"] = compressed_similarity

        # 文本到图像搜索
        text_to_image_results = search_similar_vectors(features, target_type, "IMAGE", limit)

        # 为结果添加匹配类型
        for result in text_to_text_results:
            result["match_type"] = "TEXT_TO_TEXT"

        for result in text_to_image_results:
            result["match_type"] = "TEXT_TO_IMAGE"

        # 添加详细的统计日志
        logger.info(f"文本搜索统计: 模式 TEXT→TEXT 命中 {len(text_to_text_results)}")
        logger.info(f"文本搜索统计: 模式 TEXT→IMAGE 命中 {len(text_to_image_results)}")

        # 记录文本-文本结果的详细信息
        if len(text_to_text_results) > 0:
            for i, result in enumerate(text_to_text_results[:5]):  # 只记录前5个结果
                logger.info(f"【文本搜索】文本-文本结果 #{i+1}: item_id={result['item_id']}")
        else:
            logger.warning(f"【文本搜索】文本-文本搜索没有找到结果")

        return {
            "text_to_text": text_to_text_results,
            "text_to_image": text_to_image_results
        }
    except Exception as e:
        logger.error(f"文本搜索时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/multimodal_search")
async def multimodal_search(
        file: UploadFile = File(None, description="上传的图像文件（可选）"),
        text: str = Form(None, description="搜索文本（可选）"),
        text_features: str = Form(None, description="Base64编码的文本特征向量（可选）"),
        image_features: str = Form(None, description="Base64编码的图像特征向量（可选）"),
        target_type: str = Query(..., description="目标物品类型 (LOST/FOUND)"),
        limit: int = Query(20, description="返回结果数量限制")
):
    """
    多模态搜索接口 - 同时使用图像和文本搜索相似物品

    此接口只返回综合排序后的结果，但包含四种相似度的详细信息，供前端进行雷达图展示
    """
    try:
        # 验证参数
        if target_type not in ITEM_TYPES:
            raise HTTPException(status_code=400, detail=f"无效的目标物品类型: {target_type}")

        # 提取特征
        text_features_vector = None
        image_features_vector = None

        # 处理文本特征
        if text_features:
            # 解码Base64编码的特征向量
            try:
                features_bytes = base64.b64decode(text_features)
                text_features_vector = pickle.loads(features_bytes)
                logger.info(f"多模态搜索，成功解码文本特征向量")
            except Exception as e:
                logger.error(f"解码文本特征向量时发生错误: {str(e)}")
                raise HTTPException(status_code=400, detail=f"无效的文本特征向量格式: {str(e)}")
        elif text:
            # 记录原始文本长度
            original_length = len(text)
            logger.info(f"多模态搜索，原始文本长度: {original_length} 字符")

            # 检查原始文本的token数量
            tokens = clip.tokenize([text])
            token_count = (tokens[0] != 0).sum().item()
            logger.info(f"原始文本token数量: {token_count}")

            # 从文本提取特征（内部会处理token长度限制）
            text_features_vector = extract_text_features(text)
            logger.info(f"多模态搜索，成功从文本提取特征向量")

        # 处理图像特征
        if image_features:
            # 解码Base64编码的特征向量
            try:
                features_bytes = base64.b64decode(image_features)
                image_features_vector = pickle.loads(features_bytes)
                logger.info(f"多模态搜索，成功解码图像特征向量")
            except Exception as e:
                logger.error(f"解码图像特征向量时发生错误: {str(e)}")
                raise HTTPException(status_code=400, detail=f"无效的图像特征向量格式: {str(e)}")
        elif file:
            contents = await file.read()
            image = Image.open(io.BytesIO(contents))
            image_features_vector = extract_image_features(image)
            logger.info(f"多模态搜索，成功从图像提取特征向量")

        # 检查是否至少有一种特征
        if text_features_vector is None and image_features_vector is None:
            raise HTTPException(status_code=400, detail="必须提供至少一种搜索条件（图像或文本）")

        # 执行混合搜索获取合并结果
        combined_results = search_mixed_vectors(text_features_vector, image_features_vector, target_type, limit)

        # 添加详细的统计日志
        logger.info(f"多模态搜索统计: 综合结果 命中 {len(combined_results)}")

        # 确保每个结果项中包含四种相似度的详细信息
        for result in combined_results:
            if "match_details" not in result:
                logger.warning(f"结果项缺少match_details字段: {result}")
            else:
                details = result["match_details"]
                logger.info(f"结果项 {result['item_id']} 的四种相似度: TEXT_TO_TEXT={details.get('TEXT_TO_TEXT', 0)}, "
                           f"IMAGE_TO_IMAGE={details.get('IMAGE_TO_IMAGE', 0)}, "
                           f"TEXT_TO_IMAGE={details.get('TEXT_TO_IMAGE', 0)}, "
                           f"IMAGE_TO_TEXT={details.get('IMAGE_TO_TEXT', 0)}")

        # 只返回综合排序后的结果
        return {
            "results": combined_results
        }
    except Exception as e:
        logger.error(f"多模态搜索时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/index_stats")
async def api_index_stats():
    """获取索引统计信息"""
    try:
        logger.info("【索引统计】获取索引统计信息")
        stats = {}
        for item_type in ITEM_TYPES:
            for index_type in INDEX_TYPES:
                index_name = f"{item_type}_{index_type}"
                index = indexes[index_name]

                # 获取索引中的向量数量
                vector_count = index.ntotal
                # 获取映射中的物品数量
                item_count = len(mappings[index_name])

                stats[index_name] = {
                    "vector_count": vector_count,
                    "dimension": FEATURE_DIM,
                    "item_count": item_count
                }

                logger.info(f"【索引统计】{index_name}: 向量数量={vector_count}, 维度={FEATURE_DIM}, 物品数量={item_count}")

                # 如果索引为空，记录警告
                if vector_count == 0:
                    logger.warning(f"【索引统计】警告: {index_name} 索引为空")

                # 如果向量数量和物品数量不一致，记录警告
                if vector_count != item_count:
                    logger.warning(f"【索引统计】警告: {index_name} 索引中的向量数量 ({vector_count}) 与映射中的物品数量 ({item_count}) 不一致")

                # 如果物品数量大于0，记录前5个物品ID
                if item_count > 0:
                    item_ids = list(mappings[index_name].keys())[:5]
                    logger.info(f"【索引统计】{index_name} 前5个物品ID: {item_ids}")

        logger.info(f"【索引统计】索引统计信息获取完成: {stats}")
        return {"stats": stats}
    except Exception as e:
        logger.error(f"【索引统计】获取索引统计信息时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/save_indices")
async def api_save_indices():
    """保存所有索引到磁盘"""
    try:
        save_all_indices()
        return {"success": True, "message": "所有索引已成功保存"}
    except Exception as e:
        logger.error(f"保存索引时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/rebuild_index")
async def api_rebuild_index(
        item_type: str = Form(...),
        index_type: str = Form(...)
):
    """重建索引"""
    try:
        # 验证参数
        valid_item_types = ITEM_TYPES + ["ALL"]
        valid_index_types = INDEX_TYPES + ["ALL"]

        if item_type not in valid_item_types:
            raise HTTPException(status_code=400, detail=f"无效的物品类型: {item_type}")

        if index_type not in valid_index_types:
            raise HTTPException(status_code=400, detail=f"无效的索引类型: {index_type}")

        # 确定要重建的索引
        item_types_to_rebuild = ITEM_TYPES if item_type == "ALL" else [item_type]
        index_types_to_rebuild = INDEX_TYPES if index_type == "ALL" else [index_type]

        # 重建索引
        for it in item_types_to_rebuild:
            for idx in index_types_to_rebuild:
                index_name = f"{it}_{idx}"
                # 创建新索引
                indexes[index_name] = create_hnsw_index()
                # 保留映射
                save_index(it, idx)

        return {
            "success": True,
            "message": f"已重建索引: 物品类型={item_types_to_rebuild}, 索引类型={index_types_to_rebuild}"
        }
    except Exception as e:
        logger.error(f"重建索引时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "clip_model": CLIP_MODEL_NAME,
        "device": device,
        "indices": list(indexes.keys()),
        "vector_counts": {name: index.ntotal for name, index in indexes.items()}
    }

# 启动时的初始化
@app.on_event("startup")
async def startup_event():
    """服务启动时执行的初始化操作"""
    logger.info("CLIP+FAISS API 服务正在启动...")

    # 加载索引
    load_indices()

    logger.info(f"CLIP模型已加载到{device}设备")
    logger.info(f"已初始化以下索引: {list(indexes.keys())}")

# 关闭时的清理
@app.on_event("shutdown")
async def shutdown_event():
    """服务关闭时执行的清理操作"""
    logger.info("保存所有索引...")
    save_all_indices()
    logger.info("CLIP+FAISS API 服务正在关闭...")

# 启动服务器
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)