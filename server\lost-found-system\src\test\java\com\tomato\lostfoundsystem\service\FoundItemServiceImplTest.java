package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.FoundItemDTO;
import com.tomato.lostfoundsystem.entity.FoundItem;
import com.tomato.lostfoundsystem.mapper.FoundItemMapper;
import com.tomato.lostfoundsystem.mapper.ItemAuditMapper;
import com.tomato.lostfoundsystem.service.Impl.FoundItemServiceImpl;
import com.tomato.lostfoundsystem.utils.AliyunOSSUtil;
import com.tomato.lostfoundsystem.utils.RedisUtil;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class FoundItemServiceImplTest {

    @Mock
    private FoundItemMapper foundItemMapper;

    @Mock
    private ItemAuditMapper itemAuditMapper;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private SecurityUtil securityUtil;

    @Mock
    private AutoMatchNotificationService autoMatchNotificationService;

    @Mock
    private ItemImageService itemImageService;

    @InjectMocks
    private FoundItemServiceImpl foundItemService;

    private FoundItemDTO validFoundItemDTO;
    private FoundItemDTO invalidFoundItemDTO;
    private MockMultipartFile mockImage;
    private List<MultipartFile> mockImages;

    @BeforeEach
    void setUp() {
        // 初始化有效的FoundItemDTO
        validFoundItemDTO = new FoundItemDTO();
        validFoundItemDTO.setUserId(1L);
        validFoundItemDTO.setItemName("测试拾物");
        validFoundItemDTO.setDescription("这是一个详细的拾物描述，包含了物品的颜色、形状和特征，长度超过20个字符。");
        validFoundItemDTO.setFoundTime("2023-05-01T10:00:00");
        validFoundItemDTO.setFoundLocation("图书馆");

        // 初始化无效的FoundItemDTO（描述太短）
        invalidFoundItemDTO = new FoundItemDTO();
        invalidFoundItemDTO.setUserId(1L);
        invalidFoundItemDTO.setItemName("测试拾物");
        invalidFoundItemDTO.setDescription("描述太短");
        invalidFoundItemDTO.setFoundTime("2023-05-01T10:00:00");
        invalidFoundItemDTO.setFoundLocation("图书馆");

        // 创建模拟图片文件
        mockImage = new MockMultipartFile(
                "image",
                "test.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );

        // 创建模拟多图片文件列表
        MockMultipartFile mockImage1 = new MockMultipartFile(
                "images",
                "test1.jpg",
                "image/jpeg",
                "test image content 1".getBytes()
        );
        
        MockMultipartFile mockImage2 = new MockMultipartFile(
                "images",
                "test2.jpg",
                "image/jpeg",
                "test image content 2".getBytes()
        );
        
        MockMultipartFile mockImage3 = new MockMultipartFile(
                "images",
                "test3.jpg",
                "image/jpeg",
                "test image content 3".getBytes()
        );
        
        mockImages = Arrays.asList(mockImage1, mockImage2, mockImage3);
    }

    /**
     * 测试使用多张图片发布拾物
     */
    @Test
    void publishFoundItem_WithMultipleImages_ShouldSaveAdditionalImages() throws Exception {
        // 准备测试数据
        validFoundItemDTO.setImages(mockImages);

        // 模拟AliyunOSSUtil.uploadFoundImage方法
        try (var mockedStatic = mockStatic(AliyunOSSUtil.class)) {
            mockedStatic.when(() -> AliyunOSSUtil.uploadFoundImage(any(MultipartFile.class)))
                    .thenReturn("http://example.com/test.jpg");

            // 模拟FoundItemMapper.insertFoundItem方法
            when(foundItemMapper.insertFoundItem(any(FoundItem.class))).thenAnswer(invocation -> {
                FoundItem foundItem = invocation.getArgument(0);
                foundItem.setId(1L); // 设置ID，模拟数据库自增
                return 1;
            });

            // 模拟ItemImageService.saveItemImages方法
            when(itemImageService.saveItemImages(anyLong(), anyString(), anyList()))
                    .thenReturn(Arrays.asList(
                            "http://example.com/test1.jpg",
                            "http://example.com/test2.jpg",
                            "http://example.com/test3.jpg"
                    ));

            // 执行测试
            Result<Object> result = foundItemService.publishFoundItem(validFoundItemDTO);

            // 验证结果
            assertEquals(200, result.getCode());
            assertTrue(result.getMessage().contains("发布成功"));

            // 验证方法调用
            verify(foundItemMapper, times(1)).insertFoundItem(any(FoundItem.class));
            verify(itemImageService, times(1)).saveItemImages(eq(1L), eq("FOUND"), anyList());
        }
    }

    /**
     * 测试使用单张图片发布拾物
     */
    @Test
    void publishFoundItem_WithSingleImage_ShouldSucceed() throws Exception {
        // 准备测试数据
        validFoundItemDTO.setImage(mockImage);

        // 模拟AliyunOSSUtil.uploadFoundImage方法
        try (var mockedStatic = mockStatic(AliyunOSSUtil.class)) {
            mockedStatic.when(() -> AliyunOSSUtil.uploadFoundImage(any(MultipartFile.class)))
                    .thenReturn("http://example.com/test.jpg");

            // 模拟FoundItemMapper.insertFoundItem方法
            when(foundItemMapper.insertFoundItem(any(FoundItem.class))).thenReturn(1);

            // 执行测试
            Result<Object> result = foundItemService.publishFoundItem(validFoundItemDTO);

            // 验证结果
            assertEquals(200, result.getCode());
            assertTrue(result.getMessage().contains("发布成功"));

            // 验证方法调用
            verify(foundItemMapper, times(1)).insertFoundItem(any(FoundItem.class));
        }
    }

    /**
     * 测试图片上传失败的情况
     */
    @Test
    void publishFoundItem_WhenImageUploadFails_ShouldReturnError() throws Exception {
        // 准备测试数据
        validFoundItemDTO.setImage(mockImage);

        // 模拟AliyunOSSUtil.uploadFoundImage方法抛出异常
        try (var mockedStatic = mockStatic(AliyunOSSUtil.class)) {
            mockedStatic.when(() -> AliyunOSSUtil.uploadFoundImage(any(MultipartFile.class)))
                    .thenThrow(new RuntimeException("Upload failed"));

            // 执行测试
            Result<Object> result = foundItemService.publishFoundItem(validFoundItemDTO);

            // 验证结果
            assertEquals(400, result.getCode());
            assertTrue(result.getMessage().contains("图片上传失败"));

            // 验证方法调用
            verify(foundItemMapper, never()).insertFoundItem(any(FoundItem.class));
        }
    }
}
