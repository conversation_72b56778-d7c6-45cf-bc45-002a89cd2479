/**
 * IndexedDB 管理器
 *
 * 用于管理聊天消息的本地存储，支持离线消息处理
 */

export default class IndexedDBManager {
  constructor(dbName = 'chat_db', version = 1) {
    this.dbName = dbName
    this.version = version
    this.db = null
    this.isOpen = false
  }

  // 打开数据库
  async open() {
    if (this.isOpen && this.db) return this.db

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version)

      request.onerror = (event) => {
        console.error('打开IndexedDB失败:', event.target.error)
        reject(event.target.error)
      }

      request.onsuccess = (event) => {
        this.db = event.target.result
        this.isOpen = true
        resolve(this.db)
      }

      request.onupgradeneeded = (event) => {
        const db = event.target.result

        // 创建消息存储
        if (!db.objectStoreNames.contains('messages')) {
          const messagesStore = db.createObjectStore('messages', { keyPath: 'id' })
          messagesStore.createIndex('conversationId', 'conversationId', { unique: false })
          messagesStore.createIndex('timestamp', 'timestamp', { unique: false })
          messagesStore.createIndex('status', 'status', { unique: false })
        }

        // 创建联系人存储
        if (!db.objectStoreNames.contains('contacts')) {
          const contactsStore = db.createObjectStore('contacts', { keyPath: 'id' })
          contactsStore.createIndex('lastMessageTime', 'lastMessageTime', { unique: false })
        }

        // 创建文件存储
        if (!db.objectStoreNames.contains('files')) {
          const filesStore = db.createObjectStore('files', { keyPath: 'id' })
          filesStore.createIndex('messageId', 'messageId', { unique: false })
          filesStore.createIndex('timestamp', 'timestamp', { unique: false })
        }

        // 创建设置存储
        if (!db.objectStoreNames.contains('settings')) {
          db.createObjectStore('settings', { keyPath: 'key' })
        }
      }
    })
  }

  // 关闭数据库
  close() {
    if (this.db) {
      this.db.close()
      this.isOpen = false
      this.db = null
    }
  }

  // 保存消息
  async saveMessage(message) {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['messages'], 'readwrite')
      const store = transaction.objectStore('messages')

      // 确保消息有时间戳和会话ID
      if (!message.timestamp) {
        message.timestamp = new Date().toISOString()
      }

      if (!message.conversationId && message.sender && message.receiver) {
        message.conversationId = this.getConversationId(message.sender, message.receiver)
      }

      const request = store.put(message)

      request.onsuccess = () => resolve(message)
      request.onerror = (event) => reject(event.target.error)
    })
  }

  // 批量保存消息
  async saveMessages(messages) {
    if (!messages || messages.length === 0) return []

    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['messages'], 'readwrite')
      const store = transaction.objectStore('messages')

      let completed = 0
      const results = []

      messages.forEach(message => {
        // 确保消息有时间戳和会话ID
        if (!message.timestamp) {
          message.timestamp = new Date().toISOString()
        }

        if (!message.conversationId && message.sender && message.receiver) {
          message.conversationId = this.getConversationId(message.sender, message.receiver)
        }

        const request = store.put(message)

        request.onsuccess = () => {
          results.push(message)
          completed++

          if (completed === messages.length) {
            resolve(results)
          }
        }

        request.onerror = (event) => {
          console.error('保存消息失败:', event.target.error)
          completed++

          if (completed === messages.length) {
            resolve(results)
          }
        }
      })
    })
  }

  // 获取消息
  async getMessage(messageId) {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['messages'], 'readonly')
      const store = transaction.objectStore('messages')

      const request = store.get(messageId)

      request.onsuccess = () => resolve(request.result)
      request.onerror = (event) => reject(event.target.error)
    })
  }

  // 删除消息
  async deleteMessage(messageId) {
    try {
      const db = await this.open()
      const tx = db.transaction('messages', 'readwrite')
      const store = tx.objectStore('messages')

      const request = store.delete(messageId)

      return new Promise((resolve, reject) => {
        request.onsuccess = () => {
          console.log(`成功从IndexedDB中删除消息: ${messageId}`)
          resolve(true)
        }
        request.onerror = (event) => {
          console.error(`从IndexedDB中删除消息失败: ${messageId}`, event.target.error)
          reject(event.target.error)
        }
        tx.oncomplete = () => {
          console.log(`删除消息事务完成: ${messageId}`)
        }
      })
    } catch (error) {
      console.error('删除消息失败:', error)
      throw error
    }
  }

  // 获取会话消息
  async getConversationMessages(conversationId, limit = 50, before = null) {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['messages'], 'readonly')
      const store = transaction.objectStore('messages')
      const index = store.index('conversationId')

      // 使用游标查询，按时间戳倒序
      const messages = []
      const request = index.openCursor(IDBKeyRange.only(conversationId), 'prev')

      request.onsuccess = (event) => {
        const cursor = event.target.result

        if (cursor && messages.length < limit) {
          messages.push(cursor.value)
          cursor.continue()
        } else {
          resolve(messages)
        }
      }

      request.onerror = (event) => reject(event.target.error)
    })
  }

  // 获取所有离线消息
  async getOfflineMessages() {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['messages'], 'readonly')
      const store = transaction.objectStore('messages')
      const index = store.index('status')

      const messages = []
      const request = index.openCursor(IDBKeyRange.only('OFFLINE'))

      request.onsuccess = (event) => {
        const cursor = event.target.result

        if (cursor) {
          // 检查消息是否有特殊标记
          const message = cursor.value

          // 添加到结果集，后续由 OfflineMessageHandler 进行过滤
          messages.push(message)
          cursor.continue()
        } else {
          console.log(`从IndexedDB获取到 ${messages.length} 条离线消息`)
          resolve(messages)
        }
      }

      request.onerror = (event) => reject(event.target.error)
    })
  }

  // 更新消息状态
  async updateMessageStatus(messageId, status, additionalData = {}) {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['messages'], 'readwrite')
      const store = transaction.objectStore('messages')

      // 先获取消息
      const getRequest = store.get(messageId)

      getRequest.onsuccess = () => {
        const message = getRequest.result

        if (!message) {
          // 消息不存在，但不抛出错误，而是返回一个带有警告的结果
          console.warn(`尝试更新不存在的消息状态: ${messageId}，可能是临时消息已被删除或替换`)

          // 如果是以"temp-"开头的临时消息ID，可能是正常情况
          if (String(messageId).startsWith('temp-')) {
            console.info(`这是一个临时消息ID (${messageId})，忽略此警告`)
            resolve({
              id: messageId,
              status: status,
              warning: '消息不存在，可能已被替换为永久ID'
            })
          } else {
            // 非临时消息ID不存在，可能是已经被删除或替换
            console.warn(`非临时消息ID不存在: ${messageId}，可能已被删除或替换`)

            // 尝试检查是否是数字ID
            const isNumericId = !isNaN(parseInt(messageId, 10));

            if (isNumericId) {
              // 如果是数字ID，可能是服务器上的消息，但在本地IndexedDB中不存在
              // 这种情况下返回警告而不是错误
              console.info(`这是一个数字ID (${messageId})，可能是服务器上的消息，但在本地不存在`)
              resolve({
                id: messageId,
                status: status,
                warning: '消息在本地不存在，但可能在服务器上存在'
              })
            } else {
              // 其他类型的ID，返回错误
              console.error(`无法识别的消息ID格式: ${messageId}`)
              resolve({
                id: messageId,
                status: status,
                error: '消息不存在'
              })
            }
          }
          return
        }

        // 更新状态和附加数据
        message.status = status

        // 合并附加数据
        Object.assign(message, additionalData)

        // 保存更新后的消息
        const updateRequest = store.put(message)

        updateRequest.onsuccess = () => resolve(message)
        updateRequest.onerror = (event) => reject(event.target.error)
      }

      getRequest.onerror = (event) => reject(event.target.error)
    })
  }

  // 获取会话ID
  getConversationId(userId1, userId2) {
    // 确保会话ID一致性，使用较小的ID在前
    return userId1 < userId2
      ? `${userId1}_${userId2}`
      : `${userId2}_${userId1}`
  }

  // 保存联系人
  async saveContact(contact) {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts'], 'readwrite')
      const store = transaction.objectStore('contacts')

      const request = store.put(contact)

      request.onsuccess = () => resolve(contact)
      request.onerror = (event) => reject(event.target.error)
    })
  }

  // 获取所有联系人
  async getAllContacts() {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts'], 'readonly')
      const store = transaction.objectStore('contacts')

      const contacts = []
      const request = store.openCursor()

      request.onsuccess = (event) => {
        const cursor = event.target.result

        if (cursor) {
          contacts.push(cursor.value)
          cursor.continue()
        } else {
          resolve(contacts)
        }
      }

      request.onerror = (event) => reject(event.target.error)
    })
  }

  // 保存设置
  async saveSetting(key, value) {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['settings'], 'readwrite')
      const store = transaction.objectStore('settings')

      const request = store.put({ key, value })

      request.onsuccess = () => resolve({ key, value })
      request.onerror = (event) => reject(event.target.error)
    })
  }

  // 获取设置
  async getSetting(key) {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['settings'], 'readonly')
      const store = transaction.objectStore('settings')

      const request = store.get(key)

      request.onsuccess = () => {
        const setting = request.result
        resolve(setting ? setting.value : null)
      }

      request.onerror = (event) => reject(event.target.error)
    })
  }

  // 清理过期数据
  async clearExpiredData(daysToKeep = 30) {
    const db = await this.open()
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
    const cutoffTimestamp = cutoffDate.toISOString()

    // 清理消息
    await this.clearExpiredMessages(cutoffTimestamp)

    // 清理文件
    await this.clearExpiredFiles(cutoffTimestamp)

    return true
  }

  // 清理过期消息
  async clearExpiredMessages(cutoffTimestamp) {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['messages'], 'readwrite')
      const store = transaction.objectStore('messages')
      const index = store.index('timestamp')

      const request = index.openCursor(IDBKeyRange.upperBound(cutoffTimestamp))
      let deletedCount = 0

      request.onsuccess = (event) => {
        const cursor = event.target.result

        if (cursor) {
          // 不删除离线消息，无论多久
          if (cursor.value.status !== 'OFFLINE') {
            store.delete(cursor.value.id)
            deletedCount++
          }
          cursor.continue()
        } else {
          console.log(`已清理 ${deletedCount} 条过期消息`)
          resolve(deletedCount)
        }
      }

      request.onerror = (event) => reject(event.target.error)
    })
  }

  // 清理过期文件
  async clearExpiredFiles(cutoffTimestamp) {
    const db = await this.open()

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['files'], 'readwrite')
      const store = transaction.objectStore('files')
      const index = store.index('timestamp')

      const request = index.openCursor(IDBKeyRange.upperBound(cutoffTimestamp))
      let deletedCount = 0

      request.onsuccess = (event) => {
        const cursor = event.target.result

        if (cursor) {
          store.delete(cursor.value.id)
          deletedCount++
          cursor.continue()
        } else {
          console.log(`已清理 ${deletedCount} 条过期文件`)
          resolve(deletedCount)
        }
      }

      request.onerror = (event) => reject(event.target.error)
    })
  }
}