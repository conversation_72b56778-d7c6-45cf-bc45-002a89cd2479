package com.tomato.lostfoundsystem.service.Impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.AddAdminDTO;
import com.tomato.lostfoundsystem.dto.FoundItemDetailDTO;
import com.tomato.lostfoundsystem.dto.ItemAuditDTO;
import com.tomato.lostfoundsystem.dto.LostItemDetailsDTO;
import com.tomato.lostfoundsystem.event.ItemApprovedEvent;
import com.tomato.lostfoundsystem.event.ItemRejectedEvent;
import com.tomato.lostfoundsystem.entity.FoundItem;
import com.tomato.lostfoundsystem.entity.ItemAudit;
import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.entity.User;
import com.tomato.lostfoundsystem.enums.AuditStatusEnum;
import com.tomato.lostfoundsystem.mapper.FoundItemMapper;
import com.tomato.lostfoundsystem.mapper.ItemAuditMapper;
import com.tomato.lostfoundsystem.mapper.LostItemMapper;
import com.tomato.lostfoundsystem.mapper.UserMapper;
import com.tomato.lostfoundsystem.service.AdminService;

import com.tomato.lostfoundsystem.service.AsyncTaskService;
import com.tomato.lostfoundsystem.service.KafkaProducerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import com.tomato.lostfoundsystem.utils.PasswordUtil;
import com.tomato.lostfoundsystem.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



@Slf4j
@Transactional
@Service
public class AdminServiceImpl implements AdminService {

    private final LostItemMapper lostItemMapper;
    private final FoundItemMapper foundItemMapper;
    private final ItemAuditMapper itemAuditMapper;

    private final RedisUtil redisUtil;


    @Autowired
    private UserMapper userMapper;



    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    public AdminServiceImpl(LostItemMapper lostItemMapper, FoundItemMapper foundItemMapper, ItemAuditMapper itemAuditMapper, RedisUtil redisUtil) {
        this.lostItemMapper = lostItemMapper;
        this.foundItemMapper = foundItemMapper;
        this.itemAuditMapper = itemAuditMapper;
        this.redisUtil = redisUtil;
    }
    @Override
    public Result<PageInfo<LostItemDetailsDTO>> queryAuditList(int page, int size,
                                                            String keyword,
                                                            String auditStatus,
                                                            String status,
                                                            String startDateStr,
                                                            String endDateStr,
                                                            Long userId) {
        // 1. 处理时间范围
        LocalDateTime startDate = null;
        LocalDateTime endDate = null;
        if (startDateStr != null && !startDateStr.isEmpty()) {
            startDate = LocalDateTime.parse(startDateStr);
        }
        if (endDateStr != null && !endDateStr.isEmpty()) {
            endDate = LocalDateTime.parse(endDateStr);
        }

        // 2. 使用 PageHelper 自动分页
        PageHelper.startPage(page, size);
        List<LostItemDetailsDTO> list = lostItemMapper.selectAuditList(keyword, auditStatus, status, startDate, endDate, userId);

        // 3. 封装 PageInfo 返回
        PageInfo<LostItemDetailsDTO> pageInfo = new PageInfo<>(list);
        return Result.success(pageInfo);
    }


    @Override
    public Result<?> auditLostItem(Long id, ItemAuditDTO itemAuditDTO, Long adminId) {
        try {
            // 添加日志记录
            log.info("【审核状态】审核失物信息 - ID: " + id + ", 状态: " + itemAuditDTO.getAuditStatus() + ", 管理员ID: " + adminId);
            // 查询失物信息
            LostItem lostItem = lostItemMapper.selectById(id);
            if (lostItem == null) {
                log.info("【审核状态】失物信息不存在: " + id);
                return Result.fail("该失物信息不存在");
            }
            // 校验审核状态是否合法
            String auditStatus = itemAuditDTO.getAuditStatus();
            if (!"APPROVED".equals(auditStatus) && !"REJECTED".equals(auditStatus)) {
                log.info("【审核状态】无效的审核状态: " + auditStatus);
                return Result.fail("无效的审核状态");
            }
            // 更新失物信息的审核状态
            AuditStatusEnum statusEnum = "APPROVED".equals(auditStatus) ?
                AuditStatusEnum.APPROVED : AuditStatusEnum.REJECTED;
            lostItem.setAuditStatus(statusEnum);
            log.info("【审核状态】准备更新失物审核状态，物品ID: " + id +
                ", 新状态: " + statusEnum + ", 状态码: " + statusEnum.getCode());
            // 使用专门的方法更新审核状态，确保状态被正确更新
            int updateResult = lostItemMapper.updateAuditStatus(lostItem);
            if (updateResult <= 0) {
                log.info("【审核状态】更新失物审核状态失败，物品ID: " + id);
                return Result.fail("审核失败，无法更新审核状态");
            }
            log.info("【审核状态】成功更新失物审核状态，物品ID: " + id +
                ", 影响行数: " + updateResult);
            // 再次查询确认状态已更新
            LostItem updatedItem = lostItemMapper.selectById(id);
            log.info("【审核状态】更新后的失物状态，物品ID: " + id +
                ", 审核状态: " + updatedItem.getAuditStatus() +
                ", 状态码: " + (updatedItem.getAuditStatus() != null ? updatedItem.getAuditStatus().getCode() : "null"));
            // 创建并插入审核记录
            ItemAudit itemAudit = new ItemAudit();
            itemAudit.setItemId(id);
            itemAudit.setAuditStatus(statusEnum);
            itemAudit.setAuditTime(LocalDateTime.now());
            itemAudit.setAuditorId(adminId);  // 管理员ID
            itemAudit.setRemarks(itemAuditDTO.getRemarks());  // 审核备注
            try {
                itemAuditMapper.insertLostItemAudit(itemAudit);  // 插入审核记录
                log.info("【审核状态】成功插入审核记录，物品ID: " + id);
            } catch (Exception e) {
                log.info("【审核状态】插入审核记录失败: " + e.getMessage());
                e.printStackTrace();
                // 继续执行，不要因为审核记录插入失败而中断整个流程
            }
            // 如果审核通过，发布审核通过事件（异步处理通知）
            if ("APPROVED".equals(itemAuditDTO.getAuditStatus())) {
                // 发布审核通过事件，由事件监听器异步处理通知发送
                eventPublisher.publishEvent(ItemApprovedEvent.create(lostItem.getUserId(), "lost", id));
                log.info("【审核状态】已发布审核通过事件，物品ID: " + id);
                // 使用事务同步，确保在事务提交后执行自动匹配
                log.info("【审核状态】注册事务同步，在事务提交后触发自动匹配，物品ID: " + id);

                // 获取当前用户ID，用于在事务提交后使用
                final Long finalUserId = lostItem.getUserId();

                // 注册事务同步，在事务提交后执行自动匹配
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        try {
                            log.info("【审核状态】事务已提交，开始异步处理自动匹配，物品ID: " + id);

                            // 使用Spring异步任务服务处理（主路径）
                            asyncTaskService.processItemAutoMatchAsync(id, finalUserId, "LOST");
                        } catch (Exception e) {
                            log.error("【审核状态】触发异步处理失败，尝试通过Kafka兜底: " + e.getMessage(), e);

                            // 如果触发异步处理失败，直接通过Kafka兜底（备用路径）
                            com.tomato.lostfoundsystem.model.kafka.ItemApprovedEvent event =
                                com.tomato.lostfoundsystem.model.kafka.ItemApprovedEvent.create(id, "LOST", finalUserId);
                            kafkaProducerService.sendItemApprovedEvent(event);
                        }
                    }
                });
            }

            // 如果审核拒绝，发布审核拒绝事件（异步处理通知）
            if ("REJECTED".equals(itemAuditDTO.getAuditStatus())) {
                // 发布审核拒绝事件，由事件监听器异步处理通知发送
                eventPublisher.publishEvent(ItemRejectedEvent.create(lostItem.getUserId(), "lost", id, itemAudit.getRemarks()));
                log.info("【审核状态】已发布审核拒绝事件，物品ID: " + id);
            }

            return Result.success("审核成功");
        } catch (Exception e) {
            log.info("【审核状态】审核失物失败: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("审核失败，请稍后重试: " + e.getMessage());
        }
    }

    @Override
    public Result<?> auditFoundItem(Long id, ItemAuditDTO itemAuditDTO, Long adminId) {
        try {
            // 添加日志记录
            log.info("审核拾物信息 - ID: " + id + ", 状态: " + itemAuditDTO.getAuditStatus() + ", 管理员ID: " + adminId);

            // 查询拾物信息
            FoundItem foundItem = foundItemMapper.selectById(id);
            if (foundItem == null) {
                return Result.fail("该拾物信息不存在");
            }

            // 校验审核状态是否合法
            String auditStatus = itemAuditDTO.getAuditStatus();
            if (!"APPROVED".equals(auditStatus) && !"REJECTED".equals(auditStatus)) {
                return Result.fail("无效的审核状态");
            }

            // 更新拾物信息的审核状态
            AuditStatusEnum statusEnum = "APPROVED".equals(auditStatus) ?
                AuditStatusEnum.APPROVED : AuditStatusEnum.REJECTED;
            foundItem.setAuditStatus(statusEnum);

            log.info("【审核状态】准备更新拾物审核状态，物品ID: " + id +
                ", 新状态: " + statusEnum + ", 状态码: " + statusEnum.getCode());

            // 使用专门的方法更新审核状态，确保状态被正确更新
            int updateResult = foundItemMapper.updateAuditStatus(foundItem);
            if (updateResult <= 0) {
                log.info("【审核状态】更新拾物审核状态失败，物品ID: " + id);
                return Result.fail("审核失败，无法更新审核状态");
            }

            log.info("【审核状态】成功更新拾物审核状态，物品ID: " + id +
                ", 影响行数: " + updateResult);

            // 再次查询确认状态已更新
            FoundItem updatedItem = foundItemMapper.selectById(id);
            log.info("【审核状态】更新后的拾物状态，物品ID: " + id +
                ", 审核状态: " + updatedItem.getAuditStatus() +
                ", 状态码: " + (updatedItem.getAuditStatus() != null ? updatedItem.getAuditStatus().getCode() : "null"));

            // 创建并插入审核记录
            ItemAudit itemAudit = new ItemAudit();
            itemAudit.setItemId(id);
            itemAudit.setAuditStatus(statusEnum);
            itemAudit.setAuditTime(LocalDateTime.now());
            itemAudit.setAuditorId(adminId);  // 管理员ID
            itemAudit.setRemarks(itemAuditDTO.getRemarks());  // 审核备注

            try {
                itemAuditMapper.insertFoundItemAudit(itemAudit);  // 插入审核记录
            } catch (Exception e) {
                System.err.println("插入审核记录失败: " + e.getMessage());
                e.printStackTrace();
                // 继续执行，不要因为审核记录插入失败而中断整个流程
            }

            // 如果审核通过，发布审核通过事件（异步处理通知）
            if ("APPROVED".equals(itemAuditDTO.getAuditStatus())) {
                // 发布审核通过事件，由事件监听器异步处理通知发送
                eventPublisher.publishEvent(ItemApprovedEvent.create(foundItem.getUserId(), "found", id));
                log.info("【审核状态】已发布审核通过事件，物品ID: " + id);

                // 使用事务同步，确保在事务提交后执行自动匹配
                log.info("【审核状态】注册事务同步，在事务提交后触发自动匹配，物品ID: " + id);

                // 获取当前用户ID，用于在事务提交后使用
                final Long finalUserId = foundItem.getUserId();

                // 注册事务同步，在事务提交后执行
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        try {
                            log.info("【审核状态】事务已提交，开始异步处理自动匹配，物品ID: " + id);

                            // 使用Spring异步任务服务处理（主路径）
                            asyncTaskService.processItemAutoMatchAsync(id, finalUserId, "FOUND");
                        } catch (Exception e) {
                            log.error("【审核状态】触发异步处理失败，尝试通过Kafka兜底: " + e.getMessage(), e);

                            // 如果触发异步处理失败，直接通过Kafka兜底（备用路径）
                            com.tomato.lostfoundsystem.model.kafka.ItemApprovedEvent event =
                                com.tomato.lostfoundsystem.model.kafka.ItemApprovedEvent.create(id, "FOUND", finalUserId);
                            kafkaProducerService.sendItemApprovedEvent(event);
                        }
                    }
                });
            }

            // 如果审核拒绝，发布审核拒绝事件（异步处理通知）
            if ("REJECTED".equals(itemAuditDTO.getAuditStatus())) {
                // 发布审核拒绝事件，由事件监听器异步处理通知发送
                eventPublisher.publishEvent(ItemRejectedEvent.create(foundItem.getUserId(), "found", id, itemAudit.getRemarks()));
                log.info("【审核状态】已发布审核拒绝事件，物品ID: " + id);
            }

            return Result.success("审核成功");
        } catch (Exception e) {
            log.info("审核拾物失败: " + e.getMessage());
            e.printStackTrace();
            return Result.fail("审核失败，请稍后重试: " + e.getMessage());
        }
    }

    @Override
    public Result<?> addAdmin(AddAdminDTO addAdminDTO, Long superAdminId) {

        // 1. 检查用户名是否已存在
        User existingUser = userMapper.findByUsername(addAdminDTO.getUsername());
        if (existingUser != null) {
            return Result.fail("用户名已存在");
        }

        // 1. 获取超级管理员信息
        User superAdmin = userMapper.findById(superAdminId);
        if (superAdmin == null || !"SUPER_ADMIN".equals(superAdmin.getRole())) {
            return Result.fail("只有超级管理员可以执行此操作");
        }

        // 2. 创建新的管理员
        User newAdmin = new User();
        newAdmin.setUsername(addAdminDTO.getUsername());
        newAdmin.setPassword(PasswordUtil.encode(addAdminDTO.getUsername()));  // 密码应加密处理
        newAdmin.setRole("ADMIN");  // 设置为管理员角色

        // 3. 将新管理员保存到数据库
        int result = userMapper.insertUser(newAdmin);
        if (result > 0) {
            return Result.success("管理员添加成功", newAdmin);
        } else {
            return Result.fail("管理员添加失败，请重试");
        }
    }


    /**
     *
     * @param page
     * @param size
     * @param keyword
     * @return 用户列表
     */
    @Override
    public Result<?> getUserList(int page, int size, String keyword,Boolean isActive,String role, Boolean deleted) {
        PageHelper.startPage(page, size);
        List<User> users = userMapper.searchUsers(keyword,isActive,role,deleted);
        PageInfo<User> pageInfo = new PageInfo<>(users);
        return Result.success(pageInfo.getList(), pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 根据用户ID查询用户
     * @param id
     * @return
     */

    @Override
    public Result<?> getUserById(Long id) {
        User user = userMapper.findById(id);
        if (user == null || Boolean.TRUE.equals(user.getDeleted())) {
            return Result.fail("用户不存在或已被删除");
        }
        return Result.success("查询成功", user);
    }

    /**
     * 修改用户角色
     * @param id
     * @param role
     * @return
     */
    @Override
    public Result<?> updateUserRole(Long id, String role) {
        if (!List.of("USER", "ADMIN", "SUPER_ADMIN").contains(role)) {
            return Result.fail("无效的角色类型");
        }
        User user = userMapper.findById(id);
        if (user == null || user.getDeleted()) {
            return Result.fail("用户不存在");
        }
        userMapper.updateUserRole(id, role);
        redisUtil.delete("jwt:user:"+user.getId());
        return Result.success("角色修改成功");
    }

    @Override
    public Result<?> updateUserStatus(Long id, Boolean isActive) {
        User user = userMapper.findById(id);
        if (user == null || user.getDeleted()) {
            return Result.fail("用户不存在");
        }
        userMapper.updateUserStatus(id, isActive);
        redisUtil.delete("jwt:user:"+user.getId());
        return Result.success(isActive ? "用户已启用" : "用户已禁用");
    }

    @Override
    public Result<?> resetUserPassword(Long userIdToReset, String newPassword, Long currentAdminId) {
        User currentAdmin = userMapper.findById(currentAdminId);
        if (currentAdmin == null || currentAdmin.getDeleted()) {
            return Result.fail("当前管理员身份无效");
        }

        User targetUser = userMapper.findById(userIdToReset);
        if (targetUser == null || targetUser.getDeleted()) {
            return Result.fail("目标用户不存在");
        }

        // 如果当前登录的是普通管理员，只能重置 USER 的密码
        if ("ADMIN".equals(currentAdmin.getRole()) && !"USER".equals(targetUser.getRole())) {
            return Result.fail("您没有权限重置管理员或超级管理员的密码");
        }

        String encodedPassword = PasswordUtil.encode(newPassword);
        userMapper.updateUserPassword(userIdToReset, encodedPassword);
        redisUtil.delete("jwt:user:"+targetUser.getId()); // 删除旧 token

        return Result.success("密码重置成功");
    }

    @Override
    public Result<?> deleteUser(Long id) {
        User user = userMapper.findById(id);
        if (user == null || user.getDeleted()) {
            return Result.fail("用户不存在或已删除");
        }

        userMapper.logicalDeleteUser(id);
        return Result.success("用户删除成功");
    }
    @Override
    public Result<PageInfo<FoundItemDetailDTO>> queryFoundItemList(int page, int size, String keyword, String auditStatus, String status, String startDateStr, String endDateStr, Long userId) {
        // 1. 处理时间范围
        LocalDateTime startDate = null;
        LocalDateTime endDate = null;
        if (startDateStr != null && !startDateStr.isEmpty()) {
            startDate = LocalDateTime.parse(startDateStr);
        }
        if (endDateStr != null && !endDateStr.isEmpty()) {
            endDate = LocalDateTime.parse(endDateStr);
        }

        // 2. 使用 PageHelper 自动分页
        PageHelper.startPage(page, size);
        List<FoundItemDetailDTO> list = foundItemMapper.selectFoundItemList(keyword, auditStatus, status, startDate, endDate, userId);

        // 3. 封装 PageInfo 返回
        PageInfo<FoundItemDetailDTO> pageInfo = new PageInfo<>(list);
        return Result.success(pageInfo);
    }

    @Override
    public Result<?> updateFoundItemStatus(Long id, String status) {
        try {
            // 查询拾物信息
            FoundItem foundItem = foundItemMapper.selectById(id);
            if (foundItem == null) {
                return Result.fail("该拾物信息不存在");
            }

            // 校验状态是否合法
            if (!"UNCLAIMED".equals(status) && !"RETURNED".equals(status)) {
                return Result.fail("无效的状态值");
            }

            // 更新拾物状态
            foundItem.setStatus(status);
            int result = foundItemMapper.updateById(foundItem);

            if (result > 0) {
                return Result.success("状态更新成功");
            } else {
                return Result.fail("状态更新失败，请稍后重试");
            }
        } catch (Exception e) {
            return Result.fail("操作失败：" + e.getMessage());
        }
    }

    @Override
    public Result<?> deleteFoundItem(Long id) {
        try {
            // 查询拾物信息
            FoundItem foundItem = foundItemMapper.selectById(id);
            if (foundItem == null) {
                return Result.fail("该拾物信息不存在");
            }

            // 删除拾物
            int result = foundItemMapper.deleteById(id);

            if (result > 0) {
                return Result.success("删除成功");
            } else {
                return Result.fail("删除失败，请稍后重试");
            }
        } catch (Exception e) {
            return Result.fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取管理员统计数据
     * 包括失物统计、拾物统计和用户统计
     *
     * @return 统计数据
     */
    @Override
    public Result<Map<String, Object>> getAdminStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 失物统计
            Map<String, Object> lostStats = new HashMap<>();
            int lostTotal = lostItemMapper.countByAuditStatus("APPROVED");
            int lostLost = lostItemMapper.countByStatusAndAuditStatus("LOST", "APPROVED");
            int lostFound = lostItemMapper.countByStatusAndAuditStatus("FOUND", "APPROVED");

            lostStats.put("total", lostTotal);
            lostStats.put("lost", lostLost);
            lostStats.put("found", lostFound);
            statistics.put("lost", lostStats);

            // 拾物统计
            Map<String, Object> foundStats = new HashMap<>();
            int foundTotal = foundItemMapper.countByAuditStatus("APPROVED");
            int foundUnclaimed = foundItemMapper.countByStatusAndAuditStatus("UNCLAIMED", "APPROVED");
            int foundClaimed = foundItemMapper.countByStatusAndAuditStatus("RETURNED", "APPROVED");

            foundStats.put("total", foundTotal);
            foundStats.put("unclaimed", foundUnclaimed);
            foundStats.put("claimed", foundClaimed);
            statistics.put("found", foundStats);

            // 用户统计
            Map<String, Object> userStats = new HashMap<>();
            int userTotal = userMapper.countTotalUsers();
            int userToday = userMapper.countUsersByRegistrationDate(LocalDate.now());
            int userMonth = userMapper.countUsersByRegistrationMonth(
                LocalDate.now().getYear(), LocalDate.now().getMonthValue());

            userStats.put("total", userTotal);
            userStats.put("today", userToday);
            userStats.put("month", userMonth);
            statistics.put("user", userStats);

            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取管理员统计数据失败", e);
            return Result.fail("获取统计数据失败: " + e.getMessage());
        }
    }





}

