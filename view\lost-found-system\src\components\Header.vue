<template>
  <header class="header">
    <div class="header-container">
      <div class="logo">
        <router-link to="/">
          <div class="logo-container">
            <div class="school-logo">
              <img src="/images/logo_no_text.png" alt="校园失物招领系统" class="logo-image" />
            </div>
            <span class="system-name">校园失物招领系统</span>
          </div>
        </router-link>
      </div>
      <nav class="nav">
        <router-link to="/" class="nav-item" :class="{ active: $route.path === '/' }">
          <el-icon><House /></el-icon>
          首页
        </router-link>
        <router-link to="/lost-items" class="nav-item" :class="{ active: $route.path === '/lost-items' }">
          <el-icon><Box /></el-icon>
          失物信息
        </router-link>
        <router-link to="/found-items" class="nav-item" :class="{ active: $route.path === '/found-items' }">
          <el-icon><Present /></el-icon>
          拾物信息
        </router-link>
        <router-link to="/intelligent-match" class="nav-item highlight-item" :class="{ active: $route.path === '/intelligent-match' }">
          <el-icon><Connection /></el-icon>
          智能匹配
        </router-link>
        <router-link to="/chat" class="nav-item" :class="{ active: $route.path === '/chat' }">
          <el-icon><ChatLineRound /></el-icon>
          聊天
        </router-link>
        <router-link v-if="isAdminUser" to="/admin/dashboard" class="nav-item" :class="{ active: $route.path.startsWith('/admin') }">
          <el-icon><Setting /></el-icon>
          后台管理
        </router-link>
      </nav>
      <div class="user-actions">
        <!-- 在线用户数量 -->
        <OnlineUserCount v-if="isAuthenticated" />

        <!-- 统一通知按钮 -->
        <UnifiedNotifications v-if="isAuthenticated" />

        <el-dropdown v-if="isAuthenticated" trigger="click" @command="handleCommand">
          <div class="avatar-container">
            <el-avatar
              :size="36"
              :src="user?.avatar || defaultAvatar"
              :style="!user?.avatar ? {
                'background-color': getBackgroundColor(user?.id),
                'color': 'white',
                'font-weight': 'bold',
                'font-size': '16px'
              } : {}"
              class="custom-avatar"
            >
              {{ getFirstChar(user?.username) || 'U' }}
            </el-avatar>
            <span class="username">{{ user?.username || '用户' }}</span>
            <el-icon class="dropdown-icon"><CaretBottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="router.push('/profile')">
                <el-icon><User /></el-icon>
                个人中心
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>设置
              </el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-dropdown v-else trigger="click" @command="handleCommand">
          <div class="avatar-container">
            <el-avatar
              :size="36"
              :style="{
                'background-color': '#607D8B',
                'color': 'white',
                'font-weight': 'bold',
                'font-size': '16px'
              }"
              class="custom-avatar"
            >
              游
            </el-avatar>
            <el-icon class="dropdown-icon"><CaretBottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="login">
                <el-icon><Key /></el-icon>登录
              </el-dropdown-item>
              <el-dropdown-item command="register">
                <el-icon><Plus /></el-icon>注册
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { logout } from '../api/user'
import { useUserStore, useAuthStore } from '../stores'
import { getBackgroundColor, getFirstChar } from '../utils/avatar'
import OnlineUserCount from './OnlineUserCount.vue'
import UnifiedNotifications from './UnifiedNotifications.vue'
import {
  User,
  Key,
  Plus,
  SwitchButton,
  CaretBottom,
  House,
  Setting,
  ChatLineRound,
  Connection,
  Box,
  Present
} from '@element-plus/icons-vue'
// 不再需要导入 isAdmin，使用计算属性代替

const router = useRouter()
const userStore = useUserStore()
const authStore = useAuthStore()
const defaultAvatar = ''

// 使用 store 中的状态
const isAuthenticated = computed(() => userStore.isAuthenticated)
const user = computed(() => userStore.userInfo)
// 使用ref存储管理员状态，避免频繁计算和日志输出
const isAdmin = ref(false)

// 定义头像更新事件处理函数
const handleAvatarUpdate = (event) => {
  console.log('收到头像更新事件:', event.detail)
  if (event.detail && event.detail.avatar) {
    // 更新用户头像
    const updatedUser = { ...userStore.userInfo }
    updatedUser.avatar = event.detail.avatar
    userStore.setUser(updatedUser)
  }
}

// 只在组件挂载和用户信息变化时检查一次管理员权限
const checkAdminStatus = () => {
  const userInfo = userStore.userInfo
  if (!userInfo || !userInfo.roles) {
    isAdmin.value = false
    return
  }

  // 确保roles是数组
  const roles = Array.isArray(userInfo.roles) ? userInfo.roles : [userInfo.roles]
  isAdmin.value = roles.some(role => ['ADMIN', 'SUPER_ADMIN'].includes(role))
}

// 创建计算属性，但不在内部输出日志
const isAdminUser = computed(() => isAdmin.value)

// 监听用户信息变化，更新管理员状态
watch(() => userStore.userInfo, () => {
  checkAdminStatus()
}, { immediate: true })



const initUserInfo = async () => {
  try {
    // 检查本地存储中是否有用户信息
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo) {
      const userInfo = JSON.parse(storedUserInfo)
      // 更新 store 中的用户信息
      userStore.setUser(userInfo)
      // 移除详细日志，避免敏感信息泄露
    }
  } catch (error) {
    console.error('初始化用户信息失败:', error)
  }
}

// 处理用户登录事件
const handleUserLogin = (event) => {
  console.log('收到用户登录事件:', event.detail)
  // 更新用户信息
  userStore.setUser(event.detail)
  // 检查管理员状态
  checkAdminStatus()
}

onMounted(async () => {
  // 初始化用户信息
  await initUserInfo()
  // 初始化完成后检查管理员状态
  checkAdminStatus()

  // 添加全局登录事件监听
  window.addEventListener('user-login', handleUserLogin)

  // 添加头像更新事件监听
  window.addEventListener('avatar-updated', handleAvatarUpdate)
})

// 在组件卸载时清理
onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('user-login', handleUserLogin)
  window.removeEventListener('avatar-updated', handleAvatarUpdate)
})

// 处理退出登录
const handleLogout = async () => {
  try {

    await logout()
    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    // 跳转到首页
    router.push('/')
    ElMessage.success('退出登录成功')
  } catch (error) {
    console.error('退出登录失败:', error)
    // 如果是token无效导致的退出失败，也清除本地存储并跳转
    if (error.message?.includes('Token') || error.message?.includes('登录')) {
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      router.push('/')
      ElMessage.success('已退出登录')
    } else {
      ElMessage.error('退出登录失败')
    }
  }
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'login':
      // 显示登录对话框
      authStore.showLoginDialog({
        tab: 'login'
      })
      break
    case 'register':
      // 显示注册对话框
      authStore.showLoginDialog({
        tab: 'register'
      })
      break
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 导航下拉菜单已移除，此方法不再需要
// const handleNavCommand = (path) => {
//   router.push(path)
// }


</script>

<style scoped>
.header {
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  height: 70px; /* 增加高度 */
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.98);
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.logo a {
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: transform 0.3s;
}

.logo a:hover {
  transform: translateY(-1px);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.school-logo {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
}

.logo a:hover .school-logo {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.2);
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-width: 50px;
}

.system-name {
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.5px;
  transition: color 0.3s;
  margin-left: 8px;
}

.logo a:hover .system-name {
  color: #1890ff;
}

.nav {
  display: flex;
  gap: 1.2rem;
  margin: 0 20px;
}

.nav-item {
  color: #555;
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.5rem 0.6rem;
  position: relative;
  font-weight: 500;
  border-radius: 4px;
}

.nav-item:hover {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.05);
}

.nav-item.active {
  color: #1890ff;
  font-weight: 500;
  background-color: rgba(24, 144, 255, 0.08);
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #1890ff;
  border-radius: 2px;
}

.highlight-item {
  background-color: rgba(24, 144, 255, 0.08);
  border-radius: 20px;
  padding: 6px 12px !important;
  margin: 0 4px;
  position: relative;
  overflow: hidden;
}

.highlight-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.highlight-item.active {
  background-color: rgba(24, 144, 255, 0.15);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.highlight-item:hover {
  background-color: rgba(24, 144, 255, 0.15);
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.25);
}

.nav-item :deep(.el-icon) {
  font-size: 18px;
  margin-right: 6px;
  transition: transform 0.3s;
}

.nav-item:hover :deep(.el-icon) {
  transform: scale(1.1);
  color: #1890ff;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-badge {
  margin-right: 0.5rem;
}

.notification-button {
  font-size: 1.2rem;
  padding: 0.6rem;
  transition: all 0.3s;
  background-color: #f0f2f5;
  border: none;
  color: #505050;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.notification-button:hover {
  transform: scale(1.08);
  color: #1890ff;
  background-color: #e6f7ff;
  box-shadow: 0 3px 6px rgba(24, 144, 255, 0.15);
}

.avatar-container {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.3s;
  margin-left: 0.5rem;
}

.avatar-container:hover {
  background-color: #f5f5f5;
  transform: translateY(-1px);
}

.username {
  color: #666;
  font-size: 0.9rem;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  font-size: 12px;
  color: #999;
  transition: transform 0.3s;
}

.avatar-container:hover .dropdown-icon {
  transform: rotate(180deg);
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 8px 16px;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: #f5f5f5;
}

:deep(.el-icon) {
  margin-right: 4px;
}

:deep(.el-avatar) {
  transition: transform 0.3s;
}

.avatar-container:hover :deep(.el-avatar) {
  transform: scale(1.1);
}

.custom-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50% !important;
  border: 2px solid rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.avatar-container:hover .custom-avatar {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.9) !important;
}

.avatar-text {
  font-weight: 700;
  font-size: 14px;
  letter-spacing: 0;
  text-transform: uppercase;
}

.notification-badge,
.announcement-badge {
  margin-right: 15px;
}

.announcement-button,
.notification-button {
  transition: all 0.3s;
}

.announcement-button:hover,
.notification-button:hover {
  transform: scale(1.1);
  background-color: #f5f7fa;
}

:deep(.el-badge__content) {
  background-color: #f56c6c;
  border: none;
  box-shadow: 0 2px 4px rgba(245, 108, 108, 0.2);
  font-weight: bold;
  font-size: 10px;
}
</style>