package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.service.ServiceManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 服务管理控制器
 * 用于管理系统中的各种服务状态和操作
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/services")
public class ServiceManagementController {

    @Autowired
    private ServiceManagementService serviceManagementService;

    /**
     * 获取智能匹配服务状态
     *
     * @return 服务状态信息
     */
    @GetMapping("/clip/status")
    public Result<Map<String, Object>> getClipServiceStatus() {
        log.info("获取智能匹配服务状态");
        return serviceManagementService.getClipServiceStatus();
    }

    /**
     * 启动智能匹配服务 - 已禁用
     *
     * @return 操作结果
     */
    @PostMapping("/clip/start")
    public Result<String> startClipService() {
        log.info("尝试启动智能匹配服务，但该功能已禁用");
        return Result.fail("服务管理功能已禁用，请通过SSH手动管理服务");
    }

    /**
     * 停止智能匹配服务 - 已禁用
     *
     * @return 操作结果
     */
    @PostMapping("/clip/stop")
    public Result<String> stopClipService() {
        log.info("尝试停止智能匹配服务，但该功能已禁用");
        return Result.fail("服务管理功能已禁用，请通过SSH手动管理服务");
    }

    /**
     * 重启智能匹配服务 - 已禁用
     *
     * @return 操作结果
     */
    @PostMapping("/clip/restart")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result<String> restartClipService() {
        log.info("尝试重启智能匹配服务，但该功能已禁用");
        return Result.fail("服务管理功能已禁用，请通过SSH手动管理服务");
    }

    /**
     * 重建CLIP+FAISS索引
     *
     * @param itemType 物品类型（LOST/FOUND/ALL）
     * @param indexType 索引类型（TEXT/IMAGE/ALL）
     * @return 操作结果
     */
    @PostMapping("/clip/rebuild-index")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result<String> rebuildClipIndex(
            @RequestParam(defaultValue = "ALL") String itemType,
            @RequestParam(defaultValue = "ALL") String indexType) {
        log.info("重建CLIP+FAISS索引: 物品类型={}, 索引类型={}", itemType, indexType);
        return serviceManagementService.rebuildClipIndex(itemType, indexType);
    }

    /**
     * 重新生成所有物品的特征向量
     *
     * @return 操作结果
     */
    @PostMapping("/clip/regenerate-vectors")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result<String> regenerateAllFeatureVectors() {
        log.info("重新生成所有物品的特征向量");
        return serviceManagementService.regenerateAllFeatureVectors();
    }
}
