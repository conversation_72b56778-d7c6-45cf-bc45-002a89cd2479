package com.tomato.lostfoundsystem.entity;

import com.tomato.lostfoundsystem.enums.AuditStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class LostItem {
    private Long id;                // 失物ID
    private Long userId;            // 发布人ID
    private String itemName;        // 物品名称
    private String description;     // 物品描述
    private String lostTime;        // 丢失时间
    private String lostLocation;    // 丢失地点
    private String imageUrl;        // 图片URL
    private String status;          // 状态（LOST/FOUND）
    private LocalDateTime createdAt;       // 创建时间
    private String username;  //发布人
    private String avatar;    //发布人头像
    private AuditStatusEnum auditStatus = AuditStatusEnum.PENDING;  //审核状态

}