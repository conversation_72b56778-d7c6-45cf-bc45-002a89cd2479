package com.tomato.lostfoundsystem.service;

import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;

public interface KafkaConsumerService {
    @KafkaListener(topics = "chat-topic", groupId = "chat-consumer-group")
    void consumeOfflineMessage(
            String message,
            @Header("kafka_receivedPartitionId") int partition,
            @Header("kafka_offset") long offset,
            Acknowledgment acknowledgment);

    @KafkaListener(topics = "chat-read-receipts", groupId = "read-receipt-consumer-group")
    void consumeReadReceipt(
            String message,
            @Header("kafka_receivedPartitionId") int partition,
            @Header("kafka_offset") long offset,
            Acknowledgment acknowledgment);
}
