@startuml 拾物状态流转图

skinparam StateBackgroundColor LightYellow
skinparam StateBorderColor Black
skinparam StateStartColor Green
skinparam StateEndColor Red
skinparam ArrowColor Black

title 拾物状态流转图

[*] --> 待审核 : 用户发布拾物信息
note right: 用户填写物品信息并上传图片

state 待审核 as "待审核\n(PENDING)" #LightBlue
state 已通过 as "已通过\n(APPROVED)" #LightGreen
state 已拒绝 as "已拒绝\n(REJECTED)" #Pink
state 未认领 as "未认领\n(UNCLAIMED)" #Orange
state 已归还 as "已归还\n(RETURNED)" #Green

待审核 --> 已通过 : 管理员审核通过
待审核 --> 已拒绝 : 管理员审核拒绝
note right of 已拒绝 : 可以修改后重新提交

已通过 --> 未认领 : 自动转换
note right of 未认领 : 触发智能匹配

未认领 --> 已归还 : 用户认领\n或管理员更新状态
note right of 已归还 : 可能通过智能匹配\n或线下交接完成

已归还 --> [*]

@enduml
