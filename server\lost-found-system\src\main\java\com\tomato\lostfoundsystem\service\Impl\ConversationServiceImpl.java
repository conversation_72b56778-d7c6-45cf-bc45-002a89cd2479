package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.dto.ContactDTO;
import com.tomato.lostfoundsystem.dto.MessageAttachmentDTO;
import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.entity.Conversation;
import com.tomato.lostfoundsystem.entity.LastMessageInfo;
import com.tomato.lostfoundsystem.entity.MessageAttachment;
import com.tomato.lostfoundsystem.entity.User;
import com.tomato.lostfoundsystem.enums.MessageType;
import com.tomato.lostfoundsystem.mapper.ChatMessageMapper;
import com.tomato.lostfoundsystem.mapper.ConversationMapper;
import com.tomato.lostfoundsystem.mapper.MessageAttachmentMapper;
import com.tomato.lostfoundsystem.mapper.MessageReadStatusMapper;
import com.tomato.lostfoundsystem.mapper.UserMapper;
import com.tomato.lostfoundsystem.service.ConversationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
public class ConversationServiceImpl implements ConversationService {

    @Autowired
    private ConversationMapper conversationMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RedisServiceImpl redisService;  // Redis 服务
    @Autowired
    private ChatMessageMapper messageMapper;    // 消息表操作

    @Autowired
    private MessageAttachmentMapper messageAttachmentMapper; // 消息附件表操作

    @Autowired
    private MessageReadStatusMapper messageReadStatusMapper;  //消息是否已读
    @Override
    @Transactional
    // 创建会话
    public Long createConversation(Long senderId, Long receiverId) {
        log.info("开始创建会话 - 发送者ID: {}, 接收者ID: {}", senderId, receiverId);

        // 检查是否已有会话，若没有则创建新会话
        Conversation existingSession = conversationMapper.getConversation(senderId, receiverId);

        final Long conversationId;
        final boolean isNewConversation;

        if (existingSession == null) {
            log.info("未找到现有会话，创建新会话");
            Conversation newSession = new Conversation();
            newSession.setUser1Id(senderId);
            newSession.setUser2Id(receiverId);
            newSession.setLastMessageTime(new Date());  // 设置会话的最后消息时间为当前时间
            newSession.setUnreadCount(0);  // 初始未读计数为0
            newSession.setStatus("ACTIVE");  // 初始状态为活跃
            newSession.setIsPinned(false);  // 初始不置顶
            newSession.setIsMuted(false);  // 初始不静音
            newSession.setCreatedAt(new Date());  // 设置创建时间
            newSession.setUpdatedAt(new Date());  // 设置更新时间
            conversationMapper.insert(newSession);  // 插入新会话记录

            conversationId = newSession.getId();
            isNewConversation = true;
            log.info("新会话创建成功 - 会话ID: {}", conversationId);
        } else {
            conversationId = existingSession.getId();
            isNewConversation = false;
            log.info("找到现有会话 - 会话ID: {}", conversationId);

            // 如果会话状态为已删除，重新激活
            if ("DELETED".equals(existingSession.getStatus())) {
                log.info("会话状态为已删除，重新激活 - 会话ID: {}", conversationId);
                existingSession.setStatus("ACTIVE");
                existingSession.setUpdatedAt(new Date());
                conversationMapper.update(existingSession);
            }
        }

        // 注册事务同步，在事务提交后执行额外操作
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        log.info("事务提交后的处理 - 会话ID: {}, 是否新会话: {}", conversationId, isNewConversation);

                        // 这里可以添加事务提交后的操作，如发送通知、更新缓存等
                        if (isNewConversation) {
                            // 例如，可以通知用户有新的会话创建
                            log.info("新会话创建通知 - 发送者ID: {}, 接收者ID: {}", senderId, receiverId);
                        }
                    } catch (Exception e) {
                        log.error("事务提交后处理失败: {}", e.getMessage(), e);
                    }
                }
            });
        }

        return conversationId;
    }

    @Override
    @Transactional
    public Conversation getOrCreateConversation(Long user1Id, Long user2Id) {
        log.info("获取或创建会话 - 用户1ID: {}, 用户2ID: {}", user1Id, user2Id);

        Conversation conversation = conversationMapper.getConversation(user1Id, user2Id);

        if (conversation == null) {
            log.info("未找到现有会话，创建新会话");
            conversation = new Conversation();
            conversation.setUser1Id(user1Id);
            conversation.setUser2Id(user2Id);
            conversation.setLastMessageTime(new Date());
            conversation.setUnreadCount(0);
            conversation.setStatus("ACTIVE");
            conversation.setIsPinned(false);
            conversation.setIsMuted(false);
            conversation.setCreatedAt(new Date());
            conversation.setUpdatedAt(new Date());
            conversationMapper.insert(conversation);
            log.info("新会话创建成功 - 会话ID: {}", conversation.getId());
        } else {
            log.info("找到现有会话 - 会话ID: {}", conversation.getId());

            // 如果会话状态为已删除，重新激活
            if ("DELETED".equals(conversation.getStatus())) {
                log.info("会话状态为已删除，重新激活 - 会话ID: {}", conversation.getId());
                conversation.setStatus("ACTIVE");
                conversation.setUpdatedAt(new Date());
                conversationMapper.update(conversation);
            }
        }

        return conversation;
    }

    @Override
    public void updateConversationWithMessage(ChatMessage message) {
        try {
            log.info("更新会话的最后一条消息信息 - 发送者ID: {}, 接收者ID: {}", message.getSenderId(), message.getReceiverId());

            // 获取或创建会话
            Conversation conversation = getOrCreateConversation(message.getSenderId(), message.getReceiverId());

            // 截断消息内容，最多保留100个字符
            String contentSummary = truncateContent(message.getMessage(), 100);

            // 更新会话的最后一条消息信息
            conversationMapper.updateLastMessage(
                conversation.getId(),
                message.getId(),
                contentSummary,
                message.getMessageType().toString(),
                message.getTimestamp()
            );

            log.info("会话最后一条消息信息已更新 - 会话ID: {}, 消息ID: {}", conversation.getId(), message.getId());
        } catch (Exception e) {
            log.error("更新会话最后一条消息信息失败: {}", e.getMessage(), e);
            // 不抛出异常，因为这不应该影响主要功能
        }
    }

    @Override
    public void resetUnreadCount(Long userId, Long contactId) {
        try {
            log.info("重置会话未读计数 - 用户ID: {}, 联系人ID: {}", userId, contactId);
            conversationMapper.resetUnreadCount(userId, contactId);
            log.info("会话未读计数已重置");
        } catch (Exception e) {
            log.error("重置会话未读计数失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void incrementUnreadCount(Long userId, Long contactId) {
        try {
            log.info("增加会话未读计数 - 用户ID: {}, 联系人ID: {}", userId, contactId);
            conversationMapper.incrementUnreadCount(userId, contactId);
            log.info("会话未读计数已增加");
        } catch (Exception e) {
            log.error("增加会话未读计数失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void updateStatus(Long conversationId, String status) {
        try {
            log.info("更新会话状态 - 会话ID: {}, 状态: {}", conversationId, status);
            conversationMapper.updateStatus(conversationId, status);
            log.info("会话状态已更新");
        } catch (Exception e) {
            log.error("更新会话状态失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void updatePinned(Long conversationId, Boolean isPinned) {
        try {
            log.info("更新会话置顶状态 - 会话ID: {}, 是否置顶: {}", conversationId, isPinned);
            conversationMapper.updatePinned(conversationId, isPinned);
            log.info("会话置顶状态已更新");
        } catch (Exception e) {
            log.error("更新会话置顶状态失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void updateMuted(Long conversationId, Boolean isMuted) {
        try {
            log.info("更新会话静音状态 - 会话ID: {}, 是否静音: {}", conversationId, isMuted);
            conversationMapper.updateMuted(conversationId, isMuted);
            log.info("会话静音状态已更新");
        } catch (Exception e) {
            log.error("更新会话静音状态失败: {}", e.getMessage(), e);
        }
    }

    // 截断内容，确保不超过指定长度
    private String truncateContent(String content, int maxLength) {
        if (content == null) {
            return "";
        }

        if (content.length() <= maxLength) {
            return content;
        }

        return content.substring(0, maxLength) + "...";
    }

    @Override
    public List<ContactDTO> getContacts(Long userId) {
        log.info("开始获取用户ID: {} 的联系人列表", userId);
        List<ContactDTO> contacts = new ArrayList<>();

        try {
            // 直接获取用户的所有会话
            log.debug("查询用户 {} 的所有会话", userId);
            List<Conversation> conversations = conversationMapper.getConversationsByUserId(userId);

            if (conversations == null || conversations.isEmpty()) {
                log.info("用户 {} 没有任何会话", userId);
                return contacts; // 返回空列表
            }

            log.debug("用户 {} 共有 {} 个会话", userId, conversations.size());

            for (Conversation conversation : conversations) {
                try {
                    // 确定联系人ID
                    Long contactId = conversation.getUser1Id().equals(userId) ?
                                    conversation.getUser2Id() : conversation.getUser1Id();

                    log.debug("开始处理联系人ID: {}", contactId);
                    User contact = userMapper.findById(contactId);  // 获取联系人基本信息

                    if (contact == null) {
                        log.warn("联系人ID: {} 在用户表中不存在，跳过处理", contactId);
                        continue; // 跳过不存在的用户
                    }

                    ContactDTO contactDTO = new ContactDTO();
                    contactDTO.setContactId(contact.getId());
                    log.info("处理联系人: ID={}, 用户名={}", contactId, contact.getUsername());
                    contactDTO.setName(contact.getUsername());

                    // 设置联系人头像URL
                    contactDTO.setAvatar(contact.getAvatar());
                    log.debug("设置联系人 {} 的头像URL: {}", contactId, contact.getAvatar());

                    // 安全获取用户名首字母
                    String username = contact.getUsername();
                    if (username != null && !username.isEmpty()) {
                        contactDTO.setAvatarText(username.substring(0, 1).toUpperCase());
                    } else {
                        contactDTO.setAvatarText("U"); // 默认值
                        log.warn("联系人 {} 的用户名为空，使用默认头像文本", contactId);
                    }

                    // 优先从Redis获取最后一条消息信息
                    Map<String, String> lastMessageFromRedis = redisService.getLastMessage(userId, contactId);

                    if (lastMessageFromRedis != null && !lastMessageFromRedis.isEmpty()) {
                        // 从Redis获取最后一条消息信息
                        log.debug("从Redis获取最后一条消息信息 - 用户ID: {}, 联系人ID: {}", userId, contactId);

                        // 设置消息内容
                        contactDTO.setLastMessage(lastMessageFromRedis.get("content"));

                        // 设置消息时间
                        try {
                            long timestamp = Long.parseLong(lastMessageFromRedis.get("timestamp"));
                            contactDTO.setLastTime(formatDate(new Date(timestamp)));
                        } catch (Exception e) {
                            log.warn("解析Redis中的消息时间戳失败: {}", e.getMessage());
                            contactDTO.setLastTime(formatDate(conversation.getLastMessageTime()));
                        }

                        // 设置消息类型
                        try {
                            String messageTypeStr = lastMessageFromRedis.get("messageType");
                            if (messageTypeStr != null) {
                                MessageType messageType = MessageType.valueOf(messageTypeStr);
                                contactDTO.setMessageType(messageType);
                            } else {
                                contactDTO.setMessageType(MessageType.TEXT);
                            }
                        } catch (IllegalArgumentException e) {
                            log.warn("无效的消息类型: {}, 使用默认类型TEXT", lastMessageFromRedis.get("messageType"));
                            contactDTO.setMessageType(MessageType.TEXT);
                        }

                        // 设置文件URL（如果有）
                        String fileUrl = lastMessageFromRedis.get("fileUrl");
                        MessageType messageType = contactDTO.getMessageType();

                        // 只有非TEXT类型的消息才处理文件附件
                        if (fileUrl != null && messageType != MessageType.TEXT) {
                            MessageAttachmentDTO attachmentDTO = new MessageAttachmentDTO();
                            attachmentDTO.setFileUrl(fileUrl);

                            // 根据消息类型推断文件类型
                            try {
                                attachmentDTO.setFileType(messageType.getFileType());

                                contactDTO.setMessageAttachmentDTO(attachmentDTO);

                                // 创建附件列表
                                List<MessageAttachmentDTO> attachmentDTOs = new ArrayList<>();
                                attachmentDTOs.add(attachmentDTO);
                                contactDTO.setMessageAttachments(attachmentDTOs);

                                log.debug("从Redis获取到文件URL: {}", fileUrl);
                            } catch (IllegalArgumentException e) {
                                log.warn("无法获取消息类型 {} 的文件类型: {}", messageType, e.getMessage());
                                // 对于不支持的消息类型，不设置文件附件
                            }
                        }
                    } else {
                        // 从数据库获取最后一条消息信息
                        log.debug("Redis中没有最后一条消息信息，从数据库获取 - 用户ID: {}, 联系人ID: {}", userId, contactId);

                        if (conversation.getLastMessageContent() != null) {
                            contactDTO.setLastMessage(conversation.getLastMessageContent());
                            contactDTO.setLastTime(formatDate(conversation.getLastMessageTime()));

                            // 设置消息类型
                            if (conversation.getLastMessageType() != null) {
                                try {
                                    MessageType messageType = MessageType.valueOf(conversation.getLastMessageType());
                                    contactDTO.setMessageType(messageType);
                                } catch (IllegalArgumentException e) {
                                    log.warn("无效的消息类型: {}, 使用默认类型TEXT", conversation.getLastMessageType());
                                    contactDTO.setMessageType(MessageType.TEXT);
                                }
                            } else {
                                contactDTO.setMessageType(MessageType.TEXT);
                            }

                            // 如果需要获取附件信息，可以通过lastMessageId查询
                            if (conversation.getLastMessageId() != null) {
                                try {
                                    // 获取所有附件
                                    List<MessageAttachment> attachments = messageAttachmentMapper.getAllAttachmentsByMessageId(conversation.getLastMessageId());

                                    if (attachments != null && !attachments.isEmpty()) {
                                        // 创建附件DTO列表
                                        List<MessageAttachmentDTO> attachmentDTOs = new ArrayList<>();

                                        for (MessageAttachment attachment : attachments) {
                                            MessageAttachmentDTO attachmentDTO = new MessageAttachmentDTO();
                                            attachmentDTO.setFileUrl(attachment.getFileUrl());
                                            attachmentDTO.setFileType(attachment.getFileType());
                                            attachmentDTO.setFileSize(attachment.getFileSize());
                                            attachmentDTOs.add(attachmentDTO);
                                        }

                                        // 设置第一个附件为主附件（向后兼容）
                                        contactDTO.setMessageAttachmentDTO(attachmentDTOs.get(0));

                                        // 设置所有附件
                                        contactDTO.setMessageAttachments(attachmentDTOs);

                                        log.info("获取到消息 {} 的 {} 个附件", conversation.getLastMessageId(), attachments.size());

                                        // 同步到Redis
                                        redisService.storeLastMessage(
                                            userId,
                                            contactId,
                                            conversation.getLastMessageId(),
                                            conversation.getLastMessageContent(),
                                            conversation.getLastMessageType(),
                                            conversation.getLastMessageTime().getTime(),
                                            attachmentDTOs.get(0).getFileUrl()
                                        );
                                        log.debug("已同步最后一条消息信息到Redis");
                                    }
                                } catch (Exception e) {
                                    log.warn("获取最后一条消息的附件信息失败: {}", e.getMessage());
                                }
                            } else {
                                // 同步到Redis（无附件）
                                redisService.storeLastMessage(
                                    userId,
                                    contactId,
                                    conversation.getLastMessageId(),
                                    conversation.getLastMessageContent(),
                                    conversation.getLastMessageType(),
                                    conversation.getLastMessageTime().getTime(),
                                    null
                                );
                                log.debug("已同步最后一条消息信息到Redis（无附件）");
                            }
                        } else {
                            // 如果没有最后一条消息信息，设置默认值
                            contactDTO.setLastMessage("暂无消息");
                            contactDTO.setLastTime("");
                            contactDTO.setMessageType(MessageType.TEXT);
                        }
                    }

                    // 设置未读消息数量 - 优先从Redis获取，如果Redis中没有则使用数据库中的值
                    int unreadCount = redisService.getUnreadCount(userId, contactId);
                    if (unreadCount > 0) {
                        // 使用Redis中的未读计数
                        contactDTO.setUnreadCount(unreadCount);
                        log.debug("从Redis获取未读计数 - 用户ID: {}, 联系人ID: {}, 未读计数: {}", userId, contactId, unreadCount);
                    } else {
                        // 使用数据库中的未读计数
                        contactDTO.setUnreadCount(conversation.getUnreadCount() != null ? conversation.getUnreadCount() : 0);
                        log.debug("从数据库获取未读计数 - 用户ID: {}, 联系人ID: {}, 未读计数: {}",
                                userId, contactId, conversation.getUnreadCount());

                        // 同步Redis和数据库的未读计数
                        if (conversation.getUnreadCount() != null && conversation.getUnreadCount() > 0) {
                            redisService.setUnreadCount(userId, contactId, conversation.getUnreadCount());
                            log.debug("同步未读计数到Redis - 用户ID: {}, 联系人ID: {}, 未读计数: {}",
                                    userId, contactId, conversation.getUnreadCount());
                        }
                    }

                    contacts.add(contactDTO);
                    log.debug("联系人 {} 处理完成并添加到列表", contactId);
                } catch (Exception e) {
                    log.error("处理会话时发生异常: {}", e.getMessage(), e);
                    // 继续处理下一个会话，不影响整体返回
                }
            }

            log.info("用户 {} 的联系人列表获取完成，共处理 {} 个联系人", userId, contacts.size());
            return contacts;

        } catch (Exception e) {
            log.error("获取用户 {} 联系人列表时发生异常: {}", userId, e.getMessage(), e);
            throw e; // 重新抛出异常，由调用方处理
        }
    }

    // 格式化日期
    private String formatDate(Date date) {
        if (date == null) {
            return "";
        }

        try {
            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
        } catch (Exception e) {
            log.error("格式化日期失败: {}", e.getMessage());
            return "";
        }
    }

    // 获取最后一条消息
    public LastMessageInfo getLastMessageInfo(Long userId, Long contactId) {
        try {
            log.debug("查询用户 {} 和联系人 {} 之间的最后一条消息信息", userId, contactId);
            LastMessageInfo lastMessageInfo = messageMapper.getLastMessageInfo(userId, contactId);

            // 添加详细日志，输出获取到的消息内容
            if (lastMessageInfo != null) {
                log.info("获取到最后一条消息: LastMessageInfo={}", lastMessageInfo);

                if (lastMessageInfo.getChatMessage() != null) {
                    log.info("消息内容: id={}, 发送者={}, 接收者={}, 内容={}, 类型={}, 时间={}",
                        lastMessageInfo.getChatMessage().getId(),
                        lastMessageInfo.getChatMessage().getSenderId(),
                        lastMessageInfo.getChatMessage().getReceiverId(),
                        lastMessageInfo.getChatMessage().getMessage(),
                        lastMessageInfo.getChatMessage().getMessageType(),
                        lastMessageInfo.getChatMessage().getTimestamp());

                    if (lastMessageInfo.getChatMessage().getAttachment() != null) {
                        log.info("附件信息: url={}, 类型={}, 大小={}",
                            lastMessageInfo.getChatMessage().getAttachment().getFileUrl(),
                            lastMessageInfo.getChatMessage().getAttachment().getFileType(),
                            lastMessageInfo.getChatMessage().getAttachment().getFileSize());
                    }
                } else {
                    log.warn("获取到的LastMessageInfo中chatMessage为null");
                }
            } else {
                log.warn("未找到用户{}和联系人{}之间的最后一条消息", userId, contactId);
            }

            return lastMessageInfo;
        } catch (Exception e) {
            log.error("获取最后一条消息信息失败: {}", e.getMessage(), e);
            return null; // 返回null而不是抛出异常
        }
    }

    // 获取最后一条消息的时间
    private String getLastMessageTime(Long userId, Long contactId) {
        try {
            log.debug("查询用户 {} 和联系人 {} 之间的最后一条消息时间", userId, contactId);
            String time = conversationMapper.getLastMessageTime(userId, contactId);
            return time != null ? time : ""; // 如果为null则返回空字符串
        } catch (Exception e) {
            log.error("获取最后一条消息时间失败: {}", e.getMessage(), e);
            return ""; // 发生异常时返回空字符串
        }
    }

    // 获取未读消息数量 - 优先从Redis获取，如果Redis中没有则查询数据库
    private int getUnreadCount(Long userId, Long contactId) {
        try {
            log.debug("查询用户 {} 收到来自联系人 {} 的未读消息数量", userId, contactId);

            // 优先从Redis获取未读计数
            int redisUnreadCount = redisService.getUnreadCount(userId, contactId);
            if (redisUnreadCount > 0) {
                log.debug("从Redis获取未读计数 - 用户ID: {}, 联系人ID: {}, 未读计数: {}", userId, contactId, redisUnreadCount);
                return redisUnreadCount;
            }

            // 如果Redis中没有，则查询数据库
            int dbUnreadCount = messageReadStatusMapper.countUnreadMessages(userId, contactId);
            log.debug("从数据库获取未读计数 - 用户ID: {}, 联系人ID: {}, 未读计数: {}", userId, contactId, dbUnreadCount);

            // 如果数据库中有未读消息，同步到Redis
            if (dbUnreadCount > 0) {
                redisService.setUnreadCount(userId, contactId, dbUnreadCount);
                log.debug("同步未读计数到Redis - 用户ID: {}, 联系人ID: {}, 未读计数: {}", userId, contactId, dbUnreadCount);
            }

            return dbUnreadCount;
        } catch (Exception e) {
            log.error("获取未读消息数量失败: {}", e.getMessage(), e);
            return 0; // 发生异常时返回0
        }
    }
}
