import request from '@/utils/request'

// 获取通知列表
export function getNotifications(params) {
  return request({
    url: '/notifications',
    method: 'get',
    params
  })
}

// 获取未读通知数量
export function getUnreadCount() {
  return request({
    url: '/notifications/unread/count',
    method: 'get'
  })
}

// 标记通知为已读
export function markNotificationAsRead(notificationId) {
  return request({
    url: '/notifications/mark-as-read',
    method: 'post',
    params: { notificationId }
  })
}

// 删除通知
export function deleteNotification(notificationId) {
  return request({
    url: '/notifications/delete',
    method: 'delete',
    params: { notificationId }
  })
}

/**
 * 匹配通知相关API
 */

// 获取匹配通知列表
export function getMatchNotifications() {
  return request({
    url: '/notifications/match/list',
    method: 'get'
  })
}

// 标记匹配通知为已读
export function markMatchNotificationAsRead(notificationId) {
  return request({
    url: `/notifications/match/${notificationId}/read`,
    method: 'put'
  })
}

// 获取未读匹配通知数量
export function getUnreadMatchNotificationCount() {
  return request({
    url: '/notifications/match/unread-count',
    method: 'get'
  })
}

// 删除匹配通知
export function deleteMatchNotification(notificationId) {
  return request({
    url: `/notifications/match/${notificationId}`,
    method: 'delete'
  })
}