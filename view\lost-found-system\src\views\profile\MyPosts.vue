<template>
  <div class="my-posts">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="我的失物" name="lost">
        <el-table
          v-loading="loading"
          :data="lostItems"
          style="width: 100%"
        >
          <el-table-column label="图片" width="120">
            <template #default="{ row }">
              <el-image
                class="item-image"
                :src="row.imageUrl || 'default-image.jpg'"
                fit="cover"
                :preview-src-list="row.imageUrl ? [row.imageUrl] : []"
                :initial-index="0"
                preview-teleported
              >
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="itemName" label="物品名称" min-width="120" />
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="lostLocation" label="丢失地点" min-width="120" />
          <el-table-column prop="lostTime" label="丢失时间" min-width="160" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ row.status === 'LOST' ? '未找回' : '已找回' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="auditStatus" label="审核状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getAuditStatusType(row.auditStatus)">
                {{ getAuditStatusText(row.auditStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                @click="router.push(`/lost-items/detail/${row.id}`)"
              >
                <el-tooltip content="查看详情" placement="top">
                  <el-icon><View /></el-icon>
                </el-tooltip>
              </el-button>
              <el-button
                v-if="row.status === 'LOST' && row.auditStatus !== 'PENDING'"
                type="primary"
                link
                @click="router.push(`/lost-items/edit/${row.id}`)"
              >
                <el-tooltip content="编辑" placement="top">
                  <el-icon><Edit /></el-icon>
                </el-tooltip>
              </el-button>
              <el-button
                v-if="row.auditStatus === 'REJECTED'"
                type="success"
                link
                @click="handleResubmit(row, 'lost')"
              >
                <el-tooltip content="重新提交" placement="top">
                  <el-icon><RefreshRight /></el-icon>
                </el-tooltip>
              </el-button>
              <el-button
                v-if="row.status === 'LOST' && row.auditStatus === 'APPROVED'"
                type="success"
                link
                @click="handleUpdateLostStatus(row, 'FOUND')"
              >
                <el-tooltip content="确认找回" placement="top">
                  <el-icon><Check /></el-icon>
                </el-tooltip>
              </el-button>
              <el-button
                v-if="row.status === 'LOST'"
                type="danger"
                link
                @click="handleDeleteLost(row)"
              >
                <el-tooltip content="删除" placement="top">
                  <el-icon><Delete /></el-icon>
                </el-tooltip>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="我的拾物" name="found">
        <el-table
          v-loading="loading"
          :data="foundItems"
          style="width: 100%"
        >
          <el-table-column label="图片" width="120">
            <template #default="{ row }">
              <el-image
                class="item-image"
                :src="row.imageUrl || 'default-image.jpg'"
                fit="cover"
                :preview-src-list="row.imageUrl ? [row.imageUrl] : []"
                :initial-index="0"
                preview-teleported
              >
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="itemName" label="物品名称" min-width="120" />
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="foundLocation" label="拾取地点" min-width="120" />
          <el-table-column prop="foundTime" label="拾取时间" min-width="160" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ row.status === 'UNCLAIMED' ? '未认领' : '已认领' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="auditStatus" label="审核状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getAuditStatusType(row.auditStatus)">
                {{ getAuditStatusText(row.auditStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                @click="router.push(`/found-items/detail/${row.id}`)"
              >
                <el-tooltip content="查看详情" placement="top">
                  <el-icon><View /></el-icon>
                </el-tooltip>
              </el-button>
              <el-button
                v-if="row.status === 'UNCLAIMED' && row.auditStatus !== 'PENDING'"
                type="primary"
                link
                @click="router.push(`/found-items/edit/${row.id}`)"
              >
                <el-tooltip content="编辑" placement="top">
                  <el-icon><Edit /></el-icon>
                </el-tooltip>
              </el-button>
              <el-button
                v-if="row.auditStatus === 'REJECTED'"
                type="success"
                link
                @click="handleResubmit(row, 'found')"
              >
                <el-tooltip content="重新提交" placement="top">
                  <el-icon><RefreshRight /></el-icon>
                </el-tooltip>
              </el-button>
              <el-button
                v-if="row.status === 'UNCLAIMED' && row.auditStatus === 'APPROVED'"
                type="success"
                link
                @click="handleUpdateFoundStatus(row, 'RETURNED')"
              >
                <el-tooltip content="确认归还" placement="top">
                  <el-icon><Check /></el-icon>
                </el-tooltip>
              </el-button>
              <el-button
                v-if="row.status === 'UNCLAIMED'"
                type="danger"
                link
                @click="handleDeleteFound(row)"
              >
                <el-tooltip content="删除" placement="top">
                  <el-icon><Delete /></el-icon>
                </el-tooltip>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture, View, Edit, Delete, RefreshRight, Check } from '@element-plus/icons-vue'
import { deleteLostItem, updateLostItemStatus } from '@/api/lost'
import { deleteFoundItem, updateFoundItemStatus } from '@/api/found'
import request from '@/utils/request'

const router = useRouter()
const activeTab = ref('lost')
const loading = ref(false)
const lostItems = ref([])
const foundItems = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 存储完整数据
const allLostItems = ref([])
const allFoundItems = ref([])

// 获取我发布的失物列表
const fetchMyLostItems = async () => {
  try {
    loading.value = true
    const res = await request({
      url: '/lost-items/my-published/lost',
      method: 'get'
    })

    if (res.code === 200) {
      // 保存所有数据
      allLostItems.value = Array.isArray(res.data) ? res.data : []
      total.value = allLostItems.value.length
      // 更新当前页显示的数据
      updateLostItemsList()
      console.log('获取到的失物列表：', allLostItems.value)
    } else {
      ElMessage.error(res.message || '获取失物列表失败')
    }
  } catch (error) {
    console.error('获取失物列表失败：', error)
    ElMessage.error('获取失物列表失败')
  } finally {
    loading.value = false
  }
}

// 获取我发布的拾物列表
const fetchMyFoundItems = async () => {
  try {
    loading.value = true
    const res = await request({
      url: '/found-items/my-published/found',
      method: 'get'
    })

    console.log('拾物接口返回的原始数据：', res)

    if (res.code === 200) {
      // 保存所有数据
      allFoundItems.value = Array.isArray(res.data) ? res.data : []
      total.value = allFoundItems.value.length
      // 更新当前页显示的数据
      updateFoundItemsList()
      console.log('处理后的拾物列表：', {
        all: allFoundItems.value,
        current: foundItems.value,
        total: total.value,
        currentPage: currentPage.value,
        pageSize: pageSize.value
      })
    } else {
      ElMessage.error(res.message || '获取拾物列表失败')
    }
  } catch (error) {
    console.error('获取拾物列表失败：', error)
    ElMessage.error('获取拾物列表失败')
  } finally {
    loading.value = false
  }
}

// 更新失物列表分页数据
const updateLostItemsList = () => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  lostItems.value = allLostItems.value.slice(start, end)
}

// 更新拾物列表分页数据
const updateFoundItemsList = () => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  foundItems.value = allFoundItems.value.slice(start, end)
  console.log('更新拾物分页数据：', {
    start,
    end,
    pageData: foundItems.value,
    allData: allFoundItems.value
  })
}

// 处理标签页切换
const handleTabClick = () => {
  currentPage.value = 1
  if (activeTab.value === 'lost') {
    total.value = allLostItems.value.length
    updateLostItemsList()
  } else {
    total.value = allFoundItems.value.length
    updateFoundItemsList()
    console.log('切换到拾物标签页：', {
      activeTab: activeTab.value,
      total: total.value,
      currentPage: currentPage.value
    })
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  if (activeTab.value === 'lost') {
    updateLostItemsList()
  } else {
    updateFoundItemsList()
  }
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  if (activeTab.value === 'lost') {
    updateLostItemsList()
  } else {
    updateFoundItemsList()
  }
}

// 获取状态类型
const getStatusType = (status) => {
  if (activeTab.value === 'lost') {
    return status === 'LOST' ? 'danger' : 'success'
  } else {
    return status === 'UNCLAIMED' ? 'warning' : 'success'
  }
}

// 获取审核状态类型
const getAuditStatusType = (status) => {
  if (!status) return 'info'

  // 确保status是字符串类型
  const upperStatus = String(status).toUpperCase()

  // 处理可能的状态值
  if (upperStatus.includes('PEND') || upperStatus === 'WAITING') {
    return 'warning'
  } else if (upperStatus.includes('APPROV') || upperStatus === 'PASS') {
    return 'success'
  } else if (upperStatus.includes('REJECT') || upperStatus === 'DENY' || upperStatus === 'FAIL') {
    return 'danger'
  } else {
    console.log('未知的审核状态:', status)
    return 'info'
  }
}

// 获取审核状态文本
const getAuditStatusText = (status) => {
  if (!status) return '未知'

  // 确保status是字符串类型
  const upperStatus = String(status).toUpperCase()

  // 处理可能的状态值
  if (upperStatus.includes('PEND') || upperStatus === 'WAITING') {
    return '审核中'
  } else if (upperStatus.includes('APPROV') || upperStatus === 'PASS') {
    return '已通过'
  } else if (upperStatus.includes('REJECT') || upperStatus === 'DENY' || upperStatus === 'FAIL') {
    return '已拒绝'
  } else {
    console.log('未知的审核状态:', status)
    return String(status) // 直接显示状态值，方便调试
  }
}

// 处理删除失物
const handleDeleteLost = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条失物信息吗？此操作不可恢复',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteLostItem(row.id)
    ElMessage.success('删除成功')
    fetchMyLostItems()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + (error.message || '未知错误'))
    }
  }
}

// 处理删除拾物
const handleDeleteFound = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条拾物信息吗？此操作不可恢复',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteFoundItem(row.id)
    ElMessage.success('删除成功')
    fetchMyFoundItems()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + (error.message || '未知错误'))
    }
  }
}

// 处理重新提交
const handleResubmit = (row, type) => {
  if (type === 'lost') {
    router.push({
      path: `/lost-items/edit/${row.id}`,
      query: { resubmit: true }
    })
  } else {
    router.push({
      path: `/found-items/edit/${row.id}`,
      query: { resubmit: true }
    })
  }
}

// 处理失物状态更新 - 确认找回
const handleUpdateLostStatus = async (row, status) => {
  try {
    // 只允许从未找回(LOST)变为已找回(FOUND)
    if (row.status !== 'LOST' || status !== 'FOUND') {
      ElMessage.warning('只能将未找回的物品标记为已找回')
      return
    }

    await ElMessageBox.confirm(
      '确认已找回该物品？此操作不可撤销。',
      '确认找回',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    const res = await updateLostItemStatus(row.id, status)
    if (res.code === 200) {
      ElMessage.success('物品已标记为找回')
      // 更新本地数据
      row.status = status
    } else {
      ElMessage.error(res.message || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认找回失败：', error)
      ElMessage.error('确认找回失败：' + (error.message || '未知错误'))
    }
  }
}

// 处理拾物状态更新 - 确认归还
const handleUpdateFoundStatus = async (row, status) => {
  try {
    // 只允许从未认领(UNCLAIMED)变为已归还(RETURNED)
    if (row.status !== 'UNCLAIMED' || status !== 'RETURNED') {
      ElMessage.warning('只能将未认领的物品标记为已归还')
      return
    }

    await ElMessageBox.confirm(
      '确认该物品已被归还？此操作不可撤销。',
      '确认归还',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    const res = await updateFoundItemStatus(row.id, status)
    if (res.code === 200) {
      ElMessage.success('物品已标记为归还')
      // 更新本地数据
      row.status = status
    } else {
      ElMessage.error(res.message || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认归还失败：', error)
      ElMessage.error('确认归还失败：' + (error.message || '未知错误'))
    }
  }
}

onMounted(() => {
  // 初始化时获取失物列表
  fetchMyLostItems()
  // 同时获取拾物列表
  fetchMyFoundItems()
})
</script>

<style scoped>
.my-posts {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.item-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
}

.image-placeholder {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-table) {
  margin-top: 16px;
}


</style>