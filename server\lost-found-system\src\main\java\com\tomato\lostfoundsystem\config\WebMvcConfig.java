package com.tomato.lostfoundsystem.config;


import com.tomato.lostfoundsystem.interceptors.LoginVerifyInterceptor;
import com.tomato.lostfoundsystem.interceptors.TokenInterceptor;
import com.tomato.lostfoundsystem.interceptors.VerifyCodeInterceptor;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private LoginVerifyInterceptor loginVerifyInterceptor;

    @Autowired
    private VerifyCodeInterceptor verifyCodeInterceptor;

    @Autowired
    private TokenInterceptor tokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册请求拦截
        registry.addInterceptor(verifyCodeInterceptor)
                .addPathPatterns("/api/user/register");

        // 登录请求拦截
        registry.addInterceptor(loginVerifyInterceptor)
                .addPathPatterns("/api/user/login");

    }


}
