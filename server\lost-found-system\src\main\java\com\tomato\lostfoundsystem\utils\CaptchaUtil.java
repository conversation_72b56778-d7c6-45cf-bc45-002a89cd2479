package com.tomato.lostfoundsystem.utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;

public class CaptchaUtil {

    private static final int WIDTH = 150;   // 图像宽度
    private static final int HEIGHT = 50;   // 图像高度
    private static final String[] OPERATORS = {"+", "-", "*", "/"}; // 运算符数组


    // 将图像数据转换为 Base64 编码的字符串
    public static String encodeImageToBase64(ByteArrayOutputStream outputStream) {
        return Base64.getEncoder().encodeToString(outputStream.toByteArray());
    }

    // 生成图形验证码
    public static ByteArrayOutputStream generateCaptchaImage(String question) throws IOException {
        // 创建 BufferedImage 对象
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();

        // 填充背景颜色为白色
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, WIDTH, HEIGHT);

        // 添加一些干扰线条
        addNoise(g, WIDTH, HEIGHT);

        // 设置字体样式和颜色
        g.setFont(new Font("Arial", Font.BOLD, 40));
        g.setColor(Color.BLACK);

//        // 随机旋转文本
//        Random rand = new Random();
//        g.rotate(rand.nextInt(10) - 5, WIDTH / 2, HEIGHT / 2);  // 随机旋转文本

        // 绘制算数题
        g.drawString(question, 10, 40);

        // 释放资源
        g.dispose();

        // 将图像数据写入到 ByteArrayOutputStream 中
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(image, "png", outputStream);  // 将图像转换为 PNG 格式
        return outputStream;
    }

    // 生成加减乘除的数学题，确保除数不为0
    public static String generateCaptchaQuestion() {
        Random random = new Random();
        int num1 = random.nextInt(10) + 1;  // 生成 1 到 10 之间的随机数
        int num2 = random.nextInt(10) + 1;  // 生成 1 到 10 之间的随机数
        String operator = OPERATORS[random.nextInt(OPERATORS.length)];

        // 确保除法不会出现除数为 0 的情况
        if (operator.equals("/")) {
            while (num2 == 0) {
                num2 = random.nextInt(10) + 1;  // 重新生成一个非零除数
            }
        }

        // 返回包含 = ? 的算数题
        return num1 + " " + operator + " " + num2 + " = ?";  // 这里添加了 = ?
    }

    // 计算数学题的答案，增加错误处理
    public static int calculateAnswer(String question) {
        String[] parts = question.split(" ");
        int num1 = Integer.parseInt(parts[0]);
        int num2 = Integer.parseInt(parts[2]);
        String operator = parts[1];

        // 根据运算符计算结果
        try {
            switch (operator) {
                case "+":
                    return num1 + num2;
                case "-":
                    return num1 - num2;
                case "*":
                    return num1 * num2;
                case "/":
                    // 除法计算，确保除数不为0
                    if (num2 == 0) {
                        throw new ArithmeticException("除数不能为零");
                    }
                    return num1 / num2;
                default:
                    throw new IllegalArgumentException("不支持的运算符：" + operator);
            }
        } catch (ArithmeticException e) {
            // 如果出现除数为零的情况，返回默认错误值
            System.err.println("错误: " + e.getMessage());
            return 0;  // 返回 0 或者其他适当的错误值
        } catch (IllegalArgumentException e) {
            // 如果是非法运算符，打印错误信息
            System.err.println("错误: " + e.getMessage());
            return 0;
        }
    }

    // 添加干扰线条
    private static void addNoise(Graphics2D g, int width, int height) {
        Random rand = new Random();
        g.setColor(Color.GRAY);
        for (int i = 0; i < 5; i++) {
            int x1 = rand.nextInt(width);
            int y1 = rand.nextInt(height);
            int x2 = rand.nextInt(width);
            int y2 = rand.nextInt(height);
            g.drawLine(x1, y1, x2, y2);
        }
    }

}
