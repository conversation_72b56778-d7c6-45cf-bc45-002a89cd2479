/**
 * WebSocket 核心管理模块 (优化版)
 *
 * 该模块负责：
 * 1. WebSocket 连接的建立和管理
 * 2. STOMP 客户端的初始化和配置
 * 3. 消息的发送和接收
 * 4. 心跳机制的实现
 * 5. 重连逻辑
 * 6. 在线状态管理
 *
 * 使用方式：
 * import { initWebSocketClient, sendChatMessage, isWebSocketConnected } from '@/utils/websocket'
 */

import SockJS from 'sockjs-client/dist/sockjs.min.js'
import { Client } from '@stomp/stompjs'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'
import { setConnectionStatus } from './connection-status'
import logger from './logger'

// 创建WebSocket专用日志记录器
const wsLogger = logger.createLogger('WebSocket')

// 添加全局对象兼容性处理
if (typeof global === 'undefined') {
  window.global = window
}

// 在线用户状态缓存
const onlineUsersCache = new Map()

// SockJS配置
const sockjsOptions = {
  // 使用 xhr_streaming 传输方式，这是已知可以成功的方式
  transports: ['xhr_streaming'],
  timeout: 20000
}

/**
 * 获取WebSocket URL (通过URL参数传递token)
 *
 * 确保无论使用哪种传输协议，token都能被后端获取到
 */
function getWebSocketUrl(path, token) {
  // 使用API基础URL，确保连接到后端服务器而不是前端开发服务器
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081';

  // 添加时间戳防止缓存
  const timestamp = new Date().getTime()

  // 确保路径以 / 开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`

  // 构建完整的URL，通过URL参数传递token
  // 这样无论使用哪种传输协议，后端都能通过HandshakeInterceptor获取token
  const url = `${apiBaseUrl}${normalizedPath}?token=${encodeURIComponent(token)}&t=${timestamp}`

  console.log(`创建WebSocket连接URL: ${apiBaseUrl}${normalizedPath}?token=***&t=${timestamp}`)
  return url
}



class WebSocketClient {
  constructor() {
    this.stompClient = null
    this.notificationClient = null
    this.subscriptions = []
    this.notificationSubscriptions = []
    this.connectionPending = false
    this.reconnectAttempts = 0
    this.messageHandlers = new Map()
    this.autoReconnect = true
    this.initialized = false
    this.baseUrl = ''
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 3000
    this.heartbeatTimer = null
  }

  // 新增：初始化方法
  init() {
    if (this.initialized) {
      wsLogger.info('WebSocket客户端已经初始化')
      return
    }

    const token = localStorage.getItem('token')
    if (!token) {
      wsLogger.warn('未找到token，WebSocket初始化失败')
      return
    }

    this.initialized = true
    this.connect()

    // 添加网络状态监听
    window.addEventListener('online', () => {
      wsLogger.info('网络已恢复')
      if (!this.stompClient?.connected) {
        this.connect()
      }
    })

    window.addEventListener('offline', () => {
      wsLogger.warn('网络已断开')
      this.emit('networkOffline')
    })
  }

  async connect(useFallbackTransport = false) {
    const userStore = useUserStore()
    const token = userStore.token

    if (!token) {
      wsLogger.warn('用户未登录，取消WebSocket连接')
      return
    }

    if (this.connectionPending) {
      wsLogger.warn('WebSocket连接正在进行中')
      return
    }

    this.connectionPending = true
    wsLogger.info('==== WebSocket连接开始 ====')

    // 不再发送预认证请求，直接通过URL参数传递token

    // 移除首选传输方式限制，让SockJS自动选择最佳传输方式
    const transportOptions = {
      // 不再限制传输方式
      timeout: 20000
    }

    wsLogger.debug('传输方式: 自动选择')
    wsLogger.debug('超时设置:', transportOptions.timeout)

    try {
      // 使用带token的URL
      // 使用基础路径，SockJS会自动处理/info请求
      const chatUrl = getWebSocketUrl('/ws/chat', token)
      const notificationUrl = getWebSocketUrl('/ws/notifications', token)

      console.log('聊天WebSocket URL:', chatUrl.replace(token, '***'))
      console.log('通知WebSocket URL:', notificationUrl.replace(token, '***'))

      // 简化STOMP连接头，只保留标准Authorization头
      const connectHeaders = {
        'Authorization': `Bearer ${token}`  // 标准Authorization头
      }

      console.log('准备连接到聊天WebSocket...')
      console.log('STOMP连接头:', JSON.stringify({
        Authorization: 'Bearer ***'
      }))

      // 连接聊天WebSocket
      const chatSocket = new SockJS(chatUrl, null, {
        ...transportOptions,
        withCredentials: true  // 启用凭证支持，允许发送cookie
      })

      // 添加原生WebSocket事件监听
      chatSocket.onopen = () => {
        console.log('聊天WebSocket已打开')
        // 在原生WebSocket连接打开后，可以尝试手动添加token到请求头
        if (chatSocket._transport && chatSocket._transport.xhr) {
          chatSocket._transport.xhr.setRequestHeader('Authorization', `Bearer ${token}`)
          console.log('已手动添加Authorization头到XHR对象')
        }
      }

      chatSocket.onerror = (e) => {
        console.error('聊天WebSocket错误:', e)
        // 记录更详细的错误信息
        if (e.type === 'error') {
          console.error('WebSocket连接错误类型:', e.type)
          if (e.target && e.target.readyState) {
            console.error('WebSocket状态:', e.target.readyState)
          }
        }
      }

      // 添加关闭事件监听
      chatSocket.onclose = (e) => {
        console.warn('聊天WebSocket已关闭:', {
          code: e.code,
          reason: e.reason,
          wasClean: e.wasClean
        })
      }

      this.stompClient = new Client({
        webSocketFactory: () => chatSocket,
        connectHeaders: connectHeaders,  // 使用STOMP连接头传递token
        debug: (str) => {
          if (str.includes('error') || str.includes('failed')) {
            console.error('STOMP错误:', str)
          } else if (str.includes('connect') || str.includes('subscribe')) {
            console.log('STOMP信息:', str)
          }
        },
        reconnectDelay: 5000,
        heartbeatIncoming: 10000,
        heartbeatOutgoing: 10000
      })

      console.log('准备连接到通知WebSocket...')

      // 连接通知WebSocket
      const notificationSocket = new SockJS(notificationUrl, null, {
        ...transportOptions,
        withCredentials: true  // 启用凭证支持，允许发送cookie
      })

      // 添加原生WebSocket事件监听
      notificationSocket.onopen = () => {
        console.log('通知WebSocket已打开')
        // 在原生WebSocket连接打开后，可以尝试手动添加token到请求头
        if (notificationSocket._transport && notificationSocket._transport.xhr) {
          notificationSocket._transport.xhr.setRequestHeader('Authorization', `Bearer ${token}`)
          console.log('已手动添加Authorization头到通知XHR对象')
        }
      }

      notificationSocket.onerror = (e) => {
        console.error('通知WebSocket错误:', e)
        // 记录更详细的错误信息
        if (e.type === 'error') {
          console.error('WebSocket连接错误类型:', e.type)
          if (e.target && e.target.readyState) {
            console.error('WebSocket状态:', e.target.readyState)
          }
        }
      }

      // 添加关闭事件监听
      notificationSocket.onclose = (e) => {
        console.warn('通知WebSocket已关闭:', {
          code: e.code,
          reason: e.reason,
          wasClean: e.wasClean
        })
      }

      this.notificationClient = new Client({
        webSocketFactory: () => notificationSocket,
        connectHeaders: connectHeaders,  // 使用STOMP连接头传递token
        debug: (str) => {
          if (str.includes('error') || str.includes('failed')) {
            console.error('STOMP通知错误:', str)
          }
        },
        reconnectDelay: 5000,
        heartbeatIncoming: 10000,
        heartbeatOutgoing: 10000
      })

      // 激活两个客户端
      console.log('开始激活WebSocket客户端...')
      await Promise.all([
        this.activateClient(this.stompClient, 'chat'),
        this.activateClient(this.notificationClient, 'notification')
      ])

      // 重置重连计数
      this.reconnectAttempts = 0
      this.initialized = true

      // 启动心跳
      this.startHeartbeat()

      // 使用统一的连接状态管理
      setConnectionStatus(true, false)

      console.log('==== WebSocket连接成功 ====')
      return true
    } catch (error) {
      console.group('==== WebSocket连接失败 ====')
      console.error('错误类型:', error.name)
      console.error('错误消息:', error.message)
      console.error('错误堆栈:', error.stack)

      // 检查浏览器WebSocket支持
      if (typeof WebSocket === 'undefined') {
        console.error('浏览器不支持WebSocket!')
      } else {
        console.log('浏览器WebSocket支持: 正常')
      }

      // 检查网络状态
      if (navigator.onLine) {
        console.log('网络连接状态: 在线')
      } else {
        console.error('网络连接状态: 离线')
      }
      console.groupEnd()

      this.reconnect()
      return false
    } finally {
      this.connectionPending = false
    }
  }

  async activateClient(client, type) {
    return new Promise((resolve, reject) => {
      client.onConnect = (frame) => {
        console.log(`${type} WebSocket客户端激活成功`)

        // 获取当前用户ID
        const userStore = useUserStore()
        const userId = userStore.userInfo?.id

        if (!userId) {
          console.warn(`${type} WebSocket客户端激活成功，但未找到用户ID`)
          resolve(frame)
          return
        }

        // 聊天客户端连接成功后的处理
        if (type === 'chat') {
          try {
            // 发送用户上线消息
            this.sendUserOnlineMessage(userId)
            console.log('已发送用户上线消息')

            // 连接成功后立即请求在线用户数量
            setTimeout(() => {
              this.requestOnlineUsers()
            }, 500)

            // 连接成功后立即订阅聊天主题
            setTimeout(() => {
              this.subscribeChatTopics(userId)
                .then(() => console.log('聊天主题订阅成功'))
                .catch(err => console.error('聊天主题订阅失败:', err))
            }, 1000)

            // 触发WebSocket连接成功事件
            window.dispatchEvent(new CustomEvent('websocket-connected', {
              detail: { userId, timestamp: Date.now(), type: 'chat' }
            }))
            console.log('已触发websocket-connected全局事件 (聊天)')
          } catch (error) {
            console.error('聊天客户端初始化失败:', error)
          }
        }

        // 通知客户端连接成功后的处理
        if (type === 'notification') {
          try {
            console.log('【通知调试】通知WebSocket客户端激活成功，准备订阅通知主题')

            // 连接成功后立即订阅通知主题
            setTimeout(() => {
              this.subscribeNotificationTopics(userId)
                .then(success => {
                  if (success) {
                    console.log('【通知调试】通知主题订阅成功')

                    // 触发通知WebSocket连接成功事件
                    window.dispatchEvent(new CustomEvent('notification-websocket-connected', {
                      detail: { userId, timestamp: Date.now() }
                    }))
                    console.log('已触发notification-websocket-connected全局事件')
                  } else {
                    console.error('【通知调试】通知主题订阅失败')
                  }
                })
                .catch(err => console.error('【通知调试】通知主题订阅出错:', err))
            }, 1000)
          } catch (error) {
            console.error('【通知调试】通知客户端初始化失败:', error)
          }
        }

        resolve(frame)
      }

      client.onStompError = (error) => {
        console.error(`${type} WebSocket连接错误:`, error)
        reject(error)
      }

      try {
        client.activate()
      } catch (error) {
        console.error(`${type} WebSocket客户端激活失败:`, error)
        reject(error)
      }
    })
  }

  // 请求在线用户数量
  requestOnlineUsers() {
    if (!this.stompClient || !this.stompClient.connected) {
      console.warn('WebSocket未连接，无法请求在线用户数量')
      return
    }

    try {
      console.log('WebSocket客户端正在请求在线用户数量...')
      this.stompClient.publish({
        destination: '/app/getAllOnlineUsers',
        body: JSON.stringify({
          timestamp: new Date().getTime()
        }),
        headers: {
          'content-type': 'application/json'
        }
      })
      console.log('WebSocket客户端在线用户数量请求已发送')
    } catch (error) {
      console.error('WebSocket客户端请求在线用户数量失败:', error)
    }
  }

  // 订阅聊天相关的主题
  async subscribeChatTopics(userId) {
    try {
      console.log('开始订阅聊天相关主题...')

      // 订阅私信消息
      await this.retrySubscribe(() => this.subscribeToPrivateMessages(userId), '私信消息')

      console.log('聊天相关主题订阅完成')
    } catch (error) {
      console.error('订阅聊天主题失败:', error)
      if (this.autoReconnect) {
        this.reconnect()
      }
    }
  }

  // 订阅通知相关的主题
  async subscribeNotificationTopics(userId) {
    try {
      console.log('开始订阅通知相关主题...')

      // 订阅系统通知
      await this.retrySubscribe(() => this.subscribeToNotifications(userId), '系统通知')

      console.log('通知相关主题订阅完成')
    } catch (error) {
      console.error('订阅通知主题失败:', error)
      if (this.autoReconnect) {
        this.reconnect()
      }
    }
  }

  // 统一处理所有订阅
  async subscribeAll(userId) {
    if (!this.stompClient?.connected) {
      console.warn('聊天WebSocket未连接')
      return false
    }

    try {
      // 订阅聊天相关主题
      await this.subscribeChatTopics(userId)

      // 订阅通知相关主题
      await this.subscribeNotificationTopics(userId)

      console.log('【通知调试】所有主题订阅成功')
      return true
    } catch (error) {
      console.error('【通知调试】订阅主题失败:', error)
      return false
    }
  }

  // 订阅聊天相关的主题
  async subscribeChatTopics(userId) {
    if (!this.stompClient?.connected) {
      console.warn('聊天WebSocket未连接')
      return false
    }

    try {
      console.log('【聊天调试】开始订阅聊天相关主题...')

      // 订阅用户消息
      const messageSub = this.stompClient.subscribe(
        `/user/${userId}/messages`,
        (message) => {
          try {
            console.log('收到聊天消息:', message.body)
            const chatMessage = JSON.parse(message.body)
            this.emit('message', chatMessage)
          } catch (error) {
            console.error('解析聊天消息失败:', error)
          }
        }
      )

      this.subscriptions.push(messageSub)
      console.log(`【聊天调试】成功订阅聊天主题: /user/${userId}/messages，订阅ID: ${messageSub.id}`)

      return true
    } catch (error) {
      console.error('【聊天调试】订阅聊天主题失败:', error)
      return false
    }
  }

  // 订阅通知相关的主题
  async subscribeNotificationTopics(userId) {
    try {
      console.log('【通知调试】开始订阅通知相关主题...')

      // 订阅系统通知
      const success = this.subscribeToNotifications(userId)

      if (success) {
        console.log('【通知调试】通知相关主题订阅成功')

        // 触发全局事件，通知通知组件刷新
        window.dispatchEvent(new CustomEvent('notification-subscribed', {
          detail: { userId }
        }))

        return true
      } else {
        console.error('【通知调试】通知相关主题订阅失败')
        return false
      }
    } catch (error) {
      console.error('【通知调试】订阅通知主题失败:', error)
      if (this.autoReconnect) {
        this.reconnect()
      }
      return false
    }
  }

  // 新增：重试订阅方法
  async retrySubscribe(subscribeFunc, subscriptionName, maxRetries = 3) {
    let attempts = 0
    while (attempts < maxRetries) {
      try {
        const result = await subscribeFunc()
        if (result) {
          console.log(`${subscriptionName}订阅成功`)
          return true
        }
        throw new Error(`${subscriptionName}订阅失败`)
      } catch (error) {
        attempts++
        if (attempts === maxRetries) {
          console.error(`${subscriptionName}订阅失败，已达到最大重试次数:`, error)
          throw error
        }
        console.warn(`${subscriptionName}订阅失败，正在重试(${attempts}/${maxRetries}):`, error)
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
  }

  // 订阅私信消息
  subscribeToPrivateMessages(userId) {
    return new Promise((resolve, reject) => {
      if (!this.stompClient || !this.stompClient.connected) {
        console.error('STOMP客户端未连接，无法订阅私信主题')
        reject(new Error('STOMP客户端未连接'))
        return
      }

      try {
        // 创建一个数组来存储所有订阅
        const subscriptions = [];

        // 1. 订阅主要私信主题 - 确保路径与后端发送路径匹配
        // Spring的convertAndSendToUser会自动添加/user/{userId}前缀
        // 后端使用messagingTemplate.convertAndSendToUser(receiverId, "/queue/private", messageDTO)
        const destination = `/user/queue/private`
        console.log('正在订阅主要私信主题:', destination)

        const subscription = this.stompClient.subscribe(destination, (message) => {
          console.log('收到主要私信消息:', message)
          console.log('消息头:', message.headers)
          console.log('消息体:', message.body)

          // 处理接收到的消息
          try {
            // 尝试解析消息体为JSON
            const messageData = JSON.parse(message.body)

            // 规范化消息格式，确保字段一致性
            const normalizedMessage = {
              ...messageData,
              // 确保消息内容字段一致 - 前端使用content，后端可能使用message
              content: messageData.message || messageData.content,
              // 确保消息类型字段存在
              messageType: messageData.messageType || 'TEXT',
              // 确保时间戳字段存在
              time: messageData.timestamp || messageData.time || new Date().toISOString(),
              // 添加消息状态
              status: 'SENT'
            }

            console.log('规范化后的消息:', normalizedMessage)

            // 处理消息
            this.processIncomingMessage(normalizedMessage, userId)

            // 触发全局事件
            window.dispatchEvent(new CustomEvent('chat-message', {
              detail: normalizedMessage
            }))
          } catch (error) {
            console.error('解析或处理消息时出错:', error)
            console.error('原始消息:', message.body)
          }
        })
        subscriptions.push(subscription);

        // 2. 订阅已读回执主题
        const mainReadReceiptDestination = `/queue/read-receipts`
        console.log('正在订阅已读回执主题:', mainReadReceiptDestination)

        const mainReadReceiptSubscription = this.stompClient.subscribe(mainReadReceiptDestination, (message) => {
          console.log('收到已读回执消息:', message)
          console.log('消息头:', message.headers)
          console.log('消息体:', message.body)

          try {
            const readReceipt = JSON.parse(message.body)
            // 触发已读回执事件
            window.dispatchEvent(new CustomEvent('read-receipt', {
              detail: readReceipt
            }))
          } catch (error) {
            console.error('解析已读回执消息失败:', error)
          }
        })
        subscriptions.push(mainReadReceiptSubscription);

        // 2. 订阅备用私信主题 - 修正路径，移除重复的/user前缀
        const backupDestination = `/messages`
        console.log('正在订阅备用私信主题:', backupDestination)
        console.log('完整的订阅路径将是: /user/' + userId + backupDestination)

        const backupSubscription = this.stompClient.subscribe(backupDestination, (message) => {
          console.log('收到备用私信消息:', message)
          console.log('消息头:', message.headers)
          console.log('消息体:', message.body)

          // 处理接收到的消息
          this.processIncomingMessage(message, userId)
        })
        subscriptions.push(backupSubscription);

        // 3. 订阅已发送消息确认主题 - 修正路径，移除重复的/user前缀
        const sentMessagesDestination = `/queue/sent`
        console.log('正在订阅已发送消息确认主题:', sentMessagesDestination)
        console.log('完整的订阅路径将是: /user/' + userId + sentMessagesDestination)

        const sentMessagesSubscription = this.stompClient.subscribe(sentMessagesDestination, (message) => {
          console.log('收到已发送消息确认:', message)
          console.log('消息头:', message.headers)
          console.log('消息体:', message.body)

          // 处理已发送消息确认
          this.processIncomingMessage(message, userId)
        })
        subscriptions.push(sentMessagesSubscription);

        // 4. 订阅用户私人已读回执主题 - 修正路径，移除重复的/user前缀
        const userPrivateReadReceiptsDestination = `/queue/read-receipts`
        console.log('正在订阅用户私人已读回执主题:', userPrivateReadReceiptsDestination)
        console.log('完整的订阅路径将是: /user/' + userId + userPrivateReadReceiptsDestination)

        const userPrivateReadReceiptSubscription = this.stompClient.subscribe(userPrivateReadReceiptsDestination, (message) => {
          console.log('收到用户私人已读回执:', message)
          console.log('已读回执头:', message.headers)
          console.log('已读回执体:', message.body)

              // 处理已读回执
          this.processReadReceipt(message, userId)
        })
        subscriptions.push(userPrivateReadReceiptSubscription);

        // 4.1 订阅全局已读回执主题
        const globalReadReceiptsDestination = `/topic/read-receipts`
        console.log('正在订阅全局已读回执主题:', globalReadReceiptsDestination)

        const globalReadReceiptSubscription = this.stompClient.subscribe(globalReadReceiptsDestination, (message) => {
          console.log('收到全局已读回执:', message)
          console.log('全局已读回执头:', message.headers)
          console.log('全局已读回执体:', message.body)

          try {
            // 解析消息
            const readReceiptData = JSON.parse(message.body)

            // 只处理与当前用户相关的已读回执
            if (String(readReceiptData.senderId) === String(userId)) {
              console.log('全局已读回执与当前用户相关，处理回执')
              this.processReadReceipt(message, userId)
            } else {
              console.log('全局已读回执与当前用户无关，忽略回执')
            }
          } catch (error) {
            console.error('处理全局已读回执失败:', error)
          }
        })
        subscriptions.push(globalReadReceiptSubscription);

        // 4.2 订阅用户特定的全局已读回执主题
        const userSpecificReadReceiptsDestination = `/topic/read-receipts/${userId}`
        console.log('正在订阅用户特定的全局已读回执主题:', userSpecificReadReceiptsDestination)

        const userSpecificReadReceiptSubscription = this.stompClient.subscribe(userSpecificReadReceiptsDestination, (message) => {
          console.log('收到用户特定的全局已读回执:', message)
          console.log('用户特定的全局已读回执头:', message.headers)
          console.log('用户特定的全局已读回执体:', message.body)

          // 处理已读回执
          this.processReadReceipt(message, userId)
        })
        subscriptions.push(userSpecificReadReceiptSubscription);

        // 4.3 尝试订阅另一种格式的用户特定已读回执主题
        const alternativeReadReceiptsDestination = `/user/queue/read-receipts`
        console.log('正在订阅另一种格式的用户特定已读回执主题:', alternativeReadReceiptsDestination)

        const alternativeReadReceiptSubscription = this.stompClient.subscribe(alternativeReadReceiptsDestination, (message) => {
          console.log('收到另一种格式的用户特定已读回执:', message)
          console.log('另一种格式的用户特定已读回执头:', message.headers)
          console.log('另一种格式的用户特定已读回执体:', message.body)

          // 处理已读回执
          this.processReadReceipt(message, userId)
        })
        subscriptions.push(alternativeReadReceiptSubscription);

        // 5. 订阅全局消息主题
        const globalMessagesDestination = '/topic/messages'
        console.log('正在订阅全局消息主题:', globalMessagesDestination)

        const globalMessagesSubscription = this.stompClient.subscribe(globalMessagesDestination, (message) => {
          console.log('收到全局消息:', message)
          console.log('全局消息头:', message.headers)
          console.log('全局消息体:', message.body)

          try {
            // 解析消息
            const messageData = JSON.parse(message.body)

            // 只处理与当前用户相关的消息
            if (messageData.senderId == userId || messageData.receiverId == userId) {
              console.log('全局消息与当前用户相关，处理消息')
              this.processIncomingMessage(message, userId)
            } else {
              console.log('全局消息与当前用户无关，忽略消息')
            }
          } catch (error) {
            console.error('处理全局消息失败:', error)
          }
        })
        subscriptions.push(globalMessagesSubscription);

        // 添加到订阅列表
        this.subscriptions.push(...subscriptions)

        // 成功订阅
        console.log('成功订阅所有消息主题')
        console.log('订阅数量:', subscriptions.length)
        console.log('订阅ID列表:', subscriptions.map(sub => sub.id).join(', '))

        // 返回所有订阅
        resolve(subscriptions)
      } catch (error) {
        console.error('订阅私信失败:', error)
        reject(error)
      }
    })
  }

  // 订阅系统通知
  subscribeToNotifications(userId) {
    if (!this.notificationClient?.connected) {
      console.warn('【通知调试】通知WebSocket未连接，无法订阅通知')
      return false
    }

    console.log(`【通知调试】准备订阅通知主题: /topic/notifications/${userId}`)

    try {
      // 订阅用户通知 - 修改为与后端发送路径匹配
      const notificationSub = this.notificationClient.subscribe(
        `/topic/notifications/${userId}`,
        (message) => {
          try {
            console.log('【通知调试】收到通知消息:', message.body)
            const notification = JSON.parse(message.body)

            // 使用统一的通知处理方式，只触发一个事件
            // 添加来源标记，方便调试
            notification._source = 'primary_topic';

            // 触发全局事件，确保通知能被处理
            window.dispatchEvent(new CustomEvent('websocket-notification', {
              detail: notification
            }))

            // 立即触发一次通知计数刷新
            setTimeout(() => {
              console.log('【通知调试】触发通知计数刷新');
              window.dispatchEvent(new CustomEvent('refresh-notification-count'));
            }, 100);
          } catch (error) {
            console.error('【通知调试】解析通知消息失败:', error)
          }
        }
      )

      this.notificationSubscriptions.push(notificationSub)
      console.log(`【通知调试】成功订阅通知主题: /topic/notifications/${userId}，订阅ID: ${notificationSub.id}`)

      // 同时尝试订阅备用通知主题
      try {
        const backupNotificationSub = this.notificationClient.subscribe(
          `/user/${userId}/queue/notifications`,
          (message) => {
            try {
              console.log('【通知调试】收到备用通知消息:', message.body)
              const notification = JSON.parse(message.body)

              // 添加来源标记，方便调试
              notification._source = 'backup_topic';

              // 只触发全局事件
              window.dispatchEvent(new CustomEvent('websocket-notification', {
                detail: notification
              }))
            } catch (error) {
              console.error('【通知调试】解析备用通知消息失败:', error)
            }
          }
        )

        this.notificationSubscriptions.push(backupNotificationSub)
        console.log(`【通知调试】成功订阅备用通知主题: /user/${userId}/queue/notifications，订阅ID: ${backupNotificationSub.id}`)
      } catch (backupError) {
        console.warn('【通知调试】订阅备用通知主题失败:', backupError)
      }

      return true
    } catch (error) {
      console.error(`【通知调试】订阅通知主题失败: /topic/notifications/${userId}`, error)
      return false
    }
  }

  async reconnect(useFallbackTransport = false) {
    // 检查最大重连次数
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`WebSocket重连失败，已达到最大重试次数(${this.maxReconnectAttempts})`)
      ElMessage.error('网络连接失败，请刷新页面重试')

      // 使用统一的连接状态管理
      setConnectionStatus(false, false)
      return
    }

    // 防止重复重连
    if (this.isReconnecting) {
      console.log('已有重连任务在进行中，跳过本次重连')
      return
    }

    this.isReconnecting = true

    // 使用统一的连接状态管理
    setConnectionStatus(false, true)

    this.reconnectAttempts++
    console.log(`准备第 ${this.reconnectAttempts}/${this.maxReconnectAttempts} 次重连尝试...`)

    try {
      // 计算延迟时间 - 使用指数退避策略
      // 基础延迟1秒，每次尝试增加50%，最大延迟10秒
      const delay = Math.min(1000 * Math.pow(1.5, this.reconnectAttempts - 1), 10000)
      console.log(`将在 ${delay/1000} 秒后尝试重连...`)

      // 使用setTimeout而不是直接递归调用，避免调用栈溢出
      setTimeout(async () => {
        try {
          console.log(`执行第 ${this.reconnectAttempts} 次重连...`)

          // 清理现有连接和订阅
          this.cleanup()

          // 重新连接
          const connected = await this.connect()

          if (connected) {
            console.log('WebSocket重连成功')

            // 获取当前用户ID
            const userStore = useUserStore()
            const userId = userStore.userInfo?.id

            // 重新订阅所有主题
            if (userId) {
              try {
                // 重新订阅聊天主题
                await this.subscribeChatTopics(userId)

                // 重新订阅通知主题
                await this.subscribeNotificationTopics(userId)

                console.log('所有主题重新订阅成功')
              } catch (subError) {
                console.error('重新订阅主题失败:', subError)
              }
            }
          } else {
            console.error('WebSocket重连失败')
            this.reconnect()
          }
        } catch (error) {
          console.error('setTimeout中WebSocket重连失败:', error)
          // 递归调用自身进行下一次重连
          this.reconnect()
        } finally {
          this.isReconnecting = false
        }
      }, delay)
    } catch (error) {
      console.error('WebSocket重连调度失败:', error)
      // 如果调度重连失败，尝试立即重连
      try {
        await this.connect()
      } catch (connectError) {
        console.error('立即重连失败:', connectError)
      } finally {
        this.isReconnecting = false
      }
    }
  }

  // 清理现有连接和订阅
  cleanup() {
    console.log('清理现有WebSocket连接和订阅')

    // 清理订阅
    if (this.subscriptions.length > 0) {
      console.log(`清理 ${this.subscriptions.length} 个聊天订阅`)
      this.subscriptions.forEach(subscription => {
        try {
          if (subscription && typeof subscription.unsubscribe === 'function') {
            subscription.unsubscribe()
          }
        } catch (error) {
          console.warn('取消订阅失败:', error)
        }
      })
      this.subscriptions = []
    }

    // 清理通知订阅
    if (this.notificationSubscriptions.length > 0) {
      console.log(`清理 ${this.notificationSubscriptions.length} 个通知订阅`)
      this.notificationSubscriptions.forEach(subscription => {
        try {
          if (subscription && typeof subscription.unsubscribe === 'function') {
            subscription.unsubscribe()
          }
        } catch (error) {
          console.warn('取消通知订阅失败:', error)
        }
      })
      this.notificationSubscriptions = []
    }

    // 断开STOMP客户端
    if (this.stompClient?.connected) {
      console.log('断开聊天STOMP客户端')
      try {
        this.stompClient.deactivate()
      } catch (error) {
        console.warn('断开聊天STOMP客户端失败:', error)
      }
    }

    // 断开通知STOMP客户端
    if (this.notificationClient?.connected) {
      console.log('断开通知STOMP客户端')
      try {
        this.notificationClient.deactivate()
      } catch (error) {
        console.warn('断开通知STOMP客户端失败:', error)
      }
    }

    // 清理心跳定时器
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  // 发送用户上线消息
  sendUserOnlineMessage(userId) {
    if (!this.stompClient || !this.stompClient.connected) {
      console.error('STOMP客户端未连接，无法发送上线消息')
      return
    }

    const userStore = useUserStore()
    const token = userStore.token

    try {
      console.log('发送用户上线消息:', userId)
      this.stompClient.publish({
        destination: '/app/user/online',
        headers: {
          'Authorization': `Bearer ${token}`,
          'token': token,                // 添加额外的token字段
          'user-token': token            // 额外的自定义头，增加兼容性
        },
        body: String(userId)
      })
    } catch (error) {
      console.error('发送用户上线消息失败:', error)
    }
  }

  // 发送用户离线消息
  sendUserOfflineMessage(userId) {
    if (!this.stompClient || !this.stompClient.connected) {
      console.error('STOMP客户端未连接，无法发送离线消息')
      return
    }

    const userStore = useUserStore()
    const token = userStore.token

    try {
      console.log('发送用户离线消息:', userId)
      this.stompClient.publish({
        destination: '/app/user/offline',
        headers: {
          'Authorization': `Bearer ${token}`,
          'token': token,                // 添加额外的token字段
          'user-token': token            // 额外的自定义头，增加兼容性
        },
        body: String(userId)
      })
    } catch (error) {
      console.error('发送用户离线消息失败:', error)
    }
  }

  // 启动心跳
  startHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
    }

    this.heartbeatTimer = setInterval(() => {
      this.sendHeartbeat()
    }, 30000) // 每30秒发送一次心跳

    console.log('已启动WebSocket心跳，间隔: 30秒')
  }

  // 发送心跳
  sendHeartbeat() {
    if (!this.stompClient || !this.stompClient.connected) {
      console.warn('STOMP客户端未连接，无法发送心跳')

      // 如果客户端未连接，尝试重连
      this.reconnect()
      return
    }

    const userStore = useUserStore()
    const userId = userStore.userInfo?.id

    if (!userId) {
      console.warn('未找到用户ID，无法发送心跳')
      return
    }

    try {
      const userStore = useUserStore()
      const token = userStore.token

      console.log('发送WebSocket心跳...')

      // 发送心跳消息
      this.stompClient.publish({
        destination: '/app/user/heartbeat',
        headers: {
          'Authorization': `Bearer ${token}`,
          'token': token,                // 添加额外的token字段
          'user-token': token            // 额外的自定义头，增加兼容性
        },
        body: String(userId)
      })
      console.debug('已发送心跳')
    } catch (error) {
      console.error('发送心跳失败:', error)
    }
  }

  async disconnect() {
    try {
      // 发送用户离线消息
      const userStore = useUserStore()
      const userId = userStore.userInfo?.id

      if (userId && this.stompClient?.connected) {
        this.sendUserOfflineMessage(userId)
        console.log('已发送用户离线消息')
      }

      // 停止心跳
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer)
        this.heartbeatTimer = null
      }

      // 取消所有订阅
      this.subscriptions.forEach(subscription => {
        if (subscription && subscription.unsubscribe) {
          subscription.unsubscribe()
        }
      })
      this.subscriptions = []

      this.notificationSubscriptions.forEach(subscription => {
        if (subscription && subscription.unsubscribe) {
          subscription.unsubscribe()
        }
      })
      this.notificationSubscriptions = []

      // 断开STOMP客户端连接
      if (this.stompClient) {
        await this.stompClient.deactivate()
        this.stompClient = null
      }

      if (this.notificationClient) {
        await this.notificationClient.deactivate()
        this.notificationClient = null
      }

      this.initialized = false
      this.connectionPending = false
      this.reconnectAttempts = 0
      this.messageHandlers.clear()

      // 使用统一的连接状态管理
      setConnectionStatus(false, false)

      console.log('WebSocket连接已断开')
    } catch (error) {
      console.error('断开WebSocket连接时发生错误:', error)
    }
  }

  // 添加事件发射器
  emit(eventName, data) {
    const handlers = this.messageHandlers.get(eventName) || []
    handlers.forEach(handler => {
      try {
        handler(data)
      } catch (error) {
        console.error(`处理事件 ${eventName} 时发生错误:`, error)
      }
    })
  }

  // 处理接收到的消息
  processIncomingMessage(message, userId) {
    try {
      console.log('处理接收到的消息:', message)

      // 检查消息对象是否有效
      if (!message) {
        console.error('消息对象为空，无法处理')
        return
      }

      // 检查消息是否是STOMP消息对象
      const isStompMessage = message.headers && message.body

      // 如果是STOMP消息对象，处理STOMP消息
      if (isStompMessage) {
        console.log('处理STOMP消息对象')
        console.log('消息头:', message.headers)
        console.log('消息体:', message.body)

        // 检查消息订阅路径，特别关注 /queue/sent 主题
        const destination = message.headers.destination
        console.log('消息目的地:', destination)

        // 检查是否来自sent队列的消息（用于更新发送状态）
        const isFromSentQueue = destination && destination.includes('/queue/sent')
        if (isFromSentQueue) {
          console.log('检测到来自sent队列的消息，这是发送状态更新消息')
        }

        // 检查消息是否已经处理过（防止重复处理）
        const messageId = message.headers['message-id'];
        if (messageId && this.processedMessages && this.processedMessages.has(messageId)) {
          console.log('消息已处理过，跳过:', messageId);
          return;
        }

        // 如果没有处理过的消息集合，创建一个
        if (!this.processedMessages) {
          this.processedMessages = new Set();

          // 设置定时清理，防止集合无限增长
          setInterval(() => {
            console.log('清理已处理消息集合，当前大小:', this.processedMessages.size);
            this.processedMessages.clear();
          }, 60000); // 每分钟清理一次
        }

        // 标记消息为已处理
        if (messageId) {
          this.processedMessages.add(messageId);
        }

        // 尝试解析消息体
        let parsedMessage
        try {
          parsedMessage = JSON.parse(message.body)
          console.log('解析后的消息:', parsedMessage)
        } catch (e) {
          console.warn('消息体不是有效的JSON:', e)
          // 如果不是JSON，尝试作为纯文本处理
          this.handleMessage('text', message.body)
          return
        }

        // 检查是否是已读回执
        if (parsedMessage.messageIds && Array.isArray(parsedMessage.messageIds)) {
          console.log('检测到已读回执消息，转交给processReadReceipt处理')
          this.processReadReceipt(message, userId)
          return
        }

        // 检查必要字段
        if (!parsedMessage.senderId && !parsedMessage.sender) {
          console.warn('消息缺少发送者ID:', parsedMessage)
        }

        if (!parsedMessage.receiverId && !parsedMessage.receiver) {
          console.warn('消息缺少接收者ID:', parsedMessage)
        }

        // 检查消息是否与当前用户相关
        const senderId = parsedMessage.senderId || parsedMessage.sender;
        const receiverId = parsedMessage.receiverId || parsedMessage.receiver;
        const isSentByCurrentUser = String(senderId) === String(userId);

        // 如果是来自sent队列的消息且是当前用户发送的，这是一个状态更新消息
        if (isFromSentQueue && isSentByCurrentUser) {
          console.log('处理来自sent队列的状态更新消息')

          // 查找临时ID，可能在多个地方
          const tempId = parsedMessage.clientMessageId || parsedMessage.tempId || parsedMessage.localId;

          if (tempId && tempId.startsWith('temp-')) {
            console.log('找到临时ID:', tempId, '将更新为真实ID:', parsedMessage.id)

            // 触发消息状态更新事件
            window.dispatchEvent(new CustomEvent('message-sent', {
              detail: {
                message: {
                  ...parsedMessage,
                  id: parsedMessage.id,
                  isRead: false // 初始状态为未读
                },
                tempId: tempId
              }
            }));

            console.log('已触发消息状态更新事件，临时ID:', tempId, '真实ID:', parsedMessage.id)
            return;
          }
        }

        if (String(senderId) !== String(userId) && String(receiverId) !== String(userId)) {
          console.log('消息与当前用户无关，忽略消息');
          return;
        }

        // 规范化消息格式 - 确保所有必要字段都存在
        const normalizedMessage = {
          ...parsedMessage,
          id: parsedMessage.id || parsedMessage.messageId || `temp-${Date.now()}`,
          content: parsedMessage.content || parsedMessage.message || '[空消息]',
          senderId: senderId,
          receiverId: receiverId,
          messageType: parsedMessage.messageType || 'TEXT',
          time: parsedMessage.time || parsedMessage.timestamp || new Date().toISOString(),
          isSelf: isSentByCurrentUser,
          isRead: parsedMessage.isRead || false // 只保留已读状态
        }

        console.log('规范化后的消息:', normalizedMessage)
        console.log('消息是否为自己发送:', normalizedMessage.isSelf)
        console.log('发送者ID:', normalizedMessage.senderId, '接收者ID:', normalizedMessage.receiverId)

        // 验证消息的有效性
        if (!normalizedMessage.senderId || !normalizedMessage.receiverId) {
          console.error('消息缺少必要的发送者或接收者ID，无法处理')
          return
        }

        // 检查是否有clientMessageId或tempId，用于更新临时消息状态
        if (normalizedMessage.isSelf) {
          const tempId = parsedMessage.clientMessageId || parsedMessage.tempId;
          if (tempId) {
            console.log('检测到临时消息ID，将更新临时消息状态:', tempId)

            // 触发消息发送成功事件，用于更新UI
            window.dispatchEvent(new CustomEvent('message-sent', {
              detail: {
                message: normalizedMessage,
                tempId: tempId
              }
            }))

            console.log('已触发消息状态更新事件')
          }
        }

        // 分发消息
        this.handleMessage('message', normalizedMessage)

        // 判断是否是系统公告相关消息
        const isAnnouncementMessage =
          (normalizedMessage.title && normalizedMessage.title.includes('系统公告')) ||
          (normalizedMessage.content && normalizedMessage.content.includes('系统公告'));

        // 播放通知声音 - 只有当消息不是自己发送的且不是系统公告时才播放
        if (!normalizedMessage.isSelf && !isAnnouncementMessage) {
          // 使用聊天消息音效
          this.playNotificationSound('chat')
        } else if (isAnnouncementMessage) {
          console.log('系统公告消息，不播放音效');
        }
      } else {
        // 如果不是STOMP消息对象，直接处理消息对象
        console.log('处理非STOMP消息对象')

        // 检查消息是否与当前用户相关
        const senderId = message.senderId || message.sender;
        const receiverId = message.receiverId || message.receiver;

        if (senderId && receiverId) {
          if (String(senderId) !== String(userId) && String(receiverId) !== String(userId)) {
            console.log('消息与当前用户无关，忽略消息');
            return;
          }
        }

        // 规范化消息格式
        const normalizedMessage = {
          ...message,
          id: message.id || message.messageId || `temp-${Date.now()}`,
          content: message.content || message.message || '[空消息]',
          senderId: senderId,
          receiverId: receiverId,
          messageType: message.messageType || 'TEXT',
          time: message.time || message.timestamp || new Date().toISOString(),
          isSelf: senderId ? String(senderId) === String(userId) : false,
          isRead: message.isRead || false // 只保留已读状态
        }

        console.log('规范化后的非STOMP消息:', normalizedMessage)

        // 验证消息的有效性
        if (!normalizedMessage.senderId || !normalizedMessage.receiverId) {
          console.error('消息缺少必要的发送者或接收者ID，无法处理')
          return
        }

        // 分发消息
        this.handleMessage('message', normalizedMessage)

        // 播放通知声音 - 只有当消息不是自己发送的时才播放
        if (!normalizedMessage.isSelf) {
          this.playNotificationSound('chat')
        }
      }
    } catch (error) {
      console.error('处理接收到的消息时出错:', error)
      console.error('错误详情:', error.stack)
    }
  }

  // 处理已读回执
  processReadReceipt(message, userId) {
    try {
      console.log('处理已读回执开始 =====================================')
      console.log('已读回执原始消息:', message)
      console.log('当前用户ID:', userId)
      console.log('消息头:', message.headers)
      console.log('消息体:', message.body)

      // 尝试解析已读回执
      let readReceipt
      try {
        readReceipt = JSON.parse(message.body)
        console.log('解析后的已读回执:', readReceipt)
        console.log('已读回执字段详情:')
        console.log('- readerId:', readReceipt.readerId, typeof readReceipt.readerId)
        console.log('- senderId:', readReceipt.senderId, typeof readReceipt.senderId)
        console.log('- messageIds:', readReceipt.messageIds, Array.isArray(readReceipt.messageIds))
        console.log('- timestamp:', readReceipt.timestamp)
      } catch (e) {
        console.warn('已读回执不是有效的JSON:', e)
        console.warn('原始消息体:', message.body)
        return
      }

      // 验证已读回执的有效性
      if (!readReceipt.readerId || !readReceipt.messageIds || !Array.isArray(readReceipt.messageIds)) {
        console.error('已读回执格式无效:', readReceipt)
        console.error('缺少必要字段 - readerId:', readReceipt.readerId, 'messageIds:', readReceipt.messageIds)
        return
      }

      // 确保已读回执是发给当前用户的
      if (String(readReceipt.senderId) !== String(userId)) {
        console.warn('已读回执不是发给当前用户的:')
        console.warn('- 回执中的senderId:', readReceipt.senderId, typeof readReceipt.senderId)
        console.warn('- 当前用户ID:', userId, typeof userId)
        console.warn('- 字符串比较:', String(readReceipt.senderId) === String(userId))
        return
      }

      console.log('有效的已读回执 - 读者ID:', readReceipt.readerId, '消息IDs:', readReceipt.messageIds)
      console.log('准备触发全局事件...')

              // 触发已读回执事件
              window.dispatchEvent(new CustomEvent('read-receipt', {
                detail: readReceipt
              }))

      console.log('已触发read-receipt全局事件')
      console.log('处理已读回执结束 =====================================')
    } catch (error) {
      console.error('处理已读回执时出错:', error)
      console.error('错误详情:', error.stack)
    }
  }

  // 音频缓存
  audioCache = {
    chat: null,     // 聊天消息音频
    system: null,   // 系统通知音频
    contact: null   // 联系人上线音频
  }

  // 初始化音频
  initAudio() {
    // 聊天消息音频
    this.loadAudio('chat', [
      '/sounds/chat-notification.mp3',
      './sounds/chat-notification.mp3',
      '../sounds/chat-notification.mp3',
      '/notification.mp3',
      './notification.mp3'
    ]);

    // 系统通知音频
    this.loadAudio('system', [
      '/sounds/system-notification.mp3',
      './sounds/system-notification.mp3',
      '../sounds/system-notification.mp3'
    ]);

    // 联系人上线音频
    this.loadAudio('contact', [
      '/sounds/contact-online.mp3',
      './sounds/contact-online.mp3',
      '../sounds/contact-online.mp3'
    ]);
  }

  // 加载音频
  loadAudio(type, paths) {
    this.tryLoadAudio(type, paths, 0);
  }

  // 尝试加载音频
  tryLoadAudio(type, paths, index) {
    if (index >= paths.length) {
      console.error(`所有${type}类型音频路径都加载失败`);
      return;
    }

    try {
      const path = paths[index];
      console.log(`尝试加载${type}类型音频(路径${index+1}): ${path}`);

      const audio = new Audio(path);

      audio.addEventListener('canplaythrough', () => {
        console.log(`${type}类型音频加载成功: ${path}`);
        this.audioCache[type] = audio;
      });

      audio.addEventListener('error', (error) => {
        console.error(`${type}类型音频加载失败(路径${index+1}): ${path}`, error);
        // 尝试下一个路径
        this.tryLoadAudio(type, paths, index + 1);
      });
    } catch (error) {
      console.error(`创建${type}类型音频对象失败(路径${index+1}):`, error);
      // 尝试下一个路径
      this.tryLoadAudio(type, paths, index + 1);
    }
  }

  // 播放通知声音
  async playNotificationSound(type = 'chat') {
    try {
      const globalMessageHandler = await import('@/utils/globalMessageHandler')
      globalMessageHandler.default.playNotificationSound(type)
    } catch (error) {
      console.warn('播放通知音效失败:', error)
    }
  }

  handleMessage(type, message) {
    // 调用注册的处理函数
    const handlers = this.messageHandlers.get(type) || []
    handlers.forEach(handler => {
      try {
        handler(message)
      } catch (error) {
        console.error(`处理${type}类型消息时发生错误:`, error)
      }
    })

    // 触发全局事件
    if (type === 'message') {
      console.log('准备触发chat-message全局事件:', message)

      // 确保消息有必要的字段
      if (!message.senderId || !message.receiverId) {
        console.warn('消息缺少必要字段，无法触发全局事件:', message)
              return
            }

      // 触发多个全局事件，确保所有组件都能收到消息
      try {
        // 0. 触发全局WebSocket消息事件，供全局消息处理器使用
        window.dispatchEvent(new CustomEvent('websocket-message', {
          detail: message
        }))
        console.log('websocket-message全局事件已触发，消息ID:', message.id)

        // 1. 触发聊天消息事件
        window.dispatchEvent(new CustomEvent('chat-message', {
          detail: message
        }))
        console.log('chat-message全局事件已触发，消息ID:', message.id)

        // 2. 触发刷新联系人列表事件
        window.dispatchEvent(new CustomEvent('refresh-contacts', {
          detail: { message }
        }))
        console.log('refresh-contacts全局事件已触发')

        // 3. 触发消息通知事件
        window.dispatchEvent(new CustomEvent('new-message-notification', {
          detail: message
        }))
        console.log('new-message-notification全局事件已触发')

        // 4. 触发全局刷新联系人列表事件
        window.dispatchEvent(new CustomEvent('global-refresh-contacts', {
          detail: { timestamp: Date.now() }
        }))
        console.log('global-refresh-contacts全局事件已触发')

        // 5. 触发强制刷新事件 - 这是一个关键修复
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('force-refresh-chat', {
            detail: { message, timestamp: Date.now() }
          }))
          console.log('force-refresh-chat延迟事件已触发')
        }, 500)
      } catch (error) {
        console.error('触发全局事件失败:', error)
        console.error('错误详情:', error.stack)
      }
    } else if (type === 'read-receipt') {
      try {
        window.dispatchEvent(new CustomEvent('read-receipt', {
          detail: message
        }))
        console.log('read-receipt全局事件已触发')
      } catch (error) {
        console.error('触发read-receipt全局事件失败:', error)
      }
    }
  }

  on(type, handler) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, [])
    }
    this.messageHandlers.get(type).push(handler)
  }

  off(type, handler) {
    if (!this.messageHandlers.has(type)) {
      return
    }
    const handlers = this.messageHandlers.get(type)
    const index = handlers.indexOf(handler)
    if (index !== -1) {
      handlers.splice(index, 1)
    }
  }
}

export const wsClient = new WebSocketClient()

// 将 wsClient 设置为全局变量，方便调试
window.wsClient = wsClient

// 在线状态管理器
export const userOnlineStatus = {
  // 在线状态订阅
  globalSubscription: null,
  initialized: false,
  retryCount: 0,
  maxRetries: 5,

  // 初始化方法
  init() {
    if (this.initialized) {
      console.log('在线状态管理器已初始化')
      return
    }

    const userStore = useUserStore()
    const token = userStore.token

    if (!token) {
      console.warn('未找到token，无法初始化在线状态管理器')
      return
    }

    this.initialized = true
    this.retryCount = 0
    this.subscribe()

    // 添加WebSocket重连事件监听
    window.addEventListener('websocket-connected', () => {
      console.log('WebSocket已重连，重新订阅在线状态')
      this.retryCount = 0
      this.subscribe()
    })

    // 定期检查订阅状态
    setInterval(() => {
      if (this.initialized && (!this.globalSubscription || !wsClient.stompClient?.connected)) {
        console.log('检测到在线状态订阅丢失，尝试重新订阅')
        this.subscribe()
      }
    }, 30000) // 每30秒检查一次
  },

  // 订阅全局在线状态
  subscribe() {
    if (!wsClient.stompClient) {
      console.warn('STOMP客户端不存在，无法订阅在线状态')
      this.scheduleRetry()
      return false
    }

    if (!wsClient.stompClient.connected) {
      console.warn('STOMP客户端未连接，无法订阅在线状态')
      this.scheduleRetry()
      return false
    }

    try {
      // 如果已有订阅，先取消
      if (this.globalSubscription) {
        try {
          this.globalSubscription.unsubscribe()
          console.log('已取消旧的在线状态订阅')
        } catch (e) {
          console.warn('取消旧订阅时出错:', e)
        }
      }

      console.log('正在订阅全局在线状态主题...')

      // 订阅全局在线状态主题
      this.globalSubscription = wsClient.stompClient.subscribe('/topic/onlineStatus', (message) => {
        try {
          console.log('收到在线状态消息:', message.body)
          this.handleStatusMessage(message.body)
        } catch (error) {
          console.error('处理在线状态消息时出错:', error)
        }
      })

      console.log('已成功订阅全局在线状态主题')
      this.retryCount = 0
      return true
    } catch (error) {
      console.error('订阅全局在线状态失败:', error)
      this.scheduleRetry()
      return false
    }
  },

  // 安排重试
  scheduleRetry() {
    if (this.retryCount >= this.maxRetries) {
      console.warn(`在线状态订阅失败次数过多(${this.retryCount}/${this.maxRetries})，停止重试`)
      return
    }

    this.retryCount++
    const delay = Math.min(2000 * Math.pow(1.5, this.retryCount - 1), 10000)
    console.log(`将在 ${delay/1000} 秒后第 ${this.retryCount} 次尝试订阅在线状态`)

    setTimeout(() => this.subscribe(), delay)
  },

  // 处理状态消息
  handleStatusMessage(message) {
    try {
      // 尝试解析为JSON格式
      const jsonData = JSON.parse(message)
      if (jsonData && jsonData.userId) {
        const userId = jsonData.userId
        const isOnline = jsonData.online === true

        console.log(`解析JSON状态消息: 用户 ${userId} 状态为 ${isOnline ? '在线' : '离线'}`)

        // 更新缓存
        this.updateStatus(userId, isOnline)
        return
      }
    } catch (e) {
      // 不是JSON格式，继续尝试文本格式
      console.debug('消息不是JSON格式，尝试解析文本格式')
    }

    // 解析文本格式: "User {userId} is online/offline/back online"
    const match = message.match(/User (\d+) is (online|offline|back online)/)
    if (match) {
      const userId = match[1]
      const status = match[2]
      const isOnline = status === 'online' || status === 'back online'

      console.log(`解析文本状态消息: 用户 ${userId} 状态为 ${status}, isOnline=${isOnline}`)

      // 更新缓存
      this.updateStatus(userId, isOnline)
    } else {
      console.warn('无法解析状态消息:', message)
    }
  },

  // 更新用户在线状态
  updateStatus(userId, isOnline) {
    if (!userId) return

    // 检查状态是否变化
    const currentStatus = onlineUsersCache.get(userId)
    if (currentStatus === isOnline) {
      console.log(`用户 ${userId} 在线状态未变化，仍为: ${isOnline ? '在线' : '离线'}`)
      return
    }

    console.log(`更新用户 ${userId} 在线状态: ${isOnline ? '在线' : '离线'}`)
    onlineUsersCache.set(userId, isOnline)
    this.notifyStatusChange(userId, isOnline)
  },

  // 通知状态变更
  notifyStatusChange(userId, isOnline) {
    console.log(`用户 ${userId} 在线状态变更: ${isOnline ? '在线' : '离线'}`)

    // 获取当前用户ID
    const userStore = useUserStore()
    const currentUserId = userStore.userInfo?.id

    // 检查是否是当前用户自己
    const isSelf = String(userId) === String(currentUserId)
    console.log(`上线用户ID: ${userId}, 当前用户ID: ${currentUserId}, 是否是自己: ${isSelf}`)

    // 如果用户上线，只触发user-online事件，让全局消息处理器负责其他事件
    if (isOnline) {
      // 触发用户上线事件，包含所有必要信息
      window.dispatchEvent(new CustomEvent('user-online', {
        detail: {
          userId,
          timestamp: Date.now(),
          isSelf,
          username: userId, // 如果没有用户名，使用userId作为替代
          status: 'online'
        }
      }))
      console.log(`已触发用户 ${userId} 上线事件，由全局消息处理器处理后续事件`)

      if (!isSelf) {
        console.log('其他用户上线，事件已传递给全局消息处理器')
      } else {
        console.log('自己上线，事件已传递给全局消息处理器')
      }
    } else {
      // 如果是用户离线，直接触发状态变更事件
      // 因为离线不需要播放声音，所以不需要经过全局消息处理器
      window.dispatchEvent(new CustomEvent('user-online-status', {
        detail: {
          userId,
          isOnline: false,
          status: 'offline',
          timestamp: Date.now(),
          isSelf
        }
      }))
      console.log(`用户 ${userId} 离线事件已触发`)
    }
  },

  // 获取用户在线状态
  getStatus(userId) {
    const status = onlineUsersCache.get(userId) || false
    console.debug(`获取用户 ${userId} 在线状态: ${status ? '在线' : '离线'}`)
    return status
  },

  // 获取所有在线用户
  getAllOnlineUsers() {
    const result = {}
    onlineUsersCache.forEach((value, key) => {
      if (value) {
        result[key] = true
      }
    })
    console.debug(`获取所有在线用户，共 ${Object.keys(result).length} 人`)
    return result
  }
}

// 检查用户是否在线
export function isUserOnline(userId) {
  return userOnlineStatus.getStatus(userId) || false
}

// 获取所有在线用户
export function getOnlineUsers() {
  return userOnlineStatus.getAllOnlineUsers()
}

// 检查用户在线状态
export function checkUserOnline(userId) {
  return new Promise((resolve) => {
    // 使用wsClient.stompClient而不是全局变量stompClient
    if (!wsClient.stompClient?.connected || !userId) {
      resolve(false)
      return
    }

    // 检查缓存
    if (onlineUsersCache.has(userId)) {
      resolve(onlineUsersCache.get(userId))
      return
    }

    // 发送在线状态请求
    try {
      wsClient.stompClient.publish({
        destination: `/app/checkOnlineStatus/${userId}`,
        body: String(userId)
      })

      // 创建一次性订阅来等待响应
      const subscription = wsClient.stompClient.subscribe(`/topic/onlineStatus`, (message) => {
        try {
          const statusMessage = message.body
          const isOnline = statusMessage.includes(`User ${userId} is online`)
          onlineUsersCache.set(userId, isOnline)
          subscription.unsubscribe()
          resolve(isOnline)
        } catch (error) {
          console.error('处理在线状态消息失败:', error)
          subscription.unsubscribe()
          resolve(false)
        }
      })

      // 设置超时
      setTimeout(() => {
        if (subscription) {
          subscription.unsubscribe()
          resolve(false)
        }
      }, 5000)
    } catch (error) {
      console.error('请求用户在线状态失败:', error)
      resolve(false)
    }
  })
}

// 导出初始化方法
export function initWebSocketClient() {
  console.log('开始初始化WebSocket客户端...')

  // 确保wsClient已定义
  if (!wsClient) {
    console.error('wsClient未定义，无法初始化WebSocket客户端')
    return null
  }

  // 初始化WebSocket客户端
  wsClient.init()

  // 延迟初始化在线状态管理器，确保WebSocket连接已建立
  setTimeout(() => {
    try {
      if (userOnlineStatus && typeof userOnlineStatus.init === 'function') {
        userOnlineStatus.init()
      } else {
        console.warn('userOnlineStatus未定义或init不是函数')
      }
    } catch (e) {
      console.error('初始化在线状态管理器时出错:', e)
    }
  }, 2000)

  // 延迟订阅主题，确保WebSocket连接已建立
  setTimeout(async () => {
    try {
      // 检查WebSocket连接状态
      if (wsClient.stompClient?.connected) {
        console.log('WebSocket已连接，开始订阅主题')

        // 获取用户ID
        const userStore = useUserStore()
        const userId = userStore.userInfo?.id

        if (userId) {
          // 订阅主题
          await subscribeToTopics()
          console.log('主题订阅成功')
        } else {
          console.warn('用户未登录，无法订阅主题')
        }
      } else {
        console.warn('WebSocket未连接，无法订阅主题')
      }
    } catch (error) {
      console.error('订阅主题时出错:', error)
    }
  }, 3000)

  // 返回WebSocket客户端实例
  return wsClient
}

// 检查WebSocket是否已连接并尝试重连
export async function isWebSocketConnected(attemptReconnect = false) {
  const connected = wsClient.stompClient?.connected || false;

  // 如果未连接且需要尝试重连
  if (!connected && attemptReconnect) {
    console.log('WebSocket未连接，尝试重新连接...');
    try {
      // 尝试重新连接
      await wsClient.connect();

      // 等待连接建立
      let attempts = 0;
      while (!wsClient.stompClient?.connected && attempts < 5) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
        console.log(`等待WebSocket连接建立，尝试 ${attempts}/5`);
      }

      // 返回最新的连接状态
      return wsClient.stompClient?.connected || false;
    } catch (error) {
      console.error('WebSocket重连失败:', error);
      return false;
    }
  }

  return connected;
}

// 获取在线用户数量
export function getOnlineUserCount() {
  // 如果有专门的方法，使用它
  if (wsClient.getOnlineUserCount) {
    return wsClient.getOnlineUserCount()
  }

  // 否则计算在线用户数量
  const onlineUsers = userOnlineStatus.getAllOnlineUsers()
  return Object.keys(onlineUsers).length || 1 // 至少返回1，表示当前用户
}

// 这个函数已经在其他地方定义，这里删除重复声明

// 这个函数已经在其他地方定义，这里删除重复声明

// 请求所有在线用户
export function requestAllOnlineUsers() {
  // 使用wsClient.stompClient而不是全局变量stompClient
  if (!wsClient.stompClient || !wsClient.stompClient.connected) {
    console.warn('WebSocket未连接，无法请求在线用户列表')
    return Promise.resolve({})
  }

  try {
    const userStore = useUserStore()
    const token = userStore.token

    wsClient.stompClient.publish({
      destination: '/app/getAllOnlineUsers',
      headers: {
        'Authorization': `Bearer ${token}`  // 只保留标准Authorization头
      },
      body: JSON.stringify({
        timestamp: new Date().getTime()
      })
    })
    console.log('已发送获取在线用户列表请求')
    return Promise.resolve(true)
  } catch (error) {
    console.error('请求在线用户列表失败:', error)
    return Promise.resolve(false)
  }
}

// 获取WebSocket客户端
export function getWebSocketClient() {
  return wsClient.stompClient
}

// ========================
// WebSocket 事件监听
// ========================
function setupStompEventListeners() {
  // 使用wsClient.stompClient而不是全局变量stompClient
  if (!wsClient.stompClient) return

  // 连接成功
  wsClient.stompClient.onConnect = function (frame) {
    console.log('WebSocket 已连接 (onConnect)', frame)
    // 这里可以做重连后的订阅等操作
  }

  // 连接断开
  wsClient.stompClient.onWebSocketClose = function (event) {
    console.log('WebSocket 已断开 (onWebSocketClose)', event)
    // 可选：本地 UI 提示
  }
}

// 订阅主题
export function subscribeToTopics() {
  // 使用wsClient.stompClient而不是全局变量stompClient
  if (!wsClient.stompClient || !wsClient.stompClient.connected) {
    console.warn('STOMP客户端未连接，无法订阅主题')
    return Promise.reject(new Error('STOMP客户端未连接'))
  }

  const userStore = useUserStore()
  const userId = userStore.userInfo?.id

  if (!userId) {
    console.warn('用户未登录，无法订阅主题')
    return Promise.reject(new Error('用户未登录'))
  }

  return new Promise((resolve, reject) => {
    try {
      console.log('开始订阅流程...')
      const subscriptions = []

      // 1. 订阅用户私人消息 - 确保路径与后端完全匹配
      // 后端使用: messagingTemplate.convertAndSendToUser(receiverId.toString(), "/queue/private", messageDTO)
      console.log('正在订阅用户私人消息主题:', `/user/queue/private`)
      const privateSubscription = wsClient.stompClient.subscribe(`/user/queue/private`, (message) => {
        console.log('收到私人消息:', {
          destination: message.headers.destination,
          messageId: message.headers['message-id'],
          subscription: message.headers.subscription
        })
        console.log('消息体内容:', message.body)

        try {
          // 详细记录接收到的消息
          console.log('收到私人消息详情:')
          console.log('- 消息目标路径:', message.headers.destination)
          console.log('- 消息ID:', message.headers['message-id'])
          console.log('- 订阅ID:', message.headers.subscription)
          console.log('- 消息体内容:', message.body)
          console.log('- 当前用户ID:', userId)

          // 尝试解析为JSON
          const rawMessage = JSON.parse(message.body)
          console.log('解析后的原始消息:', rawMessage)

          // 验证必要字段
          if (!rawMessage.senderId || !rawMessage.receiverId) {
            console.error('消息缺少必要字段:', rawMessage)
            return
          }

          // 详细记录消息发送者和接收者
          console.log('消息发送者ID:', rawMessage.senderId, '接收者ID:', rawMessage.receiverId)
          console.log('是否是发给当前用户的消息:', rawMessage.receiverId == userId)
          console.log('是否是当前用户发送的消息:', rawMessage.senderId == userId)

          // 处理字段映射
          const chatMessage = {
            ...rawMessage,
            content: rawMessage.message || rawMessage.content,
            messageType: rawMessage.messageType || 'TEXT',
            isSelf: rawMessage.senderId == userId, // 使用==而不是===，避免类型不匹配问题
            time: rawMessage.time || new Date().toISOString()
          }

          console.log('处理后的消息对象:', chatMessage)
          console.log('准备触发handleChatMessage函数处理消息')
          handleChatMessage(chatMessage)
        } catch (error) {
          console.error('处理消息时发生错误:', error)
          console.log('尝试作为文本消息处理:', message.body)
          handleTextMessage(message.body)
        }
      })
      subscriptions.push(privateSubscription)
      console.log('私人消息主题订阅成功，订阅ID:', privateSubscription.id)

      // 2. 订阅全局消息
      console.log('正在订阅全局消息主题:', `/topic/messages`)
      const globalSubscription = wsClient.stompClient.subscribe(`/topic/messages`, (message) => {
        console.log('收到全局消息:', {
          destination: message.headers.destination,
          messageId: message.headers['message-id'],
          subscription: message.headers.subscription
        })
        console.log('消息体内容:', message.body)

        try {
          // 详细记录接收到的全局消息
          console.log('收到全局消息详情:')
          console.log('- 消息目标路径:', message.headers.destination)
          console.log('- 消息ID:', message.headers['message-id'])
          console.log('- 订阅ID:', message.headers.subscription)
          console.log('- 消息体内容:', message.body)
          console.log('- 当前用户ID:', userId)

          // 尝试解析为JSON
          const rawMessage = JSON.parse(message.body)
          console.log('解析后的原始消息:', rawMessage)

          // 验证必要字段
          if (!rawMessage.senderId || !rawMessage.receiverId) {
            console.error('消息缺少必要字段:', rawMessage)
            return
          }

          // 详细记录消息发送者和接收者
          console.log('消息发送者ID:', rawMessage.senderId, '接收者ID:', rawMessage.receiverId)
          console.log('是否是发给当前用户的消息:', rawMessage.receiverId == userId)
          console.log('是否是当前用户发送的消息:', rawMessage.senderId == userId)

          // 只处理与当前用户相关的消息
          if (rawMessage.senderId == userId || rawMessage.receiverId == userId) {
            // 处理字段映射
            const chatMessage = {
              ...rawMessage,
              content: rawMessage.message || rawMessage.content,
              messageType: rawMessage.messageType || 'TEXT',
              isSelf: rawMessage.senderId == userId, // 使用==而不是===，避免类型不匹配问题
              time: rawMessage.time || new Date().toISOString()
            }

            console.log('处理后的消息对象:', chatMessage)
            console.log('准备触发handleChatMessage函数处理全局消息')
            handleChatMessage(chatMessage)
          } else {
            console.log('全局消息与当前用户无关，忽略')
          }
        } catch (error) {
          console.error('处理消息时发生错误:', error)
          console.log('尝试作为文本消息处理:', message.body)
          handleTextMessage(message.body)
        }
      })
      subscriptions.push(globalSubscription)
      console.log('全局消息主题订阅成功，订阅ID:', globalSubscription.id)

      // 返回所有订阅
      resolve(subscriptions)
    } catch (error) {
      console.error('订阅过程中发生错误:', error)
      reject(error)
    }
  })
}

// 处理文本消息
function handleTextMessage(text) {
  console.log('处理文本消息:', text)

  // 触发文本消息事件
  window.dispatchEvent(new CustomEvent('websocket-text-message', {
    detail: { text }
  }))
}

// 处理聊天消息
function handleChatMessage(message) {
  console.log('开始处理聊天消息...')
  console.log('收到的消息对象:', message)

  try {
    // 获取当前用户ID
    const userStore = useUserStore()
    const currentUserId = userStore.userInfo?.id

    // 确保消息格式正确
    if (!message.senderId || !message.receiverId) {
      console.error('消息缺少必要字段:', message)
      return
    }

    // 详细记录消息发送者和接收者
    console.log('消息发送者ID:', message.senderId, '接收者ID:', message.receiverId)
    console.log('当前用户ID:', currentUserId)
    console.log('是否是发给当前用户的消息:', message.receiverId == currentUserId)
    console.log('是否是当前用户发送的消息:', message.senderId == currentUserId)

    // 规范化消息对象
    const normalizedMessage = {
      ...message,
      content: message.message || message.content, // 兼容两种字段名
      messageType: message.messageType || 'TEXT',
      time: message.time || new Date().toISOString(),
      // 明确设置isSelf字段，确保正确识别消息发送者
      isSelf: String(message.senderId) == String(currentUserId)
    }

    console.log('规范化后的消息:', normalizedMessage)
    console.log('消息是否是自己发送的:', normalizedMessage.isSelf)

    // 触发全局事件
    // 使用setTimeout延迟执行，避免递归更新
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('chat-message', {
        detail: normalizedMessage
      }))
      console.log('已触发chat-message事件')
    }, 0)

    // 触发刷新联系人列表事件 - 这是关键修复
    // 使用setTimeout延迟执行，避免递归更新
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('refresh-contacts', {
        detail: { message: normalizedMessage }
      }))
      console.log('已触发refresh-contacts事件')
    }, 0)

    // 注意：移除了force-refresh-chat事件，避免页面闪烁

    // 只有当消息是发给当前用户的（而不是当前用户发送的）时才播放提示音
    if (!normalizedMessage.isSelf) {
      console.log('收到他人发送的消息，播放提示音')
      try {
        // 使用相对路径，确保能找到音频文件
        const audio = new Audio('./notification.mp3')
        audio.play().catch(err => {
          console.warn('播放通知音频失败:', err)
          // 尝试备用路径
          try {
            const backupAudio = new Audio('./assets/notification.mp3')
            backupAudio.play().catch(backupErr => {
              console.warn('备用路径播放通知音频失败:', backupErr)
            })
          } catch (backupError) {
            console.warn('创建备用音频对象失败:', backupError)
          }
        })
      } catch (error) {
        console.warn('创建音频对象失败:', error)
      }
    } else {
      console.log('这是自己发送的消息，不播放提示音')
    }
  } catch (error) {
    console.error('处理聊天消息时出错:', error)
  }
}

// 尝试重新连接
function attemptReconnect() {
  // 使用wsClient的属性而不是全局变量
  if (wsClient.reconnectAttempts >= wsClient.maxReconnectAttempts) {
    console.log('达到最大重连次数')
    return
  }

  wsClient.reconnectAttempts++
  const delay = Math.min(1000 * Math.pow(2, wsClient.reconnectAttempts), 30000)

  console.log(`将在 ${delay}ms 后进行第 ${wsClient.reconnectAttempts} 次重连`)
  setTimeout(() => {
    if (!wsClient.stompClient?.connected) {
      initWebSocketClient() // 使用正确的函数名
    }
  }, delay)
}

// 发送已读回执
export function sendReadReceipt(messageId, senderId, receiverId) {
  // 使用wsClient.stompClient而不是全局变量stompClient
  if (!wsClient.stompClient || !wsClient.stompClient.connected) {
    console.warn('WebSocket未连接，无法发送已读回执')
    return false
  }

  try {
    wsClient.stompClient.publish({
      destination: '/app/readReceipt',
      body: JSON.stringify({
        messageId,
        senderId,
        receiverId,
        timestamp: new Date().toISOString()
      }),
      headers: { 'content-type': 'application/json' }
    })
    console.log('已读回执已发送，消息ID:', messageId)
    return true
  } catch (error) {
    console.error('发送已读回执失败:', error)
    return false
  }
}

// 发送聊天消息
export async function sendChatMessage(message, file = null, retryCount = 0) {
  console.log('发送聊天消息:', message);
  console.log('WebSocket连接状态:', wsClient.stompClient?.connected ? '已连接' : '未连接');

  // 检查WebSocket连接状态
  if (!wsClient.stompClient || !wsClient.stompClient.connected) {
    console.warn('WebSocket未连接，尝试重新连接...');

    // 如果是第一次尝试，尝试重新连接
    if (retryCount === 0) {
      try {
        // 尝试重新初始化WebSocket
        // 这里使用的是当前模块中定义的函数，不需要导入

        // 等待连接建立
        let attempts = 0;
        while (!wsClient.stompClient?.connected && attempts < 5) {
          await new Promise(resolve => setTimeout(resolve, 1000));
          attempts++;
          console.log(`等待WebSocket连接建立，尝试 ${attempts}/5`);
        }

        // 如果连接成功，继续发送消息
        if (wsClient.stompClient?.connected) {
          console.log('WebSocket重新连接成功，继续发送消息');
          return sendChatMessage(message, file, retryCount + 1);
        } else {
          console.error('WebSocket重新连接失败，无法发送消息');
          ElMessage.error({
            message: 'WebSocket连接失败，请稍后再试',
            duration: 3000
          });
          return { code: 500, message: 'WebSocket连接失败' };
        }
      } catch (error) {
        console.error('WebSocket重新连接出错:', error);
        ElMessage.error({
          message: 'WebSocket连接错误，请稍后再试',
          duration: 3000
        });
        return { code: 500, message: 'WebSocket连接错误' };
      }
    } else {
      // 如果已经尝试过重连，直接返回错误
      ElMessage.warning({
        message: 'WebSocket未连接，无法发送消息',
        duration: 2000
      });
      return { code: 500, message: 'WebSocket未连接' };
    }
  }

  const userStore = useUserStore();
  const token = userStore.token;

  try {
    // 检查消息必要字段
    if (!message.senderId || !message.receiverId) {
      console.error('消息缺少必要字段:', message);
      ElMessage.error({
        message: '消息格式不正确',
        duration: 2000
      });
      return { code: 400, message: '消息格式不正确' };
    }

    // 确保消息类型字段存在，默认为TEXT
    if (!message.messageType) {
      message.messageType = 'TEXT';
    }

    // 根据消息类型选择发送方式
    const messageType = message.messageType || 'TEXT';

    // 如果有文件或消息类型是媒体类型，使用HTTP API发送
    if (file || ['IMAGE', 'VIDEO', 'AUDIO', 'FILE'].includes(messageType)) {
      console.log(`使用HTTP API发送${messageType}类型消息`);

      // 引入发送私聊消息的API方法
      const { sendPrivateMessage } = await import('@/api/chat');

      // 创建FormData对象
      const formData = new FormData();

      // 添加消息对象各字段
      formData.append('senderId', message.senderId);
      formData.append('receiverId', message.receiverId);
      formData.append('message', message.message || message.content || '');
      formData.append('messageType', messageType);

      // 添加文件（如果有）
      if (file) {
        formData.append('file', file);
      }

      // 添加音频或视频时长（如果有）
      if (messageType === 'AUDIO' && message.audioDuration) {
        formData.append('audioDuration', message.audioDuration);
      } else if (messageType === 'VIDEO' && message.videoDuration) {
        formData.append('videoDuration', message.videoDuration);
      }

      try {
        // 使用API方法发送消息
        const response = await sendPrivateMessage(formData);
        console.log(`已成功发送${messageType}类型消息`, file ? `包含文件: ${file.name}` : '');
        console.log('API响应:', response);

        // 返回标准格式的响应
        return {
          code: 200,
          message: '发送成功',
          data: response.data
        };
      } catch (error) {
        console.error('发送消息失败:', error);
        throw error;
      }
    } else {
      // 纯文本消息和表情通过WebSocket发送
      console.log('使用WebSocket发送文本/表情消息');
      console.log('消息内容:', message);
      console.log('目标路径:', '/app/privateMessage');

      // 添加时间戳和唯一ID，确保消息唯一性
      const tempId = `temp-${Date.now()}`;
      const messageWithTimestamp = {
        ...message,
        timestamp: new Date().getTime(),
        clientMessageId: tempId // 添加客户端消息ID用于跟踪
      };

      // 发送消息
      wsClient.stompClient.publish({
        destination: '/app/privateMessage',
        headers: {
          'Authorization': `Bearer ${token}`,  // 标准Authorization头
          'content-type': 'application/json'
        },
        body: JSON.stringify(messageWithTimestamp)
      });

      console.log('已成功通过WebSocket发送文本消息');

      // 触发消息发送事件，用于更新UI
      window.dispatchEvent(new CustomEvent('message-sent', {
        detail: {
          message: messageWithTimestamp,
          isRead: false // 初始状态为未读
        }
      }));

      // 返回成功响应，使用临时ID
      // 实际ID将通过WebSocket消息更新
      return {
        code: 200,
        message: '发送成功',
        data: {
          id: tempId,
          timestamp: messageWithTimestamp.timestamp,
          clientMessageId: tempId
        }
      };
    }
  } catch (error) {
    console.error('发送消息失败:', error);
    ElMessage.error({
      message: '发送消息失败',
      duration: 2000
    });
    return {
      code: 500,
      message: error.message || '发送消息失败'
    };
  }
}

// 检查WebSocket连接状态函数已在上面定义，这里不再重复定义

export const disconnect = (...args) => wsClient.disconnect(...args)
