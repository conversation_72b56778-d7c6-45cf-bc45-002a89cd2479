package com.tomato.lostfoundsystem.utils;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 图像处理工具类
 */
@Slf4j
public class ImageUtils {

    /**
     * 调整图像大小并裁剪为正方形
     *
     * @param original 原始图像
     * @param size     目标尺寸（宽度和高度相同）
     * @return 处理后的图像
     */
    public static BufferedImage resizeAndCropToSquare(BufferedImage original, int size) {
        // 首先裁剪为正方形
        BufferedImage square = cropToSquare(original);
        // 然后调整大小
        return resize(square, size, size);
    }

    /**
     * 裁剪图像为正方形
     *
     * @param original 原始图像
     * @return 裁剪后的正方形图像
     */
    public static BufferedImage cropToSquare(BufferedImage original) {
        int width = original.getWidth();
        int height = original.getHeight();
        int size = Math.min(width, height);

        // 计算裁剪区域的起始坐标（居中裁剪）
        int x = (width - size) / 2;
        int y = (height - size) / 2;

        // 裁剪图像
        return original.getSubimage(x, y, size, size);
    }

    /**
     * 调整图像大小
     *
     * @param original 原始图像
     * @param width    目标宽度
     * @param height   目标高度
     * @return 调整大小后的图像
     */
    public static BufferedImage resize(BufferedImage original, int width, int height) {
        // 创建新的图像
        BufferedImage resized = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = resized.createGraphics();

        // 设置渲染质量
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制调整大小后的图像
        g.drawImage(original, 0, 0, width, height, null);
        g.dispose();

        return resized;
    }

    /**
     * 将图像转换为输入流
     *
     * @param image     图像
     * @param formatName 图像格式名称（如"jpg"、"png"）
     * @return 图像输入流
     * @throws IOException 如果转换过程中发生错误
     */
    public static InputStream imageToInputStream(BufferedImage image, String formatName) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(image, formatName, os);
        return new ByteArrayInputStream(os.toByteArray());
    }

    /**
     * 生成默认头像（基于用户名首字符）
     *
     * @param username 用户名
     * @param userId   用户ID
     * @param size     头像尺寸
     * @return 生成的头像图像
     */
    public static BufferedImage generateDefaultAvatar(String username, Long userId, int size) {
        // 获取用户名首字符
        String firstChar = getFirstChar(username);

        // 获取背景颜色
        Color backgroundColor = getBackgroundColor(userId);

        // 创建图像
        BufferedImage image = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();

        // 设置渲染质量
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 绘制圆形背景
        g2d.setColor(backgroundColor);
        g2d.fillOval(0, 0, size, size);

        // 设置文本样式
        g2d.setColor(Color.WHITE);
        int fontSize = size / 2;
        Font font = new Font("Arial", Font.BOLD, fontSize);
        g2d.setFont(font);

        // 计算文本位置使其居中
        FontMetrics metrics = g2d.getFontMetrics(font);
        int textWidth = metrics.stringWidth(firstChar);
        int textHeight = metrics.getHeight();
        int x = (size - textWidth) / 2;
        int y = ((size - textHeight) / 2) + metrics.getAscent();

        // 绘制文本
        g2d.drawString(firstChar, x, y);

        g2d.dispose();
        return image;
    }

    /**
     * 获取用户名首字符
     *
     * @param username 用户名
     * @return 首字符（大写）
     */
    private static String getFirstChar(String username) {
        if (username == null || username.isEmpty()) {
            return "U";
        }

        // 获取第一个字符
        String firstChar = username.substring(0, 1);

        // 如果是英文字符，转为大写
        if (firstChar.matches("[a-zA-Z]")) {
            return firstChar.toUpperCase();
        }

        // 如果是中文或其他字符，直接返回
        return firstChar;
    }

    /**
     * 根据用户ID获取固定的背景色
     *
     * @param userId 用户ID
     * @return 背景色
     */
    private static Color getBackgroundColor(Long userId) {
        // 预定义的背景颜色
        Color[] colors = {
                new Color(0xf56a00), // 橙色
                new Color(0x7265e6), // 紫色
                new Color(0xffbf00), // 黄色
                new Color(0x00a2ae), // 青色
                new Color(0x1890ff), // 蓝色
                new Color(0x52c41a), // 绿色
                new Color(0xf5222d), // 红色
                new Color(0xfa541c), // 火红
                new Color(0x13c2c2), // 青柠
                new Color(0xeb2f96)  // 粉色
        };

        // 如果没有用户ID，随机选择一个颜色
        if (userId == null) {
            int randomIndex = (int) (Math.random() * colors.length);
            return colors[randomIndex];
        }

        // 根据用户ID选择固定的颜色
        int colorIndex = (int) (userId % colors.length);
        return colors[colorIndex];
    }
}
