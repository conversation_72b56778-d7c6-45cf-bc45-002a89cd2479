package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.Conversation;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface ConversationMapper {
    // 获取最后一条消息时间
    String getLastMessageTime(@Param("userId") Long userId, @Param("contactId") Long contactId);

    // 获取两个用户之间的会话
    Conversation getConversation(@Param("user1Id") Long user1Id, @Param("user2Id") Long user2Id);

    // 插入新的会话记录
    void insert(Conversation conversation);

    // 更新会话中的最后一条消息时间
    void updateLastMessageTime(@Param("sessionId") Long sessionId, @Param("lastMessageTime") Date lastMessageTime);

    // 获取所有联系人（即与用户有过聊天记录的其他用户）
    List<Long> getContacts(Long userId);

    // 获取用户的所有会话
    List<Conversation> getConversationsByUserId(@Param("userId") Long userId);

    // 更新会话信息
    void update(Conversation conversation);

    // 更新会话未读计数
    void updateUnreadCount(@Param("sessionId") Long sessionId, @Param("unreadCount") Integer unreadCount);

    // 更新会话状态
    void updateStatus(@Param("sessionId") Long sessionId, @Param("status") String status);

    // 更新会话置顶状态
    void updatePinned(@Param("sessionId") Long sessionId, @Param("isPinned") Boolean isPinned);

    // 更新会话静音状态
    void updateMuted(@Param("sessionId") Long sessionId, @Param("isMuted") Boolean isMuted);

    // 更新会话的最后一条消息信息
    void updateLastMessage(
        @Param("sessionId") Long sessionId,
        @Param("lastMessageId") Long lastMessageId,
        @Param("lastMessageContent") String lastMessageContent,
        @Param("lastMessageType") String lastMessageType,
        @Param("lastMessageTime") Date lastMessageTime
    );

    // 重置会话未读计数
    void resetUnreadCount(@Param("userId") Long userId, @Param("contactId") Long contactId);

    // 增加会话未读计数
    void incrementUnreadCount(@Param("userId") Long userId, @Param("contactId") Long contactId);
}
