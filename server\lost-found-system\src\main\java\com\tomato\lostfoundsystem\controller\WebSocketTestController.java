package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.User;
import com.tomato.lostfoundsystem.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.user.SimpUser;
import org.springframework.messaging.simp.user.SimpUserRegistry;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * WebSocket测试控制器
 */
@RestController
@RequestMapping("/api/websocket-test")
@Slf4j
public class WebSocketTestController {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private SimpUserRegistry userRegistry;

    /**
     * 发送测试消息到指定用户
     */
    @PostMapping("/send-to-user")
    public Result<Map<String, Object>> sendToUser(@RequestParam Long userId, @RequestParam String message) {
        try {
            // 获取用户名
            User user = userMapper.findById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 构建消息
            Map<String, Object> payload = new HashMap<>();
            payload.put("content", message);
            payload.put("timestamp", System.currentTimeMillis());
            payload.put("type", "TEST_MESSAGE");

            // 记录当前在线用户
            Set<String> onlineUsers = userRegistry.getUsers().stream()
                    .map(SimpUser::getName)
                    .collect(Collectors.toSet());
            
            log.info("【WebSocket测试】当前在线用户: {}", onlineUsers);
            log.info("【WebSocket测试】目标用户名: {}", user.getUsername());
            log.info("【WebSocket测试】目标用户是否在线: {}", onlineUsers.contains(user.getUsername()));

            // 使用用户名发送消息
            messagingTemplate.convertAndSendToUser(
                user.getUsername(),
                "/queue/private",
                payload
            );

            log.info("【WebSocket测试】已发送测试消息到用户: {}, 用户名: {}, 消息: {}", userId, user.getUsername(), message);
            log.info("【WebSocket测试】消息目标路径: /user/{}/queue/private", user.getUsername());

            Map<String, Object> data = new HashMap<>();
            data.put("sent", true);
            data.put("to", user.getUsername());
            data.put("message", message);
            data.put("onlineUsers", onlineUsers);
            data.put("isOnline", onlineUsers.contains(user.getUsername()));
            return Result.success(data);
        } catch (Exception e) {
            log.error("【WebSocket测试】发送测试消息失败: {}", e.getMessage(), e);
            return Result.error("发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 广播测试消息
     */
    @PostMapping("/broadcast")
    public Result<Map<String, Object>> broadcast(@RequestParam String message) {
        try {
            // 构建消息
            Map<String, Object> payload = new HashMap<>();
            payload.put("content", message);
            payload.put("timestamp", System.currentTimeMillis());
            payload.put("type", "BROADCAST_TEST");

            // 广播消息
            messagingTemplate.convertAndSend("/topic/broadcast", payload);

            log.info("【WebSocket测试】已广播测试消息: {}", message);

            Map<String, Object> data = new HashMap<>();
            data.put("sent", true);
            data.put("message", message);
            return Result.success(data);
        } catch (Exception e) {
            log.error("【WebSocket测试】广播测试消息失败: {}", e.getMessage(), e);
            return Result.error("广播失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前在线用户
     */
    @GetMapping("/online-users")
    public Result<Map<String, Object>> getOnlineUsers() {
        try {
            // 获取当前在线用户
            Set<String> onlineUsers = userRegistry.getUsers().stream()
                    .map(SimpUser::getName)
                    .collect(Collectors.toSet());
            
            log.info("【WebSocket测试】当前在线用户: {}", onlineUsers);
            
            Map<String, Object> data = new HashMap<>();
            data.put("onlineUsers", onlineUsers);
            data.put("count", onlineUsers.size());
            return Result.success(data);
        } catch (Exception e) {
            log.error("【WebSocket测试】获取在线用户失败: {}", e.getMessage(), e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }
}
