package com.tomato.lostfoundsystem.dto;

import com.tomato.lostfoundsystem.enums.MessageType;
import lombok.Data;

@Data
public class MessageDTO {
    private Long id;                // 消息ID（数据库生成）
    private Long senderId;          // 发送者的用户ID
    private Long receiverId;        // 接收者的用户ID
    private String message;         // 消息内容
    private MessageType messageType; // 消息类型（TEXT, IMAGE, AUDIO, VIDEO, DOCUMENT）
    private Long timestamp;         // 消息时间戳（毫秒）

    // 文件相关字段
    private String fileUrl;         // 文件URL
    private Long fileSize;          // 文件大小

    // 音频特有字段
    private Integer audioDuration;  // 音频时长（秒）

    // 视频特有字段
    private Integer videoDuration;  // 视频时长（秒）

    // 是否已读 - 唯一的状态标识
    private Boolean isRead;

    // 客户端消息ID，用于WebSocket消息状态更新
    private String clientMessageId;  // 客户端生成的临时消息ID

    // 确保 getId 方法存在
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
