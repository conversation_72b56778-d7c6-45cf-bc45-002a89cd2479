import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'

// 创建 axios 实例
const service = axios.create({
  baseURL: '/api',  // 添加统一的 API 前缀
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': 'any-value' // 添加 ngrok 请求头，跳过浏览器警告
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 添加请求ID，用于跟踪请求-响应对
    config.requestId = Date.now() + Math.random().toString(36).substring(2, 9)

    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      // 记录更详细的请求信息，包括请求体
      console.log(`请求: ${config.method.toUpperCase()} ${config.url}`)
      if (config.data) {
        console.log('请求数据:', config.data)
      }
    }

    // 添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    // 特殊处理登录请求
    if (config.url === '/user/login') {
      // 确保登录请求的数据格式正确
      if (config.data && typeof config.data === 'object') {
        // 移除不需要的字段
        if (config.data.agreement !== undefined) {
          delete config.data.agreement
        }

        // 确保验证码字段名称正确
        if (config.data.verifyCode) {
          // 后端可能期望的字段名是 captcha 或其他
          // config.data.captcha = config.data.verifyCode
          // 如果后端期望不同的字段名，取消注释上面的行并修改字段名
        }

        console.log('处理后的登录请求数据:', config.data)
      }
    }

    return config
  },
  error => {
    console.error('请求错误：', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 只在开发环境输出简化的响应日志
    if (process.env.NODE_ENV === 'development') {
      const requestId = response.config.requestId || '未知'
      console.log(`响应: ${response.config.method.toUpperCase()} ${response.config.url} - ${response.status}`)
    }

    // 如果响应不是预期的格式，可能是代理或服务器配置问题
    if (response.data && typeof response.data === 'string' && response.data.indexOf('<!DOCTYPE html>') !== -1) {
      console.error('收到HTML响应:', response.data)
      return Promise.reject(new Error('服务器配置错误'))
    }

    // 如果是直接返回的数据（不是标准响应格式）
    if (response.config.url.includes('/notifications/unread/count')) {
      // 确保返回标准格式
      return {
        code: 200,
        message: '获取未读通知数量成功',
        data: typeof response.data === 'number' ? response.data : 0
      }
    }

    const res = response.data

    // 聊天相关API的特殊处理 - 允许不同格式的成功响应
    if (response.config.url.includes('/chat/')) {
      console.log('处理聊天API响应:', res)

      // 如果已经是标准响应格式
      if (res && (res.code === 200 || res.status === 200)) {
        return res
      }

      // 如果是直接返回的数据数组或对象而不是包装在标准响应中
      if (Array.isArray(res) || (typeof res === 'object' && res !== null && !res.code)) {
        console.log('聊天API直接返回数据，自动包装为标准格式')
        return {
          code: 200,
          message: '操作成功',
          data: res
        }
      }
    }

    // 如果响应成功，直接返回数据
    if (res && res.code === 200) {
      return res
    }

    // 处理业务错误
    if (res && res.code !== 200) {
      // token相关错误
      if (res.code === 401) {
        console.log('Token无效或过期，清除用户信息')
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        router.push('/login')
        ElMessage.error({
          message: '登录已过期，请重新登录',
          duration: 2000
        })
        return Promise.reject(new Error(res.message || '登录已过期'))
      }

      // 对于400状态码（通常表示无数据或参数错误），直接返回响应
      if (res.code === 400) {
        return res
      }

      // 其他业务错误
      const errorMsg = res.message || '操作失败'
      ElMessage.error(errorMsg)
      return Promise.reject(new Error(errorMsg))
    }

    // 如果响应没有code字段，但有data字段，则视为成功响应
    if (res && !res.code && (res.data !== undefined)) {
      console.log('响应没有code字段，但有data字段，视为成功响应')
      return {
        code: 200,
        message: '操作成功',
        data: res.data
      }
    }

    // 如果响应既没有code字段，也没有data字段，则将整个响应包装为data
    if (res && !res.code && (res.data === undefined)) {
      console.log('响应既没有code也没有data字段，整个响应作为data返回')
      return {
        code: 200,
        message: '操作成功',
        data: res
      }
    }

    return res
  },
  error => {
    // 记录错误响应详情
    const requestId = error.config?.requestId || '未知'

    // 如果是401或403错误，且用户未登录，则静默处理
    if ((error.response?.status === 401 || error.response?.status === 403) &&
        !localStorage.getItem('token')) {
      console.log(`请求ID: ${requestId} - 未登录用户的401/403错误，静默处理:`, {
        url: error.config?.url,
        method: error.config?.method
      })
    } else {
      // 其他错误正常记录
      console.error(`请求ID: ${requestId} - 响应错误:`, {
        url: error.config?.url,
        method: error.config?.method,
        params: error.config?.params,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      })

      // 记录错误堆栈
      console.error(`请求ID: ${requestId} - 错误堆栈:`, error.stack)
    }

    // 检查是否有自定义错误处理器
    if (error.config?.errorHandler) {
      console.log(`请求ID: ${requestId} - 使用自定义错误处理器`)
      return error.config.errorHandler(error)
    }

    // 对于标记消息已读的错误响应，添加更多日志并特殊处理
    if (error.config?.url?.includes('/read') && error.config?.method === 'post') {
      console.error(`请求ID: ${requestId} - 标记消息已读错误详情:`, {
        url: error.config.url,
        params: error.config.params,
        status: error.response?.status,
        data: error.response?.data
      })

      // 特殊处理标记消息已读的错误，返回成功响应而不是抛出错误
      if (error.response?.status === 500 && error.response?.data?.message?.includes('消息不存在')) {
        console.log('标记消息已读时消息不存在，返回成功响应')
        return {
          code: 200,
          message: '消息可能已被删除，已在本地标记为已读',
          data: null
        }
      }
    }

    // 处理网络错误
    if (!error.response) {
      ElMessage.error('网络错误，请检查网络连接')
      return Promise.reject(error)
    }

    // 处理 401 错误
    if (error.response.status === 401) {
      // 清除本地 token
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')

      // 显示提示信息
      ElMessage.error('登录状态已失效，请重新登录')

      // 跳转到登录页
      router.push('/login')
    } else if (error.response.status === 404) {
      ElMessage.error('请求的资源不存在')
    } else if (error.response.status >= 500) {
      // 对于聊天相关API的500错误，特殊处理
      if (error.config?.url?.includes('/chat/')) {
        console.log('处理聊天API 500错误，返回空数据而非报错')
        ElMessage.warning({
          message: '获取聊天数据失败，服务器可能正在维护',
          duration: 3000
        })

        // 根据URL确定返回什么类型的空数据
        if (error.config.url.includes('/contacts/')) {
          console.log('返回空联系人列表')
          return {
            code: 200,
            message: '操作成功(空数据)',
            data: []
          }
        } else if (error.config.url.includes('/chatHistory/')) {
          console.log('返回空聊天历史')
          return {
            code: 200,
            message: '操作成功(空数据)',
            data: {
              list: [],
              total: 0,
              hasNextPage: false
            }
          }
        } else {
          // 其他聊天API
          return {
            code: 200,
            message: '操作成功(空数据)',
            data: null
          }
        }
      } else {
        // 非聊天API的500错误，显示常规错误提示
        ElMessage.error({
          message: '服务器错误，请稍后重试',
          duration: 2000
        })
      }
    } else {
      // 对于聊天相关API，尝试更灵活地处理错误响应
      if (error.config?.url?.includes('/chat/')) {
        console.log('处理聊天API错误响应，尝试获取可用数据')
        const errorData = error.response.data

        if (errorData) {
          // 如果错误响应中包含有效数据，尝试返回
          if (Array.isArray(errorData) || (typeof errorData === 'object' && errorData !== null)) {
            console.log('从错误响应中提取有效数据:', errorData)
            return {
              code: 200,
              message: '操作成功',
              data: errorData
            }
          }
        }
      }

      // 处理其他错误
      return error.response.data
    }

    return Promise.reject(error)
  }
)

export default service