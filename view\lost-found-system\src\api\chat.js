import request from '@/utils/request'

// 批量发送离线消息
export function sendBatchMessages(batchData) {
  console.log('API调用 - 批量发送离线消息:', {
    messageCount: batchData.messages?.length || 0,
    deviceId: batchData.deviceId,
    batchId: batchData.batchId
  })

  return request({
    url: '/offline-messages/batch',
    method: 'post',
    data: batchData
  }).then(response => {
    console.log('批量发送离线消息响应:', response)
    return response
  }).catch(error => {
    console.error('批量发送离线消息失败:', error)
    throw error
  })
}

// 获取离线消息数量
export function getOfflineMessageCount(userId) {
  console.log('API调用 - 获取离线消息数量:', { userId })

  return request({
    url: `/offline-messages/count/${userId}`,
    method: 'get'
  }).then(response => {
    console.log('获取离线消息数量响应:', response)
    return response
  }).catch(error => {
    console.error('获取离线消息数量失败:', error)
    throw error
  })
}

// 发送私聊消息
export function sendPrivateMessage(data) {
  const isFormData = data instanceof FormData

  if (isFormData) {
    // 对于FormData（文件上传），使用data参数
    return request({
      url: '/chat/privateMessage',
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  } else {
    // 对于普通对象，使用params参数而不是data
    console.log('发送私聊消息 - 使用params传递参数:', data)
    return request({
      url: '/chat/privateMessage',
      method: 'post',
      params: data  // 使用params而不是data
    })
  }
}

// 获取联系人列表
export function getContacts(userId) {
  if (!userId) {
    console.error('getContacts: userId 不能为空');
    return Promise.reject(new Error('userId 不能为空'));
  }

  console.log('API调用 - 获取联系人列表:', { userId });
  console.log('请求URL:', `/chat/contacts/${userId}`);
  console.log('请求方法:', 'GET');
  console.log('请求头:', localStorage.getItem('token') ? '包含Authorization头' : '不包含Authorization头');

  // 设置最大重试次数
  const maxRetries = 2;
  let retryCount = 0;

  // 创建一个内部函数来处理重试逻辑
  const fetchWithRetry = () => {
    console.log(`发送获取联系人列表请求 - 尝试次数: ${retryCount + 1}/${maxRetries + 1}`);

    return request({
      url: `/chat/contacts/${userId}`,
      method: 'get'
    }).then(response => {
      console.log('获取联系人列表响应状态码:', response.code || response.status);
      console.log('获取联系人列表响应消息:', response.message);
      console.log('获取联系人列表响应数据类型:', typeof response.data);
      console.log('获取联系人列表响应数据:', response.data);

      if (response.data) {
        if (Array.isArray(response.data)) {
          console.log('联系人列表是数组，长度:', response.data.length);
          if (response.data.length > 0) {
            console.log('第一个联系人示例:', response.data[0]);
          }
        } else {
          console.log('联系人列表不是数组，而是:', typeof response.data);
        }
      } else {
        console.log('联系人列表数据为空');
      }

      return response;
    }).catch(error => {
      console.error('获取联系人列表请求失败:', error);
      console.error('错误详情:', error.response || error.message || error);
      console.error('错误堆栈:', error.stack);

      // 如果是服务器错误且未达到最大重试次数，则进行重试
      if (error.response && error.response.status >= 500 && retryCount < maxRetries) {
        retryCount++;
        console.log(`尝试第${retryCount}次重新获取联系人列表...`);
        console.log(`等待 ${1000 * retryCount}ms 后重试`);
        return new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
          .then(fetchWithRetry);
      }

      // 如果重试失败或达到最大重试次数，返回模拟数据以防止UI崩溃
      if (error.response && error.response.status >= 500) {
        console.log('服务器错误，返回空联系人列表作为降级处理');
        return {
          code: 200,
          message: '操作成功(离线数据)',
          data: []
        };
      }

      console.error('无法恢复的错误，抛出异常');
      throw error;
    });
  };

  return fetchWithRetry();
}

// 获取与指定用户的聊天记录
export function getChatHistory(userId, otherUserId, page = 1, size = 20, latestTimestamp = null) {
  if (!userId || !otherUserId) {
    console.error('getChatHistory: userId 和 otherUserId 不能为空');
    return Promise.reject(new Error('参数不能为空'));
  }

  console.log('API调用 - 获取聊天记录:', { userId, otherUserId, page, size, latestTimestamp });

  // 构建请求参数
  const params = { page, size };

  // 如果提供了最新时间戳，添加到请求参数中
  if (latestTimestamp) {
    params.latestTimestamp = latestTimestamp;
    console.log('包含最新时间戳参数:', latestTimestamp);
  }

  return request({
    url: `/chat/chatHistory/${userId}/${otherUserId}`,
    method: 'get',
    params
  }).then(response => {
    console.log('获取聊天记录响应:', response);
    return response;
  }).catch(error => {
    console.error('获取聊天记录请求失败:', error);
    throw error;
  });
}

// 标记消息为已读 - 使用WebSocket发送已读回执
export async function markMessageAsRead(messageId, userId, senderId) {
  if (!messageId || !userId) {
    console.error('markMessageAsRead: messageId 和 userId 不能为空', { messageId, userId });
    return Promise.reject(new Error('参数不能为空'));
  }

  // 检查是否是临时消息ID（以"temp-"开头的字符串）
  const isTempMessage = typeof messageId === 'string' && messageId.startsWith('temp-');

  console.log('API调用 - 标记消息为已读:', { messageId, userId, senderId, isTempMessage });

  // 如果是临时消息ID，直接返回成功响应，不发送请求
  if (isTempMessage) {
    console.log('检测到临时消息ID，跳过服务器请求，直接返回成功响应');
    return Promise.resolve({
      code: 200,
      message: '临时消息已在本地标记为已读',
      data: null
    });
  }

  try {
    // 检查WebSocket连接状态
    let wsConnected = false;
    try {
      const { isWebSocketConnected } = await import('@/utils/websocket/index');
      wsConnected = isWebSocketConnected();
      console.log('WebSocket连接状态:', wsConnected ? '已连接' : '未连接');
    } catch (error) {
      console.error('检查WebSocket连接状态失败:', error);
    }

    if (!wsConnected) {
      console.log('WebSocket未连接，无法发送已读回执');
      return Promise.resolve({
        code: 500,
        message: 'WebSocket未连接，无法发送已读回执',
        data: null
      });
    }

    // 使用WebSocket发送已读回执
    const { sendReadReceipt } = await import('@/utils/websocket/index');
    const result = await sendReadReceipt(Array.isArray(messageId) ? messageId : [messageId], senderId, userId);
    console.log('已发送已读回执，消息ID:', messageId, '结果:', result);

    return {
      code: result.code || 200,
      message: result.message || '消息已标记为已读',
      data: result.data || null
    };
  } catch (error) {
    console.error('发送已读回执失败:', error);
    return Promise.reject(error);
  }
}

// 批量标记用户与联系人之间的所有未读消息为已读 - 使用WebSocket发送已读回执
export async function markAllMessagesAsRead(contactId, userId, messageIds) {
  console.log('批量标记已读请求参数:', { contactId, userId, messageIds });

  if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
    console.warn('没有消息需要标记为已读');
    return Promise.resolve({
      code: 200,
      message: '没有消息需要标记为已读',
      data: null
    });
  }

  try {
    // 检查WebSocket连接状态
    let wsConnected = false;
    try {
      const { isWebSocketConnected } = await import('@/utils/websocket/index');
      wsConnected = isWebSocketConnected();
      console.log('WebSocket连接状态:', wsConnected ? '已连接' : '未连接');
    } catch (error) {
      console.error('检查WebSocket连接状态失败:', error);
    }

    if (!wsConnected) {
      console.log('WebSocket未连接，无法发送批量已读回执');
      return Promise.resolve({
        code: 500,
        message: 'WebSocket未连接，无法发送批量已读回执',
        data: null
      });
    }

    // 使用WebSocket发送批量已读回执
    const { sendReadReceipt } = await import('@/utils/websocket/index');
    const result = await sendReadReceipt(messageIds, contactId, userId);
    console.log('已发送批量已读回执，消息数量:', messageIds.length, '结果:', result);

    return {
      code: result.code || 200,
      message: result.message || '所有消息已标记为已读',
      data: result.data || null
    };
  } catch (error) {
    console.error('发送批量已读回执失败:', error);
    return Promise.reject(error);
  }
}

// 创建会话
export function createConversation(senderId, receiverId) {
  console.log('API调用 - 创建会话:', { senderId, receiverId });

  // 参数验证
  if (!senderId || !receiverId) {
    console.error('创建会话参数无效:', { senderId, receiverId });
    return Promise.reject(new Error('创建会话参数无效'));
  }

  return request({
    url: '/chat/createConversation',
    method: 'post',
    params: {
      senderId,
      receiverId
    }
  }).then(response => {
    console.log('创建会话响应:', response);
    return response;
  }).catch(error => {
    console.error('创建会话请求失败:', error);
    throw error;
  });
}

// 更新会话状态（归档、删除等）
export function updateConversationStatus(conversationId, status) {
  console.log('API调用 - 更新会话状态:', { conversationId, status });

  // 参数验证
  if (!conversationId || !status) {
    console.error('更新会话状态参数无效:', { conversationId, status });
    return Promise.reject(new Error('更新会话状态参数无效'));
  }

  return request({
    url: `/chat/session/${conversationId}/status`,
    method: 'put',
    params: { status }
  }).then(response => {
    console.log('更新会话状态响应:', response);
    return response;
  }).catch(error => {
    console.error('更新会话状态请求失败:', error);
    throw error;
  });
}

// 更新会话置顶状态
export function updateConversationPinned(conversationId, isPinned) {
  console.log('API调用 - 更新会话置顶状态:', { conversationId, isPinned });

  // 参数验证
  if (!conversationId && isPinned !== undefined) {
    console.error('更新会话置顶状态参数无效:', { conversationId, isPinned });
    return Promise.reject(new Error('更新会话置顶状态参数无效'));
  }

  return request({
    url: `/chat/session/${conversationId}/pin`,
    method: 'put',
    params: { isPinned }
  }).then(response => {
    console.log('更新会话置顶状态响应:', response);
    return response;
  }).catch(error => {
    console.error('更新会话置顶状态请求失败:', error);
    throw error;
  });
}

// 更新会话静音状态
export function updateConversationMuted(conversationId, isMuted) {
  console.log('API调用 - 更新会话静音状态:', { conversationId, isMuted });

  // 参数验证
  if (!conversationId && isMuted !== undefined) {
    console.error('更新会话静音状态参数无效:', { conversationId, isMuted });
    return Promise.reject(new Error('更新会话静音状态参数无效'));
  }

  return request({
    url: `/chat/session/${conversationId}/mute`,
    method: 'put',
    params: { isMuted }
  }).then(response => {
    console.log('更新会话静音状态响应:', response);
    return response;
  }).catch(error => {
    console.error('更新会话静音状态请求失败:', error);
    throw error;
  });
}

// 重置会话未读计数
export function resetUnreadCount(userId, contactId) {
  console.log('API调用 - 重置会话未读计数:', { userId, contactId });

  // 参数验证
  if (!userId || !contactId) {
    console.error('重置会话未读计数参数无效:', { userId, contactId });
    return Promise.reject(new Error('重置会话未读计数参数无效'));
  }

  return request({
    url: '/chat/session/reset-unread',
    method: 'put',
    params: { userId, contactId }
  }).then(response => {
    console.log('重置会话未读计数响应:', response);
    return response;
  }).catch(error => {
    console.error('重置会话未读计数请求失败:', error);
    throw error;
  });
}

// 增加会话未读计数
export function incrementUnreadCount(userId, contactId) {
  console.log('API调用 - 增加会话未读计数:', { userId, contactId });

  // 参数验证
  if (!userId || !contactId) {
    console.error('增加会话未读计数参数无效:', { userId, contactId });
    return Promise.reject(new Error('增加会话未读计数参数无效'));
  }

  return request({
    url: '/chat/session/increment-unread',
    method: 'put',
    params: { userId, contactId }
  }).then(response => {
    console.log('增加会话未读计数响应:', response);
    return response;
  }).catch(error => {
    console.error('增加会话未读计数请求失败:', error);
    throw error;
  });
}