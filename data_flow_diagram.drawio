<mxfile host="65bd71144e">
    <diagram id="C5RBs43oDa-KdzZeNtuy" name="数据流图">
        <mxGraphModel dx="1223" dy="871" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
                <mxCell id="0" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;校园失物招领系统数据流图&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="364" y="40" width="160" height="30" as="geometry" />
                </mxCell>
                
                <!-- 外部实体 -->
                <mxCell id="1" value="普通用户" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="40" y="200" width="30" height="60" as="geometry" />
                </mxCell>
                <mxCell id="2" value="管理员" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="40" y="400" width="30" height="60" as="geometry" />
                </mxCell>
                <mxCell id="3" value="短信服务" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="40" y="600" width="30" height="60" as="geometry" />
                </mxCell>
                <mxCell id="4" value="邮件服务" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="40" y="800" width="30" height="60" as="geometry" />
                </mxCell>
                
                <!-- 处理过程 -->
                <mxCell id="5" value="用户界面" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="200" y="180" width="120" height="60" as="geometry" />
                </mxCell>
                <mxCell id="6" value="管理界面" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="200" y="380" width="120" height="60" as="geometry" />
                </mxCell>
                <mxCell id="7" value="API网关" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="400" y="180" width="120" height="60" as="geometry" />
                </mxCell>
                <mxCell id="8" value="业务逻辑层" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="400" y="380" width="120" height="60" as="geometry" />
                </mxCell>
                <mxCell id="9" value="数据访问层" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="600" y="180" width="120" height="60" as="geometry" />
                </mxCell>
                <mxCell id="10" value="数据库" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="600" y="380" width="120" height="60" as="geometry" />
                </mxCell>
                
                <!-- 数据流 -->
                <mxCell id="11" value="用户操作请求" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="1" target="5">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="管理操作请求" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="2" target="6">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="HTTP请求" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="5" target="7">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="管理API请求" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="6" target="7">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="业务处理请求" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="7" target="8">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="数据查询/更新" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="8" target="9">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="SQL操作" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="9" target="10">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="查询结果" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="10" target="9">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="数据结果" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="9" target="8">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="业务处理结果" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="8" target="7">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="HTTP响应" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="7" target="5">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="管理API响应" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="7" target="6">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="界面展示" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="5" target="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="管理界面展示" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="6" target="2">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="短信通知" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="8" target="3">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="邮件通知" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="8" target="4">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="160" y="290" as="sourcePoint" />
                        <mxPoint x="250" y="120" as="targetPoint" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 图例说明 -->
                <mxCell id="27" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;图3-6 系统数据流图&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="364" y="900" width="160" height="30" as="geometry" />
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile> 