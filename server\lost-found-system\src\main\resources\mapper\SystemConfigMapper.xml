<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomato.lostfoundsystem.mapper.SystemConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="SystemConfigMap" type="com.tomato.lostfoundsystem.entity.SystemConfig">
        <id property="id" column="id"/>
        <result property="configKey" column="config_key"/>
        <result property="configValue" column="config_value"/>
        <result property="description" column="description"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 获取所有系统配置 -->
    <select id="getAllConfigs" resultMap="SystemConfigMap">
        SELECT id, config_key, config_value, description, created_at, updated_at
        FROM system_configs
        ORDER BY id ASC
    </select>

    <!-- 根据配置键获取配置 -->
    <select id="getConfigByKey" resultMap="SystemConfigMap">
        SELECT id, config_key, config_value, description, created_at, updated_at
        FROM system_configs
        WHERE config_key = #{configKey}
    </select>

    <!-- 更新配置值 -->
    <update id="updateConfigValue">
        UPDATE system_configs
        SET config_value = #{configValue}
        WHERE config_key = #{configKey}
    </update>

    <!-- 添加配置 -->
    <insert id="addConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO system_configs (config_key, config_value, description)
        VALUES (#{configKey}, #{configValue}, #{description})
    </insert>

    <!-- 删除配置 -->
    <delete id="deleteConfig">
        DELETE FROM system_configs
        WHERE config_key = #{configKey}
    </delete>

</mapper>
