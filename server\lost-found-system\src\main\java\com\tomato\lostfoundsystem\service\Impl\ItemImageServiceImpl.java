package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.ItemImage;
import com.tomato.lostfoundsystem.mapper.ItemImageMapper;
import com.tomato.lostfoundsystem.service.ItemImageService;
import com.tomato.lostfoundsystem.utils.AliyunOSSUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物品图片服务实现类
 */
@Slf4j
@Service
public class ItemImageServiceImpl implements ItemImageService {

    @Autowired
    private ItemImageMapper itemImageMapper;

    @Override
    @Transactional
    public List<String> saveItemImages(Long itemId, String itemType, List<MultipartFile> images) {
        // 检查itemId是否为null
        if (itemId == null) {
            log.error("保存图片失败：itemId为null");
            throw new IllegalArgumentException("itemId不能为null");
        }

        if (images == null || images.isEmpty()) {
            log.info("没有图片需要保存，itemId={}, itemType={}", itemId, itemType);
            return new ArrayList<>();
        }

        log.info("开始保存图片，itemId={}, itemType={}, 图片数量={}", itemId, itemType, images.size());

        List<String> imageUrls = new ArrayList<>();
        List<ItemImage> itemImages = new ArrayList<>();

        try {
            // 遍历图片列表，上传并保存
            for (int i = 0; i < images.size(); i++) {
                MultipartFile image = images.get(i);
                if (image != null && !image.isEmpty()) {
                    log.debug("处理第{}张图片，文件名: {}, 大小: {}", i + 1, image.getOriginalFilename(), image.getSize());

                    // 上传图片到OSS，使用业务类型作为目录
                    String imageUrl;
                    if ("LOST".equalsIgnoreCase(itemType)) {
                        imageUrl = AliyunOSSUtil.uploadLostImage(image);
                    } else if ("FOUND".equalsIgnoreCase(itemType)) {
                        imageUrl = AliyunOSSUtil.uploadFoundImage(image);
                    } else {
                        imageUrl = AliyunOSSUtil.uploadImage(image);
                    }
                    log.debug("第{}张图片上传成功，URL: {}", i + 1, imageUrl);
                    imageUrls.add(imageUrl);

                    // 创建物品图片对象
                    ItemImage itemImage = new ItemImage();
                    itemImage.setItemId(itemId);
                    itemImage.setItemType(itemType);
                    itemImage.setImageUrl(imageUrl);
                    itemImage.setSortOrder(i);
                    itemImage.setCreatedAt(LocalDateTime.now());

                    itemImages.add(itemImage);
                } else {
                    log.warn("第{}张图片为空，跳过处理", i + 1);
                }
            }

            // 批量保存物品图片
            if (!itemImages.isEmpty()) {
                log.info("准备批量保存{}张图片到数据库", itemImages.size());
                try {
                    // 打印每个图片对象的详细信息
                    for (int i = 0; i < itemImages.size(); i++) {
                        ItemImage img = itemImages.get(i);
                        log.info("图片[{}]详情: itemId={}, itemType={}, imageUrl={}, sortOrder={}",
                                i, img.getItemId(), img.getItemType(), img.getImageUrl(), img.getSortOrder());
                    }

                    int insertCount = itemImageMapper.batchInsertItemImages(itemImages);
                    log.info("成功保存{}张图片到数据库", insertCount);

                    // 验证插入结果
                    if (insertCount != itemImages.size()) {
                        log.warn("实际插入的图片数量({})与预期数量({})不一致", insertCount, itemImages.size());
                    }
                } catch (Exception e) {
                    log.error("批量插入图片时发生异常: {}", e.getMessage(), e);
                    // 尝试单个插入
                    log.info("尝试逐个插入图片...");
                    int successCount = 0;
                    for (ItemImage img : itemImages) {
                        try {
                            int result = itemImageMapper.insertItemImage(img);
                            if (result > 0) {
                                successCount++;
                                log.info("单个插入图片成功: itemId={}, itemType={}, imageUrl={}",
                                        img.getItemId(), img.getItemType(), img.getImageUrl());
                            }
                        } catch (Exception ex) {
                            log.error("单个插入图片失败: {}", ex.getMessage(), ex);
                        }
                    }
                    log.info("逐个插入完成，成功插入{}张图片", successCount);
                    if (successCount == 0) {
                        throw new RuntimeException("所有图片插入都失败", e);
                    }
                }
            } else {
                log.warn("没有有效的图片需要保存到数据库");
            }

            return imageUrls;
        } catch (Exception e) {
            log.error("保存物品图片失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存物品图片失败", e);
        }
    }

    @Override
    @Transactional
    public String saveItemImage(Long itemId, String itemType, MultipartFile image, Integer sortOrder) {
        if (image == null || image.isEmpty()) {
            return null;
        }

        try {
            // 上传图片到OSS，使用业务类型作为目录
            String imageUrl;
            if ("LOST".equalsIgnoreCase(itemType)) {
                imageUrl = AliyunOSSUtil.uploadLostImage(image);
            } else if ("FOUND".equalsIgnoreCase(itemType)) {
                imageUrl = AliyunOSSUtil.uploadFoundImage(image);
            } else {
                imageUrl = AliyunOSSUtil.uploadImage(image);
            }

            // 创建物品图片对象
            ItemImage itemImage = new ItemImage();
            itemImage.setItemId(itemId);
            itemImage.setItemType(itemType);
            itemImage.setImageUrl(imageUrl);
            itemImage.setSortOrder(sortOrder);
            itemImage.setCreatedAt(LocalDateTime.now());

            // 保存物品图片
            itemImageMapper.insertItemImage(itemImage);

            return imageUrl;
        } catch (Exception e) {
            log.error("保存物品图片失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存物品图片失败", e);
        }
    }

    @Override
    public List<ItemImage> getItemImages(Long itemId, String itemType) {
        try {
            return itemImageMapper.getImagesByItemId(itemId, itemType);
        } catch (Exception e) {
            log.error("获取物品图片失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getItemImageUrls(Long itemId, String itemType) {
        try {
            List<ItemImage> itemImages = itemImageMapper.getImagesByItemId(itemId, itemType);
            return itemImages.stream()
                    .map(ItemImage::getImageUrl)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取物品图片URL失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public List<String> saveItemImageUrls(Long itemId, String itemType, List<String> imageUrls) {
        // 检查itemId是否为null
        if (itemId == null) {
            log.error("保存图片URL失败：itemId为null");
            throw new IllegalArgumentException("itemId不能为null");
        }

        if (imageUrls == null || imageUrls.isEmpty()) {
            log.info("没有图片URL需要保存，itemId={}, itemType={}", itemId, itemType);
            return new ArrayList<>();
        }

        log.info("开始保存图片URL，itemId={}, itemType={}, URL数量={}", itemId, itemType, imageUrls.size());

        List<String> savedImageUrls = new ArrayList<>();
        List<ItemImage> itemImages = new ArrayList<>();

        try {
            // 遍历图片URL列表，创建图片对象
            for (int i = 0; i < imageUrls.size(); i++) {
                String imageUrl = imageUrls.get(i);
                if (imageUrl != null && !imageUrl.isEmpty()) {
                    log.debug("处理第{}张图片URL: {}", i + 1, imageUrl);
                    savedImageUrls.add(imageUrl);

                    // 创建物品图片对象
                    ItemImage itemImage = new ItemImage();
                    itemImage.setItemId(itemId);
                    itemImage.setItemType(itemType);
                    itemImage.setImageUrl(imageUrl);
                    itemImage.setSortOrder(i);
                    itemImage.setCreatedAt(LocalDateTime.now());

                    itemImages.add(itemImage);
                } else {
                    log.warn("第{}张图片URL为空，跳过处理", i + 1);
                }
            }

            // 批量保存物品图片
            if (!itemImages.isEmpty()) {
                log.info("准备批量保存{}张图片URL到数据库", itemImages.size());
                try {
                    int insertCount = itemImageMapper.batchInsertItemImages(itemImages);
                    log.info("成功保存{}张图片URL到数据库", insertCount);

                    // 验证插入结果
                    if (insertCount != itemImages.size()) {
                        log.warn("实际插入的图片URL数量({})与预期数量({})不一致", insertCount, itemImages.size());
                    }
                } catch (Exception e) {
                    log.error("批量插入图片URL时发生异常: {}", e.getMessage(), e);
                    // 尝试单个插入
                    log.info("尝试逐个插入图片URL...");
                    int successCount = 0;
                    for (ItemImage img : itemImages) {
                        try {
                            int result = itemImageMapper.insertItemImage(img);
                            if (result > 0) {
                                successCount++;
                                log.info("单个插入图片URL成功: itemId={}, itemType={}, imageUrl={}",
                                        img.getItemId(), img.getItemType(), img.getImageUrl());
                            }
                        } catch (Exception ex) {
                            log.error("单个插入图片URL失败: {}", ex.getMessage(), ex);
                        }
                    }
                    log.info("逐个插入完成，成功插入{}张图片URL", successCount);
                    if (successCount == 0) {
                        throw new RuntimeException("所有图片URL插入都失败", e);
                    }
                }
            } else {
                log.warn("没有有效的图片URL需要保存到数据库");
            }

            return savedImageUrls;
        } catch (Exception e) {
            log.error("保存物品图片URL失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存物品图片URL失败", e);
        }
    }

    @Override
    @Transactional
    public boolean deleteItemImages(Long itemId, String itemType) {
        try {
            itemImageMapper.deleteImagesByItemId(itemId, itemType);
            return true;
        } catch (Exception e) {
            log.error("删除物品图片失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteItemImage(Long imageId) {
        try {
            itemImageMapper.deleteImageById(imageId);
            return true;
        } catch (Exception e) {
            log.error("删除物品图片失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteImagesByIds(List<Long> imageIds) {
        if (imageIds == null || imageIds.isEmpty()) {
            return true;
        }

        try {
            itemImageMapper.deleteImagesByIds(imageIds);
            return true;
        } catch (Exception e) {
            log.error("批量删除物品图片失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateImageSortOrder(Long imageId, int sortOrder) {
        try {
            itemImageMapper.updateImageSortOrder(imageId, sortOrder);
            return true;
        } catch (Exception e) {
            log.error("更新图片排序失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public Result<Object> updateItemImages(Long itemId, String itemType,
                                           MultipartFile mainImage,
                                           List<MultipartFile> additionalImages,
                                           List<Long> retainImageIds) {
        try {
            // 1. 获取当前物品的所有图片
            List<ItemImage> currentImages = itemImageMapper.getImagesByItemId(itemId, itemType);
            Map<Long, ItemImage> currentImageMap = currentImages.stream()
                    .collect(Collectors.toMap(ItemImage::getId, image -> image));

            // 2. 处理主图
            String mainImageUrl = null;
            if (mainImage != null && !mainImage.isEmpty()) {
                // 上传新主图
                if (itemType.equalsIgnoreCase("LOST")) {
                    mainImageUrl = AliyunOSSUtil.uploadLostImage(mainImage);
                } else {
                    mainImageUrl = AliyunOSSUtil.uploadFoundImage(mainImage);
                }
            } else if (retainImageIds != null && !retainImageIds.isEmpty()) {
                // 使用保留的图片中的第一张作为主图
                Long mainImageId = retainImageIds.get(0);
                ItemImage mainImageObj = currentImageMap.get(mainImageId);
                if (mainImageObj != null) {
                    mainImageUrl = mainImageObj.getImageUrl();
                    // 从保留列表和当前图片映射中移除，避免重复处理
                    retainImageIds.remove(0);
                    currentImageMap.remove(mainImageId);
                }
            }

            // 3. 处理需要删除的图片
            List<Long> imagesToDelete = new ArrayList<>();
            for (ItemImage image : currentImages) {
                if (retainImageIds == null || !retainImageIds.contains(image.getId())) {
                    imagesToDelete.add(image.getId());
                    // 删除OSS中的图片文件
                    try {
                        String objectName = AliyunOSSUtil.extractObjectNameFromUrl(image.getImageUrl());
                        if (objectName != null) {
                            AliyunOSSUtil.deleteObject(objectName);
                            log.info("删除OSS图片文件成功: {}", objectName);
                        }
                    } catch (Exception e) {
                        log.error("删除图片文件失败: {}", e.getMessage(), e);
                        // 继续处理其他图片
                    }
                }
            }

            // 从数据库中删除不需要保留的图片
            if (!imagesToDelete.isEmpty()) {
                deleteImagesByIds(imagesToDelete);
                log.info("已删除{}张不需要保留的图片", imagesToDelete.size());
            }

            // 4. 处理需要保留的图片（更新排序）
            if (retainImageIds != null && !retainImageIds.isEmpty()) {
                for (int i = 0; i < retainImageIds.size(); i++) {
                    Long imageId = retainImageIds.get(i);
                    ItemImage image = currentImageMap.get(imageId);
                    if (image != null) {
                        // 更新排序
                        updateImageSortOrder(image.getId(), i);
                        log.info("更新图片排序成功: ID={}, 排序={}", image.getId(), i);
                    }
                }
            }

            // 5. 处理新增的额外图片
            if (additionalImages != null && !additionalImages.isEmpty()) {
                List<ItemImage> newImages = new ArrayList<>();
                int sortOrder = (retainImageIds != null) ? retainImageIds.size() : 0;

                for (MultipartFile file : additionalImages) {
                    if (file != null && !file.isEmpty()) {
                        // 上传图片
                        String imageUrl;
                        if (itemType.equalsIgnoreCase("LOST")) {
                            imageUrl = AliyunOSSUtil.uploadLostImage(file);
                        } else {
                            imageUrl = AliyunOSSUtil.uploadFoundImage(file);
                        }

                        // 创建图片记录
                        ItemImage newImage = new ItemImage();
                        newImage.setItemId(itemId);
                        newImage.setItemType(itemType);
                        newImage.setImageUrl(imageUrl);
                        newImage.setSortOrder(sortOrder++);
                        newImage.setCreatedAt(LocalDateTime.now());

                        newImages.add(newImage);
                    }
                }

                // 批量保存新图片
                if (!newImages.isEmpty()) {
                    itemImageMapper.batchInsertItemImages(newImages);
                    log.info("成功上传{}张新图片", newImages.size());
                }
            }

            return Result.success("图片更新成功");
        } catch (Exception e) {
            log.error("更新物品图片失败: {}", e.getMessage(), e);
            return Result.fail("更新物品图片失败: " + e.getMessage());
        }
    }
}
