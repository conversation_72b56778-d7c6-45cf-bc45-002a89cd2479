@echo off
echo ===================================
echo 正在启动Redis服务...
echo ===================================

:: 直接使用当前目录
set CONFIG_FILE=config.bat

:: 加载配置
call "%CONFIG_FILE%"

:: 检查Redis服务是否已经运行
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo Redis服务已经在运行中！
) else (
    :: 启动Redis服务
    echo 正在启动Redis服务...
    start "Redis Server" /min "%REDIS_PATH%\redis-server.exe" "%REDIS_PATH%\%REDIS_CONFIG%"

    :: 等待服务启动
    timeout /t %WAIT_TIME% /nobreak > nul

    :: 检查服务是否成功启动
    tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe">NUL
    if "%ERRORLEVEL%"=="0" (
        echo Redis服务启动成功！
    ) else (
        echo Redis服务启动失败，请检查配置！
    )
)

echo.
echo Redis服务状态：
tasklist /FI "IMAGENAME eq redis-server.exe"
echo.
echo ===================================

:: 不自动关闭窗口
pause
