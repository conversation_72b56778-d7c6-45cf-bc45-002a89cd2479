<template>
  <div class="user-profile">
    <div class="profile-container">
      <!-- 个人信息卡片 -->
      <el-card class="profile-card" shadow="hover">
        <div class="profile-header">
          <div class="avatar-section">
            <el-avatar
              :size="100"
              :src="userForm.avatar"
              :style="!userForm.avatar ? avatarStyle : {}"
              class="user-avatar"
            >
              {{ avatarText }}
            </el-avatar>

            <div class="avatar-upload-overlay">
              <el-upload
                class="avatar-uploader"
                :action="null"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleAvatarChange"
                accept="image/jpeg,image/png,image/gif"
              >
                <el-icon class="upload-icon"><Camera /></el-icon>
              </el-upload>
            </div>

            <!-- 头像裁剪组件 -->
            <AvatarCropper
              v-model:visible="cropperVisible"
              :img-file="selectedImage"
              @crop-success="handleCropSuccess"
              @crop-cancel="handleCropCancel"
            />
          </div>

          <div class="user-info">
            <h2 class="username">{{ userForm.username || '用户名' }}</h2>
            <div class="user-meta">
              <el-tag class="role-tag" size="small" effect="plain">{{ userForm.role || '普通用户' }}</el-tag>
              <span class="join-date">
                <el-icon><Calendar /></el-icon>
                注册于 {{ formatDate(userForm.createTime) }}
              </span>
            </div>
          </div>
        </div>

        <el-divider />

        <div class="profile-tabs">
          <el-tabs type="border-card">
            <!-- 基本信息标签页 -->
            <el-tab-pane>
              <template #label>
                <div class="tab-label">
                  <el-icon><InfoFilled /></el-icon>
                  <span>基本信息</span>
                </div>
              </template>

              <div class="tab-content">
                <el-descriptions :column="1" border>
                  <el-descriptions-item label="用户名">
                    <div class="description-content">
                      <el-icon><UserFilled /></el-icon>
                      <span>{{ userForm.username }}</span>
                    </div>
                  </el-descriptions-item>

                  <el-descriptions-item label="用户身份">
                    <div class="description-content">
                      <el-icon><Avatar /></el-icon>
                      <span>{{ userForm.role }}</span>
                    </div>
                  </el-descriptions-item>

                  <el-descriptions-item label="注册时间">
                    <div class="description-content">
                      <el-icon><Calendar /></el-icon>
                      <span>{{ formatDate(userForm.createTime) }}</span>
                    </div>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-tab-pane>

            <!-- 联系方式标签页 -->
            <el-tab-pane>
              <template #label>
                <div class="tab-label">
                  <el-icon><Message /></el-icon>
                  <span>联系方式</span>
                </div>
              </template>

              <div class="tab-content">
                <div class="tab-header">
                  <h3 class="tab-title">联系信息</h3>
                  <el-button
                    v-if="!isEditing"
                    type="primary"
                    @click="handleEdit"
                    class="edit-button"
                    size="small"
                    plain
                  >
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                </div>

                <el-form
                  ref="formRef"
                  :model="userForm"
                  :rules="formRules"
                  label-position="left"
                  :label-width="isEditing ? '80px' : 'auto'"
                  class="contact-form"
                >
                  <el-form-item
                    label="邮箱地址"
                    prop="email"
                    :rules="isEditing ? formRules.email : []"
                  >
                    <div class="input-wrapper">
                      <el-input
                        v-model="userForm.email"
                        :readonly="!isEditing"
                        :disabled="!isEditing"
                        :class="{
                          'readonly-input': !isEditing,
                          'editable-input': isEditing,
                          'input-error': emailError,
                          'input-success': emailSuccess
                        }"
                        placeholder="请输入邮箱地址"
                        @focus="handleFocus('email')"
                        @blur="handleBlur('email')"
                        @input="validateEmail"
                      >
                        <template #prefix>
                          <el-icon><Message /></el-icon>
                        </template>
                        <template #suffix v-if="isEditing">
                          <el-icon
                            v-if="emailError"
                            class="error-icon"
                          >
                            <Warning />
                          </el-icon>
                          <el-icon
                            v-if="emailSuccess"
                            class="success-icon"
                          >
                            <Check />
                          </el-icon>
                        </template>
                      </el-input>
                      <transition name="fade">
                        <div v-if="emailError && isEditing" class="error-message">
                          <el-icon><Warning /></el-icon>
                          <span>请输入有效的邮箱地址</span>
                        </div>
                      </transition>
                    </div>
                  </el-form-item>

                  <el-form-item
                    label="手机号码"
                    prop="phone"
                    :rules="isEditing ? formRules.phone : []"
                  >
                    <div class="input-wrapper">
                      <el-input
                        v-model="userForm.phone"
                        :readonly="!isEditing"
                        :disabled="!isEditing"
                        :class="{
                          'readonly-input': !isEditing,
                          'editable-input': isEditing,
                          'input-error': phoneError,
                          'input-success': phoneSuccess
                        }"
                        placeholder="请输入手机号码"
                        maxlength="11"
                        @input="formatPhoneNumber"
                        @focus="handleFocus('phone')"
                        @blur="handleBlur('phone')"
                      >
                        <template #prefix>
                          <el-icon><Phone /></el-icon>
                        </template>
                        <template #suffix v-if="isEditing">
                          <el-icon
                            v-if="phoneError"
                            class="error-icon"
                          >
                            <Warning />
                          </el-icon>
                          <el-icon
                            v-if="phoneSuccess"
                            class="success-icon"
                          >
                            <Check />
                          </el-icon>
                        </template>
                      </el-input>
                      <transition name="fade">
                        <div v-if="phoneError && isEditing" class="error-message">
                          <el-icon><Warning /></el-icon>
                          <span>请输入11位有效的手机号码</span>
                        </div>
                      </transition>
                    </div>
                  </el-form-item>

                  <div v-if="isEditing" class="form-actions">
                    <el-button
                      type="primary"
                      @click="handleSave"
                      :loading="isSaving"
                      class="action-button save-button"
                    >
                      <el-icon><Check /></el-icon>
                      保存
                    </el-button>
                    <el-button
                      @click="handleCancel"
                      class="action-button cancel-button"
                    >
                      <el-icon><Close /></el-icon>
                      取消
                    </el-button>
                  </div>
                </el-form>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getUserInfo, updateProfile, uploadAvatar as apiUploadAvatar } from '../../api/user'
import { getBackgroundColor, getFirstChar } from '../../utils/avatar'
import AvatarCropper from '../../components/AvatarCropper.vue'
import { useUserStore } from '../../stores'
import {
  UserFilled,
  Edit,
  Message,
  Phone,
  Calendar,
  Avatar,
  Check,
  Close,
  Warning,
  InfoFilled,
  Camera
} from '@element-plus/icons-vue'

const isEditing = ref(false)
const isSaving = ref(false)
const formRef = ref(null)
const basicFormRef = ref(null)

// 表单数据
const userForm = ref({
  username: '',
  email: '',
  phone: '',
  role: '',
  createTime: '',
  avatar: ''
})

// 头像相关
const isUploading = ref(false)
const avatarText = computed(() => getFirstChar(userForm.value.username))
const avatarStyle = computed(() => ({
  backgroundColor: getBackgroundColor(userForm.value.id),
  color: '#fff',
  fontSize: '36px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}))

// 头像裁剪相关
const cropperVisible = ref(false)
const selectedImage = ref(null)

// 保存原始数据，用于取消编辑时恢复
const originalData = ref(null)

// 表单验证规则
const formRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: ['blur', 'change'] }
  ]
}

// 格式化手机号
const formatPhoneNumber = (value) => {
  // 移除所有非数字字符
  const cleaned = value.replace(/\D/g, '')
  // 限制长度为11位
  userForm.value.phone = cleaned.substring(0, 11)

  // 验证手机号格式
  if (cleaned.length === 11) {
    validatePhone()
  } else {
    phoneSuccess.value = false
  }
}

// 验证邮箱格式
const validateEmail = () => {
  if (!userForm.value.email) {
    emailSuccess.value = false
    return
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  emailSuccess.value = emailRegex.test(userForm.value.email)
}

// 验证手机号格式
const validatePhone = () => {
  if (!userForm.value.phone) {
    phoneSuccess.value = false
    return
  }

  const phoneRegex = /^1[3-9]\d{9}$/
  phoneSuccess.value = phoneRegex.test(userForm.value.phone)
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '暂无数据'
  try {
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) {
      console.error('无效的日期格式:', date)
      return '日期格式错误'
    }
    return dateObj.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/\//g, '-')
  } catch (error) {
    console.error('日期格式化错误:', error)
    return '日期格式错误'
  }
}

// 获取用户信息
const fetchUserProfile = async () => {
  try {
    const res = await getUserInfo()
    console.log('获取到的响应:', res)

    if (res && typeof res === 'object') {
      const isSuccess = res.success || res.code === 200 || res.code === 0

      if (isSuccess && res.data) {
        const userData = res.data
        console.log('后端返回的原始数据:', userData)

        userForm.value = {
          id: userData.id,
          username: userData.username || '',
          email: userData.email || '',
          phone: userData.phone || '',
          role: userData.role === 'USER' ? '普通用户' : userData.role === 'ADMIN' ? '管理员' : userData.role || '',
          createTime: userData.createTime,
          avatar: userData.avatar || ''
        }

        console.log('处理后的表单数据:', userForm.value)

        // 保存原始数据
        originalData.value = { ...userForm.value }

        // 验证初始数据
        validateEmail()
        validatePhone()
      } else if (!res.data) {
        console.error('未获取到用户数据')
        ElMessage.error('未获取到用户数据')
      }
    } else {
      console.error('响应格式不正确:', res)
      ElMessage.error('获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息出错:', error)
    ElMessage.error(error.message || '获取用户信息失败')
  }
}

// 编辑按钮点击
const handleEdit = () => {
  isEditing.value = true
  // 保存当前数据，用于取消时恢复
  originalData.value = { ...userForm.value }
}

// 保存按钮点击
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    isSaving.value = true

    // 只更新邮箱和手机号
    const res = await updateProfile({
      email: userForm.value.email,
      phone: userForm.value.phone
    })

    if (res.success) {
      ElMessage.success('保存成功')
      isEditing.value = false
      await fetchUserProfile() // 重新获取最新数据
    } else {
      ElMessage.error(res.message || '保存失败')
    }
  } catch (error) {
    console.error('保存用户信息出错:', error)
    ElMessage.error('保存失败')
  } finally {
    isSaving.value = false
  }
}

// 取消编辑
const handleCancel = () => {
  ElMessage.info('已取消修改')
  isEditing.value = false
  // 恢复原始数据
  if (originalData.value) {
    userForm.value = { ...originalData.value }
  }
}

// 错误状态
const emailError = ref(false)
const phoneError = ref(false)
const emailSuccess = ref(false)
const phoneSuccess = ref(false)

// 输入框聚焦处理
const handleFocus = (field) => {
  if (field === 'email') {
    emailError.value = false
  } else if (field === 'phone') {
    phoneError.value = false
  }
}

// 输入框失焦处理
const handleBlur = async (field) => {
  if (!isEditing.value) return

  try {
    await formRef.value.validateField(field)
    if (field === 'email') {
      emailError.value = false
      validateEmail()
    } else if (field === 'phone') {
      phoneError.value = false
      validatePhone()
    }
  } catch (error) {
    if (field === 'email') {
      emailError.value = true
      emailSuccess.value = false
    } else if (field === 'phone') {
      phoneError.value = true
      phoneSuccess.value = false
    }
  }
}

// 处理头像选择
const handleAvatarChange = (file) => {
  // 验证文件类型
  const isImage = ['image/jpeg', 'image/png', 'image/gif'].includes(file.raw.type)
  if (!isImage) {
    ElMessage.error('头像只能是 JPG、PNG 或 GIF 格式!')
    return
  }

  // 验证文件大小（5MB）
  const isLt5M = file.raw.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('头像大小不能超过 5MB!')
    return
  }

  // 设置选中的图片并打开裁剪器
  selectedImage.value = file.raw
  cropperVisible.value = true
}

// 处理裁剪成功
const handleCropSuccess = async (data) => {
  try {
    isUploading.value = true

    // 调用上传API
    const res = await apiUploadAvatar(data.file)

    if (res.code === 200 && res.data) {
      // 更新头像URL
      userForm.value.avatar = res.data
      ElMessage.success('头像上传成功')

      // 获取用户状态管理
      const userStore = useUserStore()

      // 更新本地存储的用户信息
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')

      // 添加时间戳参数到头像URL，避免缓存问题
      const timestamp = new Date().getTime()
      const avatarUrlWithTimestamp = res.data.includes('?')
        ? `${res.data}&t=${timestamp}`
        : `${res.data}?t=${timestamp}`

      // 更新本地存储
      userInfo.avatar = avatarUrlWithTimestamp
      localStorage.setItem('userInfo', JSON.stringify(userInfo))

      // 更新全局用户状态
      if (userStore && userStore.userInfo) {
        // 使用Pinia的action更新头像
        userStore.updateAvatar(avatarUrlWithTimestamp)

        // 触发全局事件，通知其他组件更新头像
        window.dispatchEvent(new CustomEvent('avatar-updated', {
          detail: { avatar: avatarUrlWithTimestamp }
        }))

        console.log('全局用户状态已更新，新头像URL:', avatarUrlWithTimestamp)
      }

      // 无需刷新页面，依靠响应式更新
    } else {
      ElMessage.error(res.message || '头像上传失败')
    }
  } catch (error) {
    console.error('上传头像出错:', error)
    ElMessage.error('头像上传失败: ' + (error.message || '未知错误'))
  } finally {
    isUploading.value = false
    selectedImage.value = null
  }
}

// 处理裁剪取消
const handleCropCancel = () => {
  selectedImage.value = null
}

// 组件挂载时获取用户信息
onMounted(() => {
  fetchUserProfile()
})
</script>

<style scoped>
.user-profile {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.profile-container {
  width: 100%;
}

.profile-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
  margin-bottom: 20px;
}

/* 头部个人信息样式 */
.profile-header {
  display: flex;
  align-items: center;
  padding: 20px;
}

.avatar-section {
  position: relative;
  margin-right: 24px;
}

.user-avatar {
  border: 3px solid #fff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.avatar-upload-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--el-color-primary);
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  border: 2px solid #fff;
}

.avatar-upload-overlay:hover {
  transform: scale(1.1);
  background-color: var(--el-color-primary-dark-2);
}

.upload-icon {
  color: #fff;
  font-size: 14px;
}

/* 头像裁剪相关样式 */
:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.cropper-container) {
  margin: 0 auto;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px;
  color: #303133;
}

.user-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.role-tag {
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
  border-radius: 4px;
}

.join-date {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 13px;
}

/* 标签页样式 */
.profile-tabs {
  padding: 0 20px 20px;
}

.profile-tabs :deep(.el-tabs--border-card) {
  box-shadow: none;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.tab-content {
  padding: 20px 0;
}

/* 基本信息标签页样式 */
.tab-content :deep(.el-descriptions) {
  padding: 0;
}

.tab-content :deep(.el-descriptions__body) {
  background-color: #fff;
}

.tab-content :deep(.el-descriptions__label) {
  width: 100px;
  color: #606266;
  font-weight: 600;
}

.description-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
}

.description-content .el-icon {
  color: var(--el-color-primary);
  font-size: 16px;
}

/* 联系方式标签页样式 */
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.edit-button {
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.edit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(var(--el-color-primary-rgb), 0.2);
}

.contact-form {
  max-width: 500px;
}

.input-wrapper {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
}

.readonly-input {
  background-color: #f5f7fa;
}

.readonly-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
  background-color: #f5f7fa;
}

.editable-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset !important;
}

.editable-input:focus-within :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
}

.input-error :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
  animation: shake 0.5s;
}

.input-success :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-success) inset !important;
}

.error-icon {
  color: var(--el-color-danger);
}

.success-icon {
  color: var(--el-color-success);
}

.error-message {
  position: absolute;
  bottom: -18px;
  left: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-color-danger);
  font-size: 12px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

.save-button {
  background: var(--el-color-primary);
  border: none;
}

.save-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(var(--el-color-primary-rgb), 0.3);
}

.cancel-button:hover {
  transform: translateY(-2px);
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .user-profile {
    padding: 12px;
  }

  .profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .avatar-section {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .user-meta {
    justify-content: center;
  }

  .profile-tabs {
    padding: 0 12px 12px;
  }

  .tab-content {
    padding: 16px 0;
  }

  .contact-form {
    max-width: 100%;
  }

  .form-actions {
    flex-direction: column;
  }

  .action-button {
    width: 100%;
  }
}
</style>