<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tomato.lostfoundsystem.mapper.ChatMessageMapper">
    <!-- 插入聊天消息 -->
    <insert id="insertChatMessage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chat_messages (sender_id, receiver_id, message, message_type, timestamp)
        VALUES (#{senderId}, #{receiverId}, #{message}, #{messageType}, #{timestamp})
    </insert>

    <!-- 更新聊天消息的音频和视频时长 -->
    <update id="updateChatMessageWithDuration">
        UPDATE chat_messages
        SET audio_duration = #{audioDuration}, video_duration = #{videoDuration}
        WHERE id = #{id}
    </update>
    <!-- 获取最后一条消息 -->
    <select id="getLastMessage" resultType="String">
        SELECT message
        FROM chat_messages
        WHERE (sender_id = #{userId} AND receiver_id = #{contactId})
           OR (sender_id = #{contactId} AND receiver_id = #{userId})
        ORDER BY timestamp DESC
        LIMIT 1
    </select>

    <!-- 定义LastMessageInfo的ResultMap -->
    <resultMap id="lastMessageInfoMap" type="com.tomato.lostfoundsystem.entity.LastMessageInfo">
        <association property="chatMessage" javaType="com.tomato.lostfoundsystem.entity.ChatMessage">
            <id property="id" column="id" />
            <result property="senderId" column="sender_id" />
            <result property="receiverId" column="receiver_id" />
            <result property="message" column="message" />
            <result property="timestamp" column="timestamp" />
            <result property="messageType" column="message_type" />
            <result property="audioDuration" column="audio_duration" />
            <result property="videoDuration" column="video_duration" />
            <!-- 将附件属性映射到ChatMessage的Attachment属性 -->
            <association property="Attachment" javaType="com.tomato.lostfoundsystem.entity.MessageAttachment">
                <id property="id" column="attachment_id" />
                <result property="messageId" column="id" />
                <result property="fileUrl" column="file_url" />
                <result property="fileType" column="file_type" />
                <result property="fileSize" column="file_size" />
            </association>
        </association>
    </resultMap>

    <!-- 获取最后一条消息及其附件 - 使用直接连接chat_messages表而非通过chat_sessions -->
    <select id="getLastMessageInfo" resultMap="lastMessageInfoMap">
        SELECT
            cm.id,                          -- 消息ID
            cm.sender_id,                   -- 发送者ID
            cm.receiver_id,                 -- 接收者ID
            cm.message,                     -- 消息内容
            cm.timestamp,                   -- 消息时间戳
            cm.message_type,                -- 消息类型
            cm.audio_duration,              -- 音频时长
            cm.video_duration,              -- 视频时长
            ma.id AS attachment_id,         -- 附件ID
            ma.file_url,                    -- 附件URL
            ma.file_type,                   -- 附件类型
            ma.file_size                    -- 附件大小
        FROM chat_messages cm
        LEFT JOIN message_attachments ma
            ON cm.id = ma.message_id
        WHERE (cm.sender_id = #{userId} AND cm.receiver_id = #{contactId})
            OR (cm.sender_id = #{contactId} AND cm.receiver_id = #{userId})
        ORDER BY cm.timestamp DESC
        LIMIT 1
    </select>

    <!-- 定义ChatMessage的ResultMap -->
    <resultMap id="chatMessageMap" type="com.tomato.lostfoundsystem.entity.ChatMessage">
        <id property="id" column="id" />
        <result property="senderId" column="sender_id" />
        <result property="receiverId" column="receiver_id" />
        <result property="message" column="message" />
        <result property="timestamp" column="timestamp" />
        <result property="messageType" column="message_type" />
        <result property="audioDuration" column="audio_duration" />
        <result property="videoDuration" column="video_duration" />
        <!-- 将附件属性映射到ChatMessage的Attachment属性（向后兼容） -->
        <association property="Attachment" javaType="com.tomato.lostfoundsystem.entity.MessageAttachment">
            <id property="id" column="attachment_id" />
            <result property="messageId" column="id" />
            <result property="fileUrl" column="file_url" />
            <result property="fileType" column="file_type" />
            <result property="fileSize" column="file_size" />
        </association>
    </resultMap>

    <!-- 定义带有多个附件的ChatMessage的ResultMap -->
    <resultMap id="chatMessageWithAttachmentsMap" type="com.tomato.lostfoundsystem.entity.ChatMessage">
        <id property="id" column="id" />
        <result property="senderId" column="sender_id" />
        <result property="receiverId" column="receiver_id" />
        <result property="message" column="message" />
        <result property="timestamp" column="timestamp" />
        <result property="messageType" column="message_type" />
        <result property="audioDuration" column="audio_duration" />
        <result property="videoDuration" column="video_duration" />
        <!-- 将附件属性映射到ChatMessage的attachments属性（集合） -->
        <collection property="attachments" ofType="com.tomato.lostfoundsystem.entity.MessageAttachment"
                    select="com.tomato.lostfoundsystem.mapper.MessageAttachmentMapper.getAllAttachmentsByMessageId"
                    column="id" />
    </resultMap>

    <!-- 获取用户与另一个用户的聊天记录及附件 -->
    <select id="getChatHistory" resultMap="chatMessageWithAttachmentsMap">
        <!-- 分页查询 -->
        SELECT
        cm.id,                                  <!-- 消息ID -->
        cm.sender_id,                           <!-- 发送者ID -->
        cm.receiver_id,                         <!-- 接收者ID -->
        cm.message,                             <!-- 消息内容 -->
        cm.timestamp,                           <!-- 消息时间戳 -->
        cm.message_type,                        <!-- 消息类型 -->
        cm.audio_duration,                      <!-- 音频时长 -->
        cm.video_duration                       <!-- 视频时长 -->
        FROM chat_messages cm
        WHERE (cm.sender_id = #{userId} AND cm.receiver_id = #{otherUserId})
        OR (cm.sender_id = #{otherUserId} AND cm.receiver_id = #{userId})
        ORDER BY cm.timestamp DESC
    </select>

    <!-- 根据消息ID获取消息及其附件 -->
    <select id="getMessageById" resultMap="chatMessageWithAttachmentsMap">
        SELECT
        cm.id,                                  <!-- 消息ID -->
        cm.sender_id,                           <!-- 发送者ID -->
        cm.receiver_id,                         <!-- 接收者ID -->
        cm.message,                             <!-- 消息内容 -->
        cm.timestamp,                           <!-- 消息时间戳 -->
        cm.message_type,                        <!-- 消息类型 -->
        cm.audio_duration,                      <!-- 音频时长 -->
        cm.video_duration                       <!-- 视频时长 -->
        FROM chat_messages cm
        WHERE cm.id = #{messageId}
    </select>

</mapper>
