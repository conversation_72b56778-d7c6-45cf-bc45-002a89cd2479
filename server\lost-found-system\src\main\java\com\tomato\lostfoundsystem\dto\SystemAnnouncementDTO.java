package com.tomato.lostfoundsystem.dto;

import lombok.Data;

/**
 * 系统公告数据传输对象
 */
@Data
public class SystemAnnouncementDTO {
    /**
     * 公告ID
     */
    private Long id;

    /**
     * 公告标题
     */
    private String title;

    /**
     * 公告内容
     */
    private String content;

    /**
     * 重要程度：NORMAL(普通), IMPORTANT(重要), URGENT(紧急)
     */
    private String importance;

    /**
     * 生效时间（字符串格式：yyyy-MM-dd HH:mm:ss）
     */
    private String startTime;

    /**
     * 结束时间（字符串格式：yyyy-MM-dd HH:mm:ss，可为空）
     */
    private String endTime;

    /**
     * 创建者ID
     */
    private Long createdBy;

    /**
     * 创建时间（字符串格式：yyyy-MM-dd HH:mm:ss）
     */
    private String createdAt;

    /**
     * 更新时间（字符串格式：yyyy-MM-dd HH:mm:ss）
     */
    private String updatedAt;

    /**
     * 状态：DRAFT(草稿), PUBLISHED(已发布), EXPIRED(已过期)
     */
    private String status;

    /**
     * 创建者用户名
     */
    private String createdByUsername;

    /**
     * 当前用户是否已读 (1表示已读，0表示未读)
     */
    private Integer isRead;

    /**
     * 用户阅读时间（字符串格式：yyyy-MM-dd HH:mm:ss）
     */
    private String readAt;
}
