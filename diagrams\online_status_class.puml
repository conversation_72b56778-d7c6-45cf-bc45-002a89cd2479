@startuml 在线状态管理类图

skinparam backgroundColor white
skinparam handwritten false
skinparam defaultFontName Arial
skinparam roundcorner 8
skinparam shadowing false

skinparam class {
  BackgroundColor #F0F8FF
  BorderColor #2C3E50
  ArrowColor #2C3E50
  FontColor #2C3E50
}

skinparam note {
  BackgroundColor #FFFACD
  BorderColor #DAA520
}

title 在线状态管理模块类图

package "在线状态管理" {
  class OnlineStatusService {
    - redisService: RedisService
    - sessionRegistry: SessionRegistry
    - kafkaTemplate: KafkaTemplate
    + isUserOnline(userId: Long): boolean
    + getUserStatus(userId: Long): UserStatus
    + setUserOnline(userId: Long, sessionId: String): void
    + setUserOffline(userId: Long): void
    + getOnlineUsers(): List<UserStatus>
    + getUserLastActiveTime(userId: Long): LocalDateTime
    + broadcastStatusChange(userId: Long, status: String): void
  }
  
  class UserStatus {
    - userId: Long
    - status: String
    - lastActiveTime: LocalDateTime
    - deviceInfo: String
    - sessionId: String
    + isOnline(): boolean
    + isAway(): boolean
  }
  
  class RedisService {
    - redisTemplate: RedisTemplate
    - valueOperations: ValueOperations
    - hashOperations: HashOperations
    + get(key: String): Object
    + set(key: String, value: Object): void
    + set(key: String, value: Object, timeout: long, unit: TimeUnit): void
    + delete(key: String): void
    + expire(key: String, timeout: long, unit: TimeUnit): void
    + hasKey(key: String): boolean
    + increment(key: String): Long
  }
  
  class SessionRegistry {
    - sessions: Map<String, WebSocketSession>
    - userSessionMap: Map<Long, Set<String>>
    + registerSession(userId: Long, sessionId: String, session: WebSocketSession): void
    + unregisterSession(sessionId: String): void
    + getSession(sessionId: String): WebSocketSession
    + getUserSessions(userId: Long): Set<WebSocketSession>
    + getSessionIds(userId: Long): Set<String>
    + getAllActiveSessions(): Map<String, WebSocketSession>
  }
  
  class WebSocketEventListener {
    - statusService: OnlineStatusService
    - sessionRegistry: SessionRegistry
    + handleConnect(event: SessionConnectedEvent): void
    + handleDisconnect(event: SessionDisconnectEvent): void
    + handleSubscribe(event: SessionSubscribeEvent): void
    + handleUnsubscribe(event: SessionUnsubscribeEvent): void
    + handleException(event: AbstractSubProtocolEvent): void
  }
  
  class HeartbeatService {
    - statusService: OnlineStatusService
    - sessionRegistry: SessionRegistry
    + processHeartbeat(userId: Long, sessionId: String): void
    + checkInactiveSessions(): void
    - closeInactiveSession(sessionId: String): void
    - markUserAway(userId: Long): void
    @Scheduled(fixedRate = 60000)
    + scheduledHeartbeatCheck(): void
  }
  
  class StatusNotificationService {
    - kafkaTemplate: KafkaTemplate
    - statusService: OnlineStatusService
    + notifyStatusChange(userId: Long, status: String): void
    + notifyFriendsStatusChange(userId: Long, status: String): void
    + handleStatusChangeEvent(event: StatusChangeEvent): void
  }
  
  interface WebSocketHandler {
    + handleMessage(session: WebSocketSession, message: WebSocketMessage): void
    + afterConnectionEstablished(session: WebSocketSession): void
    + afterConnectionClosed(session: WebSocketSession, status: CloseStatus): void
    + handleTransportError(session: WebSocketSession, exception: Throwable): void
  }
  
  class UserStatusController {
    - statusService: OnlineStatusService
    + getUserStatus(userId: Long): ResponseEntity<UserStatus>
    + getOnlineUsers(): ResponseEntity<List<UserStatus>>
    + getFriendsStatus(userId: Long): ResponseEntity<List<UserStatus>>
  }
}

' 关系定义
OnlineStatusService --> RedisService: 使用
OnlineStatusService --> SessionRegistry: 使用
OnlineStatusService --> UserStatus: 创建和管理
WebSocketEventListener --> OnlineStatusService: 使用
WebSocketEventListener --> SessionRegistry: 使用
HeartbeatService --> OnlineStatusService: 使用
HeartbeatService --> SessionRegistry: 使用
StatusNotificationService --> OnlineStatusService: 使用
UserStatusController --> OnlineStatusService: 使用
WebSocketHandler <|.. WebSocketEventListener: 实现

note bottom of OnlineStatusService
  核心服务类，负责管理用户在线状态，
  包括状态更新、查询和广播通知
end note

note right of RedisService
  封装Redis操作，用于存储用户状态信息，
  支持分布式部署环境下的状态同步
end note

note right of SessionRegistry
  管理WebSocket会话，维护用户ID与会话ID的映射关系，
  支持一个用户多设备同时在线
end note

note bottom of HeartbeatService
  处理客户端心跳包，检测非活跃会话，
  自动将长时间无响应的用户标记为离开状态
end note

@enduml