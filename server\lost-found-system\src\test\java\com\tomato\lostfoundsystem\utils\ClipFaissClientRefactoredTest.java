package com.tomato.lostfoundsystem.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ClipFaissClientRefactoredTest {

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private MultipartFile mockFile;

    @InjectMocks
    private ClipFaissClientRefactored clipFaissClient;

    private final String API_URL = "http://localhost:8000";
    private final String TEST_TEXT = "测试文本";
    private final byte[] TEST_IMAGE_BYTES = "测试图像数据".getBytes();
    private final byte[] TEST_FEATURE_VECTOR = "测试特征向量".getBytes();
    private final String TEST_BASE64_FEATURES = Base64.getEncoder().encodeToString(TEST_FEATURE_VECTOR);

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(clipFaissClient, "clipApiUrl", API_URL);
        ReflectionTestUtils.setField(clipFaissClient, "connectionTimeout", 3000);

        when(mockFile.getOriginalFilename()).thenReturn("test.jpg");
        try {
            when(mockFile.getBytes()).thenReturn(TEST_IMAGE_BYTES);
        } catch (Exception e) {
            fail("Mock file setup failed");
        }
    }

    @Test
    void testExtractTextFeatures() {
        // 准备模拟响应
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("features", TEST_BASE64_FEATURES);
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/extract_text_features"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        byte[] result = clipFaissClient.extractTextFeatures(TEST_TEXT);

        // 验证结果
        assertNotNull(result);
        assertArrayEquals(TEST_FEATURE_VECTOR, result);

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/extract_text_features"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }

    @Test
    void testExtractImageFeatures() {
        // 准备模拟响应
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("features", TEST_BASE64_FEATURES);
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/extract_image_features"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        byte[] result = clipFaissClient.extractImageFeatures(mockFile);

        // 验证结果
        assertNotNull(result);
        assertArrayEquals(TEST_FEATURE_VECTOR, result);

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/extract_image_features"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }

    @Test
    void testAddVectorToIndex() {
        // 准备模拟响应
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("vector_id", 123L);
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/add_vector"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        Long result = clipFaissClient.addVectorToIndex(1L, "LOST", TEST_FEATURE_VECTOR, "TEXT");

        // 验证结果
        assertNotNull(result);
        assertEquals(123L, result);

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/add_vector"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }

    @Test
    void testSearchSimilarVectors() {
        // 准备模拟响应
        Map<String, Object> item = new HashMap<>();
        item.put("item_id", 1L);
        item.put("similarity", 0.95);

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("results", List.of(item));
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/search_similar"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        List<Map<String, Object>> results = clipFaissClient.searchSimilarVectors(
                TEST_FEATURE_VECTOR, "FOUND", "TEXT", 10);

        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals(1L, results.get(0).get("item_id"));
        assertEquals(0.95, results.get(0).get("similarity"));

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/search_similar"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }

    @Test
    void testSearchMixedVectors() {
        // 准备模拟响应
        Map<String, Object> item = new HashMap<>();
        item.put("item_id", 1L);
        item.put("similarity", 0.95);
        item.put("match_type", "TEXT_TO_TEXT");

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("results", List.of(item));
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/search_mixed"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        List<Map<String, Object>> results = clipFaissClient.searchMixedVectors(
                TEST_FEATURE_VECTOR, TEST_FEATURE_VECTOR, "FOUND", 10);

        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals(1L, results.get(0).get("item_id"));
        assertEquals(0.95, results.get(0).get("similarity"));
        assertEquals("TEXT_TO_TEXT", results.get(0).get("match_type"));

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/search_mixed"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }

    @Test
    void testSearchByImageVector() {
        // 准备模拟响应
        Map<String, Object> item = new HashMap<>();
        item.put("item_id", 1L);
        item.put("similarity", 0.95);

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("image_to_image", List.of(item));
        responseBody.put("image_to_text", List.of());
        responseBody.put("combined", List.of(item));
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/image_search"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        Map<String, Object> result = clipFaissClient.searchByImageVector(
                TEST_FEATURE_VECTOR, "FOUND", 10);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.containsKey("image_to_image"));
        assertTrue(result.containsKey("combined"));

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/image_search"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }

    @Test
    void testSearchByTextVector() {
        // 准备模拟响应
        Map<String, Object> item = new HashMap<>();
        item.put("item_id", 1L);
        item.put("similarity", 0.95);

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("text_to_text", List.of(item));
        responseBody.put("text_to_image", List.of());
        responseBody.put("combined", List.of(item));
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/text_search"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        Map<String, Object> result = clipFaissClient.searchByTextVector(
                TEST_FEATURE_VECTOR, "FOUND", 10);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.containsKey("text_to_text"));
        assertTrue(result.containsKey("combined"));

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/text_search"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }

    @Test
    void testUploadAndExtractFeatures() {
        // 准备模拟响应
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("features", TEST_BASE64_FEATURES);
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/upload_and_extract"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        byte[] result = clipFaissClient.uploadAndExtractFeatures(mockFile, "LOST");

        // 验证结果
        assertNotNull(result);
        assertArrayEquals(TEST_FEATURE_VECTOR, result);

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/upload_and_extract"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }

    @Test
    void testSaveIndices() {
        // 准备模拟响应
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("success", true);
        responseBody.put("message", "所有索引已成功保存");
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/save_indices"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        boolean result = clipFaissClient.saveIndices();

        // 验证结果
        assertTrue(result);

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/save_indices"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }

    @Test
    void testGetIndexStats() {
        // 准备模拟响应
        Map<String, Object> lostTextStats = new HashMap<>();
        lostTextStats.put("vector_count", 10);
        lostTextStats.put("dimension", 512);

        Map<String, Object> statsMap = new HashMap<>();
        statsMap.put("LOST_TEXT", lostTextStats);

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("stats", statsMap);
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.getForEntity(
                eq(API_URL + "/index_stats"),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        Map<String, Object> result = clipFaissClient.getIndexStats();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.containsKey("LOST_TEXT"));

        // 验证调用
        verify(restTemplate).getForEntity(
                eq(API_URL + "/index_stats"),
                eq(Map.class)
        );
    }

    @Test
    void testRebuildIndex() {
        // 准备模拟响应
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("success", true);
        responseBody.put("message", "已重建索引");
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/rebuild_index"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        boolean result = clipFaissClient.rebuildIndex("LOST", "TEXT");

        // 验证结果
        assertTrue(result);

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/rebuild_index"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }
    @Test
    void testRemoveVectorFromIndex() {
        // 准备模拟响应
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("success", true);
        ResponseEntity<Map> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);

        // 设置模拟行为
        when(restTemplate.exchange(
                eq(API_URL + "/remove_vector"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        )).thenReturn(responseEntity);

        // 执行测试
        boolean result = clipFaissClient.removeVectorFromIndex(1L, "LOST", "TEXT");

        // 验证结果
        assertTrue(result);

        // 验证调用
        verify(restTemplate).exchange(
                eq(API_URL + "/remove_vector"),
                eq(HttpMethod.POST),
                any(HttpEntity.class),
                eq(Map.class)
        );
    }
}
