package com.tomato.lostfoundsystem.controller.admin;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.service.CDNCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CDN缓存控制器
 * 用于管理CDN缓存刷新和预热
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/cdn/cache")
public class CDNCacheController {

    @Resource
    private CDNCacheService cdnCacheService;

    /**
     * 刷新单个URL的CDN缓存
     */
    @PostMapping("/refresh-url")
    public Result<Boolean> refreshUrl(@RequestParam String url) {
        try {
            boolean success = cdnCacheService.refreshUrl(url);
            if (success) {
                return Result.success("CDN缓存刷新请求已提交", true);
            } else {
                return Result.fail("CDN缓存刷新请求提交失败");
            }
        } catch (Exception e) {
            log.error("刷新CDN缓存失败: {}", e.getMessage(), e);
            return Result.fail("刷新CDN缓存失败: " + e.getMessage());
        }
    }

    /**
     * 批量刷新URL的CDN缓存
     */
    @PostMapping("/refresh-urls")
    public Result<Integer> refreshUrls(@RequestBody List<String> urls) {
        try {
            int count = cdnCacheService.refreshUrls(urls);
            return Result.success("成功提交" + count + "个URL的CDN缓存刷新请求", count);
        } catch (Exception e) {
            log.error("批量刷新CDN缓存失败: {}", e.getMessage(), e);
            return Result.fail("批量刷新CDN缓存失败: " + e.getMessage());
        }
    }

    /**
     * 刷新目录的CDN缓存
     */
    @PostMapping("/refresh-directory")
    public Result<Boolean> refreshDirectory(@RequestParam String directory) {
        try {
            boolean success = cdnCacheService.refreshDirectory(directory);
            if (success) {
                return Result.success("目录CDN缓存刷新请求已提交", true);
            } else {
                return Result.fail("目录CDN缓存刷新请求提交失败");
            }
        } catch (Exception e) {
            log.error("刷新目录CDN缓存失败: {}", e.getMessage(), e);
            return Result.fail("刷新目录CDN缓存失败: " + e.getMessage());
        }
    }

    /**
     * 预热单个URL的CDN缓存
     */
    @PostMapping("/preload-url")
    public Result<Boolean> preloadUrl(@RequestParam String url) {
        try {
            boolean success = cdnCacheService.preloadUrl(url);
            if (success) {
                return Result.success("CDN缓存预热请求已提交", true);
            } else {
                return Result.fail("CDN缓存预热请求提交失败");
            }
        } catch (Exception e) {
            log.error("预热CDN缓存失败: {}", e.getMessage(), e);
            return Result.fail("预热CDN缓存失败: " + e.getMessage());
        }
    }

    /**
     * 批量预热URL的CDN缓存
     */
    @PostMapping("/preload-urls")
    public Result<Integer> preloadUrls(@RequestBody List<String> urls) {
        try {
            int count = cdnCacheService.preloadUrls(urls);
            return Result.success("成功提交" + count + "个URL的CDN缓存预热请求", count);
        } catch (Exception e) {
            log.error("批量预热CDN缓存失败: {}", e.getMessage(), e);
            return Result.fail("批量预热CDN缓存失败: " + e.getMessage());
        }
    }

    /**
     * 获取CDN配额信息
     */
    @GetMapping("/quota")
    public Result<Map<String, Object>> getQuotaInfo() {
        try {
            CDNCacheService.CDNQuotaInfo quotaInfo = cdnCacheService.getQuotaInfo();

            Map<String, Object> result = new HashMap<>();
            result.put("urlRefreshRemaining", quotaInfo.getUrlRefreshRemaining());
            result.put("dirRefreshRemaining", quotaInfo.getDirRefreshRemaining());
            result.put("preloadRemaining", quotaInfo.getPreloadRemaining());
            result.put("urlRefreshQuota", quotaInfo.getUrlRefreshQuota());
            result.put("dirRefreshQuota", quotaInfo.getDirRefreshQuota());
            result.put("preloadQuota", quotaInfo.getPreloadQuota());

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取CDN配额信息失败: {}", e.getMessage(), e);
            return Result.fail("获取CDN配额信息失败: " + e.getMessage());
        }
    }
}
