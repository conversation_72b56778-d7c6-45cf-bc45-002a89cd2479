<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tomato.lostfoundsystem.mapper.MessageReadStatusMapper">

    <!-- 插入已读状态 -->
    <insert id="insert" parameterType="MessageReadStatus">
        INSERT INTO message_read_status (message_id, user_id, is_read, read_at)
        VALUES (#{messageId}, #{userId}, #{isRead}, #{readAt})
    </insert>

    <!-- 更新已读状态 -->
    <update id="updateReadStatus">
        UPDATE message_read_status
        SET is_read = #{isRead}, read_at = NOW()
        WHERE message_id = #{messageId} AND user_id = #{userId}
    </update>

    <!-- 获取未读消息数量 -->
    <select id="countUnreadMessages" resultType="int">
        SELECT COUNT(*)
        FROM message_read_status mrs
        JOIN chat_messages cm ON cm.id = mrs.message_id
        WHERE mrs.user_id = #{userId}  <!-- 当前用户ID -->
        AND cm.sender_id = #{contactId}  <!-- 联系人ID -->
        AND mrs.is_read = 0  <!-- 只统计未读消息 -->
    </select>

    <!-- 获取消息的已读状态：1表示已读，0表示未读，null表示没有记录 -->
    <select id="getMessageReadStatus" resultType="java.lang.Integer">
        SELECT is_read
        FROM message_read_status
        WHERE message_id = #{messageId} AND user_id = #{userId}
    </select>

    <!-- 获取用户与联系人之间的未读消息ID列表 -->
    <select id="getUnreadMessageIds" resultType="java.lang.Long">
        SELECT mrs.message_id
        FROM message_read_status mrs
        JOIN chat_messages cm ON cm.id = mrs.message_id
        WHERE mrs.user_id = #{userId}  <!-- 当前用户ID -->
        AND cm.sender_id = #{contactId}  <!-- 联系人ID -->
        AND mrs.is_read = 0  <!-- 只获取未读消息 -->
        ORDER BY cm.timestamp ASC  <!-- 按时间升序排序 -->
    </select>

    <!-- 批量更新消息已读状态 -->
    <update id="batchUpdateReadStatus">
        UPDATE message_read_status
        SET is_read = #{isRead}, read_at = NOW()
        WHERE message_id IN
        <foreach collection="messageIds" item="messageId" open="(" separator="," close=")">
            #{messageId}
        </foreach>
        AND user_id = #{userId}
    </update>

</mapper>
