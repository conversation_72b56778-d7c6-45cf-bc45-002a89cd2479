/**
 * 联系人状态管理模块 (重构版)
 *
 * 该模块负责：
 * 1. 特定联系人在线状态的订阅和监控
 * 2. 联系人状态变化的通知
 * 3. 联系人状态的缓存管理
 * 4. 批量获取联系人状态
 *
 * 与其他模块的关系：
 * - onlineStatusService.js: 提供统一的在线状态管理服务
 *
 * 使用方式：
 * import { subscribeContactStatus, isContactOnline, getContactsStatus } from '@/utils/contact-status'
 */

import { 
  isUserOnline, 
  getUserLastActiveTime,
  checkUserOnlineStatus
} from '@/services/onlineStatusService'

// 获取WebSocket客户端
function getWebSocketClient() {
  // 从websocket.js中动态获取stompClient
  if (window.wsClient && window.wsClient.stompClient) {
    return window.wsClient.stompClient
  }
  return null
}

// 存储联系人在线状态
const contactStatus = new Map()

// 存储订阅状态
const subscriptions = new Map()

// 初始化状态
let initialized = false

/**
 * 初始化联系人状态管理
 */
export function initContactStatusManager() {
  if (initialized) {
    return
  }

  const stompClient = getWebSocketClient()
  if (!stompClient || !stompClient.connected) {
    // 监听WebSocket连接成功事件，连接成功后再初始化
    window.addEventListener('websocket-connected', initContactStatusManager, { once: true })
    return
  }

  // 监听在线状态变化事件
  window.addEventListener('online-status-changed', (event) => {
    const { userId, isOnline, timestamp } = event.detail
    
    // 更新联系人状态
    contactStatus.set(userId, {
      online: isOnline,
      lastActive: timestamp || Date.now()
    })
    
    // 触发状态更新事件
    window.dispatchEvent(new CustomEvent('contact-status-updated', {
      detail: {
        contactId: userId,
        status: contactStatus.get(userId)
      }
    }))
  })

  // 标记为已初始化
  initialized = true
}

/**
 * 订阅联系人在线状态
 * @param {string} contactId 联系人ID
 * @returns {boolean} 是否成功订阅
 */
export function subscribeContactStatus(contactId) {
  if (!contactId) {
    console.warn('联系人ID为空，无法订阅状态')
    return false
  }

  // 确保初始化
  if (!initialized) {
    initContactStatusManager()
  }

  // 检查是否已经订阅
  if (subscriptions.has(contactId)) {
    return true
  }

  const stompClient = getWebSocketClient()
  if (!stompClient || !stompClient.connected) {
    // 先检查全局在线状态
    const isOnline = isUserOnline(contactId)
    if (isOnline) {
      // 如果用户在全局在线列表中，更新联系人状态
      contactStatus.set(contactId, {
        online: true,
        lastActive: Date.now()
      })
    }

    return false
  }

  try {
    // 发送订阅请求
    stompClient.publish({
      destination: `/app/subscribeContactStatus/${contactId}`,
      body: JSON.stringify({
        timestamp: Date.now()
      })
    })

    // 记录订阅状态
    subscriptions.set(contactId, true)
    
    // 主动检查联系人状态
    checkUserOnlineStatus(contactId)

    return true
  } catch (error) {
    console.error(`订阅联系人 ${contactId} 的在线状态时出错:`, error)
    return false
  }
}

/**
 * 取消订阅联系人在线状态
 * @param {string} contactId 联系人ID
 * @returns {boolean} 是否成功取消订阅
 */
export function unsubscribeContactStatus(contactId) {
  if (!subscriptions.has(contactId)) {
    return true
  }

  const stompClient = getWebSocketClient()
  if (stompClient && stompClient.connected) {
    try {
      // 发送取消订阅请求
      stompClient.publish({
        destination: `/app/unsubscribeContactStatus/${contactId}`,
        body: JSON.stringify({
          timestamp: Date.now()
        })
      })
    } catch (error) {
      console.error(`发送取消订阅请求时出错:`, error)
    }
  }

  // 移除订阅记录
  subscriptions.delete(contactId)

  return true
}

/**
 * 批量获取联系人在线状态
 * @param {string[]} contactIds 联系人ID数组
 * @returns {Promise<Map<string, object>>} 联系人在线状态Map
 */
export function getContactsStatus(contactIds) {
  return new Promise((resolve) => {
    if (!contactIds || contactIds.length === 0) {
      resolve(new Map())
      return
    }

    // 从服务中获取状态
    const statuses = new Map()
    
    contactIds.forEach(id => {
      const isOnline = isUserOnline(id)
      const lastActive = getUserLastActiveTime(id) || Date.now()
      
      statuses.set(id, {
        online: isOnline,
        lastActive: lastActive
      })
      
      // 更新本地缓存
      contactStatus.set(id, {
        online: isOnline,
        lastActive: lastActive
      })
    })
    
    resolve(statuses)
  })
}

/**
 * 检查联系人是否在线
 * @param {string} contactId 联系人ID
 * @returns {boolean} 是否在线
 */
export function isContactOnline(contactId) {
  // 先检查联系人状态缓存
  const status = contactStatus.get(contactId)
  if (status) {
    return status.online
  }

  // 如果缓存中没有，检查全局在线状态
  return isUserOnline(contactId)
}

/**
 * 获取联系人最后活跃时间
 * @param {string} contactId 联系人ID
 * @returns {number|null} 最后活跃时间
 */
export function getContactLastActiveTime(contactId) {
  const status = contactStatus.get(contactId)
  if (status) {
    return status.lastActive
  }
  
  // 如果缓存中没有，从服务中获取
  return getUserLastActiveTime(contactId)
}

// 监听WebSocket连接成功事件
window.addEventListener('websocket-connected', initContactStatusManager)

// 导出默认对象
export default {
  initContactStatusManager,
  subscribeContactStatus,
  unsubscribeContactStatus,
  getContactsStatus,
  isContactOnline,
  getContactLastActiveTime
}
