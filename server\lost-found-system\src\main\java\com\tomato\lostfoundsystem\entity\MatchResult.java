package com.tomato.lostfoundsystem.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 匹配结果实体类
 * 记录每次匹配查询的具体结果项
 */
@Data
public class MatchResult {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 匹配历史ID
     */
    private Long matchHistoryId;
    
    /**
     * 匹配到的物品ID
     */
    private Long itemId;
    
    /**
     * 匹配到的物品类型（LOST/FOUND）
     */
    private String itemType;
    
    /**
     * 相似度分数
     */
    private BigDecimal similarityScore;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 物品详情（非数据库字段）
     */
    private Object itemDetail;
}
