/**
 * 通知工具函数
 * 用于处理通知类型、图标、颜色等
 */

import { Bell, Check, Close, InfoFilled, Link, Notification, User, Warning } from '@element-plus/icons-vue'

/**
 * 通知类型枚举
 */
export const NotificationType = {
  SYSTEM: 'SYSTEM',
  ADMIN: 'ADMIN',
  AUDIT_APPROVED: 'AUDIT_APPROVED',
  AUDIT_REJECTED: 'AUDIT_REJECTED',
  MATCH: 'MATCH',
  CLAIM: 'CLAIM',
  CLAIM_APPROVED: 'CLAIM_APPROVED',
  CLAIM_REJECTED: 'CLAIM_REJECTED',
  ANNOUNCEMENT: 'ANNOUNCEMENT'
}

/**
 * 获取通知类型
 * @param {Object} notification 通知对象
 * @returns {String} 通知类型
 */
export function getNotificationType(notification) {
  // 优先使用type字段
  if (notification.type) {
    return notification.type
  }

  // 兼容旧数据，根据标题推断类型
  if (!notification.title) return NotificationType.SYSTEM

  if (notification.title.includes('通过审核')) {
    return NotificationType.AUDIT_APPROVED
  } else if (notification.title.includes('未通过审核')) {
    return NotificationType.AUDIT_REJECTED
  } else if (notification.title.includes('已认领') || notification.title.includes('认领申请')) {
    return NotificationType.CLAIM
  } else if (notification.title.includes('系统公告') || notification.title.includes('新公告')) {
    return NotificationType.ANNOUNCEMENT
  } else if (notification.title.includes('匹配') || notification.title.includes('相似')) {
    return NotificationType.MATCH
  } else {
    return NotificationType.SYSTEM
  }
}

/**
 * 获取通知类型文本
 * @param {Object} notification 通知对象
 * @returns {String} 通知类型文本
 */
export function getNotificationTypeText(notification) {
  const type = getNotificationType(notification)

  switch (type) {
    case NotificationType.AUDIT_APPROVED:
      return '审核通过'
    case NotificationType.AUDIT_REJECTED:
      return '审核拒绝'
    case NotificationType.ADMIN:
      return '管理员通知'
    case NotificationType.CLAIM:
      return '认领通知'
    case NotificationType.CLAIM_APPROVED:
      return '认领通过'
    case NotificationType.CLAIM_REJECTED:
      return '认领拒绝'
    case NotificationType.MATCH:
      return '匹配通知'
    case NotificationType.ANNOUNCEMENT:
      return '系统公告'
    default:
      return '系统通知'
  }
}

/**
 * 获取通知图标
 * @param {Object} notification 通知对象
 * @returns {Object} 图标组件
 */
export function getNotificationIcon(notification) {
  const type = getNotificationType(notification)

  switch (type) {
    case NotificationType.AUDIT_APPROVED:
      return Check
    case NotificationType.AUDIT_REJECTED:
      return Close
    case NotificationType.ADMIN:
      return User
    case NotificationType.CLAIM:
    case NotificationType.CLAIM_APPROVED:
    case NotificationType.CLAIM_REJECTED:
      return InfoFilled
    case NotificationType.MATCH:
      return Link
    case NotificationType.ANNOUNCEMENT:
      return Notification
    default:
      return Bell
  }
}

/**
 * 获取通知标签类型
 * @param {Object} notification 通知对象
 * @returns {String} 标签类型
 */
export function getNotificationTagType(notification) {
  const type = getNotificationType(notification)

  switch (type) {
    case NotificationType.AUDIT_APPROVED:
      return 'success'
    case NotificationType.AUDIT_REJECTED:
      return 'danger'
    case NotificationType.ADMIN:
      return 'info'
    case NotificationType.CLAIM:
      return 'info'
    case NotificationType.CLAIM_APPROVED:
      return 'success'
    case NotificationType.CLAIM_REJECTED:
      return 'danger'
    case NotificationType.MATCH:
      return 'primary'
    case NotificationType.ANNOUNCEMENT:
      return 'warning'
    default:
      return 'info'
  }
}

/**
 * 获取通知颜色
 * @param {Object} notification 通知对象
 * @returns {String} 颜色值
 */
export function getNotificationColor(notification) {
  const type = getNotificationType(notification)

  switch (type) {
    case NotificationType.AUDIT_APPROVED:
      return '#67C23A'
    case NotificationType.AUDIT_REJECTED:
      return '#F56C6C'
    case NotificationType.ADMIN:
      return '#909399'
    case NotificationType.CLAIM:
      return '#409EFF'
    case NotificationType.MATCH:
      if (notification.similarity) {
        return getSimilarityColor(notification.similarity)
      }
      return '#409EFF'
    case NotificationType.ANNOUNCEMENT:
      if (notification.importance) {
        return getImportanceColor(notification.importance)
      }
      return '#E6A23C'
    default:
      return '#909399'
  }
}

/**
 * 获取相似度颜色
 * @param {Number} similarity 相似度
 * @returns {String} 颜色值
 */
export function getSimilarityColor(similarity) {
  if (similarity >= 0.9) {
    return '#67C23A' // 绿色 - 高相似度
  } else if (similarity >= 0.7) {
    return '#E6A23C' // 黄色 - 中等相似度
  } else {
    return '#909399' // 灰色 - 低相似度
  }
}

/**
 * 获取重要程度颜色
 * @param {String} importance 重要程度
 * @returns {String} 颜色值
 */
export function getImportanceColor(importance) {
  switch (importance) {
    case 'URGENT':
      return '#F56C6C' // 红色 - 紧急
    case 'IMPORTANT':
      return '#E6A23C' // 黄色 - 重要
    default:
      return '#909399' // 灰色 - 普通
  }
}

/**
 * 解析通知元数据
 * @param {Object} notification 通知对象
 * @returns {Object} 解析后的元数据
 */
export function parseMetadata(notification) {
  if (!notification.metadata) {
    return {}
  }

  try {
    return JSON.parse(notification.metadata)
  } catch (e) {
    console.error('解析元数据失败', e)
    return {}
  }
}

/**
 * 获取审核员名称
 * @param {Object} notification 通知对象
 * @returns {String|null} 审核员名称
 */
export function getAuditorName(notification) {
  const metadata = parseMetadata(notification)
  return metadata.auditorName || metadata.adminName || null
}

/**
 * 获取审核时间
 * @param {Object} notification 通知对象
 * @returns {String|null} 审核时间
 */
export function getAuditTime(notification) {
  const metadata = parseMetadata(notification)
  return metadata.auditTime || null
}

/**
 * 获取拒绝原因
 * @param {Object} notification 通知对象
 * @returns {String} 拒绝原因
 */
export function getRejectReason(notification) {
  const metadata = parseMetadata(notification)

  if (metadata.rejectReason) {
    return metadata.rejectReason
  }

  // 兼容旧数据，从消息中提取
  if (getNotificationType(notification) === NotificationType.AUDIT_REJECTED) {
    const reasonMatch = notification.message.match(/[：:](.*?)未通过审核/)
    if (reasonMatch && reasonMatch[1]) {
      return reasonMatch[1].trim()
    }
  }

  return '未提供拒绝原因'
}

/**
 * 格式化角色
 * @param {String} role 角色
 * @returns {String} 格式化后的角色名称
 */
export function formatRole(role) {
  if (!role) return ''

  switch (role.toUpperCase()) {
    case 'ADMIN':
      return '管理员'
    case 'SUPER_ADMIN':
      return '超级管理员'
    case 'USER':
      return '普通用户'
    default:
      return role
  }
}

/**
 * 格式化重要程度
 * @param {String} importance 重要程度
 * @returns {String} 格式化后的重要程度
 */
export function formatImportance(importance) {
  if (!importance) return '普通'

  switch (importance.toUpperCase()) {
    case 'URGENT':
      return '紧急'
    case 'IMPORTANT':
      return '重要'
    case 'NORMAL':
      return '普通'
    default:
      return importance
  }
}

/**
 * 获取重要程度标签类型
 * @param {String} importance 重要程度
 * @returns {String} 标签类型
 */
export function getImportanceTagType(importance) {
  if (!importance) return 'info'

  switch (importance.toUpperCase()) {
    case 'URGENT':
      return 'danger'
    case 'IMPORTANT':
      return 'warning'
    case 'NORMAL':
      return 'info'
    default:
      return 'info'
  }
}
