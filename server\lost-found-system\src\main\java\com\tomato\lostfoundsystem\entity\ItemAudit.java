package com.tomato.lostfoundsystem.entity;

import com.tomato.lostfoundsystem.enums.AuditStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ItemAudit {

    private Long id;                // 审核记录ID
    private Long itemId;            // 物品ID，关联到失物或拾物
    private AuditStatusEnum auditStatus = AuditStatusEnum.PENDING;  // 审核状态
    private LocalDateTime auditTime; // 审核时间
    private Long auditorId;         // 审核人员ID（管理员ID）
    private String remarks;         // 审核备注

}
