/**
 * 模拟数据生成工具
 * 用于生成智能匹配结果和后台统计数据
 */

// 物品类别列表
const itemCategories = [
  '电子设备', '证件卡片', '生活用品', '学习用品', '服装配饰', '钱包钥匙', '图书资料', '其他'
];

// 校园地点列表
const campusLocations = [
  '图书馆', '教学楼', '食堂', '宿舍楼', '体育馆', '操场', '实验室', '校门口', '公交站', '其他'
];

// 随机生成日期（过去30天内）
const randomDate = () => {
  const now = new Date();
  const daysAgo = Math.floor(Math.random() * 30);
  const date = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
  return date.toISOString().split('T')[0];
};

// 随机生成时间
const randomTime = () => {
  const hours = Math.floor(Math.random() * 24).toString().padStart(2, '0');
  const minutes = Math.floor(Math.random() * 60).toString().padStart(2, '0');
  return `${hours}:${minutes}`;
};

// 随机生成相似度（0.5-1.0之间）
const randomSimilarity = () => {
  return (Math.random() * 0.5 + 0.5).toFixed(2);
};

// 生成随机物品图片URL
const randomImageUrl = (category) => {
  const imageMap = {
    '电子设备': [
      'https://img.alicdn.com/imgextra/i4/O1CN01Ky3iqP24jcXuMGi7D_!!6000000007427-0-tps-800-800.jpg',
      'https://img.alicdn.com/imgextra/i4/O1CN01CUxQyQ1YKsV9JMzIm_!!6000000003046-0-tps-800-800.jpg'
    ],
    '证件卡片': [
      'https://img.alicdn.com/imgextra/i2/O1CN01aqnJOI1fJhB9mMXL3_!!6000000003989-0-tps-800-800.jpg',
      'https://img.alicdn.com/imgextra/i1/O1CN01Zn0qSn1VzRJMD4RHv_!!6000000002713-0-tps-800-800.jpg'
    ],
    '生活用品': [
      'https://img.alicdn.com/imgextra/i3/O1CN01Rp4iYg1YsLJQ36MH7_!!6000000003109-0-tps-800-800.jpg',
      'https://img.alicdn.com/imgextra/i2/O1CN01aqnJOI1fJhB9mMXL3_!!6000000003989-0-tps-800-800.jpg'
    ],
    '学习用品': [
      'https://img.alicdn.com/imgextra/i4/O1CN01CUxQyQ1YKsV9JMzIm_!!6000000003046-0-tps-800-800.jpg',
      'https://img.alicdn.com/imgextra/i3/O1CN01Rp4iYg1YsLJQ36MH7_!!6000000003109-0-tps-800-800.jpg'
    ],
    '服装配饰': [
      'https://img.alicdn.com/imgextra/i1/O1CN01Zn0qSn1VzRJMD4RHv_!!6000000002713-0-tps-800-800.jpg',
      'https://img.alicdn.com/imgextra/i2/O1CN01aqnJOI1fJhB9mMXL3_!!6000000003989-0-tps-800-800.jpg'
    ],
    '钱包钥匙': [
      'https://img.alicdn.com/imgextra/i2/O1CN01aqnJOI1fJhB9mMXL3_!!6000000003989-0-tps-800-800.jpg',
      'https://img.alicdn.com/imgextra/i1/O1CN01Zn0qSn1VzRJMD4RHv_!!6000000002713-0-tps-800-800.jpg'
    ],
    '图书资料': [
      'https://img.alicdn.com/imgextra/i3/O1CN01Rp4iYg1YsLJQ36MH7_!!6000000003109-0-tps-800-800.jpg',
      'https://img.alicdn.com/imgextra/i4/O1CN01CUxQyQ1YKsV9JMzIm_!!6000000003046-0-tps-800-800.jpg'
    ],
    '其他': [
      'https://img.alicdn.com/imgextra/i4/O1CN01Ky3iqP24jcXuMGi7D_!!6000000007427-0-tps-800-800.jpg',
      'https://img.alicdn.com/imgextra/i1/O1CN01Zn0qSn1VzRJMD4RHv_!!6000000002713-0-tps-800-800.jpg'
    ]
  };
  
  const images = imageMap[category] || imageMap['其他'];
  return images[Math.floor(Math.random() * images.length)];
};

/**
 * 生成智能匹配结果数据
 * @param {number} count 生成数量
 * @param {string} type 物品类型 'LOST'或'FOUND'
 * @returns {Array} 匹配结果数组
 */
export const generateMatchResults = (count = 10, type = 'LOST') => {
  const results = [];
  
  for (let i = 0; i < count; i++) {
    const category = itemCategories[Math.floor(Math.random() * itemCategories.length)];
    const location = campusLocations[Math.floor(Math.random() * campusLocations.length)];
    const date = randomDate();
    const time = randomTime();
    const similarity = randomSimilarity();
    
    results.push({
      id: 10000 + i,
      itemName: `${category}${i + 1}`,
      itemType: type,
      category: category,
      description: `这是一个${category}，在${location}${type === 'LOST' ? '丢失' : '拾到'}的。`,
      location: location,
      date: date,
      time: time,
      imageUrl: randomImageUrl(category),
      similarity: parseFloat(similarity),
      contactPerson: `用户${1000 + i}`,
      contactInfo: `1380013${i.toString().padStart(4, '0')}`,
      status: Math.random() > 0.3 ? (type === 'LOST' ? 'LOST' : 'UNCLAIMED') : (type === 'LOST' ? 'FOUND' : 'RETURNED')
    });
  }
  
  // 按相似度排序
  return results.sort((a, b) => b.similarity - a.similarity);
};

/**
 * 生成后台统计数据
 * @returns {Object} 统计数据对象
 */
export const generateStatisticsData = () => {
  // 物品类别分布
  const categoryDistribution = itemCategories.map(category => ({
    name: category,
    lostCount: Math.floor(Math.random() * 50) + 10,
    foundCount: Math.floor(Math.random() * 40) + 5,
    returnedCount: Math.floor(Math.random() * 30)
  }));
  
  // 地点分布
  const locationDistribution = campusLocations.map(location => ({
    name: location,
    lostCount: Math.floor(Math.random() * 40) + 5,
    foundCount: Math.floor(Math.random() * 30) + 5
  }));
  
  // 近30天每日数据
  const dailyData = [];
  const now = new Date();
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    const dateStr = date.toISOString().split('T')[0];
    
    dailyData.push({
      date: dateStr,
      lostCount: Math.floor(Math.random() * 10) + 1,
      foundCount: Math.floor(Math.random() * 8) + 1,
      returnedCount: Math.floor(Math.random() * 6),
      matchCount: Math.floor(Math.random() * 5)
    });
  }
  
  // 匹配成功率数据
  const matchSuccessRate = {
    totalMatches: Math.floor(Math.random() * 500) + 300,
    successMatches: Math.floor(Math.random() * 300) + 100,
    rate: ((Math.random() * 30) + 40).toFixed(2) // 40%-70%之间
  };
  
  // 用户活跃度数据
  const userActivity = {
    totalUsers: Math.floor(Math.random() * 1000) + 500,
    activeUsers: Math.floor(Math.random() * 500) + 200,
    newUsers: Math.floor(Math.random() * 100) + 50,
    publishCount: Math.floor(Math.random() * 300) + 100,
    matchCount: Math.floor(Math.random() * 200) + 50,
    messageCount: Math.floor(Math.random() * 1000) + 500
  };
  
  return {
    summary: {
      totalLost: categoryDistribution.reduce((sum, item) => sum + item.lostCount, 0),
      totalFound: categoryDistribution.reduce((sum, item) => sum + item.foundCount, 0),
      totalReturned: categoryDistribution.reduce((sum, item) => sum + item.returnedCount, 0),
      totalMatches: matchSuccessRate.totalMatches,
      successMatches: matchSuccessRate.successMatches,
      matchRate: matchSuccessRate.rate,
      totalUsers: userActivity.totalUsers,
      activeUsers: userActivity.activeUsers
    },
    categoryDistribution,
    locationDistribution,
    dailyData,
    matchSuccessRate,
    userActivity
  };
};

export default {
  generateMatchResults,
  generateStatisticsData
};
