server:
  port: 8081

spring:
  datasource:
    url: *****************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver

  # 异步任务配置
  task:
    execution:
      pool:
        core-size: 5
        max-size: 10
        queue-capacity: 100
        keep-alive: 60s


  #  security:
#    user:
#      name: admin
#      password: 123456
  kafka:
    enabled: true  # 启用 Kafka
    bootstrap-servers: localhost:9092  # Kafka 集群地址
    consumer:
      group-id: chat-consumer-group   # 消费者组
      auto-offset-reset: earliest    # 自动偏移重置策略（earliest/latest）
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer  # 键反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer  # 值反序列化方式
      # 新增配置
      enable-auto-commit: false  # 禁用自动提交，改为手动确认
      properties:
        max.poll.records: 500  # 每次拉取的最大记录数
        max.poll.interval.ms: 300000  # 两次poll之间的最大时间间隔(5分钟)
        session.timeout.ms: 30000  # 会话超时时间(30秒)
        heartbeat.interval.ms: 3000  # 心跳间隔时间(3秒)
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer  # 键序列化方式
      value-serializer: org.apache.kafka.common.serialization.StringSerializer  # 值序列化方式
      # 新增配置
      properties:
        acks: all  # 确保消息被所有副本确认
        retries: 3  # 重试次数
        retry.backoff.ms: 1000  # 重试间隔
        batch.size: 16384  # 批处理大小
        linger.ms: 5  # 批处理延迟(5毫秒)
        buffer.memory: 33554432  # 缓冲区大小(32MB)
        compression.type: snappy  # 启用压缩
    # 自定义Kafka主题配置
    topic:
      item-approved: item-approved-topic  # 物品审核通过事件主题
    # 自定义Kafka消费者组配置
    group:
      item-processor: item-processor-group  # 物品处理消费者组


  servlet:
    multipart:
      enabled: true                  # 启用文件上传
      max-file-size: 500MB            # 最大文件大小限制
      max-request-size: 500MB         # 请求的最大大小限制
      file-size-threshold: 1MB        # 在进行文件上传前，文件多大将被缓存在内存中

  data:
    redis:
      host: localhost
      port: 6379
      timeout: 5000ms

  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>         # 你的邮箱
    password: sthdbwhktrpudceb          # QQ邮箱授权码（不是登录密码）
    protocol: smtp
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true


mybatis:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.tomato.lostfoundsystem.entity
  type-handlers-package: com.tomato.lostfoundsystem.config.mybatis



logging:
  level:
    com.tomato.lostfoundsystem: INFO  # 默认日志级别为INFO
    com.tomato.lostfoundsystem.controller: INFO  # 控制器日志级别
    com.tomato.lostfoundsystem.service: INFO  # 服务层日志级别
    com.tomato.lostfoundsystem.aspect: INFO  # 切面日志级别
    com.tomato.lostfoundsystem.utils: INFO  # 工具类日志级别
    org.springframework.jdbc.core: INFO  # SQL日志级别
    org.hibernate.SQL: INFO  # Hibernate日志级别
    org.springframework.web: INFO  # Spring Web日志级别
    org.springframework.security: INFO  # Spring Security日志级别

  file:
    name: logs/application.log  # 设置日志输出路径和文件名
    max-size: 10MB  # 单个日志文件最大大小
    max-history: 30  # 保留的历史日志文件个数
    total-size-cap: 3GB  # 日志文件总大小上限

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"  # 控制台日志格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"  # 文件日志格式

aliyun:
  sms:
    accessKeyId: LTAI5t5yyG5WmB8wwCTqG8wu         # 你的阿里云 AccessKeyId
    accessKeySecret: ****************************** # 你的阿里云 AccessKeySecret
    signName: 阿里云短信测试               # 你的短信签名名称（必须经过阿里云审核）
    templateCode: SMS_154950909       # 你的短信模板编号（必须在阿里云控制台创建并审核）
  oss:
    endpoint: oss-cn-guangzhou.aliyuncs.com  # OSS端点
    access-key-id: LTAI5tM55W1ULPhMVsHtFP7M       # 访问密钥ID
    access-key-secret: ****************************** # 访问密钥密码
    bucket-name: laofanqi-service            # OSS存储空间名称
    avatar-directory: avatars                # 头像存储目录
  cdn:
    domain: ""  # CDN域名已禁用，改为使用OSS直接访问
  image:
    search:
      access-key-id: LTAI5tM55W1ULPhMVsHtFP7M # 使用与OSS相同的AccessKeyId
      access-key-secret: ****************************** # 访问密钥密码
      instance-id: imagesearch-cn-yzh48nvqf002 # 替换为实际创建的图像搜索实例ID
      region: cn-shenzhen # 替换为实际区域
      endpoint: imagesearch.cn-shenzhen.aliyuncs.com # 图像搜索服务的Endpoint


# application.yml
jwt:
  secret-key: "your-256-bit-long-secret-key-here!"  # 32 字节的密钥"
  expiration-time: 8640000  # 令牌过期时间（例如 24小时以秒为单位）

request:
  body:
    wrap:
      paths: /api/user/login,/api/user/register,/api/lost-items/publish,/api/found-items/publish

# WebSocket配置
websocket:
  heartbeat:
    timeout: 30  # 心跳超时时间（分钟）
    interval: 25 # 心跳间隔（秒）

# AutoDL CLIP+FAISS API配置
autodl:
  clip:
    api:
      url: http://localhost:8000  # 通过SSH隧道访问AutoDL实例上的CLIP+FAISS服务
    service:
      check-enabled: true  # 是否启用服务可用性检查
      connection-timeout: 3000  # 连接超时时间（毫秒）
      script-path: ./clip_faiss_service/start_clip_service.sh  # 启动脚本路径

# 自定义文件配置
file:
  upload:
    max-size: 524288000  # 500MB，单位字节
    allowed-types:
      image: jpg,jpeg,png,gif,webp,bmp,tiff
      video: mp4,mov,avi,wmv,flv,mkv,webm,m4v,3gp
      audio: mp3,wav,ogg,m4a,aac,flac,wma
      document: pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv,rtf,zip,rar,7z

# 特征向量配置
feature:
  vector:
    version: v1  # 特征向量版本

# 匹配配置
match:
  notification:
    similarity-threshold: 0.7  # 通知阈值
  result:
    similarity-threshold: 0.5  # 结果阈值

# 验证码配置
captcha:
  cache:
    enabled: true  # 是否启用验证码缓存
    size: 10  # 缓存大小
  expiration:
    minutes: 5  # 验证码过期时间（分钟）

# 性能监控配置
performance:
  monitor:
    enabled: true  # 是否启用性能监控
    log-threshold: 500  # 记录日志的阈值（毫秒）
    warn-threshold: 1000  # 警告阈值（毫秒）

