<template>
  <div class="websocket-test">
    <h2>WebSocket 测试</h2>
    
    <el-card class="connection-card">
      <template #header>
        <div class="card-header">
          <span>连接状态</span>
          <el-button type="primary" size="small" @click="checkConnection">检查连接</el-button>
        </div>
      </template>
      <div class="connection-info">
        <p><strong>连接状态:</strong> {{ connected ? '已连接' : '未连接' }}</p>
        <p><strong>初始化状态:</strong> {{ initialized ? '已初始化' : '未初始化' }}</p>
        <p><strong>订阅状态:</strong> {{ subscribed ? '已订阅' : '未订阅' }}</p>
        <div class="button-group">
          <el-button type="success" @click="connect" :disabled="connected">连接</el-button>
          <el-button type="danger" @click="disconnect" :disabled="!connected">断开</el-button>
          <el-button type="warning" @click="subscribe" :disabled="!connected || subscribed">订阅</el-button>
        </div>
      </div>
    </el-card>
    
    <el-card class="message-card">
      <template #header>
        <div class="card-header">
          <span>发送消息</span>
        </div>
      </template>
      <div class="message-form">
        <el-form :model="messageForm" label-width="100px">
          <el-form-item label="接收者ID">
            <el-input v-model="messageForm.receiverId" placeholder="请输入接收者ID"></el-input>
          </el-form-item>
          <el-form-item label="消息内容">
            <el-input v-model="messageForm.message" type="textarea" :rows="3" placeholder="请输入消息内容"></el-input>
          </el-form-item>
          <el-form-item label="消息类型">
            <el-select v-model="messageForm.messageType" placeholder="请选择消息类型">
              <el-option label="文本" value="TEXT"></el-option>
              <el-option label="图片" value="IMAGE"></el-option>
              <el-option label="文件" value="FILE"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="sendMessage" :disabled="!connected || sending">发送消息</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <el-card class="log-card">
      <template #header>
        <div class="card-header">
          <span>日志</span>
          <el-button type="info" size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </template>
      <div class="log-container" ref="logContainer">
        <div v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-content">{{ log.content }}</span>
        </div>
      </div>
    </el-card>
    
    <el-card class="received-card">
      <template #header>
        <div class="card-header">
          <span>接收到的消息</span>
          <el-button type="info" size="small" @click="clearMessages">清空消息</el-button>
        </div>
      </template>
      <div class="message-container">
        <div v-for="(msg, index) in receivedMessages" :key="index" class="message-item">
          <div class="message-header">
            <span class="message-time">{{ formatTime(msg.timestamp) }}</span>
            <span class="message-sender">发送者: {{ msg.senderId }}</span>
          </div>
          <div class="message-content">{{ msg.message }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'
import { Client } from '@stomp/stompjs'
import SockJS from 'sockjs-client/dist/sockjs.min.js'

// 状态
const connected = ref(false)
const initialized = ref(false)
const subscribed = ref(false)
const sending = ref(false)
const logs = ref([])
const receivedMessages = ref([])
const logContainer = ref(null)
const stompClient = ref(null)
const subscriptions = ref([])

// 表单数据
const messageForm = reactive({
  receiverId: '',
  message: '',
  messageType: 'TEXT'
})

// 用户信息
const userStore = useUserStore()
const userInfo = ref(userStore.userInfo || {})

// 添加日志
const addLog = (content, type = 'info') => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    content,
    type
  })
  
  // 滚动到底部
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTop = logContainer.value.scrollHeight
    }
  })
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 清空消息
const clearMessages = () => {
  receivedMessages.value = []
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleTimeString()
}

// 检查连接
const checkConnection = () => {
  if (!stompClient.value) {
    addLog('WebSocket 客户端未初始化', 'error')
    connected.value = false
    return
  }
  
  connected.value = stompClient.value.connected
  addLog(`WebSocket 连接状态: ${connected.value ? '已连接' : '未连接'}`)
}

// 连接 WebSocket
const connect = async () => {
  try {
    addLog('开始连接 WebSocket...')
    
    // 获取 Token
    const token = userStore.token
    if (!token) {
      addLog('未找到用户 Token，无法连接', 'error')
      ElMessage.error('未找到用户 Token，请先登录')
      return
    }
    
    // 创建 SockJS 实例
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081'
    const wsUrl = `${apiBaseUrl}/ws?token=${encodeURIComponent(token)}&t=${Date.now()}`
    addLog(`连接 URL: ${wsUrl.replace(token, '***')}`)
    const socket = new SockJS(wsUrl)
    
    // 创建 STOMP 客户端
    stompClient.value = new Client({
      webSocketFactory: () => socket,
      connectHeaders: {
        Authorization: `Bearer ${token}`
      },
      debug: (str) => {
        console.log(str)
      },
      reconnectDelay: 5000,
      heartbeatIncoming: 25000,
      heartbeatOutgoing: 25000
    })
    
    // 连接成功回调
    stompClient.value.onConnect = (frame) => {
      addLog('WebSocket 连接成功', 'success')
      connected.value = true
      initialized.value = true
    }
    
    // 连接错误回调
    stompClient.value.onStompError = (frame) => {
      addLog(`STOMP 错误: ${frame.headers?.message || JSON.stringify(frame)}`, 'error')
      connected.value = false
    }
    
    // 连接断开回调
    stompClient.value.onWebSocketClose = (event) => {
      addLog(`WebSocket 连接关闭: ${event.code} ${event.reason}`, 'warning')
      connected.value = false
      subscribed.value = false
    }
    
    // 激活连接
    stompClient.value.activate()
    addLog('WebSocket 连接请求已发送')
  } catch (error) {
    addLog(`连接 WebSocket 失败: ${error.message}`, 'error')
    console.error('连接 WebSocket 失败:', error)
    connected.value = false
  }
}

// 断开连接
const disconnect = () => {
  if (!stompClient.value) {
    addLog('WebSocket 客户端未初始化', 'error')
    return
  }
  
  try {
    // 取消所有订阅
    subscriptions.value.forEach(sub => {
      try {
        sub.unsubscribe()
      } catch (error) {
        console.warn('取消订阅失败:', error)
      }
    })
    subscriptions.value = []
    
    // 断开连接
    stompClient.value.deactivate()
    addLog('WebSocket 连接已断开')
    connected.value = false
    subscribed.value = false
  } catch (error) {
    addLog(`断开 WebSocket 连接失败: ${error.message}`, 'error')
    console.error('断开 WebSocket 连接失败:', error)
  }
}

// 订阅主题
const subscribe = () => {
  if (!stompClient.value || !stompClient.value.connected) {
    addLog('WebSocket 未连接，无法订阅', 'error')
    return
  }
  
  try {
    // 订阅私人消息
    const privateSubscription = stompClient.value.subscribe('/user/queue/private', (message) => {
      addLog(`收到私人消息: ${message.headers.destination}`)
      try {
        const data = JSON.parse(message.body)
        receivedMessages.value.push(data)
      } catch (error) {
        addLog(`解析消息失败: ${error.message}`, 'error')
      }
    })
    subscriptions.value.push(privateSubscription)
    addLog('已订阅私人消息: /user/queue/private', 'success')
    
    // 订阅发送确认
    const sentSubscription = stompClient.value.subscribe('/user/queue/sent', (message) => {
      addLog(`收到发送确认: ${message.headers.destination}`)
      try {
        const data = JSON.parse(message.body)
        addLog(`消息已发送成功，ID: ${data.id}`, 'success')
      } catch (error) {
        addLog(`解析发送确认失败: ${error.message}`, 'error')
      }
    })
    subscriptions.value.push(sentSubscription)
    addLog('已订阅发送确认: /user/queue/sent', 'success')
    
    subscribed.value = true
  } catch (error) {
    addLog(`订阅主题失败: ${error.message}`, 'error')
    console.error('订阅主题失败:', error)
  }
}

// 发送消息
const sendMessage = async () => {
  if (!stompClient.value || !stompClient.value.connected) {
    addLog('WebSocket 未连接，无法发送消息', 'error')
    return
  }
  
  if (!messageForm.receiverId || !messageForm.message) {
    addLog('接收者ID和消息内容不能为空', 'warning')
    return
  }
  
  try {
    sending.value = true
    addLog('准备发送消息...')
    
    // 准备消息数据
    const messageDTO = {
      senderId: userInfo.value.id,
      receiverId: messageForm.receiverId,
      message: messageForm.message,
      messageType: messageForm.messageType,
      clientMessageId: `test-${Date.now()}`,
      timestamp: Date.now()
    }
    
    // 发送消息
    stompClient.value.publish({
      destination: '/app/privateMessage',
      headers: {
        'content-type': 'application/json'
      },
      body: JSON.stringify(messageDTO)
    })
    
    addLog(`消息已发送: ${JSON.stringify(messageDTO)}`, 'success')
  } catch (error) {
    addLog(`发送消息失败: ${error.message}`, 'error')
    console.error('发送消息失败:', error)
  } finally {
    sending.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  addLog('WebSocket 测试页面已加载')
})

onUnmounted(() => {
  // 断开连接
  if (stompClient.value && stompClient.value.connected) {
    disconnect()
  }
})
</script>

<style scoped>
.websocket-test {
  padding: 20px;
}

.connection-card,
.message-card,
.log-card,
.received-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connection-info {
  margin-bottom: 15px;
}

.button-group {
  margin-top: 15px;
}

.log-container {
  height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  padding: 10px;
  background-color: #f9f9f9;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
}

.log-time {
  color: #999;
  margin-right: 10px;
}

.log-item.info .log-content {
  color: #333;
}

.log-item.success .log-content {
  color: #67c23a;
}

.log-item.warning .log-content {
  color: #e6a23c;
}

.log-item.error .log-content {
  color: #f56c6c;
}

.message-container {
  height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  padding: 10px;
}

.message-item {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #999;
}

.message-content {
  word-break: break-all;
}
</style>
