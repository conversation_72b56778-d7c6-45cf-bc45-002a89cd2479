package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.SystemConfig;
import com.tomato.lostfoundsystem.mapper.SystemConfigMapper;
import com.tomato.lostfoundsystem.service.SystemConfigService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 系统配置服务实现类
 */
@Slf4j
@Service
public class SystemConfigServiceImpl implements SystemConfigService {
    
    @Resource
    private SystemConfigMapper systemConfigMapper;
    
    /**
     * 配置缓存
     */
    private final Map<String, String> configCache = new ConcurrentHashMap<>();
    
    /**
     * 初始化配置缓存
     */
    @PostConstruct
    public void init() {
        refreshConfigCache();
    }
    
    @Override
    public Result<List<SystemConfig>> getAllConfigs() {
        try {
            List<SystemConfig> configs = systemConfigMapper.getAllConfigs();
            return Result.success(configs);
        } catch (Exception e) {
            log.error("获取所有系统配置失败", e);
            return Result.fail("获取系统配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public Result<SystemConfig> getConfigByKey(String configKey) {
        try {
            SystemConfig config = systemConfigMapper.getConfigByKey(configKey);
            if (config == null) {
                return Result.fail("配置不存在: " + configKey);
            }
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取系统配置失败: {}", configKey, e);
            return Result.fail("获取系统配置失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public Result<String> updateConfigValue(String configKey, String configValue) {
        try {
            SystemConfig config = systemConfigMapper.getConfigByKey(configKey);
            if (config == null) {
                return Result.fail("配置不存在: " + configKey);
            }
            
            int rows = systemConfigMapper.updateConfigValue(configKey, configValue);
            if (rows > 0) {
                // 更新缓存
                configCache.put(configKey, configValue);
                log.info("配置已更新: {} = {}", configKey, configValue);
                return Result.success("配置已更新");
            } else {
                return Result.fail("更新配置失败");
            }
        } catch (Exception e) {
            log.error("更新配置失败: {}", configKey, e);
            return Result.fail("更新配置失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public Result<String> batchUpdateConfig(Map<String, String> configs) {
        try {
            if (configs == null || configs.isEmpty()) {
                return Result.fail("配置不能为空");
            }
            
            Map<String, String> successUpdates = new HashMap<>();
            Map<String, String> failedUpdates = new HashMap<>();
            
            for (Map.Entry<String, String> entry : configs.entrySet()) {
                String configKey = entry.getKey();
                String configValue = entry.getValue();
                
                try {
                    SystemConfig config = systemConfigMapper.getConfigByKey(configKey);
                    if (config == null) {
                        failedUpdates.put(configKey, "配置不存在");
                        continue;
                    }
                    
                    int rows = systemConfigMapper.updateConfigValue(configKey, configValue);
                    if (rows > 0) {
                        // 更新缓存
                        configCache.put(configKey, configValue);
                        successUpdates.put(configKey, configValue);
                    } else {
                        failedUpdates.put(configKey, "更新失败");
                    }
                } catch (Exception e) {
                    log.error("更新配置失败: {}", configKey, e);
                    failedUpdates.put(configKey, e.getMessage());
                }
            }
            
            if (failedUpdates.isEmpty()) {
                log.info("所有配置已更新: {}", successUpdates);
                return Result.success("所有配置已更新");
            } else {
                log.warn("部分配置更新失败: 成功={}, 失败={}", successUpdates, failedUpdates);
                return Result.fail("部分配置更新失败: " + failedUpdates);
            }
        } catch (Exception e) {
            log.error("批量更新配置失败", e);
            return Result.fail("批量更新配置失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public Result<String> addConfig(SystemConfig config) {
        try {
            if (config == null || config.getConfigKey() == null || config.getConfigKey().trim().isEmpty()) {
                return Result.fail("配置键不能为空");
            }
            
            SystemConfig existingConfig = systemConfigMapper.getConfigByKey(config.getConfigKey());
            if (existingConfig != null) {
                return Result.fail("配置已存在: " + config.getConfigKey());
            }
            
            int rows = systemConfigMapper.addConfig(config);
            if (rows > 0) {
                // 更新缓存
                configCache.put(config.getConfigKey(), config.getConfigValue());
                log.info("配置已添加: {} = {}", config.getConfigKey(), config.getConfigValue());
                return Result.success("配置已添加");
            } else {
                return Result.fail("添加配置失败");
            }
        } catch (Exception e) {
            log.error("添加配置失败", e);
            return Result.fail("添加配置失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    public Result<String> deleteConfig(String configKey) {
        try {
            SystemConfig config = systemConfigMapper.getConfigByKey(configKey);
            if (config == null) {
                return Result.fail("配置不存在: " + configKey);
            }
            
            int rows = systemConfigMapper.deleteConfig(configKey);
            if (rows > 0) {
                // 更新缓存
                configCache.remove(configKey);
                log.info("配置已删除: {}", configKey);
                return Result.success("配置已删除");
            } else {
                return Result.fail("删除配置失败");
            }
        } catch (Exception e) {
            log.error("删除配置失败: {}", configKey, e);
            return Result.fail("删除配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        // 先从缓存中获取
        String value = configCache.get(configKey);
        if (value != null) {
            return value;
        }
        
        // 缓存中没有，从数据库获取
        try {
            SystemConfig config = systemConfigMapper.getConfigByKey(configKey);
            if (config != null && config.getConfigValue() != null) {
                // 更新缓存
                configCache.put(configKey, config.getConfigValue());
                return config.getConfigValue();
            }
        } catch (Exception e) {
            log.error("获取配置值失败: {}", configKey, e);
        }
        
        // 返回默认值
        return defaultValue;
    }
    
    @Override
    public Result<String> refreshConfigCache() {
        try {
            List<SystemConfig> configs = systemConfigMapper.getAllConfigs();
            
            // 清空缓存
            configCache.clear();
            
            // 重新加载配置
            for (SystemConfig config : configs) {
                configCache.put(config.getConfigKey(), config.getConfigValue());
            }
            
            log.info("配置缓存已刷新，共加载 {} 个配置", configs.size());
            return Result.success("配置缓存已刷新");
        } catch (Exception e) {
            log.error("刷新配置缓存失败", e);
            return Result.fail("刷新配置缓存失败: " + e.getMessage());
        }
    }
}
