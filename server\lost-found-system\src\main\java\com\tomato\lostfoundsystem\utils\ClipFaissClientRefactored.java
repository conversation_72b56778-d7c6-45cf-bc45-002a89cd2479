package com.tomato.lostfoundsystem.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CLIP+FAISS客户端工具类
 * 纯通信客户端，只负责与CLIP+FAISS API服务进行通信
 * 重构版本，与*****************接口完全匹配
 */
@Slf4j
@Component
public class ClipFaissClientRefactored {

    @Value("${autodl.clip.api.url}")
    private String clipApiUrl;

    @Value("${autodl.clip.service.connection-timeout:3000}")
    private int connectionTimeout;

    private final RestTemplate restTemplate;

    public ClipFaissClientRefactored(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 检查CLIP+FAISS服务是否可用
     *
     * @return 服务是否可用
     */
    public boolean isServiceAvailable() {
        try {
            log.debug("检查CLIP+FAISS服务是否可用");
            URL url = new URL(clipApiUrl + "/health");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(connectionTimeout);
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            boolean available = responseCode == 200;
            log.debug("CLIP+FAISS服务状态: {}", available ? "可用" : "不可用");
            return available;
        } catch (Exception e) {
            log.error("检查CLIP+FAISS服务可用性时发生异常", e);
            return false;
        }
    }

    /**
     * 从文本中提取特征向量
     *
     * @param text 文本内容
     * @return 特征向量的字节数组，提取失败返回null
     */
    public byte[] extractTextFeatures(String text) {
        try {
            log.info("【CLIP+FAISS】发送请求到API: /extract_text_features, 文本长度: {} 字符", text != null ? text.length() : 0);

            // 预处理文本，确保不会超过CLIP模型的限制
            String processedText = preprocessTextForCLIP(text);
            log.info("【CLIP+FAISS】预处理后的文本长度: {} 字符", processedText.length());

            // 记录处理前后的文本（仅在DEBUG级别记录完整文本）
            if (log.isDebugEnabled()) {
                log.debug("【CLIP+FAISS】原始文本: {}", text);
                log.debug("【CLIP+FAISS】预处理后的文本: {}", processedText);
            } else {
                // 在INFO级别只记录前30个字符
                String originalPreview = text != null && text.length() > 30 ? text.substring(0, 30) + "..." : text;
                String processedPreview = processedText.length() > 30 ? processedText.substring(0, 30) + "..." : processedText;
                log.info("【CLIP+FAISS】原始文本预览: {}", originalPreview);
                log.info("【CLIP+FAISS】预处理后的文本预览: {}", processedPreview);
            }

            // 构建请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("text", processedText);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建HTTP实体
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    clipApiUrl + "/extract_text_features",
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                String base64Features = (String) response.getBody().get("features");
                byte[] features = Base64.getDecoder().decode(base64Features);
                log.info("【CLIP+FAISS】成功提取文本特征，特征向量大小: {} 字节", features.length);
                return features;
            } else {
                log.error("【CLIP+FAISS】API调用失败: /extract_text_features, 状态码: {}", response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("【CLIP+FAISS】提取文本特征时发生异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 预处理文本，确保不会超过CLIP模型的限制
     * CLIP模型的token限制为77个token，大约对应100-150个汉字
     *
     * @param text 原始文本
     * @return 预处理后的文本
     */
    private String preprocessTextForCLIP(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 1. 移除多余的空格
        text = text.trim().replaceAll("\\s+", " ");

        // 2. 限制文本长度
        // 保守估计，一个汉字可能对应1-2个token，我们限制在50个汉字以内
        final int MAX_CHARS = 50;

        if (text.length() > MAX_CHARS) {
            log.warn("【CLIP+FAISS】文本长度({})超过限制({}), 将进行截断", text.length(), MAX_CHARS);

            // 截取前MAX_CHARS个字符
            text = text.substring(0, MAX_CHARS);

            // 确保不会截断到一半的词
            // 如果最后一个字符是英文或数字，可能会截断单词，尝试找到最后一个空格
            if (Character.isLetterOrDigit(text.charAt(text.length() - 1))) {
                int lastSpaceIndex = text.lastIndexOf(' ');
                if (lastSpaceIndex > MAX_CHARS / 2) {  // 只有当空格在合理位置时才截断
                    text = text.substring(0, lastSpaceIndex);
                }
            }
        }

        return text;
    }

    /**
     * 从图像文件提取特征向量
     *
     * @param imageFile 图像文件
     * @return 特征向量的字节数组，提取失败返回null
     */
    public byte[] extractImageFeatures(MultipartFile imageFile) {
        try {
            log.debug("发送请求到API: /extract_image_features (文件上传)");

            // 构建多部分请求
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 添加文件部分
            ByteArrayResource fileResource = new ByteArrayResource(imageFile.getBytes()) {
                @Override
                public String getFilename() {
                    return imageFile.getOriginalFilename();
                }
            };

            body.add("file", fileResource);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 创建HTTP实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    clipApiUrl + "/extract_image_features",
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                String base64Features = (String) response.getBody().get("features");
                return Base64.getDecoder().decode(base64Features);
            } else {
                log.error("API调用失败: /extract_image_features, 状态码: {}", response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("API调用异常: /extract_image_features", e);
            return null;
        }
    }

    /**
     * 从图像URL提取特征向量
     *
     * @param imageUrl 图像URL
     * @return 特征向量的字节数组，提取失败返回null
     */
    public byte[] extractImageFeaturesFromUrl(String imageUrl) {
        try {
            log.debug("从URL提取图像特征: {}", imageUrl);

            // 下载图像
            ResponseEntity<byte[]> imageResponse = restTemplate.getForEntity(imageUrl, byte[].class);
            if (imageResponse.getStatusCode() != HttpStatus.OK || imageResponse.getBody() == null) {
                log.error("下载图像失败: {}", imageUrl);
                return null;
            }

            // 构建多部分请求
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 添加文件部分
            ByteArrayResource fileResource = new ByteArrayResource(imageResponse.getBody()) {
                @Override
                public String getFilename() {
                    return "image.jpg"; // 默认文件名
                }
            };

            body.add("file", fileResource);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 创建HTTP实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    clipApiUrl + "/extract_image_features",
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                String base64Features = (String) response.getBody().get("features");
                return Base64.getDecoder().decode(base64Features);
            } else {
                log.error("API调用失败: /extract_image_features, 状态码: {}", response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("从URL提取图像特征时发生异常: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 将特征向量添加到索引
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @param featureVector 特征向量
     * @param indexType 索引类型（TEXT/IMAGE）
     * @return 向量ID，添加失败返回null
     */
    public Long addVectorToIndex(Long itemId, String itemType, byte[] featureVector, String indexType) {
        try {
            log.debug("发送请求到API: /add_vector");

            // 将特征向量转换为Base64编码
            String base64Features = Base64.getEncoder().encodeToString(featureVector);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("item_id", itemId);
            requestBody.put("item_type", itemType);
            requestBody.put("index_type", indexType);
            requestBody.put("features", base64Features);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建HTTP实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    clipApiUrl + "/add_vector",
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Number vectorId = (Number) response.getBody().get("vector_id");
                return vectorId != null ? vectorId.longValue() : null;
            } else {
                log.error("API调用失败: /add_vector, 状态码: {}", response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("API调用异常: /add_vector", e);
            return null;
        }
    }

    /**
     * 从索引中移除向量
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @param indexType 索引类型（TEXT/IMAGE），如果为null则移除所有类型的向量
     * @return 是否成功
     */
    public boolean removeVectorFromIndex(Long itemId, String itemType, String indexType) {
        try {
            log.debug("发送请求到API: /remove_vector");

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("item_id", itemId);
            requestBody.put("item_type", itemType);
            if (indexType != null) {
                requestBody.put("index_type", indexType);
            }

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建HTTP实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    clipApiUrl + "/remove_vector",
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Boolean success = (Boolean) response.getBody().get("success");
                return Boolean.TRUE.equals(success);
            } else {
                log.error("API调用失败: /remove_vector, 状态码: {}", response.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            log.error("API调用异常: /remove_vector", e);
            return false;
        }
    }

    /**
     * 搜索相似向量
     *
     * @param featureVector 特征向量
     * @param targetType 目标物品类型（LOST/FOUND）
     * @param indexType 索引类型（TEXT/IMAGE）
     * @param limit 返回结果数量限制
     * @return 搜索结果列表，搜索失败返回空列表
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> searchSimilarVectors(byte[] featureVector, String targetType, String indexType, int limit) {
        try {
            log.debug("发送请求到API: /search_similar");

            // 将特征向量转换为Base64编码
            String base64Features = Base64.getEncoder().encodeToString(featureVector);

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("features", base64Features);
            requestBody.put("item_type", targetType);
            requestBody.put("index_type", indexType);
            requestBody.put("limit", limit);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建HTTP实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    clipApiUrl + "/search_similar",
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                List<Map<String, Object>> results = (List<Map<String, Object>>) response.getBody().get("results");
                return results != null ? results : List.of();
            } else {
                log.error("API调用失败: /search_similar, 状态码: {}", response.getStatusCode());
                return List.of();
            }
        } catch (Exception e) {
            log.error("API调用异常: /search_similar", e);
            return List.of();
        }
    }

    /**
     * 混合搜索，同时使用文本和图像特征
     *
     * @param textFeatures 文本特征向量（可选）
     * @param imageFeatures 图像特征向量（可选）
     * @param targetType 目标物品类型（LOST/FOUND）
     * @param limit 返回结果数量限制
     * @return 搜索结果列表，搜索失败返回空列表
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> searchMixedVectors(byte[] textFeatures, byte[] imageFeatures, String targetType, int limit) {
        try {
            log.info("【CLIP+FAISS】发送请求到API: /search_mixed, 目标类型: {}, 限制: {}", targetType, limit);

            // 检查是否至少有一种特征向量
            if (textFeatures == null && imageFeatures == null) {
                log.error("【CLIP+FAISS】没有提供任何特征向量，无法执行混合搜索");
                return List.of();
            }

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();

            // 添加文本特征（如果有）
            if (textFeatures != null) {
                requestBody.put("text_features", Base64.getEncoder().encodeToString(textFeatures));
                log.debug("【CLIP+FAISS】添加文本特征到请求体, 大小: {} 字节", textFeatures.length);
            }

            // 添加图像特征（如果有）
            if (imageFeatures != null) {
                requestBody.put("image_features", Base64.getEncoder().encodeToString(imageFeatures));
                log.debug("【CLIP+FAISS】添加图像特征到请求体, 大小: {} 字节", imageFeatures.length);
            }

            // 添加目标类型和限制
            requestBody.put("target_type", targetType);
            requestBody.put("limit", limit);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建HTTP实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            String url = clipApiUrl + "/search_mixed";
            log.info("【CLIP+FAISS】请求URL: {}", url);

            ResponseEntity<Map> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                List<Map<String, Object>> results = (List<Map<String, Object>>) response.getBody().get("results");
                log.info("【CLIP+FAISS】成功获取混合搜索结果, 数量: {}", results != null ? results.size() : 0);
                return results != null ? results : List.of();
            } else {
                log.error("【CLIP+FAISS】API调用失败: /search_mixed, 状态码: {}", response.getStatusCode());
                return List.of();
            }
        } catch (Exception e) {
            log.error("【CLIP+FAISS】API调用异常: /search_mixed", e);
            return List.of();
        }
    }

    /**
     * 使用图像和文本特征向量进行混合搜索，返回综合排序后的结果
     *
     * @param imageFeatures 图像特征向量（可选）
     * @param textFeatures 文本特征向量（可选）
     * @param targetType 目标物品类型（LOST/FOUND）
     * @param limit 返回结果数量限制
     * @return 搜索结果Map，包含综合排序后的结果，搜索失败返回空Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> searchByMixedVectors(byte[] imageFeatures, byte[] textFeatures, String targetType, int limit) {
        try {
            log.info("【CLIP+FAISS】发送请求到API: /multimodal_search, 目标类型: {}, 限制: {}, 图像特征: {}, 文本特征: {}",
                    targetType, limit,
                    imageFeatures != null ? imageFeatures.length + "字节" : "无",
                    textFeatures != null ? textFeatures.length + "字节" : "无");

            // 检查是否至少有一种特征向量
            if (textFeatures == null && imageFeatures == null) {
                log.error("【CLIP+FAISS】没有提供任何特征向量，无法执行混合搜索");
                return Map.of();
            }

            // 构建多部分请求
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 添加文本特征（如果有）
            if (textFeatures != null) {
                body.add("text_features", Base64.getEncoder().encodeToString(textFeatures));
                log.debug("【CLIP+FAISS】添加文本特征到请求体, 大小: {} 字节", textFeatures.length);
            }

            // 添加图像特征（如果有）
            if (imageFeatures != null) {
                body.add("image_features", Base64.getEncoder().encodeToString(imageFeatures));
                log.debug("【CLIP+FAISS】添加图像特征到请求体, 大小: {} 字节", imageFeatures.length);
            }

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 创建HTTP实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 构建URL并添加查询参数
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(clipApiUrl + "/multimodal_search")
                    .queryParam("target_type", targetType)
                    .queryParam("limit", limit);

            String url = builder.toUriString();
            log.info("【CLIP+FAISS】请求URL: {}", url);

            ResponseEntity<Map> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> results = response.getBody();
                log.info("【CLIP+FAISS】混合搜索结果包含以下键: {}", results.keySet());

                // 获取综合排序后的结果
                List<Map<String, Object>> combinedResults = (List<Map<String, Object>>) results.get("results");
                log.info("【CLIP+FAISS】综合匹配结果数量: {}", combinedResults != null ? combinedResults.size() : 0);

                // 记录前几个结果的详细信息，特别是四种相似度
                if (combinedResults != null && !combinedResults.isEmpty()) {
                    for (int i = 0; i < Math.min(3, combinedResults.size()); i++) {
                        Map<String, Object> result = combinedResults.get(i);
                        log.info("【CLIP+FAISS】综合结果 #{}: ID={}, 相似度={}, 匹配类型={}",
                                i+1, result.get("item_id"), result.get("similarity"), result.get("match_type"));

                        // 记录四种相似度的详细信息
                        if (result.containsKey("match_details")) {
                            Map<String, Object> details = (Map<String, Object>) result.get("match_details");
                            log.info("【CLIP+FAISS】四种相似度: TEXT_TO_TEXT={}, IMAGE_TO_IMAGE={}, TEXT_TO_IMAGE={}, IMAGE_TO_TEXT={}",
                                    details.get("TEXT_TO_TEXT"), details.get("IMAGE_TO_IMAGE"),
                                    details.get("TEXT_TO_IMAGE"), details.get("IMAGE_TO_TEXT"));
                        } else {
                            log.warn("【CLIP+FAISS】结果项缺少match_details字段");
                        }
                    }
                }

                // 直接返回原始结果，保持与Python API的结构一致
                return results;
            } else {
                log.error("【CLIP+FAISS】API调用失败: /multimodal_search, 状态码: {}", response.getStatusCode());
                return Map.of();
            }
        } catch (Exception e) {
            log.error("【CLIP+FAISS】API调用异常: /multimodal_search", e);
            e.printStackTrace();
            return Map.of();
        }
    }

    /**
     * 使用图像特征向量搜索相似图像
     *
     * @param imageFeatures 图像特征向量
     * @param targetType 目标物品类型（LOST/FOUND）
     * @param limit 返回结果数量限制
     * @return 搜索结果Map，包含results字段，搜索失败返回空Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> searchByImageVector(byte[] imageFeatures, String targetType, int limit) {
        try {
            log.info("【CLIP+FAISS】发送请求到API: /multimodal_search, 目标类型: {}, 限制: {}, 特征向量大小: {} 字节",
                    targetType, limit, imageFeatures != null ? imageFeatures.length : 0);

            // 检查特征向量是否为空
            if (imageFeatures == null) {
                log.error("【CLIP+FAISS】没有提供图像特征向量，无法执行搜索");
                return Map.of();
            }

            // 构建多部分请求
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 添加图像特征向量（Base64编码）
            String base64Features = Base64.getEncoder().encodeToString(imageFeatures);
            body.add("image_features", base64Features);
            log.debug("【CLIP+FAISS】添加图像特征向量到请求体，Base64编码后大小: {} 字符", base64Features.length());

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 创建HTTP实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 构建URL并添加查询参数
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(clipApiUrl + "/multimodal_search")
                    .queryParam("target_type", targetType)
                    .queryParam("limit", limit);

            String url = builder.toUriString();
            log.info("【CLIP+FAISS】请求URL: {}", url);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> results = response.getBody();
                log.info("【CLIP+FAISS】多模态搜索结果包含以下键: {}", results.keySet());

                // 记录结果数量
                if (results.containsKey("results")) {
                    List<Map<String, Object>> searchResults = (List<Map<String, Object>>) results.get("results");
                    log.info("【CLIP+FAISS】搜索结果数量: {}", searchResults != null ? searchResults.size() : 0);

                    // 记录前几个结果的详细信息
                    if (searchResults != null && !searchResults.isEmpty()) {
                        for (int i = 0; i < Math.min(3, searchResults.size()); i++) {
                            Map<String, Object> result = searchResults.get(i);
                            log.info("【CLIP+FAISS】搜索结果 #{}: ID={}, 相似度={}, 匹配类型={}",
                                    i+1, result.get("item_id"), result.get("similarity"), result.get("match_type"));

                            // 记录匹配详情
                            if (result.containsKey("match_details")) {
                                Map<String, Object> details = (Map<String, Object>) result.get("match_details");
                                log.info("【CLIP+FAISS】匹配详情: IMAGE_TO_IMAGE={}, IMAGE_TO_TEXT={}, TEXT_TO_IMAGE={}, TEXT_TO_TEXT={}",
                                        details.get("IMAGE_TO_IMAGE"), details.get("IMAGE_TO_TEXT"),
                                        details.get("TEXT_TO_IMAGE"), details.get("TEXT_TO_TEXT"));
                            }
                        }
                    } else {
                        log.warn("【CLIP+FAISS】搜索未返回任何结果");
                    }
                }

                // 为了保持与之前接口的兼容性，将results字段的内容提取出来作为image_to_image结果
                if (results.containsKey("results")) {
                    List<Map<String, Object>> searchResults = (List<Map<String, Object>>) results.get("results");

                    // 创建一个新的结果Map，包含image_to_image和image_to_text字段
                    Map<String, Object> compatResults = new HashMap<>();
                    compatResults.put("image_to_image", searchResults);
                    compatResults.put("image_to_text", List.of());  // 空列表，保持兼容性

                    return compatResults;
                }

                return results;
            } else {
                log.error("【CLIP+FAISS】API调用失败: /multimodal_search, 状态码: {}", response.getStatusCode());
                return Map.of();
            }
        } catch (Exception e) {
            log.error("【CLIP+FAISS】API调用异常: /multimodal_search", e);
            e.printStackTrace();
            return Map.of();
        }
    }

    /**
     * 使用文本特征向量搜索
     *
     * @param textFeatures 文本特征向量
     * @param targetType 目标物品类型（LOST/FOUND）
     * @param limit 返回结果数量限制
     * @return 搜索结果Map，包含text_to_text、text_to_image和combined结果，搜索失败返回空Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> searchByTextVector(byte[] textFeatures, String targetType, int limit) {
        try {
            log.info("【CLIP+FAISS】发送请求到API: /text_search, 目标类型: {}, 限制: {}", targetType, limit);

            // 检查特征向量是否为空
            if (textFeatures == null) {
                log.error("【CLIP+FAISS】没有提供文本特征向量，无法执行搜索");
                return Map.of();
            }

            // 将特征向量转换为Base64编码
            String base64Features = Base64.getEncoder().encodeToString(textFeatures);
            log.debug("【CLIP+FAISS】添加文本特征到请求体, 大小: {} 字节", textFeatures.length);

            // 构建多部分请求
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("text_features", base64Features);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 创建HTTP实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 构建URL并添加查询参数
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(clipApiUrl + "/text_search")
                    .queryParam("target_type", targetType)
                    .queryParam("limit", limit);

            String url = builder.toUriString();
            log.info("【CLIP+FAISS】请求URL: {}", url);

            ResponseEntity<Map> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> results = response.getBody();
                log.info("【CLIP+FAISS】文本搜索结果包含以下键: {}", results.keySet());

                // 记录各类型结果数量
                if (results.containsKey("text_to_text")) {
                    List<Map<String, Object>> textToTextResults = (List<Map<String, Object>>) results.get("text_to_text");
                    log.info("【CLIP+FAISS】文本-文本匹配结果数量: {}", textToTextResults != null ? textToTextResults.size() : 0);

                    // 记录前几个结果的详细信息
                    if (textToTextResults != null && !textToTextResults.isEmpty()) {
                        for (int i = 0; i < Math.min(3, textToTextResults.size()); i++) {
                            Map<String, Object> result = textToTextResults.get(i);
                            log.info("【CLIP+FAISS】文本-文本结果 #{}: ID={}, 相似度={}",
                                    i+1, result.get("item_id"), result.get("similarity"));
                        }
                    } else {
                        log.warn("【CLIP+FAISS】文本-文本搜索未返回任何结果");
                    }
                }

                if (results.containsKey("text_to_image")) {
                    List<Map<String, Object>> textToImageResults = (List<Map<String, Object>>) results.get("text_to_image");
                    log.info("【CLIP+FAISS】文本-图像匹配结果数量: {}", textToImageResults != null ? textToImageResults.size() : 0);

                    // 记录前几个结果的详细信息
                    if (textToImageResults != null && !textToImageResults.isEmpty()) {
                        for (int i = 0; i < Math.min(3, textToImageResults.size()); i++) {
                            Map<String, Object> result = textToImageResults.get(i);
                            log.info("【CLIP+FAISS】文本-图像结果 #{}: ID={}, 相似度={}",
                                    i+1, result.get("item_id"), result.get("similarity"));
                        }
                    } else {
                        log.warn("【CLIP+FAISS】文本-图像搜索未返回任何结果");
                    }
                }

                if (results.containsKey("combined")) {
                    List<Map<String, Object>> combinedResults = (List<Map<String, Object>>) results.get("combined");
                    log.info("【CLIP+FAISS】综合匹配结果数量: {}", combinedResults != null ? combinedResults.size() : 0);
                }

                return results;
            } else {
                log.error("【CLIP+FAISS】API调用失败: /text_search, 状态码: {}", response.getStatusCode());
                return Map.of();
            }
        } catch (Exception e) {
            log.error("【CLIP+FAISS】API调用异常: /text_search", e);
            e.printStackTrace();
            return Map.of();
        }
    }

    /**
     * 上传图像并提取特征向量
     *
     * @param imageFile 图像文件
     * @param itemType 物品类型
     * @return 特征向量的字节数组，提取失败返回null
     */
    public byte[] uploadAndExtractFeatures(MultipartFile imageFile, String itemType) {
        try {
            log.debug("发送请求到API: /upload_and_extract");

            // 构建多部分请求
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 添加文件部分
            ByteArrayResource fileResource = new ByteArrayResource(imageFile.getBytes()) {
                @Override
                public String getFilename() {
                    return imageFile.getOriginalFilename();
                }
            };
            body.add("file", fileResource);

            // 添加物品类型
            body.add("item_type", itemType);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 创建HTTP实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    clipApiUrl + "/upload_and_extract",
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                String base64Features = (String) response.getBody().get("features");
                return Base64.getDecoder().decode(base64Features);
            } else {
                log.error("API调用失败: /upload_and_extract, 状态码: {}", response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("API调用异常: /upload_and_extract", e);
            return null;
        }
    }

    /**
     * 保存所有索引到磁盘
     *
     * @return 是否成功
     */
    public boolean saveIndices() {
        try {
            log.debug("发送请求到API: /save_indices");

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建HTTP实体
            HttpEntity<String> requestEntity = new HttpEntity<>("{}", headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    clipApiUrl + "/save_indices",
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Boolean success = (Boolean) response.getBody().get("success");
                return Boolean.TRUE.equals(success);
            } else {
                log.error("API调用失败: /save_indices, 状态码: {}", response.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            log.error("API调用异常: /save_indices", e);
            return false;
        }
    }

    /**
     * 获取索引统计信息
     *
     * @return 索引统计信息Map，获取失败返回空Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getIndexStats() {
        try {
            log.debug("发送请求到API: /index_stats");

            // 发送请求
            ResponseEntity<Map> response = restTemplate.getForEntity(
                    clipApiUrl + "/index_stats",
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> stats = (Map<String, Object>) response.getBody().get("stats");
                return stats != null ? stats : Map.of();
            } else {
                log.error("API调用失败: /index_stats, 状态码: {}", response.getStatusCode());
                return Map.of();
            }
        } catch (Exception e) {
            log.error("API调用异常: /index_stats", e);
            return Map.of();
        }
    }

    /**
     * 重建索引
     *
     * @param itemType 物品类型（LOST/FOUND/ALL）
     * @param indexType 索引类型（TEXT/IMAGE/ALL）
     * @return 是否成功
     */
    public boolean rebuildIndex(String itemType, String indexType) {
        try {
            log.debug("发送请求到API: /rebuild_index");

            // 构建多部分请求
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("item_type", itemType);
            body.add("index_type", indexType);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 创建HTTP实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    clipApiUrl + "/rebuild_index",
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Boolean success = (Boolean) response.getBody().get("success");
                return Boolean.TRUE.equals(success);
            } else {
                log.error("API调用失败: /rebuild_index, 状态码: {}", response.getStatusCode());
                return false;
            }
        } catch (Exception e) {
            log.error("API调用异常: /rebuild_index", e);
            return false;
        }
    }

    /**
     * 使用图像文件搜索相似物品
     *
     * @param imageFile 图像文件
     * @param targetType 目标物品类型（LOST/FOUND）
     * @param limit 返回结果数量限制
     * @return 搜索结果Map，包含image_to_image、image_to_text和combined结果，搜索失败返回空Map
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> searchByImageFile(MultipartFile imageFile, String targetType, int limit) {
        try {
            log.info("【CLIP+FAISS】发送请求到API: /image_search, 目标类型: {}, 限制: {}", targetType, limit);

            // 检查文件是否为空
            if (imageFile == null || imageFile.isEmpty()) {
                log.error("【CLIP+FAISS】没有提供图像文件，无法执行搜索");
                return Map.of();
            }

            // 记录文件信息
            log.info("【CLIP+FAISS】图像文件信息: 名称={}, 大小={}, 内容类型={}",
                    imageFile.getOriginalFilename(),
                    imageFile.getSize(),
                    imageFile.getContentType());

            // 构建多部分请求
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 添加文件部分
            ByteArrayResource fileResource = new ByteArrayResource(imageFile.getBytes()) {
                @Override
                public String getFilename() {
                    return imageFile.getOriginalFilename() != null ? imageFile.getOriginalFilename() : "image.jpg";
                }
            };
            body.add("file", fileResource);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 创建HTTP实体
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 构建URL并添加查询参数
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(clipApiUrl + "/image_search")
                    .queryParam("target_type", targetType)
                    .queryParam("limit", limit);

            String url = builder.toUriString();
            log.info("【CLIP+FAISS】请求URL: {}", url);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> results = response.getBody();
                log.info("【CLIP+FAISS】图像搜索结果包含以下键: {}", results.keySet());

                // 记录各类型结果数量
                if (results.containsKey("image_to_image")) {
                    List<Map<String, Object>> imageToImageResults = (List<Map<String, Object>>) results.get("image_to_image");
                    log.info("【CLIP+FAISS】图像-图像匹配结果数量: {}", imageToImageResults != null ? imageToImageResults.size() : 0);

                    // 记录前几个结果的详细信息
                    if (imageToImageResults != null && !imageToImageResults.isEmpty()) {
                        for (int i = 0; i < Math.min(3, imageToImageResults.size()); i++) {
                            Map<String, Object> result = imageToImageResults.get(i);
                            log.info("【CLIP+FAISS】图像-图像结果 #{}: ID={}, 相似度={}",
                                    i+1, result.get("item_id"), result.get("similarity"));
                        }
                    } else {
                        log.warn("【CLIP+FAISS】图像-图像搜索未返回任何结果");
                    }
                }

                if (results.containsKey("image_to_text")) {
                    List<Map<String, Object>> imageToTextResults = (List<Map<String, Object>>) results.get("image_to_text");
                    log.info("【CLIP+FAISS】图像-文本匹配结果数量: {}", imageToTextResults != null ? imageToTextResults.size() : 0);

                    // 记录前几个结果的详细信息
                    if (imageToTextResults != null && !imageToTextResults.isEmpty()) {
                        for (int i = 0; i < Math.min(3, imageToTextResults.size()); i++) {
                            Map<String, Object> result = imageToTextResults.get(i);
                            log.info("【CLIP+FAISS】图像-文本结果 #{}: ID={}, 相似度={}",
                                    i+1, result.get("item_id"), result.get("similarity"));
                        }
                    } else {
                        log.warn("【CLIP+FAISS】图像-文本搜索未返回任何结果");
                    }
                }

                return results;
            } else {
                log.error("【CLIP+FAISS】API调用失败: /image_search, 状态码: {}", response.getStatusCode());
                return Map.of();
            }
        } catch (Exception e) {
            log.error("【CLIP+FAISS】API调用异常: /image_search", e);
            e.printStackTrace();
            return Map.of();
        }
    }
}
