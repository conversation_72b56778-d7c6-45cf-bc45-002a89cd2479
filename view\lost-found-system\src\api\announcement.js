import request from '@/utils/request'

// 获取有效的系统公告
export function getValidAnnouncements() {
  return request({
    url: '/announcements/valid',
    method: 'get',
    // 添加错误处理
    errorHandler: (error) => {
      // 如果是401或403错误，静默处理
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        console.log('用户未登录或无权限，静默处理公告请求');
        return {
          code: 200,
          message: '获取系统公告失败，但不影响使用',
          data: []
        };
      }
      console.error('获取有效系统公告时出错:', error);
      return {
        code: 200,
        message: '获取系统公告失败，但不影响使用',
        data: []
      };
    }
  })
}

// 获取最新的系统公告
export function getLatestAnnouncement() {
  return request({
    url: '/announcements/latest',
    method: 'get',
    // 添加错误处理
    errorHandler: (error) => {
      // 如果是401或403错误，静默处理
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        console.log('用户未登录或无权限，静默处理公告请求');
        return {
          code: 200,
          message: '获取系统公告失败，但不影响使用',
          data: null
        };
      }
      console.error('获取最新系统公告时出错:', error);
      return {
        code: 200,
        message: '获取系统公告失败，但不影响使用',
        data: null
      };
    }
  })
}

// 获取系统公告详情
export function getAnnouncementDetail(id) {
  return request({
    url: `/announcements/detail/${id}`,
    method: 'get'
  })
}

// 标记公告为已读
export function markAnnouncementAsRead(id) {
  console.log('【API调试】调用标记公告已读API，ID:', id);
  return request({
    url: `/announcements/mark-read/${id}`,
    method: 'post',
    // 添加错误处理
    errorHandler: (error) => {
      console.error('【API错误】标记公告已读失败:', error);
      // 返回错误响应，便于上层处理
      return {
        code: 500,
        message: '标记公告已读失败: ' + (error.message || '未知错误'),
        data: null
      };
    }
  })
}

// 获取未读公告数量
export function getUnreadAnnouncementCount() {
  return request({
    url: '/announcements/unread/count',
    method: 'get'
  })
}

// 管理员接口：获取所有公告
export function getAllAnnouncements() {
  return request({
    url: '/announcements/all',
    method: 'get'
  })
}

// 管理员接口：创建公告
export function createAnnouncement(data) {
  return request({
    url: '/announcements/create',
    method: 'post',
    data
  })
}

// 管理员接口：更新公告
export function updateAnnouncement(data) {
  return request({
    url: '/announcements/update',
    method: 'put',
    data
  })
}

// 管理员接口：删除公告
export function deleteAnnouncement(id) {
  return request({
    url: `/announcements/delete/${id}`,
    method: 'delete'
  })
}

// 管理员接口：发布公告
export function publishAnnouncement(id) {
  return request({
    url: `/announcements/publish/${id}`,
    method: 'post'
  })
}
