package com.tomato.lostfoundsystem.controller;

import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.FoundItem;
import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.entity.User;
import com.tomato.lostfoundsystem.enums.FileType;
import com.tomato.lostfoundsystem.mapper.FoundItemMapper;
import com.tomato.lostfoundsystem.mapper.LostItemMapper;
import com.tomato.lostfoundsystem.mapper.UserMapper;
import com.tomato.lostfoundsystem.service.AutoMatchNotificationService;
import com.tomato.lostfoundsystem.service.IntelligentMatchService;
import com.tomato.lostfoundsystem.utils.AliyunSmsUtil;
import com.tomato.lostfoundsystem.utils.ClipFaissClientRefactored;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器，用于测试各种功能
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AliyunSmsUtil aliyunSmsUtil;

    @Autowired
    private JavaMailSender javaMailSender;

    @Autowired
    private AutoMatchNotificationService autoMatchNotificationService;

    @Autowired
    private LostItemMapper lostItemMapper;

    @Autowired
    private FoundItemMapper foundItemMapper;

    @Autowired
    private SecurityUtil securityUtil;

    @Autowired
    private ClipFaissClientRefactored clipFaissClient;

    @Autowired
    private IntelligentMatchService intelligentMatchService;

    @Autowired
    private com.tomato.lostfoundsystem.utils.AliyunOSSUtil aliyunOSSUtil;

    @Value("${spring.mail.username}")
    private String senderEmail;

    @GetMapping("/user")
    public User getUser(@RequestParam String username) {
        return userMapper.findByUsername(username);
    }

    @GetMapping("/insert")
    public String testInsert() {
        User user = new User();
        user.setUsername("testUser");
        user.setPassword("123456");
        user.setEmail("<EMAIL>");
        user.setPhone("1234567890");
        user.setRole("user");
        user.setCreateTime(LocalDateTime.now());

        userMapper.insertUser(user);

        return "插入成功";
    }

    /**
     * 测试短信发送功能
     */
    @GetMapping("/sms")
    public Result<String> testSms(@RequestParam String phone) {
        log.info("收到短信测试请求 - 手机号: {}", phone);

        try {
            String code = "123456"; // 测试验证码
            log.info("开始测试短信发送 - 手机号: {}, 验证码: {}", phone, code);

            SendSmsResponse response = aliyunSmsUtil.sendCode(phone, code);

            log.info("短信测试结果 - 状态码: {}, 消息: {}", response.getCode(), response.getMessage());

            StringBuilder result = new StringBuilder();
            result.append("短信发送结果:\n");
            result.append("- 状态码: ").append(response.getCode()).append("\n");
            result.append("- 消息: ").append(response.getMessage()).append("\n");
            result.append("- 请求ID: ").append(response.getRequestId()).append("\n");
            result.append("- 业务ID: ").append(response.getBizId());

            return Result.success(result.toString());
        } catch (Exception e) {
            log.error("短信测试失败", e);
            return Result.fail("短信发送失败: " + e.getMessage());
        }
    }

    /**
     * 测试邮件发送功能
     */
    @GetMapping("/email")
    public Result<String> testEmail(@RequestParam String to) {
        log.info("收到邮件测试请求 - 接收邮箱: {}", to);

        try {
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setFrom(senderEmail);
            helper.setTo(to);
            helper.setSubject("测试邮件");
            helper.setText("这是一个来自校园失物招领系统的测试邮件", true);

            log.info("准备发送测试邮件 - 发件人: {}, 收件人: {}", senderEmail, to);
            javaMailSender.send(message);
            log.info("测试邮件发送成功 - 收件人: {}", to);

            return Result.success("邮件发送成功！请检查收件箱或垃圾箱");
        } catch (MessagingException e) {
            log.error("邮件测试失败", e);
            return Result.fail("邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 测试公开接口
     */
    @GetMapping("/public")
    public Result<String> testPublic() {
        return Result.success("这是一个公开的测试接口，无需认证");
    }

    /**
     * 测试失物自动匹配
     */
    @GetMapping("/match-lost/{lostItemId}")
    public Result<?> testMatchLost(@PathVariable Long lostItemId) {
        Long userId = securityUtil.getCurrentUserId();
        if (userId == null) {
            return Result.fail("未登录");
        }

        try {
            // 查询失物信息
            LostItem lostItem = lostItemMapper.selectById(lostItemId);
            if (lostItem == null) {
                return Result.fail("失物信息不存在");
            }

            log.error("【测试】开始测试失物自动匹配，物品ID: {}, 用户ID: {}", lostItemId, userId);

            // 同步调用，不使用异步
            autoMatchNotificationService.processLostItemAutoMatch(lostItemId, userId);

            return Result.success("测试匹配已触发");
        } catch (Exception e) {
            log.error("【测试】测试失物自动匹配失败", e);
            return Result.fail("测试匹配失败: " + e.getMessage());
        }
    }

    /**
     * 测试拾物自动匹配
     */
    @GetMapping("/match-found/{foundItemId}")
    public Result<?> testMatchFound(@PathVariable Long foundItemId) {
        Long userId = securityUtil.getCurrentUserId();
        if (userId == null) {
            return Result.fail("未登录");
        }

        try {
            // 查询拾物信息
            FoundItem foundItem = foundItemMapper.selectById(foundItemId);
            if (foundItem == null) {
                return Result.fail("拾物信息不存在");
            }

            log.error("【测试】开始测试拾物自动匹配，物品ID: {}, 用户ID: {}", foundItemId, userId);

            // 同步调用，不使用异步
            autoMatchNotificationService.processFoundItemAutoMatch(foundItemId, userId);

            return Result.success("测试匹配已触发");
        } catch (Exception e) {
            log.error("【测试】测试拾物自动匹配失败", e);
            return Result.fail("测试匹配失败: " + e.getMessage());
        }
    }

    /**
     * 检查CLIP+FAISS服务状态
     */
    @GetMapping("/check-clip-service")
    public Result<?> checkClipService() {
        try {
            String clipApiUrl = "http://localhost:8000";
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map> response = restTemplate.getForEntity(clipApiUrl, Map.class);
            boolean isAvailable = response.getStatusCode() == HttpStatus.OK;

            Map<String, Object> result = new HashMap<>();
            result.put("available", isAvailable);
            result.put("statusCode", response.getStatusCode().toString());
            result.put("body", response.getBody());

            return Result.success(result);
        } catch (Exception e) {
            log.error("【测试】CLIP+FAISS服务连接失败", e);
            return Result.fail("CLIP+FAISS服务连接失败: " + e.getMessage());
        }
    }

    /**
     * 保存FAISS索引
     */
    @GetMapping("/save-indices")
    public Result<?> saveIndices() {
        try {
            log.error("【测试】开始保存FAISS索引");
            boolean success = clipFaissClient.saveIndices();
            return Result.success("保存索引" + (success ? "成功" : "失败"));
        } catch (Exception e) {
            log.error("【测试】保存FAISS索引失败", e);
            return Result.fail("保存索引失败: " + e.getMessage());
        }
    }

    /**
     * 重建所有索引
     */
    @GetMapping("/rebuild-indices")
    public Result<?> rebuildIndices() {
        try {
            log.error("【测试】开始重建所有索引");
            return intelligentMatchService.generateFeatureVectorsForAllItems();
        } catch (Exception e) {
            log.error("【测试】重建索引失败", e);
            return Result.fail("重建索引失败: " + e.getMessage());
        }
    }

    /**
     * 测试特征向量提取和存储
     * 这个方法用于验证特征向量是否正确存储到数据库中
     */
    @GetMapping("/test-feature-vector")
    public Result<?> testFeatureVector(@RequestParam String text) {
        try {
            log.info("【特征向量验证】开始测试特征向量提取和存储，文本: {}", text);

            // 1. 提取文本特征向量
            log.info("【特征向量验证】调用clipFaissClient.extractTextFeatures提取特征向量");
            byte[] textFeatures = clipFaissClient.extractTextFeatures(text);

            if (textFeatures == null) {
                log.error("【特征向量验证】提取特征向量失败");
                return Result.fail("提取特征向量失败");
            }

            log.info("【特征向量验证】成功提取特征向量，大小: {} 字节", textFeatures.length);

            // 2. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("text", text);
            result.put("vectorSize", textFeatures.length);
            result.put("success", true);

            return Result.success(result);
        } catch (Exception e) {
            log.error("【特征向量验证】测试特征向量提取和存储失败", e);
            return Result.fail("测试失败: " + e.getMessage());
        }
    }

    /**
     * 测试文件上传功能
     * 这个方法用于测试文件上传到阿里云OSS的功能
     */
    @PostMapping("/upload-file")
    public Result<?> testFileUpload(@RequestParam("file") MultipartFile file) {
        log.info("【文件上传测试】收到文件上传请求，文件名: {}, 大小: {} 字节", file.getOriginalFilename(), file.getSize());

        try {
            // 上传文件到阿里云OSS
            String fileUrl = aliyunOSSUtil.uploadFile(file, "test-uploads");

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            result.put("fileUrl", fileUrl);
            result.put("contentType", file.getContentType());

            log.info("【文件上传测试】文件上传成功，URL: {}", fileUrl);
            return Result.success(result);
        } catch (Exception e) {
            log.error("【文件上传测试】文件上传失败", e);
            return Result.fail("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 测试特定物品的特征向量生成
     * 这个方法用于手动触发特定物品的特征向量生成并存储到数据库
     */
    @GetMapping("/generate-vector/{itemType}/{itemId}")
    public Result<?> generateVector(@PathVariable String itemType, @PathVariable Long itemId) {
        try {
            log.info("【特征向量验证】开始为物品生成特征向量，类型: {}, ID: {}", itemType, itemId);

            String description = null;

            // 1. 获取物品信息
            if ("LOST".equalsIgnoreCase(itemType)) {
                LostItem lostItem = lostItemMapper.selectById(itemId);
                if (lostItem == null) {
                    return Result.fail("失物信息不存在");
                }
                description = lostItem.getItemName() + " " + (lostItem.getDescription() != null ? lostItem.getDescription() : "");
                log.info("【特征向量验证】获取到失物信息，描述: {}", description);
            } else if ("FOUND".equalsIgnoreCase(itemType)) {
                FoundItem foundItem = foundItemMapper.selectById(itemId);
                if (foundItem == null) {
                    return Result.fail("拾物信息不存在");
                }
                description = foundItem.getItemName() + " " + (foundItem.getDescription() != null ? foundItem.getDescription() : "");
                log.info("【特征向量验证】获取到拾物信息，描述: {}", description);
            } else {
                return Result.fail("无效的物品类型，必须是 LOST 或 FOUND");
            }

            // 2. 提取文本特征向量
            log.info("【特征向量验证】开始提取文本特征向量");
            byte[] textFeatures = clipFaissClient.extractTextFeatures(description);

            if (textFeatures == null) {
                log.error("【特征向量验证】提取文本特征向量失败");
                return Result.fail("提取文本特征向量失败");
            }

            log.info("【特征向量验证】成功提取文本特征向量，大小: {} 字节", textFeatures.length);

            // 3. 调用生成特征向量的方法
            log.info("【特征向量验证】开始调用generateFeatureVectorsForAllItems方法");
            Result<String> result = intelligentMatchService.generateFeatureVectorsForAllItems();
            log.info("【特征向量验证】generateFeatureVectorsForAllItems方法调用完成，结果: {}", result.getData());

            // 4. 返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("itemType", itemType);
            response.put("itemId", itemId);
            response.put("description", description);
            response.put("vectorSize", textFeatures.length);
            response.put("generateResult", result.getData());

            return Result.success(response);
        } catch (Exception e) {
            log.error("【特征向量验证】生成特征向量失败", e);
            return Result.fail("生成特征向量失败: " + e.getMessage());
        }
    }
}
