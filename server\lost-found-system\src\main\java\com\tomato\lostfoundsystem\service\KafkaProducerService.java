package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.model.kafka.ChatMessageEnvelope;
import com.tomato.lostfoundsystem.model.kafka.ItemApprovedEvent;

public interface KafkaProducerService {
    // 现有方法
    void storeOfflineMessage(String message);

    // 新增方法
    void sendNotification(String message);
    void sendSystemMessage(String message);
    void sendRetryMessage(String message);
    void sendToDeadLetterQueue(String message);
    void sendWithKey(String topic, String key, String message);

    /**
     * 发送物品审核通过事件
     *
     * @param event 物品审核通过事件
     * @return 是否发送成功
     */
    boolean sendItemApprovedEvent(ItemApprovedEvent event);

    /**
     * 发送聊天消息到Kafka
     *
     * @param messageDTO 聊天消息DTO
     * @return 是否发送成功
     */
    boolean sendChatMessage(MessageDTO messageDTO);

    /**
     * 发送聊天消息信封到Kafka
     *
     * @param envelope 聊天消息信封
     * @return 是否发送成功
     */
    boolean sendChatMessageEnvelope(ChatMessageEnvelope envelope);

    /**
     * 发送已读回执到Kafka
     *
     * @param readReceipt 已读回执DTO
     * @return 是否发送成功
     */
    boolean sendReadReceipt(com.tomato.lostfoundsystem.dto.ReadReceiptDTO readReceipt);
}
