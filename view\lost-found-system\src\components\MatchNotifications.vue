<template>
  <div class="match-notifications">
    <el-popover
      placement="bottom"
      :width="350"
      trigger="click"
      popper-class="notification-popover"
      v-model:visible="popoverVisible"
      :teleported="true"
    >
      <template #reference>
        <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="notification-badge">
          <el-button class="notification-button" :icon="Bell" circle />
        </el-badge>
      </template>

      <div class="notification-header">
        <h3>匹配通知</h3>
        <!-- TODO: 当升级到 Element Plus 3.0.0 时，将 type="text" 改为 type="link" -->
        <el-button v-if="notifications.length > 0" type="text" @click="markAllAsRead">
          全部标为已读
        </el-button>
      </div>

      <el-divider />

      <div v-if="loading" class="notification-loading">
        <el-skeleton :rows="3" animated />
      </div>

      <div v-else-if="notifications.length === 0" class="empty-notifications">
        <el-empty description="暂无匹配通知" />
      </div>

      <el-scrollbar max-height="400px" v-else>
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': !notification.isRead }"
          @click="handleNotificationClick(notification)"
        >
          <div class="notification-icon">
            <el-avatar :icon="Link" :size="40" :style="{ backgroundColor: getSimilarityColor(notification.similarity) }" />
          </div>
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-text">{{ formatNotificationContent(notification.content) }}</div>
            <div class="notification-time">{{ formatTime(notification.createdAt) }}</div>
          </div>
          <div class="notification-similarity">
            {{ (notification.similarity * 100).toFixed(0) }}%
          </div>
        </div>
      </el-scrollbar>

      <div class="notification-footer">
        <el-button type="primary" link @click="viewAllNotifications">
          查看全部通知
        </el-button>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { Bell, Link } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMatchNotifications, markMatchNotificationAsRead, getUnreadMatchNotificationCount } from '@/api/notification'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const router = useRouter()
const popoverVisible = ref(false)
const notifications = ref([])
const unreadCount = ref(0)
const loading = ref(false)

// 获取通知列表
const fetchNotifications = async () => {
  try {
    loading.value = true
    const res = await getMatchNotifications()
    if (res.code === 200) {
      notifications.value = res.data || []
    }
  } catch (error) {
    console.error('获取匹配通知失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取未读通知数量
const fetchUnreadCount = async () => {
  try {
    const res = await getUnreadMatchNotificationCount()
    if (res.code === 200) {
      unreadCount.value = res.data || 0
    }
  } catch (error) {
    console.error('获取未读通知数量失败:', error)
  }
}

// 标记通知为已读
const markAsRead = async (notificationId) => {
  try {
    const res = await markMatchNotificationAsRead(notificationId)
    if (res.code === 200) {
      // 更新本地通知状态
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.isRead = true
      }
      // 更新未读数量
      fetchUnreadCount()
    }
  } catch (error) {
    console.error('标记通知已读失败:', error)
  }
}

// 全部标为已读
const markAllAsRead = async () => {
  try {
    const unreadNotifications = notifications.value.filter(n => !n.isRead)
    if (unreadNotifications.length === 0) return

    for (const notification of unreadNotifications) {
      await markAsRead(notification.id)
    }

    ElMessage.success('已将所有通知标记为已读')
  } catch (error) {
    console.error('标记全部已读失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 处理通知点击
const handleNotificationClick = async (notification) => {
  // 如果未读，标记为已读
  if (!notification.isRead) {
    await markAsRead(notification.id)
  }

  // 关闭弹窗
  popoverVisible.value = false

  // 根据通知类型跳转到相应页面
  const itemType = notification.itemType
  const itemId = notification.itemId

  // 确认是否查看详情
  ElMessageBox.confirm(
    '是否查看匹配物品详情？',
    '提示',
    {
      confirmButtonText: '查看详情',
      cancelButtonText: '稍后查看',
      type: 'info'
    }
  ).then(() => {
    // 跳转到物品详情页
    const route = itemType === 'LOST'
      ? `/lost-items/detail/${itemId}`
      : `/found-items/detail/${itemId}`
    router.push(route)
  }).catch(() => {
    // 用户取消，不做任何操作
  })
}

// 查看全部通知
const viewAllNotifications = () => {
  popoverVisible.value = false
  router.push('/profile/notifications')
}

// 格式化通知内容（只显示前30个字符）
const formatNotificationContent = (content) => {
  if (!content) return ''
  const maxLength = 30
  return content.length > maxLength ? content.substring(0, maxLength) + '...' : content
}

// 格式化时间为"几分钟前"的形式
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

// 根据相似度获取颜色
const getSimilarityColor = (similarity) => {
  if (similarity >= 0.9) return '#67C23A' // 高相似度 - 绿色
  if (similarity >= 0.7) return '#E6A23C' // 中等相似度 - 黄色
  return '#909399' // 低相似度 - 灰色
}

// 定时刷新通知
let refreshInterval = null

onMounted(() => {
  fetchNotifications()
  fetchUnreadCount()

  // 每分钟刷新一次未读通知数量
  refreshInterval = setInterval(() => {
    fetchUnreadCount()
  }, 60000)
})

onBeforeUnmount(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>

<style scoped>
.match-notifications {
  display: inline-block;
}

.notification-badge {
  margin-right: 10px;
}

.notification-button {
  font-size: 18px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.notification-header h3 {
  margin: 0;
  font-size: 16px;
}

.notification-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #f0f7ff;
}

.notification-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: bold;
  margin-bottom: 4px;
  color: #303133;
}

.notification-text {
  font-size: 13px;
  color: #606266;
  margin-bottom: 4px;
  white-space: pre-line;
  word-break: break-word;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-similarity {
  font-size: 14px;
  font-weight: bold;
  color: #409EFF;
  margin-left: 8px;
  align-self: center;
}

.notification-footer {
  text-align: center;
  padding: 10px 0;
  border-top: 1px solid #f0f0f0;
}

.empty-notifications {
  padding: 20px 0;
}

.notification-loading {
  padding: 10px;
}
</style>
