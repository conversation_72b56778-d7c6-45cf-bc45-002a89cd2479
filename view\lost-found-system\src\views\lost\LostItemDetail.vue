<template>
  <div class="lost-item-detail">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="handleBack" link>
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h2>失物详情</h2>
      </div>
    </div>
    <el-card class="detail-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <h2 class="title">失物详情</h2>
          <div class="actions">
            <el-button type="primary" @click="handleBack">
              <el-icon><Back /></el-icon>
              返回列表
            </el-button>
            <template v-if="itemDetail">
              <el-button
                v-if="isOwner && itemDetail.status === 'LOST'"
                type="primary"
                @click="router.push(`/lost-items/edit/${itemDetail.id}`)"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                v-if="isOwner && itemDetail.status === 'LOST'"
                type="danger"
                @click="handleDelete"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
              <el-button
                v-if="!isOwner && itemDetail.status === 'LOST'"
                type="info"
                @click="contactOwner"
              >
                <el-icon><ChatRound /></el-icon>
                联系失主
              </el-button>
            </template>
          </div>
        </div>
      </template>

      <div v-if="itemDetail" class="detail-content">
        <!-- 左侧图片区域 -->
        <div class="image-section" v-if="imageList.length > 0">
          <el-carousel
            :interval="4000"
            height="400px"
            :autoplay="imageList.length > 1"
            indicator-position="outside"
            class="image-carousel"
            arrow="always"
            v-if="imageList.length > 0"
          >
            <el-carousel-item v-for="(url, index) in imageList" :key="index">
              <div class="carousel-image-container">
                <el-image
                  :src="url"
                  fit="contain"
                  :preview-src-list="imageList"
                  :initial-index="index"
                  class="carousel-image"
                  :zoom-rate="1.2"
                  :preview-teleported="true"
                >
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                      <span>加载中...</span>
                    </div>
                  </template>
                  <template #error>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                      <span>加载失败</span>
                    </div>
                  </template>
                </el-image>
              </div>
            </el-carousel-item>
          </el-carousel>

          <!-- 缩略图预览 -->
          <div class="image-thumbnails" v-if="imageList.length > 1">
            <div
              v-for="(url, index) in imageList"
              :key="index"
              class="thumbnail-item"
              :class="{ active: index === activeImageIndex }"
              @click="setActiveImage(index)"
            >
              <el-image
                :src="url"
                fit="cover"
                class="thumbnail-image"
              />
            </div>
          </div>
        </div>

        <!-- 右侧信息区域 -->
        <div class="info-section">
          <div class="info-header">
            <h1 class="item-name">{{ itemDetail.itemName }}</h1>
            <el-tag :type="getStatusType(itemDetail.status)" class="status-tag">
              {{ itemDetail.status === 'LOST' ? '未认领' : '已认领' }}
            </el-tag>
          </div>

          <el-tabs v-model="activeTab" class="info-tabs">
            <el-tab-pane label="基本信息" name="basic">
              <el-descriptions :column="1" border class="detail-info">
                <el-descriptions-item label="物品名称">
                  <span class="info-value">{{ itemDetail.itemName }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="丢失地点">
                  <span class="info-value">{{ itemDetail.lostLocation }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="丢失时间">
                  <span class="info-value">{{ formatDateTime(itemDetail.lostTime) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="发布人">
                  <span class="info-value">{{ itemDetail.username || '匿名用户' }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </el-tab-pane>

            <el-tab-pane label="详细描述" name="description">
              <div class="description-box">
                {{ itemDetail.description }}
              </div>
            </el-tab-pane>

            <el-tab-pane label="时间信息" name="time">
              <el-descriptions :column="1" border class="detail-info">
                <el-descriptions-item label="发布时间">
                  <span class="info-value">{{ formatDateTime(itemDetail.createdAt) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="更新时间">
                  <span class="info-value">{{ formatDateTime(itemDetail.updatedAt) }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <el-empty v-else description="未找到失物信息" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Back, Edit, Delete, Picture, ArrowLeft, ChatRound } from '@element-plus/icons-vue'
import { getLostItemDetail, deleteLostItem } from '../../api/lost'
import { createConversation } from '../../api/chat'
import { useUserStore } from '../../stores'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const currentUserId = computed(() => userStore.userInfo?.id)

const loading = ref(false)
const itemDetail = ref(null)
const activeTab = ref('basic')
const activeImageIndex = ref(0)

// 图片列表
const imageList = computed(() => {
  if (!itemDetail.value) return []

  // 如果有imageUrls属性（多图片），使用它
  if (itemDetail.value.imageUrls && itemDetail.value.imageUrls.length > 0) {
    // 确保主图不重复显示
    if (itemDetail.value.imageUrl) {
      // 过滤掉与主图相同的URL，避免重复
      const filteredUrls = itemDetail.value.imageUrls.filter(url => url !== itemDetail.value.imageUrl)
      // 将主图放在第一位
      return [itemDetail.value.imageUrl, ...filteredUrls]
    }
    return itemDetail.value.imageUrls
  }

  // 否则，如果有单张图片，使用它
  if (itemDetail.value.imageUrl) {
    return [itemDetail.value.imageUrl]
  }

  return []
})

// 判断当前用户是否为物品所有者
const isOwner = computed(() => {
  return itemDetail.value?.userId === currentUserId.value
})

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'LOST':
      return 'warning'
    case 'FOUND':
      return 'success'
    default:
      return 'info'
  }
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '未知'

  try {
    const date = new Date(dateTimeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${year}年${month}月${day}日 ${hours}:${minutes}`
  } catch (error) {
    console.error('日期格式化错误：', error)
    return dateTimeStr
  }
}

// 处理返回
const handleBack = () => {
  if (route.query.from === 'admin') {
    router.push('/admin/lost-items')
  } else {
    router.push('/lost-items')
  }
}

// 获取失物详情
const fetchItemDetail = async () => {
  const id = route.params.id
  if (!id) {
    ElMessage.error('参数错误')
    return
  }

  try {
    loading.value = true
    const res = await getLostItemDetail(id)
    if (res.code === 200) {
      itemDetail.value = res.data
    } else {
      ElMessage.error(res.message || '获取详情失败')
    }
  } catch (error) {
    console.error('获取失物详情失败：', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 处理删除
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除这条失物信息吗？此操作不可恢复', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await deleteLostItem(itemDetail.value.id);
    ElMessage.success('删除成功');
    router.push('/lost-items');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

// 联系失主
const contactOwner = async () => {
  try {
    // 检查参数
    console.log('创建会话参数检查:', {
      currentUserId: currentUserId.value,
      ownerId: itemDetail.value.userId,
      userInfo: userStore.userInfo
    });

    // 参数验证
    if (!currentUserId.value) {
      ElMessage.warning({
        message: '您需要先登录才能联系失主',
        duration: 2000
      });
      return;
    }

    if (!itemDetail.value.userId) {
      ElMessage.warning({
        message: '无法获取失主信息，请刷新页面重试',
        duration: 2000
      });
      return;
    }

    // 创建会话
    const chatRes = await createConversation(currentUserId.value, itemDetail.value.userId);
    console.log('创建会话响应:', chatRes);

    if (chatRes.code === 200) {
      ElMessage.success({
        message: '已创建会话，即将跳转到聊天页面...',
        duration: 1500
      });

      // 延迟跳转，让用户看到提示信息
      setTimeout(() => {
        router.push('/chat');
      }, 1500);
    }
  } catch (error) {
    console.error('创建会话失败：', error);
    ElMessage.error('创建会话失败，请稍后重试');
  }
};

// 设置当前活动图片
const setActiveImage = (index) => {
  activeImageIndex.value = index
}

onMounted(() => {
  fetchItemDetail()
})
</script>

<style scoped>
.lost-item-detail {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #303133;
}

:deep(.el-button--text) {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

:deep(.el-button--text:hover) {
  color: #409eff;
}

.detail-card {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  margin: 0;
  font-size: 20px;
  color: #303133;
}

.actions {
  display: flex;
  gap: 8px;
}

.detail-content {
  display: flex;
  gap: 24px;
  padding: 20px;
}

.image-section {
  flex: 0 0 40%;
  max-width: 40%;
  background-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.image-carousel {
  width: 100%;
  height: 100%;
}

.carousel-image-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.info-section {
  flex: 2;
  min-width: 400px;
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.item-name {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  flex: 1;
}

.status-tag {
  font-size: 14px;
  padding: 6px 12px;
  margin-left: 16px;
}

.info-tabs {
  margin-bottom: 24px;
}

.detail-info {
  margin-bottom: 0;
}

.description-box {
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  min-height: 100px;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

.info-value {
  font-weight: 500;
  color: #303133;
}

.image-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
}

.image-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

:deep(.el-descriptions__label) {
  width: 120px;
  color: #606266;
  font-weight: 500;
}

:deep(.el-descriptions__content) {
  color: #303133;
}

:deep(.el-carousel__container) {
  height: 400px;
}

:deep(.el-image__inner) {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

:deep(.el-carousel__item) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-carousel__arrow) {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 36px;
  height: 36px;
}

:deep(.el-carousel__arrow:hover) {
  background-color: rgba(0, 0, 0, 0.5);
}

.image-thumbnails {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.thumbnail-item {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.thumbnail-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.thumbnail-item.active {
  border-color: var(--el-color-primary);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  font-size: 15px;
  padding: 0 20px;
}

:deep(.el-tabs__item.is-active) {
  font-weight: 600;
}

@media screen and (max-width: 768px) {
  .detail-content {
    flex-direction: column;
  }

  .image-section {
    flex: none;
    max-width: 100%;
    margin-bottom: 20px;
  }

  :deep(.el-carousel__container) {
    height: 300px;
  }

  .info-section {
    min-width: 0;
  }

  .info-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .status-tag {
    margin-left: 0;
    margin-top: 8px;
  }
}
</style>