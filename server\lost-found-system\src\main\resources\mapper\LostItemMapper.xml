<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomato.lostfoundsystem.mapper.LostItemMapper">
    <insert id="insertLostItem" parameterType="com.tomato.lostfoundsystem.entity.LostItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO lost_items (
            user_id,
            item_name,
            description,
            lost_time,
            lost_location,
            image_url,
            status,
            created_at,
            audit_status
        ) VALUES (
                     #{userId},
                     #{itemName},
                     #{description},
                     #{lostTime},
                     #{lostLocation},
                     #{imageUrl},
                     #{status},
                     #{createdAt},
                     #{auditStatus}
                 )
    </insert>
    <select id="selectLostItems" resultType="com.tomato.lostfoundsystem.entity.LostItem">
        SELECT
        li.id AS id,
        li.user_id,
        li.item_name,
        li.description,
        li.lost_time,
        li.lost_location,
        li.image_url,
        li.status,
        li.created_at,
        users.username,
        users.avatar
        FROM
        lost_items AS li
        JOIN
        users ON li.user_id = users.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND li.item_name LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="lostLocation != null and lostLocation != ''">
                AND li.lost_location LIKE CONCAT('%', #{lostLocation}, '%')
            </if>
            <if test="status != null and status != ''">
                AND li.status = #{status}
            </if>
            <if test="startDateTime != null and endDateTime != null">
                <choose>
                    <when test="timeFilterType == 'lostTime'">
                        AND li.lost_time BETWEEN #{startDateTime} AND #{endDateTime}
                    </when>
                    <when test="timeFilterType == 'createdAt'">
                        AND li.created_at BETWEEN #{startDateTime} AND #{endDateTime}
                    </when>
                    <otherwise>
                        AND li.created_at BETWEEN #{startDateTime} AND #{endDateTime}
                    </otherwise>
                </choose>
            </if>
            <!-- 只有审核通过的物品 -->
            AND li.audit_status = 'APPROVED' <!-- 确保查询仅审核通过的物品 -->
        </where>
        ORDER BY
        <choose>
            <when test="timeFilterType == 'lostTime'">
                li.lost_time DESC
            </when>
            <when test="timeFilterType == 'createdAt'">
                li.created_at DESC
            </when>
        </choose>
    </select>

    <select id="selectLostItemDetailsById" resultType="com.tomato.lostfoundsystem.dto.LostItemDetailsDTO">
        SELECT li.id AS id,
               li.user_id,
               li.item_name,
               li.description,
               li.lost_time,
               li.lost_location,
               li.image_url,
               li.status,
               li.created_at,
               u.username
        FROM lost_items li
                 JOIN users u ON li.user_id = u.id
        WHERE li.id = #{id}
    </select>


    <!-- 根据ID查询失物信息 -->
    <select id="selectById" resultType="com.tomato.lostfoundsystem.entity.LostItem">
        SELECT * FROM lost_items WHERE id = #{id}
    </select>

    <!-- 更新失物信息 -->
    <update id="updateById" parameterType="com.tomato.lostfoundsystem.entity.LostItem">
        UPDATE lost_items
        <set>
            <if test="itemName != null">item_Name = #{itemName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="lostLocation != null">lost_location = #{lostLocation},</if>
            <if test="lostTime != null">lost_time = #{lostTime},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
        </set>
        WHERE id = #{id} AND user_id = #{userId}
    </update>


    <!-- 删除失物信息 -->
    <delete id="deleteLostItem">
        DELETE FROM lost_items WHERE id = #{id}
    </delete>
    <!-- 根据用户ID查询失物信息-->
    <select id="findLostItemsByUserId" resultType="com.tomato.lostfoundsystem.entity.LostItem">
        SELECT
        li.id AS id,
        li.user_id AS userId,
        li.item_name AS itemName,
        li.description AS description,
        li.lost_time AS lostTime,
        li.lost_location AS lostLocation,
        li.image_url AS imageUrl,
        li.status AS status,
        li.created_at AS createdAt,
        li.audit_status AS auditStatus
        FROM
        lost_items AS li
        WHERE
        li.user_id = #{userId}
    </select>

   <!--审核表的查询-->
    <select id="selectAuditList" resultType="com.tomato.lostfoundsystem.dto.LostItemDetailsDTO">
        SELECT li.*, u.username
        FROM lost_items li
        JOIN users u ON li.user_id = u.id
        <where>
            <if test="keyword != null and keyword != ''">
                AND (li.item_name LIKE CONCAT('%', #{keyword}, '%')
                OR li.description LIKE CONCAT('%', #{keyword}, '%')
                OR li.lost_location LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="auditStatus != null and auditStatus != ''">
                AND li.audit_status = #{auditStatus}
            </if>
            <if test="status != null and status != ''">
                AND li.status = #{status}
            </if>
            <if test="startDate != null">
                AND li.created_at &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND li.created_at &lt;= #{endDate}
            </if>
            <if test="userId != null">
                AND li.user_id = #{userId}
            </if>
        </where>
        ORDER BY li.created_at DESC
    </select>

    <update id="updateAuditStatus" parameterType="com.tomato.lostfoundsystem.entity.LostItem">
        UPDATE lost_items
        SET audit_status = #{auditStatus, typeHandler=org.apache.ibatis.type.EnumTypeHandler}
        WHERE id = #{id}
    </update>

    <!-- 更新失物状态 -->
    <update id="updateLostItemStatus">
        UPDATE lost_items
        SET status = #{status}
        WHERE id = #{id}
    </update>

    <!-- 根据审核状态统计失物数量 -->
    <select id="countByAuditStatus" resultType="int">
        SELECT COUNT(*)
        FROM lost_items
        WHERE audit_status = #{auditStatus}
    </select>

    <!-- 根据状态和审核状态统计失物数量 -->
    <select id="countByStatusAndAuditStatus" resultType="int">
        SELECT COUNT(*)
        FROM lost_items
        WHERE status = #{status} AND audit_status = #{auditStatus}
    </select>
</mapper>