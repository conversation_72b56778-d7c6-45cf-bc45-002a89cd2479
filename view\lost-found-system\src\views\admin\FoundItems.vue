<template>
  <div class="admin-found-items">
    <div class="page-header">
      <div class="header-left">
        <h2>拾物管理</h2>
        <el-tag type="info" class="item-count">共 {{ total }} 条</el-tag>
      </div>
      <div class="header-right">
        <el-input
          v-model="keyword"
          placeholder="搜索物品名称/地点"
          class="search-input"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-select v-model="auditStatus" placeholder="审核状态" clearable @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="待审核" value="PENDING" />
          <el-option label="已通过" value="APPROVED" />
          <el-option label="已拒绝" value="REJECTED" />
        </el-select>

        <el-select v-model="status" placeholder="认领状态" clearable @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="未认领" value="UNCLAIMED" />
          <el-option label="已归还" value="RETURNED" />
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DDTHH:mm:ss"
          @change="handleSearch"
        />
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="foundItems"
      style="width: 100%"
      border
      stripe
      :header-cell-style="{
        background: '#f5f7fa',
        color: '#606266',
        fontWeight: '500',
        fontSize: '14px',
        height: '48px'
      }"
      :cell-style="{
        fontSize: '14px',
        padding: '8px 12px',
        color: '#606266'
      }"
      :row-style="{ height: '56px' }"
    >
      <el-table-column type="expand">
        <template #default="{ row }">
          <div class="expand-detail">
            <p><strong>详细描述：</strong>{{ row.description }}</p>
            <p><strong>发布时间：</strong>{{ formatDate(row.createdAt) }}</p>
            <p><strong>发布者ID：</strong>{{ row.userId }}</p>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="图片" width="100" align="center">
        <template #default="{ row }">
          <el-image
            :src="row.imageUrl || 'default-image.jpg'"
            fit="cover"
            class="item-image"
            :preview-src-list="row.imageUrl ? [row.imageUrl] : []"
          >
            <template #error>
              <div class="image-placeholder">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </template>
      </el-table-column>

      <el-table-column prop="itemName" label="物品名称" min-width="120" />
      <el-table-column prop="foundLocation" label="拾取地点" min-width="120" />
      <el-table-column prop="foundTime" label="拾取时间" min-width="160">
        <template #default="{ row }">
          {{ formatDate(row.foundTime) }}
        </template>
      </el-table-column>

      <el-table-column prop="username" label="发布者" min-width="120" />

      <el-table-column label="审核状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag
            :type="getAuditStatusType(row.auditStatus)"
            size="small"
            effect="light"
          >
            {{ getAuditStatusText(row.auditStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="认领状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag
            :type="row.status === 'UNCLAIMED' ? 'warning' : 'success'"
            size="small"
            effect="light"
          >
            {{ row.status === 'UNCLAIMED' ? '未认领' : '已归还' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="280" fixed="right" align="center">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              link
              @click="handleView(row)"
            >
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <template v-if="!row.auditStatus || getAuditStatusText(row.auditStatus) === '审核中'">
              <el-button
                type="success"
                link
                @click="handleAudit(row, 'APPROVED')"
              >
                <el-icon><Check /></el-icon>
                通过
              </el-button>
              <el-button
                type="danger"
                link
                @click="handleAudit(row, 'REJECTED')"
              >
                <el-icon><Close /></el-icon>
                拒绝
              </el-button>
            </template>
            <el-button
              type="success"
              link
              v-if="row.status === 'UNCLAIMED'"
              @click="handleMarkAsReturned(row)"
            >
              <el-icon><Check /></el-icon>
              标记归还
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[4, 10, 20, 50]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { Search, View, Delete, Picture, Check, Close } from '@element-plus/icons-vue'
import { foundItemAPI } from '@/api/admin'

const router = useRouter()
const loading = ref(false)
const foundItems = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(4)
const keyword = ref('')
const status = ref('')
const auditStatus = ref('')
const dateRange = ref([])

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-').replace(',', '')
}

// 获取拾物列表
const fetchFoundItems = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      keyword: keyword.value,
      status: status.value,
      auditStatus: auditStatus.value,
      startDate: dateRange.value?.[0],
      endDate: dateRange.value?.[1]
    }

    const res = await foundItemAPI.getList(params)
    console.log('后端返回的数据：', res.data.list)

    if (res.code === 200) {
      foundItems.value = res.data.list
      total.value = res.data.total
      currentPage.value = res.data.pageNum
      pageSize.value = res.data.pageSize
    }
  } catch (error) {
    console.error('获取拾物列表失败：', error)
    // 添加更详细的错误信息
    if (error.response) {
      // 服务器响应了，但状态码不是2xx
      console.error('错误状态码:', error.response.status)
      console.error('错误数据:', error.response.data)
      ElMessage.error(`获取拾物列表失败: ${error.response.data?.message || '服务器错误'}`)
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('未收到响应:', error.request)
      ElMessage.error('获取拾物列表失败: 服务器无响应')
    } else {
      // 请求设置时发生错误
      console.error('请求错误:', error.message)
      ElMessage.error(`获取拾物列表失败: ${error.message}`)
    }
  } finally {
    loading.value = false
  }
}

// 查看详情
const handleView = (row) => {
  router.push({
    path: `/found-items/detail/${row.id}`,
    query: {
      from: 'admin'  // 标记来源，用于返回时导航
    }
  })
}

// 审核
const handleAudit = async (row, status) => {
  try {
    const action = status === 'APPROVED' ? '通过' : '拒绝'
    let remarks = ''

    if (status === 'REJECTED') {
      const { value } = await ElMessageBox.prompt('请输入拒绝原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请输入拒绝原因',
        inputValidator: (value) => {
          if (!value) {
            return '请输入拒绝原因'
          }
          return true
        }
      })
      remarks = value
    } else {
      await ElMessageBox.confirm(
        `确定要${action}该拾物信息的审核吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    }

    // 显示加载中
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '审核中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      const res = await foundItemAPI.audit(row.id, {
        auditStatus: status,
        remarks: remarks
      })

      if (res.code === 200) {
        ElMessage.success(`审核${action}成功`)

        // 更新本地数据，避免重新加载整个列表
        const index = foundItems.value.findIndex(item => item.id === row.id)
        if (index !== -1) {
          foundItems.value[index].auditStatus = status
        }

        // 刷新列表
        fetchFoundItems()
      } else {
        const errorMsg = res?.message || '审核失败，请稍后重试'
        ElMessage.error(errorMsg)
      }
    } finally {
      // 关闭加载中
      loadingInstance.close()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败：', error)

      // 添加更详细的错误信息
      if (error.response) {
        // 服务器响应了，但状态码不是2xx
        console.error('错误状态码:', error.response.status)
        console.error('错误数据:', error.response.data)

        // 显示更具体的错误信息
        const errorMsg = error.response.data?.message || '审核失败，服务器错误'
        ElMessage.error(`审核失败: ${errorMsg}`)
      } else if (error.request) {
        // 请求已发送但没有收到响应
        console.error('未收到响应:', error.request)
        ElMessage.error('审核失败: 服务器无响应')
      } else {
        // 请求设置时发生错误
        console.error('请求错误:', error.message)
        ElMessage.error(`审核失败: ${error.message}`)
      }
    }
  }
}

// 标记为已归还
const handleMarkAsReturned = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要将该物品标记为已归还吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const res = await foundItemAPI.updateStatus(row.id, {
      status: 'RETURNED'
    })

    if (res.code === 200) {
      ElMessage.success('操作成功')
      fetchFoundItems()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('标记拾物状态失败：', error)
      ElMessage.error('操作失败')
    }
  }
}

// 删除拾物
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该拾物信息吗？此操作不可恢复',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const res = await foundItemAPI.delete(row.id)

    if (res.code === 200) {
      ElMessage.success('删除成功')
      fetchFoundItems()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除拾物失败：', error)
      ElMessage.error('删除失败')
    }
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchFoundItems()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchFoundItems()
}

// 处理每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchFoundItems()
}

// 获取审核状态类型
const getAuditStatusType = (status) => {
  if (!status) return 'info'

  // 确保status是字符串类型
  const upperStatus = String(status).toUpperCase()

  // 处理可能的状态值
  if (upperStatus.includes('PEND') || upperStatus === 'WAITING') {
    return 'warning'
  } else if (upperStatus.includes('APPROV') || upperStatus === 'PASS') {
    return 'success'
  } else if (upperStatus.includes('REJECT') || upperStatus === 'DENY' || upperStatus === 'FAIL') {
    return 'danger'
  } else {
    console.log('未知的审核状态:', status)
    return 'info'
  }
}

// 获取审核状态文本
const getAuditStatusText = (status) => {
  if (!status) return '未知'

  // 确保status是字符串类型
  const upperStatus = String(status).toUpperCase()

  // 处理可能的状态值
  if (upperStatus.includes('PEND') || upperStatus === 'WAITING') {
    return '审核中'
  } else if (upperStatus.includes('APPROV') || upperStatus === 'PASS') {
    return '已通过'
  } else if (upperStatus.includes('REJECT') || upperStatus === 'DENY' || upperStatus === 'FAIL') {
    return '已拒绝'
  } else {
    console.log('未知的审核状态:', status)
    return String(status) // 直接显示状态值，方便调试
  }
}

// 在组件挂载时检查是否需要设置返回按钮
onMounted(() => {
  fetchFoundItems()
  // 设置全局的返回处理
  const handleBack = (e) => {
    if (e.state?.from === 'admin-found-items') {
      router.push('/admin/found-items')
    }
  }
  window.addEventListener('popstate', handleBack)

  // 组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('popstate', handleBack)
  })
})
</script>

<style scoped>
.admin-found-items {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  width: 300px;
}

.item-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
}

.image-placeholder {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
  border-radius: 4px;
}

.expand-detail {
  padding: 16px 24px;
  background: #f5f7fa;
  border-radius: 4px;
  margin: 0 24px;
}

.expand-detail p {
  margin: 8px 0;
  line-height: 1.6;
}

.expand-detail strong {
  color: #606266;
  margin-right: 8px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

:deep(.el-table__row) {
  transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa !important;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

:deep(.el-button--text) {
  padding: 0 8px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  border-radius: 4px;
  transition: all 0.3s;
}

:deep(.el-button--text:hover) {
  background-color: var(--el-button-hover-bg-color);
  color: var(--el-button-hover-text-color);
}

:deep(.el-tag) {
  border-radius: 4px;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
}

:deep(.el-tag--light) {
  border-width: 1px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>