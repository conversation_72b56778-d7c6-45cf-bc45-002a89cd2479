/**
 * WebSocket相关的组合式函数
 * 提供WebSocket连接管理、消息发送和接收等功能
 */
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import logger from '@/utils/logger'

// 创建WebSocket日志记录器
const wsLogger = logger.createLogger('WebSocket')

/**
 * WebSocket组合式函数
 * @param {Object} options 配置选项
 * @returns {Object} WebSocket相关的状态和方法
 */
export function useWebSocket(options = {}) {
  // WebSocket状态
  const connected = ref(false)
  const connecting = ref(false)
  const reconnecting = ref(false)
  const reconnectAttempts = ref(0)
  
  // 事件监听器
  const eventListeners = ref({
    'websocket-connected': [],
    'websocket-disconnected': [],
    'websocket-reconnecting': [],
    'chat-message': [],
    'message-sent': [],
    'user-online-status': [],
    'contact-status-updated': [],
    'online-users-updated': []
  })
  
  /**
   * 初始化WebSocket
   */
  const initializeWebSocket = async () => {
    try {
      wsLogger.info('开始初始化WebSocket')
      
      // 动态导入WebSocket服务
      const { initializeWebSocketService, isWebSocketConnected } = await import('@/utils/websocket/index')
      
      // 检查WebSocket连接状态
      const wsConnected = await isWebSocketConnected()
      wsLogger.info('WebSocket连接状态:', wsConnected ? '已连接' : '未连接')
      
      if (!wsConnected) {
        wsLogger.info('WebSocket未连接，尝试初始化WebSocket服务')
        
        // 初始化WebSocket服务
        const initialized = await initializeWebSocketService()
        
        if (initialized) {
          wsLogger.info('WebSocket服务初始化成功')
          connected.value = true
          connecting.value = false
          reconnecting.value = false
          reconnectAttempts.value = 0
          return true
        } else {
          wsLogger.error('WebSocket服务初始化失败')
          connected.value = false
          connecting.value = false
          ElMessage.warning({
            message: '聊天服务连接失败，部分功能可能不可用',
            duration: 5000
          })
          return false
        }
      } else {
        wsLogger.info('WebSocket已连接，无需重新初始化')
        connected.value = true
        connecting.value = false
        return true
      }
    } catch (error) {
      wsLogger.error('初始化WebSocket失败:', error)
      connected.value = false
      connecting.value = false
      ElMessage.error('聊天服务连接失败')
      return false
    }
  }
  
  /**
   * 诊断WebSocket问题
   */
  const diagnoseWebSocketIssues = async () => {
    const issues = []
    
    try {
      // 动态导入WebSocket服务
      const { isWebSocketConnected } = await import('@/utils/websocket/index')
      
      // 检查WebSocket连接状态
      const wsConnected = await isWebSocketConnected()
      
      if (!wsConnected) {
        issues.push('WebSocket未连接')
      }
      
      wsLogger.info('WebSocket连接状态诊断:', wsConnected ? '已连接' : '未连接')
      
      return { hasIssues: issues.length > 0, issues }
    } catch (error) {
      wsLogger.error('诊断WebSocket问题失败:', error)
      issues.push(`WebSocket诊断失败: ${error.message}`)
      return { hasIssues: true, issues }
    }
  }
  
  /**
   * 发送聊天消息
   * @param {Object} message 消息对象
   * @param {File} file 文件对象（可选）
   * @returns {Promise<Object>} 发送结果
   */
  const sendChatMessage = async (message, file = null) => {
    try {
      // 确保WebSocket已连接
      if (!connected.value) {
        const initialized = await initializeWebSocket()
        if (!initialized) {
          throw new Error('WebSocket未连接，无法发送消息')
        }
      }
      
      // 动态导入WebSocket服务
      const { sendChatMessage: sendMessage } = await import('@/utils/websocket/index')
      
      // 发送消息
      return await sendMessage(message, file)
    } catch (error) {
      wsLogger.error('发送聊天消息失败:', error)
      throw error
    }
  }
  
  /**
   * 发送已读回执
   * @param {string} messageId 消息ID
   * @param {string} senderId 发送者ID
   * @param {string} receiverId 接收者ID
   * @returns {Promise<boolean>} 发送是否成功
   */
  const sendReadReceipt = async (messageId, senderId, receiverId) => {
    try {
      // 确保WebSocket已连接
      if (!connected.value) {
        const initialized = await initializeWebSocket()
        if (!initialized) {
          throw new Error('WebSocket未连接，无法发送已读回执')
        }
      }
      
      // 动态导入WebSocket服务
      const { sendReadReceipt: sendReceipt } = await import('@/utils/websocket/index')
      
      // 发送已读回执
      return await sendReceipt(messageId, senderId, receiverId)
    } catch (error) {
      wsLogger.error('发送已读回执失败:', error)
      throw error
    }
  }
  
  /**
   * 添加事件监听器
   * @param {string} eventName 事件名称
   * @param {Function} listener 监听器函数
   */
  const addEventListener = (eventName, listener) => {
    if (!eventListeners.value[eventName]) {
      eventListeners.value[eventName] = []
    }
    
    if (typeof listener === 'function' && !eventListeners.value[eventName].includes(listener)) {
      eventListeners.value[eventName].push(listener)
      window.addEventListener(eventName, listener)
    }
  }
  
  /**
   * 移除事件监听器
   * @param {string} eventName 事件名称
   * @param {Function} listener 监听器函数
   */
  const removeEventListener = (eventName, listener) => {
    if (!eventListeners.value[eventName]) return
    
    const index = eventListeners.value[eventName].indexOf(listener)
    if (index !== -1) {
      eventListeners.value[eventName].splice(index, 1)
      window.removeEventListener(eventName, listener)
    }
  }
  
  /**
   * 处理网络状态变化
   */
  const handleNetworkStatusChange = async () => {
    const isOnline = navigator.onLine
    wsLogger.info('网络状态变化:', isOnline ? '在线' : '离线')
    
    if (isOnline) {
      // 网络恢复，尝试重连WebSocket
      await initializeWebSocket()
    } else {
      // 网络断开
      connected.value = false
      ElMessage.warning('网络已断开，消息将在网络恢复后发送')
    }
  }
  
  // 组件挂载时添加事件监听
  onMounted(() => {
    window.addEventListener('online', handleNetworkStatusChange)
    window.addEventListener('offline', handleNetworkStatusChange)
    
    // 初始化WebSocket
    initializeWebSocket()
  })
  
  // 组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('online', handleNetworkStatusChange)
    window.removeEventListener('offline', handleNetworkStatusChange)
    
    // 移除所有事件监听器
    Object.keys(eventListeners.value).forEach(eventName => {
      eventListeners.value[eventName].forEach(listener => {
        window.removeEventListener(eventName, listener)
      })
    })
    
    // 清空事件监听器
    Object.keys(eventListeners.value).forEach(eventName => {
      eventListeners.value[eventName] = []
    })
  })
  
  return {
    // 状态
    connected,
    connecting,
    reconnecting,
    reconnectAttempts,
    
    // 方法
    initializeWebSocket,
    diagnoseWebSocketIssues,
    sendChatMessage,
    sendReadReceipt,
    addEventListener,
    removeEventListener,
    handleNetworkStatusChange
  }
}

export default useWebSocket
