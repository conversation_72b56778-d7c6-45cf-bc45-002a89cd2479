@echo off
echo ===================================
echo 一键启动所有服务
echo ===================================

:: 直接使用当前目录
set CONFIG_FILE=config.bat

:: 加载配置
call "%CONFIG_FILE%"

:: 设置颜色
color 0A

echo 正在启动 %SYSTEM_NAME% 所需的所有服务...

echo 1. 启动Redis服务...
call "start_redis.bat"

echo.
echo 2. 启动Kafka服务...
call "start_kafka.bat"

echo.
echo 3. 启动智能匹配服务...
call "start_clip_service.bat"

echo.
echo ===================================
echo 所有服务已启动完成！
echo ===================================

:: 不自动关闭窗口
pause
