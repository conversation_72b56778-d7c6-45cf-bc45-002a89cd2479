@startuml 失物招领系统架构图

!define RECTANGLE class
!define DATABASE database
!define CLOUD cloud
!define QUEUE queue

skinparam backgroundColor white
skinparam componentStyle uml2
skinparam defaultTextAlignment center
skinparam monochrome false
skinparam shadowing false
skinparam linetype ortho

title 校园失物招领系统架构图

' 客户端
rectangle "用户/客户端" as Client #LightBlue
rectangle "管理员/客户端" as AdminClient #LightBlue

' 前端应用
rectangle "前端应用层\n(Vue 3 + Vite)" as Frontend #PaleGreen
rectangle "管理后台\n(Vue 3 + Vite)" as AdminFrontend #PaleGreen

' API网关
rectangle "API网关/负载均衡" as Gateway #Orange

' 后端服务
rectangle "后端服务层\n(Spring Boot)" as Backend #Yellow {
    rectangle "认证授权服务\n(JWT)" as Auth #LightYellow
    rectangle "业务逻辑层\n(Service)" as Service #LightYellow
    rectangle "数据访问层\n(MyBatis)" as DAO #LightYellow
    rectangle "WebSocket服务\n(STOMP)" as WebSocket #LightYellow
}

' 中间件
database "数据库\n(MySQL)" as MySQL #Pink
database "缓存服务\n(Redis)" as Redis #Pink
queue "消息队列\n(Kafka)" as Kafka #LightPink

' 外部服务
cloud "文件存储服务\n(阿里云OSS)" as OSS #LightCyan
cloud "智能匹配服务\n(CLIP+FAISS)" as CLIP #LightCyan
cloud "GPU计算资源\n(AutoDL)" as GPU #LightCyan

' 连接关系
Client --> Frontend
AdminClient --> AdminFrontend

Frontend --> Gateway
AdminFrontend --> Gateway

Gateway --> Backend

Backend --> MySQL
Backend --> Redis
Backend --> Kafka
Backend --> OSS
Backend --> CLIP

CLIP --> GPU

' 详细连接
Auth --> Redis : 存储Token
Service --> DAO : 数据操作
DAO --> MySQL : 持久化
WebSocket --> Redis : 会话管理
WebSocket --> Kafka : 消息订阅
Service --> OSS : 文件上传
Service --> CLIP : 特征提取和匹配

@enduml
