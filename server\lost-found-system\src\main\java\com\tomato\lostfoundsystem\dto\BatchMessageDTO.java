package com.tomato.lostfoundsystem.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量消息数据传输对象
 * 用于前端批量发送离线消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchMessageDTO {

    /**
     * 批量消息列表
     */
    private List<MessageDTO> messages;

    /**
     * 设备标识符
     */
    private String deviceId;

    /**
     * 批次ID
     */
    private String batchId;

    // 确保 getMessages 方法存在
    public List<MessageDTO> getMessages() {
        return messages;
    }

    public void setMessages(List<MessageDTO> messages) {
        this.messages = messages;
    }
}
