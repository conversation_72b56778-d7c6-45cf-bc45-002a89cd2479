package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.ItemImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物品图片Mapper接口
 */
@Mapper
public interface ItemImageMapper {

    /**
     * 插入物品图片
     *
     * @param itemImage 物品图片对象
     * @return 影响的行数
     */
    int insertItemImage(ItemImage itemImage);

    /**
     * 批量插入物品图片
     *
     * @param itemImages 物品图片列表
     * @return 影响的行数
     */
    int batchInsertItemImages(@Param("itemImages") List<ItemImage> itemImages);

    /**
     * 根据物品ID和类型获取图片列表
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @return 图片列表
     */
    List<ItemImage> getImagesByItemId(@Param("itemId") Long itemId, @Param("itemType") String itemType);

    /**
     * 根据物品ID和类型删除图片
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @return 影响的行数
     */
    int deleteImagesByItemId(@Param("itemId") Long itemId, @Param("itemType") String itemType);

    /**
     * 根据ID删除图片
     *
     * @param id 图片ID
     * @return 影响的行数
     */
    int deleteImageById(@Param("id") Long id);

    /**
     * 根据ID列表批量删除图片
     *
     * @param ids 图片ID列表
     * @return 影响的行数
     */
    int deleteImagesByIds(@Param("ids") List<Long> ids);

    /**
     * 更新图片排序
     *
     * @param id 图片ID
     * @param sortOrder 排序顺序
     * @return 影响的行数
     */
    int updateImageSortOrder(@Param("id") Long id, @Param("sortOrder") int sortOrder);

    /**
     * 根据ID获取图片
     *
     * @param id 图片ID
     * @return 图片对象
     */
    ItemImage getImageById(@Param("id") Long id);
}
