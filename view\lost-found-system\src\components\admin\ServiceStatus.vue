<template>
  <div class="service-status-container">
    <!-- 服务状态卡片 -->
    <el-card class="service-card">
      <template #header>
        <div class="card-header">
          <h3>智能匹配服务状态</h3>
          <el-button type="primary" size="small" @click="refreshStatus" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <div class="service-info">
        <div class="status-indicator">
          <div class="status-dot" :class="{ 'status-running': serviceStatus.running, 'status-stopped': !serviceStatus.running }"></div>
          <span class="status-text">{{ serviceStatus.running ? '运行中' : '已停止' }}</span>
        </div>

        <div class="service-details">
          <div class="detail-item">
            <span class="label">服务地址:</span>
            <span class="value">{{ serviceStatus.url || 'N/A' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">检查时间:</span>
            <span class="value">{{ serviceStatus.checkTime || 'N/A' }}</span>
          </div>
          <div v-if="serviceStatus.info" class="detail-item">
            <span class="label">服务信息:</span>
            <span class="value">{{ serviceStatus.info }}</span>
          </div>
        </div>

        <div class="service-actions">
          <el-alert
            title="服务管理已禁用"
            type="info"
            description="服务需要通过SSH手动管理，请联系系统管理员。"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </el-card>

    <!-- 索引管理卡片 -->
    <el-card class="service-card index-management-card">
      <template #header>
        <div class="card-header">
          <h3>索引管理</h3>
        </div>
      </template>

      <div class="index-management">
        <p class="description">
          定期重建索引可以优化智能匹配性能，特别是在添加了大量新物品后。
        </p>

        <el-form :model="indexForm" label-position="top" class="index-form">
          <el-form-item label="物品类型">
            <el-select v-model="indexForm.itemType" placeholder="选择物品类型">
              <el-option label="全部" value="ALL" />
              <el-option label="失物" value="LOST" />
              <el-option label="拾物" value="FOUND" />
            </el-select>
          </el-form-item>

          <el-form-item label="索引类型">
            <el-select v-model="indexForm.indexType" placeholder="选择索引类型">
              <el-option label="全部" value="ALL" />
              <el-option label="文本" value="TEXT" />
              <el-option label="图像" value="IMAGE" />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="index-actions">
          <el-button
            type="success"
            :disabled="!serviceStatus.running"
            @click="regenerateVectors"
            :loading="actionLoading.regenerateVectors"
          >
            <el-icon><Refresh /></el-icon>
            生成特征向量并构建索引
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getClipServiceStatus,
  regenerateAllFeatureVectors
} from '@/api/service'
import { Refresh } from '@element-plus/icons-vue'

const loading = ref(false)
const serviceStatus = ref({
  running: false,
  url: '',
  checkTime: '',
  info: ''
})

const indexForm = ref({
  itemType: 'ALL',
  indexType: 'ALL'
})

const actionLoading = ref({
  regenerateVectors: false
})

// 获取服务状态
const fetchServiceStatus = async () => {
  loading.value = true
  try {
    const res = await getClipServiceStatus()
    if (res.code === 200 && res.data) {
      serviceStatus.value = res.data
    } else {
      ElMessage.error(res.message || '获取服务状态失败')
    }
  } catch (error) {
    console.error('获取服务状态出错:', error)
    ElMessage.error('获取服务状态失败')
  } finally {
    loading.value = false
  }
}

// 刷新状态
const refreshStatus = () => {
  fetchServiceStatus()
}

// 服务管理函数已移除

// 重新生成特征向量
const regenerateVectors = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要为没有特征向量的物品生成特征向量并构建索引吗？这将处理尚未生成特征向量的物品，并将其添加到索引中。此操作可能需要较长时间，取决于物品数量。',
      '生成特征向量并构建索引',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    actionLoading.value.regenerateVectors = true
    const res = await regenerateAllFeatureVectors()

    if (res.code === 200) {
      ElMessage.success(res.data || '特征向量生成成功')
    } else {
      ElMessage.error(res.message || '特征向量生成失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('生成特征向量出错:', error)
      ElMessage.error('生成特征向量失败')
    }
  } finally {
    actionLoading.value.regenerateVectors = false
  }
}

// 组件挂载时获取服务状态
onMounted(() => {
  fetchServiceStatus()
})
</script>

<style scoped>
.service-status-container {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.service-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.index-management-card {
  margin-top: 0;
}

.description {
  color: #606266;
  margin-bottom: 20px;
  line-height: 1.5;
}

.index-form {
  margin-bottom: 20px;
  display: flex;
  gap: 20px;
}

.index-form .el-form-item {
  flex: 1;
  margin-bottom: 0;
}

.index-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.service-info {
  padding: 10px 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-running {
  background-color: #67c23a;
  box-shadow: 0 0 8px #67c23a;
}

.status-stopped {
  background-color: #f56c6c;
  box-shadow: 0 0 8px #f56c6c;
}

.status-text {
  font-weight: 600;
  font-size: 16px;
}

.service-details {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.detail-item {
  margin-bottom: 10px;
  display: flex;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  color: #606266;
  width: 100px;
}

.value {
  color: #303133;
  word-break: break-all;
}

.service-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .service-actions {
    flex-direction: column;
    gap: 10px;
  }

  .detail-item {
    flex-direction: column;
  }

  .label {
    width: 100%;
    margin-bottom: 5px;
  }

  .index-form {
    flex-direction: column;
    gap: 15px;
  }

  .index-actions {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
