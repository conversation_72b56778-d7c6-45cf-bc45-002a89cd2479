package com.tomato.lostfoundsystem.entity;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 匹配历史实体类
 * 记录用户的匹配查询历史
 */
@Data
public class MatchHistory {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 查询类型（IMAGE/TEXT/MIXED/IMAGE_MULTIMODAL）
     */
    private String queryType;

    /**
     * 查询图片URL
     */
    private String queryImageUrl;

    /**
     * 查询文本
     */
    private String queryText;

    /**
     * 查询物品类型（LOST/FOUND）
     * LOST表示查找拾物，FOUND表示查找失物
     */
    private String itemType;

    /**
     * 结果数量
     */
    private Integer resultCount;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 匹配结果列表（非数据库字段）
     */
    private List<MatchResult> results;
}
