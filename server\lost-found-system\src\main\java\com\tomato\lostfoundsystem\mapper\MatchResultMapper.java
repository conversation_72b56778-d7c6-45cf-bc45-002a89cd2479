package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.MatchResult;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 匹配结果Mapper接口
 */
@Mapper
public interface MatchResultMapper {

    /**
     * 批量插入匹配结果
     *
     * @param matchResults 匹配结果列表
     * @return 影响的行数
     */
    @Insert("<script>" +
            "INSERT INTO match_results(match_history_id, item_id, item_type, similarity_score) VALUES " +
            "<foreach collection='list' item='result' separator=','>" +
            "(#{result.matchHistoryId}, #{result.itemId}, #{result.itemType}, #{result.similarityScore})" +
            "</foreach>" +
            "</script>")
    int batchInsertMatchResults(@Param("list") List<MatchResult> matchResults);

    /**
     * 根据匹配历史ID查询匹配结果
     *
     * @param matchHistoryId 匹配历史ID
     * @return 匹配结果列表
     */
    @Select("SELECT * FROM match_results WHERE match_history_id = #{matchHistoryId} ORDER BY similarity_score DESC")
    List<MatchResult> getMatchResultsByHistoryId(@Param("matchHistoryId") Long matchHistoryId);

    /**
     * 删除匹配结果
     *
     * @param matchHistoryId 匹配历史ID
     * @return 影响的行数
     */
    @Delete("DELETE FROM match_results WHERE match_history_id = #{matchHistoryId}")
    int deleteMatchResults(@Param("matchHistoryId") Long matchHistoryId);
}
