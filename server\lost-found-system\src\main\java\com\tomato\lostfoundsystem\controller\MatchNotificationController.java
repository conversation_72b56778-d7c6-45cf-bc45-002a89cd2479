package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.MatchNotification;
import com.tomato.lostfoundsystem.service.MatchNotificationService;
import com.tomato.lostfoundsystem.utils.JWTUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 匹配通知控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/notifications/match")
public class MatchNotificationController {

    @Autowired
    private MatchNotificationService matchNotificationService;

    /**
     * 获取用户的匹配通知列表
     *
     * @param request HTTP请求
     * @return 通知列表
     */
    @GetMapping("/list")
    public Result<List<MatchNotification>> getUserNotifications(HttpServletRequest request) {
        try {
            Long userId = JWTUtil.getUserId(request);
            if (userId == null) {
                return Result.fail("用户未登录");
            }

            return matchNotificationService.getUserNotifications(userId);
        } catch (Exception e) {
            log.error("获取用户匹配通知列表失败", e);
            return Result.fail("获取通知列表失败: " + e.getMessage());
        }
    }

    /**
     * 标记通知为已读
     *
     * @param notificationId 通知ID
     * @param request HTTP请求
     * @return 处理结果
     */
    @PutMapping("/{notificationId}/read")
    public Result markNotificationAsRead(@PathVariable Long notificationId, HttpServletRequest request) {
        try {
            Long userId = JWTUtil.getUserId(request);
            if (userId == null) {
                return Result.fail("用户未登录");
            }

            return matchNotificationService.markNotificationAsRead(notificationId, userId);
        } catch (Exception e) {
            log.error("标记通知为已读失败", e);
            return Result.fail("标记已读失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户未读通知数量
     *
     * @param request HTTP请求
     * @return 未读通知数量
     */
    @GetMapping("/unread-count")
    public Result<Integer> getUnreadNotificationCount(HttpServletRequest request) {
        try {
            Long userId = JWTUtil.getUserId(request);
            if (userId == null) {
                return Result.fail("用户未登录");
            }

            return matchNotificationService.getUnreadNotificationCount(userId);
        } catch (Exception e) {
            log.error("获取用户未读通知数量失败", e);
            return Result.fail("获取未读通知数量失败: " + e.getMessage());
        }
    }
}
