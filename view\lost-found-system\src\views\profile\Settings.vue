<template>
  <div class="settings">
    <h2>账号设置</h2>

    <el-divider />

    <div class="settings-section">
      <h3>密码管理</h3>
      <el-button type="primary" @click="showChangePasswordDialog">修改密码</el-button>
    </div>

    <el-divider />

    <div class="settings-section danger-zone">
      <h3>危险操作</h3>
      <p class="warning-text">以下操作不可逆，请谨慎操作</p>

      <el-button type="danger" @click="showDeactivateDialog">注销账号</el-button>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog
      title="修改密码"
      v-model="changePasswordDialogVisible"
      width="400px"
    >
      <el-form :model="passwordForm" label-width="100px" :rules="passwordRules" ref="passwordFormRef">
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input v-model="passwordForm.currentPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="changePasswordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="changePassword" :loading="changePasswordLoading">
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 注销账号对话框 -->
    <el-dialog
      title="注销账号"
      v-model="deactivateDialogVisible"
      width="400px"
    >
      <div class="deactivate-warning">
        <el-alert
          title="警告：此操作不可逆"
          type="error"
          description="注销账号后，您的个人信息将被匿名化，无法再登录此账号。您发布的物品信息和聊天记录将保留，但不再与您的个人信息关联。"
          show-icon
          :closable="false"
        />
      </div>

      <el-form :model="deactivateForm" label-width="100px" :rules="deactivateRules" ref="deactivateFormRef">
        <el-form-item label="账号密码" prop="password">
          <el-input v-model="deactivateForm.password" type="password" show-password placeholder="请输入您的密码以确认" />
        </el-form-item>
        <el-form-item label="确认操作" prop="confirmation">
          <el-input v-model="deactivateForm.confirmation" placeholder="请输入 CONFIRM 以确认注销" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deactivateDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="deactivateAccount" :loading="deactivateLoading">
            确认注销
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores'
import { deactivateAccount as apiDeactivateAccount } from '@/api/user'

const router = useRouter()
const userStore = useUserStore()

// 修改密码相关
const changePasswordDialogVisible = ref(false)
const changePasswordLoading = ref(false)
const passwordFormRef = ref(null)
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const showChangePasswordDialog = () => {
  changePasswordDialogVisible.value = true
  passwordForm.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
}

const changePassword = async () => {
  // 这里暂时不实现修改密码功能，因为后端API还未提供
  ElMessage.info('修改密码功能暂未实现')
  changePasswordDialogVisible.value = false
}

// 注销账号相关
const deactivateDialogVisible = ref(false)
const deactivateLoading = ref(false)
const deactivateFormRef = ref(null)
const deactivateForm = ref({
  password: '',
  confirmation: ''
})

const deactivateRules = {
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  confirmation: [
    { required: true, message: '请输入确认文字', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== 'CONFIRM') {
          callback(new Error('请输入 CONFIRM 以确认注销'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const showDeactivateDialog = () => {
  deactivateDialogVisible.value = true
  deactivateForm.value = {
    password: '',
    confirmation: ''
  }
}

const deactivateAccount = async () => {
  try {
    await deactivateFormRef.value.validate()

    // 再次确认
    await ElMessageBox.confirm(
      '您确定要注销账号吗？此操作不可逆！',
      '最终确认',
      {
        confirmButtonText: '确认注销',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    deactivateLoading.value = true

    // 调用API注销账号
    const res = await apiDeactivateAccount(deactivateForm.value)

    if (res.code === 200) {
      ElMessage.success(res.message || '账号已成功注销')

      // 清除用户状态
      userStore.clearUser()
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')

      // 关闭对话框
      deactivateDialogVisible.value = false

      // 跳转到首页
      router.push('/')
    } else {
      ElMessage.error(res.message || '注销账号失败')
    }
  } catch (error) {
    if (error === 'cancel') {
      // 用户取消操作
      return
    }
    console.error('注销账号失败:', error)
    ElMessage.error('注销账号失败: ' + (error.message || '未知错误'))
  } finally {
    deactivateLoading.value = false
  }
}
</script>

<style scoped>
.settings {
  padding: 30px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-width: 800px;
  margin: 0 auto;
}

.settings-section {
  margin: 20px 0;
}

.danger-zone {
  padding: 20px;
  background-color: #fff5f5;
  border-radius: 8px;
  border: 1px solid #ffccc7;
}

.warning-text {
  color: #f56c6c;
  margin-bottom: 20px;
}

.deactivate-warning {
  margin-bottom: 20px;
}
</style>