/**
 * 聊天工具函数
 * 提供消息格式化、日期处理等通用功能
 */

/**
 * 格式化日期
 * @param {string|Date} time 时间
 * @returns {string} 格式化后的日期
 */
export function formatDate(time) {
  if (!time) return ''
  
  const date = new Date(time)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  // 如果是今天
  if (date.toDateString() === today.toDateString()) {
    return '今天'
  }
  
  // 如果是昨天
  if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  }
  
  // 其他日期
  return date.toLocaleDateString([], {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

/**
 * 格式化时间
 * @param {string|Date} time 时间
 * @returns {string} 格式化后的时间
 */
export function formatTime(time) {
  if (!time) return ''
  
  const date = new Date(time)
  return date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 格式化文件大小
 * @param {number} size 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(size) {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  }
}

/**
 * 格式化消息
 * @param {Object} message 原始消息
 * @param {Object} userInfo 当前用户信息
 * @param {Object} currentContact 当前联系人
 * @returns {Object} 格式化后的消息
 */
export function formatMessage(message, userInfo, currentContact) {
  try {
    if (!message) {
      return {
        id: `temp-${Date.now()}`,
        content: '[无效消息]',
        time: new Date().toISOString(),
        isSelf: false,
        messageType: 'TEXT'
      }
    }
    
    // 处理时间戳
    let formattedTime = message.time || message.timestamp || new Date().toISOString()
    
    // 检查时间是否为字符串格式，例如: "Mon Apr 28 22:51:54 CST 2025"
    if (typeof formattedTime === 'string' && formattedTime.includes('CST')) {
      try {
        // 解析非标准时间格式
        const date = new Date(formattedTime)
        if (!isNaN(date.getTime())) {
          formattedTime = date.toISOString()
        }
      } catch (timeError) {
        console.warn('时间戳格式解析失败:', timeError)
        formattedTime = new Date().toISOString()
      }
    }
    
    // 确保消息有所有必要的属性
    return {
      ...message,
      id: message.id || message.messageId || `temp-${Date.now()}`,
      content: message.content || message.message || '[空消息]',
      senderId: message.senderId || message.sender || userInfo?.id,
      receiverId: message.receiverId || message.receiver || currentContact?.id,
      messageType: message.messageType || 'TEXT',
      time: formattedTime,
      isSelf: message.isSelf !== undefined ? message.isSelf :
              (String(message.senderId) === String(userInfo?.id) || 
               String(message.sender) === String(userInfo?.id))
    }
  } catch (error) {
    console.error('格式化消息时出错:', error)
    // 返回安全的默认消息
    return {
      id: `error-${Date.now()}`,
      content: '[消息格式错误]',
      time: new Date().toISOString(),
      isSelf: false,
      messageType: 'TEXT'
    }
  }
}

/**
 * 检测消息是否重复
 * @param {Object} newMsg 新消息
 * @param {Array} existingMsgs 现有消息列表
 * @returns {boolean} 是否重复
 */
export function isMessageDuplicate(newMsg, existingMsgs) {
  if (!newMsg || !existingMsgs || !Array.isArray(existingMsgs)) {
    return false
  }
  
  return existingMsgs.some(m => {
    // 1. 精确匹配ID (非临时ID)
    if (m.id === newMsg.id && !m.id.toString().startsWith('temp-') && !m.id.toString().startsWith('error-')) {
      console.log('消息重复检测: ID精确匹配', m.id)
      return true
    }
    
    // 2. 临时ID匹配（处理发送中的消息）
    if (m.id.toString().startsWith('temp-') && newMsg.id.toString().startsWith('temp-') &&
        m.content === newMsg.content &&
        String(m.senderId) === String(newMsg.senderId) &&
        Math.abs(new Date(m.time) - new Date(newMsg.time)) < 2000) {
      console.log('消息重复检测: 临时ID匹配', m.id, newMsg.id)
      return true
    }
    
    // 3. 内容+发送者+接收者+时间匹配（处理没有ID或ID不同的消息）
    if (m.content === newMsg.content &&
        String(m.senderId) === String(newMsg.senderId) &&
        String(m.receiverId) === String(newMsg.receiverId) &&
        Math.abs(new Date(m.time) - new Date(newMsg.time)) < 5000) {
      console.log('消息重复检测: 内容+发送者+接收者+时间匹配')
      return true
    }
    
    // 4. 处理WebSocket重连导致的重复消息
    if (m.content === newMsg.content &&
        String(m.senderId) === String(newMsg.senderId) &&
        Math.abs(new Date(m.time) - new Date(newMsg.time)) < 30000) { // 30秒内的相同内容和发送者
      console.log('消息重复检测: WebSocket重连导致的重复消息')
      return true
    }
    
    return false
  })
}

/**
 * 生成头像颜色
 * @param {string} name 用户名
 * @returns {string} 颜色值
 */
export function generateAvatarColor(name) {
  if (!name) return '#1890ff'
  
  // 使用用户名的第一个字符的ASCII码生成颜色
  const charCode = name.charCodeAt(0)
  const colors = [
    '#f56a00', '#7265e6', '#ffbf00', '#00a2ae',
    '#1890ff', '#52c41a', '#722ed1', '#eb2f96',
    '#faad14', '#13c2c2', '#fa541c', '#a0d911'
  ]
  
  return colors[charCode % colors.length]
}

/**
 * 获取用户名首字符
 * @param {string} name 用户名
 * @returns {string} 首字符
 */
export function getNameInitial(name) {
  if (!name) return '?'
  return name.charAt(0).toUpperCase()
}

/**
 * 播放通知声音
 */
export function playNotificationSound() {
  try {
    const audio = new Audio('/sounds/notification.mp3')
    audio.volume = 0.5
    audio.play()
  } catch (error) {
    console.error('播放通知声音失败:', error)
  }
}

export default {
  formatDate,
  formatTime,
  formatFileSize,
  formatMessage,
  isMessageDuplicate,
  generateAvatarColor,
  getNameInitial,
  playNotificationSound
}
