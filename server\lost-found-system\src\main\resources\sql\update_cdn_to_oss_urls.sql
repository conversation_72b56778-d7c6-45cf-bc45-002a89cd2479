-- 更新CDN URL为OSS URL的SQL脚本
-- 此脚本将数据库中的CDN URL (https://cdn.laofanqi.top/...) 替换为OSS URL (https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/...)

-- 设置变量
SET @cdn_domain = 'https://cdn.laofanqi.top';
SET @oss_domain = 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com';

-- 1. 更新lost_items表中的image_url字段
UPDATE lost_items
SET image_url = REPLACE(image_url, @cdn_domain, @oss_domain)
WHERE image_url LIKE CONCAT(@cdn_domain, '%');

-- 2. 更新found_items表中的image_url字段
UPDATE found_items
SET image_url = REPLACE(image_url, @cdn_domain, @oss_domain)
WHERE image_url LIKE CONCAT(@cdn_domain, '%');

-- 3. 更新item_images表中的image_url字段
UPDATE item_images
SET image_url = REPLACE(image_url, @cdn_domain, @oss_domain)
WHERE image_url LIKE CONCAT(@cdn_domain, '%');

-- 4. 更新message_attachments表中的file_url字段
UPDATE message_attachments
SET file_url = REPLACE(file_url, @cdn_domain, @oss_domain)
WHERE file_url LIKE CONCAT(@cdn_domain, '%');

-- 5. 更新users表中的avatar_url字段
UPDATE users
SET avatar_url = REPLACE(avatar_url, @cdn_domain, @oss_domain)
WHERE avatar_url LIKE CONCAT(@cdn_domain, '%');

-- 6. 更新match_history表中的query_image_url字段
UPDATE match_history
SET query_image_url = REPLACE(query_image_url, @cdn_domain, @oss_domain)
WHERE query_image_url LIKE CONCAT(@cdn_domain, '%');

-- 7. 更新系统配置表中的CDN域名配置
UPDATE system_config
SET config_value = ''
WHERE config_key = 'aliyun.cdn.domain';

-- 8. 禁用CDN
UPDATE system_config
SET config_value = 'false'
WHERE config_key = 'aliyun.cdn.enabled';

-- 9. 更新CDN最后更新时间
UPDATE system_config
SET config_value = NOW()
WHERE config_key = 'aliyun.cdn.last_updated';

-- 输出更新结果
SELECT 'URL更新完成，已将CDN URL替换为OSS URL' AS '操作结果';
