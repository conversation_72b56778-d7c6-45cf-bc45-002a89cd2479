package com.tomato.lostfoundsystem.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 匹配通知实体类
 * 用于存储高相似度匹配的通知信息
 */
@Data
public class MatchNotification {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 接收通知的用户ID
     */
    private Long userId;

    /**
     * 匹配历史ID
     */
    private Long matchHistoryId;

    /**
     * 匹配物品ID
     */
    private Long itemId;

    /**
     * 匹配物品类型（LOST/FOUND）
     */
    private String itemType;

    /**
     * 匹配相似度
     */
    private Float similarity;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 是否已读
     */
    private Boolean isRead;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 元数据（JSON格式）
     */
    private String metadata;

    /**
     * 匹配类型
     */
    private String matchType;

    /**
     * 文本-文本相似度
     */
    private Float textToTextSimilarity;

    /**
     * 文本-图像相似度
     */
    private Float textToImageSimilarity;

    /**
     * 图像-文本相似度
     */
    private Float imageToTextSimilarity;

    /**
     * 图像-图像相似度
     */
    private Float imageToImageSimilarity;

    /**
     * 元数据Map（非数据库字段）
     */
    @JsonIgnore
    private transient Map<String, Object> metadataMap;

    /**
     * 获取解析后的元数据
     * @return 元数据Map
     */
    public Map<String, Object> getMetadataMap() {
        if (metadata == null || metadata.isEmpty()) {
            return new HashMap<>();
        }

        if (metadataMap != null) {
            return metadataMap;
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            metadataMap = mapper.readValue(metadata, new TypeReference<Map<String, Object>>() {});
            return metadataMap;
        } catch (JsonProcessingException e) {
            return new HashMap<>();
        }
    }
}
