package com.tomato.lostfoundsystem.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.tomato.lostfoundsystem.enums.FileType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import jakarta.annotation.PostConstruct;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
@Component
public class AliyunOSSUtil {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.oss.access-key-secret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucket-name}")
    private String bucketName;

    @Value("${aliyun.cdn.domain:#{null}}")
    private String cdnDomain;

    @jakarta.annotation.Resource
    private com.tomato.lostfoundsystem.service.CDNConfigService cdnConfigService;

    private static String staticEndpoint;
    private static String staticAccessKeyId;
    private static String staticAccessKeySecret;
    private static String staticBucketName;
    private static String staticCdnDomain;
    private static com.tomato.lostfoundsystem.service.CDNConfigService staticCdnConfigService;

    @PostConstruct
    public void init() {
        staticEndpoint = this.endpoint;
        staticAccessKeyId = this.accessKeyId;
        staticAccessKeySecret = this.accessKeySecret;
        staticBucketName = this.bucketName;
        staticCdnDomain = this.cdnDomain;
        staticCdnConfigService = this.cdnConfigService;
    }

    /**
     * 上传文件到阿里云 OSS，根据文件类型选择存储目录
     * @param file 上传的文件
     * @param fileType 文件类型（如 IMAGE, AUDIO, VIDEO）
     * @return 文件的 URL
     */
    public static String uploadFile(MultipartFile file, FileType fileType) {
        return uploadFile(file, fileType, null);
    }

    /**
     * 上传文件到阿里云 OSS，使用自定义目录
     * @param file 上传的文件
     * @param customDirectory 自定义目录
     * @return 文件的 URL
     */
    public static String uploadFile(MultipartFile file, String customDirectory) {
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(staticEndpoint, staticAccessKeyId, staticAccessKeySecret);

            // 生成文件名，避免文件名冲突
            String fileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();

            // 如果指定了目录路径，添加到文件名中
            if (customDirectory != null && !customDirectory.isEmpty()) {
                fileName = customDirectory + "/" + fileName;
            }

            // 上传文件到 OSS
            PutObjectRequest putObjectRequest = new PutObjectRequest(staticBucketName, fileName, file.getInputStream());
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(file.getContentType());  // 设置文件的 MIME 类型
            putObjectRequest.setMetadata(metadata);
            ossClient.putObject(putObjectRequest);

            log.info("文件上传成功: {}", fileName);

            // 返回文件的访问 URL
            return getFileUrl(fileName);
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            return null;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 上传文件到阿里云 OSS，根据文件类型和业务类型选择存储目录
     * @param file 上传的文件
     * @param fileType 文件类型（如 IMAGE, AUDIO, VIDEO）
     * @param businessType 业务类型（如 lost, found 等）
     * @return 文件的 URL
     */
    public static String uploadFile(MultipartFile file, FileType fileType, String businessType) {
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(staticEndpoint, staticAccessKeyId, staticAccessKeySecret);

            // 生成文件名，避免文件名冲突
            String fileName = System.currentTimeMillis() + "_" + file.getOriginalFilename();

            // 根据文件类型和业务类型设置存储的目录路径
            String directory = getDirectoryByFileType(fileType, businessType);

            // 如果指定了目录路径，添加到文件名中
            if (directory != null && !directory.isEmpty()) {
                fileName = directory + "/" + fileName;
            }

            // 上传文件到 OSS
            PutObjectRequest putObjectRequest = new PutObjectRequest(staticBucketName, fileName, file.getInputStream());
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(file.getContentType());  // 设置文件的 MIME 类型
            putObjectRequest.setMetadata(metadata);
            ossClient.putObject(putObjectRequest);

            log.info("文件上传成功: {}", fileName);

            // 返回文件的访问 URL
            return getFileUrl(fileName);
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            return null;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 根据文件类型返回相应的存储目录
     * @param fileType 文件类型
     * @return 对应的存储目录路径
     */
    private static String getDirectoryByFileType(FileType fileType) {
        return getDirectoryByFileType(fileType, null);
    }

    /**
     * 根据文件类型和业务类型返回相应的存储目录
     * @param fileType 文件类型
     * @param businessType 业务类型（如 lost, found 等）
     * @return 对应的存储目录路径
     */
    private static String getDirectoryByFileType(FileType fileType, String businessType) {
        // 获取当前日期作为子目录
        String dateDir = new SimpleDateFormat("yyyy/MM/dd").format(new Date());

        String typeDir;
        switch (fileType) {
            case IMAGE:
                typeDir = "images";  // 图片文件上传到 images 目录
                break;
            case AUDIO:
                typeDir = "audios";  // 音频文件上传到 audios 目录
                break;
            case VIDEO:
                typeDir = "videos";  // 视频文件上传到 videos 目录
                break;
            case DOCUMENT:
                typeDir = "documents";  // 文档文件上传到 documents 目录
                break;
            default:
                typeDir = "others";  // 默认上传到 others 目录
                break;
        }

        // 如果提供了业务类型，则添加到路径中
        if (businessType != null && !businessType.isEmpty()) {
            return typeDir + "/" + businessType.toLowerCase() + "/" + dateDir;
        }

        return typeDir + "/" + dateDir;
    }

    // 上传图片文件
    public static String uploadImage(MultipartFile file) {
        return uploadFile(file, FileType.IMAGE);  // 上传到 images 目录
    }

    // 上传失物图片文件
    public static String uploadLostImage(MultipartFile file) {
        return uploadFile(file, FileType.IMAGE, "lost");  // 上传到 images/lost 目录
    }

    // 上传拾物图片文件
    public static String uploadFoundImage(MultipartFile file) {
        return uploadFile(file, FileType.IMAGE, "found");  // 上传到 images/found 目录
    }

    // 上传视频文件
    public static String uploadVideo(MultipartFile file) {
        return uploadFile(file, FileType.VIDEO);  // 上传到 videos 目录
    }

    // 上传音频文件
    public static String uploadAudio(MultipartFile file) {
        return uploadFile(file, FileType.AUDIO);  // 上传到 audios 目录
    }

    // 上传文档文件
    public static String uploadDocument(MultipartFile file) {
        return uploadFile(file, FileType.DOCUMENT);  // 上传到 documents 目录
    }

    /**
     * 从阿里云OSS删除文件
     * @param objectName 对象名称（文件路径）
     * @return 是否删除成功
     */
    public static boolean deleteObject(String objectName) {
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(staticEndpoint, staticAccessKeyId, staticAccessKeySecret);
            ossClient.deleteObject(staticBucketName, objectName);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 获取文件的URL
     * @param objectName 对象名称（文件路径）
     * @return 文件的URL
     */
    public static String getFileUrl(String objectName) {
        // 直接使用OSS域名，不再使用CDN
        return "https://" + staticBucketName + "." + staticEndpoint + "/" + objectName;
    }

    /**
     * 从URL中提取OSS对象名
     * @param url 完整的OSS URL
     * @return 对象名
     */
    public static String extractObjectNameFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }

        // 优先从CDN配置服务获取域名
        String cdnDomain = null;
        try {
            if (staticCdnConfigService != null) {
                cdnDomain = staticCdnConfigService.getCDNDomain();
            }
        } catch (Exception e) {
            log.warn("从CDN配置服务获取域名失败，将使用静态配置: {}", e.getMessage());
        }

        // 如果CDN配置服务不可用或未返回域名，则使用静态配置
        if (cdnDomain == null || cdnDomain.isEmpty()) {
            cdnDomain = staticCdnDomain;
        }

        // 如果配置了CDN域名，先检查是否是CDN URL
        if (cdnDomain != null && !cdnDomain.isEmpty()) {
            // 标准化CDN域名（移除尾部斜杠）
            String normalizedCdnDomain = cdnDomain.endsWith("/")
                ? cdnDomain.substring(0, cdnDomain.length() - 1)
                : cdnDomain;

            if (url.startsWith(normalizedCdnDomain)) {
                // 提取对象键（确保以斜杠开头）
                String path = url.substring(normalizedCdnDomain.length());
                return path.startsWith("/") ? path.substring(1) : path;
            }
        }

        // URL格式: https://bucket-name.endpoint/object-name
        String prefix = "https://" + staticBucketName + "." + staticEndpoint + "/";
        if (url.startsWith(prefix)) {
            return url.substring(prefix.length());
        }
        return null;
    }
}
