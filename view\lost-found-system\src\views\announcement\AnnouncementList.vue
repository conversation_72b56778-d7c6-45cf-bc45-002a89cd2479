<template>
  <div class="announcement-list-container">
    <div class="page-header">
      <h2>系统公告</h2>
    </div>

    <el-card class="announcement-list-card">
      <template #header>
        <div class="card-header">
          <span>公告列表</span>
          <div class="header-actions">
            <el-button
              type="primary"
              plain
              @click="markAllAsRead"
              :disabled="!hasUnread || loading"
              size="small"
            >
              <el-icon><Check /></el-icon>
              全部标记为已读
            </el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading">
        <el-empty v-if="announcements.length === 0" description="暂无系统公告" />

        <el-timeline v-else>
          <el-timeline-item
            v-for="item in announcements"
            :key="item.id"
            :type="getTimelineItemType(item)"
            :color="getTimelineItemColor(item)"
            :timestamp="formatDate(item.createdAt)"
            placement="top"
          >
            <el-card
              class="announcement-card"
              :class="{ unread: !item.isRead }"
              @click="viewDetail(item)"
            >
              <div class="announcement-card-header">
                <h3 class="announcement-title">
                  {{ item.title }}
                  <el-tag v-if="!item.isRead" size="small" type="danger">未读</el-tag>
                </h3>
                <el-tag :type="getImportanceType(item)" size="small">
                  {{ getImportanceText(item.importance) }}
                </el-tag>
              </div>
              <div class="announcement-preview">
                {{ getContentPreview(item.content) }}
              </div>
              <div class="announcement-footer">
                <span>有效期: {{ formatDate(item.startTime) }} 至 {{ item.endTime ? formatDate(item.endTime) : '长期有效' }}</span>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>

    <!-- 公告详情弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="currentAnnouncement?.title || '系统公告'"
      width="600px"
      destroy-on-close
    >
      <div class="announcement-detail">
        <div class="announcement-meta">
          <el-tag :type="getImportanceType(currentAnnouncement)" size="small">
            {{ getImportanceText(currentAnnouncement?.importance) }}
          </el-tag>
          <span class="announcement-time">
            发布时间: {{ formatDate(currentAnnouncement?.createdAt) }}
          </span>
        </div>
        <div class="announcement-content" v-html="formattedContent"></div>
        <div class="announcement-validity">
          <span>有效期: {{ formatDate(currentAnnouncement?.startTime) }} 至 {{ currentAnnouncement?.endTime ? formatDate(currentAnnouncement?.endTime) : '长期有效' }}</span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="markAsRead" v-if="currentAnnouncement && !currentAnnouncement.isRead">
            标记为已读
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Check } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getValidAnnouncements, markAnnouncementAsRead, getAnnouncementDetail } from '@/api/announcement'

const announcements = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const currentAnnouncement = ref(null)

// 获取公告列表
const fetchAnnouncements = async () => {
  loading.value = true
  try {
    const res = await getValidAnnouncements()
    if (res.code === 200) {
      // 处理公告列表，确保isRead是布尔类型
      announcements.value = (res.data || []).map(announcement => {
        // 打印调试信息
        console.log(`【公告列表】处理公告ID: ${announcement.id}, 原始isRead:`,
                    announcement.isRead, `(类型: ${typeof announcement.isRead})`);

        // 严格处理isRead属性
        let isRead = false;
        if (announcement.isRead === 1) {
          isRead = true;
        }

        console.log(`【公告列表】处理公告ID: ${announcement.id}, 处理后isRead:`,
                    isRead, `(类型: ${typeof isRead})`);

        return {
          ...announcement,
          isRead: isRead  // 使用严格转换后的布尔值
        };
      });

      console.log('【公告列表】加载完成，总数:', announcements.value.length,
                 '已读:', announcements.value.filter(a => a.isRead).length,
                 '未读:', announcements.value.filter(a => !a.isRead).length);
    }
  } catch (error) {
    console.error('获取系统公告失败:', error)
    ElMessage.error('获取系统公告失败')
  } finally {
    loading.value = false
  }
}

// 查看公告详情
const viewDetail = async (announcement) => {
  try {
    const res = await getAnnouncementDetail(announcement.id)
    if (res.code === 200) {
      currentAnnouncement.value = res.data
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取公告详情失败:', error)
    ElMessage.error('获取公告详情失败')
  }
}

// 标记为已读
const markAsRead = async () => {
  if (!currentAnnouncement.value) return

  try {
    console.log('【公告列表】开始标记公告已读，ID:', currentAnnouncement.value.id)
    const res = await markAnnouncementAsRead(currentAnnouncement.value.id)
    console.log('【公告列表】标记公告已读响应:', res)

    if (res.code === 200) {
      ElMessage.success('已标记为已读')
      // 强制设置为布尔类型true
      currentAnnouncement.value.isRead = true
      console.log('【公告列表】更新当前公告状态为已读, 类型:', typeof currentAnnouncement.value.isRead)

      // 更新列表中的已读状态
      const index = announcements.value.findIndex(item => item.id === currentAnnouncement.value.id)
      if (index !== -1) {
        console.log('【公告列表】更新列表中公告状态为已读')
        // 使用对象替换确保触发响应式更新
        const updatedAnnouncement = {
          ...announcements.value[index],
          isRead: true
        }
        announcements.value.splice(index, 1, updatedAnnouncement)

        // 检查更新后的状态
        console.log('【公告列表】更新后isRead值:', announcements.value[index].isRead,
                    '类型:', typeof announcements.value[index].isRead)
      }

      // 强制DOM更新
      announcements.value = [...announcements.value]
    } else {
      console.error('【公告列表】标记已读失败，响应码:', res.code)
      ElMessage.error(res.message || '标记已读失败')
    }
  } catch (error) {
    console.error('【公告列表】标记已读失败:', error)
    ElMessage.error('标记已读失败，请稍后重试')
  }
}

// 全部标记为已读
const markAllAsRead = async () => {
  try {
    ElMessageBox.confirm('确定要将所有公告标记为已读吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      // 过滤真实未读状态的公告
      const unreadAnnouncements = announcements.value.filter(item => {
        // 打印调试信息
        console.log(`【公告列表】检查公告ID: ${item.id}, isRead:`, item.isRead, `(类型: ${typeof item.isRead})`);
        return item.isRead !== true;
      });

      console.log('【公告列表】未读公告数量:', unreadAnnouncements.length);

      if (unreadAnnouncements.length === 0) {
        ElMessage.info('没有未读公告');
        return;
      }

      loading.value = true;
      const updatedAnnouncements = [...announcements.value];

      // 逐个标记为已读
      for (const announcement of unreadAnnouncements) {
        try {
          console.log(`【公告列表】开始标记公告 ${announcement.id} 为已读`);
          const res = await markAnnouncementAsRead(announcement.id);
          console.log(`【公告列表】标记公告 ${announcement.id} 已读响应:`, res);

          if (res.code === 200) {
          // 更新列表中的已读状态
            const index = updatedAnnouncements.findIndex(item => item.id === announcement.id);
          if (index !== -1) {
              console.log(`【公告列表】更新公告 ${announcement.id} 在列表中的状态为已读`);
              // 使用新对象替换以确保响应式更新
              updatedAnnouncements[index] = {
                ...updatedAnnouncements[index],
                isRead: true
              };
            }
          } else {
            console.error(`【公告列表】标记公告 ${announcement.id} 已读API返回错误:`, res);
          }

          // 添加短暂延迟，避免请求过快
          await new Promise(resolve => setTimeout(resolve, 300));
        } catch (error) {
          console.error(`【公告列表】标记公告 ${announcement.id} 已读失败:`, error);
        }
      }

      // 更新整个数组以确保响应式更新
      announcements.value = updatedAnnouncements;

      // 检查更新后的状态
      const stillUnread = announcements.value.filter(item => !item.isRead);
      console.log('【公告列表】操作后仍未读的公告数量:', stillUnread.length);

      ElMessage.success('已全部标记为已读');
      loading.value = false;
    }).catch(() => {
      // 用户取消操作
    });
  } catch (error) {
    console.error('【公告列表】标记全部已读失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString()
}

// 获取内容预览
const getContentPreview = (content) => {
  if (!content) return ''
  return content.length > 100 ? content.substring(0, 100) + '...' : content
}

// 根据重要程度获取时间线项类型
const getTimelineItemType = (announcement) => {
  switch (announcement.importance) {
    case 'URGENT':
      return 'danger'
    case 'IMPORTANT':
      return 'warning'
    default:
      return 'primary'
  }
}

// 根据重要程度获取时间线项颜色
const getTimelineItemColor = (announcement) => {
  switch (announcement.importance) {
    case 'URGENT':
      return '#f56c6c'
    case 'IMPORTANT':
      return '#e6a23c'
    default:
      return '#409eff'
  }
}

// 根据重要程度获取标签类型
const getImportanceType = (announcement) => {
  if (!announcement) return ''
  switch (announcement.importance) {
    case 'URGENT':
      return 'danger'
    case 'IMPORTANT':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取重要程度文本
const getImportanceText = (importance) => {
  switch (importance) {
    case 'URGENT':
      return '紧急'
    case 'IMPORTANT':
      return '重要'
    default:
      return '普通'
  }
}

// 格式化内容（将换行符转换为<br>）
const formattedContent = computed(() => {
  if (!currentAnnouncement.value) return ''
  return currentAnnouncement.value.content.replace(/\n/g, '<br>')
})

// 是否有未读公告
const hasUnread = computed(() => {
  return announcements.value.some(item => !item.isRead)
})

onMounted(() => {
  fetchAnnouncements()
})
</script>

<style scoped>
.announcement-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.announcement-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 10px;
}

.announcement-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.announcement-card.unread {
  border-left: 3px solid #f56c6c;
}

.announcement-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.announcement-title {
  margin: 0;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.announcement-title .el-tag {
  margin-left: 10px;
}

.announcement-preview {
  color: #606266;
  margin-bottom: 10px;
  line-height: 1.5;
}

.announcement-footer {
  font-size: 12px;
  color: #909399;
}

.announcement-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.announcement-time {
  font-size: 12px;
  color: #909399;
}

.announcement-content {
  line-height: 1.6;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 100px;
  margin-bottom: 15px;
}

.announcement-validity {
  font-size: 12px;
  color: #909399;
  text-align: right;
}
</style>
