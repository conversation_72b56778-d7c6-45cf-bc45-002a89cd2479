package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.service.KafkaProducerService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.model.kafka.ChatMessageEnvelope;
import com.tomato.lostfoundsystem.model.kafka.ItemApprovedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Slf4j  // 自动生成日志功能
@Service
public class KafkaProducerServiceImpl implements KafkaProducerService {

    private KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    @Value("${spring.kafka.enabled:true}")
    private boolean kafkaEnabled;

    // 定义多个主题
    private static final String CHAT_TOPIC = "chat-topic";  // 聊天消息主题
    private static final String NOTIFICATION_TOPIC = "notification-topic";  // 通知消息主题
    private static final String SYSTEM_TOPIC = "system-topic";  // 系统消息主题
    private static final String RETRY_TOPIC = "retry-topic";  // 重试消息主题
    private static final String DLQ_TOPIC = "dead-letter-queue";  // 死信队列主题
    private static final String ITEM_APPROVED_TOPIC = "item-approved-topic";  // 物品审核通过主题
    private static final String READ_RECEIPT_TOPIC = "chat-read-receipts";  // 已读回执主题

    // 构造函数注入 ObjectMapper
    public KafkaProducerServiceImpl() {
        this.objectMapper = new ObjectMapper();
    }

    // 可选注入 KafkaTemplate
    @Autowired(required = false)
    public void setKafkaTemplate(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    @Override
    public void storeOfflineMessage(String message) {
        sendToTopic(CHAT_TOPIC, message);
    }

    // 添加新方法发送通知消息
    @Override
    public void sendNotification(String message) {
        sendToTopic(NOTIFICATION_TOPIC, message);
    }

    // 添加新方法发送系统消息
    @Override
    public void sendSystemMessage(String message) {
        sendToTopic(SYSTEM_TOPIC, message);
    }

    // 添加新方法发送重试消息
    @Override
    public void sendRetryMessage(String message) {
        sendToTopic(RETRY_TOPIC, message);
    }

    // 添加新方法发送到死信队列
    @Override
    public void sendToDeadLetterQueue(String message) {
        sendToTopic(DLQ_TOPIC, message);
    }

    // 通用发送方法
    private void sendToTopic(String topic, String message) {
        if (!kafkaEnabled || kafkaTemplate == null) {
            log.warn("Kafka 未启用或 KafkaTemplate 未配置，跳过消息发送: {}", topic);
            return;
        }

        try {
            kafkaTemplate.send(topic, message);
            log.info("消息已发送到Kafka主题: {}", topic);
        } catch (Exception e) {
            log.error("发送消息到Kafka主题失败: {}, 错误: {}", topic, e.getMessage(), e);
        }
    }

    // 添加带键的发送方法
    @Override
    public void sendWithKey(String topic, String key, String message) {
        if (!kafkaEnabled || kafkaTemplate == null) {
            log.warn("Kafka 未启用或 KafkaTemplate 未配置，跳过带键消息发送: {}, 键: {}", topic, key);
            return;
        }

        try {
            kafkaTemplate.send(topic, key, message);
            log.info("带键的消息已发送到Kafka主题: {}, 键: {}", topic, key);
        } catch (Exception e) {
            log.error("发送带键的消息到Kafka主题失败: {}, 键: {}, 错误: {}",
                     topic, key, e.getMessage(), e);
        }
    }

    /**
     * 发送物品审核通过事件
     *
     * @param event 物品审核通过事件
     * @return 是否发送成功
     */
    @Override
    public boolean sendItemApprovedEvent(ItemApprovedEvent event) {
        try {
            if (!kafkaEnabled || kafkaTemplate == null) {
                log.error("【审核通过】Kafka 未启用或 KafkaTemplate 未配置，无法发送物品审核通过事件: 物品ID={}, 类型={}",
                        event.getItemId(), event.getItemType());
                return false;
            }

            // 将事件对象转换为JSON字符串
            String jsonMessage = objectMapper.writeValueAsString(event);

            // 使用物品类型和ID作为消息键，确保相同物品的消息按顺序处理
            String key = event.getItemType() + "_" + event.getItemId();

            // 发送消息到物品审核通过主题
            kafkaTemplate.send(ITEM_APPROVED_TOPIC, key, jsonMessage);

            log.info("【审核通过】物品审核通过事件已发送到Kafka, 物品ID: {}, 类型: {}, 用户ID: {}",
                    event.getItemId(), event.getItemType(), event.getUserId());

            return true;
        } catch (Exception e) {
            log.error("【审核通过】发送物品审核通过事件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送聊天消息到Kafka
     *
     * @param messageDTO 聊天消息DTO
     * @return 是否发送成功
     */
    @Override
    public boolean sendChatMessage(MessageDTO messageDTO) {
        try {
            if (!kafkaEnabled || kafkaTemplate == null) {
                log.error("【聊天消息】Kafka 未启用或 KafkaTemplate 未配置，无法发送聊天消息");
                return false;
            }

            log.info("【聊天消息】开始封装消息准备发送: messageId={}, clientMessageId={}, senderId={}, receiverId={}",
                    messageDTO.getId(),
                    messageDTO.getClientMessageId(),
                    messageDTO.getSenderId(),
                    messageDTO.getReceiverId());

            // 创建消息信封
            ChatMessageEnvelope envelope = ChatMessageEnvelope.create(messageDTO);

            log.info("【聊天消息】消息已封装为信封格式: envelopeId={}, messageId={}, clientMessageId={}",
                    envelope.getMessageId(),
                    messageDTO.getId(),
                    messageDTO.getClientMessageId());

            // 发送消息信封
            return sendChatMessageEnvelope(envelope);
        } catch (Exception e) {
            log.error("【聊天消息】发送聊天消息失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送聊天消息信封到Kafka
     *
     * @param envelope 聊天消息信封
     * @return 是否发送成功
     */
    @Override
    public boolean sendChatMessageEnvelope(ChatMessageEnvelope envelope) {
        try {
            if (!kafkaEnabled || kafkaTemplate == null) {
                log.error("【聊天消息】Kafka 未启用或 KafkaTemplate 未配置，无法发送聊天消息信封");
                return false;
            }

            // 将消息信封转换为JSON字符串
            String jsonMessage = objectMapper.writeValueAsString(envelope);

            // 使用消息ID作为消息键，确保相同消息的幂等性
            String key = envelope.getMessageId();

            // 更新消息状态为已入队
            envelope.setStatus(ChatMessageEnvelope.MessageStatus.QUEUED);

            // 发送消息到聊天主题
            log.info("【聊天消息】准备发送消息到Kafka, 主题: {}, 消息键: {}, 消息长度: {} 字节",
                    CHAT_TOPIC, key, jsonMessage.length());

            kafkaTemplate.send(CHAT_TOPIC, key, jsonMessage);

            log.info("【聊天消息】消息已发送到Kafka, 消息ID: {}, 发送者: {}, 接收者: {}, 客户端消息ID: {}",
                    envelope.getMessageId(),
                    envelope.getPayload().getSenderId(),
                    envelope.getPayload().getReceiverId(),
                    envelope.getPayload().getClientMessageId());

            return true;
        } catch (Exception e) {
            log.error("【聊天消息】发送聊天消息信封失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送已读回执到Kafka
     *
     * @param readReceipt 已读回执DTO
     * @return 是否发送成功
     */
    @Override
    public boolean sendReadReceipt(com.tomato.lostfoundsystem.dto.ReadReceiptDTO readReceipt) {
        try {
            if (!kafkaEnabled || kafkaTemplate == null) {
                log.error("【已读回执】Kafka 未启用或 KafkaTemplate 未配置，无法发送已读回执");
                return false;
            }

            // 将已读回执转换为JSON字符串
            String jsonMessage = objectMapper.writeValueAsString(readReceipt);

            // 使用读者ID和发送者ID作为消息键，确保相同用户的已读回执按顺序处理
            String key = readReceipt.getReaderId() + "_" + readReceipt.getSenderId();

            // 发送消息到已读回执主题
            log.info("【已读回执】准备发送已读回执到Kafka, 主题: {}, 消息键: {}, 消息长度: {} 字节",
                    READ_RECEIPT_TOPIC, key, jsonMessage.length());

            kafkaTemplate.send(READ_RECEIPT_TOPIC, key, jsonMessage);

            log.info("【已读回执】已读回执已发送到Kafka, 读者ID: {}, 发送者ID: {}, 消息数量: {}",
                    readReceipt.getReaderId(),
                    readReceipt.getSenderId(),
                    readReceipt.getMessageIds().size());

            return true;
        } catch (Exception e) {
            log.error("【已读回执】发送已读回执失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
