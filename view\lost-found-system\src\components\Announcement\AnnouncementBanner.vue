<template>
  <div v-if="announcements.length > 0" class="announcement-banner">
    <el-carousel
      height="50px"
      direction="vertical"
      :autoplay="true"
      :interval="5000"
      indicator-position="none"
      class="announcement-carousel"
    >
      <el-carousel-item v-for="item in announcements" :key="item.id" @click="viewDetail(item)">
        <div class="announcement-item" :class="getImportanceClass(item)">
          <el-icon><Bell /></el-icon>
          <span class="announcement-title">{{ item.title }}</span>
          <span v-if="!item.isRead" class="unread-badge"></span>
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- 公告详情弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="currentAnnouncement?.title || '系统公告'"
      width="600px"
      destroy-on-close
    >
      <div class="announcement-detail">
        <div class="announcement-meta">
          <el-tag :type="getImportanceType(currentAnnouncement)" size="small">
            {{ getImportanceText(currentAnnouncement?.importance) }}
          </el-tag>
          <span class="announcement-time">
            发布时间: {{ formatDate(currentAnnouncement?.createdAt) }}
          </span>
        </div>
        <div class="announcement-content" v-html="formattedContent"></div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="markAsRead" v-if="currentAnnouncement && !currentAnnouncement.isRead">
            标记为已读
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Bell } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getValidAnnouncements, markAnnouncementAsRead, getAnnouncementDetail } from '@/api/announcement'

const announcements = ref([])
const dialogVisible = ref(false)
const currentAnnouncement = ref(null)

// 获取公告列表
const fetchAnnouncements = async () => {
  try {
    const res = await getValidAnnouncements()
    if (res.code === 200) {
      // 处理公告列表，确保isRead是布尔类型
      announcements.value = (res.data || []).map(announcement => {
        // 打印调试信息
        console.log(`【公告横幅】处理公告ID: ${announcement.id}, 原始isRead:`,
                    announcement.isRead, `(类型: ${typeof announcement.isRead})`);

        // 严格处理isRead属性
        let isRead = false;
        if (announcement.isRead === 1) {
          isRead = true;
        }

        console.log(`【公告横幅】处理公告ID: ${announcement.id}, 处理后isRead:`,
                    isRead, `(类型: ${typeof isRead})`);

        return {
          ...announcement,
          isRead: isRead  // 使用严格转换后的布尔值
        };
      });

      console.log('【公告横幅】加载完成，总数:', announcements.value.length,
                 '已读:', announcements.value.filter(a => a.isRead).length,
                 '未读:', announcements.value.filter(a => !a.isRead).length);
    }
  } catch (error) {
    console.error('获取系统公告失败:', error)
  }
}

// 查看公告详情
const viewDetail = async (announcement) => {
  try {
    console.log('【公告横幅】查看公告详情，ID:', announcement.id);
    const res = await getAnnouncementDetail(announcement.id)
    console.log('【公告横幅】获取公告详情响应:', res);

    if (res.code === 200) {
      // 处理公告详情，确保isRead是布尔类型
      const announcementData = res.data;

      // 打印调试信息
      console.log(`【公告横幅】处理公告详情，原始isRead:`,
                  announcementData.isRead,
                  `(类型: ${typeof announcementData.isRead})`);

      // 严格处理isRead属性
      let isRead = false;
      if (announcementData.isRead === 1) {
        isRead = true;
      }

      // 更新处理后的数据
      currentAnnouncement.value = {
        ...announcementData,
        isRead: isRead  // 使用严格转换后的布尔值
      };

      console.log(`【公告横幅】处理后公告详情，isRead:`,
                  currentAnnouncement.value.isRead,
                  `(类型: ${typeof currentAnnouncement.value.isRead})`);

      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取公告详情失败:', error)
    ElMessage.error('获取公告详情失败')
  }
}

// 标记为已读
const markAsRead = async () => {
  if (!currentAnnouncement.value) return

  try {
    console.log('【公告横幅】开始标记公告已读，ID:', currentAnnouncement.value.id);
    const res = await markAnnouncementAsRead(currentAnnouncement.value.id)
    console.log('【公告横幅】标记公告已读响应:', res);

    if (res.code === 200) {
      ElMessage.success('已标记为已读')

      // 确保使用布尔值true
      console.log('【公告横幅】更新当前公告状态为已读，原状态:',
                  currentAnnouncement.value.isRead,
                  `(类型: ${typeof currentAnnouncement.value.isRead})`);

      currentAnnouncement.value.isRead = true;

      console.log('【公告横幅】更新后状态:',
                  currentAnnouncement.value.isRead,
                  `(类型: ${typeof currentAnnouncement.value.isRead})`);

      // 更新列表中的已读状态
      const index = announcements.value.findIndex(item => item.id === currentAnnouncement.value.id)
      if (index !== -1) {
        console.log('【公告横幅】更新列表中公告状态为已读，原状态:',
                    announcements.value[index].isRead,
                    `(类型: ${typeof announcements.value[index].isRead})`);

        // 使用新对象替换以确保响应式更新
        announcements.value[index] = {
          ...announcements.value[index],
          isRead: true
        };

        console.log('【公告横幅】更新后状态:',
                    announcements.value[index].isRead,
                    `(类型: ${typeof announcements.value[index].isRead})`);
      }

      // 刷新公告列表，确保状态一致
      setTimeout(() => {
        fetchAnnouncements();
      }, 500);
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('标记已读失败')
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString()
}

// 根据重要程度获取样式类
const getImportanceClass = (announcement) => {
  switch (announcement.importance) {
    case 'URGENT':
      return 'urgent'
    case 'IMPORTANT':
      return 'important'
    default:
      return 'normal'
  }
}

// 根据重要程度获取标签类型
const getImportanceType = (announcement) => {
  if (!announcement) return ''
  switch (announcement.importance) {
    case 'URGENT':
      return 'danger'
    case 'IMPORTANT':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取重要程度文本
const getImportanceText = (importance) => {
  switch (importance) {
    case 'URGENT':
      return '紧急'
    case 'IMPORTANT':
      return '重要'
    default:
      return '普通'
  }
}

// 格式化内容（将换行符转换为<br>）
const formattedContent = computed(() => {
  if (!currentAnnouncement.value) return ''
  return currentAnnouncement.value.content.replace(/\n/g, '<br>')
})

onMounted(() => {
  fetchAnnouncements()
})
</script>

<style scoped>
.announcement-banner {
  width: 100%;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
  overflow: hidden;
}

.announcement-carousel {
  width: 100%;
}

.announcement-item {
  display: flex;
  align-items: center;
  padding: 0 15px;
  height: 100%;
  cursor: pointer;
  transition: background-color 0.3s;
}

.announcement-item:hover {
  background-color: #ecf5ff;
}

.announcement-item .el-icon {
  margin-right: 10px;
  font-size: 18px;
}

.announcement-title {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.unread-badge {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #f56c6c;
  margin-left: 10px;
}

.announcement-item.urgent {
  color: #f56c6c;
  background-color: #fef0f0;
}

.announcement-item.important {
  color: #e6a23c;
  background-color: #fdf6ec;
}

.announcement-item.normal {
  color: #409eff;
  background-color: #ecf5ff;
}

.announcement-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.announcement-time {
  font-size: 12px;
  color: #909399;
}

.announcement-content {
  line-height: 1.6;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 100px;
}
</style>
