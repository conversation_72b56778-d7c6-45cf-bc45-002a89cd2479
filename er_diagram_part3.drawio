<mxfile host="app.diagrams.net" modified="2023-06-15T12:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64)" etag="your-etag" version="14.7.7" type="device">
  <diagram id="lost-found-er-diagram" name="失物招领系统ER图">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 关系：用户-发布-失物信息 -->
        <mxCell id="publish_lost" value="发布" style="shape=rhombus;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;align=center;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="300" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 关系：用户-发布-招领信息 -->
        <mxCell id="publish_found" value="发布" style="shape=rhombus;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;align=center;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="400" y="300" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 关系：失物信息-匹配-招领信息 -->
        <mxCell id="match" value="匹配" style="shape=rhombus;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;align=center;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="395" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 关系：失物信息-包含-物品图片 -->
        <mxCell id="lost_has_image" value="包含" style="shape=rhombus;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;align=center;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="170" y="500" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 关系：招领信息-包含-物品图片 -->
        <mxCell id="found_has_image" value="包含" style="shape=rhombus;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;align=center;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="350" y="500" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 关系：失物信息-生成-特征向量 -->
        <mxCell id="lost_has_vector" value="生成" style="shape=rhombus;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;align=center;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="170" y="600" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 关系：招领信息-生成-特征向量 -->
        <mxCell id="found_has_vector" value="生成" style="shape=rhombus;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;align=center;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="350" y="600" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 关系：用户-参与-聊天会话 -->
        <mxCell id="user_in_chat" value="参与" style="shape=rhombus;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;align=center;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="350" y="195" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 关系：聊天会话-包含-聊天消息 -->
        <mxCell id="session_has_message" value="包含" style="shape=rhombus;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;align=center;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="500" y="250" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 关系：用户-发布-系统公告 -->
        <mxCell id="user_publish_announcement" value="发布" style="shape=rhombus;perimeter=rhombusPerimeter;whiteSpace=wrap;html=1;align=center;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="350" y="150" width="100" height="50" as="geometry" />
        </mxCell>
        
        <!-- 实体：用户 -->
        <mxCell id="user" value="用户" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="200" y="200" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：失物信息 -->
        <mxCell id="lost_item" value="失物信息" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="400" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：招领信息 -->
        <mxCell id="found_item" value="招领信息" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="400" y="400" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：物品图片 -->
        <mxCell id="item_image" value="物品图片" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="550" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：特征向量 -->
        <mxCell id="feature_vector" value="特征向量" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="650" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：匹配记录 -->
        <mxCell id="match_record" value="匹配记录" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="450" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：聊天会话 -->
        <mxCell id="chat_session" value="聊天会话" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="500" y="200" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：聊天消息 -->
        <mxCell id="chat_message" value="聊天消息" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="500" y="300" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：系统公告 -->
        <mxCell id="system_announcement" value="系统公告" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="350" y="100" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 连接线：用户-发布-失物信息 -->
        <mxCell id="user_to_publish_lost" value="" style="endArrow=none;html=1;rounded=0;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user" target="publish_lost">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="publish_lost_to_lost" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="publish_lost" target="lost_item">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 连接线：用户-发布-招领信息 -->
        <mxCell id="user_to_publish_found" value="" style="endArrow=none;html=1;rounded=0;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="user" target="publish_found">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="publish_found_to_found" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="publish_found" target="found_item">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 连接线：失物信息-匹配-招领信息 -->
        <mxCell id="lost_to_match" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="lost_item" target="match">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="match_to_found" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="match" target="found_item">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 连接线：失物信息-包含-物品图片 -->
        <mxCell id="lost_to_has_image" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="lost_item" target="lost_has_image">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170" y="525"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="lost_has_image_to_image" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="lost_has_image" target="item_image">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 连接线：招领信息-包含-物品图片 -->
        <mxCell id="found_to_has_image" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="found_item" target="found_has_image">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="450" y="525"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="found_has_image_to_image" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="found_has_image" target="item_image">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 连接线：失物信息-生成-特征向量 -->
        <mxCell id="lost_to_has_vector" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="lost_item" target="lost_has_vector">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="170" y="625"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="lost_has_vector_to_vector" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="lost_has_vector" target="feature_vector">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 连接线：招领信息-生成-特征向量 -->
        <mxCell id="found_to_has_vector" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="found_item" target="found_has_vector">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="450" y="625"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="found_has_vector_to_vector" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="found_has_vector" target="feature_vector">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 连接线：用户-参与-聊天会话 -->
        <mxCell id="user_to_in_chat" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user" target="user_in_chat">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="in_chat_to_chat" value="" style="endArrow=none;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_in_chat" target="chat_session">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 连接线：聊天会话-包含-聊天消息 -->
        <mxCell id="session_to_has_message" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="chat_session" target="session_has_message">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="has_message_to_message" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="session_has_message" target="chat_message">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 连接线：用户-发布-系统公告 -->
        <mxCell id="user_to_publish_announcement" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="user" target="user_publish_announcement">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="250" y="175"/>
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="publish_announcement_to_announcement" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="user_publish_announcement" target="system_announcement">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 基数标记 -->
        <mxCell id="card_user_publish_lost" value="1" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="180" y="250" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="card_publish_lost_lost" value="n" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="150" y="370" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="card_user_publish_found" value="1" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="290" y="250" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="card_publish_found_found" value="n" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="430" y="370" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="card_lost_match" value="m" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="220" y="395" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="card_match_found" value="n" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="370" y="395" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="card_lost_has_image" value="1" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="150" y="500" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="card_has_image_image" value="n" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="230" y="550" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="card_found_has_image" value="1" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="450" y="500" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="card_has_image_image2" value="n" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="360" y="550" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="card_user_in_chat" value="m" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="300" y="195" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="card_in_chat_chat" value="n" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="470" y="195" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="card_session_has_message" value="1" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="550" y="240" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="card_has_message_message" value="n" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="550" y="270" width="30" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
