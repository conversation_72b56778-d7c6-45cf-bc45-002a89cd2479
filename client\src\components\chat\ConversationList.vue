<template>
  <div class="conversation-list">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <span>加载中...</span>
    </div>
    
    <div v-else-if="error" class="error-container">
      <span class="error-message">{{ error }}</span>
      <button @click="fetchConversations" class="retry-button">重试</button>
    </div>
    
    <template v-else>
      <div v-if="sortedConversations.length === 0" class="empty-container">
        <span>暂无会话</span>
      </div>
      
      <div v-else class="conversation-items">
        <div 
          v-for="conversation in sortedConversations" 
          :key="conversation.contactId"
          class="conversation-item"
          :class="{ 
            'pinned': conversation.isPinned, 
            'muted': conversation.isMuted,
            'active': selectedContactId === conversation.contactId 
          }"
          @click="selectConversation(conversation)"
        >
          <!-- 头像 -->
          <div class="avatar-container">
            <img 
              v-if="conversation.avatar" 
              :src="conversation.avatar" 
              :alt="conversation.name" 
              class="avatar"
            />
            <div v-else class="avatar-text">
              {{ conversation.avatarText || conversation.name.charAt(0).toUpperCase() }}
            </div>
            
            <!-- 在线状态 -->
            <div v-if="onlineUsers.includes(conversation.contactId)" class="online-status">
              <span class="online-text">在线</span>
            </div>
          </div>
          
          <!-- 会话信息 -->
          <div class="conversation-info">
            <div class="conversation-header">
              <span class="contact-name">{{ conversation.name }}</span>
              <span class="last-time">{{ formatTime(conversation.lastTime) }}</span>
            </div>
            
            <div class="conversation-content">
              <!-- 根据消息类型显示不同的内容 -->
              <span v-if="conversation.messageType === 'TEXT'" class="last-message">
                {{ conversation.lastMessage }}
              </span>
              <span v-else-if="conversation.messageType === 'IMAGE'" class="last-message">
                [图片]
              </span>
              <span v-else-if="conversation.messageType === 'AUDIO'" class="last-message">
                [语音 {{ formatDuration(conversation.audioDuration) }}]
              </span>
              <span v-else-if="conversation.messageType === 'VIDEO'" class="last-message">
                [视频 {{ formatDuration(conversation.videoDuration) }}]
              </span>
              <span v-else-if="conversation.messageType === 'FILE'" class="last-message">
                [文件]
              </span>
              
              <!-- 未读消息计数 -->
              <div v-if="conversation.unreadCount > 0" class="unread-count">
                {{ conversation.unreadCount > 99 ? '99+' : conversation.unreadCount }}
              </div>
            </div>
          </div>
          
          <!-- 会话操作 -->
          <div class="conversation-actions" @click.stop>
            <el-dropdown trigger="click" @command="handleCommand($event, conversation)">
              <span class="el-dropdown-link">
                <i class="el-icon-more"></i>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="conversation.isPinned ? 'unpin' : 'pin'">
                    {{ conversation.isPinned ? '取消置顶' : '置顶' }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="conversation.isMuted ? 'unmute' : 'mute'">
                    {{ conversation.isMuted ? '取消静音' : '静音' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="archive">归档</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { computed, onMounted, watch } from 'vue';
import { useConversationStore } from '@/stores/conversationStore';
import { useUserStore } from '@/stores/userStore';
import { useWebSocketStore } from '@/stores/webSocketStore';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  selectedContactId: {
    type: Number,
    default: null
  }
});

const emit = defineEmits(['select-conversation']);

const conversationStore = useConversationStore();
const userStore = useUserStore();
const webSocketStore = useWebSocketStore();

// 计算属性
const loading = computed(() => conversationStore.loading);
const error = computed(() => conversationStore.error);
const sortedConversations = computed(() => conversationStore.sortedConversations);
const onlineUsers = computed(() => webSocketStore.onlineUsers);

// 方法
function fetchConversations() {
  conversationStore.fetchConversations();
}

function selectConversation(conversation) {
  emit('select-conversation', conversation);
  
  // 如果有未读消息，重置未读计数
  if (conversation.unreadCount > 0) {
    conversationStore.resetUnreadCount(userStore.user.id, conversation.contactId);
  }
}

function formatTime(timestamp) {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now - date;
  
  // 今天的消息只显示时间
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  }
  
  // 昨天的消息显示"昨天"
  if (diff < 48 * 60 * 60 * 1000 && date.getDate() === now.getDate() - 1) {
    return '昨天';
  }
  
  // 一周内的消息显示星期几
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return weekdays[date.getDay()];
  }
  
  // 其他消息显示日期
  return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
}

function formatDuration(seconds) {
  if (!seconds) return '';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  if (minutes > 0) {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${remainingSeconds}秒`;
  }
}

async function handleCommand(command, conversation) {
  switch (command) {
    case 'pin':
      await conversationStore.updateConversationPinned(conversation.conversationId, true);
      ElMessage.success('会话已置顶');
      break;
    case 'unpin':
      await conversationStore.updateConversationPinned(conversation.conversationId, false);
      ElMessage.success('已取消置顶');
      break;
    case 'mute':
      await conversationStore.updateConversationMuted(conversation.conversationId, true);
      ElMessage.success('会话已静音');
      break;
    case 'unmute':
      await conversationStore.updateConversationMuted(conversation.conversationId, false);
      ElMessage.success('已取消静音');
      break;
    case 'archive':
      await conversationStore.updateConversationStatus(conversation.conversationId, 'ARCHIVED');
      ElMessage.success('会话已归档');
      break;
    case 'delete':
      ElMessageBox.confirm('确定要删除此会话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await conversationStore.updateConversationStatus(conversation.conversationId, 'DELETED');
        ElMessage.success('会话已删除');
      }).catch(() => {});
      break;
  }
}

// 生命周期钩子
onMounted(() => {
  fetchConversations();
});

// 监听 WebSocket 连接状态
watch(() => webSocketStore.connected, (connected) => {
  if (connected) {
    fetchConversations();
  }
});
</script>

<style scoped>
.conversation-list {
  height: 100%;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  margin-top: 10px;
  padding: 5px 15px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.conversation-items {
  padding: 0;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.conversation-item:hover {
  background-color: #f0f0f0;
}

.conversation-item.active {
  background-color: #e6f7ff;
}

.conversation-item.pinned {
  background-color: #f9f9f9;
}

.avatar-container {
  position: relative;
  margin-right: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-text {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
}

.online-status {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: #2ecc71;
  border-radius: 10px;
  padding: 2px 4px;
  font-size: 10px;
}

.online-text {
  color: white;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.contact-name {
  font-weight: bold;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.last-time {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}

.conversation-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-message {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.unread-count {
  min-width: 18px;
  height: 18px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 9px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
}

.conversation-actions {
  margin-left: 10px;
  opacity: 0;
  transition: opacity 0.2s;
}

.conversation-item:hover .conversation-actions {
  opacity: 1;
}

.conversation-item.muted .last-message {
  color: #999;
}
</style>
