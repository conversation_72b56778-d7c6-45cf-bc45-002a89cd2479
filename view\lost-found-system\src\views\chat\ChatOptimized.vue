<template>
  <div class="chat-container">
    <!-- 左侧联系人列表 -->
    <div class="chat-sidebar">
      <contact-list
        :contacts="contacts"
        :current-contact="currentContact"
        :loading="contactsLoading"
        @select="handleSelectContact"
        @refresh="loadContacts"
      />
    </div>

    <!-- 右侧聊天区域 -->
    <div class="chat-main">
      <template v-if="currentContact">
        <!-- 聊天头部 -->
        <chat-header
          :contact="currentContact"
          :loading="loading"
          @refresh="handleRefreshChat"
          @diagnose="runWebSocketDiagnostics"
        />

        <!-- 聊天消息列表 -->
        <chat-message-list
          ref="messageListRef"
          :messages="messages"
          :user="userInfo"
          :contact="currentContact"
          :loading="loading"
          :loading-more="loadingMore"
          :no-more-messages="noMoreMessages"
          @load-more="loadMoreMessages"
          @retry="handleResendMessage"
          @preview-image="handlePreviewImage"
        />

        <!-- 聊天输入框 -->
        <chat-input
          v-model="inputMessage"
          :sending="sending"
          :disabled="!currentContact"
          @send="sendTextMessage"
          @image-select="handleImageSelect"
          @file-select="handleFileSelect"
        />
      </template>

      <div v-else class="chat-placeholder">
        <el-empty description="选择一个联系人开始聊天" />
      </div>
    </div>

    <!-- 图片上传对话框 -->
    <file-upload-dialog
      v-model:visible="imageUploadVisible"
      :file="selectedImage"
      file-type="IMAGE"
      :preview-url="uploadPreview.url"
      :uploading="sending"
      @cancel="clearUploadPreview"
      @confirm="handleSendImage"
    />

    <!-- 文件上传对话框 -->
    <file-upload-dialog
      v-model:visible="fileUploadVisible"
      :file="selectedFile"
      file-type="DOCUMENT"
      :uploading="sending"
      @cancel="clearUploadPreview"
      @confirm="handleSendFile"
    />

    <!-- 网络状态提示 -->
    <el-alert
      v-if="!networkStatus.connected"
      title="网络已断开，消息将在网络恢复后发送"
      type="warning"
      :closable="false"
      show-icon
      class="network-alert"
    />

    <!-- 升级通知 -->
    <el-alert
      v-if="showUpgradeNotice"
      title="聊天界面已升级，体验更流畅的聊天功能"
      type="success"
      :closable="true"
      show-icon
      class="upgrade-alert"
      @close="hideUpgradeNotice"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useUserStore } from '@/stores'
import { ElMessage, ElMessageBox } from 'element-plus'
import logger from '@/utils/logger'

// 导入组件
import ContactList from '@/components/chat/ContactList.vue'
import ChatHeader from '@/components/chat/ChatHeader.vue'
import ChatMessageList from '@/components/chat/ChatMessageList.vue'
import ChatInput from '@/components/chat/ChatInput.vue'
import FileUploadDialog from '@/components/chat/FileUploadDialog.vue'

// 导入组合式函数
import useChat from '@/composables/useChat'
import useContacts from '@/composables/useContacts'
import useFileUpload from '@/composables/useFileUpload'
import useWebSocket from '@/composables/useWebSocket'

// 导入工具函数
import { isMessageDuplicate, playNotificationSound } from '@/utils/chat-utils'

// 创建聊天日志记录器
const chatLogger = logger.createLogger('Chat')

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo || {})

// 使用组合式函数
const {
  messages, currentContact, sending, loading, loadingMore, noMoreMessages,
  inputMessage, messagesContainer, networkStatus, sendTextMessage,
  updateMessageStatus, scrollToBottom, loadChatHistory, formatMessage
} = useChat()

const {
  contacts, contactsLoading, loadContacts, selectContact, openContactFromUrl,
  updateContactLastMessage, isContactOnline
} = useContacts()

const {
  uploadPreview, selectedFile, selectedImage, fileUploadVisible, imageUploadVisible,
  handleFileSelect: handleFileSelectBase, handleImageSelect: handleImageSelectBase,
  clearUploadPreview
} = useFileUpload()

const {
  connected: wsConnected, initializeWebSocket, diagnoseWebSocketIssues,
  sendChatMessage, sendReadReceipt, addEventListener, removeEventListener
} = useWebSocket()

// 引用
const messageListRef = ref(null)

// 离线消息计数
const offlineMessageCount = ref(0)

// 升级通知
const showUpgradeNotice = ref(!localStorage.getItem('chatUpgradeNoticeDismissed'))

// 隐藏升级通知
const hideUpgradeNotice = () => {
  showUpgradeNotice.value = false
  // 保存到本地存储，避免重复显示
  localStorage.setItem('chatUpgradeNoticeDismissed', 'true')
}

// 初始化WebSocket
const initChat = async () => {
  chatLogger.info('开始初始化聊天组件')
  chatLogger.info('当前用户信息:', userInfo.value)

  try {
    // 初始化WebSocket
    await initializeWebSocket()

    // 加载联系人列表
    await loadContacts()

    // 从URL参数获取要打开的联系人ID
    openContactFromUrl()

    // 处理离线消息
    if (offlineMessageCount.value > 0) {
      chatLogger.info(`发现 ${offlineMessageCount.value} 条离线消息，尝试发送`)
      await processOfflineMessages()
    }

    chatLogger.info('聊天组件初始化完成')
  } catch (error) {
    chatLogger.error('初始化聊天组件失败:', error)
    ElMessage.error('聊天组件初始化失败，请刷新页面重试')
  }
}

// 加载更多消息
const loadMoreMessages = async () => {
  if (loadingMore.value || !currentContact.value) return

  try {
    loadingMore.value = true

    // 获取当前最早的消息ID作为分页标记
    const earliestMessage = messages.value[0]
    if (!earliestMessage) return

    // 加载更早的消息
    const response = await fetch(`/api/chat/history/${currentContact.value.id}?before=${earliestMessage.id}`)
    const result = await response.json()

    if (result.code === 200 && Array.isArray(result.data)) {
      // 如果没有更多消息
      if (result.data.length === 0) {
        noMoreMessages.value = true
        return
      }

      // 格式化消息
      const formattedMessages = result.data.map(msg => formatMessage(msg, userInfo.value, currentContact.value))

      // 添加到消息列表前面
      messages.value.unshift(...formattedMessages)
    } else {
      ElMessage.error('加载更多消息失败')
    }
  } catch (error) {
    chatLogger.error('加载更多消息失败:', error)
    ElMessage.error('加载更多消息失败')
  } finally {
    loadingMore.value = false
  }
}

// 处理选择联系人
const handleSelectContact = async (contact) => {
  if (!contact) return

  // 如果已经选中该联系人，不做任何操作
  if (currentContact.value && String(currentContact.value.id) === String(contact.id)) {
    return
  }

  // 更新当前联系人
  selectContact(contact)

  // 清空消息列表
  messages.value = []

  // 加载聊天历史
  await loadChatHistory(contact.id)

  // 滚动到底部
  await nextTick()
  scrollToBottom()
}

// 处理刷新聊天
const handleRefreshChat = async () => {
  if (!currentContact.value) return

  // 重新加载联系人列表
  await loadContacts()

  // 重新加载聊天历史
  await loadChatHistory(currentContact.value.id)

  // 滚动到底部
  await nextTick()
  scrollToBottom()
}

// 处理图片选择
const handleImageSelect = (event) => {
  handleImageSelectBase(event)
}

// 处理文件选择
const handleFileSelect = (event) => {
  handleFileSelectBase(event)
}

// 处理发送图片
const handleSendImage = async (data) => {
  if (!currentContact.value || !selectedImage.value) return

  try {
    sending.value = true

    // 创建临时消息ID
    const tempId = `temp-${Date.now()}`

    // 添加临时消息到聊天窗口
    messages.value.push({
      sender: userInfo.value.id,
      receiver: currentContact.value.id,
      content: data.message || '图片',
      messageType: 'IMAGE',
      time: new Date().toISOString(),
      isSelf: true,
      id: tempId,
      status: 'SENDING',
      isRead: false,
      uploadProgress: 0
    })

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    // 准备消息数据
    const messageDTO = {
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      message: data.message || '图片',
      messageType: 'IMAGE',
      clientMessageId: tempId
    }

    // 发送消息
    const result = await sendChatMessage(messageDTO, selectedImage.value)

    if (result && result.code === 200) {
      // 更新消息状态
      updateMessageStatus(tempId, false, {
        id: result.data?.id || tempId,
        time: result.data?.timestamp || new Date().toISOString(),
        fileUrl: result.data?.fileUrl || null,
        status: 'SENT'
      })

      // 更新联系人最新消息
      updateContactLastMessage({
        ...messageDTO,
        id: result.data?.id || tempId,
        content: data.message || '图片',
        time: result.data?.timestamp || new Date().toISOString()
      })

      // 关闭对话框
      imageUploadVisible.value = false
      clearUploadPreview()
    } else {
      // 更新消息状态为发送失败
      updateMessageStatus(tempId, false, {
        errorMessage: result?.message || '发送失败',
        status: 'ERROR'
      })

      ElMessage.error('发送图片失败: ' + (result?.message || '未知错误'))
    }
  } catch (error) {
    chatLogger.error('发送图片失败:', error)
    ElMessage.error('发送图片失败: ' + error.message)
  } finally {
    sending.value = false
  }
}

// 处理发送文件
const handleSendFile = async (data) => {
  if (!currentContact.value || !selectedFile.value) return

  try {
    sending.value = true

    // 创建临时消息ID
    const tempId = `temp-${Date.now()}`

    // 添加临时消息到聊天窗口
    messages.value.push({
      sender: userInfo.value.id,
      receiver: currentContact.value.id,
      content: selectedFile.value.name,
      messageType: 'DOCUMENT',
      time: new Date().toISOString(),
      isSelf: true,
      id: tempId,
      status: 'SENDING',
      isRead: false,
      fileSize: selectedFile.value.size,
      uploadProgress: 0
    })

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    // 准备消息数据
    const messageDTO = {
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      message: data.message || selectedFile.value.name,
      messageType: 'DOCUMENT',
      clientMessageId: tempId
    }

    // 发送消息
    const result = await sendChatMessage(messageDTO, selectedFile.value)

    if (result && result.code === 200) {
      // 更新消息状态
      updateMessageStatus(tempId, false, {
        id: result.data?.id || tempId,
        time: result.data?.timestamp || new Date().toISOString(),
        fileUrl: result.data?.fileUrl || null,
        status: 'SENT'
      })

      // 更新联系人最新消息
      updateContactLastMessage({
        ...messageDTO,
        id: result.data?.id || tempId,
        content: selectedFile.value.name,
        time: result.data?.timestamp || new Date().toISOString()
      })

      // 关闭对话框
      fileUploadVisible.value = false
      clearUploadPreview()
    } else {
      // 更新消息状态为发送失败
      updateMessageStatus(tempId, false, {
        errorMessage: result?.message || '发送失败',
        status: 'ERROR'
      })

      ElMessage.error('发送文件失败: ' + (result?.message || '未知错误'))
    }
  } catch (error) {
    chatLogger.error('发送文件失败:', error)
    ElMessage.error('发送文件失败: ' + error.message)
  } finally {
    sending.value = false
  }
}

// 处理重发消息
const handleResendMessage = async (messageId) => {
  const message = messages.value.find(m => m.id === messageId)
  if (!message) return

  // 确认重发
  try {
    await ElMessageBox.confirm('确定要重新发送此消息吗？', '重发消息', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 更新消息状态为发送中
    updateMessageStatus(messageId, false, {
      status: 'SENDING'
    })

    // 准备消息数据
    const messageDTO = {
      senderId: message.senderId || userInfo.value.id,
      receiverId: message.receiverId || currentContact.value.id,
      message: message.content,
      messageType: message.messageType || 'TEXT',
      clientMessageId: messageId
    }

    // 发送消息
    const result = await sendChatMessage(messageDTO, null)

    if (result && result.code === 200) {
      // 更新消息状态
      updateMessageStatus(messageId, false, {
        id: result.data?.id || messageId,
        time: result.data?.timestamp || new Date().toISOString(),
        status: 'SENT'
      })

      ElMessage.success('消息已重新发送')
    } else {
      // 更新消息状态为发送失败
      updateMessageStatus(messageId, false, {
        errorMessage: result?.message || '发送失败',
        status: 'ERROR'
      })

      ElMessage.error('重发消息失败: ' + (result?.message || '未知错误'))
    }
  } catch (error) {
    if (error !== 'cancel') {
      chatLogger.error('重发消息失败:', error)
      ElMessage.error('重发消息失败: ' + error.message)
    }
  }
}

// 处理预览图片
const handlePreviewImage = (url) => {
  if (!url) return

  // 使用Element Plus的图片预览功能
  const imgElement = document.createElement('img')
  imgElement.src = url
  imgElement.style.display = 'none'
  document.body.appendChild(imgElement)

  // 触发点击事件
  imgElement.click()

  // 移除元素
  setTimeout(() => {
    document.body.removeChild(imgElement)
  }, 100)
}

// 处理新消息事件
const handleNewMessage = (event) => {
  try {
    const messageData = event.detail

    // 格式化消息
    const formattedMessage = formatMessage(messageData, userInfo.value, currentContact.value)

    // 检查是否是重复消息
    if (isMessageDuplicate(formattedMessage, messages.value)) {
      chatLogger.info('忽略重复消息:', formattedMessage.id)
      return
    }

    // 如果是当前聊天的消息，添加到消息列表
    if (currentContact.value &&
        (String(formattedMessage.senderId) === String(currentContact.value.id) ||
         String(formattedMessage.receiverId) === String(currentContact.value.id))) {

      // 添加到消息列表
      messages.value.push(formattedMessage)

      // 滚动到底部
      nextTick(() => scrollToBottom())

      // 如果是接收到的消息，发送已读回执
      if (!formattedMessage.isSelf) {
        // 发送已读回执
        sendReadReceipt(formattedMessage.id, formattedMessage.senderId, userInfo.value.id)

        // 播放通知声音
        playNotificationSound()
      }
    }

    // 更新联系人最新消息
    updateContactLastMessage(formattedMessage)
  } catch (error) {
    chatLogger.error('处理新消息事件失败:', error)
  }
}

// 处理消息发送确认事件
const handleMessageSent = (event) => {
  try {
    const { message, status, tempId } = event.detail

    // 更新消息状态
    if (tempId) {
      updateMessageStatus(tempId, false, {
        id: message.id || tempId,
        status: status || 'SENT'
      })
    }
  } catch (error) {
    chatLogger.error('处理消息发送确认事件失败:', error)
  }
}

// 处理已读回执事件
const handleReadReceipt = (event) => {
  try {
    const { messageId } = event.detail

    // 更新消息已读状态
    const index = messages.value.findIndex(m => m.id === messageId)
    if (index !== -1) {
      messages.value[index].isRead = true
    }
  } catch (error) {
    chatLogger.error('处理已读回执事件失败:', error)
  }
}

// 处理联系人状态更新事件
const handleContactStatusUpdate = (event) => {
  try {
    const { contactId, online } = event.detail

    // 更新联系人在线状态
    const index = contacts.value.findIndex(c => String(c.id) === String(contactId))
    if (index !== -1) {
      contacts.value[index].online = online

      // 如果是当前联系人，更新当前联系人状态
      if (currentContact.value && String(currentContact.value.id) === String(contactId)) {
        currentContact.value.online = online
      }
    }
  } catch (error) {
    chatLogger.error('处理联系人状态更新事件失败:', error)
  }
}

// 运行WebSocket诊断
const runWebSocketDiagnostics = async () => {
  chatLogger.info('开始WebSocket诊断')

  try {
    // 运行诊断
    const diagnosticResult = await diagnoseWebSocketIssues()
    chatLogger.info('诊断结果:', diagnosticResult)

    // 显示诊断结果
    if (diagnosticResult.hasIssues) {
      ElMessage.warning({
        message: `发现 ${diagnosticResult.issues.length} 个连接问题，请查看控制台`,
        duration: 5000
      })

      chatLogger.error('WebSocket连接问题:')
      diagnosticResult.issues.forEach((issue, index) => {
        chatLogger.error(`${index + 1}. ${issue}`)
      })

      // 尝试重新连接
      chatLogger.info('尝试重新初始化WebSocket服务...')
      const initialized = await initializeWebSocket()

      if (initialized) {
        chatLogger.info('WebSocket服务重新初始化成功')
        ElMessage.success('WebSocket服务已重新连接')
      } else {
        chatLogger.error('WebSocket服务重新初始化失败')
        ElMessage.error('WebSocket服务重新连接失败')
      }
    } else {
      ElMessage.success({
        message: 'WebSocket连接正常',
        duration: 3000
      })

      chatLogger.info('WebSocket连接正常')
    }
  } catch (error) {
    chatLogger.error('WebSocket诊断失败:', error)
    ElMessage.error('WebSocket诊断失败，请查看控制台')
  }
}

// 处理离线消息
const processOfflineMessages = async () => {
  // 实现离线消息处理逻辑
  // ...
}

// 组件挂载时初始化
onMounted(async () => {
  // 添加事件监听
  addEventListener('chat-message', handleNewMessage)
  addEventListener('message-sent', handleMessageSent)
  addEventListener('read-receipt', handleReadReceipt)
  addEventListener('contact-status-updated', handleContactStatusUpdate)

  // 初始化聊天
  await initChat()
})

// 组件卸载时清理
onUnmounted(() => {
  // 移除事件监听
  removeEventListener('chat-message', handleNewMessage)
  removeEventListener('message-sent', handleMessageSent)
  removeEventListener('read-receipt', handleReadReceipt)
  removeEventListener('contact-status-updated', handleContactStatusUpdate)
})
</script>

<style scoped>
.chat-container {
  display: flex;
  height: 100%;
  width: 100%;
  background-color: #f0f2f5;
  position: relative;
}

.chat-sidebar {
  width: 320px;
  height: 100%;
  border-right: 1px solid #e9edef;
  flex-shrink: 0;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #efeae2;
  background-image: url('@/assets/chat-bg.png');
  background-size: contain;
}

.chat-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.network-alert {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
}

.upgrade-alert {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  z-index: 9998;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}
</style>
