import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'vue-cropper/dist/index.css'

import App from './App.vue'
import router from './router'
import LoggerPlugin, { LogLevel } from './plugins/logger'
import { initOnlineStatusService } from '@/services/onlineStatusService'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 如果使用TypeScript，为vue3-emoji-picker添加声明
// declare module 'vue3-emoji-picker'

app.use(createPinia())
app.use(router)
// 添加全局错误处理器
app.config.errorHandler = (err, vm, info) => {
  // 处理 Element Plus 相关错误
  const isElementPlusError =
    // getBoundingClientRect 错误
    (err.message && (
      err.message.includes('getBoundingClientRect is not a function') ||
      err.message.includes('_a2.getBoundingClientRect is not a function')
    )) ||
    // use-content.ts 相关错误
    (info && info.includes('use-content.ts')) ||
    // 其他 Element Plus 内部错误
    (err.stack && err.stack.includes('node_modules/element-plus')) ||
    // Popper 相关错误
    (err.stack && err.stack.includes('@popperjs/core'));

  if (isElementPlusError) {
    console.warn('捕获到 Element Plus 相关错误，这通常是由于组件渲染时序问题导致的，已忽略此错误');
    console.warn('错误详情:', err.message);
    console.warn('组件信息:', vm?.$options?.name || 'Unknown Component');
    console.warn('错误位置:', info);
    console.warn('错误堆栈:', err.stack?.split('\n').slice(0, 3).join('\n') || '无堆栈信息');
    return; // 阻止错误继续传播
  }

  // 其他错误正常处理
  console.error('全局错误:', err);
  console.error('错误组件:', vm?.$options?.name || 'Unknown Component');
  console.error('错误信息:', info);
};

app.use(ElementPlus, {
  locale: zhCn,
  // 添加 zIndex 配置，避免 z-index 冲突
  zIndex: 3000
})

// 注册日志插件
app.use(LoggerPlugin, {
  level: process.env.NODE_ENV === 'production' ? LogLevel.ERROR : LogLevel.WARN, // 生产环境只显示错误，开发环境显示警告和错误
  showTimestamp: true,
  showContext: true,
  enabled: process.env.NODE_ENV !== 'production' // 生产环境禁用详细日志
})

// 全局注册 tooltip 指令
import { ElTooltip } from 'element-plus'
app.directive('tooltip', {
  mounted(el, binding) {
    const tooltip = document.createElement('div')
    tooltip.className = 'el-tooltip__trigger'
    tooltip.setAttribute('data-tooltip', binding.value)
    el.appendChild(tooltip)
    new ElTooltip({
      content: binding.value,
      trigger: 'hover',
      placement: binding.arg || 'top',
      ...binding.modifiers
    })
  }
})

// 导入WebSocket服务
import { initializeWebSocketService } from '@/utils/websocket/index'

// 先挂载应用，确保用户信息已从本地存储中恢复
app.mount('#app')

// 添加调试日志
console.log('应用已挂载，WebSocket服务将在用户信息加载后初始化')
