package com.tomato.lostfoundsystem.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
public class RedisUtil {

    private static final Logger logger = LoggerFactory.getLogger(RedisUtil.class);

    private final StringRedisTemplate redisTemplate;

    public RedisUtil(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    // ==================== 通用缓存操作 ====================

    public void set(String key, String value, long timeoutMinutes) {
        try {
            redisTemplate.opsForValue().set(key, value, timeoutMinutes,TimeUnit.MINUTES);
            logger.debug("Redis设置成功 - key: {}, timeout: {}分钟", key, timeoutMinutes);
        } catch (Exception e) {
            logger.error("Redis设置失败 - key: {}, error: {}", key, e.getMessage(), e);
            throw new RuntimeException("Redis操作失败", e);
        }
    }

    public String get(String key) {
        try {
            String value = redisTemplate.opsForValue().get(key);
            logger.debug("Redis获取成功 - key: {}", key);
            return value;
        } catch (Exception e) {
            logger.error("Redis获取失败 - key: {}, error: {}", key, e.getMessage(), e);
            throw new RuntimeException("Redis操作失败", e);
        }
    }

    public void delete(String key) {
        try {
            redisTemplate.delete(key);
            logger.debug("Redis删除成功 - key: {}", key);
        } catch (Exception e) {
            logger.error("Redis删除失败 - key: {}, error: {}", key, e.getMessage(), e);
            throw new RuntimeException("Redis操作失败", e);
        }
    }

    /**
     * 删除单个key
     */
    public void deleteKey(String key) {
        delete(key);
    }

    public boolean hasKey(String key) {
        try {
            Boolean exists = redisTemplate.hasKey(key);
            return Boolean.TRUE.equals(exists);
        } catch (Exception e) {
            logger.error("Redis检查key失败 - key: {}, error: {}", key, e.getMessage(), e);
            throw new RuntimeException("Redis操作失败", e);
        }
    }

    /**
     * 延长key的过期时间
     */
    public void extendExpiration(String key, long timeoutMinutes) {
        try {
            if (hasKey(key)) {
                String value = get(key);
                set(key, value, timeoutMinutes);
                logger.debug("延长key过期时间成功 - key: {}, timeout: {}分钟", key, timeoutMinutes);
            }
        } catch (Exception e) {
            logger.error("延长key过期时间失败 - key: {}, error: {}", key, e.getMessage(), e);
            throw new RuntimeException("Redis操作失败", e);
        }
    }

    /**
     * 添加字符串到集合
     */
    public void addToSet(String key, String value) {
        try {
            redisTemplate.opsForSet().add(key, value);
            logger.debug("Redis添加集合元素成功 - key: {}, value: {}", key, value);
        } catch (Exception e) {
            logger.error("Redis添加集合元素失败 - key: {}, value: {}, error: {}", key, value, e.getMessage(), e);
            throw new RuntimeException("Redis操作失败", e);
        }
    }

    /**
     * 获取集合所有成员
     */
    public Set<String> getSetMembers(String key) {
        try {
            Set<String> members = redisTemplate.opsForSet().members(key);
            logger.debug("Redis获取集合成员成功 - key: {}", key);
            return members != null ? members : new HashSet<>();
        } catch (Exception e) {
            logger.error("Redis获取集合成员失败 - key: {}, error: {}", key, e.getMessage(), e);
            throw new RuntimeException("Redis操作失败", e);
        }
    }

    /**
     * 根据模式获取所有匹配的键
     */
    public Set<String> getKeysByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            logger.debug("Redis获取模式键成功 - pattern: {}", pattern);
            return keys != null ? keys : new HashSet<>();
        } catch (Exception e) {
            logger.error("Redis获取模式键失败 - pattern: {}, error: {}", pattern, e.getMessage(), e);
            throw new RuntimeException("Redis操作失败", e);
        }
    }

    // ==================== WebSocket 会话和已读消息管理 ====================

    /**
     * 存储用户已读的消息
     * @param userId 用户ID
     * @param messageId 消息ID
     */
    public void storeReadMessage(Long userId, Long messageId) {
        redisTemplate.opsForSet().add("user:" + userId + ":readMessages", messageId.toString());
    }

    /**
     * 判断用户是否已读某条消息
     * @param userId 用户ID
     * @param messageId 消息ID
     * @return 是否已读
     */
    public boolean hasReadMessage(Long userId, Long messageId) {
        return redisTemplate.opsForSet().isMember("user:" + userId + ":readMessages", messageId.toString());
    }

    /**
     * 获取未读消息计数键名
     * @param userId 用户ID
     * @param contactId 联系人ID
     * @return 未读计数键名
     */
    private String getUnreadCountKey(Long userId, Long contactId) {
        return "chat:unread:" + userId + ":" + contactId;
    }

    /**
     * 设置用户与联系人之间的未读消息计数
     * @param userId 用户ID
     * @param contactId 联系人ID
     * @param count 未读消息数量
     */
    public void setUnreadCount(Long userId, Long contactId, int count) {
        try {
            String key = getUnreadCountKey(userId, contactId);
            redisTemplate.opsForValue().set(key, String.valueOf(count));
            // 设置30天过期时间，防止长期不活跃的键占用内存
            redisTemplate.expire(key, 30, TimeUnit.DAYS);
            logger.debug("Redis设置未读计数成功 - 用户ID: {}, 联系人ID: {}, 计数: {}", userId, contactId, count);
        } catch (Exception e) {
            logger.error("Redis设置未读计数失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage(), e);
        }
    }

    /**
     * 获取用户与联系人之间的未读消息计数
     * @param userId 用户ID
     * @param contactId 联系人ID
     * @return 未读消息数量
     */
    public int getUnreadCount(Long userId, Long contactId) {
        try {
            String key = getUnreadCountKey(userId, contactId);
            String countStr = redisTemplate.opsForValue().get(key);
            if (countStr != null) {
                logger.debug("Redis获取未读计数成功 - 用户ID: {}, 联系人ID: {}, 计数: {}", userId, contactId, countStr);
                return Integer.parseInt(countStr);
            }
            return 0;
        } catch (Exception e) {
            logger.error("Redis获取未读计数失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 增加用户与联系人之间的未读消息计数
     * @param userId 用户ID
     * @param contactId 联系人ID
     * @return 增加后的未读消息数量
     */
    public int incrementUnreadCount(Long userId, Long contactId) {
        try {
            String key = getUnreadCountKey(userId, contactId);
            Long newCount = redisTemplate.opsForValue().increment(key);
            // 设置30天过期时间，防止长期不活跃的键占用内存
            redisTemplate.expire(key, 30, TimeUnit.DAYS);
            logger.debug("Redis增加未读计数成功 - 用户ID: {}, 联系人ID: {}, 新计数: {}", userId, contactId, newCount);
            return newCount != null ? newCount.intValue() : 1;
        } catch (Exception e) {
            logger.error("Redis增加未读计数失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 重置用户与联系人之间的未读消息计数
     * @param userId 用户ID
     * @param contactId 联系人ID
     */
    public void resetUnreadCount(Long userId, Long contactId) {
        try {
            String key = getUnreadCountKey(userId, contactId);
            redisTemplate.opsForValue().set(key, "0");
            logger.debug("Redis重置未读计数成功 - 用户ID: {}, 联系人ID: {}", userId, contactId);
        } catch (Exception e) {
            logger.error("Redis重置未读计数失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage(), e);
        }
    }

    /**
     * 获取最后一条消息键名
     * @param userId 用户ID
     * @param contactId 联系人ID
     * @return 最后一条消息键名
     */
    private String getLastMessageKey(Long userId, Long contactId) {
        return "chat:lastmsg:" + userId + ":" + contactId;
    }

    /**
     * 存储最后一条聊天信息
     * @param userId 用户ID
     * @param contactId 联系人ID
     * @param messageId 消息ID
     * @param content 消息内容
     * @param messageType 消息类型
     * @param timestamp 消息时间戳
     * @param fileUrl 文件URL（可选）
     */
    public void storeLastMessage(Long userId, Long contactId, Long messageId, String content,
                                String messageType, Long timestamp, String fileUrl) {
        try {
            String key = getLastMessageKey(userId, contactId);

            // 使用Hash结构存储最后一条消息的各个字段
            Map<String, String> messageMap = new HashMap<>();
            messageMap.put("messageId", String.valueOf(messageId));
            messageMap.put("content", content != null ? content : "");
            messageMap.put("messageType", messageType);
            messageMap.put("timestamp", String.valueOf(timestamp));
            if (fileUrl != null) {
                messageMap.put("fileUrl", fileUrl);
            }

            // 存储到Redis
            redisTemplate.opsForHash().putAll(key, messageMap);

            // 设置30天过期时间
            redisTemplate.expire(key, 30, TimeUnit.DAYS);

            logger.debug("Redis存储最后一条消息成功 - 用户ID: {}, 联系人ID: {}, 消息ID: {}", userId, contactId, messageId);
        } catch (Exception e) {
            logger.error("Redis存储最后一条消息失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage(), e);
        }
    }

    /**
     * 获取最后一条聊天信息
     * @param userId 用户ID
     * @param contactId 联系人ID
     * @return 最后一条聊天信息的Map
     */
    public Map<String, String> getLastMessage(Long userId, Long contactId) {
        try {
            String key = getLastMessageKey(userId, contactId);
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);

            if (entries == null || entries.isEmpty()) {
                return null;
            }

            // 转换为String类型的Map
            Map<String, String> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                result.put(entry.getKey().toString(), entry.getValue().toString());
            }

            logger.debug("Redis获取最后一条消息成功 - 用户ID: {}, 联系人ID: {}", userId, contactId);
            return result;
        } catch (Exception e) {
            logger.error("Redis获取最后一条消息失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 批量删除缓存，支持按照模式删除
     * @param pattern 匹配模式
     */
    public void deleteByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null) {
                redisTemplate.delete(keys);  // 删除匹配的所有键
                logger.debug("Redis批量删除成功 - pattern: {}", pattern);
            }
        } catch (Exception e) {
            logger.error("Redis批量删除失败 - pattern: {}, error: {}", pattern, e.getMessage(), e);
            throw new RuntimeException("Redis操作失败", e);
        }
    }

    // ==================== 活跃用户统计相关方法 ====================

    /**
     * 记录用户活动
     * @param userId 用户ID
     */
    public void recordUserActivity(Long userId) {
        if (userId == null) {
            return;
        }

        try {
            LocalDate today = LocalDate.now();
            String dailyKey = "active:users:daily:" + today.format(DateTimeFormatter.ISO_LOCAL_DATE);
            String weeklyKey = "active:users:weekly:" + today.format(DateTimeFormatter.ofPattern("yyyy-ww"));
            String monthlyKey = "active:users:monthly:" + today.format(DateTimeFormatter.ofPattern("yyyy-MM"));

            // 使用HyperLogLog记录活跃用户
            redisTemplate.opsForHyperLogLog().add(dailyKey, userId.toString());
            redisTemplate.opsForHyperLogLog().add(weeklyKey, userId.toString());
            redisTemplate.opsForHyperLogLog().add(monthlyKey, userId.toString());

            // 设置过期时间
            redisTemplate.expire(dailyKey, 2, TimeUnit.DAYS);       // 保留2天
            redisTemplate.expire(weeklyKey, 8, TimeUnit.DAYS);      // 保留8天
            redisTemplate.expire(monthlyKey, 35, TimeUnit.DAYS);    // 保留35天

            // 更新用户最后活跃时间
            redisTemplate.opsForHash().put("user:last_active", userId.toString(),
                    String.valueOf(System.currentTimeMillis()));

        } catch (Exception e) {
            logger.error("记录用户活动失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取日活跃用户数
     */
    public long getDailyActiveUsers() {
        String key = "active:users:daily:" + LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        Long count = redisTemplate.opsForHyperLogLog().size(key);
        return count != null ? count : 0;
    }

    /**
     * 获取周活跃用户数
     */
    public long getWeeklyActiveUsers() {
        String key = "active:users:weekly:" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-ww"));
        Long count = redisTemplate.opsForHyperLogLog().size(key);
        return count != null ? count : 0;
    }

    /**
     * 获取月活跃用户数
     */
    public long getMonthlyActiveUsers() {
        String key = "active:users:monthly:" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        Long count = redisTemplate.opsForHyperLogLog().size(key);
        return count != null ? count : 0;
    }

    /**
     * 更新首页统计数据缓存
     */
    public void updateHomeStatistics(Map<String, Object> statistics) {
        try {
            String key = "statistics:home";

            // 将Map转换为Redis Hash格式
            Map<String, String> stringMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : statistics.entrySet()) {
                stringMap.put(entry.getKey(), String.valueOf(entry.getValue()));
            }

            // 更新Redis Hash
            redisTemplate.opsForHash().putAll(key, stringMap);

        } catch (Exception e) {
            logger.error("更新首页统计数据缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取首页统计数据缓存
     */
    public Map<String, Object> getHomeStatistics() {
        try {
            String key = "statistics:home";
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);

            // 转换为所需的格式
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                String strKey = entry.getKey().toString();
                String strValue = entry.getValue().toString();

                // 尝试将值转换为数字
                try {
                    if (strValue.contains(".")) {
                        result.put(strKey, Double.parseDouble(strValue));
                    } else {
                        result.put(strKey, Integer.parseInt(strValue));
                    }
                } catch (NumberFormatException e) {
                    result.put(strKey, strValue);
                }
            }

            return result;
        } catch (Exception e) {
            logger.error("获取首页统计数据缓存失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
}
