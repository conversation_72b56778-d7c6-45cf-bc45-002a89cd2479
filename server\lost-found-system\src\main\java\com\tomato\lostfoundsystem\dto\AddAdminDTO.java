package com.tomato.lostfoundsystem.dto;


import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class AddAdminDTO {

    @NotEmpty(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度应在 3 到 50 个字符之间")
    private String username;  // 新管理员的用户名

    @NotEmpty(message = "密码不能为空")
    @Size(min = 6, message = "密码长度应大于等于 6 个字符")
    private String password;  // 新管理员的密码
}
