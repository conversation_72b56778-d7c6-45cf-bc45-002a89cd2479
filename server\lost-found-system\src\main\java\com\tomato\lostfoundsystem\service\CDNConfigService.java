package com.tomato.lostfoundsystem.service;

/**
 * CDN配置服务接口
 * 用于管理CDN相关配置
 */
public interface CDNConfigService {
    
    /**
     * 获取当前CDN域名
     * 
     * @return CDN域名
     */
    String getCDNDomain();
    
    /**
     * 更新CDN域名
     * 
     * @param domain 新的CDN域名
     * @return 是否更新成功
     */
    boolean updateCDNDomain(String domain);
    
    /**
     * 检查CDN域名是否有效
     * 
     * @param domain CDN域名
     * @return 是否有效
     */
    boolean validateCDNDomain(String domain);
    
    /**
     * 获取CDN状态信息
     * 
     * @return CDN状态信息
     */
    CDNStatusInfo getCDNStatus();
    
    /**
     * CDN状态信息类
     */
    class CDNStatusInfo {
        private String domain;
        private boolean enabled;
        private String lastUpdated;
        
        public CDNStatusInfo(String domain, boolean enabled, String lastUpdated) {
            this.domain = domain;
            this.enabled = enabled;
            this.lastUpdated = lastUpdated;
        }
        
        public String getDomain() {
            return domain;
        }
        
        public void setDomain(String domain) {
            this.domain = domain;
        }
        
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public String getLastUpdated() {
            return lastUpdated;
        }
        
        public void setLastUpdated(String lastUpdated) {
            this.lastUpdated = lastUpdated;
        }
    }
}
