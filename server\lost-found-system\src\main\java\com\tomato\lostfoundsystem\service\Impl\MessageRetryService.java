package com.tomato.lostfoundsystem.service.Impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.tomato.lostfoundsystem.service.KafkaProducerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class MessageRetryService {
    
    private final KafkaProducerService kafkaProducerService;
    private final ObjectMapper objectMapper;
    
    // 重试间隔（毫秒）
    private final long[] retryIntervals = {1000, 5000, 15000, 30000, 60000}; // 1秒, 5秒, 15秒, 30秒, 1分钟
    
    @Autowired
    public MessageRetryService(KafkaProducerService kafkaProducerService) {
        this.kafkaProducerService = kafkaProducerService;
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 调度消息重试
     * @param message 消息内容
     * @param retryCount 当前重试次数
     */
    public void scheduleRetry(String message, int retryCount) {
        if (retryCount >= retryIntervals.length) {
            // 超过最大重试次数，发送到死信队列
            sendToDeadLetterQueue(message, retryCount);
            return;
        }
        
        // 计算延迟时间
        long delay = retryIntervals[retryCount];
        
        // 使用ScheduledExecutorService进行延迟重试
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        executor.schedule(() -> {
            try {
                // 添加重试次数到消息中
                JsonNode node = objectMapper.readTree(message);
                ((ObjectNode) node).put("retryCount", retryCount + 1);
                String updatedMessage = objectMapper.writeValueAsString(node);
                
                // 发送到重试主题
                kafkaProducerService.sendRetryMessage(updatedMessage);
                log.info("消息已调度重试，重试次数: {}, 延迟: {}ms", retryCount + 1, delay);
            } catch (Exception e) {
                log.error("调度重试失败: {}", e.getMessage(), e);
                sendToDeadLetterQueue(message, retryCount);
            } finally {
                executor.shutdown();
            }
        }, delay, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 发送消息到死信队列
     * @param message 消息内容
     * @param retryCount 重试次数
     */
    private void sendToDeadLetterQueue(String message, int retryCount) {
        try {
            // 添加失败信息
            JsonNode node = objectMapper.readTree(message);
            ((ObjectNode) node).put("retryCount", retryCount);
            ((ObjectNode) node).put("failedAt", System.currentTimeMillis());
            String updatedMessage = objectMapper.writeValueAsString(node);
            
            // 发送到死信队列
            kafkaProducerService.sendToDeadLetterQueue(updatedMessage);
            log.warn("消息发送到死信队列，重试次数: {}", retryCount);
        } catch (Exception e) {
            log.error("发送到死信队列失败: {}", e.getMessage(), e);
        }
    }
}
