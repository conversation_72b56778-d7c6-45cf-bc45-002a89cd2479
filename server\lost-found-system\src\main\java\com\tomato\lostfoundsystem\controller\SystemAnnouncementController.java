package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.SystemAnnouncementDTO;
import com.tomato.lostfoundsystem.service.SystemAnnouncementService;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统公告控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/announcements")
public class SystemAnnouncementController {

    @Autowired
    private SystemAnnouncementService systemAnnouncementService;

    @Autowired
    private SecurityUtil securityUtil;

    /**
     * 创建系统公告（仅管理员）
     */
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    @PostMapping("/create")
    public Result<Long> createAnnouncement(@RequestBody SystemAnnouncementDTO announcementDTO) {
        try {
            Long adminId = securityUtil.getCurrentUserId();
            if (adminId == null) {
                return Result.fail("未登录或登录已过期");
            }

            return systemAnnouncementService.createAnnouncement(announcementDTO, adminId);
        } catch (Exception e) {
            log.error("创建系统公告失败", e);
            return Result.fail("创建系统公告失败: " + e.getMessage());
        }
    }

    /**
     * 更新系统公告（仅管理员）
     */
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    @PutMapping("/update")
    public Result<String> updateAnnouncement(@RequestBody SystemAnnouncementDTO announcementDTO) {
        try {
            Long adminId = securityUtil.getCurrentUserId();
            if (adminId == null) {
                return Result.fail("未登录或登录已过期");
            }

            return systemAnnouncementService.updateAnnouncement(announcementDTO, adminId);
        } catch (Exception e) {
            log.error("更新系统公告失败", e);
            return Result.fail("更新系统公告失败: " + e.getMessage());
        }
    }

    /**
     * 删除系统公告（仅管理员）
     */
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    @DeleteMapping("/delete/{id}")
    public Result<String> deleteAnnouncement(@PathVariable Long id) {
        try {
            Long adminId = securityUtil.getCurrentUserId();
            if (adminId == null) {
                return Result.fail("未登录或登录已过期");
            }

            return systemAnnouncementService.deleteAnnouncement(id, adminId);
        } catch (Exception e) {
            log.error("删除系统公告失败", e);
            return Result.fail("删除系统公告失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统公告详情
     */
    @GetMapping("/detail/{id}")
    public Result<SystemAnnouncementDTO> getAnnouncementDetail(@PathVariable Long id) {
        try {
            Long userId = securityUtil.getCurrentUserId();
            return systemAnnouncementService.getAnnouncementDetail(id, userId);
        } catch (Exception e) {
            log.error("获取系统公告详情失败", e);
            return Result.fail("获取系统公告详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取有效的系统公告列表
     */
    @GetMapping("/valid")
    public Result<List<SystemAnnouncementDTO>> getValidAnnouncements() {
        try {
            Long userId = securityUtil.getCurrentUserId();
            return systemAnnouncementService.getValidAnnouncements(userId);
        } catch (Exception e) {
            log.error("获取有效系统公告失败", e);
            return Result.fail("获取有效系统公告失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新的系统公告
     */
    @GetMapping("/latest")
    public Result<SystemAnnouncementDTO> getLatestAnnouncement() {
        try {
            Long userId = securityUtil.getCurrentUserId();
            return systemAnnouncementService.getLatestAnnouncement(userId);
        } catch (Exception e) {
            log.error("获取最新系统公告失败", e);
            return Result.fail("获取最新系统公告失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有系统公告（仅管理员）
     */
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    @GetMapping("/all")
    public Result<List<SystemAnnouncementDTO>> getAllAnnouncements() {
        try {
            return systemAnnouncementService.getAllAnnouncements();
        } catch (Exception e) {
            log.error("获取所有系统公告失败", e);
            return Result.fail("获取所有系统公告失败: " + e.getMessage());
        }
    }

    /**
     * 发布系统公告（仅管理员）
     */
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    @PostMapping("/publish/{id}")
    public Result<String> publishAnnouncement(@PathVariable Long id) {
        try {
            Long adminId = securityUtil.getCurrentUserId();
            if (adminId == null) {
                return Result.fail("未登录或登录已过期");
            }

            return systemAnnouncementService.publishAnnouncement(id, adminId);
        } catch (Exception e) {
            log.error("发布系统公告失败", e);
            return Result.fail("发布系统公告失败: " + e.getMessage());
        }
    }

    /**
     * 标记公告为已读
     */
    @PostMapping("/mark-read/{id}")
    public Result<String> markAnnouncementAsRead(@PathVariable Long id) {
        try {
            log.info("接收到标记公告已读请求，公告ID: {}", id);

            Long userId = securityUtil.getCurrentUserId();
            if (userId == null) {
                log.warn("标记公告已读失败：用户未登录或登录已过期");
                return Result.fail("未登录或登录已过期");
            }

            log.info("开始处理标记公告已读，公告ID: {}, 用户ID: {}", id, userId);
            Result<String> result = systemAnnouncementService.markAnnouncementAsRead(id, userId);
            log.info("标记公告已读处理完成，结果: {}", result);

            return result;
        } catch (Exception e) {
            log.error("标记公告已读失败，公告ID: {}, 错误: {}", id, e.getMessage(), e);
            return Result.fail("标记公告已读失败: " + e.getMessage());
        }
    }

    /**
     * 获取未读公告数量
     */
    @GetMapping("/unread/count")
    public Result<Integer> getUnreadCount() {
        try {
            Long userId = securityUtil.getCurrentUserId();
            if (userId == null) {
                return Result.fail("未登录或登录已过期");
            }

            return systemAnnouncementService.getUnreadCount(userId);
        } catch (Exception e) {
            log.error("获取未读公告数量失败", e);
            return Result.fail("获取未读公告数量失败: " + e.getMessage());
        }
    }
}
