<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/images/logo_no_text.png" />
    <link rel="icon" type="image/png" href="/images/logo_no_text.png" sizes="32x32" />
    <link rel="apple-touch-icon" href="/images/logo_no_text.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="description" content="校园失物招领系统 - 帮助师生快速找回失物或归还拾获物品" />
    <meta name="theme-color" content="#409EFF" />
    <title>校园失物招领系统</title>

    <!-- 预加载关键资源 -->
    <link rel="preload" href="/fonts/element-icons.woff" as="font" type="font/woff" crossorigin />
    <link rel="preload" href="/images/logo_no_text.png" as="image" />

    <!-- 预连接到可能的API域 -->
    <link rel="dns-prefetch" href="//localhost:8081" />
    <link rel="preconnect" href="//localhost:8081" crossorigin />

    <!-- Vue3 Emoji Picker 样式 -->
    <link rel="stylesheet" href="/vue3-emoji-style.css">

    <!-- 内联关键CSS -->
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f5f7fa;
      }
      #app {
        width: 100%;
        height: 100vh;
      }
      .app-loading {
        display: flex;
        width: 100%;
        height: 100vh;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        background-color: #f5f7fa;
      }
      .app-loading-logo {
        width: 240px;
        height: auto;
        margin-bottom: 30px;
      }
      .app-loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #409EFF;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 初始加载状态 -->
      <div class="app-loading">
        <img src="/images/logo_text.png" alt="校园失物招领系统" class="app-loading-logo" />
        <div class="app-loading-spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
