<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-logo">
        <img src="/images/logo_text.png" alt="校园失物招领系统" class="footer-logo-image" />
      </div>
      <div class="footer-links">
        <div class="footer-section">
          <h3>快速链接</h3>
          <div class="link-grid">
            <router-link to="/" class="footer-link">首页</router-link>
            <router-link to="/lost-items" class="footer-link">失物信息</router-link>
            <router-link to="/found-items" class="footer-link">拾物信息</router-link>
            <router-link to="/intelligent-match" class="footer-link">智能匹配</router-link>
            <router-link to="/chat" class="footer-link">聊天</router-link>
          </div>
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <p>© {{ new Date().getFullYear() }} 校园失物招领系统 - 版权所有</p>
    </div>
  </footer>
</template>

<script setup>
// 无需额外的脚本逻辑
</script>

<style scoped>
.footer {
  background-color: #f0f2f5;
  border-top: 1px solid #e0e0e0;
  padding: 2rem 0 1rem;
  width: 100%;
  position: relative;
  z-index: 5; /* 确保z-index低于聊天容器 */
  margin-top: auto; /* 这会将页脚推到底部 */
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}

.footer-logo {
  flex: 0 0 300px;
  text-align: center;
  padding: 0 1rem;
}

.footer-logo-image {
  width: 220px;
  height: auto;
  object-fit: contain;
}

.footer-links {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 0 1rem;
}

.footer-section {
  flex: 1;
  max-width: 600px;
  text-align: center;
}

.footer-section h3 {
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 1rem;
  font-weight: 600;
}

.link-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.5rem;
}

.footer-link {
  color: #666;
  text-decoration: none;
  transition: all 0.2s;
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.5);
}

.footer-link:hover {
  color: #1890ff;
  background-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.footer-bottom {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
  color: #666;
  font-size: 0.9rem;
  background-color: rgba(0, 0, 0, 0.02);
}

@media (max-width: 768px) {
  .footer-container {
    flex-direction: column;
  }

  .footer-logo {
    margin-bottom: 1.5rem;
    flex: 0 0 auto;
  }

  .footer-links {
    width: 100%;
  }

  .link-grid {
    justify-content: center;
  }

  .footer-link {
    flex: 0 0 calc(50% - 1rem);
    text-align: center;
  }
}
</style>
