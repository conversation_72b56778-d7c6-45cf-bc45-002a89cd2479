
package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.dto.FoundItemDTO;
import com.tomato.lostfoundsystem.dto.FoundItemDetailDTO;
import com.tomato.lostfoundsystem.entity.FoundItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface FoundItemMapper {
    int insertFoundItem(FoundItem foundItem);

    List<FoundItem> selectFoundItems(String keyword, String foundLocation, String status, LocalDateTime startDateTime, LocalDateTime endDateTime, String timeFilterType);

    /**
     * 根据拾物ID查询拾物信息
     */
    FoundItemDetailDTO selectFoundItemDetailsById(Long id);

    /**
     * 更新拾物信息
     */
    int updateById(FoundItem foundItem);

    /**
     * 更新拾物审核状态
     */
    int updateAuditStatus(FoundItem foundItem);

    /**
     * 更新拾物状态
     * @param id 拾物ID
     * @param status 状态值
     * @return 影响的行数
     */
    int updateFoundItemStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 根据拾物ID查询拾物信息
     */
    FoundItem selectById(Long id);


    /**
     * 删除拾物信息
     */
    int deleteById(Long id);

    /**
     * 获取个人发布的拾物信息
     */
    List<FoundItem> findFoundItemsByUserId(Long userId);

    /**
     * 查询拾物列表（管理员）
     * @param keyword 关键词
     * @param auditStatus 审核状态
     * @param status 认领状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userId 用户ID
     * @return 拾物列表
     */
    List<FoundItemDetailDTO> selectFoundItemList(String keyword, String auditStatus, String status, LocalDateTime startDate, LocalDateTime endDate, Long userId);

    /**
     * 根据审核状态统计拾物数量
     *
     * @param auditStatus 审核状态
     * @return 拾物数量
     */
    int countByAuditStatus(@Param("auditStatus") String auditStatus);

    /**
     * 根据状态和审核状态统计拾物数量
     *
     * @param status 状态
     * @param auditStatus 审核状态
     * @return 拾物数量
     */
    int countByStatusAndAuditStatus(@Param("status") String status, @Param("auditStatus") String auditStatus);
}
