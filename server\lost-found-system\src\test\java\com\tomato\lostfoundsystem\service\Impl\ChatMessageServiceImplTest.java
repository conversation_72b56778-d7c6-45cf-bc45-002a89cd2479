package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.entity.Conversation;
import com.tomato.lostfoundsystem.entity.MessageReadStatus;
import com.tomato.lostfoundsystem.enums.MessageType;
import com.tomato.lostfoundsystem.mapper.ChatMessageMapper;
import com.tomato.lostfoundsystem.mapper.ConversationMapper;
import com.tomato.lostfoundsystem.mapper.MessageAttachmentMapper;
import com.tomato.lostfoundsystem.mapper.MessageReadStatusMapper;
import com.tomato.lostfoundsystem.service.AsyncTaskService;
import com.tomato.lostfoundsystem.service.KafkaProducerService;
import com.tomato.lostfoundsystem.service.RedisService;
import com.tomato.lostfoundsystem.utils.FileValidationUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ChatMessageServiceImplTest {

    @InjectMocks
    private ChatMessageServiceImpl chatMessageService;

    @Mock
    private ChatMessageMapper chatMessageMapper;

    @Mock
    private MessageReadStatusMapper messageReadStatusMapper;

    @Mock
    private RedisService redisService;

    @Mock
    private KafkaProducerService kafkaProducerService;

    @Mock
    private SimpMessagingTemplate messagingTemplate;

    @Mock
    private MessageAttachmentMapper messageAttachmentMapper;

    @Mock
    private ConversationMapper conversationMapper;

    @Mock
    private AsyncTaskService asyncTaskService;

    @Mock
    private FileValidationUtil fileValidationUtil;

    @Mock
    private ConversationServiceImpl conversationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置事务同步管理器
        TransactionSynchronizationManager.initSynchronization();
    }

    @Test
    void testMarkAllAsRead() {
        // 准备测试数据
        Long userId = 1L;
        Long contactId = 2L;
        
        // 创建未读消息ID列表
        List<Long> unreadMessageIds = new ArrayList<>();
        unreadMessageIds.add(100L);
        unreadMessageIds.add(101L);
        
        // 创建会话
        Conversation conversation = new Conversation();
        conversation.setId(1L);
        conversation.setUser1Id(userId);
        conversation.setUser2Id(contactId);
        conversation.setUnreadCount(2);
        
        // 模拟 messageReadStatusMapper.getUnreadMessageIds 返回未读消息ID列表
        when(messageReadStatusMapper.getUnreadMessageIds(userId, contactId)).thenReturn(unreadMessageIds);
        
        // 模拟 conversationMapper.getConversation 返回会话
        when(conversationMapper.getConversation(userId, contactId)).thenReturn(conversation);
        
        // 调用被测试方法
        chatMessageService.markAllAsRead(contactId, userId);
        
        // 验证 messageReadStatusMapper.batchUpdateReadStatus 被调用
        verify(messageReadStatusMapper).batchUpdateReadStatus(unreadMessageIds, userId, true);
        
        // 验证 redisService.storeReadMessage 被调用
        verify(redisService, times(2)).storeReadMessage(userId, unreadMessageIds.get(0));
        verify(redisService, times(2)).storeReadMessage(userId, unreadMessageIds.get(1));
        
        // 验证 asyncTaskService.pushReadReceiptToWebSocket 被调用
        verify(asyncTaskService).pushReadReceiptToWebSocket(eq(contactId), any());
        
        // 验证 conversation.setUnreadCount 被调用
        assertEquals(0, conversation.getUnreadCount());
        
        // 验证 conversationMapper.update 被调用
        verify(conversationMapper).update(conversation);
    }

    @Test
    void testMarkAllAsRead_NoUnreadMessages() {
        // 准备测试数据
        Long userId = 1L;
        Long contactId = 2L;
        
        // 模拟 messageReadStatusMapper.getUnreadMessageIds 返回空列表
        when(messageReadStatusMapper.getUnreadMessageIds(userId, contactId)).thenReturn(new ArrayList<>());
        
        // 调用被测试方法
        chatMessageService.markAllAsRead(contactId, userId);
        
        // 验证 messageReadStatusMapper.batchUpdateReadStatus 没有被调用
        verify(messageReadStatusMapper, never()).batchUpdateReadStatus(anyList(), anyLong(), anyBoolean());
        
        // 验证 redisService.storeReadMessage 没有被调用
        verify(redisService, never()).storeReadMessage(anyLong(), anyLong());
        
        // 验证 asyncTaskService.pushReadReceiptToWebSocket 没有被调用
        verify(asyncTaskService, never()).pushReadReceiptToWebSocket(anyLong(), any());
        
        // 验证 conversationMapper.update 没有被调用
        verify(conversationMapper, never()).update(any(Conversation.class));
    }

    @Test
    void testMarkAsRead() {
        // 准备测试数据
        Long messageId = 100L;
        Long userId = 1L;
        
        // 创建消息
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setId(messageId);
        chatMessage.setSenderId(2L);
        chatMessage.setReceiverId(userId);
        chatMessage.setMessage("Test message");
        chatMessage.setMessageType(MessageType.TEXT);
        chatMessage.setTimestamp(new Date());
        
        // 模拟 chatMessageMapper.getMessageById 返回消息
        when(chatMessageMapper.getMessageById(messageId)).thenReturn(chatMessage);
        
        // 模拟 messageReadStatusMapper.updateReadStatus 返回 1，表示更新成功
        when(messageReadStatusMapper.updateReadStatus(messageId, userId, true)).thenReturn(1);
        
        // 调用被测试方法
        chatMessageService.markAsRead(messageId, userId);
        
        // 验证 chatMessageMapper.getMessageById 被调用
        verify(chatMessageMapper).getMessageById(messageId);
        
        // 验证 messageReadStatusMapper.updateReadStatus 被调用
        verify(messageReadStatusMapper).updateReadStatus(messageId, userId, true);
        
        // 验证 redisService.storeReadMessage 被调用
        verify(redisService).storeReadMessage(userId, messageId);
        
        // 验证 asyncTaskService.pushReadReceiptToWebSocket 被调用
        verify(asyncTaskService).pushReadReceiptToWebSocket(eq(2L), any());
    }
}
