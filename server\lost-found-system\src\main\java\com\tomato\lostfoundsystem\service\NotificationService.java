package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.dto.NotificationDTO;
import com.tomato.lostfoundsystem.entity.Notification;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface NotificationService {
    /**
     * 创建并存储审核通过通知
     *
     * @param userId 用户ID
     * @param itemType 物品类型
     * @param itemId 物品ID
     * @return 通知ID
     */
    Long createApprovalNotification(Long userId, String itemType, Long itemId);

    /**
     * 创建并存储审核拒绝通知
     *
     * @param userId 用户ID
     * @param itemType 物品类型
     * @param itemId 物品ID
     * @param remarks 拒绝原因
     * @return 通知ID
     */
    Long createRejectionNotification(Long userId, String itemType, Long itemId, String remarks);

    /**
     * 创建并存储通知
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param type 通知类型
     * @return 通知ID
     */
    Long createNotification(Long userId, String title, String message, String type);

    /**
     * 创建并存储通知（带有元数据）
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param type 通知类型
     * @param metadata 元数据JSON字符串
     * @return 通知ID
     */
    Long createNotification(Long userId, String title, String message, String type, String metadata);

    /**
     * 创建并存储完整通知
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param type 通知类型
     * @param relatedItemId 关联物品ID
     * @param relatedItemType 关联物品类型
     * @param metadata 元数据JSON字符串
     * @param auditorId 审核员ID
     * @return 通知ID
     */
    Long createNotification(Long userId, String title, String message, String type,
                           Long relatedItemId, String relatedItemType,
                           String metadata, Long auditorId);

    // 兼容旧版本的方法（内部使用新的方法实现）
    void sendApprovalNotification(Long userId, String itemType, Long itemId);
    void sendRejectionNotification(Long userId, String itemType, Long itemId, String remarks);


    List<NotificationDTO> getNotifications(Long userId, String status);

    void markNotificationAsRead(Long notificationId);

    void deleteNotification(Long notificationId);

    void sendNotificationToUser(Long userId, String title, String message);

    void sendNotificationToAllUsers(NotificationDTO notificationDTO);

    void sendNotificationToAdmins(NotificationDTO notificationDTO);

    void sendNotificationToUsersByRole(NotificationDTO notificationDTO);

    Integer getUnreadCount(Long userId);

    /**
     * 检查通知是否已经发送过
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @return 如果通知已经发送过，返回true；否则返回false
     */
    boolean isNotificationSent(Long userId, Long notificationId);

    /**
     * 标记通知为已发送
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @param expirationSeconds 过期时间（秒）
     */
    void markNotificationAsSent(Long userId, Long notificationId, int expirationSeconds);

    /**
     * 清理过期的通知记录
     */
    void cleanExpiredNotifications();
}

