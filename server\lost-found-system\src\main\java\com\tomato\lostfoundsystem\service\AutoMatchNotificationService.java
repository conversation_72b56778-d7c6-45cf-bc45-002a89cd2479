package com.tomato.lostfoundsystem.service;

/**
 * 自动匹配通知服务接口
 * 用于在物品发布或审核通过时自动触发匹配并发送通知
 */
public interface AutoMatchNotificationService {

    /**
     * 处理失物信息的自动匹配
     *
     * @param lostItemId 失物ID
     * @param userId 用户ID
     */
    void processLostItemAutoMatch(Long lostItemId, Long userId);

    /**
     * 处理拾物信息的自动匹配
     *
     * @param foundItemId 拾物ID
     * @param userId 用户ID
     */
    void processFoundItemAutoMatch(Long foundItemId, Long userId);
}
