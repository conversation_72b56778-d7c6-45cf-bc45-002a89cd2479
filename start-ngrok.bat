@echo off
echo Starting ngrok tunnel...
echo.

REM Set ngrok path and config path
set NGROK_PATH=D:\ngrok\ngrok.exe
set CONFIG_PATH=C:\Users\<USER>\AppData\Local\ngrok\ngrok.yml

REM Display config path
echo Using config file: %CONFIG_PATH%
echo.

REM Start frontend tunnel with explicit config file
"%NGROK_PATH%" start --config="%CONFIG_PATH%" lost-found-frontend

echo.
echo Press any key to exit...
pause > nul
