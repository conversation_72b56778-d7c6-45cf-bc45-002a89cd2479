<template>
  <div class="websocket-debug-container">
    <h1>WebSocket 调试页面</h1>

    <!-- 连接状态 -->
    <div class="status-panel">
      <div class="status-item">
        <span class="label">连接状态:</span>
        <span class="value" :class="{ 'connected': connected, 'disconnected': !connected }">
          {{ connected ? '已连接' : '未连接' }}
        </span>
      </div>
      <div class="status-item">
        <span class="label">用户ID:</span>
        <span class="value">{{ userInfo?.id || '未登录' }}</span>
      </div>
      <div class="status-item">
        <span class="label">用户名:</span>
        <span class="value">{{ userInfo?.username || '未登录' }}</span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-panel">
      <el-button type="primary" @click="connect" :disabled="connected || connecting">
        {{ connecting ? '连接中...' : '连接WebSocket' }}
      </el-button>
      <el-button type="danger" @click="disconnect" :disabled="!connected">断开连接</el-button>
      <el-button type="success" @click="subscribe" :disabled="!connected || subscribed">订阅消息</el-button>
      <el-button type="warning" @click="clearMessages">清空消息</el-button>
    </div>

    <!-- 自定义订阅 -->
    <div class="custom-subscription-panel" v-if="connected">
      <h3>自定义订阅</h3>
      <div class="custom-subscription-form">
        <el-input
          v-model="customSubscriptionPath"
          placeholder="输入订阅路径，例如: /user/queue/private"
          :disabled="!connected"
        >
          <template #prepend>路径</template>
        </el-input>
        <el-button
          type="primary"
          @click="subscribeCustomPath"
          :disabled="!connected || !customSubscriptionPath"
        >
          订阅
        </el-button>
      </div>
      <div class="custom-subscriptions-list" v-if="customSubscriptions.length > 0">
        <h4>当前自定义订阅 ({{ customSubscriptions.length }})</h4>
        <el-tag
          v-for="(sub, index) in customSubscriptions"
          :key="index"
          closable
          @close="unsubscribeCustomPath(index)"
          class="custom-subscription-tag"
        >
          {{ sub.path }}
        </el-tag>
      </div>
    </div>

    <!-- 发送消息 -->
    <div class="send-panel">
      <h3>发送测试消息</h3>
      <el-form :model="messageForm" label-width="100px">
        <el-form-item label="接收者ID">
          <el-input v-model="messageForm.receiverId" placeholder="输入接收者ID"></el-input>
        </el-form-item>
        <el-form-item label="消息内容">
          <el-input v-model="messageForm.message" placeholder="输入消息内容"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="sendMessage" :disabled="!connected || !subscribed || sending">
            {{ sending ? '发送中...' : '发送消息' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 消息列表 -->
    <div class="message-panel">
      <h3>接收到的消息 ({{ receivedMessages.length }})</h3>
      <div class="message-list" ref="messageList">
        <div v-for="(message, index) in receivedMessages" :key="index" class="message-item">
          <div class="message-header">
            <span class="message-time">{{ formatTime(message._receivedTime) }}</span>
            <span class="message-type">{{ message.messageType || 'TEXT' }}</span>
          </div>
          <div class="message-content">
            <pre>{{ JSON.stringify(message, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志面板 -->
    <div class="log-panel">
      <h3>操作日志</h3>
      <div class="log-list" ref="logList">
        <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ formatTime(log.time) }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'
import { Client } from '@stomp/stompjs'
import SockJS from 'sockjs-client/dist/sockjs.min.js'

// 状态
const connected = ref(false)
const connecting = ref(false)
const subscribed = ref(false)
const sending = ref(false)
const logs = ref([])
const receivedMessages = ref([])
const logList = ref(null)
const messageList = ref(null)
const stompClient = ref(null)
const subscriptions = ref([])
const customSubscriptionPath = ref('')
const customSubscriptions = ref([])

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo || {})

// 表单数据
const messageForm = reactive({
  receiverId: '',
  message: '',
  messageType: 'TEXT'
})

// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.push({
    time: new Date(),
    message,
    type
  })

  // 滚动到底部
  nextTick(() => {
    if (logList.value) {
      logList.value.scrollTop = logList.value.scrollHeight
    }
  })
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleTimeString('zh-CN', { hour12: false }) + '.' + date.getMilliseconds().toString().padStart(3, '0')
}

// 连接 WebSocket
const connect = async () => {
  try {
    connecting.value = true
    addLog('开始连接 WebSocket...')

    // 获取 Token
    const token = userStore.token
    if (!token) {
      addLog('未找到用户 Token，无法连接', 'error')
      ElMessage.error('未找到用户 Token，请先登录')
      connecting.value = false
      return
    }

    // 创建 SockJS 实例
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081'
    const wsUrl = `${apiBaseUrl}/ws?token=${encodeURIComponent(token)}&t=${Date.now()}`
    addLog(`连接 URL: ${wsUrl.replace(token, '***')}`)
    const socket = new SockJS(wsUrl)

    // 创建 STOMP 客户端
    stompClient.value = new Client({
      webSocketFactory: () => socket,
      connectHeaders: {
        Authorization: `Bearer ${token}`
      },
      debug: (str) => {
        console.log(str)
      },
      reconnectDelay: 5000,
      heartbeatIncoming: 25000,
      heartbeatOutgoing: 25000
    })

    // 连接成功回调
    stompClient.value.onConnect = (frame) => {
      addLog(`WebSocket 连接成功: ${frame.command}`, 'success')
      connected.value = true
      connecting.value = false

      // 自动订阅
      subscribe()
    }

    // 连接错误回调
    stompClient.value.onStompError = (frame) => {
      addLog(`WebSocket 连接错误: ${frame.headers?.message || JSON.stringify(frame)}`, 'error')
      connected.value = false
      connecting.value = false
      subscribed.value = false
    }

    // 连接断开回调
    stompClient.value.onWebSocketClose = (event) => {
      addLog(`WebSocket 连接断开: ${event.code} ${event.reason}`, 'warning')
      connected.value = false
      connecting.value = false
      subscribed.value = false
    }

    // 激活连接
    stompClient.value.activate()
    addLog('WebSocket 连接请求已发送')
  } catch (error) {
    addLog(`WebSocket 连接失败: ${error.message}`, 'error')
    console.error('WebSocket 连接失败:', error)
    connected.value = false
    connecting.value = false
  }
}

// 断开连接
const disconnect = () => {
  try {
    if (stompClient.value) {
      // 取消所有订阅
      subscriptions.value.forEach(sub => {
        try {
          sub.unsubscribe()
        } catch (error) {
          console.warn('取消订阅失败:', error)
        }
      })
      subscriptions.value = []

      // 断开连接
      stompClient.value.deactivate()
      addLog('WebSocket 连接已断开', 'warning')
    }

    connected.value = false
    subscribed.value = false
  } catch (error) {
    addLog(`断开连接失败: ${error.message}`, 'error')
    console.error('断开连接失败:', error)
  }
}

// 订阅消息
const subscribe = () => {
  if (!connected.value || !stompClient.value) {
    addLog('WebSocket 未连接，无法订阅', 'error')
    return
  }

  try {
    // 取消之前的订阅
    subscriptions.value.forEach(sub => {
      try {
        sub.unsubscribe()
      } catch (error) {
        console.warn('取消订阅失败:', error)
      }
    })
    subscriptions.value = []

    addLog('开始订阅主题...')

    // 订阅私人消息
    const privateSubscription = stompClient.value.subscribe('/user/queue/private', (message) => {
      addLog(`收到私人消息: ${message.headers.destination}`, 'success')
      addLog(`原始消息头: ${JSON.stringify(message.headers)}`, 'info')
      addLog(`原始消息体: ${message.body}`, 'info')

      try {
        // 记录原始消息对象的所有属性
        console.log('完整的STOMP消息对象:', message)
        console.log('消息头:', message.headers)
        console.log('消息体:', message.body)

        // 尝试解析消息体
        let data
        try {
          data = JSON.parse(message.body)
          addLog(`成功解析JSON消息: ${JSON.stringify(data)}`, 'success')
        } catch (parseError) {
          addLog(`消息体不是有效的JSON: ${parseError.message}`, 'warning')
          // 如果不是JSON，使用原始消息体
          data = {
            rawContent: message.body,
            _isRawMessage: true
          }
        }

        // 添加接收时间和消息头信息
        data._receivedTime = new Date()
        data._headers = message.headers
        data._destination = message.headers.destination

        // 添加到消息列表
        receivedMessages.value.unshift(data)

        // 滚动到顶部
        nextTick(() => {
          if (messageList.value) {
            messageList.value.scrollTop = 0
          }
        })
      } catch (error) {
        addLog(`处理消息失败: ${error.message}`, 'error')
        console.error('处理消息错误:', error)

        // 尝试保存原始消息
        try {
          receivedMessages.value.unshift({
            _receivedTime: new Date(),
            _isErrorMessage: true,
            _rawMessage: message.body,
            _error: error.message
          })
        } catch (e) {
          console.error('保存原始消息失败:', e)
        }
      }
    })
    subscriptions.value.push(privateSubscription)
    addLog('已订阅私人消息: /user/queue/private', 'success')

    // 订阅发送确认
    const sentSubscription = stompClient.value.subscribe('/user/queue/sent', (message) => {
      addLog(`收到发送确认: ${message.headers.destination}`, 'success')
      addLog(`原始确认消息头: ${JSON.stringify(message.headers)}`, 'info')
      addLog(`原始确认消息体: ${message.body}`, 'info')

      try {
        // 记录原始消息对象的所有属性
        console.log('完整的STOMP确认消息对象:', message)
        console.log('确认消息头:', message.headers)
        console.log('确认消息体:', message.body)

        // 尝试解析消息体
        let data
        try {
          data = JSON.parse(message.body)
          addLog(`成功解析确认消息: ${JSON.stringify(data)}`, 'success')
          addLog(`消息已发送成功，ID: ${data.id}`, 'success')
        } catch (parseError) {
          addLog(`确认消息体不是有效的JSON: ${parseError.message}`, 'warning')
          // 如果不是JSON，使用原始消息体
          data = {
            rawContent: message.body,
            _isRawMessage: true
          }
        }

        // 添加接收时间和消息头信息
        data._receivedTime = new Date()
        data._headers = message.headers
        data._destination = message.headers.destination
        data._type = 'SENT_CONFIRMATION'

        // 添加到消息列表
        receivedMessages.value.unshift(data)

        // 滚动到顶部
        nextTick(() => {
          if (messageList.value) {
            messageList.value.scrollTop = 0
          }
        })
      } catch (error) {
        addLog(`处理确认消息失败: ${error.message}`, 'error')
        console.error('处理确认消息错误:', error)

        // 尝试保存原始消息
        try {
          receivedMessages.value.unshift({
            _receivedTime: new Date(),
            _isErrorMessage: true,
            _type: 'SENT_CONFIRMATION_ERROR',
            _rawMessage: message.body,
            _error: error.message
          })
        } catch (e) {
          console.error('保存原始确认消息失败:', e)
        }
      }
    })
    subscriptions.value.push(sentSubscription)
    addLog('已订阅发送确认: /user/queue/sent', 'success')

    // 尝试订阅其他可能的路径
    try {
      // 尝试订阅 /queue/private 路径（不带/user前缀）
      const alternativePrivateSubscription = stompClient.value.subscribe('/queue/private', (message) => {
        addLog(`收到备用路径私人消息: /queue/private`, 'success')
        addLog(`备用路径原始消息头: ${JSON.stringify(message.headers)}`, 'info')
        addLog(`备用路径原始消息体: ${message.body}`, 'info')

        try {
          // 记录原始消息对象的所有属性
          console.log('备用路径完整的STOMP消息对象:', message)

          // 尝试解析消息体
          let data
          try {
            data = JSON.parse(message.body)
            addLog(`成功解析备用路径消息: ${JSON.stringify(data)}`, 'success')
          } catch (parseError) {
            addLog(`备用路径消息体不是有效的JSON: ${parseError.message}`, 'warning')
            data = {
              rawContent: message.body,
              _isRawMessage: true
            }
          }

          // 添加接收时间和消息头信息
          data._receivedTime = new Date()
          data._headers = message.headers
          data._destination = message.headers.destination
          data._type = 'ALTERNATIVE_PATH'

          // 添加到消息列表
          receivedMessages.value.unshift(data)
        } catch (error) {
          addLog(`处理备用路径消息失败: ${error.message}`, 'error')
        }
      })

      subscriptions.value.push(alternativePrivateSubscription)
      addLog(`已订阅备用私人消息路径: /queue/private`, 'success')
    } catch (error) {
      addLog(`订阅备用路径失败: ${error.message}`, 'warning')
    }

    subscribed.value = true
  } catch (error) {
    addLog(`订阅失败: ${error.message}`, 'error')
    console.error('订阅失败:', error)
    subscribed.value = false
  }
}

// 发送消息
const sendMessage = () => {
  if (!connected.value || !subscribed.value) {
    addLog('WebSocket 未连接或未订阅，无法发送消息', 'error')
    return
  }

  if (!messageForm.receiverId || !messageForm.message) {
    addLog('接收者ID和消息内容不能为空', 'error')
    return
  }

  try {
    sending.value = true
    addLog('准备发送消息...')

    // 准备消息数据
    const messageDTO = {
      senderId: userInfo.value.id,
      receiverId: messageForm.receiverId,
      message: messageForm.message,
      messageType: messageForm.messageType || 'TEXT',
      clientMessageId: `debug-${Date.now()}`,
      timestamp: Date.now()
    }

    // 发送消息
    stompClient.value.publish({
      destination: '/app/privateMessage',
      headers: {
        'content-type': 'application/json'
      },
      body: JSON.stringify(messageDTO)
    })

    addLog(`消息已发送: ${JSON.stringify(messageDTO)}`, 'success')

    // 清空消息内容
    messageForm.message = ''
  } catch (error) {
    addLog(`发送消息失败: ${error.message}`, 'error')
    console.error('发送消息失败:', error)
  } finally {
    sending.value = false
  }
}

// 清空消息
const clearMessages = () => {
  receivedMessages.value = []
  addLog('消息列表已清空')
}

// 订阅自定义路径
const subscribeCustomPath = () => {
  if (!connected.value || !stompClient.value || !customSubscriptionPath.value) {
    addLog('WebSocket 未连接或路径为空，无法订阅', 'error')
    return
  }

  try {
    const path = customSubscriptionPath.value
    addLog(`尝试订阅自定义路径: ${path}`)

    // 检查是否已经订阅过该路径
    const existingSubscription = customSubscriptions.value.find(sub => sub.path === path)
    if (existingSubscription) {
      addLog(`已经订阅过该路径: ${path}`, 'warning')
      return
    }

    // 创建新订阅
    const subscription = stompClient.value.subscribe(path, (message) => {
      addLog(`收到自定义路径消息: ${path}`, 'success')
      addLog(`自定义路径原始消息头: ${JSON.stringify(message.headers)}`, 'info')
      addLog(`自定义路径原始消息体: ${message.body}`, 'info')

      try {
        // 记录原始消息对象
        console.log(`自定义路径 ${path} 完整的STOMP消息对象:`, message)

        // 尝试解析消息体
        let data
        try {
          data = JSON.parse(message.body)
          addLog(`成功解析自定义路径消息: ${JSON.stringify(data)}`, 'success')
        } catch (parseError) {
          addLog(`自定义路径消息体不是有效的JSON: ${parseError.message}`, 'warning')
          data = {
            rawContent: message.body,
            _isRawMessage: true
          }
        }

        // 添加接收时间和消息头信息
        data._receivedTime = new Date()
        data._headers = message.headers
        data._destination = message.headers.destination
        data._type = 'CUSTOM_SUBSCRIPTION'
        data._path = path

        // 添加到消息列表
        receivedMessages.value.unshift(data)
      } catch (error) {
        addLog(`处理自定义路径消息失败: ${error.message}`, 'error')
      }
    })

    // 添加到自定义订阅列表
    customSubscriptions.value.push({
      path,
      subscription
    })

    addLog(`已成功订阅自定义路径: ${path}`, 'success')

    // 清空输入框
    customSubscriptionPath.value = ''
  } catch (error) {
    addLog(`订阅自定义路径失败: ${error.message}`, 'error')
    console.error('订阅自定义路径失败:', error)
  }
}

// 取消自定义订阅
const unsubscribeCustomPath = (index) => {
  try {
    const subscription = customSubscriptions.value[index]
    if (subscription && subscription.subscription) {
      subscription.subscription.unsubscribe()
      addLog(`已取消订阅自定义路径: ${subscription.path}`, 'warning')
    }

    // 从列表中移除
    customSubscriptions.value.splice(index, 1)
  } catch (error) {
    addLog(`取消订阅自定义路径失败: ${error.message}`, 'error')
    console.error('取消订阅自定义路径失败:', error)
  }
}

// 生命周期钩子
onMounted(() => {
  addLog('WebSocket 调试页面已加载')
})

onUnmounted(() => {
  // 断开连接
  disconnect()

  // 清理自定义订阅
  customSubscriptions.value.forEach(sub => {
    try {
      if (sub.subscription) {
        sub.subscription.unsubscribe()
      }
    } catch (error) {
      console.warn('清理自定义订阅失败:', error)
    }
  })
})
</script>

<style scoped>
.websocket-debug-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.status-panel {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.status-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  margin-right: 5px;
}

.connected {
  color: #67c23a;
  font-weight: bold;
}

.disconnected {
  color: #f56c6c;
  font-weight: bold;
}

.action-panel {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.custom-subscription-panel {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.custom-subscription-form {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.custom-subscriptions-list {
  margin-top: 10px;
}

.custom-subscription-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.send-panel {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.message-panel {
  margin-bottom: 20px;
}

.message-list {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #fff;
}

.message-item {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #606266;
}

.message-content {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

.message-content pre {
  margin: 0;
  white-space: pre-wrap;
}

.log-panel {
  margin-bottom: 20px;
}

.log-list {
  height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #fff;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
}

.info {
  color: #606266;
}

.success {
  color: #67c23a;
}

.warning {
  color: #e6a23c;
}

.error {
  color: #f56c6c;
}
</style>
