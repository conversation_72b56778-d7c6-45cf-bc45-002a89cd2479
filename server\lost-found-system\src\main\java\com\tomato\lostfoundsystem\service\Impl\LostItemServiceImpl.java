package com.tomato.lostfoundsystem.service.Impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.ItemAuditDTO;
import com.tomato.lostfoundsystem.dto.LostItemDTO;
import com.tomato.lostfoundsystem.dto.LostItemDetailsDTO;
import com.tomato.lostfoundsystem.entity.ItemAudit;
import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.entity.ItemImage;
import com.tomato.lostfoundsystem.enums.AuditStatusEnum;
import com.tomato.lostfoundsystem.mapper.ItemAuditMapper;
import com.tomato.lostfoundsystem.mapper.ItemImageMapper;
import com.tomato.lostfoundsystem.mapper.LostItemMapper;
import com.tomato.lostfoundsystem.service.AsyncTaskService;
import com.tomato.lostfoundsystem.service.ItemImageService;
import com.tomato.lostfoundsystem.service.LostItemService;
import com.tomato.lostfoundsystem.utils.AliyunOSSUtil;
import com.tomato.lostfoundsystem.utils.RedisUtil;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class LostItemServiceImpl implements LostItemService {

    private final LostItemMapper lostItemMapper;
    private final AliyunOSSUtil aliyunOSSUtil;

    private final ItemAuditMapper itemAuditMapper;


    private final RedisUtil redisUtil;

    private final SecurityUtil securityUtil;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private ItemImageService itemImageService;

    public LostItemServiceImpl(LostItemMapper lostItemMapper, AliyunOSSUtil aliyunOSSUtil,  ItemAuditMapper itemAuditMapper, RedisUtil redisUtil, SecurityUtil securityUtil) {
        this.lostItemMapper = lostItemMapper;
        this.aliyunOSSUtil = aliyunOSSUtil;
        this.itemAuditMapper = itemAuditMapper;
        this.redisUtil = redisUtil;
        this.securityUtil = securityUtil;
    }

    @Override
    public Result<Object> publishLostItem(LostItemDTO dto) {
        try {
            // 1. 统一处理图片上传 - 将所有图片合并到一个列表中处理
            List<MultipartFile> allImages = new ArrayList<>();
            List<String> uploadedImageUrls = new ArrayList<>(); // 存储所有上传后的图片URL
            String imageUrl = null;

            // 如果有单独的主图，添加到列表开头
            if (dto.getImage() != null && !dto.getImage().isEmpty()) {
                allImages.add(dto.getImage());
            }

            // 如果有多图片，添加到列表中
            if (dto.getImages() != null && !dto.getImages().isEmpty()) {
                allImages.addAll(dto.getImages());
            }

            // 确定主图索引
            int mainIndex = 0; // 默认使用第一张图片作为主图

            // 如果指定了主图索引且有图片上传，则使用指定的索引
            if (dto.getMainImageIndex() != null && !allImages.isEmpty()) {
                // 确保索引在有效范围内
                mainIndex = Math.min(dto.getMainImageIndex(), allImages.size() - 1);
                mainIndex = Math.max(0, mainIndex);
                log.info("使用指定的主图索引: {}", mainIndex);
            }

            log.info("处理图片上传 - 总图片数: {}, 主图索引: {}", allImages.size(), mainIndex);

            // 先上传所有图片，避免重复上传
            if (!allImages.isEmpty()) {
                try {
                    for (int i = 0; i < allImages.size(); i++) {
                        MultipartFile image = allImages.get(i);
                        if (image != null && !image.isEmpty()) {
                            String url = AliyunOSSUtil.uploadLostImage(image);
                            uploadedImageUrls.add(url);
                            log.info("图片{}上传成功，URL: {}", i, url);
                        } else {
                            uploadedImageUrls.add(null); // 保持索引一致
                        }
                    }
                } catch (Exception e) {
                    log.error("图片上传失败: {}", e.getMessage(), e);
                    return Result.fail("图片上传失败，请重试");
                }

                // 获取主图URL
                if (!uploadedImageUrls.isEmpty() && mainIndex < uploadedImageUrls.size()) {
                    imageUrl = uploadedImageUrls.get(mainIndex);
                    log.info("主图URL: {}", imageUrl);
                }
            } else {
                // 如果没有图片，使用默认图片
                imageUrl = "/default/no-image-available.png"; // 默认图片路径
                log.info("用户未上传图片，使用默认图片");
            }

            // 2. 创建并保存失物信息
            LostItem lostItem = new LostItem();
            BeanUtils.copyProperties(dto, lostItem);
            lostItem.setLostTime(dto.getLostTime());
            lostItem.setImageUrl(imageUrl);
            lostItem.setStatus("lost"); // 初始状态
            lostItem.setCreatedAt(LocalDateTime.now());
            lostItem.setAuditStatus(AuditStatusEnum.PENDING);

            // 3. 保存到数据库
            int result = lostItemMapper.insertLostItem(lostItem);
            if (result > 0) {
                // 4. 处理多图片上传（如果有）
                if (!allImages.isEmpty()) {
                    try {
                        // 使用已上传的图片URL列表，而不是重新上传图片
                        List<String> savedImageUrls = itemImageService.saveItemImageUrls(lostItem.getId(), "LOST", uploadedImageUrls);
                        log.info("成功保存{}张图片到item_images表，URLs: {}", uploadedImageUrls.size(), savedImageUrls);

                        // 验证保存的图片数量是否正确
                        if (savedImageUrls.size() != uploadedImageUrls.size()) {
                            log.warn("保存的图片数量({})与上传的图片数量({})不一致", savedImageUrls.size(), uploadedImageUrls.size());
                        }
                    } catch (Exception e) {
                        log.error("保存图片到item_images表失败: {}", e.getMessage(), e);
                        // 不影响主流程，继续执行
                    }
                }

                // 5. 如果审核状态为自动通过，则触发自动匹配
                if (AuditStatusEnum.APPROVED.equals(lostItem.getAuditStatus())) {
                    // 使用异步任务服务处理特征提取和智能匹配
                    asyncTaskService.processItemAutoMatchAsync(lostItem.getId(), lostItem.getUserId(), "LOST");
                }

                return Result.success("发布成功,等待审核", lostItem);
            } else {
                return Result.fail("发布失败，请重试");
            }
        } catch (Exception e) {
            log.error("发布失物信息失败: {}", e.getMessage(), e);
            return Result.fail("发布失败，请稍后重试");
        }
    }


    @Override
    public Result<Map<String, Object>> searchLostItems(String keyword, String lostLocation, String status, String timeRange,
                                                       String timeFilterType, String startDate, String endDate, int page, int size) {
        // 获取时间范围的筛选条件
        LocalDateTime[] dateRange = getDateRange(timeRange, startDate, endDate);
        LocalDateTime startDateTime = dateRange[0];
        LocalDateTime endDateTime = dateRange[1];

        // 使用 PageHelper 设置分页
        PageHelper.startPage(page, size); // 自动处理 limit 和 offset
        log.info("page: {}, size: {}", page, size);  // 打印 page 和 size 参数
        // 执行数据库查询
        List<LostItem> lostItemsList = lostItemMapper.selectLostItems(keyword, lostLocation, status, startDateTime, endDateTime, timeFilterType);
        // 包装分页信息
        PageInfo<LostItem> pageInfo = new PageInfo<>(lostItemsList);
        log.info("查询总数: {}, 总页数: {}, 当前页: {}, 每页大小: {}", pageInfo.getTotal(), pageInfo.getPages(), page, size);
        // 如果没有查询到失物，返回提示
        if (lostItemsList == null || lostItemsList.isEmpty()) {
            return Result.fail("没有符合条件的丢失物品。您可以扩大查询范围或稍后再试。");
        }
        return Result.success(lostItemsList,pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public LostItemDetailsDTO getLostItemDetails(Long id) {
        // 查询失物详情和发布人信息
        LostItemDetailsDTO lostItemDetails = lostItemMapper.selectLostItemDetailsById(id);

        // 如果没有找到该失物信息，返回 null
        if (lostItemDetails == null) {
            return null;
        }

        // 获取物品的所有图片
        try {
            List<ItemImage> itemImages = itemImageService.getItemImages(id, "LOST");
            lostItemDetails.setItemImages(itemImages);

            // 构建所有图片URL列表
            List<String> allImageUrls = new ArrayList<>();
            String mainImageUrl = lostItemDetails.getImageUrl();

            // 从item_images表获取所有图片URL
            if (itemImages != null && !itemImages.isEmpty()) {
                // 按照sortOrder排序
                itemImages.sort(Comparator.comparing(ItemImage::getSortOrder));

                // 将所有图片URL添加到列表中
                for (ItemImage image : itemImages) {
                    String imageUrl = image.getImageUrl();
                    if (imageUrl != null && !imageUrl.isEmpty()) {
                        allImageUrls.add(imageUrl);
                    }
                }
            } else if (mainImageUrl != null && !mainImageUrl.isEmpty()) {
                // 如果没有额外图片，但有主图，则添加主图
                allImageUrls.add(mainImageUrl);
            }

            log.info("构建图片URL列表 - 主图: {}, 总图片数: {}", mainImageUrl, allImageUrls.size());
            lostItemDetails.setImageUrls(allImageUrls);
        } catch (Exception e) {
            log.error("获取失物图片失败: {}", e.getMessage(), e);
            // 不影响主流程，继续返回基本信息
        }

        return lostItemDetails;
    }

    @Override
    public LostItem getLostItemById(Long id) {
        return lostItemMapper.selectById(id);
    }

    /**
     * 根据时间范围（今天、昨天、三天内、一周内、一个月内）或自定义时间，获取起始和结束时间
     */
    private LocalDateTime[] getDateRange(String timeRange, String startDate, String endDate) {
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = LocalDateTime.now();

        // 根据用户选择的时间范围处理日期
        if (timeRange != null) {
            switch (timeRange) {
                case "today":
                    startDateTime = LocalDateTime.now().toLocalDate().atStartOfDay();
                    break;
                case "yesterday":
                    startDateTime = LocalDateTime.now().minusDays(1).toLocalDate().atStartOfDay(); // 2025-04-06 00:00
                    endDateTime = startDateTime.plusDays(1).minusNanos(1); // 2025-04-06 23:59:59.999999999
                    break;
                case "lastThreeDays":
                    startDateTime = endDateTime.minusDays(3).toLocalDate().atStartOfDay(); // 3天前的00:00
                    endDateTime = endDateTime.minusNanos(1); // 当前时间的23:59:59.999999999
                    break;
                case "lastWeek":
                    startDateTime = endDateTime.minusWeeks(1).toLocalDate().atStartOfDay(); // 一周前的00:00
                    endDateTime = endDateTime.minusNanos(1); // 当前时间的23:59:59.999999999
                    break;
                case "lastMonth":
                    startDateTime = endDateTime.minusMonths(1).toLocalDate().atStartOfDay(); // 一个月前的00:00
                    endDateTime = endDateTime.minusNanos(1); // 当前时间的23:59:59.999999999
                    break;
                default:
                    // 自定义时间段
                    if (startDate != null && !startDate.isEmpty()) {
                        startDateTime = LocalDateTime.parse(startDate);
                    }
                    if (endDate != null && !endDate.isEmpty()) {
                        endDateTime = LocalDateTime.parse(endDate);
                    }
                    break;
            }
        }

        return new LocalDateTime[]{startDateTime, endDateTime};
    }


    public Result<Object> updateLostItem(Long id, LostItemDTO lostItemUpdateDTO) {
        // 获取当前登录用户的ID
        Long userId = securityUtil.getCurrentUserId();
        if (userId == null) {
            return Result.fail("用户未登录");
        }

        // 获取失物信息
        LostItem lostItem = lostItemMapper.selectById(id);
        if (lostItem == null) {
            return Result.fail("失物信息不存在");
        }

        // 检查当前用户是否是该失物信息的发布者
        if (!lostItem.getUserId().equals(userId)) {
            return Result.fail("无权限修改该失物信息");
        }

        // 1. 统一处理图片上传 - 将所有图片合并到一个列表中处理
        List<MultipartFile> allImages = new ArrayList<>();
        List<String> uploadedImageUrls = new ArrayList<>(); // 存储所有上传后的图片URL
        String imageUrl = lostItem.getImageUrl();  // 默认使用原始图片URL

        // 如果有单独的主图，添加到列表开头
        if (lostItemUpdateDTO.getImage() != null && !lostItemUpdateDTO.getImage().isEmpty()) {
            allImages.add(lostItemUpdateDTO.getImage());
        }

        // 如果有多图片，添加到列表中
        if (lostItemUpdateDTO.getImages() != null && !lostItemUpdateDTO.getImages().isEmpty()) {
            allImages.addAll(lostItemUpdateDTO.getImages());
        }

        if (!allImages.isEmpty()) {
            // 确定主图索引
            int mainIndex = 0; // 默认使用第一张图片作为主图

            // 如果指定了主图索引且有图片上传，则使用指定的索引
            if (lostItemUpdateDTO.getMainImageIndex() != null && !allImages.isEmpty()) {
                // 确保索引在有效范围内
                mainIndex = Math.min(lostItemUpdateDTO.getMainImageIndex(), allImages.size() - 1);
                mainIndex = Math.max(0, mainIndex);
                log.info("使用指定的主图索引: {}", mainIndex);
            }

            log.info("处理图片上传 - 总图片数: {}, 主图索引: {}", allImages.size(), mainIndex);

            // 先上传所有图片，避免重复上传
            try {
                for (int i = 0; i < allImages.size(); i++) {
                    MultipartFile image = allImages.get(i);
                    if (image != null && !image.isEmpty()) {
                        String url = AliyunOSSUtil.uploadLostImage(image);
                        uploadedImageUrls.add(url);
                        log.info("图片{}上传成功，URL: {}", i, url);
                    } else {
                        uploadedImageUrls.add(null); // 保持索引一致
                    }
                }
            } catch (Exception e) {
                log.error("图片上传失败: {}", e.getMessage(), e);
                return Result.fail("图片上传失败，请重试");
            }

            // 获取主图URL
            if (!uploadedImageUrls.isEmpty() && mainIndex < uploadedImageUrls.size()) {
                imageUrl = uploadedImageUrls.get(mainIndex);
                log.info("主图URL: {}", imageUrl);
            }
        }

        // 2. 更新失物信息，只有修改过的字段才会更新
        if (lostItemUpdateDTO.getItemName() != null && !lostItemUpdateDTO.getItemName().equals(lostItem.getItemName())) {
            lostItem.setItemName(lostItemUpdateDTO.getItemName());
        }

        if (lostItemUpdateDTO.getLostLocation() != null && !lostItemUpdateDTO.getLostLocation().equals(lostItem.getLostLocation())) {
            lostItem.setLostLocation(lostItemUpdateDTO.getLostLocation());
        }

        if (lostItemUpdateDTO.getLostTime() != null && !lostItemUpdateDTO.getLostTime().equals(lostItem.getLostTime())) {
            lostItem.setLostTime(lostItemUpdateDTO.getLostTime());
        }

        // 更新描述信息
        if (lostItemUpdateDTO.getDescription() != null) {
            // 由于描述可能包含大量文本，不进行相等性比较，直接更新
            lostItem.setDescription(lostItemUpdateDTO.getDescription());
            log.info("更新描述信息");
        }

        // 如果有新图片上传，删除旧图片
        if (imageUrl != null && !imageUrl.equals(lostItem.getImageUrl()) && lostItem.getImageUrl() != null) {
            try {
                String oldObjectName = AliyunOSSUtil.extractObjectNameFromUrl(lostItem.getImageUrl());
                if (oldObjectName != null) {
                    boolean deleted = AliyunOSSUtil.deleteObject(oldObjectName);
                    log.info("删除旧图片 {}: {}", oldObjectName, deleted ? "成功" : "失败");
                }
            } catch (Exception e) {
                log.error("删除旧图片失败: {}", e.getMessage(), e);
                // 不影响主流程，继续执行
            }
            lostItem.setImageUrl(imageUrl);
        }

        // 3. 更新失物信息
        int result = lostItemMapper.updateById(lostItem);
        if (result == 1) {
            // 4. 处理多图片上传（如果有）
            if (lostItemUpdateDTO.getImages() != null && !lostItemUpdateDTO.getImages().isEmpty()) {
                try {
                    // 获取原有的额外图片，并从OSS中删除
                    List<ItemImage> oldImages = itemImageService.getItemImages(id, "LOST");
                    for (ItemImage oldImage : oldImages) {
                        try {
                            if (oldImage.getImageUrl() != null && !oldImage.getImageUrl().isEmpty()) {
                                String oldObjectName = AliyunOSSUtil.extractObjectNameFromUrl(oldImage.getImageUrl());
                                if (oldObjectName != null) {
                                    boolean deleted = AliyunOSSUtil.deleteObject(oldObjectName);
                                    log.info("删除旧额外图片 {}: {}", oldObjectName, deleted ? "成功" : "失败");
                                }
                            }
                        } catch (Exception e) {
                            log.error("删除旧额外图片失败: {}", e.getMessage(), e);
                            // 继续处理其他图片
                        }
                    }

                    // 从数据库中删除原有的额外图片记录
                    itemImageService.deleteItemImages(id, "LOST");

                    // 使用已上传的图片URL列表，而不是重新上传图片
                    if (!uploadedImageUrls.isEmpty()) {
                        List<String> savedImageUrls = itemImageService.saveItemImageUrls(lostItem.getId(), "LOST", uploadedImageUrls);
                        log.info("成功更新{}张图片到item_images表，URLs: {}", uploadedImageUrls.size(), savedImageUrls);

                        // 验证保存的图片数量是否正确
                        if (savedImageUrls.size() != uploadedImageUrls.size()) {
                            log.warn("保存的图片数量({})与上传的图片数量({})不一致", savedImageUrls.size(), uploadedImageUrls.size());
                        }
                    }
                } catch (Exception e) {
                    log.error("更新额外图片失败: {}", e.getMessage(), e);
                    // 不影响主流程，继续执行
                }
            } else if (lostItemUpdateDTO.isKeepExistingImages()) {
                // 如果没有新上传的图片，但是设置了保留原有图片
                log.info("保留原有图片，不进行图片更新操作");

                // 如果主图索引变化了，需要更新主图
                if (lostItemUpdateDTO.isMainImageChanged()) {
                    log.info("主图索引变化，需要更新主图");

                    // 获取所有图片
                    List<ItemImage> itemImages = itemImageService.getItemImages(id, "LOST");

                    // 检查主图索引是否有效
                    if (lostItemUpdateDTO.getMainImageIndex() != null &&
                        lostItemUpdateDTO.getMainImageIndex() >= 0 &&
                        lostItemUpdateDTO.getMainImageIndex() < itemImages.size()) {

                        // 获取新的主图URL
                        String newMainImageUrl = itemImages.get(lostItemUpdateDTO.getMainImageIndex()).getImageUrl();

                        // 更新主图URL
                        lostItem.setImageUrl(newMainImageUrl);
                        lostItemMapper.updateById(lostItem);

                        log.info("成功更新主图URL: {}", newMainImageUrl);
                    } else {
                        log.warn("主图索引无效: {}, 图片数量: {}", lostItemUpdateDTO.getMainImageIndex(), itemImages.size());
                    }
                }
            }

            // 修改返回消息格式，与publishLostItem保持一致
            return Result.success("更新成功,等待审核", lostItem);
        } else {
            return Result.fail("更新失败，请重试");
        }
    }

    @Override
    public Result<Object> deleteLostItem(Long id, Long userId) {
        try {
            // 查询失物信息
            LostItem lostItem = lostItemMapper.selectById(id);
            if (lostItem == null) {
                return Result.fail("失物信息不存在");
            }

            // 检查是否是发布者本人
            if (!lostItem.getUserId().equals(userId)) {
                return Result.fail("您无权删除此失物信息");
            }

            // 收集所有需要删除的图片URL
            List<String> imageUrls = new ArrayList<>();

            // 添加主图片URL
            if (lostItem.getImageUrl() != null && !lostItem.getImageUrl().isEmpty()) {
                imageUrls.add(lostItem.getImageUrl());
            }

            // 获取并添加额外图片URL
            List<ItemImage> itemImages = itemImageService.getItemImages(id, "LOST");
            for (ItemImage image : itemImages) {
                if (image.getImageUrl() != null && !image.getImageUrl().isEmpty()) {
                    imageUrls.add(image.getImageUrl());
                }
            }

            // 从OSS删除图片文件
            for (String url : imageUrls) {
                try {
                    String objectName = AliyunOSSUtil.extractObjectNameFromUrl(url);
                    if (objectName != null) {
                        boolean deleted = AliyunOSSUtil.deleteObject(objectName);
                        log.info("删除OSS图片 {}: {}", objectName, deleted ? "成功" : "失败");
                    }
                } catch (Exception e) {
                    log.error("删除OSS图片失败: {}", e.getMessage(), e);
                    // 继续处理其他图片，不影响主流程
                }
            }

            // 删除数据库中的图片记录
            try {
                itemImageService.deleteItemImages(id, "LOST");
                log.info("已删除失物ID={}的相关图片记录", id);
            } catch (Exception e) {
                log.error("删除失物图片记录失败: {}", e.getMessage(), e);
                // 不影响主流程，继续执行
            }

            // 执行删除操作
            int result = lostItemMapper.deleteLostItem(id);
            if (result > 0) {
                return Result.success("删除成功");
            } else {
                return Result.fail("删除失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("删除失物信息失败: {}", e.getMessage(), e);
            return Result.fail("删除失败，请稍后重试");
        }
    }

    @Override
    public Result<Object> getMyPublishedItems(Long userId) {
        try {
            // 查询该用户发布的所有失物信息
            List<LostItem> lostItems = lostItemMapper.findLostItemsByUserId(userId);
            return Result.success("查询成功", lostItems);
        } catch (Exception e) {
            log.error("获取发布的失物信息失败: {}", e.getMessage(), e);
            return Result.fail("获取失败，请稍后重试");
        }
    }

    @Override
    public Result<Object> updateLostItemStatus(Long id, String status, Long userId) {
        try {
            // 1. 查询失物信息
            LostItem lostItem = lostItemMapper.selectById(id);
            if (lostItem == null) {
                return Result.error("失物信息不存在");
            }

            // 2. 验证用户是否有权限更新该失物信息
            if (!lostItem.getUserId().equals(userId)) {
                return Result.error("您无权更新此失物信息");
            }

            // 3. 检查审核状态
            if (!AuditStatusEnum.APPROVED.equals(lostItem.getAuditStatus())) {
                return Result.error("该物品尚未通过审核，无法更新状态");
            }

            // 4. 校验状态变更是否合法 - 只允许从未找回(LOST)变为已找回(FOUND)
            if (!"FOUND".equals(status)) {
                return Result.error("无效的状态值，只能标记为已找回");
            }

            if (!"LOST".equals(lostItem.getStatus())) {
                return Result.error("该物品当前状态不是未找回，无法更新");
            }

            // 5. 更新失物状态
            int result = lostItemMapper.updateLostItemStatus(id, status);

            if (result > 0) {
                return Result.success("物品已标记为找回");
            } else {
                return Result.error("状态更新失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("更新失物状态失败: {}", e.getMessage(), e);
            return Result.error("更新失败，请稍后重试");
        }
    }

}
