/**
 * WebSocket 诊断工具
 *
 * 该模块提供了一系列函数，用于诊断 WebSocket 连接和消息传递问题。
 */

import { getWebSocketClient } from './websocket';
import { ElMessage } from 'element-plus';

/**
 * 检查 WebSocket 连接状态
 * @returns {Object} 连接状态信息
 */
export function checkWebSocketConnection() {
  const wsClient = getWebSocketClient();
  const connected = wsClient?.connected || false;

  console.group('WebSocket 连接状态检查');
  console.log('WebSocket 连接状态:', connected ? '已连接' : '未连接');

  if (wsClient) {
    console.log('WebSocket 客户端详情:', {
      connected: wsClient.connected,
      _transport: wsClient._transport ? {
        url: wsClient._transport.url,
        transport: wsClient._transport.transport
      } : '无传输层信息'
    });
  } else {
    console.log('WebSocket 客户端未初始化');
  }

  console.groupEnd();

  return {
    connected,
    client: wsClient ? {
      connected: wsClient.connected,
      transport: wsClient._transport?.transport
    } : null
  };
}

/**
 * 检查 WebSocket 订阅状态
 * @returns {Object} 订阅状态信息
 */
export function checkWebSocketSubscriptions() {
  const wsClient = getWebSocketClient();

  console.group('WebSocket 订阅状态检查');

  if (!wsClient) {
    console.log('WebSocket 客户端未初始化，无法检查订阅');
    console.groupEnd();
    return { subscriptions: [] };
  }

  // 获取所有活跃的订阅
  let subscriptions = [];

  // 首先尝试从activeSubscriptions属性获取
  if (wsClient.activeSubscriptions && Array.isArray(wsClient.activeSubscriptions)) {
    subscriptions = wsClient.activeSubscriptions;
    console.log('从activeSubscriptions属性获取到订阅:', subscriptions);
  }
  // 如果有getActiveSubscriptions方法，则使用该方法
  else if (typeof wsClient.getActiveSubscriptions === 'function') {
    subscriptions = wsClient.getActiveSubscriptions();
    console.log('从getActiveSubscriptions方法获取到订阅:', subscriptions);
  }
  // 最后尝试从_stompHandler获取
  else if (wsClient._stompHandler && wsClient._stompHandler.subscriptions) {
    for (const subId in wsClient._stompHandler.subscriptions) {
      const sub = wsClient._stompHandler.subscriptions[subId];
      subscriptions.push({
        id: subId,
        destination: sub.destination
      });
    }
    console.log('从_stompHandler获取到订阅:', subscriptions);
  }
  // 如果是stompClient，尝试从它获取
  else if (wsClient.stompClient && wsClient.stompClient.connected) {
    // 尝试从stompClient获取订阅
    if (wsClient.stompClient._stompHandler && wsClient.stompClient._stompHandler.subscriptions) {
      for (const subId in wsClient.stompClient._stompHandler.subscriptions) {
        const sub = wsClient.stompClient._stompHandler.subscriptions[subId];
        subscriptions.push({
          id: subId,
          destination: sub.destination
        });
      }
      console.log('从stompClient._stompHandler获取到订阅:', subscriptions);
    }
  }

  console.log('活跃订阅数量:', subscriptions.length);
  console.log('订阅详情:', subscriptions);
  console.groupEnd();

  return { subscriptions };
}

/**
 * 发送测试消息
 * @param {string} destination 目标路径
 * @param {Object} message 消息内容
 * @returns {boolean} 是否发送成功
 */
export function sendTestMessage(destination, message) {
  const wsClient = getWebSocketClient();

  if (!wsClient || !wsClient.connected) {
    console.error('WebSocket 未连接，无法发送测试消息');
    ElMessage.error('WebSocket 未连接，无法发送测试消息');
    return false;
  }

  try {
    wsClient.publish({
      destination,
      body: JSON.stringify(message),
      headers: { 'content-type': 'application/json' }
    });

    console.log('测试消息已发送:', {
      destination,
      message
    });

    ElMessage.success('测试消息已发送');
    return true;
  } catch (error) {
    console.error('发送测试消息失败:', error);
    ElMessage.error('发送测试消息失败: ' + error.message);
    return false;
  }
}

/**
 * 诊断 WebSocket 连接问题
 * @returns {Object} 诊断结果
 */
export function diagnoseWebSocketIssues() {
  const connectionStatus = checkWebSocketConnection();
  const subscriptionStatus = checkWebSocketSubscriptions();

  const issues = [];

  if (!connectionStatus.connected) {
    issues.push('WebSocket 未连接');
  }

  if (subscriptionStatus.subscriptions.length === 0 && connectionStatus.connected) {
    issues.push('WebSocket 已连接但没有活跃订阅');
  }

  // 检查是否缺少关键订阅
  const requiredSubscriptions = [
    '/user/queue/private',
    '/topic/messages'
  ];

  const missingSubscriptions = requiredSubscriptions.filter(req =>
    !subscriptionStatus.subscriptions.some(sub => sub.destination.includes(req))
  );

  if (missingSubscriptions.length > 0) {
    issues.push(`缺少关键订阅: ${missingSubscriptions.join(', ')}`);
  }

  return {
    connectionStatus,
    subscriptionStatus,
    issues,
    hasIssues: issues.length > 0
  };
}

export default {
  checkWebSocketConnection,
  checkWebSocketSubscriptions,
  sendTestMessage,
  diagnoseWebSocketIssues
};
