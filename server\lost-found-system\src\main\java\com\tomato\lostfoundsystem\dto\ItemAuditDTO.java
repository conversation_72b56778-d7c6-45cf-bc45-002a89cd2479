package com.tomato.lostfoundsystem.dto;

import com.tomato.lostfoundsystem.enums.AuditStatusEnum;
import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Data
public class ItemAuditDTO {

    // 移除 itemId 字段，因为它已经在路径参数中提供
    // private Long itemId;  // 物品ID

    @NotNull(message = "审核状态不能为空")
    private String auditStatus;  // 审核状态：APPROVED 或 REJECTED

    @Size(max = 500, message = "审核备注不能超过500个字符")
    private String remarks;  // 审核备注

    // 移除 auditorId 字段，因为它已经在请求属性中提供
    // private Long auditorId;  // 审核人ID（管理员ID）

}
