@echo off
echo ===================================
echo 正在启动智能匹配服务...
echo ===================================

:: 直接使用当前目录
set CONFIG_FILE=config.bat

:: 加载配置
call "%CONFIG_FILE%"

:: 检查Python进程是否已经运行
tasklist /FI "IMAGENAME eq python.exe" 2>NUL | find /I "clip_faiss_api.py">NUL
if "%ERRORLEVEL%"=="0" (
    echo 智能匹配服务已经在运行中！
) else (
    :: 启动智能匹配服务
    echo 正在启动智能匹配服务...

    :: 切换到服务目录并激活虚拟环境
    cd /d %CLIP_SERVICE_PATH%

    :: 启动服务
    start "CLIP+FAISS Service" /min cmd /c "call %CLIP_ENV_PATH%\Scripts\activate.bat && python %CLIP_API_SCRIPT%"

    :: 等待服务启动
    echo 等待智能匹配服务启动...
    timeout /t %WAIT_TIME% /nobreak > nul

    :: 检查服务是否成功启动
    tasklist /FI "IMAGENAME eq python.exe" 2>NUL | find /I "python.exe">NUL
    if "%ERRORLEVEL%"=="0" (
        echo 智能匹配服务启动成功！
    ) else (
        echo 智能匹配服务启动失败，请检查配置！
    )
)

echo.
echo 智能匹配服务状态：
tasklist /FI "IMAGENAME eq python.exe"
echo.
echo ===================================

:: 不自动关闭窗口
pause
