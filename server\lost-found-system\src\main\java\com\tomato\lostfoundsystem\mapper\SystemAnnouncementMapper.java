package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.dto.SystemAnnouncementDTO;
import com.tomato.lostfoundsystem.entity.SystemAnnouncement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统公告Mapper接口
 */
@Mapper
public interface SystemAnnouncementMapper {
    /**
     * 插入系统公告
     *
     * @param announcement 公告对象
     * @return 影响的行数
     */
    int insertAnnouncement(SystemAnnouncement announcement);

    /**
     * 更新系统公告
     *
     * @param announcement 公告对象
     * @return 影响的行数
     */
    int updateAnnouncement(SystemAnnouncement announcement);

    /**
     * 删除系统公告
     *
     * @param id 公告ID
     * @return 影响的行数
     */
    int deleteAnnouncement(Long id);

    /**
     * 根据ID查询系统公告
     *
     * @param id 公告ID
     * @return 公告对象
     */
    SystemAnnouncement selectById(Long id);

    /**
     * 查询有效的系统公告
     *
     * @param currentTime 当前时间
     * @return 公告列表
     */
    List<SystemAnnouncement> selectValidAnnouncements(LocalDateTime currentTime);

    /**
     * 查询最新的系统公告
     *
     * @param currentTime 当前时间
     * @return 最新的公告
     */
    SystemAnnouncement selectLatestAnnouncement(LocalDateTime currentTime);

    /**
     * 查询所有系统公告（管理员用）
     *
     * @return 公告列表
     */
    List<SystemAnnouncement> selectAllAnnouncements();

    /**
     * 更新公告状态
     *
     * @param id 公告ID
     * @param status 状态
     * @return 影响的行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 查询有效的系统公告并包含用户已读状态
     *
     * @param currentTime 当前时间
     * @param userId 用户ID
     * @return 公告列表（包含已读状态）
     */
    List<SystemAnnouncementDTO> selectValidAnnouncementsWithReadStatus(@Param("currentTime") LocalDateTime currentTime, @Param("userId") Long userId);
}
