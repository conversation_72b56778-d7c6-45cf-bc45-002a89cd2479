import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import Home from '../views/Home.vue'
import LostItems from '../views/lost/LostItems.vue'
import Profile from '../views/Profile.vue'
import PublishLostItemNew from '../views/lost/PublishLostItemNew.vue'
import FoundItems from '@/views/found/FoundItems.vue'
import PublishFoundItemNew from '@/views/found/PublishFoundItemNew.vue'
import IntelligentMatch from '@/views/match/IntelligentMatch.vue'
import IntelligentMatchDemo from '@/views/match/IntelligentMatchDemo.vue'
import { useUserStore, useAuthStore } from '../stores'
import { parseJwt } from '../utils/jwt'
import { isAdmin, getUserInfo } from '@/utils/auth'
import { jwtDecode } from 'jwt-decode'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/login',
    name: 'Login',
    component: Home,
    beforeEnter: (to, from, next) => {
      // 设置需要登录的标记，App.vue 中会检测并显示登录对话框
      sessionStorage.setItem('needLogin', 'true')
      if (to.query.redirect) {
        sessionStorage.setItem('loginRedirect', to.query.redirect)
      }
      next('/')
    }
  },
  {
    path: '/intelligent-match',
    name: 'IntelligentMatch',
    component: IntelligentMatch,
    meta: {
      title: '智能匹配',
      requiresAuth: true
    }
  },
  {
    path: '/intelligent-match-demo',
    name: 'IntelligentMatchDemo',
    component: IntelligentMatchDemo,
    meta: {
      title: '智能匹配演示',
      requiresAuth: true
    }
  },
  // 登录和注册页面已移除，使用对话框代替
  {
    path: '/chat',
    name: 'Chat',
    component: () => import('@/views/chat/Chat.vue'),
    meta: {
      title: '聊天',
      requiresAuth: true
    }
  },
  {
    path: '/lost-items',
    name: 'LostItems',
    component: () => import('../views/lost/LostItems.vue'),
    meta: {
      requiresAuth: false
    }
  },
  {
    path: '/lost-items/detail/:id',
    name: 'LostItemDetail',
    component: () => import('../views/lost/LostItemDetail.vue'),
    meta: {
      title: '失物详情',
      requiresAuth: false
    }
  },
  {
    path: '/lost-items/edit/:id',
    name: 'EditLostItem',
    component: PublishLostItemNew,
    props: route => ({ isEdit: true, itemId: route.params.id }),
    meta: {
      requiresAuth: true,
      title: '编辑失物信息'
    }
  },
  {
    path: '/lost-items/publish',
    name: 'PublishLostItem',
    component: PublishLostItemNew,
    props: { isEdit: false },
    meta: {
      requiresAuth: true,
      title: '发布失物信息'
    }
  },
  {
    path: '/found-items',
    name: 'FoundItems',
    component: () => import('../views/found/FoundItems.vue'),
    meta: {
      title: '拾物信息',
      requiresAuth: false
    }
  },
  {
    path: '/found-items/detail/:id',
    name: 'FoundItemDetail',
    component: () => import('../views/found/FoundItemDetail.vue'),
    meta: {
      title: '拾物详情',
      requiresAuth: false
    }
  },
  {
    path: '/found-items/edit/:id',
    name: 'EditFoundItem',
    component: PublishFoundItemNew,
    props: route => ({ isEdit: true, itemId: route.params.id }),
    meta: {
      title: '修改拾物信息',
      requiresAuth: true
    }
  },
  {
    path: '/found-items/search',
    name: 'FoundItemsSearch',
    component: FoundItems,
    meta: {
      title: '搜索拾物信息'
    }
  },
  {
    path: '/found-items/publish',
    name: 'PublishFoundItem',
    component: PublishFoundItemNew,
    props: { isEdit: false },
    meta: {
      title: '发布拾物信息',
      requiresAuth: true
    }
  },

  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      requiresAuth: true
    },
    redirect: '/profile/profile',
    children: [
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('../views/profile/UserProfile.vue')
      },
      {
        path: 'my-posts',
        name: 'MyPosts',
        component: () => import('../views/profile/MyPosts.vue')
      },
      {
        path: 'matches',
        name: 'Matches',
        component: () => import('../views/profile/ProfileMatches.vue'),
        meta: { title: '匹配状态' }
      },
      {
        path: 'notifications',
        name: 'Notifications',
        component: () => import('../views/profile/ProfileNotifications.vue'),
        meta: { title: '系统通知' }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('../views/profile/Settings.vue')
      }
    ]
  },
  // 系统公告路由
  {
    path: '/announcements',
    name: 'AnnouncementList',
    component: () => import('@/views/announcement/AnnouncementList.vue'),
    meta: {
      title: '系统公告',
      requiresAuth: true
    }
  },
  // 后台管理路由
  {
    path: '/admin',
    component: () => import('@/layouts/AdminLayout.vue'),
    meta: { requiresAdmin: true },
    children: [
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: () => import('@/views/admin/Dashboard.vue'),
        meta: { title: '管理控制台' }
      },
      {
        path: 'lost-items',
        name: 'AdminLostItems',
        component: () => import('@/views/admin/LostItems.vue'),
        meta: { title: '失物管理' }
      },
      {
        path: 'found-items',
        name: 'AdminFoundItems',
        component: () => import('@/views/admin/FoundItems.vue'),
        meta: { title: '拾物管理' }
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: () => import('@/views/admin/Users.vue'),
        meta: { title: '用户管理' }
      },
      {
        path: 'notifications',
        name: 'AdminNotifications',
        component: () => import('@/views/admin/Notifications.vue'),
        meta: { title: '系统通知管理' }
      },
      {
        path: 'announcements',
        name: 'AdminAnnouncements',
        component: () => import('@/views/admin/Announcements.vue'),
        meta: { title: '系统公告管理' }
      },
      {
        path: 'services',
        name: 'AdminServices',
        component: () => import('@/views/admin/Services.vue'),
        meta: { title: '服务管理' }
      },
      {
        path: 'config',
        name: 'AdminSystemConfig',
        component: () => import('@/views/admin/SystemConfig.vue'),
        meta: { title: '系统配置' }
      },
      {
        path: 'statistics',
        name: 'AdminStatistics',
        component: () => import('@/views/admin/Statistics.vue'),
        meta: { title: '统计数据' }
      },
      {
        path: 'match-statistics',
        name: 'AdminMatchStatistics',
        component: () => import('@/views/admin/MatchStatistics.vue'),
        meta: { title: '匹配统计' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 检查token是否过期
const isTokenExpired = (token) => {
  try {
    const tokenData = parseJwt(token)
    if (!tokenData.exp) return true

    // exp是UTC时间戳（秒）
    const expireTime = tokenData.exp * 1000 // 转换为毫秒
    const currentTime = Date.now()

    console.log('Token状态:', {
      过期时间: new Date(expireTime).toLocaleString(),
      当前时间: new Date(currentTime).toLocaleString(),
      是否过期: currentTime > expireTime
    })

    return currentTime > expireTime
  } catch (error) {
    console.error('解析token失败:', error)
    return true
  }
}

router.beforeEach(async (to, from, next) => {
  console.log('路由守卫触发:', {
    to: to.path,
    from: from.path
  })

  const userStore = useUserStore()
  const token = localStorage.getItem('token')
  const userInfo = getUserInfo()

  // 检查token是否过期
  if (token) {
    try {
      const decoded = jwtDecode(token)
      if (decoded.exp * 1000 < Date.now()) {
        // token过期，清除用户信息
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        userStore.clearUser()

        // 如果访问需要认证的页面，显示登录对话框
        if (to.meta.requiresAuth) {
          // 这里不直接显示登录对话框，而是在组件挂载后显示
          // 将需要登录的信息保存到会话存储中
          sessionStorage.setItem('needLogin', 'true')
          sessionStorage.setItem('loginRedirect', to.fullPath)
        }
      }
    } catch (error) {
      console.error('Token解析失败:', error)
      userStore.clearUser()
    }
  }

  // 如果路由需要认证且用户未登录
  if (to.meta.requiresAuth && (!token || !userInfo)) {
    console.log('需要认证的路由，但用户未登录:', to.path)
    // 将需要登录的信息保存到会话存储中
    sessionStorage.setItem('needLogin', 'true')
    sessionStorage.setItem('loginRedirect', to.fullPath)

    // 获取认证存储
    const authStore = useAuthStore()

    // 显示登录对话框
    authStore.showLoginDialog({
      tab: 'login',
      onSuccess: () => {
        // 登录成功后，重定向到原始目标路由
        router.push(to.fullPath)
      }
    })

    // 重定向到首页
    next('/')
    return
  }

  // 需要管理员权限的路由
  if (to.meta.requiresAdmin) {
    if (!token || !userInfo || !Array.isArray(userInfo.roles)) {
      ElMessage.error('您没有权限访问该页面')
      next('/')
      return
    }

    if (!userInfo.roles.some(role => ['ADMIN', 'SUPER_ADMIN'].includes(role))) {
      ElMessage.error('您没有权限访问该页面')
      next('/')
      return
    }
  }

  // 需要超级管理员权限的路由
  if (to.meta.requiresSuperAdmin) {
    if (!token || !userInfo || !Array.isArray(userInfo.roles)) {
      ElMessage.error('您没有权限访问该页面')
      next('/')
      return
    }

    if (!userInfo.roles.includes('SUPER_ADMIN')) {
      ElMessage.error('您没有权限访问该页面')
      next('/')
      return
    }
  }

  console.log('允许路由跳转:', to.path)
  next()
})

export default router