package com.tomato.lostfoundsystem.dto;

import com.tomato.lostfoundsystem.entity.ItemImage;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


@Data
public class FoundItemDetailDTO {
    private Long id;
    private Long userId;
    private String itemName;
    private String description;
    private LocalDateTime foundTime;
    private String foundLocation;
    private String imageUrl;  // 主图片URL
    private List<String> imageUrls; // 所有图片URL列表
    private String status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt; // 更新时间
    private String username;  // 发布人的用户名
    private String auditStatus; // 审核状态
    private String auditStatusDescription; // 审核状态描述
    private List<ItemImage> itemImages; // 物品图片列表

}
