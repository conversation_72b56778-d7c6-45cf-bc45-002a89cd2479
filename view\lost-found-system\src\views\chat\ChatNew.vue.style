</script>

<style scoped>
.chat-container {
  display: flex;
  height: calc(100vh - 60px);
  background-color: #f0f2f5;
  overflow: hidden;
}

.chat-sidebar {
  width: 320px;
  min-width: 280px;
  max-width: 420px;
  height: 100%;
  border-right: 1px solid #e9edef;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  resize: horizontal;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #efeae2;
  background-image: linear-gradient(rgba(0, 0, 0, 0.02) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
  background-size: 20px 20px;
  position: relative;
  overflow: hidden;
}

.chat-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f2f5;
}

.placeholder-content {
  text-align: center;
  padding: 20px;
  max-width: 400px;
}

.placeholder-image {
  width: 200px;
  height: 200px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.placeholder-content h3 {
  font-size: 20px;
  font-weight: 600;
  color: #41464b;
  margin-bottom: 10px;
}

.placeholder-content p {
  font-size: 14px;
  color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-sidebar {
    width: 100%;
    max-width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .chat-sidebar.active {
    transform: translateX(0);
  }
  
  .chat-main {
    width: 100%;
  }
  
  .placeholder-image {
    width: 150px;
    height: 150px;
  }
}
</style>
