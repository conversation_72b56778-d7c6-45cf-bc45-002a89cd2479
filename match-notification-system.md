## 基于WebSocket的实时匹配结果推送机制

在校园失物招领系统中，匹配结果的及时推送对于提高物品找回率至关重要。本系统实现了基于WebSocket的实时通知机制，确保用户能够第一时间获知潜在的匹配结果，从而加速失物与招领物品的对接过程。

### 1. 系统架构

匹配结果推送机制采用了基于STOMP协议的WebSocket实现，主要由以下组件构成：

- **服务端推送模块**：负责筛选高相似度匹配结果并通过WebSocket推送通知
- **客户端接收模块**：负责接收、解析和展示匹配通知
- **通知持久化模块**：确保通知的可靠存储和状态管理

![匹配结果推送机制架构图](https://i.imgur.com/Yk2Lfqm.png)

### 2. 核心实现

#### 2.1 高相似度匹配处理

系统只推送相似度高于阈值（默认0.7）的匹配结果，避免低质量匹配对用户造成干扰。核心代码如下：

```java
@Override
@Transactional
public Result processHighSimilarityMatches(List<Map<String, Object>> matchResults, 
                                          Long matchHistoryId,
                                          Long userId, 
                                          Float similarityThreshold) {
    try {
        log.info("【智能匹配】处理高相似度匹配结果，用户ID: {}, 匹配历史ID: {}, 阈值: {}", 
                userId, matchHistoryId, similarityThreshold);

        // 如果未指定阈值，使用默认阈值
        if (similarityThreshold == null) {
            similarityThreshold = defaultSimilarityThreshold;
            log.info("【智能匹配】使用默认相似度阈值: {}", similarityThreshold);
        }

        // 找出相似度最高的结果
        Map<String, Object> highestSimilarityResult = null;
        Float highestSimilarity = 0f;

        for (Map<String, Object> result : matchResults) {
            Float similarity = (Float) result.get("similarity");

            // 只考虑高于阈值的结果
            if (similarity >= similarityThreshold) {
                // 如果当前结果的相似度高于之前找到的最高相似度，则更新
                if (highestSimilarityResult == null || similarity > highestSimilarity) {
                    highestSimilarityResult = result;
                    highestSimilarity = similarity;
                }
            }
        }

        // 如果找到了高相似度的结果，则发送通知
        if (highestSimilarityResult != null) {
            Long itemId = Long.valueOf(highestSimilarityResult.get("id").toString());
            String itemType = (String) highestSimilarityResult.get("itemType");
            Float similarity = (Float) highestSimilarityResult.get("similarity");

            // 创建通知
            Result notificationResult = createMatchNotification(userId, matchHistoryId, 
                                                              itemId, itemType,
                                                              similarity, highestSimilarityResult);
            
            return Result.success("处理完成，发送了1条最高相似度通知");
        } else {
            return Result.success("没有找到高于阈值的匹配结果，不发送通知");
        }
    } catch (Exception e) {
        log.error("处理高相似度匹配结果失败", e);
        return Result.fail("处理失败: " + e.getMessage());
    }
}
```

#### 2.2 WebSocket消息推送

通知创建后，系统通过WebSocket实时推送给用户：

```java
// 通过WebSocket推送通知
Map<String, Object> wsMessage = new HashMap<>();
wsMessage.put("type", "MATCH_NOTIFICATION");
wsMessage.put("notificationId", notification.getId());
wsMessage.put("title", notification.getTitle());
wsMessage.put("content", notification.getContent());
wsMessage.put("itemId", itemId);
wsMessage.put("itemType", itemType);
wsMessage.put("similarity", similarity);
wsMessage.put("time", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

// 添加匹配详情到WebSocket消息
if (itemInfo.containsKey("match_details")) {
    wsMessage.put("match_details", itemInfo.get("match_details"));
}

webSocketService.sendMessage(userId, wsMessage);
```

#### 2.3 前端接收与处理

前端通过订阅个人通知队列接收匹配通知：

```javascript
// 订阅用户通知
const notificationSub = this.notificationClient.subscribe(
  `/user/queue/notifications`,
  (message) => {
    try {
      console.log('【通知调试】收到通知消息:', message.body)
      const notification = JSON.parse(message.body)

      // 触发全局事件，确保通知能被处理
      window.dispatchEvent(new CustomEvent('websocket-notification', {
        detail: notification
      }))

      // 触发通知计数刷新
      window.dispatchEvent(new CustomEvent('refresh-notification-count'));
    } catch (error) {
      console.error('【通知调试】解析通知消息失败:', error)
    }
  }
)
```

### 3. 多种匹配触发机制

系统实现了多种匹配触发机制，确保用户能够在不同场景下及时发现相似物品：

- **物品发布触发**：用户发布物品后自动与现有物品进行匹配
- **审核通过触发**：管理员审核通过物品后触发匹配
- **主动搜索触发**：用户主动搜索时可查看匹配结果（不触发通知）

### 4. 结论

基于WebSocket的实时匹配结果推送机制显著提高了系统的用户体验和物品找回效率。通过设置合理的相似度阈值，系统能够在保证匹配质量的同时，及时将有价值的匹配结果推送给用户，实现了失物与招领物品的高效对接。
