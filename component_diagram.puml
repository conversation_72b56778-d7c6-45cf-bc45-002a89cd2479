@startuml 失物招领系统组件图

!define RECTANGLE component
!define DATABASE database
!define CLOUD cloud
!define QUEUE queue

skinparam backgroundColor white
skinparam componentStyle uml2
skinparam defaultTextAlignment center
skinparam monochrome false
skinparam shadowing false

title 校园失物招领系统组件图

' 前端组件
package "前端应用" {
    [用户界面] as UI
    [路由管理] as Router
    [状态管理] as State
    [API客户端] as APIClient
    [表单验证] as Validation
}

' 后端组件
package "后端服务" {
    [控制器层] as Controller
    [服务层] as Service
    [数据访问层] as DAO
    [安全过滤器] as SecurityFilter
    [异常处理] as ExceptionHandler
    [WebSocket处理器] as WebSocketHandler
}

' 业务模块
package "业务模块" {
    [用户管理] as UserModule
    [失物管理] as LostItemModule
    [招领管理] as FoundItemModule
    [智能匹配] as MatchModule
    [即时通讯] as ChatModule
    [系统公告] as AnnouncementModule
}

' 外部服务
cloud "阿里云OSS" as OSS
cloud "CLIP+FAISS服务" as CLIP
database "MySQL" as MySQL
database "Redis" as Redis
queue "Kafka" as Kafka

' 连接关系
UI --> Router
Router --> State
State --> APIClient
UI --> Validation
APIClient --> Controller

Controller --> SecurityFilter
SecurityFilter --> Service
Service --> DAO
Service --> WebSocketHandler
Controller --> ExceptionHandler
WebSocketHandler --> Kafka

DAO --> MySQL
Service --> Redis
WebSocketHandler --> Redis

UserModule --> Service
LostItemModule --> Service
FoundItemModule --> Service
MatchModule --> Service
ChatModule --> Service
AnnouncementModule --> Service

Service --> OSS : 文件上传/下载
Service --> CLIP : 特征提取/匹配
Service --> Kafka : 消息发布

' 接口定义
interface "RESTful API" as RestAPI
interface "WebSocket API" as WsAPI
interface "数据库接口" as DBAPI
interface "缓存接口" as CacheAPI
interface "文件存储接口" as FileAPI
interface "智能匹配接口" as MatchAPI

Controller -up- RestAPI
WebSocketHandler -up- WsAPI
DAO -down- DBAPI
Service -down- CacheAPI
Service -down- FileAPI
Service -down- MatchAPI

APIClient -down-> RestAPI
APIClient -down-> WsAPI
MySQL -up- DBAPI
Redis -up- CacheAPI
OSS -up- FileAPI
CLIP -up- MatchAPI

@enduml
