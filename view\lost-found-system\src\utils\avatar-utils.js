/**
 * 头像工具函数
 * 提供用于生成和管理用户头像的工具函数
 */

// 可用的深色背景色列表
const darkColors = [
  '#1976D2', // 深蓝色
  '#388E3C', // 深绿色
  '#7B1FA2', // 深紫色
  '#455A64', // 蓝灰色
  '#5D4037', // 深棕色
  '#0288D1', // 亮蓝色
  '#303F9F', // 靛蓝色
  '#00796B', // 深青色
  '#C2185B', // 深粉色
  '#512DA8', // 深紫罗兰色
  '#D32F2F', // 深红色
  '#F57C00', // 深橙色
  '#0097A7', // 青色
  '#689F38', // 酸橙色
  '#616161'  // 深灰色
]

// 生成随机深色背景色
export const getRandomDarkColor = () => {
  return darkColors[Math.floor(Math.random() * darkColors.length)]
}

/**
 * 生成随机深色背景色，但确保与指定颜色不同
 * @param {string} excludeColor - 要排除的颜色
 * @returns {string} - 随机颜色代码
 */
export const getRandomDarkColorExcept = (excludeColor) => {
  if (!excludeColor) return getRandomDarkColor()

  // 过滤掉要排除的颜色
  const availableColors = darkColors.filter(color => color.toLowerCase() !== excludeColor.toLowerCase())

  // 如果过滤后没有颜色可用（极少情况），返回一个默认颜色
  if (availableColors.length === 0) return '#795548' // 棕色，作为备选

  // 从可用颜色中随机选择
  return availableColors[Math.floor(Math.random() * availableColors.length)]
}

/**
 * 为联系人生成一致的背景色（基于联系人ID）
 * @param {string|number} contactId - 联系人ID
 * @param {string} currentUserColor - 当前用户的颜色（可选，如果提供则确保生成的颜色与之不同）
 * @returns {string} - 颜色代码
 */
export const getContactColor = (contactId, currentUserColor) => {
  if (!contactId) return getRandomDarkColor()

  // 将contactId转换为字符串
  const idStr = String(contactId)

  // 使用联系人ID的哈希值来选择颜色，确保同一联系人始终获得相同颜色
  let hash = 0
  for (let i = 0; i < idStr.length; i++) {
    hash = idStr.charCodeAt(i) + ((hash << 5) - hash)
  }

  // 使用哈希值选择颜色
  const index = Math.abs(hash) % darkColors.length
  const color = darkColors[index]

  // 如果提供了当前用户颜色，确保生成的颜色与之不同
  if (currentUserColor && color.toLowerCase() === currentUserColor.toLowerCase()) {
    return getRandomDarkColorExcept(currentUserColor)
  }

  return color
}

/**
 * 为用户生成一致的背景色（基于用户名）
 * @param {string} username - 用户名
 * @returns {string} - 颜色代码
 */
export const getUserColor = (username) => {
  if (!username) return getRandomDarkColor()

  // 使用用户名的哈希值来选择颜色，确保同一用户始终获得相同颜色
  let hash = 0
  for (let i = 0; i < username.length; i++) {
    hash = username.charCodeAt(i) + ((hash << 5) - hash)
  }

  // 使用哈希值选择颜色
  const index = Math.abs(hash) % darkColors.length
  return darkColors[index]
}

/**
 * 根据联系人ID生成头像背景颜色
 * @param {number|string} contactId - 联系人ID
 * @returns {string} - 颜色代码
 */
export const getContactAvatarColor = (contactId) => {
  return getContactColor(contactId)
}
