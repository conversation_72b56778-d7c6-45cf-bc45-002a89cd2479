<template>
  <div class="home">
    <!-- 系统公告横幅 -->
    <AnnouncementBanner />

    <!-- 顶部横幅 -->
    <div class="banner">
      <el-carousel height="400px" indicator-position="outside" :interval="5000">
        <el-carousel-item v-for="(item, index) in bannerItems" :key="index">
          <div class="banner-content">
            <el-image
              :src="item.image"
              fit="cover"
              class="banner-image"
              @load="() => console.log('图片加载成功:', item.image)"
              @error="() => console.error('图片加载失败:', item.image)"
            >
              <template #error>
                <div class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                  <span>图片加载失败: {{ item.image }}</span>
                </div>
              </template>
            </el-image>
            <div class="banner-overlay"></div>
            <div class="banner-text">
              <h1>{{ item.title }}</h1>
              <p>{{ item.description }}</p>
              <div class="banner-buttons">
                <el-button type="primary" size="large" @click="router.push(item.primaryLink)">
                  <el-icon><component :is="item.primaryIcon" /></el-icon>
                  {{ item.primaryText }}
                </el-button>
                <el-button size="large" @click="router.push(item.secondaryLink)">
                  <el-icon><component :is="item.secondaryIcon" /></el-icon>
                  {{ item.secondaryText }}
                </el-button>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 统计数据 -->
    <StatisticsCard />

    <!-- 主要功能区 -->
    <div class="section-title">
      <h2>我要发布</h2>
      <div class="title-line"></div>
    </div>

    <div class="features">
      <div class="feature-card lost-card">
        <div class="card-icon">
          <el-icon><Box /></el-icon>
        </div>
        <h3>发布失物信息</h3>
        <p>如果您丢失了物品，可以在这里发布失物信息，让拾到物品的人能够联系到您</p>
        <router-link to="/lost-items/publish" class="feature-link lost-link">
          <el-icon><Plus /></el-icon>
          发布失物
        </router-link>
      </div>
      <div class="feature-card found-card">
        <div class="card-icon">
          <el-icon><Present /></el-icon>
        </div>
        <h3>发布拾物信息</h3>
        <p>如果您拾到了物品，可以在这里发布拾物信息，帮助失主找回丢失的物品</p>
        <router-link to="/found-items/publish" class="feature-link found-link">
          <el-icon><Plus /></el-icon>
          发布拾物
        </router-link>
      </div>
      <div class="feature-card match-card">
        <div class="card-icon">
          <el-icon><Connection /></el-icon>
        </div>
        <h3>智能匹配</h3>
        <p>使用CLIP+FAISS技术，通过图片或文字描述快速匹配失物与拾物，提高找回几率</p>
        <router-link to="/intelligent-match" class="feature-link match-link">
          <el-icon><Search /></el-icon>
          开始匹配
        </router-link>
      </div>
    </div>

    <!-- 使用指南 -->
    <div class="section-title">
      <h2>使用指南</h2>
      <div class="title-line"></div>
    </div>

    <div class="guide">
      <div class="guide-step">
        <div class="step-number">1</div>
        <div class="step-icon">
          <el-icon><Edit /></el-icon>
        </div>
        <h3>发布信息</h3>
        <p>发布您丢失或拾到的物品信息，提供尽可能详细的描述和图片</p>
      </div>
      <div class="guide-step">
        <div class="step-number">2</div>
        <div class="step-icon">
          <el-icon><Search /></el-icon>
        </div>
        <h3>查找匹配</h3>
        <p>使用智能匹配功能或浏览物品列表，寻找可能的匹配项</p>
      </div>
      <div class="guide-step">
        <div class="step-number">3</div>
        <div class="step-icon">
          <el-icon><ChatLineRound /></el-icon>
        </div>
        <h3>联系沟通</h3>
        <p>通过系统内置的聊天功能，与物品发布者取得联系</p>
      </div>
      <div class="guide-step">
        <div class="step-number">4</div>
        <div class="step-icon">
          <el-icon><Check /></el-icon>
        </div>
        <h3>确认归还</h3>
        <p>确认物品归还后，更新物品状态并完成流程</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Plus,
  Connection,
  Search,
  Box,
  Present,
  Picture,
  Edit,
  ChatLineRound,
  Check
} from '@element-plus/icons-vue'
import AnnouncementBanner from '@/components/Announcement/AnnouncementBanner.vue'
import StatisticsCard from '@/components/StatisticsCard.vue'

const router = useRouter()

// 轮播图数据
const bannerItems = [
  {
    title: '校园失物招领平台',
    description: '快速发布、查找和匹配失物与拾物信息',
    image: 'images/banner/banner1.jpg',
    primaryLink: '/lost-items/publish',
    primaryIcon: 'Box',
    primaryText: '我丢了东西',
    secondaryLink: '/found-items/publish',
    secondaryIcon: 'Present',
    secondaryText: '我捡到东西'
  },
  {
    title: '智能匹配技术',
    description: '使用CLIP+FAISS技术，通过图片或文字描述快速匹配失物与拾物',
    image: '/images/banner/banner2.jpg',
    primaryLink: '/intelligent-match',
    primaryIcon: 'Connection',
    primaryText: '开始匹配',
    secondaryLink: '/lost-items',
    secondaryIcon: 'Search',
    secondaryText: '浏览物品'
  },
  {
    title: '高效便捷的沟通',
    description: '内置即时通讯系统，方便失主与拾主快速联系',
    image: '/images/banner/banner3.jpg',
    primaryLink: '/chat',
    primaryIcon: 'ChatLineRound',
    primaryText: '开始沟通',
    secondaryLink: '/profile',
    secondaryIcon: 'User',
    secondaryText: '个人中心'
  }
]

// 统计数据已移至StatisticsCard组件

// 生命周期钩子
onMounted(() => {
  // 这里可以添加获取实际数据的API调用
  console.log('Home页面已加载')
})
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
  padding-bottom: 3rem;
}

/* 横幅样式 */
.banner {
  margin-bottom: 2rem;
  border-radius: 8px;
  overflow: hidden;
}

.banner-content {
  height: 400px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.banner-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #909399;
  font-size: 16px;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.banner-text {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.banner-text h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.banner-text p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.banner-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* 统计数据样式 */
.statistics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3rem;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.stat-item {
  text-align: center;
  flex: 1;
  padding: 1rem;
  border-right: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-right: none;
}

.stat-icon {
  font-size: 2rem;
  color: #1890ff;
  margin-bottom: 0.5rem;
}

.stat-count {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

/* 章节标题样式 */
.section-title {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.section-title h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.title-line {
  width: 60px;
  height: 3px;
  background-color: #1890ff;
  margin: 0 auto;
  margin-bottom: 1.5rem;
}

/* 功能卡片样式 */
.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  margin-bottom: 1.5rem;
  font-size: 2rem;
}

.lost-card .card-icon {
  background-color: #f6ffed;
  color: #52c41a;
}

.found-card .card-icon {
  background-color: #e6f7ff;
  color: #1890ff;
}

.match-card .card-icon {
  background-color: #f9f0ff;
  color: #722ed1;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.lost-card h3 {
  color: #52c41a;
}

.found-card h3 {
  color: #1890ff;
}

.match-card h3 {
  color: #722ed1;
}

.feature-card p {
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.feature-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.3s;
  font-weight: 500;
}

.lost-link {
  background-color: #52c41a;
}

.lost-link:hover {
  background-color: #73d13d;
}

.found-link {
  background-color: #1890ff;
}

.found-link:hover {
  background-color: #40a9ff;
}

.match-link {
  background-color: #722ed1;
}

.match-link:hover {
  background-color: #9254de;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #bbb;
  font-size: 2rem;
}

/* 使用指南样式 */
.guide {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.guide-step {
  flex: 1;
  min-width: 200px;
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  text-align: center;
  position: relative;
}

.step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-color: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.step-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  color: #1890ff;
}

.guide-step h3 {
  font-size: 1.2rem;
  margin-bottom: 0.8rem;
  color: #333;
  font-weight: 600;
}

.guide-step p {
  color: #666;
  line-height: 1.6;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .banner-text h1 {
    font-size: 2rem;
  }

  .banner-text p {
    font-size: 1rem;
  }

  .statistics {
    flex-wrap: wrap;
  }

  .stat-item {
    flex: 1 0 40%;
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 1rem;
  }

  .guide-step {
    flex: 1 0 100%;
  }
}

@media (max-width: 480px) {
  .banner-buttons {
    flex-direction: column;
  }

  .stat-item {
    flex: 1 0 100%;
  }
}
</style>