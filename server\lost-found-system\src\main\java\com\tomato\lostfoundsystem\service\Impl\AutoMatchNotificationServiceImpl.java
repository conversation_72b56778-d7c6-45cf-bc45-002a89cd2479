package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.FoundItem;
import com.tomato.lostfoundsystem.entity.ItemFeatureVector;
import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.mapper.FoundItemMapper;
import com.tomato.lostfoundsystem.mapper.ItemFeatureVectorMapper;
import com.tomato.lostfoundsystem.mapper.LostItemMapper;
import com.tomato.lostfoundsystem.service.AutoMatchNotificationService;
import com.tomato.lostfoundsystem.service.FeatureExtractionService;
import com.tomato.lostfoundsystem.service.IntelligentMatchService;
import com.tomato.lostfoundsystem.utils.ItemDescriptionBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 自动匹配通知服务实现类
 */
@Slf4j
@Service
public class AutoMatchNotificationServiceImpl implements AutoMatchNotificationService {

    @Autowired
    private IntelligentMatchService intelligentMatchService;

    @Autowired
    private LostItemMapper lostItemMapper;

    @Autowired
    private FoundItemMapper foundItemMapper;

    @Autowired
    private ItemFeatureVectorMapper itemFeatureVectorMapper;

    @Autowired
    private FeatureExtractionService featureExtractionService;

    /**
     * 异步处理失物信息的自动匹配
     *
     * @param lostItemId 失物ID
     * @param userId 用户ID
     */
    @Async
    @Override
    public void processLostItemAutoMatch(Long lostItemId, Long userId) {
        try {
            log.info("【智能匹配】开始处理失物自动匹配: 物品ID={}, 用户ID={}", lostItemId, userId);

            // 获取失物信息
            LostItem lostItem = lostItemMapper.selectById(lostItemId);
            if (lostItem == null) {
                log.error("【智能匹配】失物信息不存在: {}", lostItemId);
                return;
            }

            // 检查审核状态
            if (lostItem.getAuditStatus() == null) {
                log.error("【智能匹配】失物审核状态为null: {}", lostItemId);
                return;
            }

            // 使用枚举的equals方法或者比较code
            if (!"APPROVED".equals(lostItem.getAuditStatus().toString()) &&
                    !"APPROVED".equals(lostItem.getAuditStatus().getCode())) {
                log.info("【智能匹配】失物信息未审核通过，跳过自动匹配: {}, 当前状态: {}, 状态码: {}",
                        lostItemId, lostItem.getAuditStatus(),
                        lostItem.getAuditStatus() != null ? lostItem.getAuditStatus().getCode() : "null");
                return;
            }

            log.info("【智能匹配】失物信息已审核通过，开始进行匹配: ID={}, 名称={}",
                    lostItemId, lostItem.getItemName());

            // 使用工具类构建完整描述文本
            String description = ItemDescriptionBuilder.buildLostItemDescription(lostItem);
            log.info("【智能匹配】失物完整描述文本: {}", description);

            // 调用智能匹配服务进行混合匹配（同时使用文本和图像特征）
            log.info("【智能匹配】开始进行混合匹配: 用户ID={}, 物品类型=LOST", userId);

            Result<Map<String, Object>> result;

            // 执行自动匹配
            result = intelligentMatchService.performAutoMatch(userId, lostItemId, "LOST");

            log.info("【智能匹配】匹配方法调用完成");

            // 记录匹配结果
            if (result != null && result.getCode() == 200 && result.getData() != null) {
                Map<String, Object> data = result.getData();

                // 处理结果格式，适配不同的返回结构
                List<Map<String, Object>> items;
                int matchCount = 0;

                if (data.containsKey("results")) {
                    // 新的API返回格式
                    items = (List<Map<String, Object>>) data.get("results");
                    matchCount = items != null ? items.size() : 0;
                } else if (data.containsKey("items")) {
                    // 旧的API返回格式
                    items = (List<Map<String, Object>>) data.get("items");
                    matchCount = data.containsKey("count") ?
                            Integer.parseInt(data.get("count").toString()) :
                            (items != null ? items.size() : 0);
                } else {
                    items = null;
                }

                log.info("【智能匹配】失物自动匹配结果: 找到 {} 个匹配项", matchCount);

                if (items != null && !items.isEmpty()) {
                    for (int i = 0; i < Math.min(5, items.size()); i++) {
                        Map<String, Object> item = items.get(i);
                        // 适配不同的字段名
                        Long itemId = item.containsKey("item_id") ?
                                Long.valueOf(item.get("item_id").toString()) :
                                Long.valueOf(item.get("id").toString());

                        Double similarity = item.containsKey("similarity") ?
                                Double.valueOf(item.get("similarity").toString()) : 0.0;

                        String itemName = item.containsKey("itemName") ?
                                item.get("itemName").toString() : "未知";

                        log.info("【智能匹配】匹配项 #{}: ID={}, 相似度={}, 名称={}",
                                i + 1, itemId, similarity, itemName);
                    }
                }
            } else {
                log.info("【智能匹配】失物自动匹配未返回有效结果");
            }

            log.info("【智能匹配】失物自动匹配处理完成: {}", lostItemId);
        } catch (Exception e) {
            log.error("【智能匹配】处理失物自动匹配时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 异步处理拾物信息的自动匹配
     *
     * @param foundItemId 拾物ID
     * @param userId 用户ID
     */
    @Async
    @Override
    public void processFoundItemAutoMatch(Long foundItemId, Long userId) {
        try {
            log.info("【智能匹配】开始处理拾物自动匹配: 物品ID={}, 用户ID={}", foundItemId, userId);

            // 获取拾物信息
            FoundItem foundItem = foundItemMapper.selectById(foundItemId);
            if (foundItem == null) {
                log.error("【智能匹配】拾物信息不存在: {}", foundItemId);
                return;
            }

            // 检查审核状态
            if (foundItem.getAuditStatus() == null) {
                log.error("【智能匹配】拾物审核状态为null: {}", foundItemId);
                return;
            }

            // 使用枚举的equals方法或者比较code
            if (!"APPROVED".equals(foundItem.getAuditStatus().toString()) &&
                    !"APPROVED".equals(foundItem.getAuditStatus().getCode())) {
                log.info("【智能匹配】拾物信息未审核通过，跳过自动匹配: {}, 当前状态: {}, 状态码: {}",
                        foundItemId, foundItem.getAuditStatus(),
                        foundItem.getAuditStatus() != null ? foundItem.getAuditStatus().getCode() : "null");
                return;
            }

            log.info("【智能匹配】拾物信息已审核通过，开始进行匹配: ID={}, 名称={}",
                    foundItemId, foundItem.getItemName());

            // 使用工具类构建完整描述文本
            String description = ItemDescriptionBuilder.buildFoundItemDescription(foundItem);
            log.info("【智能匹配】拾物完整描述文本: {}", description);

            // 调用智能匹配服务进行混合匹配（同时使用文本和图像特征）
            log.info("【智能匹配】开始进行混合匹配: 用户ID={}, 物品类型=FOUND", userId);

            Result<Map<String, Object>> result;

            // 执行自动匹配
            result = intelligentMatchService.performAutoMatch(userId, foundItemId, "FOUND");

            log.info("【智能匹配】匹配方法调用完成");

            // 记录匹配结果
            if (result != null && result.getCode() == 200 && result.getData() != null) {
                Map<String, Object> data = result.getData();

                // 处理结果格式，适配不同的返回结构
                List<Map<String, Object>> items;
                int matchCount = 0;

                if (data.containsKey("results")) {
                    // 新的API返回格式
                    items = (List<Map<String, Object>>) data.get("results");
                    matchCount = items != null ? items.size() : 0;
                } else if (data.containsKey("items")) {
                    // 旧的API返回格式
                    items = (List<Map<String, Object>>) data.get("items");
                    matchCount = data.containsKey("count") ?
                            Integer.parseInt(data.get("count").toString()) :
                            (items != null ? items.size() : 0);
                } else {
                    items = null;
                }

                log.info("【智能匹配】拾物自动匹配结果: 找到 {} 个匹配项", matchCount);

                if (items != null && !items.isEmpty()) {
                    for (int i = 0; i < Math.min(5, items.size()); i++) {
                        Map<String, Object> item = items.get(i);
                        // 适配不同的字段名
                        Long itemId = item.containsKey("item_id") ?
                                Long.valueOf(item.get("item_id").toString()) :
                                Long.valueOf(item.get("id").toString());

                        Double similarity = item.containsKey("similarity") ?
                                Double.valueOf(item.get("similarity").toString()) : 0.0;

                        String itemName = item.containsKey("itemName") ?
                                item.get("itemName").toString() : "未知";

                        log.info("【智能匹配】匹配项 #{}: ID={}, 相似度={}, 名称={}",
                                i + 1, itemId, similarity, itemName);
                    }
                }
            } else {
                log.info("【智能匹配】拾物自动匹配未返回有效结果");
            }

            log.info("【智能匹配】拾物自动匹配处理完成: {}", foundItemId);
        } catch (Exception e) {
            log.error("【智能匹配】处理拾物自动匹配时发生异常: {}", e.getMessage(), e);
        }
    }


}