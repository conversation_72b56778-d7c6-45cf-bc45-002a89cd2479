package com.tomato.lostfoundsystem.utils;

import org.springframework.web.multipart.MultipartFile;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

/**
 * FFmpeg 工具类，用于处理音频和视频文件
 *
 * 未来改进方向：
 * 1. 使用阿里云媒体处理服务直接获取媒体时长
 * 2. 在客户端获取媒体时长，避免服务器处理
 * 3. 使用更高效的媒体处理库
 */
public class FFmpegUtils {

    /**
     * 从URL获取音频或视频文件的时长（单位：秒）
     *
     * @param filePath 文件URL或路径
     * @return 媒体时长（秒）
     */
    public static long getMediaDuration(String filePath) {
        try {
            // 构建 FFmpeg 命令
            String command = "ffmpeg -i " + filePath;

            // 执行命令
            ProcessBuilder processBuilder = new ProcessBuilder("bash", "-c", command);
            Process process = processBuilder.start();

            // 读取 FFmpeg 输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("Duration")) {
                    // 获取时长信息，格式为 "00:03:25.30"
                    String durationStr = line.split("Duration:")[1].split(",")[0].trim();
                    String[] timeParts = durationStr.split(":");

                    // 解析时长，计算总时长（单位：秒）
                    int hours = Integer.parseInt(timeParts[0]);
                    int minutes = Integer.parseInt(timeParts[1]);
                    float seconds = Float.parseFloat(timeParts[2]);

                    // 将时长转换为秒
                    return (long) ((hours * 3600) + (minutes * 60) + seconds);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;  // 如果无法获取时长，返回 0
    }

    /**
     * 从本地文件获取音频或视频的时长（单位：秒）
     * 注意：此方法需要将 MultipartFile 临时保存到本地，然后使用 FFmpeg 获取时长
     *
     * @param file MultipartFile 对象
     * @return 媒体时长（秒）
     */
    public static long getMediaDurationFromLocalFile(MultipartFile file) {
        File tempFile = null;
        try {
            // 创建临时文件
            Path tempPath = Files.createTempFile("media_", getFileExtension(file.getOriginalFilename()));
            tempFile = tempPath.toFile();

            // 将 MultipartFile 保存到临时文件
            Files.copy(file.getInputStream(), tempPath, StandardCopyOption.REPLACE_EXISTING);

            // 构建 FFmpeg 命令
            String command = "ffmpeg -i " + tempFile.getAbsolutePath();

            // 执行命令
            ProcessBuilder processBuilder = new ProcessBuilder("bash", "-c", command);
            Process process = processBuilder.start();

            // 读取 FFmpeg 输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("Duration")) {
                    // 获取时长信息，格式为 "00:03:25.30"
                    String durationStr = line.split("Duration:")[1].split(",")[0].trim();
                    String[] timeParts = durationStr.split(":");

                    // 解析时长，计算总时长（单位：秒）
                    int hours = Integer.parseInt(timeParts[0]);
                    int minutes = Integer.parseInt(timeParts[1]);
                    float seconds = Float.parseFloat(timeParts[2]);

                    // 将时长转换为秒
                    return (long) ((hours * 3600) + (minutes * 60) + seconds);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 删除临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
        return 0;  // 如果无法获取时长，返回 0
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 文件扩展名（包含点，如 ".mp4"）
     */
    private static String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty() || !filename.contains(".")) {
            return ".tmp";
        }
        return filename.substring(filename.lastIndexOf("."));
    }
}
