<template>
  <div class="contact-list">
    <div class="list-header">
      <h3>消息</h3>
      <el-button 
        circle 
        @click="$emit('refresh')" 
        :loading="loading"
      >
        <el-icon><Refresh /></el-icon>
      </el-button>
    </div>
    
    <div class="search-box">
      <el-input
        v-model="searchQuery"
        placeholder="搜索联系人..."
        prefix-icon="Search"
        clearable
      />
    </div>
    
    <el-scrollbar class="contacts-scrollbar">
      <div v-if="loading && !contacts.length" class="loading-contacts">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else-if="!contacts.length" class="empty-contacts">
        <el-empty description="暂无联系人" />
      </div>
      
      <div v-else class="contacts-container">
        <div
          v-for="contact in filteredContacts"
          :key="contact.id"
          class="contact-item"
          :class="{ 'active': isActive(contact) }"
          @click="$emit('select', contact)"
        >
          <div class="contact-avatar">
            <el-badge :value="contact.unreadCount" :hidden="!contact.unreadCount" type="danger">
              <el-avatar :size="40" :src="contact.avatar">
                <span 
                  class="avatar-text" 
                  :style="{ backgroundColor: getAvatarColor(contact.name) }"
                >
                  {{ getNameInitial(contact.name) }}
                </span>
              </el-avatar>
            </el-badge>
          </div>
          
          <div class="contact-info">
            <div class="contact-name-row">
              <span class="contact-name">{{ contact.name }}</span>
              <span class="contact-time">{{ formatMessageTime(contact.lastTime) }}</span>
            </div>
            
            <div class="contact-message-row">
              <div class="contact-last-message" :title="contact.lastMessage">
                <span v-if="contact.messageType === 'IMAGE'" class="message-type-icon">
                  <el-icon><Picture /></el-icon> 图片
                </span>
                <span v-else-if="contact.messageType === 'DOCUMENT'" class="message-type-icon">
                  <el-icon><Document /></el-icon> 文件
                </span>
                <span v-else>{{ contact.lastMessage }}</span>
              </div>
              
              <div class="contact-status">
                <span v-if="contact.online" class="online-status">在线</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Refresh, Search, Picture, Document } from '@element-plus/icons-vue'
import { generateAvatarColor, getNameInitial, formatDate, formatTime } from '@/utils/chat-utils'

const props = defineProps({
  contacts: {
    type: Array,
    default: () => []
  },
  currentContact: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['select', 'refresh'])

// 搜索功能
const searchQuery = ref('')

// 过滤联系人
const filteredContacts = computed(() => {
  if (!searchQuery.value) {
    return props.contacts
  }
  
  const query = searchQuery.value.toLowerCase()
  return props.contacts.filter(contact => 
    contact.name.toLowerCase().includes(query) ||
    (contact.lastMessage && contact.lastMessage.toLowerCase().includes(query))
  )
})

// 检查联系人是否为当前选中的联系人
const isActive = (contact) => {
  return props.currentContact && String(props.currentContact.id) === String(contact.id)
}

// 格式化消息时间
const formatMessageTime = (time) => {
  if (!time) return ''
  
  const date = new Date(time)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  // 如果是今天
  if (date.toDateString() === today.toDateString()) {
    return formatTime(time)
  }
  
  // 如果是昨天
  if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  }
  
  // 如果是本周
  const daysDiff = Math.floor((today - date) / (1000 * 60 * 60 * 24))
  if (daysDiff < 7) {
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return days[date.getDay()]
  }
  
  // 其他日期
  return formatDate(time)
}

// 获取头像颜色
const getAvatarColor = (name) => {
  return generateAvatarColor(name)
}
</script>

<style scoped>
.contact-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f7fa;
  border-right: 1px solid #e9edef;
}

.list-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e9edef;
}

.list-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111b21;
}

.search-box {
  padding: 8px 16px;
  border-bottom: 1px solid #e9edef;
}

.contacts-scrollbar {
  flex: 1;
  overflow: hidden;
}

.contacts-container {
  padding: 8px 0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.contact-item:hover {
  background-color: #f0f2f5;
}

.contact-item.active {
  background-color: #e9edef;
}

.contact-avatar {
  margin-right: 12px;
  position: relative;
}

.avatar-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 16px;
  font-weight: bold;
  color: white;
}

.contact-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.contact-name {
  font-weight: 600;
  font-size: 14px;
  color: #111b21;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-time {
  font-size: 11px;
  color: #8696a0;
  white-space: nowrap;
  margin-left: 8px;
}

.contact-message-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.contact-last-message {
  font-size: 12px;
  color: #667781;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.message-type-icon {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.contact-status {
  font-size: 11px;
  white-space: nowrap;
}

.online-status {
  color: #00a884;
  font-weight: 500;
}

.loading-contacts, .empty-contacts {
  padding: 16px;
}
</style>
