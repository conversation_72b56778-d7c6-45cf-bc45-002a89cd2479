@echo off
echo ===================================
echo 正在停止所有服务...
echo ===================================

:: 直接使用当前目录
set CONFIG_FILE=config.bat

:: 加载配置
call "%CONFIG_FILE%"

:: 设置颜色
color 0C

echo 正在停止 %SYSTEM_NAME% 的所有服务...

:: 停止智能匹配服务
echo 1. 停止智能匹配服务...
taskkill /F /FI "WINDOWTITLE eq CLIP+FAISS Service*" /T > nul 2>&1
taskkill /F /FI "IMAGENAME eq python.exe" /T > nul 2>&1
echo 智能匹配服务已停止！

:: 停止Kafka服务
echo 2. 停止Kafka服务...
taskkill /F /FI "WINDOWTITLE eq Kafka Server*" /T > nul 2>&1
echo Kafka服务已停止！

:: 停止Zookeeper服务
echo 3. 停止Zookeeper服务...
taskkill /F /FI "WINDOWTITLE eq Zookeeper Server*" /T > nul 2>&1
echo Zookeeper服务已停止！

:: 停止Redis服务
echo 4. 停止Redis服务...
taskkill /F /FI "IMAGENAME eq redis-server.exe" > nul 2>&1
echo Redis服务已停止！

echo.
echo ===================================
echo 所有服务已停止！
echo ===================================

:: 不自动关闭窗口
pause
