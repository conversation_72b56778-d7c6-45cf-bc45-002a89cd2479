/**
 * 在线状态管理模块 (重构版)
 *
 * 该模块负责：
 * 1. 全局在线用户状态的监控和管理
 * 2. 在线用户数量的统计和更新
 * 3. 在线状态变化的通知
 *
 * 与其他模块的关系：
 * - websocket.js: 提供基础的WebSocket连接和消息处理功能
 * - contact-status.js: 使用本模块的在线状态信息，专注于联系人状态管理
 *
 * 使用方式：
 * import { initOnlineStatusMonitor, getOnlineUserCount, isUserOnline } from '@/utils/online-status'
 *
 * 重构说明：
 * 使用统一的在线状态服务，避免状态管理混乱和循环依赖
 */

import { 
  initOnlineStatusService, 
  isUserOnline as serviceIsUserOnline,
  getOnlineUserCount as serviceGetOnlineUserCount,
  getOnlineUsers as serviceGetOnlineUsers,
  getUserLastActiveTime as serviceGetUserLastActiveTime,
  requestOnlineUsers as serviceRequestOnlineUsers,
  checkUserOnlineStatus
} from '@/services/onlineStatusService'
import { useOnlineStatusStore } from '@/stores'

// 初始化状态
let initialized = false

/**
 * 初始化在线状态监控
 * @param {string} token 用户令牌
 */
export function initOnlineStatusMonitor(token) {
  if (!token) {
    console.warn('未找到token，无法初始化在线状态监控')
    return
  }

  if (initialized) {
    console.log('在线状态监控已初始化')
    // 即使已初始化，也请求最新的在线用户列表
    setTimeout(() => {
      try {
        serviceRequestOnlineUsers()
      } catch (e) {
        console.warn('请求在线用户列表失败:', e)
      }
    }, 2000)
    return
  }

  console.log('初始化在线状态监控...')

  // 初始化在线状态服务
  const service = initOnlineStatusService()

  // 监听在线状态变化事件
  window.addEventListener('online-status-changed', (event) => {
    const { userId, isOnline, timestamp } = event.detail
    
    // 触发在线用户列表更新事件
    window.dispatchEvent(new CustomEvent('online-users-updated', {
      detail: {
        users: serviceGetOnlineUsers(),
        count: serviceGetOnlineUserCount(),
        timestamp: timestamp || Date.now()
      }
    }))
  })

  // 标记为已初始化
  initialized = true

  // 触发初始化完成事件
  window.dispatchEvent(new Event('online-status-initialized'))

  return service
}

/**
 * 请求获取在线用户列表
 */
export function requestOnlineUsers() {
  return serviceRequestOnlineUsers()
}

/**
 * 获取在线用户数量
 * @returns {number} 在线用户数量
 */
export function getOnlineUserCount() {
  return serviceGetOnlineUserCount()
}

/**
 * 获取在线用户列表
 * @returns {string[]} 在线用户ID数组
 */
export function getOnlineUsers() {
  return serviceGetOnlineUsers()
}

/**
 * 检查用户是否在线
 * @param {string} userId 用户ID
 * @returns {boolean} 是否在线
 */
export function isUserOnline(userId) {
  return serviceIsUserOnline(userId)
}

/**
 * 获取用户最后活跃时间
 * @param {string} userId 用户ID
 * @returns {number|null} 最后活跃时间
 */
export function getUserLastActiveTime(userId) {
  return serviceGetUserLastActiveTime(userId)
}

/**
 * 获取WebSocket连接状态
 * @returns {boolean} 是否已连接
 */
export function isConnected() {
  const store = useOnlineStatusStore()
  return store.isConnected
}

/**
 * 检查特定用户的在线状态
 * @param {string} userId 用户ID
 */
export function checkUserOnline(userId) {
  return checkUserOnlineStatus(userId)
}

// 导出默认对象
export default {
  initOnlineStatusMonitor,
  getOnlineUserCount,
  getOnlineUsers,
  isUserOnline,
  getUserLastActiveTime,
  isConnected,
  requestOnlineUsers,
  checkUserOnline
}
