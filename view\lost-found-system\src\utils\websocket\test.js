/**
 * WebSocket 功能测试脚本
 * 
 * 此脚本用于测试优化后的 WebSocket 功能是否正常工作。
 * 可以在浏览器控制台中运行，或者在组件中导入并调用。
 */

import {
  initWebSocketClient,
  isWebSocketConnected,
  getOnlineUserCount,
  getOnlineUsers,
  isUserOnline,
  sendChatMessage,
  disconnect
} from './index';

/**
 * 测试 WebSocket 连接
 */
export function testWebSocketConnection() {
  console.group('测试 WebSocket 连接');
  
  // 初始化 WebSocket 客户端
  console.log('初始化 WebSocket 客户端...');
  initWebSocketClient();
  
  // 检查连接状态
  setTimeout(() => {
    const connected = isWebSocketConnected();
    console.log('WebSocket 连接状态:', connected ? '已连接' : '未连接');
    
    if (connected) {
      // 获取在线用户数量
      const count = getOnlineUserCount();
      console.log('在线用户数量:', count);
      
      // 获取在线用户列表
      const users = getOnlineUsers();
      console.log('在线用户列表:', users);
      
      // 测试发送消息
      testSendMessage();
    } else {
      console.error('WebSocket 连接失败');
    }
    
    console.groupEnd();
  }, 3000);
}

/**
 * 测试发送消息
 */
function testSendMessage() {
  console.group('测试发送消息');
  
  // 创建测试消息
  const message = {
    senderId: '1',
    receiverId: '2',
    content: '这是一条测试消息',
    messageType: 'TEXT'
  };
  
  // 发送消息
  console.log('发送测试消息:', message);
  sendChatMessage(message)
    .then(result => {
      console.log('消息发送结果:', result ? '成功' : '失败');
    })
    .catch(error => {
      console.error('发送消息时出错:', error);
    });
  
  console.groupEnd();
}

/**
 * 测试断开连接
 */
export function testDisconnect() {
  console.group('测试断开连接');
  
  console.log('断开 WebSocket 连接...');
  disconnect();
  
  setTimeout(() => {
    const connected = isWebSocketConnected();
    console.log('WebSocket 连接状态:', connected ? '已连接' : '已断开');
    console.groupEnd();
  }, 1000);
}

// 导出所有测试函数
export default {
  testWebSocketConnection,
  testDisconnect
};
