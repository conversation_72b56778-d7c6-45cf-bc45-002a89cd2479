package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.service.RedisService;
import com.tomato.lostfoundsystem.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Service
public class RedisServiceImpl implements RedisService {

    @Autowired
    private RedisUtil redisUtil;  // 使用 RedisUtil 来执行基础的 Redis 操作

    // 存储 WebSocket 会话信息

    @Value("${jwt.expiration-time}")
    private long expirationTime;

    // 心跳超时时间（分钟）
    @Value("${websocket.heartbeat.timeout:30}")
    private long heartbeatTimeout;

    // 心跳键前缀
    private static final String HEARTBEAT_PREFIX = "user:heartbeat:";
    // 会话键前缀
    private static final String SESSION_PREFIX = "user:session:";
    // 用户会话集合键前缀
    private static final String USER_SESSIONS_PREFIX = "user:allsessions:";
    // 临时离线前缀
    private static final String TEMP_OFFLINE_PREFIX = "user:temp_offline:";
    // 临时离线超时时间（分钟）
    private static final long TEMP_OFFLINE_TIMEOUT = 5;

    @Override
    public void storeSession(Long userId, String sessionId) {
        try {
            // 存储单个会话（向后兼容）
            redisUtil.set(SESSION_PREFIX + userId, sessionId, expirationTime);

            // 同时将会话添加到多设备支持的集合中
            addUserSession(userId, sessionId, "unknown");

            log.info("Stored session for userId: {} with sessionId: {}", userId, sessionId);
        } catch (Exception e) {
            log.error("Failed to store session for userId: {}: {}", userId, e.getMessage());
        }
    }

    @Override
    public void addUserSession(Long userId, String sessionId, String deviceInfo) {
        try {
            // 检查用户是否已有会话，如果有则移除旧会话（单设备登录）
            Set<String> existingSessions = getUserSessions(userId);
            for (String sessionInfo : existingSessions) {
                String[] parts = sessionInfo.split(":", 2);
                String oldSessionId = parts[0];
                if (!oldSessionId.equals(sessionId)) {
                    // 移除旧会话
                    removeUserSession(userId, oldSessionId);
                    log.info("Removed old session {} for user {} (single device login)", oldSessionId, userId);
                }
            }

            // 存储会话信息
            String sessionKey = SESSION_PREFIX + sessionId;
            redisUtil.set(sessionKey, userId.toString(), heartbeatTimeout);

            // 更新心跳时间
            updateHeartbeat(userId, sessionId);

            // 将会话ID添加到用户的会话集合中（仍然使用集合，但只会有一个元素）
            String userSessionsKey = USER_SESSIONS_PREFIX + userId;
            redisUtil.deleteKey(userSessionsKey); // 先清空集合
            redisUtil.addToSet(userSessionsKey, sessionId + ":" + deviceInfo);

            log.info("Added session for userId: {} with sessionId: {} on device: {} (single device login)",
                    userId, sessionId, deviceInfo);
        } catch (Exception e) {
            log.error("Failed to add session for userId: {}: {}", userId, e.getMessage());
        }
    }

    @Override
    public String getSession(Long userId) {
        try {
            String sessionId = redisUtil.get(SESSION_PREFIX + userId);
            log.info("Retrieved session for userId: {}: {}", userId, sessionId);
            return sessionId;
        } catch (Exception e) {
            log.error("Failed to retrieve session for userId: {}: {}", userId, e.getMessage());
            return null;
        }
    }

    @Override
    public Set<String> getUserSessions(Long userId) {
        try {
            String userSessionsKey = USER_SESSIONS_PREFIX + userId;
            Set<String> sessions = redisUtil.getSetMembers(userSessionsKey);

            // 过滤出仍然有效的会话
            Set<String> activeSessions = sessions.stream()
                .filter(session -> {
                    String[] parts = session.split(":", 2);
                    String sessionId = parts[0];
                    return isSessionActive(sessionId);
                })
                .collect(Collectors.toSet());

            // 如果有失效的会话，更新Redis中的集合
            if (activeSessions.size() < sessions.size()) {
                redisUtil.deleteKey(userSessionsKey);
                for (String session : activeSessions) {
                    redisUtil.addToSet(userSessionsKey, session);
                }
            }

            log.debug("Retrieved {} active sessions for userId: {}", activeSessions.size(), userId);
            return activeSessions;
        } catch (Exception e) {
            log.error("Failed to retrieve sessions for userId: {}: {}", userId, e.getMessage());
            return new HashSet<>();
        }
    }

    @Override
    public int countUserSessions(Long userId) {
        try {
            // 获取用户的所有活跃会话
            Set<String> sessions = getUserSessions(userId);
            int count = sessions.size();
            log.debug("用户 {} 的活跃会话数: {}", userId, count);
            return count;
        } catch (Exception e) {
            log.error("获取用户 {} 的会话数量时出错: {}", userId, e.getMessage());
            return 0;
        }
    }

    @Override
    public boolean isUserOnline(Long userId) {
        try {
            // 在单设备登录模式下，用户只有一个会话
            // 检查用户是否有活跃的会话
            Set<String> sessions = getUserSessions(userId);
            boolean isOnline = !sessions.isEmpty();

            // 添加更详细的日志
            log.info("用户 {} 在线状态检查 - 会话数: {}, 在线状态: {}",
                    userId, sessions.size(), isOnline ? "在线" : "离线");

            // 如果有会话，检查每个会话的心跳状态
            if (!sessions.isEmpty()) {
                for (String sessionInfo : sessions) {
                    String[] parts = sessionInfo.split(":", 2);
                    String sessionId = parts[0];

                    // 检查心跳是否存在且未过期
                    String heartbeatKey = HEARTBEAT_PREFIX + sessionId;
                    boolean heartbeatExists = redisUtil.hasKey(heartbeatKey);

                    log.info("用户 {} 会话 {} 心跳状态: {}",
                            userId, sessionId, heartbeatExists ? "活跃" : "过期");

                    // 如果至少有一个会话的心跳存在，则用户在线
                    if (heartbeatExists) {
                        return true;
                    }
                }

                // 如果所有会话的心跳都不存在，则用户离线
                log.info("用户 {} 的所有会话心跳都已过期，标记为离线", userId);
                return false;
            }

            return isOnline;
        } catch (Exception e) {
            log.error("检查用户 {} 在线状态时出错: {}", userId, e.getMessage(), e);
            // 出错时默认为在线，避免消息丢失
            return true;
        }
    }

    @Override
    public boolean isSessionActive(String sessionId) {
        try {
            // 检查会话是否存在
            String sessionKey = SESSION_PREFIX + sessionId;
            boolean sessionExists = redisUtil.hasKey(sessionKey);

            // 检查心跳是否存在且未过期
            String heartbeatKey = HEARTBEAT_PREFIX + sessionId;
            boolean heartbeatExists = redisUtil.hasKey(heartbeatKey);

            boolean isActive = sessionExists && heartbeatExists;
            log.debug("Session {} is active: {}", sessionId, isActive);
            return isActive;
        } catch (Exception e) {
            log.error("Failed to check if session is active: {}: {}", sessionId, e.getMessage());
            return false;
        }
    }

    @Override
    public void updateHeartbeat(Long userId, String sessionId) {
        try {
            // 更新心跳时间
            String heartbeatKey = HEARTBEAT_PREFIX + sessionId;
            redisUtil.set(heartbeatKey, String.valueOf(System.currentTimeMillis()), heartbeatTimeout);

            // 延长会话有效期
            String sessionKey = SESSION_PREFIX + sessionId;
            if (redisUtil.hasKey(sessionKey)) {
                redisUtil.extendExpiration(sessionKey, heartbeatTimeout);
            }

            log.debug("Updated heartbeat for user {} with session {}", userId, sessionId);
        } catch (Exception e) {
            log.error("Failed to update heartbeat for user {}: {}", userId, e.getMessage());
        }
    }

    // 存储用户已读的消息
    @Override
    public void storeReadMessage(Long userId, Long messageId) {
        try {
            redisUtil.storeReadMessage(userId, messageId);
            log.info("Stored read message for userId: {} and messageId: {}", userId, messageId);
        } catch (Exception e) {
            log.error("Failed to store read message for userId: {} and messageId: {}: {}", userId, messageId, e.getMessage());
        }
    }

    // 判断用户是否已读某条消息
    @Override
    public boolean hasReadMessage(Long userId, Long messageId) {
        try {
            boolean hasRead = redisUtil.hasReadMessage(userId, messageId);
            log.info("User {} has read message {}: {}", userId, messageId, hasRead);
            return hasRead;
        } catch (Exception e) {
            log.error("Failed to check if userId: {} has read messageId: {}: {}", userId, messageId, e.getMessage());
            return false;
        }
    }

    // 设置用户与联系人之间的未读消息计数
    @Override
    public void setUnreadCount(Long userId, Long contactId, int count) {
        try {
            redisUtil.setUnreadCount(userId, contactId, count);
            log.info("设置未读计数 - 用户ID: {}, 联系人ID: {}, 计数: {}", userId, contactId, count);
        } catch (Exception e) {
            log.error("设置未读计数失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage());
        }
    }

    // 获取用户与联系人之间的未读消息计数
    @Override
    public int getUnreadCount(Long userId, Long contactId) {
        try {
            int count = redisUtil.getUnreadCount(userId, contactId);
            log.debug("获取未读计数 - 用户ID: {}, 联系人ID: {}, 计数: {}", userId, contactId, count);
            return count;
        } catch (Exception e) {
            log.error("获取未读计数失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage());
            return 0;
        }
    }

    // 增加用户与联系人之间的未读消息计数
    @Override
    public int incrementUnreadCount(Long userId, Long contactId) {
        try {
            int newCount = redisUtil.incrementUnreadCount(userId, contactId);
            log.info("增加未读计数 - 用户ID: {}, 联系人ID: {}, 新计数: {}", userId, contactId, newCount);
            return newCount;
        } catch (Exception e) {
            log.error("增加未读计数失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage());
            return 0;
        }
    }

    // 重置用户与联系人之间的未读消息计数
    @Override
    public void resetUnreadCount(Long userId, Long contactId) {
        try {
            redisUtil.resetUnreadCount(userId, contactId);
            log.info("重置未读计数 - 用户ID: {}, 联系人ID: {}", userId, contactId);
        } catch (Exception e) {
            log.error("重置未读计数失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage());
        }
    }

    // 存储最后一条聊天信息
    @Override
    public void storeLastMessage(Long userId, Long contactId, Long messageId, String content,
                                String messageType, Long timestamp, String fileUrl) {
        try {
            redisUtil.storeLastMessage(userId, contactId, messageId, content, messageType, timestamp, fileUrl);
            log.info("存储最后一条消息 - 用户ID: {}, 联系人ID: {}, 消息ID: {}", userId, contactId, messageId);
        } catch (Exception e) {
            log.error("存储最后一条消息失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage());
        }
    }

    // 获取最后一条聊天信息
    @Override
    public Map<String, String> getLastMessage(Long userId, Long contactId) {
        try {
            Map<String, String> lastMessage = redisUtil.getLastMessage(userId, contactId);
            if (lastMessage != null) {
                log.debug("获取最后一条消息成功 - 用户ID: {}, 联系人ID: {}", userId, contactId);
            } else {
                log.debug("Redis中没有最后一条消息 - 用户ID: {}, 联系人ID: {}", userId, contactId);
            }
            return lastMessage;
        } catch (Exception e) {
            log.error("获取最后一条消息失败 - 用户ID: {}, 联系人ID: {}, 错误: {}", userId, contactId, e.getMessage());
            return null;
        }
    }

    // 批量删除缓存，支持按照模式删除
    @Override
    public void deleteByPattern(String pattern) {
        try {
            redisUtil.deleteByPattern(pattern);
            log.info("Deleted cache by pattern: {}", pattern);
        } catch (Exception e) {
            log.error("Failed to delete cache by pattern: {}: {}", pattern, e.getMessage());
        }
    }

    @Override
    public void removeUserSession(Long userId, String sessionId) {
        try {
            // 删除会话
            String sessionKey = SESSION_PREFIX + sessionId;
            redisUtil.deleteKey(sessionKey);

            // 删除心跳
            String heartbeatKey = HEARTBEAT_PREFIX + sessionId;
            redisUtil.deleteKey(heartbeatKey);

            // 从用户会话集合中移除
            String userSessionsKey = USER_SESSIONS_PREFIX + userId;
            Set<String> sessions = redisUtil.getSetMembers(userSessionsKey);
            Set<String> updatedSessions = sessions.stream()
                .filter(s -> !s.startsWith(sessionId + ":"))
                .collect(Collectors.toSet());

            if (updatedSessions.size() < sessions.size()) {
                redisUtil.deleteKey(userSessionsKey);
                for (String session : updatedSessions) {
                    redisUtil.addToSet(userSessionsKey, session);
                }
            }

            log.info("Removed session {} for user {}", sessionId, userId);
        } catch (Exception e) {
            log.error("Failed to remove session for user {}: {}", userId, e.getMessage());
        }
    }

    /**
     * 定时清理过期的会话，每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000)
    @Override
    public void cleanExpiredSessions() {
        try {
            log.info("Starting scheduled cleanup of expired sessions");

            // 获取所有会话键
            Set<String> sessionKeys = redisUtil.getKeysByPattern(SESSION_PREFIX + "*");
            int expiredCount = 0;

            for (String sessionKey : sessionKeys) {
                // 提取会话ID
                String sessionId = sessionKey.substring(SESSION_PREFIX.length());

                // 检查心跳是否存在
                String heartbeatKey = HEARTBEAT_PREFIX + sessionId;
                if (!redisUtil.hasKey(heartbeatKey)) {
                    // 心跳不存在，删除会话
                    String userId = redisUtil.get(sessionKey);
                    if (userId != null) {
                        removeUserSession(Long.valueOf(userId), sessionId);
                        expiredCount++;
                    } else {
                        redisUtil.deleteKey(sessionKey);
                        expiredCount++;
                    }
                }
            }

            log.info("Cleanup completed. Removed {} expired sessions", expiredCount);
        } catch (Exception e) {
            log.error("Error during session cleanup: {}", e.getMessage(), e);
        }
    }

    @Override
    public Set<Long> getAllUserIds() {
        try {
            // 获取所有用户会话集合键
            Set<String> userSessionKeys = redisUtil.getKeysByPattern(USER_SESSIONS_PREFIX + "*");

            // 提取用户ID
            Set<Long> userIds = new HashSet<>();
            for (String key : userSessionKeys) {
                String userIdStr = key.substring(USER_SESSIONS_PREFIX.length());
                try {
                    Long userId = Long.valueOf(userIdStr);
                    userIds.add(userId);
                } catch (NumberFormatException e) {
                    log.warn("Invalid user ID format in key: {}", key);
                }
            }

            log.info("Retrieved {} user IDs", userIds.size());
            return userIds;
        } catch (Exception e) {
            log.error("Failed to get all user IDs: {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    @Override
    public void markSessionAsTemporaryOffline(Long userId, String sessionId) {
        try {
            // 标记会话为临时离线状态
            String tempOfflineKey = TEMP_OFFLINE_PREFIX + sessionId;
            redisUtil.set(tempOfflineKey, userId.toString(), TEMP_OFFLINE_TIMEOUT);

            log.info("Marked session {} for user {} as temporary offline", sessionId, userId);
        } catch (Exception e) {
            log.error("Failed to mark session as temporary offline: {}", e.getMessage(), e);
        }
    }

    @Override
    public boolean isSessionTemporaryOffline(String sessionId) {
        try {
            // 检查会话是否处于临时离线状态
            String tempOfflineKey = TEMP_OFFLINE_PREFIX + sessionId;
            boolean isTemporaryOffline = redisUtil.hasKey(tempOfflineKey);

            log.debug("Session {} is temporary offline: {}", sessionId, isTemporaryOffline);
            return isTemporaryOffline;
        } catch (Exception e) {
            log.error("Failed to check if session is temporary offline: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public long getLastActiveTime(Long userId) {
        try {
            // 获取用户的会话（单设备登录下只有一个）
            Set<String> sessions = getUserSessions(userId);

            if (sessions.isEmpty()) {
                log.debug("No active session found for user {} (single device login)", userId);
                return 0;
            }

            // 在单设备登录模式下，只有一个会话
            String sessionInfo = sessions.iterator().next();
            String[] parts = sessionInfo.split(":", 2);
            String sessionId = parts[0];

            String heartbeatKey = HEARTBEAT_PREFIX + sessionId;
            String heartbeatValue = redisUtil.get(heartbeatKey);

            if (heartbeatValue != null) {
                try {
                    long heartbeatTime = Long.parseLong(heartbeatValue);
                    log.debug("Last active time for user {} (single device login): {}", userId, heartbeatTime);
                    return heartbeatTime;
                } catch (NumberFormatException e) {
                    log.warn("Invalid heartbeat value for session {}: {}", sessionId, heartbeatValue);
                }
            }

            return 0;
        } catch (Exception e) {
            log.error("Failed to get last active time for user {}: {}", userId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Set<String> getKeysByPattern(String pattern) {
        try {
            Set<String> keys = redisUtil.getKeysByPattern(pattern);
            log.debug("Retrieved {} keys matching pattern: {}", keys.size(), pattern);
            return keys;
        } catch (Exception e) {
            log.error("Failed to get keys by pattern {}: {}", pattern, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    @Override
    public void set(String key, String value, long expireSeconds) {
        try {
            redisUtil.set(key, value, expireSeconds);
            log.debug("Set key {} with expiration {} seconds", key, expireSeconds);
        } catch (Exception e) {
            log.error("Failed to set key {}: {}", key, e.getMessage(), e);
        }
    }

    @Override
    public boolean hasKey(String key) {
        try {
            boolean exists = redisUtil.hasKey(key);
            log.debug("Key {} exists: {}", key, exists);
            return exists;
        } catch (Exception e) {
            log.error("Failed to check if key {} exists: {}", key, e.getMessage(), e);
            return false;
        }
    }
}