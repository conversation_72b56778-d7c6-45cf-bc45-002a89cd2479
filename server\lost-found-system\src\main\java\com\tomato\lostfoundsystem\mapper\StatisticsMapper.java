package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.StatisticsHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 统计数据Mapper接口
 */
@Mapper
public interface StatisticsMapper {
    
    /**
     * 获取总物品数
     */
    int getTotalItems();
    
    /**
     * 获取已归还物品数
     */
    int getReturnedItems();
    
    /**
     * 获取成功匹配数
     */
    int getSuccessMatches();
    
    /**
     * 保存统计数据历史
     */
    int saveStatisticsHistory(StatisticsHistory statisticsHistory);
    
    /**
     * 获取指定日期和类型的统计数据
     */
    StatisticsHistory getStatisticsByDateAndType(@Param("statDate") LocalDate statDate, @Param("statType") String statType);
    
    /**
     * 获取统计数据趋势
     */
    List<Map<String, Object>> getStatisticsTrend(@Param("statType") String statType, 
                                                @Param("startDate") LocalDate startDate, 
                                                @Param("endDate") LocalDate endDate);
}
