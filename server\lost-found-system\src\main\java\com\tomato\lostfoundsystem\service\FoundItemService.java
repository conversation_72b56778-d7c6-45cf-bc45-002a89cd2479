
package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.FoundItemDTO;
import com.tomato.lostfoundsystem.entity.FoundItem;
import org.springframework.web.multipart.MultipartFile;

public interface FoundItemService {
    Result<Object> publishFoundItem(FoundItemDTO foundItemDTO);

    Result<Object> searchFoundItems(String keyword, String foundLocation, String status, String timeRange, String timeFilterType, String startDate, String endDate, int page, int size);

    Result<Object> getFoundItemDetails(Long id);

    /**
     * 根据ID获取拾物信息
     *
     * @param id 拾物ID
     * @return 拾物信息
     */
    FoundItem getFoundItemById(Long id);

    Result<Object> updateFoundItem(Long id, FoundItemDTO foundItemDTO);

    Result<Object> deleteFoundItem(Long id, Long userId);

    Result<Object> getMyFoundItems(Long userId);

    /**
     * 认领拾物
     * @param id 拾物ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Object> claimFoundItem(Long id, Long userId);

    /**
     * 更新拾物状态（未认领/已认领）
     *
     * @param id 拾物ID
     * @param status 状态值（UNCLAIMED/RETURNED）
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Object> updateFoundItemStatus(Long id, String status, Long userId);
}
