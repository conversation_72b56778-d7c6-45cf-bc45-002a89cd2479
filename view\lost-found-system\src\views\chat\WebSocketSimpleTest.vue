<template>
  <div class="simple-test-container">
    <h1>WebSocket 简单测试</h1>

    <div class="status-panel">
      <div class="status-item">
        <span class="label">连接状态:</span>
        <span class="value" :class="{ 'connected': connected, 'disconnected': !connected }">
          {{ connected ? '已连接' : '未连接' }}
        </span>
      </div>
      <div class="status-item">
        <span class="label">用户ID:</span>
        <span class="value">{{ userInfo?.id || '未登录' }}</span>
      </div>
    </div>

    <div class="action-panel">
      <el-button type="primary" @click="connect" :disabled="connected">连接</el-button>
      <el-button type="danger" @click="disconnect" :disabled="!connected">断开</el-button>
      <el-button type="warning" @click="clearLogs">清空日志</el-button>
      <el-button type="success" @click="testSendToSelf" :disabled="!connected">测试发送给自己</el-button>
      <el-button type="info" @click="testBroadcast" :disabled="!connected">测试广播</el-button>
      <el-button type="primary" @click="getOnlineUsers">获取在线用户</el-button>
      <el-button type="warning" @click="openImageUpload">测试图片消息</el-button>
      <el-button type="primary" @click="openReadReceiptTest" :disabled="!connected">测试已读回执</el-button>
    </div>

    <div class="subscription-panel" v-if="connected">
      <h3>订阅测试</h3>
      <div class="subscription-form">
        <el-input v-model="subscriptionPath" placeholder="输入订阅路径">
          <template #prepend>路径</template>
        </el-input>
        <el-button type="primary" @click="subscribe">订阅</el-button>
      </div>

      <div class="quick-subscriptions">
        <h4>快速订阅</h4>
        <div class="quick-buttons">
          <el-button size="small" @click="subscriptionPath = '/user/queue/private'; subscribe()">
            /user/queue/private
          </el-button>
          <el-button size="small" @click="subscriptionPath = '/user/queue/notifications'; subscribe()">
            /user/queue/notifications
          </el-button>
          <el-button size="small" @click="subscriptionPath = '/topic/broadcast'; subscribe()">
            /topic/broadcast
          </el-button>
          <el-button size="small" @click="subscriptionPath = '/user/queue/sent'; subscribe()">
            /user/queue/sent
          </el-button>
          <el-button size="small" @click="subscriptionPath = '/user/queue/read-receipts'; subscribe()">
            /user/queue/read-receipts
          </el-button>
        </div>
      </div>

      <div class="active-subscriptions" v-if="subscriptions.length > 0">
        <h4>当前订阅 ({{ subscriptions.length }})</h4>
        <div class="subscription-list">
          <el-tag
            v-for="(sub, index) in subscriptions"
            :key="index"
            closable
            @close="unsubscribe(index)"
            class="subscription-tag"
          >
            {{ sub.destination }}
          </el-tag>
        </div>
      </div>
    </div>

    <div class="send-panel" v-if="connected">
      <h3>发送测试</h3>
      <div class="send-form">
        <el-input v-model="destination" placeholder="输入目标路径">
          <template #prepend>目标</template>
        </el-input>
        <el-input v-model="message" placeholder="输入消息内容" type="textarea" :rows="5">
          <template #prepend>消息</template>
        </el-input>
        <div class="message-templates">
          <h4>快速模板</h4>
          <div class="template-buttons">
            <el-button size="small" @click="useMessageTemplate('self')">
              发给自己
            </el-button>
            <el-button size="small" @click="useMessageTemplate('other')">
              发给他人
            </el-button>
            <el-button size="small" @click="useMessageTemplate('text')">
              纯文本
            </el-button>
          </div>
        </div>
        <el-button type="success" @click="sendMessage">发送</el-button>
      </div>
    </div>

    <div class="log-panel">
      <h3>日志</h3>
      <div class="log-list" ref="logList">
        <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ formatTime(log.time) }}</span>
          <span class="log-message" v-html="log.message"></span>
        </div>
      </div>
    </div>

    <!-- 图片上传对话框 -->
    <el-dialog
      v-model="imageUploadVisible"
      title="发送图片消息"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <div v-if="imageUploadVisible" class="image-upload-form">
        <div class="form-item">
          <span class="label">发送者ID:</span>
          <el-input v-model.number="imageForm.senderId" disabled />
        </div>

        <div class="form-item">
          <span class="label">接收者ID:</span>
          <el-input v-model.number="imageForm.receiverId" placeholder="请输入接收者ID" />
        </div>

        <div class="form-item">
          <span class="label">消息内容:</span>
          <el-input v-model="imageForm.message" type="textarea" :rows="2" placeholder="可选的图片描述" />
        </div>

        <div class="form-item">
          <span class="label">图片:</span>
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :limit="1"
            :on-change="handleImageChange"
            :on-remove="handleImageRemove"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </div>
      </div>

      <template #footer>
        <span>
          <el-button @click="imageUploadVisible = false">取消</el-button>
          <el-button type="primary" @click="sendImageMessage" :loading="sending" :disabled="!selectedImage">
            发送图片
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 已读回执测试对话框 -->
    <el-dialog
      v-model="readReceiptVisible"
      title="发送已读回执"
      width="500px"
      append-to-body
      destroy-on-close
    >
      <div v-if="readReceiptVisible" class="read-receipt-form">
        <div class="form-item">
          <span class="label">读取者ID:</span>
          <el-input v-model.number="readReceiptForm.readerId" disabled />
        </div>

        <div class="form-item">
          <span class="label">发送者ID:</span>
          <el-input v-model.number="readReceiptForm.senderId" placeholder="请输入消息发送者ID" />
        </div>

        <div class="form-item">
          <span class="label">消息ID:</span>
          <el-input v-model="readReceiptForm.messageIds" type="textarea" :rows="2" placeholder="请输入消息ID，多个ID用逗号分隔" />
        </div>
      </div>

      <template #footer>
        <span>
          <el-button @click="readReceiptVisible = false">取消</el-button>
          <el-button type="primary" @click="sendReadReceipt" :loading="sending">
            发送已读回执
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useUserStore } from '@/stores'
import { Client } from '@stomp/stompjs'
import SockJS from 'sockjs-client'
import { Plus } from '@element-plus/icons-vue'
import { sendPrivateMessage } from '@/api/chat'

// 状态
const connected = ref(false)
const logs = ref([])
const logList = ref(null)
const stompClient = ref(null)
const subscriptions = ref([])
const subscriptionPath = ref('/user/queue/private')
const destination = ref('/app/privateMessage')
const message = ref('{"senderId":5,"receiverId":"8","message":"测试消息","messageType":"TEXT"}')

// 图片上传相关
const imageUploadVisible = ref(false)
const selectedImage = ref(null)
const sending = ref(false)
const imageForm = ref({
  senderId: null,
  receiverId: null,
  message: '',
  messageType: 'IMAGE'
})

// 已读回执相关
const readReceiptVisible = ref(false)
const readReceiptForm = ref({
  readerId: null,
  senderId: null,
  messageIds: ''
})

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo || {})

// 添加日志
const addLog = (message, type = 'info') => {
  logs.value.push({
    time: new Date(),
    message,
    type
  })

  // 滚动到底部
  nextTick(() => {
    if (logList.value) {
      logList.value.scrollTop = logList.value.scrollHeight
    }
  })
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleTimeString('zh-CN', { hour12: false }) + '.' + date.getMilliseconds().toString().padStart(3, '0')
}

// 连接WebSocket
const connect = async () => {
  try {
    addLog('开始连接WebSocket...')

    // 获取Token
    const token = userStore.token
    if (!token) {
      addLog('未找到用户Token，无法连接', 'error')
      return
    }

    // 创建SockJS实例
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081'
    const wsUrl = `${apiBaseUrl}/ws?token=${encodeURIComponent(token)}&t=${Date.now()}`
    addLog(`连接URL: ${wsUrl.replace(token, '***')}`)

    const socket = new SockJS(wsUrl)

    // 创建STOMP客户端
    stompClient.value = new Client({
      webSocketFactory: () => socket,
      connectHeaders: {
        Authorization: `Bearer ${token}`
      },
      debug: (str) => {
        console.log(str)
        // 添加STOMP调试日志
        addLog(`<span class="stomp-debug">${str}</span>`, 'debug')
      },
      reconnectDelay: 5000,
      heartbeatIncoming: 25000,
      heartbeatOutgoing: 25000
    })

    // 连接成功回调
    stompClient.value.onConnect = (frame) => {
      addLog(`WebSocket连接成功: ${frame.command}`, 'success')
      addLog(`连接头信息: ${JSON.stringify(frame.headers)}`, 'info')
      connected.value = true

      // 自动订阅默认路径
      subscribe()
    }

    // 连接错误回调
    stompClient.value.onStompError = (frame) => {
      addLog(`WebSocket连接错误: ${frame.headers?.message || JSON.stringify(frame)}`, 'error')
      connected.value = false
    }

    // 连接断开回调
    stompClient.value.onWebSocketClose = (event) => {
      addLog(`WebSocket连接断开: ${event.code} ${event.reason}`, 'warning')
      connected.value = false
    }

    // 激活连接
    stompClient.value.activate()
    addLog('WebSocket连接请求已发送')
  } catch (error) {
    addLog(`WebSocket连接失败: ${error.message}`, 'error')
    console.error('WebSocket连接失败:', error)
    connected.value = false
  }
}

// 断开连接
const disconnect = () => {
  try {
    if (stompClient.value) {
      // 取消所有订阅
      subscriptions.value.forEach(sub => {
        try {
          sub.unsubscribe()
        } catch (error) {
          console.warn('取消订阅失败:', error)
        }
      })
      subscriptions.value = []

      // 断开连接
      stompClient.value.deactivate()
      addLog('WebSocket连接已断开', 'warning')
    }

    connected.value = false
  } catch (error) {
    addLog(`断开连接失败: ${error.message}`, 'error')
    console.error('断开连接失败:', error)
  }
}

// 订阅
const subscribe = () => {
  if (!connected.value || !stompClient.value) {
    addLog('WebSocket未连接，无法订阅', 'error')
    return
  }

  try {
    const path = subscriptionPath.value
    addLog(`开始订阅: ${path}`)

    // 检查是否已经订阅了该路径
    const existingSubscription = subscriptions.value.find(sub => sub.destination === path)
    if (existingSubscription) {
      addLog(`已经订阅了该路径: ${path}`, 'warning')
      return
    }

    const subscription = stompClient.value.subscribe(path, (message) => {
      addLog(`收到消息: ${message.headers.destination}`, 'success')
      addLog(`消息头: ${JSON.stringify(message.headers)}`, 'info')
      addLog(`消息体: ${message.body}`, 'info')

      try {
        const data = JSON.parse(message.body)
        addLog(`解析后的消息: ${JSON.stringify(data, null, 2)}`, 'success')
      } catch (error) {
        addLog(`解析消息失败: ${error.message}`, 'error')
      }
    })

    // 保存订阅信息
    subscriptions.value.push({
      id: subscription.id,
      destination: path,
      subscription: subscription
    })

    addLog(`订阅成功: ${path}`, 'success')
  } catch (error) {
    addLog(`订阅失败: ${error.message}`, 'error')
    console.error('订阅失败:', error)
  }
}

// 取消订阅
const unsubscribe = (index) => {
  try {
    const subscription = subscriptions.value[index]
    if (subscription && subscription.subscription) {
      subscription.subscription.unsubscribe()
      addLog(`已取消订阅: ${subscription.destination}`, 'warning')

      // 从列表中移除
      subscriptions.value.splice(index, 1)
    }
  } catch (error) {
    addLog(`取消订阅失败: ${error.message}`, 'error')
    console.error('取消订阅失败:', error)
  }
}

// 使用消息模板
const useMessageTemplate = (type) => {
  try {
    const userId = userInfo.value.id

    if (!userId) {
      addLog('未找到用户ID，无法使用模板', 'error')
      return
    }

    switch (type) {
      case 'self':
        // 发给自己的消息
        message.value = JSON.stringify({
          senderId: userId,
          receiverId: userId,
          message: `测试消息 ${new Date().toLocaleTimeString()}`,
          messageType: 'TEXT',
          clientMessageId: `test-${Date.now()}`
        }, null, 2)
        break

      case 'other':
        // 发给他人的消息（需要修改receiverId）
        message.value = JSON.stringify({
          senderId: userId,
          receiverId: '请修改为接收者ID',
          message: `测试消息 ${new Date().toLocaleTimeString()}`,
          messageType: 'TEXT',
          clientMessageId: `test-${Date.now()}`
        }, null, 2)
        break

      case 'text':
        // 纯文本消息
        message.value = `测试消息 ${new Date().toLocaleTimeString()}`
        break
    }

    addLog(`已使用${type}消息模板`, 'info')
  } catch (error) {
    addLog(`使用消息模板失败: ${error.message}`, 'error')
    console.error('使用消息模板失败:', error)
  }
}

// 发送消息
const sendMessage = () => {
  if (!connected.value || !stompClient.value) {
    addLog('WebSocket未连接，无法发送消息', 'error')
    return
  }

  try {
    const dest = destination.value
    const msg = message.value

    addLog(`准备发送消息到: ${dest}`, 'info')
    addLog(`消息内容: ${msg}`, 'info')

    // 尝试解析JSON
    let body = msg
    try {
      JSON.parse(msg) // 只是验证是否为有效JSON
      // 如果是有效JSON，直接使用字符串
    } catch (error) {
      // 如果不是有效JSON，尝试作为普通文本发送
      body = JSON.stringify({ text: msg })
      addLog(`消息不是有效JSON，将作为文本发送: ${body}`, 'warning')
    }

    // 发送消息
    stompClient.value.publish({
      destination: dest,
      headers: {
        'content-type': 'application/json'
      },
      body: body
    })

    addLog(`消息已发送`, 'success')
  } catch (error) {
    addLog(`发送消息失败: ${error.message}`, 'error')
    console.error('发送消息失败:', error)
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  addLog('日志已清空')
}

// 测试发送给自己
const testSendToSelf = async () => {
  try {
    if (!userInfo.value || !userInfo.value.id) {
      addLog('用户未登录，无法发送测试消息', 'error')
      return
    }

    addLog('开始发送测试消息给自己...', 'info')

    const response = await fetch(`/api/websocket-test/send-to-user?userId=${userInfo.value.id}&message=测试消息-${Date.now()}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    const result = await response.json()

    if (result.code === 200) {
      addLog(`测试消息发送成功: ${JSON.stringify(result.data)}`, 'success')

      // 显示在线用户
      if (result.data && result.data.onlineUsers) {
        addLog(`当前在线用户: ${JSON.stringify(result.data.onlineUsers)}`, 'info')
      }
    } else {
      addLog(`测试消息发送失败: ${result.message}`, 'error')
    }
  } catch (error) {
    addLog(`测试消息发送出错: ${error.message}`, 'error')
    console.error('测试消息发送出错:', error)
  }
}

// 测试广播
const testBroadcast = async () => {
  try {
    addLog('开始广播测试消息...', 'info')

    const response = await fetch(`/api/websocket-test/broadcast?message=广播测试-${Date.now()}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    const result = await response.json()

    if (result.code === 200) {
      addLog(`广播消息发送成功: ${JSON.stringify(result.data)}`, 'success')
    } else {
      addLog(`广播消息发送失败: ${result.message}`, 'error')
    }
  } catch (error) {
    addLog(`广播消息发送出错: ${error.message}`, 'error')
    console.error('广播消息发送出错:', error)
  }
}

// 获取在线用户
const getOnlineUsers = async () => {
  try {
    addLog('获取在线用户...', 'info')

    const response = await fetch('/api/websocket-test/online-users', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    const result = await response.json()

    if (result.code === 200) {
      addLog(`在线用户获取成功: ${JSON.stringify(result.data)}`, 'success')
    } else {
      addLog(`在线用户获取失败: ${result.message}`, 'error')
    }
  } catch (error) {
    addLog(`获取在线用户出错: ${error.message}`, 'error')
    console.error('获取在线用户出错:', error)
  }
}

// 打开图片上传对话框
const openImageUpload = () => {
  // 设置默认发送者ID
  imageForm.value.senderId = userInfo.value.id

  // 清空其他字段
  imageForm.value.receiverId = null
  imageForm.value.message = ''
  selectedImage.value = null

  // 显示对话框
  imageUploadVisible.value = true
}

// 打开已读回执测试对话框
const openReadReceiptTest = () => {
  // 设置默认读取者ID（当前用户）
  readReceiptForm.value.readerId = userInfo.value.id

  // 清空其他字段
  readReceiptForm.value.senderId = null
  readReceiptForm.value.messageIds = ''

  // 显示对话框
  readReceiptVisible.value = true
}

// 处理图片选择
const handleImageChange = (file) => {
  selectedImage.value = file.raw
  addLog(`已选择图片: ${file.name}, 大小: ${formatFileSize(file.size)}`, 'info')
}

// 处理图片移除
const handleImageRemove = () => {
  selectedImage.value = null
  addLog('已移除图片', 'warning')
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  }
}

// 发送图片消息
const sendImageMessage = async () => {
  if (!imageForm.value.senderId || !imageForm.value.receiverId) {
    addLog('请填写发送者ID和接收者ID', 'error')
    return
  }

  if (!selectedImage.value) {
    addLog('请选择一张图片', 'error')
    return
  }

  try {
    sending.value = true
    addLog('开始发送图片消息...', 'info')

    // 创建FormData对象
    const formData = new FormData()
    formData.append('senderId', imageForm.value.senderId)
    formData.append('receiverId', imageForm.value.receiverId)
    formData.append('message', imageForm.value.message || '图片消息')
    formData.append('messageType', 'IMAGE')
    formData.append('file', selectedImage.value)

    // 获取token
    const token = localStorage.getItem('token');
    addLog(`使用的token: ${token ? token.substring(0, 10) + '...' : 'null'}`, 'info');

    // 直接发送图片消息
    addLog(`准备发送请求到: /api/chat/privateMessage`, 'info');
    addLog(`请求参数: senderId=${imageForm.value.senderId}, receiverId=${imageForm.value.receiverId}, messageType=IMAGE`, 'info');

    try {
      // 发送图片消息
      addLog(`开始发送图片消息...`, 'info');

      // 使用chat.js中的sendPrivateMessage函数
      addLog(`使用chat.js中的sendPrivateMessage函数发送图片消息`, 'info');

      // 发送请求
      const response = await sendPrivateMessage(formData);
      addLog(`收到响应: ${JSON.stringify(response)}`, 'info');

      if (response && response.code === 200) {
        addLog(`图片消息发送成功: ${JSON.stringify(response.data)}`, 'success');

        // 关闭对话框
        imageUploadVisible.value = false;

        // 清空表单
        selectedImage.value = null;
      } else {
        addLog(`图片消息发送失败: ${response.data ? response.data.message : '未知错误'}`, 'error');
      }
    } catch (error) {
      if (error.response) {
        // 服务器返回了错误状态码
        addLog(`服务器返回错误: ${error.response.status} - ${error.response.statusText}`, 'error');
        addLog(`错误详情: ${JSON.stringify(error.response.data)}`, 'error');
      } else if (error.request) {
        // 请求已发送但没有收到响应
        addLog(`未收到服务器响应: ${error.request}`, 'error');
      } else {
        // 设置请求时发生错误
        addLog(`请求设置错误: ${error.message}`, 'error');
      }
      throw error; // 重新抛出错误，让外层catch捕获
    }

    // 响应处理已经在try块中完成
  } catch (error) {
    addLog(`发送图片消息出错: ${error.message}`, 'error')
    console.error('发送图片消息出错:', error)
  } finally {
    sending.value = false
  }
}

// 发送已读回执
const sendReadReceipt = async () => {
  if (!connected.value || !stompClient.value) {
    addLog('WebSocket未连接，无法发送已读回执', 'error')
    return
  }

  if (!readReceiptForm.value.readerId || !readReceiptForm.value.senderId || !readReceiptForm.value.messageIds) {
    addLog('请填写完整的已读回执信息', 'error')
    return
  }

  try {
    sending.value = true
    addLog('准备发送已读回执...', 'info')

    // 解析消息ID列表 - 确保转换为数字类型
    const messageIds = readReceiptForm.value.messageIds.split(',')
      .map(id => id.trim())
      .filter(id => id)
      .map(id => Number(id)) // 强制转换为数字类型
      .filter(id => !isNaN(id)) // 过滤掉无效的数字

    if (messageIds.length === 0) {
      addLog('请输入至少一个有效的消息ID', 'error')
      sending.value = false
      return
    }

    // 创建已读回执对象 - 确保ID是数字类型
    const readReceipt = {
      messageIds: messageIds,
      readerId: Number(readReceiptForm.value.readerId), // 确保是数字
      senderId: Number(readReceiptForm.value.senderId), // 确保是数字
      timestamp: Date.now() // 使用毫秒时间戳而不是ISO字符串
    }

    addLog(`已读回执内容: ${JSON.stringify(readReceipt, null, 2)}`, 'info')

    // 确保订阅了正确的回执主题
    const hasReadReceiptSubscription = subscriptions.value.some(
      sub => sub.destination === '/user/queue/read-receipts'
    )

    if (!hasReadReceiptSubscription) {
      addLog('警告: 未订阅 /user/queue/read-receipts 主题，可能无法接收到回执确认', 'warning')
      // 自动订阅
      subscriptionPath.value = '/user/queue/read-receipts'
      subscribe()
    }

    // 发送已读回执
    stompClient.value.publish({
      destination: '/app/read-receipt',
      headers: {
        'content-type': 'application/json'
      },
      body: JSON.stringify(readReceipt)
    })

    addLog('已读回执已发送', 'success')

    // 添加调试信息
    addLog(`发送的目标: /app/read-receipt`, 'info')
    addLog(`发送的内容类型: application/json`, 'info')
    addLog(`发送的消息体: ${JSON.stringify(readReceipt)}`, 'info')

    // 关闭对话框
    readReceiptVisible.value = false
  } catch (error) {
    addLog(`发送已读回执失败: ${error.message}`, 'error')
    console.error('发送已读回执失败:', error)
  } finally {
    sending.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  addLog('WebSocket简单测试页面已加载')
})

onUnmounted(() => {
  disconnect()
})
</script>

<style scoped>
.simple-test-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.status-panel {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.status-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: bold;
  margin-right: 5px;
}

.connected {
  color: #67c23a;
  font-weight: bold;
}

.disconnected {
  color: #f56c6c;
  font-weight: bold;
}

.action-panel {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.subscription-panel, .send-panel {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.subscription-form, .send-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.quick-subscriptions, .message-templates {
  margin-top: 15px;
}

.quick-buttons, .template-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.active-subscriptions {
  margin-top: 15px;
}

.subscription-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.subscription-tag {
  margin-bottom: 5px;
}

.log-panel {
  margin-bottom: 20px;
}

.log-list {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  background-color: #fff;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 5px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
}

.log-message {
  white-space: pre-wrap;
  word-break: break-all;
}

.info {
  color: #606266;
}

.success {
  color: #67c23a;
}

.warning {
  color: #e6a23c;
}

.error {
  color: #f56c6c;
}

.debug {
  color: #909399;
  font-size: 10px;
}

.stomp-debug {
  opacity: 0.7;
}

/* 图片上传表单和已读回执表单样式 */
.image-upload-form,
.read-receipt-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-item {
  display: flex;
  align-items: flex-start;
}

.form-item .label {
  width: 80px;
  text-align: right;
  padding-right: 10px;
  line-height: 32px;
}

/* 确保上传组件正确渲染 */
.el-upload--picture-card {
  --el-upload-picture-card-size: 100px;
  height: var(--el-upload-picture-card-size);
  width: var(--el-upload-picture-card-size);
}
</style>
