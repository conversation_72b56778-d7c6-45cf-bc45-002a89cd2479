/**
 * 文件上传相关的组合式函数
 * 提供文件选择、预览和上传功能
 */
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import logger from '@/utils/logger'

// 创建文件上传日志记录器
const uploadLogger = logger.createLogger('FileUpload')

// 文件类型和大小限制
const FILE_CONFIG = {
  IMAGE: {
    maxSize: 10, // MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    typeLabel: '图片',
    formatHint: 'jpg/png/gif/webp格式图片'
  },
  DOCUMENT: {
    maxSize: 20, // MB
    allowedTypes: null, // 允许所有类型
    typeLabel: '文件',
    formatHint: '所有文件格式'
  },
  AUDIO: {
    maxSize: 20, // MB
    allowedTypes: ['audio/mpeg', 'audio/wav', 'audio/ogg'],
    typeLabel: '音频',
    formatHint: 'mp3/wav/ogg格式音频'
  },
  VIDEO: {
    maxSize: 50, // MB
    allowedTypes: ['video/mp4', 'video/webm', 'video/ogg'],
    typeLabel: '视频',
    formatHint: 'mp4/webm/ogg格式视频'
  }
}

/**
 * 文件上传组合式函数
 * @param {Object} options 配置选项
 * @returns {Object} 文件上传相关的状态和方法
 */
export function useFileUpload(options = {}) {
  // 文件上传状态
  const previewList = ref([])
  const uploadPreview = ref({
    file: null,
    type: null,
    url: null
  })
  const selectedFile = ref(null)
  const selectedImage = ref(null)
  const fileUploadVisible = ref(false)
  const imageUploadVisible = ref(false)
  
  /**
   * 处理文件选择
   * @param {Event} event 文件选择事件
   * @param {string} fileType 文件类型
   */
  const handleFileSelect = (event, fileType) => {
    const files = Array.from(event.target.files)
    const config = FILE_CONFIG[fileType]
    
    if (!config) {
      uploadLogger.error('未知文件类型:', fileType)
      return
    }
    
    files.forEach(file => {
      // 检查文件大小
      if (file.size > config.maxSize * 1024 * 1024) {
        ElMessage.error(`${config.typeLabel}不能超过${config.maxSize}MB`)
        return
      }
      
      // 检查文件类型
      if (config.allowedTypes && !config.allowedTypes.includes(file.type)) {
        ElMessage.error(`仅支持${config.formatHint}`)
        return
      }
      
      // 添加到预览列表
      previewList.value.push({
        file,
        type: fileType,
        url: shouldCreateObjectURL(file) ? URL.createObjectURL(file) : null,
        progress: 0,
        status: 'ready',
        errorMsg: ''
      })
    })
    
    // 重置文件输入框
    event.target.value = null
  }
  
  /**
   * 判断是否应该创建对象URL
   * @param {File} file 文件对象
   * @returns {boolean} 是否应该创建对象URL
   */
  const shouldCreateObjectURL = (file) => {
    return file.type.startsWith('image/') || 
           file.type.startsWith('video/') || 
           file.type.startsWith('audio/')
  }
  
  /**
   * 处理图片选择
   * @param {Event} event 文件选择事件
   */
  const handleImageSelect = (event) => {
    const file = event.target.files[0]
    if (!file) return
    
    const config = FILE_CONFIG.IMAGE
    
    // 检查文件大小
    if (file.size > config.maxSize * 1024 * 1024) {
      ElMessage.error(`图片不能超过${config.maxSize}MB`)
      return
    }
    
    // 检查文件类型
    if (!config.allowedTypes.includes(file.type)) {
      ElMessage.error(`仅支持${config.formatHint}`)
      return
    }
    
    // 更新选中的图片
    selectedImage.value = file
    
    // 创建预览URL
    if (uploadPreview.value.url) {
      URL.revokeObjectURL(uploadPreview.value.url)
    }
    
    uploadPreview.value = {
      file,
      type: 'IMAGE',
      url: URL.createObjectURL(file)
    }
    
    // 显示图片上传对话框
    imageUploadVisible.value = true
    
    // 重置文件输入框
    event.target.value = null
  }
  
  /**
   * 处理文档选择
   * @param {Event} event 文件选择事件
   */
  const handleDocumentSelect = (event) => {
    const file = event.target.files[0]
    if (!file) return
    
    const config = FILE_CONFIG.DOCUMENT
    
    // 检查文件大小
    if (file.size > config.maxSize * 1024 * 1024) {
      ElMessage.error(`文件不能超过${config.maxSize}MB`)
      return
    }
    
    // 更新选中的文件
    selectedFile.value = file
    
    // 显示文件上传对话框
    fileUploadVisible.value = true
    
    // 重置文件输入框
    event.target.value = null
  }
  
  /**
   * 清除预览
   */
  const clearUploadPreview = () => {
    if (uploadPreview.value.url) {
      URL.revokeObjectURL(uploadPreview.value.url)
    }
    uploadPreview.value = {
      file: null,
      type: null,
      url: null
    }
  }
  
  /**
   * 移除预览
   * @param {number} index 预览索引
   */
  const removePreview = (index) => {
    const item = previewList.value[index]
    if (item.url) {
      URL.revokeObjectURL(item.url)
    }
    previewList.value.splice(index, 1)
  }
  
  /**
   * 上传单个文件
   * @param {Object} item 文件项
   * @param {number} index 文件索引
   * @param {Object} options 上传选项
   */
  const uploadSingleFile = async (item, index, options = {}) => {
    const { onStart, onProgress, onSuccess, onError, messageDTO } = options
    
    uploadLogger.info(`开始上传第 ${index + 1} 个文件:`, item.file.name)
    
    // 更新上传状态
    item.status = 'uploading'
    item.progress = 0
    
    if (onStart) {
      onStart(item, index)
    }
    
    try {
      // 创建FormData
      const formData = new FormData()
      formData.append('file', item.file)
      formData.append('senderId', messageDTO.senderId)
      formData.append('receiverId', messageDTO.receiverId)
      formData.append('message', messageDTO.message)
      formData.append('messageType', messageDTO.messageType)
      
      if (messageDTO.clientMessageId) {
        formData.append('clientMessageId', messageDTO.clientMessageId)
      }
      
      // 发送请求
      const response = await axios.post('/api/chat/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (e) => {
          const progress = Math.round((e.loaded * 100) / e.total)
          item.progress = progress
          
          if (onProgress) {
            onProgress(progress, item, index)
          }
        }
      })
      
      // 更新状态
      item.status = 'success'
      item.progress = 100
      
      uploadLogger.info(`文件 ${index + 1} 上传成功:`, response.data)
      
      if (onSuccess) {
        onSuccess(response.data, item, index)
      }
      
      return response.data
    } catch (error) {
      uploadLogger.error(`文件 ${index + 1} 上传失败:`, error)
      
      item.status = 'error'
      item.errorMsg = error.message || '上传失败，请重试'
      
      if (onError) {
        onError(error, item, index)
      }
      
      throw error
    }
  }
  
  /**
   * 批量上传文件
   * @param {Object} options 上传选项
   */
  const uploadAllFiles = async (options = {}) => {
    for (let i = 0; i < previewList.value.length; i++) {
      const item = previewList.value[i]
      if (item.status === 'ready' || item.status === 'error') {
        await uploadSingleFile(item, i, options)
      }
    }
  }
  
  /**
   * 格式化文件大小
   * @param {number} size 文件大小（字节）
   * @returns {string} 格式化后的文件大小
   */
  const formatFileSize = (size) => {
    if (size < 1024) {
      return size + ' B'
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + ' KB'
    } else {
      return (size / (1024 * 1024)).toFixed(2) + ' MB'
    }
  }
  
  return {
    // 状态
    previewList,
    uploadPreview,
    selectedFile,
    selectedImage,
    fileUploadVisible,
    imageUploadVisible,
    
    // 方法
    handleFileSelect,
    handleImageSelect,
    handleDocumentSelect,
    clearUploadPreview,
    removePreview,
    uploadSingleFile,
    uploadAllFiles,
    formatFileSize,
    
    // 配置
    FILE_CONFIG
  }
}

export default useFileUpload
