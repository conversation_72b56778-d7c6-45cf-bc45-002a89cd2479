import request from '@/utils/request'

// 获取拾物列表
export function getFoundItems(params) {
  return request({
    url: '/found-items/search',
    method: 'get',
    params
  })
}

// 获取拾物详情
export function getFoundItemDetail(id) {
  return request({
    url: `/found-items/detail/${id}`,
    method: 'get'
  })
}

// 发布拾物信息
export function publishFoundItem(data) {
  return request({
    url: '/found-items/publish',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 更新拾物信息
 * @param {String} id 拾物ID
 * @param {Object} data 更新的数据
 * @returns {Promise} 返回请求结果
 */
export function updateFoundItem(id, data) {
  console.log('更新拾物信息:', { id, data })
  return request({
    url: `/found-items/update/${id}`,
    method: 'put',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).catch(error => {
    console.error('更新拾物信息失败:', error)
    throw error
  })
}

/**
 * 删除拾物信息
 * @param {String} id 拾物ID
 * @returns {Promise} 返回请求结果
 */
export function deleteFoundItem(id) {
  return request({
    url: `/found-items/delete/${id}`,
    method: 'delete'
  })
}

// 认领拾物
export function claimFoundItem(id) {
  return request({
    url: `/found-items/claim/${id}`,
    method: 'post'
  })
}

/**
 * 更新拾物图片
 * @param {String} id 拾物ID
 * @param {Object} data 图片数据
 * @returns {Promise} 返回请求结果
 */
export function updateFoundItemImages(id, data) {
  console.log('更新拾物图片:', { id, data })
  return request({
    url: `/found-items/update-images/${id}`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).catch(error => {
    console.error('更新拾物图片失败:', error)
    throw error
  })
}

/**
 * 更新拾物状态（未认领/已认领）
 * @param {String} id 拾物ID
 * @param {String} status 状态值（UNCLAIMED/RETURNED）
 * @returns {Promise} 返回请求结果
 */
export function updateFoundItemStatus(id, status) {
  return request({
    url: `/found-items/${id}/status`,
    method: 'put',
    data: { status }
  })
}