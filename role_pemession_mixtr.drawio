<mxfile host="65bd71144e">
    <diagram id="C5RBs43oDa-KdzZeNtuy" name="角色权限矩阵图">
        <mxGraphModel dx="1223" dy="871" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
                <mxCell id="0" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;失物招领系统角色权限矩阵&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="314" y="40" width="200" height="30" as="geometry" />
                </mxCell>
                <mxCell id="1" value="" style="shape=table;startSize=0;container=1;collapsible=0;childLayout=tableLayout;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="120" y="90" width="600" height="610" as="geometry" />
                </mxCell>
                <mxCell id="2" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry width="600" height="40" as="geometry" />
                </mxCell>
                <mxCell id="3" value="&lt;b&gt;功能/权限&lt;/b&gt;" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#dae8fc;top=0;left=0;bottom=0;right=0;pointerEvents=1;strokeColor=#6c8ebf;" vertex="1" parent="2">
                    <mxGeometry width="300" height="40" as="geometry">
                        <mxRectangle width="300" height="40" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="&lt;b&gt;普通用户&lt;/b&gt;" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#dae8fc;top=0;left=0;bottom=0;right=0;pointerEvents=1;strokeColor=#6c8ebf;" vertex="1" parent="2">
                    <mxGeometry x="300" width="100" height="40" as="geometry">
                        <mxRectangle width="100" height="40" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="&lt;b&gt;管理员&lt;/b&gt;" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#dae8fc;top=0;left=0;bottom=0;right=0;pointerEvents=1;strokeColor=#6c8ebf;" vertex="1" parent="2">
                    <mxGeometry x="400" width="100" height="40" as="geometry">
                        <mxRectangle width="100" height="40" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="&lt;b&gt;超级管理员&lt;/b&gt;" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#dae8fc;top=0;left=0;bottom=0;right=0;pointerEvents=1;strokeColor=#6c8ebf;" vertex="1" parent="2">
                    <mxGeometry x="500" width="100" height="40" as="geometry">
                        <mxRectangle width="100" height="40" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 用户管理分类标题 -->
                <mxCell id="7" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="40" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="8" value="&lt;b&gt;用户管理&lt;/b&gt;" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="7">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="9" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="7">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="7">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="7">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <!-- 注册账号 -->
                <mxCell id="12" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="70" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="13" value="注册账号" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="12">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="12">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="12">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="12">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 登录系统 -->
                <mxCell id="17" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="100" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="18" value="登录系统" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="17">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="17">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="17">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="17">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 修改个人信息 -->
                <mxCell id="22" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="130" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="23" value="修改个人信息" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="22">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="22">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="22">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="22">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <!-- 查看用户列表 -->
                <mxCell id="27" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="160" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="28" value="查看用户列表" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="27">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="29" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="27">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="30" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="27">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="27">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 启用/禁用用户 -->
                <mxCell id="32" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="190" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="33" value="启用/禁用用户" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="32">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="34" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="32">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="35" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="32">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="32">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 重置用户密码 -->
                <mxCell id="37" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="220" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="38" value="重置用户密码" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="37">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="39" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="37">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="37">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="37">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <!-- 修改用户角色 -->
                <mxCell id="42" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="250" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="43" value="修改用户角色" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="42">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="42">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="42">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="46" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="42">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 添加管理员 -->
                <mxCell id="47" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="280" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="48" value="添加管理员" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="47">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="49" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="47">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="50" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="47">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="51" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="47">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 删除用户 -->
                <mxCell id="52" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="310" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="53" value="删除用户" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="52">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="54" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="52">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="55" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="52">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="56" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="52">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <!-- 失物/拾物管理分类标题 -->
                <mxCell id="57" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="340" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="58" value="&lt;b&gt;失物/拾物管理&lt;/b&gt;" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="57">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="59" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="57">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="60" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="57">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="61" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="57">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 发布失物信息 -->
                <mxCell id="62" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="370" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="63" value="发布失物信息" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="62">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="64" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="62">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="65" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="62">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="66" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="62">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 发布拾物信息 -->
                <mxCell id="67" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="400" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="68" value="发布拾物信息" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="67">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="69" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="67">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="70" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="67">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="71" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="67">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 搜索物品 -->
                <mxCell id="72" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="430" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="73" value="搜索物品" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="72">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="74" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="72">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="75" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="72">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="76" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="72">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <!-- 审核物品信息 -->
                <mxCell id="77" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="460" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="78" value="审核物品信息" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="77">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="79" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="77">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="80" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="77">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="81" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="77">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 物品匹配分类标题 -->
                <mxCell id="82" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="490" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="83" value="&lt;b&gt;物品匹配&lt;/b&gt;" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="82">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="84" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="82">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="85" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="82">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="86" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="82">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 使用智能匹配 -->
                <mxCell id="87" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="520" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="88" value="使用智能匹配" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="87">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="89" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="87">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="90" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="87">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="91" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="87">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <!-- 即时通讯分类标题 -->
                <mxCell id="92" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="550" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="93" value="&lt;b&gt;即时通讯&lt;/b&gt;" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="92">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="94" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="92">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="95" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="92">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="96" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="92">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 发送私信 -->
                <mxCell id="97" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="580" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="98" value="发送私信" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="97">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="99" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="97">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="100" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="97">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="101" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="97">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 系统管理分类标题 -->
                <mxCell id="102" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="610" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="103" value="&lt;b&gt;系统管理&lt;/b&gt;" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=#f5f5f5;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="102">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="104" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="102">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="105" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="102">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="106" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="102">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 发送系统通知 -->
                <mxCell id="107" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="640" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="108" value="发送系统通知" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="107">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="109" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="107">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="110" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="107">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="111" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="107">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <!-- 系统配置管理 -->
                <mxCell id="112" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="1">
                    <mxGeometry y="670" width="600" height="30" as="geometry" />
                </mxCell>
                <mxCell id="113" value="系统配置管理" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="112">
                    <mxGeometry width="300" height="30" as="geometry">
                        <mxRectangle width="300" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="114" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="112">
                    <mxGeometry x="300" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="115" value="✗" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="112">
                    <mxGeometry x="400" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                <mxCell id="116" value="✓" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="112">
                    <mxGeometry x="500" width="100" height="30" as="geometry">
                        <mxRectangle width="100" height="30" as="alternateBounds" />
                    </mxGeometry>
                </mxCell>
                
                <mxCell id="117" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;图3-2 角色权限矩阵图&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
                    <mxGeometry x="364" y="720" width="160" height="30" as="geometry" />
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>