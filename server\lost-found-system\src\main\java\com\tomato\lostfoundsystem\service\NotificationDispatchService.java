package com.tomato.lostfoundsystem.service;

/**
 * 通知分发服务接口
 * 负责根据用户在线状态选择合适的通知发送方式
 */
public interface NotificationDispatchService {
    
    /**
     * 分发通知
     * 根据用户在线状态选择通过WebSocket发送或存储为离线通知
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param notificationId 通知ID
     * @param notificationType 通知类型
     */
    void dispatchNotification(Long userId, String title, String message, Long notificationId, String notificationType);
    
    /**
     * 分发通知（带有相关物品信息）
     * 根据用户在线状态选择通过WebSocket发送或存储为离线通知
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param notificationId 通知ID
     * @param notificationType 通知类型
     * @param relatedItemId 相关物品ID
     * @param relatedItemType 相关物品类型
     */
    void dispatchNotification(Long userId, String title, String message, Long notificationId, 
                             String notificationType, Long relatedItemId, String relatedItemType);
    
    /**
     * 分发通知（带有元数据）
     * 根据用户在线状态选择通过WebSocket发送或存储为离线通知
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param notificationId 通知ID
     * @param notificationType 通知类型
     * @param metadata 元数据JSON字符串
     */
    void dispatchNotification(Long userId, String title, String message, Long notificationId, 
                             String notificationType, String metadata);
    
    /**
     * 分发通知（完整版本）
     * 根据用户在线状态选择通过WebSocket发送或存储为离线通知
     *
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     * @param notificationId 通知ID
     * @param notificationType 通知类型
     * @param relatedItemId 相关物品ID
     * @param relatedItemType 相关物品类型
     * @param metadata 元数据JSON字符串
     */
    void dispatchNotification(Long userId, String title, String message, Long notificationId, 
                             String notificationType, Long relatedItemId, String relatedItemType, String metadata);
}
