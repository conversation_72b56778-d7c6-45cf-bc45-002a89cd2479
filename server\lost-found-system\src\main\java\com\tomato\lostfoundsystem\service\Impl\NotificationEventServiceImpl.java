package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.dto.NotificationDTO;
import com.tomato.lostfoundsystem.service.NotificationEventService;
import com.tomato.lostfoundsystem.service.RedisService;
import com.tomato.lostfoundsystem.websocket.NotificationWebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 通知事件服务实现类
 *
 * 该类作为通知系统的中间层，负责处理通知事件，解耦通知服务和WebSocket处理器
 */
@Slf4j
@Service
public class NotificationEventServiceImpl implements NotificationEventService {

    private NotificationWebSocketHandler notificationWebSocketHandler;
    private final RedisService redisService;
    private final ApplicationContext applicationContext;

    // 通知发送记录前缀
    private static final String NOTIFICATION_SENT_PREFIX = "notification:sent:";

    // 通知去重过期时间（秒）
    private static final int NOTIFICATION_DEDUPLICATION_EXPIRATION = 300; // 5分钟

    @Autowired
    public NotificationEventServiceImpl(
            RedisService redisService,
            ApplicationContext applicationContext) {
        this.redisService = redisService;
        this.applicationContext = applicationContext;
    }

    // 延迟初始化NotificationWebSocketHandler，避免循环依赖
    private NotificationWebSocketHandler getNotificationWebSocketHandler() {
        if (notificationWebSocketHandler == null) {
            notificationWebSocketHandler = applicationContext.getBean(NotificationWebSocketHandler.class);
        }
        return notificationWebSocketHandler;
    }

    @Override
    public void sendWebSocketNotification(Long userId, String title, String message, Long notificationId) {
        try {
            // 如果提供了通知ID，检查是否已经发送过
            if (notificationId != null) {
                if (isNotificationSent(userId, notificationId)) {
                    log.info("通知已经发送过，跳过重复发送 - 用户ID: {}, 通知ID: {}", userId, notificationId);
                    return;
                }
            }

            // 创建通知对象
            NotificationDTO notification = new NotificationDTO();
            notification.setUserId(userId);
            notification.setTitle(title);
            notification.setMessage(message);
            notification.setStatus("UNREAD");

            // 如果提供了通知ID，设置到通知对象中
            if (notificationId != null) {
                notification.setId(notificationId);
            }

            // 使用WebSocket处理器发送通知
            getNotificationWebSocketHandler().sendNotification(userId, notification);

            log.info("成功发送通知 - 用户ID: {}, 标题: {}, 通知ID: {}",
                    userId, title, notificationId != null ? notificationId : "未提供");

            // 如果提供了通知ID，标记为已发送
            if (notificationId != null) {
                markNotificationAsSent(userId, notificationId, NOTIFICATION_DEDUPLICATION_EXPIRATION);
            }
        } catch (Exception e) {
            log.error("发送通知失败 - 用户ID: {}, 标题: {}, 错误: {}",
                    userId, title, e.getMessage(), e);
        }
    }

    @Override
    public boolean isNotificationSent(Long userId, Long notificationId) {
        try {
            if (userId == null || notificationId == null) {
                log.warn("用户ID或通知ID为空，无法检查通知是否已发送");
                return false;
            }

            String key = NOTIFICATION_SENT_PREFIX + userId + ":" + notificationId;
            boolean isSent = redisService.hasKey(key);

            log.debug("检查通知是否已发送 - 用户ID: {}, 通知ID: {}, 结果: {}",
                    userId, notificationId, isSent ? "已发送" : "未发送");

            return isSent;
        } catch (Exception e) {
            log.error("检查通知是否已发送时出错 - 用户ID: {}, 通知ID: {}, 错误: {}",
                    userId, notificationId, e.getMessage(), e);
            // 出错时返回false，确保通知能够发送
            return false;
        }
    }

    @Override
    public void markNotificationAsSent(Long userId, Long notificationId, int expirationSeconds) {
        try {
            if (userId == null || notificationId == null) {
                log.warn("用户ID或通知ID为空，无法标记通知为已发送");
                return;
            }

            String key = NOTIFICATION_SENT_PREFIX + userId + ":" + notificationId;
            redisService.set(key, "1", expirationSeconds);

            log.debug("标记通知为已发送 - 用户ID: {}, 通知ID: {}, 过期时间: {}秒",
                    userId, notificationId, expirationSeconds);
        } catch (Exception e) {
            log.error("标记通知为已发送时出错 - 用户ID: {}, 通知ID: {}, 错误: {}",
                    userId, notificationId, e.getMessage(), e);
        }
    }
}
