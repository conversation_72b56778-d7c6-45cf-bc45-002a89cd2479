package com.tomato.lostfoundsystem.config;

import org.springframework.boot.web.server.Compression;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * API响应压缩配置
 * 启用GZIP压缩，减少网络传输数据量
 */
@Configuration
public class CompressionConfig {

    @Bean
    public WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> compressionCustomizer() {
        return factory -> {
            Compression compression = new Compression();

            // 启用压缩
            compression.setEnabled(true);

            // 设置压缩的MIME类型
            compression.setMimeTypes(new String[] {
                "text/html",
                "text/xml",
                "text/plain",
                "text/css",
                "text/javascript",
                "application/javascript",
                "application/json",
                "application/xml"
            });

            // 设置压缩的最小响应大小（默认为2048字节）
            compression.setMinResponseSize(org.springframework.util.unit.DataSize.ofBytes(1024));

            // 设置排除的用户代理
            compression.setExcludedUserAgents(new String[] {
                "MSIE 6.0"
            });

            factory.setCompression(compression);
        };
    }
}
