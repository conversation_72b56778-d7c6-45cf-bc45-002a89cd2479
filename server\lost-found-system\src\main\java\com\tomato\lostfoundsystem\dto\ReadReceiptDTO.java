package com.tomato.lostfoundsystem.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 已读回执DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReadReceiptDTO {
    private Long readerId;        // 阅读者ID（谁读了消息）
    private Long senderId;        // 发送者ID（谁发送的消息）
    private List<Long> messageIds; // 已读消息ID列表
    private Long timestamp;       // 已读时间戳
    
    public ReadReceiptDTO(Long readerId, Long senderId, List<Long> messageIds) {
        this.readerId = readerId;
        this.senderId = senderId;
        this.messageIds = messageIds;
        this.timestamp = System.currentTimeMillis();
    }
}
