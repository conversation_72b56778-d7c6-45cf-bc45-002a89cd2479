@startuml 失物招领系统数据流图

!define RECTANGLE rectangle
!define DATABASE database
!define CLOUD cloud
!define QUEUE queue
!define ARROW -->

skinparam backgroundColor white
skinparam defaultTextAlignment center
skinparam monochrome false
skinparam shadowing false
skinparam ArrowColor Black
skinparam ArrowThickness 1.5

title 校园失物招领系统数据流图

' 外部实体
rectangle "用户" as User #LightBlue
rectangle "管理员" as Admin #LightBlue

' 处理过程
rectangle "用户认证" as Auth #Yellow
rectangle "失物管理" as LostItem #Yellow
rectangle "招领管理" as FoundItem #Yellow
rectangle "智能匹配" as Match #Yellow
rectangle "即时通讯" as Chat #Yellow
rectangle "系统管理" as SysManage #Yellow

' 数据存储
database "用户数据" as UserDB #Pink
database "失物数据" as LostDB #Pink
database "招领数据" as FoundDB #Pink
database "匹配数据" as MatchDB #Pink
database "聊天数据" as ChatDB #Pink
database "系统数据" as SysDB #Pink

' 外部系统
cloud "阿里云OSS" as OSS #LightCyan
cloud "CLIP+FAISS服务" as CLIP #LightCyan
queue "Kafka消息队列" as Kafka #LightPink

' 数据流
' 用户认证流程
User ARROW Auth : 1.1 提交登录信息
Auth ARROW UserDB : 1.2 验证用户信息
Auth ARROW User : 1.3 返回认证结果

' 失物管理流程
User ARROW LostItem : 2.1 发布失物信息
LostItem ARROW OSS : 2.2 上传图片
LostItem ARROW LostDB : 2.3 保存失物信息
LostItem ARROW User : 2.4 返回发布结果

' 招领管理流程
User ARROW FoundItem : 3.1 发布招领信息
FoundItem ARROW OSS : 3.2 上传图片
FoundItem ARROW FoundDB : 3.3 保存招领信息
FoundItem ARROW User : 3.4 返回发布结果

' 智能匹配流程
User ARROW Match : 4.1 提交匹配请求
Match ARROW CLIP : 4.2 提取特征向量
CLIP ARROW Match : 4.3 返回特征向量
Match ARROW LostDB : 4.4a 查询失物数据
Match ARROW FoundDB : 4.4b 查询招领数据
Match ARROW MatchDB : 4.5 保存匹配结果
Match ARROW User : 4.6 返回匹配结果

' 即时通讯流程
User ARROW Chat : 5.1 发送消息
Chat ARROW OSS : 5.2 上传媒体文件
Chat ARROW ChatDB : 5.3 保存消息记录
Chat ARROW Kafka : 5.4 发布消息事件
Kafka ARROW Chat : 5.5 消费消息事件
Chat ARROW User : 5.6 推送消息

' 系统管理流程
Admin ARROW SysManage : 6.1 提交管理操作
SysManage ARROW SysDB : 6.2 执行管理操作
SysManage ARROW Admin : 6.3 返回操作结果

@enduml
