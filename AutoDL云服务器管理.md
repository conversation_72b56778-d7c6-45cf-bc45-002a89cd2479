# AutoDL云服务器管理指南

本文档提供了在AutoDL云服务器上管理失物招领系统的详细说明和命令，包括实例管理、服务启停和资源优化等内容。

## 目录

- [实例管理](#实例管理)
- [服务管理](#服务管理)
- [数据管理](#数据管理)
- [资源优化](#资源优化)
- [常见问题](#常见问题)
- [有用的命令](#有用的命令)

## 实例管理

### 启动实例

1. 登录到[AutoDL平台](https://www.autodl.com)
2. 在控制台找到你的实例
3. 点击"开机"按钮启动实例
4. 等待实例状态变为"运行中"

**注意**：实例开机后即开始计费，不使用时请及时关机以节省费用。

### 关闭实例

1. 登录到[AutoDL平台](https://www.autodl.com)
2. 在控制台找到你的实例
3. 点击"关机"按钮关闭实例
4. 等待实例状态变为"已关机"

**注意**：实例关机后停止计费，但存储空间仍会继续计费。

### 连接实例

#### 方法1：使用SSH

```bash
ssh username@your-instance-ip
```

#### 方法2：使用Web终端

1. 登录到AutoDL平台
2. 在控制台找到你的实例
3. 点击"Web终端"按钮

#### 方法3：使用JupyterLab

1. 登录到AutoDL平台
2. 在控制台找到你的实例
3. 点击"打开JupyterLab"按钮

## 服务管理

### 启动所有服务

登录到实例后，执行以下命令启动所有服务：

```bash
cd /path/to/your/project
./start_services.sh
```

### 单独启动CLIP+FAISS服务

如果只需要启动CLIP+FAISS图像匹配服务：

```bash
cd /path/to/clip_faiss_service
source clip_faiss_env/bin/activate
python clip_faiss_api.py
```

或使用启动脚本：

```bash
cd /path/to/clip_faiss_service
./start_clip_faiss.sh
```

### 停止所有服务

```bash
cd /path/to/your/project
./stop_services.sh
```

### 停止CLIP+FAISS服务

```bash
pkill -f "python.*clip_faiss_api.py"
```

### 检查服务状态

```bash
cd /path/to/your/project
./check_services.sh
```

## 数据管理

### 数据上传

#### 使用SCP

```bash
scp -r /local/path/to/data username@your-instance-ip:/remote/path/
```

#### 使用SFTP

```bash
sftp username@your-instance-ip
cd /remote/path/
put -r /local/path/to/data
```

#### 使用AutoDL网盘

1. 登录到AutoDL平台
2. 点击"网盘"
3. 上传文件或文件夹
4. 在实例中访问网盘目录（通常在`/root/autodl-tmp`或类似路径）

### 数据备份

#### 备份到本地

```bash
scp -r username@your-instance-ip:/remote/path/to/backup /local/backup/path/
```

#### 备份到AutoDL网盘

```bash
cp -r /path/to/your/data /root/autodl-tmp/backups/
```

### 数据库备份

如果你的系统使用MySQL数据库：

```bash
# 备份
mysqldump -u username -p database_name > backup.sql

# 恢复
mysql -u username -p database_name < backup.sql
```

## 资源优化

### 监控资源使用

```bash
# 实时监控系统资源
htop

# 查看磁盘使用情况
df -h

# 查看内存使用情况
free -m

# 查看GPU使用情况
nvidia-smi
```

### 优化GPU使用

```bash
# 设置CUDA可见设备
export CUDA_VISIBLE_DEVICES=0

# 限制TensorFlow内存增长
python -c "import tensorflow as tf; tf.config.experimental.set_memory_growth(tf.config.list_physical_devices('GPU')[0], True)"

# 监控GPU使用
watch -n 1 nvidia-smi
```

### 清理磁盘空间

```bash
# 查找大文件
find / -type f -size +100M -exec ls -lh {} \; | sort -k5,5hr

# 清理pip缓存
pip cache purge

# 清理apt缓存
sudo apt clean

# 清理日志文件
sudo find /var/log -type f -name "*.log" -exec truncate -s 0 {} \;
```

## 常见问题

### 实例无法启动

1. 检查账户余额是否充足
2. 检查是否达到资源配额限制
3. 联系AutoDL客服支持

### 服务自动停止

1. 检查系统日志：
   ```bash
   tail -n 100 /var/log/syslog
   ```

2. 检查服务日志：
   ```bash
   tail -n 100 /path/to/service.log
   ```

3. 检查是否内存不足：
   ```bash
   dmesg | grep -i "out of memory"
   ```

### 网络连接问题

1. 检查安全组设置是否允许相应端口
2. 检查服务是否绑定到正确的地址（0.0.0.0而不是127.0.0.1）
3. 检查防火墙设置：
   ```bash
   sudo ufw status
   ```

## 有用的命令

### 系统命令

```bash
# 查看系统信息
uname -a

# 查看操作系统版本
cat /etc/os-release

# 查看IP地址
ip addr show

# 查看已安装的软件包
dpkg -l | grep <package_name>
```

### Python环境管理

```bash
# 创建虚拟环境
python -m venv myenv

# 激活虚拟环境
source myenv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 查看已安装的包
pip list
```

### 进程管理

```bash
# 查看所有Python进程
ps aux | grep python

# 查看特定端口的进程
lsof -i :<port_number>

# 后台运行命令并记录日志
nohup command > output.log 2>&1 &

# 查看后台任务
jobs
```

### 文件传输

```bash
# 从本地上传到服务器
scp local_file username@your-instance-ip:remote_path

# 从服务器下载到本地
scp username@your-instance-ip:remote_file local_path

# 同步目录
rsync -avz local_dir/ username@your-instance-ip:remote_dir/
```

---

**注意**：请根据实际部署情况，替换上述命令中的路径、用户名和IP地址。

**最后更新**：2025年5月3日
