/**
 * 前端日志工具
 * 提供不同级别的日志记录功能，支持格式化输出和颜色区分
 */

// 保存原始控制台方法，避免递归调用
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error,
  debug: console.debug
}

// 日志级别枚举
export const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4
}

// 默认配置
const defaultConfig = {
  level: process.env.NODE_ENV === 'production' ? LogLevel.ERROR : LogLevel.WARN, // 生产环境只显示错误，开发环境显示警告和错误
  showTimestamp: true,
  showLevel: true,
  showContext: true,
  enabled: process.env.NODE_ENV !== 'production', // 生产环境默认禁用详细日志
  colors: {
    debug: '#7f8c8d', // 灰色
    info: '#2ecc71',  // 绿色
    warn: '#f39c12',  // 橙色
    error: '#e74c3c'  // 红色
  }
}

// 日志类
class Logger {
  constructor(config = {}) {
    this.config = { ...defaultConfig, ...config }
    this.contextStack = []
    this.logListeners = []
  }

  /**
   * 设置日志配置
   * @param {Object} config 配置对象
   */
  setConfig(config) {
    this.config = { ...this.config, ...config }
    return this
  }

  /**
   * 获取当前配置
   * @returns {Object} 当前配置
   */
  getConfig() {
    return { ...this.config }
  }

  /**
   * 设置日志级别
   * @param {LogLevel} level 日志级别
   */
  setLevel(level) {
    this.config.level = level
    return this
  }

  /**
   * 启用日志
   */
  enable() {
    this.config.enabled = true
    return this
  }

  /**
   * 禁用日志
   */
  disable() {
    this.config.enabled = false
    return this
  }

  /**
   * 推入上下文
   * @param {string} context 上下文名称
   */
  pushContext(context) {
    this.contextStack.push(context)
    return this
  }

  /**
   * 弹出上下文
   */
  popContext() {
    this.contextStack.pop()
    return this
  }

  /**
   * 清除所有上下文
   */
  clearContext() {
    this.contextStack = []
    return this
  }

  /**
   * 获取当前上下文路径
   * @returns {string} 上下文路径
   */
  getContextPath() {
    return this.contextStack.join('/')
  }

  /**
   * 格式化日志消息
   * @param {string} level 日志级别
   * @param {Array} args 日志参数
   * @returns {Array} 格式化后的参数
   */
  formatMessage(level, args) {
    const result = []

    // 添加时间戳
    if (this.config.showTimestamp) {
      const now = new Date()
      const timeStr = `[${now.toLocaleTimeString('en-US', { hour12: false })}.${String(now.getMilliseconds()).padStart(3, '0')}]`
      result.push(`%c${timeStr}`, 'color: #888; font-weight: normal;')
    }

    // 添加日志级别
    if (this.config.showLevel) {
      const levelStr = `[${level.toUpperCase()}]`
      result.push(`%c${levelStr}`, `color: ${this.config.colors[level]}; font-weight: bold;`)
    }

    // 添加上下文
    if (this.config.showContext && this.contextStack.length > 0) {
      const contextStr = `[${this.getContextPath()}]`
      result.push(`%c${contextStr}`, 'color: #3498db; font-weight: normal;')
    }

    // 添加实际日志内容
    args.forEach(arg => {
      if (typeof arg === 'string') {
        result.push(`%c${arg}`, 'color: inherit; font-weight: normal;')
      } else {
        result.push(arg)
      }
    })

    return result
  }

  /**
   * 添加日志监听器
   * @param {Function} listener 监听器函数，接收 (level, context, args) 参数
   */
  setLogListener(listener) {
    if (typeof listener === 'function') {
      this.logListeners.push(listener)
    }
    return this
  }

  /**
   * 移除日志监听器
   * @param {Function} listener 要移除的监听器函数，如果不提供则移除所有监听器
   */
  removeLogListener(listener) {
    if (listener) {
      const index = this.logListeners.indexOf(listener)
      if (index !== -1) {
        this.logListeners.splice(index, 1)
      }
    } else {
      this.logListeners = []
    }
    return this
  }

  /**
   * 触发日志监听器
   * @param {string} level 日志级别
   * @param {Array} args 日志参数
   */
  triggerLogListeners(level, args) {
    const context = this.contextStack.length > 0 ? this.getContextPath() : ''

    // 创建一个安全的参数副本，避免响应式引用
    const safeArgs = args.map(arg => {
      if (typeof arg === 'object' && arg !== null) {
        try {
          return JSON.parse(JSON.stringify(arg))
        } catch (e) {
          return String(arg)
        }
      }
      return arg
    })

    // 使用setTimeout避免递归更新
    setTimeout(() => {
      // 触发所有监听器
      this.logListeners.forEach(listener => {
        try {
          listener(level, context, safeArgs)
        } catch (error) {
          // 使用原始console避免递归
          originalConsole.error('日志监听器执行错误:', error)
        }
      })

      // 触发全局事件
      try {
        window.dispatchEvent(new CustomEvent('custom-log', {
          detail: { level, context, args: safeArgs }
        }))
      } catch (error) {
        // 使用原始console避免递归
        originalConsole.error('触发日志事件错误:', error)
      }
    }, 0)
  }

  /**
   * 记录调试日志
   * @param {...any} args 日志参数
   */
  debug(...args) {
    if (!this.config.enabled || this.config.level > LogLevel.DEBUG) return
    // 使用原始控制台方法，避免递归调用
    originalConsole.debug(...this.formatMessage('debug', args))
    this.triggerLogListeners('DEBUG', args)
    return this
  }

  /**
   * 记录信息日志
   * @param {...any} args 日志参数
   */
  info(...args) {
    if (!this.config.enabled || this.config.level > LogLevel.INFO) return
    // 使用原始控制台方法，避免递归调用
    originalConsole.info(...this.formatMessage('info', args))
    this.triggerLogListeners('INFO', args)
    return this
  }

  /**
   * 记录警告日志
   * @param {...any} args 日志参数
   */
  warn(...args) {
    if (!this.config.enabled || this.config.level > LogLevel.WARN) return
    // 使用原始控制台方法，避免递归调用
    originalConsole.warn(...this.formatMessage('warn', args))
    this.triggerLogListeners('WARN', args)
    return this
  }

  /**
   * 记录错误日志
   * @param {...any} args 日志参数
   */
  error(...args) {
    if (!this.config.enabled || this.config.level > LogLevel.ERROR) return
    // 使用原始控制台方法，避免递归调用
    originalConsole.error(...this.formatMessage('error', args))
    this.triggerLogListeners('ERROR', args)
    return this
  }

  /**
   * 创建一个子日志记录器，继承当前配置但有自己的上下文
   * @param {string} context 上下文名称
   * @returns {Logger} 新的日志记录器实例
   */
  createLogger(context) {
    const childLogger = new Logger(this.config)
    childLogger.contextStack = [...this.contextStack]
    if (context) {
      childLogger.pushContext(context)
    }
    return childLogger
  }

  /**
   * 记录性能计时开始
   * @param {string} label 计时标签
   */
  time(label) {
    if (!this.config.enabled) return
    console.time(label)
    return this
  }

  /**
   * 记录性能计时结束
   * @param {string} label 计时标签
   */
  timeEnd(label) {
    if (!this.config.enabled) return
    console.timeEnd(label)
    return this
  }

  /**
   * 记录性能计时点
   * @param {string} label 计时标签
   */
  timeLog(label) {
    if (!this.config.enabled) return
    console.timeLog(label)
    return this
  }

  /**
   * 记录对象的表格视图
   * @param {Object|Array} data 要显示的数据
   */
  table(data) {
    if (!this.config.enabled) return
    console.table(data)
    return this
  }

  /**
   * 分组日志开始
   * @param {string} label 分组标签
   * @param {boolean} collapsed 是否默认折叠
   */
  group(label, collapsed = false) {
    if (!this.config.enabled) return
    if (collapsed) {
      console.groupCollapsed(label)
    } else {
      console.group(label)
    }
    return this
  }

  /**
   * 分组日志结束
   */
  groupEnd() {
    if (!this.config.enabled) return
    console.groupEnd()
    return this
  }
}

// 创建默认日志实例
const logger = new Logger()

// 导出
export default logger
