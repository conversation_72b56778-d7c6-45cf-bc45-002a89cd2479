package com.tomato.lostfoundsystem.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 用户登录请求数据传输对象
 */
@Data
public class LoginRequestDTO {

    private Long userId;           // 用户ID

    @Size(min = 3, max = 20, message = "用户名长度应在3-20个字符之间")
    private String username;       // 用户名

    @Size(min = 6, max = 20, message = "密码长度应在6-20个字符之间")
    private String password;       // 密码

    private String verifyCode;     // 图形验证码
    private String captchaId;      // 图形验证码ID

    @Pattern(regexp = "^$|^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;          // 手机号

    @Email(message = "邮箱格式不正确")
    private String email;          // 邮箱

    @Size(min = 0, max = 6, message = "验证码长度应为6位")
    private String code;           // 邮箱/手机验证码
}
