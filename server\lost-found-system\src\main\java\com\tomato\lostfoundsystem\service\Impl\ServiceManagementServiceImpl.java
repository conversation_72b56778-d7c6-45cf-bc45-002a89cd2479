package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.service.IntelligentMatchService;
import com.tomato.lostfoundsystem.service.ServiceManagementService;
import com.tomato.lostfoundsystem.service.SystemConfigService;
import com.tomato.lostfoundsystem.utils.ClipFaissClientRefactored;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 服务管理服务实现类
 */
@Service
@Slf4j
public class ServiceManagementServiceImpl implements ServiceManagementService {



    @Resource
    private SystemConfigService systemConfigService;

    @Autowired
    private ClipFaissClientRefactored clipFaissClient;

    @Autowired
    private IntelligentMatchService intelligentMatchService;

    private String getClipApiUrl() {
        return systemConfigService.getConfigValue("autodl.clip.api.url", "http://localhost:8000");
    }

    private int getConnectionTimeout() {
        String value = systemConfigService.getConfigValue("autodl.clip.service.connection-timeout", "3000");
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 3000; // 默认值
        }
    }

    private String getClipServiceScriptPath() {
        return systemConfigService.getConfigValue("autodl.clip.service.script-path", "./clip_faiss_service/start_clip_service.sh");
    }

    @Override
    public Result<Map<String, Object>> getClipServiceStatus() {
        try {
            Map<String, Object> statusInfo = new HashMap<>();
            boolean isRunning = checkClipServiceRunning();

            statusInfo.put("running", isRunning);
            statusInfo.put("url", getClipApiUrl());
            statusInfo.put("checkTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            if (isRunning) {
                // 如果服务正在运行，尝试获取更多信息
                try {
                    Map<String, Object> serviceInfo = getClipServiceInfo();
                    statusInfo.putAll(serviceInfo);
                } catch (Exception e) {
                    log.warn("获取智能匹配服务详细信息失败: {}", e.getMessage());
                }
            }

            return Result.success(statusInfo);
        } catch (Exception e) {
            log.error("获取智能匹配服务状态失败", e);
            return Result.fail("获取服务状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> startClipService() {
        try {
            // 检查服务是否已经在运行
            if (checkClipServiceRunning()) {
                return Result.fail("服务已经在运行中");
            }

            // 执行启动脚本
            boolean success = executeCommand(getClipServiceScriptPath() + " start");

            if (success) {
                // 等待服务启动
                for (int i = 0; i < 10; i++) {
                    if (checkClipServiceRunning()) {
                        return Result.success("服务启动成功");
                    }
                    TimeUnit.SECONDS.sleep(2);
                }
                return Result.fail("服务启动超时，请检查日志");
            } else {
                return Result.fail("服务启动失败，请检查日志");
            }
        } catch (Exception e) {
            log.error("启动智能匹配服务失败", e);
            return Result.fail("启动服务失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> stopClipService() {
        try {
            // 检查服务是否在运行
            if (!checkClipServiceRunning()) {
                return Result.fail("服务未在运行");
            }

            // 执行停止脚本
            boolean success = executeCommand(getClipServiceScriptPath() + " stop");

            if (success) {
                // 等待服务停止
                for (int i = 0; i < 5; i++) {
                    if (!checkClipServiceRunning()) {
                        return Result.success("服务已停止");
                    }
                    TimeUnit.SECONDS.sleep(2);
                }
                return Result.fail("服务停止超时，可能需要手动终止进程");
            } else {
                return Result.fail("服务停止失败，请检查日志");
            }
        } catch (Exception e) {
            log.error("停止智能匹配服务失败", e);
            return Result.fail("停止服务失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> restartClipService() {
        try {
            // 先停止服务
            Result<String> stopResult = stopClipService();
            if (stopResult.getCode() != 200 && !stopResult.getMessage().contains("服务未在运行")) {
                return stopResult;
            }

            // 等待一段时间
            TimeUnit.SECONDS.sleep(2);

            // 启动服务
            return startClipService();
        } catch (Exception e) {
            log.error("重启智能匹配服务失败", e);
            return Result.fail("重启服务失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> rebuildClipIndex(String itemType, String indexType) {
        try {
            log.info("开始重建CLIP+FAISS索引: 物品类型={}, 索引类型={}", itemType, indexType);

            // 检查服务是否可用
            if (!checkClipServiceRunning()) {
                return Result.fail("智能匹配服务未运行，无法重建索引");
            }

            // 验证参数
            if (!"LOST".equals(itemType) && !"FOUND".equals(itemType) && !"ALL".equals(itemType)) {
                return Result.fail("无效的物品类型，必须是 LOST、FOUND 或 ALL");
            }

            if (!"TEXT".equals(indexType) && !"IMAGE".equals(indexType) && !"ALL".equals(indexType)) {
                return Result.fail("无效的索引类型，必须是 TEXT、IMAGE 或 ALL");
            }

            // 调用CLIP+FAISS客户端重建索引
            boolean success = clipFaissClient.rebuildIndex(itemType, indexType);

            if (success) {
                String message = String.format("成功重建索引: 物品类型=%s, 索引类型=%s", itemType, indexType);
                log.info(message);
                return Result.success(message);
            } else {
                return Result.fail("重建索引失败，请查看日志获取详细信息");
            }
        } catch (Exception e) {
            log.error("重建CLIP+FAISS索引失败", e);
            return Result.fail("重建索引失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> regenerateAllFeatureVectors() {
        try {
            log.info("开始重新生成所有物品的特征向量");

            // 检查服务是否可用
            if (!checkClipServiceRunning()) {
                return Result.fail("智能匹配服务未运行，无法生成特征向量");
            }

            // 调用智能匹配服务生成特征向量
            Result<String> result = intelligentMatchService.generateFeatureVectorsForAllItems();

            if (result.getCode() == 200) {
                // 生成成功后，保存索引
                boolean saved = clipFaissClient.saveIndices();
                if (saved) {
                    log.info("特征向量生成完成，索引已保存");
                    return Result.success(result.getData() + "，索引已保存");
                } else {
                    log.warn("特征向量生成完成，但索引保存失败");
                    return Result.success(result.getData() + "，但索引保存失败");
                }
            } else {
                return result;
            }
        } catch (Exception e) {
            log.error("重新生成特征向量失败", e);
            return Result.fail("生成特征向量失败: " + e.getMessage());
        }
    }

    /**
     * 检查CLIP服务是否正在运行
     *
     * @return 服务是否运行
     */
    private boolean checkClipServiceRunning() {
        try {
            String apiUrl = getClipApiUrl();
            log.info("检查CLIP+FAISS服务是否可用: {}", apiUrl);

            // 首先尝试使用/health端点
            try {
                URL healthUrl = new URL(apiUrl + "/health");
                HttpURLConnection healthConnection = (HttpURLConnection) healthUrl.openConnection();
                healthConnection.setRequestMethod("GET");
                healthConnection.setConnectTimeout(getConnectionTimeout());
                healthConnection.setReadTimeout(getConnectionTimeout());

                int healthResponseCode = healthConnection.getResponseCode();
                if (healthResponseCode == 200) {
                    log.info("CLIP+FAISS服务健康检查成功，服务可用");
                    return true;
                }
                log.warn("CLIP+FAISS服务健康检查失败，响应码: {}", healthResponseCode);
            } catch (Exception e) {
                log.warn("CLIP+FAISS服务健康检查异常: {}, 尝试根路径检查", e.getMessage());
            }

            // 如果健康检查失败，尝试根路径
            URL rootUrl = new URL(apiUrl);
            HttpURLConnection rootConnection = (HttpURLConnection) rootUrl.openConnection();
            rootConnection.setRequestMethod("GET");
            rootConnection.setConnectTimeout(getConnectionTimeout());
            rootConnection.setReadTimeout(getConnectionTimeout());

            int rootResponseCode = rootConnection.getResponseCode();
            boolean isAvailable = (rootResponseCode == 200);

            log.info("CLIP+FAISS服务根路径检查结果: {}, 响应码: {}",
                    isAvailable ? "可用" : "不可用", rootResponseCode);

            return isAvailable;
        } catch (Exception e) {
            log.debug("检查服务状态异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取CLIP服务详细信息
     *
     * @return 服务详细信息
     */
    private Map<String, Object> getClipServiceInfo() {
        Map<String, Object> info = new HashMap<>();
        try {
            URL url = new URL(getClipApiUrl() + "/health");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(getConnectionTimeout());
            connection.setReadTimeout(getConnectionTimeout());

            if (connection.getResponseCode() == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                // 这里假设返回的是JSON格式，实际情况可能需要解析
                info.put("healthInfo", response.toString());

                // 添加一些基本信息
                info.put("model", "CLIP ViT-B/32");
                info.put("status", "running");
                info.put("lastChecked", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
        } catch (Exception e) {
            log.debug("获取服务详细信息异常: {}", e.getMessage());
            info.put("status", "error");
            info.put("error", e.getMessage());
        }
        return info;
    }

    /**
     * 执行命令行命令
     *
     * @param command 要执行的命令
     * @return 是否执行成功
     */
    private boolean executeCommand(String command) {
        try {
            log.info("执行命令: {}", command);

            ProcessBuilder processBuilder = new ProcessBuilder();

            // 根据操作系统设置命令
            if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                processBuilder.command("cmd.exe", "/c", command);
            } else {
                processBuilder.command("bash", "-c", command);
            }

            // 设置工作目录
            File scriptDir = new File(getClipServiceScriptPath()).getParentFile();
            if (scriptDir != null && scriptDir.exists()) {
                processBuilder.directory(scriptDir);
            }

            // 合并错误流和标准输出流
            processBuilder.redirectErrorStream(true);

            // 启动进程
            Process process = processBuilder.start();

            // 读取输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            StringBuilder output = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }

            // 等待进程完成
            int exitCode = process.waitFor();
            log.info("命令执行完成，退出码: {}, 输出: {}", exitCode, output);

            return exitCode == 0;
        } catch (Exception e) {
            log.error("执行命令失败", e);
            return false;
        }
    }
}
