package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;

import java.util.Map;

/**
 * 服务管理服务接口
 * 用于管理系统中的各种服务状态和操作
 */
public interface ServiceManagementService {

    /**
     * 获取智能匹配服务状态
     *
     * @return 服务状态信息
     */
    Result<Map<String, Object>> getClipServiceStatus();

    /**
     * 启动智能匹配服务
     *
     * @return 操作结果
     */
    Result<String> startClipService();

    /**
     * 停止智能匹配服务
     *
     * @return 操作结果
     */
    Result<String> stopClipService();

    /**
     * 重启智能匹配服务
     *
     * @return 操作结果
     */
    Result<String> restartClipService();

    /**
     * 重建CLIP+FAISS索引
     *
     * @param itemType 物品类型（LOST/FOUND/ALL）
     * @param indexType 索引类型（TEXT/IMAGE/ALL）
     * @return 操作结果
     */
    Result<String> rebuildClipIndex(String itemType, String indexType);

    /**
     * 重新生成所有物品的特征向量
     *
     * @return 操作结果
     */
    Result<String> regenerateAllFeatureVectors();
}
