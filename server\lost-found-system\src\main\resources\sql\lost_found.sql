/*
Navicat MySQL Data Transfer

Source Server         : localhost_3306
Source Server Version : 80013
Source Host           : localhost:3306
Source Database       : lost_found

Target Server Type    : MYSQL
Target Server Version : 80013
File Encoding         : 65001

Date: 2025-05-15 13:33:42
*/

SET FOREIGN_KEY_CHECKS=0;
-- ----------------------------
-- Table structure for `chat_messages`
-- ----------------------------
DROP TABLE IF EXISTS `chat_messages`;
CREATE TABLE `chat_messages` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sender_id` bigint(20) NOT NULL,
  `receiver_id` bigint(20) NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  `message_type` enum('TEXT','<PERSON><PERSON><PERSON>','VIDEO','DOCUMENT','AUDIO') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `audio_duration` int(11) DEFAULT NULL,
  `video_duration` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `chat_messages_ibfk_1` (`sender_id`),
  KEY `chat_messages_ibfk_2` (`receiver_id`),
  CONSTRAINT `chat_messages_ibfk_1` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`),
  CONSTRAINT `chat_messages_ibfk_2` FOREIGN KEY (`receiver_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=320 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of chat_messages
-- ----------------------------
INSERT INTO chat_messages VALUES ('240', '5', '8', 'hello', '2025-05-14 14:28:24', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('241', '8', '5', '啊啊啊', '2025-05-14 14:31:01', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('242', '5', '8', '?', '2025-05-14 14:40:01', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('243', '8', '5', '?', '2025-05-14 14:41:35', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('244', '5', '8', '1', '2025-05-14 14:44:40', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('245', '8', '5', '2', '2025-05-14 14:54:58', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('246', '5', '8', '3', '2025-05-14 14:57:26', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('247', '8', '5', '4', '2025-05-14 15:13:01', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('248', '8', '5', '5', '2025-05-14 15:16:00', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('249', '8', '5', '6', '2025-05-14 15:30:38', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('250', '8', '5', '7', '2025-05-14 15:36:51', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('251', '8', '5', '8', '2025-05-14 15:54:32', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('252', '5', '8', '8', '2025-05-14 15:55:30', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('253', '8', '5', '9', '2025-05-14 16:00:55', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('254', '5', '8', '哈哈哈', '2025-05-14 16:10:29', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('255', '5', '8', '123', '2025-05-14 16:11:31', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('256', '5', '8', '56', '2025-05-14 16:18:24', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('257', '8', '8', '78', '2025-05-14 16:22:00', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('258', '8', '8', '78', '2025-05-14 16:22:17', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('259', '5', '8', '234', '2025-05-14 16:24:52', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('260', '5', '8', '12345', '2025-05-14 16:25:51', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('261', '8', '5', '哈哈哈', '2025-05-14 16:29:22', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('262', '8', '5', '嘻嘻', '2025-05-14 16:31:10', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('263', '8', '8', '嘻嘻', '2025-05-14 16:31:22', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('264', '8', '8', '哈哈哈', '2025-05-14 16:57:52', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('265', '5', '8', '23456', '2025-05-14 16:59:05', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('266', '8', '5', '哈哈哈', '2025-05-14 17:00:21', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('267', '5', '8', '?', '2025-05-14 17:10:47', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('268', '5', '5', '555', '2025-05-14 17:15:30', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('269', '5', '5', '234', '2025-05-14 17:31:11', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('270', '5', '8', '234', '2025-05-14 17:34:22', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('271', '8', '5', '567', '2025-05-14 17:37:53', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('272', '5', '8', '789', '2025-05-14 17:38:14', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('273', '5', '8', '890', '2025-05-14 17:38:33', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('274', '5', '8', '6666', '2025-05-14 17:44:33', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('275', '5', '8', 'hhh', '2025-05-14 17:46:50', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('276', '5', '5', 'Hello, this is a self-test.', '2025-05-14 17:58:43', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('277', '5', '5', '测试消息 18:23:41', '2025-05-14 18:23:44', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('278', '5', '5', '测试消息 18:23:56', '2025-05-14 18:23:59', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('279', '5', '8', '测试消息 18:25:29', '2025-05-14 18:25:41', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('280', '5', '8', '测试消息 18:27:12', '2025-05-14 18:27:21', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('281', '8', '8', '测试消息 18:32:33', '2025-05-14 18:32:37', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('282', '8', '5', '测试消息 18:33:00', '2025-05-14 18:33:10', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('283', '5', '8', '测试消息 18:43:45', '2025-05-14 18:43:58', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('284', '5', '8', '测试消息 18:47:36', '2025-05-14 18:47:45', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('285', '5', '8', '测试消息 18:51:08', '2025-05-14 18:51:16', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('286', '8', '5', '测试消息 18:51:32', '2025-05-14 18:51:40', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('287', '5', '8', '在吗', '2025-05-14 18:52:30', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('288', '8', '5', '在啊', '2025-05-14 18:52:50', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('289', '5', '8', '你干嘛，哎呦', '2025-05-14 18:53:23', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('290', '8', '5', '小黑子', '2025-05-14 18:53:34', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('291', '8', '5', '嘻嘻', '2025-05-14 18:54:05', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('292', '5', '8', '你妹的', '2025-05-14 18:54:16', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('293', '8', '5', '呜呜呜', '2025-05-14 18:55:15', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('294', '5', '8', '哈哈哈', '2025-05-14 19:04:25', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('295', '8', '5', '嘻嘻嘻', '2025-05-14 19:04:36', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('296', '5', '8', '?', '2025-05-14 19:05:02', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('297', '5', '8', '哈哈哈', '2025-05-14 19:44:55', 'IMAGE', null, null);
INSERT INTO chat_messages VALUES ('298', '8', '5', '这是什么，', '2025-05-14 20:09:51', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('299', '8', '5', '你没看到吗？', '2025-05-14 21:09:50', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('300', '5', '8', '?', '2025-05-14 21:10:32', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('301', '8', '5', '?', '2025-05-14 21:17:00', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('302', '8', '5', '崩溃了', '2025-05-14 21:41:33', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('303', '5', '8', '?', '2025-05-14 22:00:54', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('304', '8', '5', '?', '2025-05-14 23:01:54', 'IMAGE', null, null);
INSERT INTO chat_messages VALUES ('305', '8', '5', '图片消息', '2025-05-14 23:28:07', 'IMAGE', null, null);
INSERT INTO chat_messages VALUES ('306', '5', '8', '?', '2025-05-14 23:30:53', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('307', '8', '5', '嘻嘻', '2025-05-14 23:31:11', 'IMAGE', null, null);
INSERT INTO chat_messages VALUES ('308', '8', '5', '唏嘘', '2025-05-15 00:11:10', 'IMAGE', null, null);
INSERT INTO chat_messages VALUES ('309', '5', '8', '图片消息', '2025-05-15 00:12:15', 'IMAGE', null, null);
INSERT INTO chat_messages VALUES ('310', '8', '5', '?', '2025-05-15 00:30:20', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('311', '5', '8', '嘻嘻', '2025-05-15 00:30:52', 'IMAGE', null, null);
INSERT INTO chat_messages VALUES ('312', '8', '5', '?', '2025-05-15 00:41:23', 'IMAGE', null, null);
INSERT INTO chat_messages VALUES ('313', '5', '8', '?', '2025-05-15 00:41:48', 'IMAGE', null, null);
INSERT INTO chat_messages VALUES ('314', '8', '5', '?', '2025-05-15 01:10:17', 'IMAGE', null, null);
INSERT INTO chat_messages VALUES ('315', '5', '8', '?', '2025-05-15 05:06:43', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('316', '5', '5', '测试消息', '2025-05-15 05:27:21', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('317', '5', '5', '测试消息 05:29:52', '2025-05-15 05:29:57', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('318', '8', '5', '在吗', '2025-05-15 05:40:44', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('319', '8', '5', '在的', '2025-05-15 05:41:12', 'TEXT', '0', '0');

-- ----------------------------
-- Table structure for `chat_sessions`
-- ----------------------------
DROP TABLE IF EXISTS `chat_sessions`;
CREATE TABLE `chat_sessions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user1_id` bigint(20) NOT NULL,
  `user2_id` bigint(20) NOT NULL,
  `last_message_time` datetime DEFAULT NULL,
  `last_message_id` bigint(20) DEFAULT NULL COMMENT '最后一条消息ID',
  `last_message_content` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后一条消息内容摘要',
  `last_message_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后一条消息类型',
  `unread_count` int(11) DEFAULT '0' COMMENT '未读消息计数',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'ACTIVE' COMMENT '会话状态：ACTIVE, ARCHIVED, DELETED',
  `is_pinned` tinyint(1) DEFAULT '0' COMMENT '是否置顶',
  `is_muted` tinyint(1) DEFAULT '0' COMMENT '是否静音',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `chat_sessions_ibfk_1` (`user1_id`),
  KEY `chat_sessions_ibfk_2` (`user2_id`),
  KEY `idx_chat_sessions_user1_id` (`user1_id`),
  KEY `idx_chat_sessions_user2_id` (`user2_id`),
  KEY `idx_chat_sessions_status` (`status`),
  KEY `idx_chat_sessions_last_message_time` (`last_message_time`),
  KEY `idx_chat_sessions_is_pinned` (`is_pinned`),
  CONSTRAINT `chat_sessions_ibfk_1` FOREIGN KEY (`user1_id`) REFERENCES `users` (`id`),
  CONSTRAINT `chat_sessions_ibfk_2` FOREIGN KEY (`user2_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of chat_sessions
-- ----------------------------
INSERT INTO chat_sessions VALUES ('2', '5', '8', '2025-05-15 05:41:12', '319', '在的', 'TEXT', '1', 'ACTIVE', '0', '0', '2025-05-14 13:42:53', '2025-05-15 05:41:12');

-- ----------------------------
-- Table structure for `found_items`
-- ----------------------------
DROP TABLE IF EXISTS `found_items`;
CREATE TABLE `found_items` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '拾取人 ID',
  `item_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '物品描述',
  `found_time` datetime DEFAULT NULL COMMENT '拾取时间',
  `found_location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拾取地点',
  `image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片 URL',
  `status` enum('UNCLAIMED','RETURNED') COLLATE utf8mb4_unicode_ci DEFAULT 'UNCLAIMED' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `audit_status` enum('PENDING','APPROVED','REJECTED') COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `found_items_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of found_items
-- ----------------------------
INSERT INTO found_items VALUES ('1', '5', '水杯', '红色保温杯，未损坏', '2025-04-04 15:30:00', '教学楼B区一楼', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1743761801221_红色保温杯.jpg', 'UNCLAIMED', '2025-04-04 18:16:42', 'PENDING');
INSERT INTO found_items VALUES ('3', '8', 'ipad', 'ipadAir6，海绵宝宝外壳', '2025-04-15 15:51:20', '图书馆', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1744703525329_ipad.jpg', 'UNCLAIMED', '2025-04-15 15:52:06', 'APPROVED');
INSERT INTO found_items VALUES ('7', '5', '12345', '234567ikjhgfdssdfghjkwgtju3yijxzvGD', '2025-05-15 12:00:00', '1234567', 'https://cdn.laofanqi.top/images/found/2025/05/10/1746823671076_书本1.jpg', 'UNCLAIMED', '2025-05-10 04:47:52', 'PENDING');

-- ----------------------------
-- Table structure for `found_item_audit`
-- ----------------------------
DROP TABLE IF EXISTS `found_item_audit`;
CREATE TABLE `found_item_audit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) NOT NULL,
  `audit_status` enum('PENDING','APPROVED','REJECTED') COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  `audit_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `auditor_id` bigint(20) NOT NULL,
  `remarks` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `fk_found_item` (`item_id`),
  CONSTRAINT `fk_found_item` FOREIGN KEY (`item_id`) REFERENCES `found_items` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of found_item_audit
-- ----------------------------
INSERT INTO found_item_audit VALUES ('14', '3', 'APPROVED', '2025-05-13 19:10:10', '5', '');

-- ----------------------------
-- Table structure for `item_attachments`
-- ----------------------------
DROP TABLE IF EXISTS `item_attachments`;
CREATE TABLE `item_attachments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `lost_item_id` bigint(20) NOT NULL,
  `found_item_id` bigint(20) NOT NULL,
  `image_similarity` decimal(5,4) NOT NULL,
  `text_similarity` decimal(5,4) NOT NULL,
  `total_similarity` decimal(5,4) NOT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `lost_item_id` (`lost_item_id`),
  KEY `found_item_id` (`found_item_id`),
  CONSTRAINT `item_attachments_ibfk_1` FOREIGN KEY (`lost_item_id`) REFERENCES `lost_items` (`id`),
  CONSTRAINT `item_attachments_ibfk_2` FOREIGN KEY (`found_item_id`) REFERENCES `found_items` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of item_attachments
-- ----------------------------

-- ----------------------------
-- Table structure for `item_feature_vectors`
-- ----------------------------
DROP TABLE IF EXISTS `item_feature_vectors`;
CREATE TABLE `item_feature_vectors` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) NOT NULL COMMENT '物品ID',
  `item_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品类型（LOST/FOUND）',
  `text_vector` blob COMMENT '文本特征向量',
  `image_vector` blob COMMENT '图像特征向量',
  `vector_version` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '向量版本',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item_id_type` (`item_id`,`item_type`),
  KEY `idx_item_type` (`item_type`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物品特征向量表';

-- ----------------------------
-- Records of item_feature_vectors
-- ----------------------------
INSERT INTO item_feature_vectors VALUES ('16', '29', 'LOST', 0x8004958F080000000000008C166E756D70792E5F636F72652E6D756C74696172726179948C0C5F7265636F6E7374727563749493948C056E756D7079948C076E6461727261799493944B0085944301629487945294284B014B014D0002869468038C0564747970659493948C02663494898887945294284B038C013C944E4E4E4AFFFFFFFF4AFFFFFFFF4B0074946289420008000000C080BD0020A33C0020B03C0040DCBB00E0063B0040EABC006068BC00C0013D0020A03C0080DE3C00C090BB00C0553B00C012BD00A05C3A00C0E13C00A08A3B008098BC00C039BC0020073D00A0F73B00A094BD0000E73C00E085BB00A07ABB00A0983B00C00FBD00201E3B00E0D83C002096BC0020A53C00202BBD0080A83C00608B3B0000DF3B00A0F9BC0020853C00C02E3B0040ACBC0020683C0040063D0040BFBC00001CBB00E0CD3C0040903B00C0C53C0080DBBB00C0683C00E0693C00804CBC0020A0BB0000113D0020873C00E04F3C0020F9BB0000893C00201D3C00402E3C0000213D0080033D00E0BBBC0060753D002062BB00002BBC008002BD0020C8BC00A0963B00A0BB3B0080473D0060883C00C0D6BC00A045BD00E052BB00E0BB3B00805A3C0000663C0000B6BB00E070BC0040903C00E03D3B0060DEBC000018BD00A02ABC0060E83B0000993C0020D1BB00A03A3D008010BC00A0FD3A0040DA3C00A0B8BB00607C3B00C02F3B00E00ABE00407B3D0040803B000014BC00E02DBC0060AE3C00A023BB0020EFBB00E001BD0060C1BB0040813C00E02ABC00C016BC004087BA00807ABD006011BD0020F83C0000A43C0080953C00602FBA00E0D1BC0060453C000055BC0080BFBB0060C8BC0040FE3C00E0B03B0000223D002062BB00A0CE3C00E0DD3C00E080BC0060043D00E0C5BB0080173B0060143C00E0CB3B0060423D00C054BC00A057BD00A0FCBB00E00A3F00602A3C004029BD0000AD3B0000F7BC00E005BD004032BD0000DC3B0020C3BC00A044BB00C0E23C00C06EBC0020E83C00000ABD00E01F3D00C0003D00C0B03C00E0FABC00201ABC00A06CBD004049BD0060113C0020D0BC0040AD3C00A07ABC00E0F0BB00400FBD0020C5BC00E0B2BC0000273C0040993C0020F83C0020F2BC00809E3C00609ABC00C06D380000373C00E006BC006046BC00808ABC00A065BC002037BC0040A83C006038BC00E0A2BC0060793D0060A83B00C052BD0080713C00003D3C0040103C00C00A3D00A099BC00204E3D00E0E4BC00E00BBB00E06C3C0040813C0000E3BC00C0E83C00602ABB00804C3C0040B1BB0040B6BC00E0163D004036BC00200D3C00A0453C006008BB00C045BC0060FF3C00A09DBC00C09D3C0060443C00C01EBC00807E3C00C0E83900C0C63B00E0233D0080DD3C004002BD00E0F63C00E0DC3C00A0193D0080EBBB0000B13C00000EBD006013BD00803EBD00009E3C00C0A9BC00C0813D002093BC0060F9BC0080FE3C00403E3D00C0A7BC0080153D0000B53B00E081BC00E02EBD0040B63C00A0373D00809EBA00A0D2BA00C089BC00805DBB0000673C008082BA00000C3D002081BD00A0D23B00A0503D00C033BB000067BD000073BD00806E3C0000DCBC006085BC0000ED3C00A0AE3C00402EBC0000D73C0060083C0060BBBC00E0113D0020E13C00409C3C00C0ADBC00004E3D0000E0BB00C0A63B00C0FBBC0060EC3C006028BD0060A9BC00C0BF3C0000D4BC00800B3D008075BB0080C6BC0060EB3B00C0653D00A095BB0080A63C00E01EBB00E07D3A0000B4BC00802EBD0040C93C0020E5BC00806EBB00409F3C0080533C00C0183C0000EDBC00C0E73C00805DBC00A0E23C00807BBC0020CF3B00E0ABBC002078B90080AEBC00E038BC00E00DBD0080DDBB0080D63C0020DEBB006099BB00A0C3BC00C0143D00A02A3D000067BB002000BC00E0AA3C0060273D004064BC008078BC00200B3F000000BC00E0293D0080523C0040B9BC00400C3E00C095BC00609A3B00200EBD0020A53D00E00A3D0080DA3C00004BBC004025B900801ABC0020EFBC00C02BBD00C0ADBD0040D63C00E05FBC00A0A13C0060C4BC0020413C00605BBC0040773C00001DBC00C0183C00A02EBD002075BC00A096BC00A0ED3C00A0923C00602F3B00E0D7BB008097BC00C0193C002046BC00809C3B00A0A83A00A0763C00E038BD002011BC004098BC0000143C0080923C00E00E3C0080313D00403FBD004081BD00C089BD0060343D00609E3D0040E33B00C006BD0060F9BC00C0A43B00A0D3BC00001B3D00204BBC00003F3D00601ABC00A03C3D00A0953C000011BC00E0A5BC00E043BD00E09BBD0040E4BB002048BC0060413C0060C4BB0040BBBB0060213C00E0EABB00C0CDBC00002F3C0020283D0000193C002035BD00A00C3C0040D5BB00206CBD00203EBD00E0863C0000C1BC0020C5BC00E0933C00E0AA3D00A010BC008033BC00200CBC00804E3D00C0903C00001D3D0020B9390040373D002020BD0020C0BC0020A33B0040D73B0080313D00A084BB00A01D3B0020DE3A0080F8BC00A0F0BC00A047BB0020433C0080F43C00C0BEBC0060283D00801F3C00A0A6BC00407BB900A0C13B002006BD000001BD00C0173D00C0613C0020013C00402FBD008022BD00C011BC00A036BC00A0DCBC008011BD00E0B0BC00603CBC0040C33C00E0F9BA00400ABD00E092BB00E03DBD0060EC3B00C011BD00C0B1BC0060BC3B0000343D00207FBC00807A3C0020653C00607F3C0000C53C0000C83C00E026BD006015BC0040893C0020CF3B00608DBB00C01C3C00808FBC0080A2BC00A0C8BB00E0093C00C0DE3C006008BC0020823C004028BD008057BD00602DBC0080B53C00A05C3B00608C3C0080473C0020B13B00C0333D00008A3B00C05A3C00A0833B00A0FDBD00203EBB00C024BD00A0883C00201FBC00605EBC00A0CEBC0040F4BC00C05ABA00C064BC0080303C0000C5BC004077BD008032BD0060A6BC00E02DBB000048BD0000D2BC0000863C00808BBC00C0A63C00A04A3C0060D43C00602F3D0040AA3C0000C43900C067BC00A0623C00805DBD00206E3A00E001BC947494622E, 0x8004958F080000000000008C166E756D70792E5F636F72652E6D756C74696172726179948C0C5F7265636F6E7374727563749493948C056E756D7079948C076E6461727261799493944B0085944301629487945294284B014B014D0002869468038C0564747970659493948C02663494898887945294284B038C013C944E4E4E4AFFFFFFFF4AFFFFFFFF4B0074946289420008000000C0BD3C0040CB3C00E0C5BC00E015BC00602ABC00608FBC00603FBC00E03B3D00C0B33C00603BBC0000043D00808C3B0020EB3C0020F2BB0060023D00E08C3D00E0E93D0080A33C00E0DF3B0000353B006094BD00602C3C00803EBD00E0D5BC0020A53A0000923C00A0293B0020FDBA0000B0BC0040E0BC0020F4BB00A030BD00A0A83B0060943D00E0F4BC00E0A23C00A0CABC00C03A3D0060273C00E0743C00C0F6BC0080E13C00E0F6BB00C01BBD0060603D00E07C3D00C00FBC00C0233D00609ABD00E0F43B0060C13C00A00B3D00E057BB000099BC0080183B0040873C00803C3D0080E5BC00C0243C0060B23C00405A3D00801DBD0040F9BC00A01EBD00C0D63C006085BC00608C3C00A006BD000001BA0000D0BC0040F1BC00A05E39002013BC0060023D00609C3C004073BB002034BC0020C83C00A0123D00C0F1BC0060CEBC00E0CE3A00607EBC00604CBC00A0383C006098BC006076BD00E00CBC0060C9BC008028BD004083BC00001DBA00A01EBF00E0063D0020023D008028BC00E0933C000098BC0020A5BD00C0FBBD0060E03B00E042BD0080953D0080AD3C00601D3C00A0853C00603B3C00C0B6BC00C0043D0060733C00E0EE3C0060863D0060903C0020CFBC008003BC00807E3B00C00FBD00E09D3C00C0433D0020D0BC00C08F3C00E0A03C0040773D0020D63C0080D53B00600EBC00E0A5BB00404F3D00E03B3D0060BFBB00C08CBB00802D3D00804EB900A0A73D00203DBC00801BBD00A0E6BC0080553C00A0053D00A009BB00401C3D00E0C63C00A0C63A0060E3BB00E0FEBC00A0063D008063BC0020B1BC0000A0BC00A0393B0020AA3C00800EBD00C0BA3B0060ACBC00A097BC00A0093D00A0283C00C0CEBC00A04ABD0060F7BB0040BE3A00C0ED3C00606F390020A4BC00802FBC00A0CF3C0040303B00A0A9BC00004E3D00E09ABC00A089BD0000693C00A0F2BC00E006BD0040D1BB0020113C00E050BD00C06DBB0020563D00A0C2390040A3BC00E01BBD00A0073D00E067BB00A068BD00C07B3B0000C33C00C060BC0000B0BB00C00C3D00E0073C0080BB3C00005A3D0000EDBC0080163E00C076BC004036BC00601ABC00C060BB00C0D83C00A08A3C002067BB00E061BA00A0DE3B002004BD0040C43C008070BD0020A3BC00E09ABC00E0023D0060763900A0F9BB00E02B3C00C0D2BB00E0A4BB00207C3C00C0C13C00602C3C00A0643C00E0033D0080283D00E057BD00001A3D0060923C0060D23B00C0D63C00609F3B00A00FBD00402D3D00A01FBD00E02FBB002068BC00E09CBC0060F9BB0060503C00202CBC0080543C00C07DBB00408BBC00008C3C00003B3D0060CEBC00A086BC00008BBB0080A6BC0000393C0060C93C00802A3D00C0643C00200B3C00E057BD0060E7BC0060613D0080BE3C00C0913C00A0CB3C0040C23C00E0393C00C04F3D00A0AFBB0080DBBC00C0FA3A00A0AFBC008013BD00A0B03C00208ABB0020A2BB00E006BD0000B93B00E028BD0060ACBC0080413C0060903C0020AABC00A04ABC0080163A0000DFBB008000BC00400A3C0060613D004049BC0040FD3C00E011BC00806ABC00401C3D0040C33C00803E3E0020ECBB00009F3C00A0463B00E0633D0080A93C00C087BD0080823C00C0943C0000F2BC0000603B0080F23C0000B53C00C06FBC0080D13B000011BD0000F5BC0020E83B004072BC0060CD3C0060A03C0020B3BC002027BD00C0B33C00E0563C008011BD0080A73D0080D83C00A0093D0040123D00C0B83C0060DA3C00A05D3C0080EDBD00E0403D000027BE0040F3BC0040713D00E0203D006097BC0020343B0020FBBC004032BB0000873C00200C3D0060A43C004079BC00C012BD00E018BD00E0653D0020BDBC00C007BA0000313B00C023BD00A0843C008025BD0020823C0080443D004075BB00802CBA0040BA3C0020953D0000453C00E0EA3A00E0ABBB0000F93B00C0E53C00E0ADBC00E0C23C00402CBD0080F8BC00A0C0BD00A0313D0000F93C004035BD00E048BD00E0453C00802CBC00C0FEBD0040CFBC0000A63C00000E3D00C04CBC00605E3C00402CBD0080E8BC00E094BC0060223C0000A5BA00602FBD004012BC00201A3D0060DA3B0020013C00A007BD0060AABB008008BC00E04ABD00E029BD0040723C00C008BC008065BC0020A1BC000033BE00C0F9BC000025BD00C04EBD006061BC00E09ABC00C039BD00C0BA3C00009EBD0040E83C00E061BD00004BBC0000F1BC00207D3B002053BC00803EBC00C0023D0060833C0020E63C00E0EBBC00E032BD00C0E93C006039BC00C0583D00602FBD0020F8BB008068BB00C01A3B00E0973C00A04D3B00C0203C0020153D0060CABC0020013C0060753D00E016BD0040C4BC00205B3B00007C3B00E009BE006049BB0080113D00800BBC00C08D3D006075BB00E0F8BB0040BCBC0060F1BC00E0AA3C0000FABC00C0143D0080BABC00600E3D00E09ABC0060C13A00204ABC0080033C00A00CBD0020D63C0040213C00C0E9BB0060F13C004042BC0020243B0020BD3B00403ABD0060A43C0060CB3C0000CD3C002093BC00201BBD00A09CBD00C0D3BB00A0753C00C0923C00A0DA3B00207F3C0060C93C0060CD3C00E0803B00A0093C00E003BD00A07BBC0060B43D00604F3B0080FCBC002097BD0040F7BA00A02F3D0080BD3C0060F4BB0080A5BB002081BC00A092BD0020AF3C0080D2BC00E0F4BB0020EB3C0020D4BB0060E13A0040B9BB0000B13B004044BD00C0223900A02EBC0000B0BC0060D0BC00800CBC0060823B00E0A2BC0000203D004080BA0000203B00E0043D0000B9BB00C0373C0000323C00E029BC00601D3C00E02CBC00E013BC0060D8BC0060F8BC947494622E, 'v1', '2025-05-13 19:08:03', '2025-05-13 19:08:03');
INSERT INTO item_feature_vectors VALUES ('17', '3', 'FOUND', 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v1', '2025-05-13 19:10:10', '2025-05-13 19:10:10');
INSERT INTO item_feature_vectors VALUES ('18', '27', 'LOST', 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v1', '2025-05-13 19:10:50', '2025-05-13 19:10:50');

-- ----------------------------
-- Table structure for `item_images`
-- ----------------------------
DROP TABLE IF EXISTS `item_images`;
CREATE TABLE `item_images` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) NOT NULL COMMENT '物品ID',
  `item_type` varchar(10) NOT NULL COMMENT '物品类型(LOST/FOUND)',
  `image_url` varchar(255) NOT NULL COMMENT '图片URL',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_item_id_type` (`item_id`,`item_type`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='存储失物和拾物的多张图片';

-- ----------------------------
-- Records of item_images
-- ----------------------------
INSERT INTO item_images VALUES ('1', '7', 'FOUND', 'https://cdn.laofanqi.top/images/found/2025/05/10/1746823671850_书本2.jpg', '0', '2025-05-10 04:47:52');
INSERT INTO item_images VALUES ('2', '7', 'FOUND', 'https://cdn.laofanqi.top/images/found/2025/05/10/1746823672076_书本1.jpg', '1', '2025-05-10 04:47:52');
INSERT INTO item_images VALUES ('3', '30', 'LOST', 'https://cdn.laofanqi.top/images/lost/2025/05/10/1746845848280_笔记本图片1.jpg', '0', '2025-05-10 10:57:28');
INSERT INTO item_images VALUES ('4', '30', 'LOST', 'https://cdn.laofanqi.top/images/lost/2025/05/10/1746845848434_笔记本电脑2.jpg', '1', '2025-05-10 10:57:28');

-- ----------------------------
-- Table structure for `lost_items`
-- ----------------------------
DROP TABLE IF EXISTS `lost_items`;
CREATE TABLE `lost_items` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '发布人 ID',
  `item_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '物品描述',
  `lost_time` datetime DEFAULT NULL COMMENT '丢失时间',
  `lost_location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '丢失地点',
  `image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片 URL',
  `status` enum('LOST','FOUND') COLLATE utf8mb4_unicode_ci DEFAULT 'LOST' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `audit_status` enum('PENDING','APPROVED','REJECTED') COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `lost_items_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of lost_items
-- ----------------------------
INSERT INTO lost_items VALUES ('1', '5', '手机', '黑色手机，屏幕破损', '2025-04-02 12:00:00', ' 教学楼', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1743598138595_blackPhone.jpg', 'LOST', '2025-04-02 20:48:59', 'PENDING');
INSERT INTO lost_items VALUES ('12', '5', '黑色钱包', '黑色钱包，内有身份证和银行卡', '2025-04-01 14:30:00', '图书馆', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/images/lost/2025/05/08/1746713112599_黑色钱包.png', 'LOST', '2025-04-01 15:00:00', 'PENDING');
INSERT INTO lost_items VALUES ('13', '7', '蓝色雨伞', '蓝色雨伞，丢失在餐厅', '2025-04-02 18:00:00', '餐厅', 'https://example.com/image2.jpg', 'LOST', '2025-04-02 18:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('14', '8', '红色手提包', '红色手提包，内有化妆品', '2025-03-29 10:00:00', '学校门口', 'https://cdn.laofanqi.top/images/lost/2025/05/09/1746787249296_红色手提包.jpg', 'LOST', '2025-03-29 10:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('15', '5', '黑色背包', '黑色背包，内有笔记本和手机', '2025-04-01 09:00:00', '教室', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/images/lost/2025/05/08/1746713171777_黑色背包.jpg', 'LOST', '2025-04-01 09:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('16', '7', '白色耳机', '白色耳机，丢失在图书馆', '2025-03-28 15:00:00', '图书馆', 'https://example.com/image5.jpg', 'LOST', '2025-03-28 15:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('17', '8', '绿色水杯', '绿色水杯，丢失在宿舍楼', '2025-03-30 20:00:00', '宿舍楼', 'https://cdn.laofanqi.top/images/lost/2025/05/09/1746787335493_绿色水杯.jpg', 'LOST', '2025-03-30 20:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('18', '5', '蓝色牛仔裤', '蓝色牛仔裤，丢失在超市', '2025-03-25 12:30:00', '超市', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/images/lost/2025/05/09/1746721424671_蓝色牛仔库.jpg', 'LOST', '2025-03-25 13:00:00', 'PENDING');
INSERT INTO lost_items VALUES ('19', '7', '黑色钱包', '黑色钱包，丢失在停车场', '2025-03-27 17:00:00', '停车场', 'https://example.com/image8.jpg', 'LOST', '2025-03-27 17:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('20', '8', '红色手表', '红色手表，丢失在咖啡厅', '2025-03-31 08:00:00', '咖啡厅', 'https://cdn.laofanqi.top/images/lost/2025/05/09/1746787568411_红色手表.jpg', 'LOST', '2025-03-31 08:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('21', '5', '黑色拉杆箱', '黑色拉杆箱，丢失在机场', '2025-04-03 13:30:00', '机场', 'https://cdn.laofanqi.top/images/lost/2025/05/09/1746788151331_黑色拉杆箱.jpg', 'LOST', '2025-04-03 14:00:00', 'PENDING');
INSERT INTO lost_items VALUES ('22', '8', '苹果耳机', '耳机只剩一只了，耳机仓有龙头图标', '2025-04-07 14:54:00', '篮球场', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1744008861945_苹果蓝牙耳机.jpg', 'LOST', '2025-04-07 14:54:22', 'PENDING');
INSERT INTO lost_items VALUES ('24', '8', '保温杯', '红色外观，500ml', '2025-04-07 15:04:00', '饭堂', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1744009473956_红色保温杯.jpg', 'LOST', '2025-04-07 15:04:34', 'PENDING');
INSERT INTO lost_items VALUES ('27', '8', '平板', 'ipad air 5,z紫色，爆屏', '2025-04-14 17:33:00', '教学楼', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1744623199978_ipad.jpg', 'LOST', '2025-04-14 17:33:20', 'APPROVED');
INSERT INTO lost_items VALUES ('28', '5', '钱包', '哆啦A梦卡通钱包，里面人民币若干元，还有我的证件，很重要', '2025-05-08 21:55:00', '操场', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/images/lost/2025/05/08/1746712702370_哆啦A梦钱包.jpg', 'LOST', '2025-05-08 21:58:23', 'PENDING');
INSERT INTO lost_items VALUES ('29', '8', '篮球', '篮球是李宁的，在操场忘记拿了，有没有谁捡到的', '2025-05-01 12:00:00', '篮球场', 'https://cdn.laofanqi.top/images/lost/2025/05/09/1746791580626_李宁篮球1.jpg', 'LOST', '2025-05-09 19:53:01', 'APPROVED');
INSERT INTO lost_items VALUES ('30', '5', ' 黑色笔记本电脑', '这是一台黑色的联想ThinkPad笔记本电脑，型号X1 Carbon，屏幕大小14英寸，机身有一个明显的贴纸，贴纸上有校徽图案。电脑包里还有一个充电器和一个蓝色的U盘。丢失时电脑处于关机状态，放在一个深灰色的电脑包里。', '2025-05-02 12:00:00', '图书馆三楼自习区', 'https://cdn.laofanqi.top/images/lost/2025/05/10/1746845847973_笔记本图片1.jpg', 'LOST', '2025-05-10 10:57:28', 'PENDING');

-- ----------------------------
-- Table structure for `lost_item_audit`
-- ----------------------------
DROP TABLE IF EXISTS `lost_item_audit`;
CREATE TABLE `lost_item_audit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) NOT NULL,
  `audit_status` enum('PENDING','APPROVED','REJECTED') COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  `audit_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `auditor_id` bigint(20) NOT NULL,
  `remarks` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `fk_lost_item` (`item_id`),
  CONSTRAINT `fk_lost_item` FOREIGN KEY (`item_id`) REFERENCES `lost_items` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of lost_item_audit
-- ----------------------------
INSERT INTO lost_item_audit VALUES ('56', '29', 'APPROVED', '2025-05-13 19:08:02', '5', '');
INSERT INTO lost_item_audit VALUES ('57', '27', 'APPROVED', '2025-05-13 19:10:50', '5', '');

-- ----------------------------
-- Table structure for `match_history`
-- ----------------------------
DROP TABLE IF EXISTS `match_history`;
CREATE TABLE `match_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `query_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '查询类型（TEXT/IMAGE/MIXED）',
  `query_image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '查询图片URL',
  `query_text` text COLLATE utf8mb4_unicode_ci COMMENT '查询文本',
  `item_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品类型（LOST/FOUND）',
  `result_count` int(11) DEFAULT '0' COMMENT '结果数量',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配历史表';

-- ----------------------------
-- Records of match_history
-- ----------------------------
INSERT INTO match_history VALUES ('31', '8', 'MIXED', null, null, 'FOUND', '0', '2025-05-13 19:08:03');
INSERT INTO match_history VALUES ('32', '8', 'MIXED', null, null, 'LOST', '1', '2025-05-13 19:10:11');
INSERT INTO match_history VALUES ('33', '8', 'MIXED', null, null, 'FOUND', '1', '2025-05-13 19:10:50');
INSERT INTO match_history VALUES ('34', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747134762896_李宁篮球2.jpg', null, 'found', '2', '2025-05-13 19:12:43');
INSERT INTO match_history VALUES ('35', '5', 'TEXT', null, '篮球 篮球场 05-01 00:00', 'found', '2', '2025-05-13 19:18:33');
INSERT INTO match_history VALUES ('36', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/13/1747135268496_李宁篮球2.jpg', '篮球 篮球场 05-01 00:00', 'found', '1', '2025-05-13 19:21:08');
INSERT INTO match_history VALUES ('37', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747135528151_笔记本图片1.jpg', null, 'found', '2', '2025-05-13 19:25:28');
INSERT INTO match_history VALUES ('38', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747135784280_笔记本图片1.jpg', null, 'found', '2', '2025-05-13 19:29:44');
INSERT INTO match_history VALUES ('39', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747135896338_李宁篮球2.jpg', null, 'found', '2', '2025-05-13 19:31:36');
INSERT INTO match_history VALUES ('40', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747137600797_李宁篮球2.jpg', null, 'lost', '2', '2025-05-13 20:00:01');
INSERT INTO match_history VALUES ('41', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747138118414_李宁篮球2.jpg', null, 'lost', '2', '2025-05-13 20:08:38');
INSERT INTO match_history VALUES ('42', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747138126324_李宁篮球2.jpg', null, 'found', '2', '2025-05-13 20:08:46');
INSERT INTO match_history VALUES ('43', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747138175046_李宁篮球2.jpg', null, 'found', '2', '2025-05-13 20:09:35');
INSERT INTO match_history VALUES ('44', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747145237355_李宁篮球2.jpg', null, 'found', '0', '2025-05-13 22:07:18');
INSERT INTO match_history VALUES ('45', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747146144753_李宁篮球2.jpg', null, 'found', '0', '2025-05-13 22:22:24');
INSERT INTO match_history VALUES ('46', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747146705880_李宁篮球2.jpg', null, 'found', '0', '2025-05-13 22:31:46');
INSERT INTO match_history VALUES ('47', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747147086003_李宁篮球2.jpg', null, 'found', '0', '2025-05-13 22:38:06');
INSERT INTO match_history VALUES ('48', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747147525334_李宁篮球2.jpg', null, 'found', '0', '2025-05-13 22:45:25');
INSERT INTO match_history VALUES ('49', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747148328040_李宁篮球2.jpg', null, 'found', '0', '2025-05-13 22:58:48');
INSERT INTO match_history VALUES ('50', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747148514696_李宁篮球2.jpg', null, 'lost', '0', '2025-05-13 23:01:55');
INSERT INTO match_history VALUES ('51', '5', 'IMAGE', 'https://cdn.laofanqi.top/images/2025/05/13/1747148566377_李宁篮球2.jpg', null, 'lost', '4', '2025-05-13 23:02:46');
INSERT INTO match_history VALUES ('52', '5', 'TEXT', null, '篮球 篮球场 05-01 00:00', 'found', '0', '2025-05-13 23:10:44');
INSERT INTO match_history VALUES ('53', '5', 'TEXT', null, '篮球 篮球场 05-01 00:00', 'found', '0', '2025-05-13 23:14:41');
INSERT INTO match_history VALUES ('54', '5', 'TEXT', null, '篮球 篮球场 05-01 00:00', 'found', '0', '2025-05-13 23:14:56');
INSERT INTO match_history VALUES ('55', '5', 'TEXT', null, '篮球 篮球场 05-01 00:00', 'found', '4', '2025-05-13 23:22:41');
INSERT INTO match_history VALUES ('56', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/13/1747149833164_李宁篮球2.jpg', '篮球 篮球场 05-01 00:00', 'found', '0', '2025-05-13 23:23:53');
INSERT INTO match_history VALUES ('57', '5', 'TEXT', null, '篮球 篮球场 05-01 00:00', 'found', '4', '2025-05-13 23:28:30');
INSERT INTO match_history VALUES ('58', '5', 'TEXT', null, '篮球 篮球场 05-01 00:00', 'found', '4', '2025-05-13 23:39:04');
INSERT INTO match_history VALUES ('59', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/13/1747150756728_李宁篮球2.jpg', '篮球 篮球场 05-01 00:00', 'found', '0', '2025-05-13 23:39:17');
INSERT INTO match_history VALUES ('60', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/13/1747151199610_李宁篮球2.jpg', '篮球 篮球场 05-01 00:00', 'found', '2', '2025-05-13 23:46:40');
INSERT INTO match_history VALUES ('61', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/13/1747151623598_李宁篮球2.jpg', '篮球 体育场 05-01 00:00', 'found', '2', '2025-05-13 23:53:43');
INSERT INTO match_history VALUES ('62', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/14/1747152037142_李宁篮球2.jpg', '篮球 运动场 05-01 00:00', 'found', '2', '2025-05-14 00:00:37');
INSERT INTO match_history VALUES ('63', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/14/1747152252248_李宁篮球2.jpg', '篮球 运动场 05-01 00:00', 'found', '2', '2025-05-14 00:04:12');
INSERT INTO match_history VALUES ('64', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/14/1747152829551_李宁篮球2.jpg', '篮球 运动场 05-01 00:00', 'found', '2', '2025-05-14 00:13:49');
INSERT INTO match_history VALUES ('65', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/14/1747153492108_李宁篮球2.jpg', '篮球 运动场 05-01 00:00', 'found', '2', '2025-05-14 00:24:52');
INSERT INTO match_history VALUES ('66', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/14/1747154013984_李宁篮球2.jpg', '篮球 运动场 05-01 00:00', 'found', '2', '2025-05-14 00:33:34');
INSERT INTO match_history VALUES ('67', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/14/1747154284515_李宁篮球2.jpg', '篮球 运动场 05-01 00:00', 'found', '2', '2025-05-14 00:38:04');
INSERT INTO match_history VALUES ('68', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/14/1747154629754_李宁篮球2.jpg', '篮球 运动场 05-01 00:00', 'found', '2', '2025-05-14 00:43:50');
INSERT INTO match_history VALUES ('69', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/14/1747154970600_李宁篮球2.jpg', '篮球 运动场 05-02 00:00', 'found', '2', '2025-05-14 00:49:30');
INSERT INTO match_history VALUES ('70', '5', 'MIXED', 'https://cdn.laofanqi.top/images/2025/05/14/1747155029470_李宁篮球1.jpg', '1234567 234567 05-01 00:00', 'found', '2', '2025-05-14 00:50:29');

-- ----------------------------
-- Table structure for `match_notifications`
-- ----------------------------
DROP TABLE IF EXISTS `match_notifications`;
CREATE TABLE `match_notifications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '接收通知的用户ID',
  `match_history_id` bigint(20) DEFAULT NULL COMMENT '匹配历史ID',
  `item_id` bigint(20) NOT NULL COMMENT '匹配物品ID',
  `item_type` varchar(10) NOT NULL COMMENT '匹配物品类型（LOST/FOUND）',
  `similarity` float NOT NULL COMMENT '匹配相似度',
  `title` varchar(100) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `metadata` text COMMENT '元数据(JSON格式)',
  `match_type` varchar(20) DEFAULT NULL COMMENT '匹配类型(IMAGE_TO_IMAGE/TEXT_TO_TEXT等)',
  `text_to_text_similarity` float DEFAULT NULL COMMENT '文本-文本相似度',
  `text_to_image_similarity` float DEFAULT NULL COMMENT '文本-图像相似度',
  `image_to_text_similarity` float DEFAULT NULL COMMENT '图像-文本相似度',
  `image_to_image_similarity` float DEFAULT NULL COMMENT '图像-图像相似度',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_match_history_id` (`match_history_id`),
  KEY `idx_item_id_type` (`item_id`,`item_type`),
  KEY `idx_match_notifications_match_type` (`match_type`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='匹配通知表';

-- ----------------------------
-- Records of match_notifications
-- ----------------------------

-- ----------------------------
-- Table structure for `match_results`
-- ----------------------------
DROP TABLE IF EXISTS `match_results`;
CREATE TABLE `match_results` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `match_history_id` bigint(20) NOT NULL COMMENT '匹配历史ID',
  `item_id` bigint(20) NOT NULL COMMENT '物品ID',
  `item_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品类型（LOST/FOUND）',
  `similarity_score` decimal(10,4) NOT NULL COMMENT '相似度分数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_match_history_id` (`match_history_id`),
  KEY `idx_item_id_type` (`item_id`,`item_type`)
) ENGINE=InnoDB AUTO_INCREMENT=160 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配结果表';

-- ----------------------------
-- Records of match_results
-- ----------------------------
INSERT INTO match_results VALUES ('101', '32', '29', 'LOST', '0.5621', '2025-05-13 19:10:11');
INSERT INTO match_results VALUES ('102', '33', '3', 'FOUND', '0.6322', '2025-05-13 19:10:50');
INSERT INTO match_results VALUES ('103', '34', '29', 'LOST', '0.9658', '2025-05-13 19:12:43');
INSERT INTO match_results VALUES ('104', '34', '27', 'LOST', '0.7365', '2025-05-13 19:12:43');
INSERT INTO match_results VALUES ('105', '35', '29', 'LOST', '0.9873', '2025-05-13 19:18:33');
INSERT INTO match_results VALUES ('106', '35', '27', 'LOST', '0.9566', '2025-05-13 19:18:33');
INSERT INTO match_results VALUES ('107', '36', '29', 'LOST', '0.6394', '2025-05-13 19:21:08');
INSERT INTO match_results VALUES ('108', '37', '27', 'LOST', '0.8593', '2025-05-13 19:25:28');
INSERT INTO match_results VALUES ('109', '37', '29', 'LOST', '0.7322', '2025-05-13 19:25:28');
INSERT INTO match_results VALUES ('110', '38', '27', 'LOST', '0.8593', '2025-05-13 19:29:44');
INSERT INTO match_results VALUES ('111', '38', '29', 'LOST', '0.7322', '2025-05-13 19:29:44');
INSERT INTO match_results VALUES ('112', '39', '29', 'LOST', '0.9658', '2025-05-13 19:31:36');
INSERT INTO match_results VALUES ('113', '39', '27', 'LOST', '0.7365', '2025-05-13 19:31:36');
INSERT INTO match_results VALUES ('114', '40', '29', 'LOST', '0.9658', '2025-05-13 20:00:01');
INSERT INTO match_results VALUES ('115', '40', '27', 'LOST', '0.7365', '2025-05-13 20:00:01');
INSERT INTO match_results VALUES ('116', '41', '29', 'LOST', '0.9658', '2025-05-13 20:08:38');
INSERT INTO match_results VALUES ('117', '41', '27', 'LOST', '0.7365', '2025-05-13 20:08:38');
INSERT INTO match_results VALUES ('118', '42', '29', 'LOST', '0.9658', '2025-05-13 20:08:46');
INSERT INTO match_results VALUES ('119', '42', '27', 'LOST', '0.7365', '2025-05-13 20:08:46');
INSERT INTO match_results VALUES ('120', '43', '29', 'LOST', '0.9658', '2025-05-13 20:09:35');
INSERT INTO match_results VALUES ('121', '43', '27', 'LOST', '0.7365', '2025-05-13 20:09:35');
INSERT INTO match_results VALUES ('122', '51', '29', 'LOST', '0.9658', '2025-05-13 23:02:46');
INSERT INTO match_results VALUES ('123', '51', '27', 'LOST', '0.7365', '2025-05-13 23:02:46');
INSERT INTO match_results VALUES ('124', '51', '29', 'LOST', '0.6055', '2025-05-13 23:02:46');
INSERT INTO match_results VALUES ('125', '51', '27', 'LOST', '0.5927', '2025-05-13 23:02:46');
INSERT INTO match_results VALUES ('126', '55', '29', 'LOST', '0.9873', '2025-05-13 23:22:42');
INSERT INTO match_results VALUES ('127', '55', '27', 'LOST', '0.9566', '2025-05-13 23:22:42');
INSERT INTO match_results VALUES ('128', '55', '27', 'LOST', '0.6150', '2025-05-13 23:22:42');
INSERT INTO match_results VALUES ('129', '55', '29', 'LOST', '0.6039', '2025-05-13 23:22:42');
INSERT INTO match_results VALUES ('130', '57', '29', 'LOST', '0.9873', '2025-05-13 23:28:30');
INSERT INTO match_results VALUES ('131', '57', '27', 'LOST', '0.9566', '2025-05-13 23:28:30');
INSERT INTO match_results VALUES ('132', '57', '27', 'LOST', '0.6150', '2025-05-13 23:28:30');
INSERT INTO match_results VALUES ('133', '57', '29', 'LOST', '0.6039', '2025-05-13 23:28:30');
INSERT INTO match_results VALUES ('134', '58', '29', 'LOST', '0.9873', '2025-05-13 23:39:04');
INSERT INTO match_results VALUES ('135', '58', '27', 'LOST', '0.9566', '2025-05-13 23:39:04');
INSERT INTO match_results VALUES ('136', '58', '27', 'LOST', '0.6150', '2025-05-13 23:39:04');
INSERT INTO match_results VALUES ('137', '58', '29', 'LOST', '0.6039', '2025-05-13 23:39:04');
INSERT INTO match_results VALUES ('138', '60', '29', 'LOST', '2.5578', '2025-05-13 23:46:40');
INSERT INTO match_results VALUES ('139', '60', '27', 'LOST', '2.2969', '2025-05-13 23:46:40');
INSERT INTO match_results VALUES ('140', '61', '29', 'LOST', '2.5497', '2025-05-13 23:53:44');
INSERT INTO match_results VALUES ('141', '61', '27', 'LOST', '2.2977', '2025-05-13 23:53:44');
INSERT INTO match_results VALUES ('142', '62', '29', 'LOST', '2.5483', '2025-05-14 00:00:37');
INSERT INTO match_results VALUES ('143', '62', '27', 'LOST', '2.2949', '2025-05-14 00:00:37');
INSERT INTO match_results VALUES ('144', '63', '29', 'LOST', '2.5483', '2025-05-14 00:04:12');
INSERT INTO match_results VALUES ('145', '63', '27', 'LOST', '2.2949', '2025-05-14 00:04:12');
INSERT INTO match_results VALUES ('146', '64', '29', 'LOST', '2.5483', '2025-05-14 00:13:50');
INSERT INTO match_results VALUES ('147', '64', '27', 'LOST', '2.2949', '2025-05-14 00:13:50');
INSERT INTO match_results VALUES ('148', '65', '29', 'LOST', '2.5483', '2025-05-14 00:24:52');
INSERT INTO match_results VALUES ('149', '65', '27', 'LOST', '2.2949', '2025-05-14 00:24:52');
INSERT INTO match_results VALUES ('150', '66', '29', 'LOST', '2.5483', '2025-05-14 00:33:34');
INSERT INTO match_results VALUES ('151', '66', '27', 'LOST', '2.2949', '2025-05-14 00:33:34');
INSERT INTO match_results VALUES ('152', '67', '29', 'LOST', '2.5483', '2025-05-14 00:38:05');
INSERT INTO match_results VALUES ('153', '67', '27', 'LOST', '2.2949', '2025-05-14 00:38:05');
INSERT INTO match_results VALUES ('154', '68', '29', 'LOST', '2.5483', '2025-05-14 00:43:50');
INSERT INTO match_results VALUES ('155', '68', '27', 'LOST', '2.2949', '2025-05-14 00:43:50');
INSERT INTO match_results VALUES ('156', '69', '29', 'LOST', '2.5380', '2025-05-14 00:49:30');
INSERT INTO match_results VALUES ('157', '69', '27', 'LOST', '2.2874', '2025-05-14 00:49:30');
INSERT INTO match_results VALUES ('158', '70', '29', 'LOST', '2.5003', '2025-05-14 00:50:29');
INSERT INTO match_results VALUES ('159', '70', '27', 'LOST', '2.2273', '2025-05-14 00:50:29');

-- ----------------------------
-- Table structure for `message_attachments`
-- ----------------------------
DROP TABLE IF EXISTS `message_attachments`;
CREATE TABLE `message_attachments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` bigint(20) NOT NULL,
  `file_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_type` enum('IMAGE','VIDEO','DOCUMENT','AUDIO') COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` bigint(20) DEFAULT NULL,
  `duration` int(11) DEFAULT '0' COMMENT '媒体时长（秒），用于音频和视频',
  PRIMARY KEY (`id`),
  KEY `message_id` (`message_id`),
  CONSTRAINT `message_attachments_ibfk_1` FOREIGN KEY (`message_id`) REFERENCES `chat_messages` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of message_attachments
-- ----------------------------
INSERT INTO message_attachments VALUES ('1', '297', 'https://cdn.laofanqi.top/images/2025/05/14/1747223095167_卡卡西.jpeg', 'IMAGE', '120496', '0');
INSERT INTO message_attachments VALUES ('2', '304', 'https://cdn.laofanqi.top/images/2025/05/14/1747234914036_鸣人.jpg', 'IMAGE', '5711', '0');
INSERT INTO message_attachments VALUES ('3', '305', 'https://cdn.laofanqi.top/images/2025/05/14/1747236486953_李宁篮球2.jpg', 'IMAGE', '6238', '0');
INSERT INTO message_attachments VALUES ('4', '307', 'https://cdn.laofanqi.top/images/2025/05/14/1747236671456_卡卡西.jpeg', 'IMAGE', '120496', '0');
INSERT INTO message_attachments VALUES ('5', '308', 'https://cdn.laofanqi.top/images/2025/05/15/1747239070169_blackPhone.jpg', 'IMAGE', '27022', '0');
INSERT INTO message_attachments VALUES ('6', '309', 'https://cdn.laofanqi.top/images/2025/05/15/1747239134753_哆啦A梦钱包.jpg', 'IMAGE', '5925', '0');
INSERT INTO message_attachments VALUES ('7', '311', 'https://cdn.laofanqi.top/images/2025/05/15/1747240251657_绿色水杯.jpg', 'IMAGE', '6297', '0');
INSERT INTO message_attachments VALUES ('8', '312', 'https://cdn.laofanqi.top/images/2025/05/15/1747240882999_李宁篮球1.jpg', 'IMAGE', '11929', '0');
INSERT INTO message_attachments VALUES ('9', '313', 'https://cdn.laofanqi.top/images/2025/05/15/1747240908208_卡卡西.jpeg', 'IMAGE', '120496', '0');
INSERT INTO message_attachments VALUES ('10', '314', 'https://cdn.laofanqi.top/images/2025/05/15/1747242617368_黑色拉杆箱.jpg', 'IMAGE', '4811', '0');

-- ----------------------------
-- Table structure for `message_read_status`
-- ----------------------------
DROP TABLE IF EXISTS `message_read_status`;
CREATE TABLE `message_read_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `read_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `message_id` (`message_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `message_read_status_ibfk_1` FOREIGN KEY (`message_id`) REFERENCES `chat_messages` (`id`),
  CONSTRAINT `message_read_status_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=320 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of message_read_status
-- ----------------------------
INSERT INTO message_read_status VALUES ('240', '240', '8', '1', '2025-05-14 14:30:53');
INSERT INTO message_read_status VALUES ('241', '241', '5', '1', '2025-05-14 14:31:09');
INSERT INTO message_read_status VALUES ('242', '242', '8', '1', '2025-05-14 14:44:19');
INSERT INTO message_read_status VALUES ('243', '243', '5', '1', '2025-05-14 14:41:56');
INSERT INTO message_read_status VALUES ('244', '244', '8', '1', '2025-05-14 14:53:16');
INSERT INTO message_read_status VALUES ('245', '245', '5', '1', '2025-05-14 14:57:15');
INSERT INTO message_read_status VALUES ('246', '246', '8', '1', '2025-05-14 15:30:33');
INSERT INTO message_read_status VALUES ('247', '247', '5', '1', '2025-05-14 15:30:25');
INSERT INTO message_read_status VALUES ('248', '248', '5', '1', '2025-05-14 15:30:25');
INSERT INTO message_read_status VALUES ('249', '249', '5', '1', '2025-05-14 15:43:50');
INSERT INTO message_read_status VALUES ('250', '250', '5', '1', '2025-05-14 15:43:50');
INSERT INTO message_read_status VALUES ('251', '251', '5', '1', '2025-05-14 15:58:45');
INSERT INTO message_read_status VALUES ('252', '252', '8', '1', '2025-05-14 15:58:43');
INSERT INTO message_read_status VALUES ('253', '253', '5', '1', '2025-05-14 16:08:21');
INSERT INTO message_read_status VALUES ('254', '254', '8', '1', '2025-05-14 16:11:10');
INSERT INTO message_read_status VALUES ('255', '255', '8', '1', '2025-05-14 16:20:54');
INSERT INTO message_read_status VALUES ('256', '256', '8', '1', '2025-05-14 16:20:54');
INSERT INTO message_read_status VALUES ('257', '257', '8', '1', '2025-05-14 16:26:01');
INSERT INTO message_read_status VALUES ('258', '258', '8', '1', '2025-05-14 16:26:01');
INSERT INTO message_read_status VALUES ('259', '259', '8', '1', '2025-05-14 16:25:27');
INSERT INTO message_read_status VALUES ('260', '260', '8', '1', '2025-05-14 16:26:03');
INSERT INTO message_read_status VALUES ('261', '261', '5', '1', '2025-05-14 16:59:12');
INSERT INTO message_read_status VALUES ('262', '262', '5', '1', '2025-05-14 16:59:12');
INSERT INTO message_read_status VALUES ('263', '263', '8', '1', '2025-05-14 16:58:09');
INSERT INTO message_read_status VALUES ('264', '264', '8', '1', '2025-05-14 16:58:09');
INSERT INTO message_read_status VALUES ('265', '265', '8', '1', '2025-05-14 16:59:20');
INSERT INTO message_read_status VALUES ('266', '266', '5', '1', '2025-05-14 17:10:32');
INSERT INTO message_read_status VALUES ('267', '267', '8', '1', '2025-05-14 17:11:03');
INSERT INTO message_read_status VALUES ('268', '268', '5', '1', '2025-05-14 17:38:06');
INSERT INTO message_read_status VALUES ('269', '269', '5', '1', '2025-05-14 17:38:06');
INSERT INTO message_read_status VALUES ('270', '270', '8', '1', '2025-05-14 17:37:46');
INSERT INTO message_read_status VALUES ('271', '271', '5', '1', '2025-05-14 17:38:02');
INSERT INTO message_read_status VALUES ('272', '272', '8', '1', '2025-05-14 17:38:21');
INSERT INTO message_read_status VALUES ('273', '273', '8', '1', '2025-05-14 17:38:37');
INSERT INTO message_read_status VALUES ('274', '274', '8', '1', '2025-05-14 17:56:14');
INSERT INTO message_read_status VALUES ('275', '275', '8', '1', '2025-05-14 17:56:14');
INSERT INTO message_read_status VALUES ('276', '276', '5', '1', '2025-05-14 18:49:31');
INSERT INTO message_read_status VALUES ('277', '277', '5', '1', '2025-05-14 18:49:31');
INSERT INTO message_read_status VALUES ('278', '278', '5', '1', '2025-05-14 18:49:31');
INSERT INTO message_read_status VALUES ('279', '279', '8', '1', '2025-05-14 18:27:01');
INSERT INTO message_read_status VALUES ('280', '280', '8', '1', '2025-05-14 18:32:14');
INSERT INTO message_read_status VALUES ('281', '281', '8', '1', '2025-05-14 18:49:40');
INSERT INTO message_read_status VALUES ('282', '282', '5', '1', '2025-05-14 18:49:29');
INSERT INTO message_read_status VALUES ('283', '283', '8', '1', '2025-05-14 18:49:42');
INSERT INTO message_read_status VALUES ('284', '284', '8', '1', '2025-05-14 18:49:42');
INSERT INTO message_read_status VALUES ('285', '285', '8', '1', '2025-05-14 18:52:08');
INSERT INTO message_read_status VALUES ('286', '286', '5', '1', '2025-05-14 18:51:58');
INSERT INTO message_read_status VALUES ('287', '287', '8', '1', '2025-05-14 19:03:06');
INSERT INTO message_read_status VALUES ('288', '288', '5', '1', '2025-05-14 19:03:06');
INSERT INTO message_read_status VALUES ('289', '289', '8', '1', '2025-05-14 19:03:06');
INSERT INTO message_read_status VALUES ('290', '290', '5', '1', '2025-05-14 19:03:06');
INSERT INTO message_read_status VALUES ('291', '291', '5', '1', '2025-05-14 19:03:06');
INSERT INTO message_read_status VALUES ('292', '292', '8', '1', '2025-05-14 19:03:06');
INSERT INTO message_read_status VALUES ('293', '293', '5', '1', '2025-05-14 19:03:06');
INSERT INTO message_read_status VALUES ('294', '294', '8', '1', '2025-05-14 19:05:39');
INSERT INTO message_read_status VALUES ('295', '295', '5', '1', '2025-05-14 19:05:26');
INSERT INTO message_read_status VALUES ('296', '296', '8', '1', '2025-05-14 19:05:39');
INSERT INTO message_read_status VALUES ('297', '297', '8', '1', '2025-05-14 19:53:18');
INSERT INTO message_read_status VALUES ('298', '298', '5', '1', '2025-05-14 20:58:35');
INSERT INTO message_read_status VALUES ('299', '299', '5', '1', '2025-05-14 21:33:21');
INSERT INTO message_read_status VALUES ('300', '300', '8', '1', '2025-05-14 21:33:12');
INSERT INTO message_read_status VALUES ('301', '301', '5', '1', '2025-05-14 21:33:21');
INSERT INTO message_read_status VALUES ('302', '302', '5', '1', '2025-05-14 21:48:27');
INSERT INTO message_read_status VALUES ('303', '303', '8', '1', '2025-05-14 22:01:26');
INSERT INTO message_read_status VALUES ('304', '304', '5', '1', '2025-05-14 23:04:44');
INSERT INTO message_read_status VALUES ('305', '305', '5', '1', '2025-05-14 23:28:31');
INSERT INTO message_read_status VALUES ('306', '306', '8', '1', '2025-05-15 00:00:21');
INSERT INTO message_read_status VALUES ('307', '307', '5', '1', '2025-05-14 23:35:59');
INSERT INTO message_read_status VALUES ('308', '308', '5', '1', '2025-05-15 00:11:30');
INSERT INTO message_read_status VALUES ('309', '309', '8', '1', '2025-05-15 00:19:51');
INSERT INTO message_read_status VALUES ('310', '310', '5', '1', '2025-05-15 00:36:23');
INSERT INTO message_read_status VALUES ('311', '311', '8', '1', '2025-05-15 00:36:22');
INSERT INTO message_read_status VALUES ('312', '312', '5', '1', '2025-05-15 00:50:35');
INSERT INTO message_read_status VALUES ('313', '313', '8', '1', '2025-05-15 00:50:34');
INSERT INTO message_read_status VALUES ('314', '314', '5', '1', '2025-05-15 05:38:22');
INSERT INTO message_read_status VALUES ('315', '315', '8', '1', '2025-05-15 05:36:49');
INSERT INTO message_read_status VALUES ('316', '316', '5', '0', null);
INSERT INTO message_read_status VALUES ('317', '317', '5', '1', '2025-05-15 05:34:06');
INSERT INTO message_read_status VALUES ('318', '318', '5', '0', null);
INSERT INTO message_read_status VALUES ('319', '319', '5', '0', null);

-- ----------------------------
-- Table structure for `statistics_history`
-- ----------------------------
DROP TABLE IF EXISTS `statistics_history`;
CREATE TABLE `statistics_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '统计类型：DAU, WAU, MAU, TOTAL_ITEMS, etc.',
  `stat_value` int(11) NOT NULL COMMENT '统计值',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_type` (`stat_date`,`stat_type`) COMMENT '确保每天每种类型只有一条记录'
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统计数据历史表';

-- ----------------------------
-- Records of statistics_history
-- ----------------------------
INSERT INTO statistics_history VALUES ('1', '2025-05-08', 'DAU', '2', '2025-05-09 00:05:00');
INSERT INTO statistics_history VALUES ('2', '2025-05-08', 'WAU', '3', '2025-05-09 00:05:00');
INSERT INTO statistics_history VALUES ('3', '2025-05-08', 'MAU', '3', '2025-05-09 00:05:00');
INSERT INTO statistics_history VALUES ('4', '2025-05-08', 'TOTAL_ITEMS', '6', '2025-05-09 00:05:00');
INSERT INTO statistics_history VALUES ('5', '2025-05-08', 'RETURNED_ITEMS', '0', '2025-05-09 00:05:00');
INSERT INTO statistics_history VALUES ('6', '2025-05-08', 'SUCCESS_MATCHES', '0', '2025-05-09 00:05:00');
INSERT INTO statistics_history VALUES ('7', '2025-05-09', 'DAU', '2', '2025-05-10 00:05:00');
INSERT INTO statistics_history VALUES ('8', '2025-05-09', 'WAU', '3', '2025-05-10 00:05:00');
INSERT INTO statistics_history VALUES ('9', '2025-05-09', 'MAU', '3', '2025-05-10 00:05:00');
INSERT INTO statistics_history VALUES ('10', '2025-05-09', 'TOTAL_ITEMS', '6', '2025-05-10 00:05:00');
INSERT INTO statistics_history VALUES ('11', '2025-05-09', 'RETURNED_ITEMS', '0', '2025-05-10 00:05:00');
INSERT INTO statistics_history VALUES ('12', '2025-05-09', 'SUCCESS_MATCHES', '0', '2025-05-10 00:05:00');
INSERT INTO statistics_history VALUES ('13', '2025-05-10', 'DAU', '2', '2025-05-11 00:05:00');
INSERT INTO statistics_history VALUES ('14', '2025-05-10', 'WAU', '3', '2025-05-11 00:05:00');
INSERT INTO statistics_history VALUES ('15', '2025-05-10', 'MAU', '3', '2025-05-11 00:05:00');
INSERT INTO statistics_history VALUES ('16', '2025-05-10', 'TOTAL_ITEMS', '7', '2025-05-11 00:05:00');
INSERT INTO statistics_history VALUES ('17', '2025-05-10', 'RETURNED_ITEMS', '0', '2025-05-11 00:05:00');
INSERT INTO statistics_history VALUES ('18', '2025-05-10', 'SUCCESS_MATCHES', '0', '2025-05-11 00:05:00');
INSERT INTO statistics_history VALUES ('19', '2025-05-11', 'DAU', '2', '2025-05-12 00:05:00');
INSERT INTO statistics_history VALUES ('20', '2025-05-11', 'WAU', '2', '2025-05-12 00:05:00');
INSERT INTO statistics_history VALUES ('21', '2025-05-11', 'MAU', '3', '2025-05-12 00:05:00');
INSERT INTO statistics_history VALUES ('22', '2025-05-11', 'TOTAL_ITEMS', '3', '2025-05-12 00:05:00');
INSERT INTO statistics_history VALUES ('23', '2025-05-11', 'RETURNED_ITEMS', '0', '2025-05-12 00:05:00');
INSERT INTO statistics_history VALUES ('24', '2025-05-11', 'SUCCESS_MATCHES', '0', '2025-05-12 00:05:00');
INSERT INTO statistics_history VALUES ('25', '2025-05-12', 'DAU', '2', '2025-05-13 00:05:00');
INSERT INTO statistics_history VALUES ('26', '2025-05-12', 'WAU', '2', '2025-05-13 00:05:00');
INSERT INTO statistics_history VALUES ('27', '2025-05-12', 'MAU', '3', '2025-05-13 00:05:00');
INSERT INTO statistics_history VALUES ('28', '2025-05-12', 'TOTAL_ITEMS', '0', '2025-05-13 00:05:00');
INSERT INTO statistics_history VALUES ('29', '2025-05-12', 'RETURNED_ITEMS', '0', '2025-05-13 00:05:00');
INSERT INTO statistics_history VALUES ('30', '2025-05-12', 'SUCCESS_MATCHES', '0', '2025-05-13 00:05:00');
INSERT INTO statistics_history VALUES ('31', '2025-05-13', 'DAU', '2', '2025-05-14 00:05:00');
INSERT INTO statistics_history VALUES ('32', '2025-05-13', 'WAU', '2', '2025-05-14 00:05:00');
INSERT INTO statistics_history VALUES ('33', '2025-05-13', 'MAU', '3', '2025-05-14 00:05:00');
INSERT INTO statistics_history VALUES ('34', '2025-05-13', 'TOTAL_ITEMS', '3', '2025-05-14 00:05:00');
INSERT INTO statistics_history VALUES ('35', '2025-05-13', 'RETURNED_ITEMS', '0', '2025-05-14 00:05:00');
INSERT INTO statistics_history VALUES ('36', '2025-05-13', 'SUCCESS_MATCHES', '0', '2025-05-14 00:05:00');
INSERT INTO statistics_history VALUES ('37', '2025-05-14', 'DAU', '2', '2025-05-15 00:05:00');
INSERT INTO statistics_history VALUES ('38', '2025-05-14', 'WAU', '2', '2025-05-15 00:05:00');
INSERT INTO statistics_history VALUES ('39', '2025-05-14', 'MAU', '3', '2025-05-15 00:05:00');
INSERT INTO statistics_history VALUES ('40', '2025-05-14', 'TOTAL_ITEMS', '3', '2025-05-15 00:05:00');
INSERT INTO statistics_history VALUES ('41', '2025-05-14', 'RETURNED_ITEMS', '0', '2025-05-15 00:05:00');
INSERT INTO statistics_history VALUES ('42', '2025-05-14', 'SUCCESS_MATCHES', '0', '2025-05-15 00:05:00');

-- ----------------------------
-- Table structure for `system_announcements`
-- ----------------------------
DROP TABLE IF EXISTS `system_announcements`;
CREATE TABLE `system_announcements` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告标题',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告内容',
  `importance` enum('NORMAL','IMPORTANT','URGENT') COLLATE utf8mb4_unicode_ci DEFAULT 'NORMAL' COMMENT '重要程度',
  `start_time` datetime NOT NULL COMMENT '生效时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `created_by` bigint(20) NOT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` enum('DRAFT','PUBLISHED','EXPIRED') COLLATE utf8mb4_unicode_ci DEFAULT 'DRAFT' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_status_time` (`status`,`start_time`,`end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of system_announcements
-- ----------------------------
INSERT INTO system_announcements VALUES ('1', '系统维护通知', '尊敬的用户，我们将于2023年12月15日凌晨2:00-4:00进行系统维护，期间系统将暂停服务。给您带来的不便，敬请谅解。', 'IMPORTANT', '2023-12-09 00:00:00', '2026-12-24 00:00:00', '1', '2025-05-04 00:34:43', '2025-05-11 21:44:37', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('2', '失物招领平台使用指南', '为了提高失物招领效率，我们更新了平台使用指南。请所有用户仔细阅读，了解如何正确发布失物和招领信息，以及如何使用智能匹配功能。', 'IMPORTANT', '2023-11-01 00:00:00', null, '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('3', '校园失物招领活动', '我们将于本月20日在学生中心举办校园失物招领专场活动，欢迎所有同学参加。活动现场将展示近期收集的失物，并提供现场认领服务。', 'NORMAL', '2023-11-15 00:00:00', '2023-12-20 00:00:00', '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('4', '系统升级完成通知', '系统升级已于昨日完成，新版本增加了多项功能，包括智能匹配算法优化、界面美化等。感谢您的支持与理解。', 'NORMAL', '2023-10-01 00:00:00', '2023-10-31 00:00:00', '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('5', '新功能预告', '我们即将推出全新的移动端应用，敬请期待！', 'IMPORTANT', '2024-01-01 00:00:00', null, '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'DRAFT');
INSERT INTO system_announcements VALUES ('6', '2024年春季学期失物招领须知', '新学期开始，请大家注意保管好自己的物品。如有丢失，请及时在平台发布信息。', 'NORMAL', '2024-02-01 00:00:00', null, '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('7', '关于加强校园失物招领管理的通知', '为进一步规范校园失物招领管理，提高服务质量，特制定以下规定：\n\n\n     1. 所有拾获的物品应在24小时内上交至失物招领中心或在平台发布信息；\n\n     2. 贵重物品（如手机、电脑、钱包等）必须由管理员验证后才能发布；\n\n     3. 认领物品时，认领人需提供有效证件和物品特征描述；\n\n     4. 发布虚假信息者将被平台禁言处理；\n\n     5. 平台保留对违规行为进行处理的权利。\n\n\n     请所有用户遵守以上规定，共同维护良好的校园环境。', 'IMPORTANT', '2023-11-10 00:00:00', null, '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('8', '重要安全提醒', '近期校园内出现多起财物丢失事件，请同学们妥善保管个人物品，特别是在图书馆、食堂等公共场所。', 'URGENT', '2025-05-03 16:00:00', '2025-05-14 16:00:00', '1', '2025-05-04 00:34:43', '2025-05-12 03:32:21', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('9', '你好', '请问你能看到这个公告吗？', 'NORMAL', '2025-05-11 13:45:32', '2025-05-14 16:00:00', '5', '2025-05-11 21:45:53', '2025-05-11 21:46:47', 'PUBLISHED');

-- ----------------------------
-- Table structure for `system_configs`
-- ----------------------------
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text COLLATE utf8mb4_unicode_ci COMMENT '配置值',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ----------------------------
-- Records of system_configs
-- ----------------------------
INSERT INTO system_configs VALUES ('1', 'match.notification.similarity-threshold', '0.7', '触发匹配通知的相似度阈值', '2025-05-03 17:25:49', '2025-05-03 17:25:49');
INSERT INTO system_configs VALUES ('2', 'match.notification.auto-notify', 'true', '是否启用自动匹配通知功能', '2025-05-03 17:25:49', '2025-05-03 17:25:49');
INSERT INTO system_configs VALUES ('3', 'aliyun.cdn.domain', 'https://cdn.laofanqi.top', 'CDN域名', '2025-05-09 16:09:07', '2025-05-09 16:09:07');
INSERT INTO system_configs VALUES ('4', 'aliyun.cdn.enabled', 'true', 'CDN是否启用', '2025-05-09 16:09:07', '2025-05-09 16:09:07');
INSERT INTO system_configs VALUES ('5', 'aliyun.cdn.last_updated', '2025-05-09 16:09:07', 'CDN最后更新时间', '2025-05-09 16:09:07', '2025-05-09 16:09:07');
INSERT INTO system_configs VALUES ('6', 'autodl.clip.api.url', 'http://localhost:8000', 'AutoDL CLIP+FAISS服务URL', '2025-05-10 12:18:54', '2025-05-10 12:53:50');
INSERT INTO system_configs VALUES ('7', 'autodl.clip.service.check-enabled', 'true', 'CLIP+FAISS服务检查是否启用', '2025-05-10 12:19:00', '2025-05-10 12:19:00');
INSERT INTO system_configs VALUES ('8', 'autodl.clip.service.connection-timeout', '5000', 'CLIP+FAISS服务连接超时时间(毫秒)', '2025-05-10 12:19:06', '2025-05-10 12:19:06');
INSERT INTO system_configs VALUES ('9', 'match.filter.text-to-text-threshold', '0.60', '文本到文本匹配的过滤阈值', '2025-05-13 17:01:52', '2025-05-13 17:01:52');
INSERT INTO system_configs VALUES ('10', 'match.filter.image-to-image-threshold', '0.55', '图像到图像匹配的过滤阈值', '2025-05-13 17:01:52', '2025-05-13 17:01:52');
INSERT INTO system_configs VALUES ('11', 'match.filter.text-to-image-threshold', '0.45', '文本到图像匹配的过滤阈值', '2025-05-13 17:01:52', '2025-05-13 17:01:52');
INSERT INTO system_configs VALUES ('12', 'match.filter.image-to-text-threshold', '0.3', '图像到文本匹配的过滤阈值', '2025-05-13 17:01:52', '2025-05-13 19:41:02');
INSERT INTO system_configs VALUES ('13', 'match.filter.combined-threshold', '0.50', '综合匹配的过滤阈值', '2025-05-13 17:01:52', '2025-05-13 17:01:52');

-- ----------------------------
-- Table structure for `users`
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码（加密）',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `role` enum('USER','ADMIN','SUPER_ADMIN') COLLATE utf8mb4_unicode_ci DEFAULT 'USER',
  `create_time` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否逻辑删除',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户头像URL',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_email` (`email`),
  UNIQUE KEY `unique_phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO users VALUES ('5', 'testuser1', '$2a$10$.6rE8HL3kRnp/E8KTdzrFOnvZ2j/Oa8Nt4G6qzzIRWrZdKxkmBVcC', '<EMAIL>', null, 'SUPER_ADMIN', '2025-04-02 00:00:49', '1', '0', '2025-05-09 17:39:07', 'https://cdn.laofanqi.top/avatars/avatar_5_55405872b9fc491f98eed20210d382c6_medium.png');
INSERT INTO users VALUES ('7', '王小明', '$2a$10$mmeDopvidBDsDVdGCcTFYOYkMnCsT.nvlWpdTKJrmY17zG3QiVdtS', '<EMAIL>', null, 'USER', '2025-04-03 16:47:14', '1', '0', '2025-04-17 16:07:36', null);
INSERT INTO users VALUES ('8', 'wangyang', '$2a$10$pEhmQJdcvSgXTXjIxMte5uM7dZMeeDUBnTAMuM3LymO/V7gehSJ0u', null, '18929741873', 'USER', '2025-04-03 17:27:28', '1', '0', '2025-05-09 19:13:49', 'https://cdn.laofanqi.top/avatars/avatar_8_e75cb09b0bd8452588c6c4e06792db1f_medium.png');
INSERT INTO users VALUES ('9', 'admin1', '$2a$10$nDmB0kD60DLfcd5attSlBOepRuGqg3KHoAUeMbdNPjnrHwHlNvG.q', null, null, 'ADMIN', null, '1', '0', '2025-04-17 16:07:36', null);

-- ----------------------------
-- Table structure for `user_announcement_reads`
-- ----------------------------
DROP TABLE IF EXISTS `user_announcement_reads`;
CREATE TABLE `user_announcement_reads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `announcement_id` bigint(20) NOT NULL COMMENT '公告ID',
  `read_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_announcement` (`user_id`,`announcement_id`),
  KEY `idx_announcement_id` (`announcement_id`),
  CONSTRAINT `fk_user_announcement_reads_announcement` FOREIGN KEY (`announcement_id`) REFERENCES `system_announcements` (`id`),
  CONSTRAINT `fk_user_announcement_reads_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=403 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of user_announcement_reads
-- ----------------------------
INSERT INTO user_announcement_reads VALUES ('370', '5', '7', '2025-05-14 21:14:50');
INSERT INTO user_announcement_reads VALUES ('371', '5', '9', '2025-05-14 06:18:54');
INSERT INTO user_announcement_reads VALUES ('372', '5', '8', '2025-05-14 06:18:54');
INSERT INTO user_announcement_reads VALUES ('375', '5', '6', '2025-05-14 21:14:50');
INSERT INTO user_announcement_reads VALUES ('376', '5', '1', '2025-05-14 21:14:50');
INSERT INTO user_announcement_reads VALUES ('382', '8', '1', '2025-05-14 02:00:07');
INSERT INTO user_announcement_reads VALUES ('383', '8', '2', '2025-05-14 02:00:07');
INSERT INTO user_announcement_reads VALUES ('387', '8', '8', '2025-05-14 02:00:07');
INSERT INTO user_announcement_reads VALUES ('390', '8', '7', '2025-05-14 02:00:07');
INSERT INTO user_announcement_reads VALUES ('391', '8', '9', '2025-05-14 02:00:07');
INSERT INTO user_announcement_reads VALUES ('392', '8', '6', '2025-05-14 02:00:07');
INSERT INTO user_announcement_reads VALUES ('393', '5', '2', '2025-05-14 21:14:50');

-- ----------------------------
-- Table structure for `user_notifications`
-- ----------------------------
DROP TABLE IF EXISTS `user_notifications`;
CREATE TABLE `user_notifications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('UNREAD','READ') COLLATE utf8mb4_unicode_ci DEFAULT 'UNREAD',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'SYSTEM' COMMENT '通知类型',
  `related_item_id` bigint(20) DEFAULT NULL COMMENT '关联物品ID',
  `related_item_type` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联物品类型(LOST/FOUND)',
  `metadata` text COLLATE utf8mb4_unicode_ci COMMENT '额外元数据(JSON格式)',
  `auditor_id` bigint(20) DEFAULT NULL COMMENT '审核员ID(仅审核通知)',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_notifications_type` (`type`),
  KEY `idx_notifications_related_item` (`related_item_id`,`related_item_type`),
  KEY `idx_notifications_auditor_id` (`auditor_id`),
  CONSTRAINT `user_notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of user_notifications
-- ----------------------------
INSERT INTO user_notifications VALUES ('92', '8', '您的发布已通过审核', '您的失物信息（ID: 29）已经审核通过，并成功发布。', 'READ', '2025-05-13 19:08:01', '2025-05-14 02:00:07', 'AUDIT_APPROVED', '29', 'LOST', null, null);
INSERT INTO user_notifications VALUES ('93', '8', '您的发布已通过审核', '您的拾物信息（ID: 3）已经审核通过，并成功发布。', 'READ', '2025-05-13 19:10:10', '2025-05-14 02:00:07', 'AUDIT_APPROVED', '3', 'FOUND', null, null);
INSERT INTO user_notifications VALUES ('94', '8', '您的发布已通过审核', '您的失物信息（ID: 27）已经审核通过，并成功发布。', 'READ', '2025-05-13 19:10:49', '2025-05-14 02:00:07', 'AUDIT_APPROVED', '27', 'LOST', null, null);
