package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.MatchNotification;

import java.util.List;
import java.util.Map;

/**
 * 匹配通知服务接口
 */
public interface MatchNotificationService {

    /**
     * 创建匹配通知
     *
     * @param userId 用户ID
     * @param matchHistoryId 匹配历史ID
     * @param itemId 物品ID
     * @param itemType 物品类型
     * @param similarity 相似度
     * @param itemInfo 物品信息
     * @return 处理结果
     */
    Result createMatchNotification(Long userId, Long matchHistoryId, Long itemId, String itemType, 
                                  Float similarity, Map<String, Object> itemInfo);

    /**
     * 获取用户的匹配通知列表
     *
     * @param userId 用户ID
     * @return 通知列表
     */
    Result<List<MatchNotification>> getUserNotifications(Long userId);

    /**
     * 标记通知为已读
     *
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 处理结果
     */
    Result markNotificationAsRead(Long notificationId, Long userId);

    /**
     * 获取用户未读通知数量
     *
     * @param userId 用户ID
     * @return 未读通知数量
     */
    Result<Integer> getUnreadNotificationCount(Long userId);
    
    /**
     * 处理高相似度匹配结果并发送通知
     *
     * @param matchResults 匹配结果
     * @param matchHistoryId 匹配历史ID
     * @param userId 用户ID
     * @param similarityThreshold 相似度阈值
     * @return 处理结果
     */
    Result processHighSimilarityMatches(List<Map<String, Object>> matchResults, Long matchHistoryId, 
                                       Long userId, Float similarityThreshold);
}
