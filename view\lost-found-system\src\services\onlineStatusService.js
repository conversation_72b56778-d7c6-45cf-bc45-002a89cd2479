/**
 * 在线状态服务
 * 统一管理用户在线状态的订阅和更新
 */
import { useOnlineStatusStore } from '@/stores/onlineStatusStore'
import { useUserStore } from '@/stores'

// 全局订阅引用
let globalStatusSubscription = null
let onlineCountSubscription = null

/**
 * 获取WebSocket客户端
 * 注意：这里使用函数而不是直接导入，避免循环依赖
 */
function getWebSocketClient() {
  // 首先尝试从window对象获取stompClient
  if (window.wsClient && window.wsClient.stompClient) {
    return window.wsClient.stompClient
  }

  // 如果window对象上没有，尝试动态导入
  try {
    // 使用同步方式检查WebSocket连接状态
    const { isWebSocketConnected } = require('@/utils/websocket/index')
    if (isWebSocketConnected()) {
      console.log('WebSocket已连接，但window.wsClient.stompClient未定义')
      // 这种情况下，可能需要重新初始化WebSocket
      setTimeout(() => {
        const { reinitializeWebSocketService } = require('@/utils/websocket/index')
        reinitializeWebSocketService().then(success => {
          console.log('WebSocket重新初始化结果:', success ? '成功' : '失败')
        })
      }, 1000)
    }
  } catch (error) {
    console.warn('动态导入WebSocket模块失败:', error)
  }

  return null
}

// 定期同步定时器
let periodicSyncTimer = null

/**
 * 初始化在线状态服务
 */
export function initOnlineStatusService() {
  const store = useOnlineStatusStore()

  // 标记服务已初始化
  store.setServiceInitialized(true)

  // 启动定期同步
  startPeriodicSync()

  // 页面加载时主动请求最新的在线用户统计数据
  // 使用setTimeout确保在其他初始化完成后执行
  setTimeout(() => {
    console.log('页面加载时主动请求最新的在线用户统计数据')

    // 检查WebSocket是否已连接
    try {
      import('@/utils/websocket/index').then(module => {
        const connected = module.isWebSocketConnected()
        if (connected) {
          console.log('WebSocket已连接，立即请求在线用户列表')
          requestOnlineUsers()
        } else {
          console.log('WebSocket未连接，将在连接后请求在线用户列表')
        }
      })
    } catch (error) {
      console.warn('检查WebSocket连接状态失败:', error)
    }
  }, 1000)

  // 监听WebSocket连接事件
  window.addEventListener('websocket-connected', () => {
    console.log('WebSocket已连接，初始化在线状态服务')
    store.setConnectionStatus(true, false)

    // 延迟一点时间，确保WebSocket连接已完全建立
    setTimeout(() => {
      subscribeToOnlineStatus()
      requestOnlineUsers()

      // 确保定期同步已启动
      startPeriodicSync()
    }, 500)
  })

  // 监听WebSocket断开连接事件
  window.addEventListener('websocket-disconnected', () => {
    console.log('WebSocket已断开连接')
    store.setConnectionStatus(false, false)

    // 停止定期同步
    stopPeriodicSync()
  })

  // 监听WebSocket重连事件
  window.addEventListener('websocket-reconnecting', (event) => {
    console.log('WebSocket正在重连:', event.detail)
    store.setConnectionStatus(false, true, event.detail.attempts)
  })

  // 监听用户状态更新事件
  window.addEventListener('user-online-status', (event) => {
    const { userId, isOnline, timestamp } = event.detail

    // 更新Store中的用户状态
    const changed = store.updateUserStatus(userId, isOnline)

    if (changed) {
      console.log(`用户 ${userId} 在线状态已更新为: ${isOnline ? '在线' : '离线'}`)

      // 触发全局事件
      window.dispatchEvent(new CustomEvent('online-status-changed', {
        detail: {
          userId,
          isOnline,
          timestamp: timestamp || Date.now()
        }
      }))
    }
  })

  // 检查WebSocket是否已连接
  try {
    // 使用动态导入替代require
    import('@/utils/websocket/index').then(module => {
      const connected = module.isWebSocketConnected()
      store.setConnectionStatus(connected, false)

      if (connected) {
        console.log('WebSocket已连接，初始化订阅')
        // 初始订阅
        setTimeout(() => {
          subscribeToOnlineStatus()
          requestOnlineUsers()
        }, 500)
      } else {
        console.warn('WebSocket未连接，在线状态服务将在连接成功后自动初始化')
      }
    }).catch(error => {
      console.warn('导入WebSocket模块失败:', error)
      store.setConnectionStatus(false, false)
    })
  } catch (error) {
    console.warn('检查WebSocket连接状态失败:', error)
    store.setConnectionStatus(false, false)
  }

  return {
    isUserOnline: (userId) => store.isUserOnline(userId),
    getUserLastActiveTime: (userId) => store.getUserLastActiveTime(userId),
    getOnlineUserCount: () => store.onlineUserCount,
    getOnlineUsers: () => store.onlineUsersList
  }
}

/**
 * 订阅在线状态更新
 */
function subscribeToOnlineStatus() {
  const stompClient = getWebSocketClient()

  if (!stompClient || !stompClient.connected) {
    console.warn('WebSocket未连接，无法订阅在线状态')
    return false
  }

  try {
    // 取消现有订阅
    if (globalStatusSubscription) {
      try {
        globalStatusSubscription.unsubscribe()
        console.log('已取消旧的在线状态订阅')
      } catch (e) {
        console.warn('取消旧订阅时出错:', e)
      }
    }

    if (onlineCountSubscription) {
      try {
        onlineCountSubscription.unsubscribe()
        console.log('已取消旧的在线用户数量订阅')
      } catch (e) {
        console.warn('取消旧订阅时出错:', e)
      }
    }

    // 获取用户信息
    const userStore = useUserStore()
    const username = userStore.userInfo?.username

    if (username) {
      console.log('当前用户名:', username)
    } else {
      console.warn('未找到用户名，可能影响在线用户列表订阅')
    }

    // 订阅全局在线状态主题
    globalStatusSubscription = stompClient.subscribe('/topic/onlineStatus', (message) => {
      handleOnlineStatusMessage(message.body)
    })

    // 订阅在线用户数量主题
    onlineCountSubscription = stompClient.subscribe('/topic/onlineCount', (message) => {
      handleOnlineCountMessage(message.body)
    })

    // 订阅在线用户列表主题
    try {
      // 只订阅广播主题 /topic/onlineUsers，简化订阅逻辑
      stompClient.subscribe('/topic/onlineUsers', (message) => {
        console.log('收到广播在线用户列表消息:', message)
        handleOnlineUsersListMessage(message.body)
      })
      console.log('已订阅广播在线用户列表主题: /topic/onlineUsers')
    } catch (e) {
      console.error('订阅在线用户列表主题失败:', e)
    }

    console.log('在线状态订阅成功')
    return true
  } catch (error) {
    console.error('订阅在线状态失败:', error)
    return false
  }
}

/**
 * 处理在线状态消息
 */
function handleOnlineStatusMessage(messageBody) {
  try {
    const store = useOnlineStatusStore()
    let userId, isOnline, timestamp;

    // 尝试解析为JSON
    try {
      const data = JSON.parse(messageBody)
      if (data.userId && typeof data.online === 'boolean') {
        // JSON格式的状态消息
        userId = data.userId;
        isOnline = data.online;
        timestamp = data.timestamp || Date.now();

        // 更新状态
        const changed = store.updateUserStatus(userId, isOnline);

        // 如果状态变化了，触发事件
        if (changed) {
          triggerStatusChangedEvent(userId, isOnline, timestamp);
        }
        return;
      }
    } catch (e) {
      // 不是JSON格式，继续尝试文本格式
    }

    // 尝试解析文本格式: "User {userId} is online/offline"
    const match = messageBody.match(/User (\d+) is (online|offline|back online)/)
    if (match) {
      userId = match[1];
      const status = match[2];
      isOnline = status === 'online' || status === 'back online';
      timestamp = Date.now();

      // 更新状态
      const changed = store.updateUserStatus(userId, isOnline);

      // 如果状态变化了，触发事件
      if (changed) {
        triggerStatusChangedEvent(userId, isOnline, timestamp);
      }
    }
  } catch (error) {
    console.error('处理在线状态消息时出错:', error)
  }
}

/**
 * 触发状态变更事件
 */
function triggerStatusChangedEvent(userId, isOnline, timestamp) {
  console.log(`用户 ${userId} 在线状态已更新为: ${isOnline ? '在线' : '离线'}`);

  const eventTimestamp = timestamp || Date.now();

  // 使用防抖处理，避免短时间内多次触发同一用户的状态更新
  if (!window._statusEventDebounce) {
    window._statusEventDebounce = new Map();
  }

  // 如果已经有相同用户的状态更新在处理中，取消它
  if (window._statusEventDebounce.has(userId)) {
    clearTimeout(window._statusEventDebounce.get(userId));
  }

  // 设置新的延迟处理
  window._statusEventDebounce.set(userId, setTimeout(() => {
    // 触发全局事件 - 使用新的统一事件
    window.dispatchEvent(new CustomEvent('online-status-changed', {
      detail: {
        userId,
        isOnline,
        timestamp: eventTimestamp
      }
    }));

    // 清理防抖Map
    window._statusEventDebounce.delete(userId);

    // 每5秒最多触发一次全局用户列表更新事件
    if (!window._lastUsersListUpdate || (Date.now() - window._lastUsersListUpdate) > 5000) {
      window._lastUsersListUpdate = Date.now();

      // 触发在线用户列表更新事件
      const store = useOnlineStatusStore();
      window.dispatchEvent(new CustomEvent('online-users-updated', {
        detail: {
          users: store.onlineUsersList,
          count: store.onlineUserCount,
          timestamp: eventTimestamp
        }
      }));
    }
  }, 500)); // 500ms防抖
}

/**
 * 处理在线用户数量消息
 */
function handleOnlineCountMessage(messageBody) {
  try {
    // 尝试解析JSON
    let data;
    try {
      data = JSON.parse(messageBody)
    } catch (jsonError) {
      console.error('解析在线用户数量JSON失败:', jsonError, '原始消息:', messageBody)
      return
    }

    // 记录收到的数据
    const onlineCount = data.onlineCount || 0
    const timestamp = data.timestamp || Date.now()
    console.log(`收到在线用户数量: ${onlineCount}, 时间戳: ${new Date(timestamp).toLocaleTimeString()}`)

    // 获取Pinia Store
    const store = useOnlineStatusStore()

    // 记录更新前的状态
    const prevCount = store.onlineUserCount

    // 更新在线用户数量
    store.setOnlineCount(onlineCount)

    // 记录更新后的状态变化
    if (prevCount !== onlineCount) {
      console.log(`在线用户数量变化: ${prevCount} -> ${onlineCount}`)
    }

    // 如果消息中包含在线用户ID列表，更新在线用户列表
    if (data.onlineUserIds && Array.isArray(data.onlineUserIds)) {
      // 将数字ID转换为字符串ID
      const userIds = data.onlineUserIds.map(id => String(id))

      // 记录更新前的用户列表
      const prevUsers = [...store.onlineUsersList]

      // 更新在线用户列表
      store.updateOnlineUsers(userIds)

      // 记录新增和移除的用户
      const added = userIds.filter(id => !prevUsers.includes(id))
      const removed = prevUsers.filter(id => !userIds.includes(id))

      if (added.length > 0) {
        console.log(`新增在线用户: ${added.join(', ')}`)
      }

      if (removed.length > 0) {
        console.log(`移除在线用户: ${removed.join(', ')}`)
      }

      console.log(`从在线用户数量消息更新在线用户列表: ${userIds.length}个用户`)
    } else if (onlineCount > 0 && store.onlineUsersList.length === 0) {
      // 如果没有在线用户ID列表，但有在线用户数量，主动请求一次在线用户列表
      console.log('在线用户数量不为0但列表为空，主动请求在线用户列表')
      setTimeout(() => requestOnlineUsers(), 500)
    } else if (onlineCount === 0 && store.onlineUsersList.length > 0) {
      // 如果在线用户数量为0但列表不为空，可能是数据不一致
      console.warn('在线用户数量为0但列表不为空，可能是数据不一致')
      // 清空在线用户列表
      store.updateOnlineUsers([])
    } else if (onlineCount !== store.onlineUsersList.length) {
      // 如果在线用户数量与列表长度不一致，可能是数据不一致
      console.warn(`在线用户数量(${onlineCount})与列表长度(${store.onlineUsersList.length})不一致，可能是数据不一致`)
      // 主动请求一次在线用户列表
      setTimeout(() => requestOnlineUsers(), 1000)
    }

    // 触发全局事件通知UI更新
    window.dispatchEvent(new CustomEvent('online-count-updated', {
      detail: {
        count: onlineCount,
        userIds: data.onlineUserIds || [],
        timestamp: timestamp,
        prevCount: prevCount
      }
    }))
  } catch (error) {
    console.error('处理在线用户数量消息时出错:', error)

    // 发生错误时，尝试重新请求
    setTimeout(() => {
      console.log('由于处理错误，重新请求在线用户列表')
      requestOnlineUsers()
    }, 3000)
  }
}

/**
 * 处理在线用户列表消息
 */
function handleOnlineUsersListMessage(messageBody) {
  try {
    console.log('处理在线用户列表消息:', messageBody)

    // 尝试解析JSON
    let data;
    try {
      data = JSON.parse(messageBody)
    } catch (jsonError) {
      console.error('解析在线用户列表JSON失败:', jsonError, '原始消息:', messageBody)
      return
    }

    // 记录请求ID（如果有）
    const requestId = data.requestId || '未知'
    console.log(`处理在线用户列表响应 (ID: ${requestId})`)

    // 检查数据格式
    if (data && data.onlineUsers) {
      // 获取在线用户列表
      const onlineUsers = data.onlineUsers
      const userIds = Object.keys(onlineUsers)
      console.log(`收到${userIds.length}个在线用户 (ID: ${requestId}):`, userIds)

      // 获取Store
      const store = useOnlineStatusStore()

      // 记录更新前的状态
      const prevCount = store.onlineUserCount
      const prevUsers = [...store.onlineUsersList]

      // 更新在线用户列表
      const result = store.updateOnlineUsers(userIds)

      // 记录更新后的状态变化
      console.log(`在线用户数量变化: ${prevCount} -> ${store.onlineUserCount}`)

      // 记录新增和移除的用户
      const added = userIds.filter(id => !prevUsers.includes(id))
      const removed = prevUsers.filter(id => !userIds.includes(id))

      if (added.length > 0) {
        console.log(`新增在线用户: ${added.join(', ')}`)
      }

      if (removed.length > 0) {
        console.log(`移除在线用户: ${removed.join(', ')}`)
      }

      console.log('更新在线用户列表结果:', result)

      // 更新连接状态
      store.setConnectionStatus(true, false)

      // 触发全局事件
      window.dispatchEvent(new CustomEvent('online-users-updated', {
        detail: {
          users: userIds,
          count: userIds.length,
          timestamp: data.timestamp || Date.now(),
          requestId: requestId
        }
      }))

      // 如果在线用户数量为0但应该有用户在线，可能是数据不一致
      if (userIds.length === 0 && prevCount > 0) {
        console.warn('收到空的在线用户列表，但之前有用户在线，可能是数据不一致')
        // 5秒后重新请求
        setTimeout(() => {
          console.log('由于可能的数据不一致，重新请求在线用户列表')
          requestOnlineUsers()
        }, 5000)
      }
    } else {
      console.warn(`收到的在线用户列表数据格式不正确 (ID: ${requestId}):`, data)

      // 如果数据格式不正确，尝试重新请求
      setTimeout(() => {
        console.log('由于数据格式不正确，重新请求在线用户列表')
        requestOnlineUsers()
      }, 3000)
    }
  } catch (error) {
    console.error('处理在线用户列表消息时出错:', error)

    // 发生错误时，尝试重新请求
    setTimeout(() => {
      console.log('由于处理错误，重新请求在线用户列表')
      requestOnlineUsers()
    }, 3000)
  }
}

/**
 * 请求在线用户列表
 * @param {number} retryCount 重试次数
 * @returns {boolean} 是否成功发送请求
 */
export function requestOnlineUsers(retryCount = 0) {
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 1000; // 1秒后重试

  console.log(`===== 请求在线用户列表 (尝试 ${retryCount + 1}/${MAX_RETRIES + 1}) =====`)

  const stompClient = getWebSocketClient()

  if (!stompClient || !stompClient.connected) {
    console.warn(`WebSocket未连接，无法请求在线用户列表 (尝试 ${retryCount + 1}/${MAX_RETRIES + 1})`)

    // 如果未达到最大重试次数，则安排重试
    if (retryCount < MAX_RETRIES) {
      console.log(`将在 ${RETRY_DELAY}ms 后重试请求在线用户列表`)
      setTimeout(() => {
        requestOnlineUsers(retryCount + 1)
      }, RETRY_DELAY)
    } else {
      console.error('达到最大重试次数，放弃请求在线用户列表')

      // 尝试通过刷新页面获取最新数据
      console.log('尝试通过刷新页面获取最新数据')
      // 不要真的刷新页面，只是提示用户
      window.dispatchEvent(new CustomEvent('online-status-error', {
        detail: {
          message: '无法获取在线用户列表，请尝试刷新页面',
          timestamp: Date.now()
        }
      }))
    }

    return false
  }

  try {
    const userStore = useUserStore()
    const token = userStore.token
    const requestId = `req-${Date.now()}-${Math.floor(Math.random() * 1000)}`

    console.log(`准备发送请求 (ID: ${requestId})`)
    console.log(`- 目标: /app/getAllOnlineUsers`)
    console.log(`- 用户: ${userStore.userInfo?.username || '未知'}`)
    console.log(`- 令牌: ${token ? '已设置' : '未设置'}`)

    // 发送请求前，先检查订阅情况
    if (stompClient.subscriptions) {
      const subscriptions = Object.keys(stompClient.subscriptions)
      console.log(`当前有 ${subscriptions.length} 个订阅:`)
      subscriptions.forEach(subId => {
        const sub = stompClient.subscriptions[subId]
        console.log(`- ${subId}: ${sub.destination}`)
      })
    } else {
      console.warn('无法获取订阅信息')
    }

    // 发送请求
    stompClient.publish({
      destination: '/app/getAllOnlineUsers',
      headers: {
        'Authorization': `Bearer ${token}`,
        'request-id': requestId,
        'username': userStore.userInfo?.username || 'unknown'
      },
      body: JSON.stringify({
        timestamp: Date.now(),
        requestId: requestId,
        username: userStore.userInfo?.username || 'unknown'
      })
    })

    console.log(`已发送获取在线用户列表请求 (ID: ${requestId})`)

    // 设置超时检查
    setTimeout(() => {
      const store = useOnlineStatusStore()
      console.log(`检查请求 ${requestId} 的响应情况:`)
      console.log(`- 当前在线用户列表长度: ${store.onlineUsersList.length}`)
      console.log(`- 当前在线用户数量: ${store.onlineUserCount}`)

      // 如果在线用户列表为空，可能是请求失败
      if (store.onlineUsersList.length === 0) {
        console.warn(`在线用户列表请求可能失败 (ID: ${requestId})，尝试重新请求`)
        // 如果未达到最大重试次数，则重试
        if (retryCount < MAX_RETRIES) {
          requestOnlineUsers(retryCount + 1)
        } else {
          // 尝试通过广播主题获取在线用户列表
          console.log('尝试通过广播主题获取在线用户列表')
          stompClient.publish({
            destination: '/app/broadcastOnlineUsers',
            headers: {
              'Authorization': `Bearer ${token}`,
              'request-id': `broadcast-${Date.now()}`
            },
            body: JSON.stringify({
              timestamp: Date.now(),
              requestId: `broadcast-${Date.now()}`
            })
          })
        }
      } else {
        console.log(`请求 ${requestId} 已成功响应`)
      }
    }, 5000) // 5秒后检查

    return true
  } catch (error) {
    console.error('请求在线用户列表失败:', error)

    // 如果未达到最大重试次数，则重试
    if (retryCount < MAX_RETRIES) {
      console.log(`将在 ${RETRY_DELAY}ms 后重试请求在线用户列表`)
      setTimeout(() => {
        requestOnlineUsers(retryCount + 1)
      }, RETRY_DELAY)
    }

    return false
  }
}

/**
 * 检查特定用户的在线状态
 * 使用节流控制，每个用户ID每30秒最多检查一次
 */
export function checkUserOnlineStatus(userId) {
  const stompClient = getWebSocketClient()

  if (!stompClient || !stompClient.connected) {
    console.warn('WebSocket未连接，无法检查用户在线状态')
    return false
  }

  // 使用静态Map存储上次检查时间
  if (!checkUserOnlineStatus._lastCheckTime) {
    checkUserOnlineStatus._lastCheckTime = new Map()
  }

  // 检查是否在节流期内（2分钟内已经检查过）
  const now = Date.now()
  const lastCheckTime = checkUserOnlineStatus._lastCheckTime.get(userId) || 0
  const THROTTLE_TIME = 120000 // 2分钟节流时间

  if (now - lastCheckTime < THROTTLE_TIME) {
    console.log(`用户 ${userId} 在线状态已在 ${Math.floor((now - lastCheckTime) / 1000)} 秒前检查过，跳过本次检查`)
    return false
  }

  try {
    // 更新最后检查时间
    checkUserOnlineStatus._lastCheckTime.set(userId, now)

    // 发送请求
    stompClient.publish({
      destination: `/app/checkOnlineStatus/${userId}`,
      body: ''
    })

    console.log(`已发送检查用户 ${userId} 在线状态请求`)
    return true
  } catch (error) {
    console.error('检查用户在线状态失败:', error)
    return false
  }
}

/**
 * 获取用户在线状态
 */
export function isUserOnline(userId) {
  const store = useOnlineStatusStore()
  return store.isUserOnline(userId)
}

/**
 * 获取在线用户数量
 */
export function getOnlineUserCount() {
  const store = useOnlineStatusStore()
  return store.onlineUserCount
}

/**
 * 获取在线用户列表
 */
export function getOnlineUsers() {
  const store = useOnlineStatusStore()
  return store.onlineUsersList
}

/**
 * 获取用户最后活跃时间
 */
export function getUserLastActiveTime(userId) {
  const store = useOnlineStatusStore()
  return store.getUserLastActiveTime(userId)
}

/**
 * 启动定期同步
 * 每30秒自动请求一次在线用户列表
 */
export function startPeriodicSync() {
  // 如果已经有定时器在运行，先停止它
  stopPeriodicSync()

  // 创建新的定时器，每3分钟同步一次
  periodicSyncTimer = setInterval(() => {
    const stompClient = getWebSocketClient()

    if (stompClient && stompClient.connected) {
      console.log('定期同步: 请求最新的在线用户列表')
      requestOnlineUsers()
    } else {
      console.warn('定期同步: WebSocket未连接，跳过本次同步')

      // 如果WebSocket未连接，尝试重新连接
      try {
        import('@/utils/websocket/index').then(module => {
          if (!module.isWebSocketConnected()) {
            console.log('WebSocket未连接，尝试重新初始化')
            module.reinitializeWebSocketService().then(success => {
              console.log('WebSocket重新初始化结果:', success ? '成功' : '失败')
            })
          }
        })
      } catch (error) {
        console.warn('尝试重新连接WebSocket失败:', error)
      }
    }
  }, 180000) // 3分钟 = 180000毫秒

  console.log('已启动定期同步，间隔: 3分钟')
  return true
}

/**
 * 停止定期同步
 */
export function stopPeriodicSync() {
  if (periodicSyncTimer) {
    clearInterval(periodicSyncTimer)
    periodicSyncTimer = null
    console.log('已停止定期同步')
  }
  return true
}

/**
 * 获取联系人在线状态
 * @param {Array<string>} contactIds 联系人ID数组
 * @returns {Promise<Object>} 联系人在线状态
 */
export async function getContactsStatus(contactIds) {
  // 使用静态变量存储上次请求时间
  if (!getContactsStatus._lastRequestTime) {
    getContactsStatus._lastRequestTime = 0;
  }

  // 记录请求的联系人ID
  if (contactIds && contactIds.length > 0) {
    console.log(`请求获取 ${contactIds.length} 个联系人的在线状态`);

    // 检查联系人在线状态
    const store = useOnlineStatusStore();
    const onlineContacts = contactIds.filter(id => store.isContactOnline(id));
    console.log(`当前有 ${onlineContacts.length} 个联系人在线`);
  } else {
    console.log('请求获取所有联系人的在线状态');
  }

  // 检查是否需要请求最新的在线用户列表（每5分钟最多请求一次）
  const now = Date.now();
  const THROTTLE_TIME = 300000; // 5分钟节流时间
  let result = false;

  if (now - getContactsStatus._lastRequestTime >= THROTTLE_TIME) {
    // 更新最后请求时间
    getContactsStatus._lastRequestTime = now;

    // 请求最新的在线用户列表
    console.log('已超过5分钟，请求最新的在线用户列表');
    result = requestOnlineUsers();
  } else {
    console.log(`距离上次请求在线用户列表不足5分钟，使用缓存数据 (${Math.floor((now - getContactsStatus._lastRequestTime) / 1000)}秒前)`);
    result = true; // 假设成功，使用缓存数据
  }

  // 返回结果
  return {
    success: result,
    message: result ? '已请求联系人在线状态' : '请求联系人在线状态失败',
    contactIds: contactIds || [],
    fromCache: now - getContactsStatus._lastRequestTime < THROTTLE_TIME
  };
}
