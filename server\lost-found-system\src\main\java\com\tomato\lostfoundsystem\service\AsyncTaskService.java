package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.dto.ReadReceiptDTO;
import com.tomato.lostfoundsystem.entity.MessageAttachment;
import org.springframework.web.multipart.MultipartFile;

import java.util.concurrent.CompletableFuture;

public interface AsyncTaskService {

    /**
     * 异步上传文件到阿里云 OSS
     *
     * @param file 上传的文件
     * @param messageAttachment 文件的附件对象
     */
    void uploadFileToOSS(MultipartFile file, MessageAttachment messageAttachment);

    /**
     * 异步获取音频或视频时长
     *
     * @param messageAttachment 附件对象，包含文件信息
     * @return 返回音视频的时长（秒）
     */
    CompletableFuture<Integer> getMediaDuration(MessageAttachment messageAttachment);

    /**
     * 异步推送已读回执到WebSocket
     *
     * @param senderId 消息发送者ID（接收已读回执的用户）
     * @param readReceiptDTO 已读回执DTO
     */
    void pushReadReceiptToWebSocket(Long senderId, ReadReceiptDTO readReceiptDTO);

    /**
     * 异步存储离线消息到 Kafka
     *
     * @param message 离线消息内容
     */
    void storeOfflineMessageAsync(String message);

    /**
     * 异步存储完整的离线消息对象到 Kafka
     * 用于前端离线消息同步
     *
     * @param messageDTO 完整的消息对象
     */
    void storeOfflineMessageObjectAsync(MessageDTO messageDTO);

    /**
     * 异步推送消息到 WebSocket
     *
     * @param receiverId 接收消息的用户 ID
     * @param messageDTO 消息数据传输对象
     */
    void pushMessageToWebSocket(Long receiverId, MessageDTO messageDTO);

    /**
     * 异步处理文件上传和媒体时长计算
     *
     * @param file 上传的文件
     * @param attachmentId 附件ID
     * @param messageId 消息ID
     */
    void processFileAsync(MultipartFile file, Long attachmentId, Long messageId);

    /**
     * 异步处理物品审核通过后的特征提取和智能匹配
     * 主路径：使用Spring异步处理
     *
     * @param itemId 物品ID
     * @param userId 用户ID
     * @param itemType 物品类型（LOST/FOUND）
     */
    void processItemAutoMatchAsync(Long itemId, Long userId, String itemType);
}
