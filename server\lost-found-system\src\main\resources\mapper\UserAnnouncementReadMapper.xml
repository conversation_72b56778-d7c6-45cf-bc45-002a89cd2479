<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomato.lostfoundsystem.mapper.UserAnnouncementReadMapper">
    <insert id="insertRead" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_announcement_reads (
            user_id, announcement_id
        ) VALUES (
            #{userId}, #{announcementId}
        )
        ON DUPLICATE KEY UPDATE read_at = CURRENT_TIMESTAMP
    </insert>
    
    <select id="selectByUserId" resultType="com.tomato.lostfoundsystem.entity.UserAnnouncementRead">
        SELECT 
            id, user_id as userId, announcement_id as announcementId, 
            read_at as readAt
        FROM user_announcement_reads
        WHERE user_id = #{userId}
    </select>
    
    <select id="selectByAnnouncementId" resultType="com.tomato.lostfoundsystem.entity.UserAnnouncementRead">
        SELECT 
            id, user_id as userId, announcement_id as announcementId, 
            read_at as readAt
        FROM user_announcement_reads
        WHERE announcement_id = #{announcementId}
    </select>
    
    <select id="selectByUserAndAnnouncement" resultType="com.tomato.lostfoundsystem.entity.UserAnnouncementRead">
        SELECT 
            id, user_id as userId, announcement_id as announcementId, 
            read_at as readAt
        FROM user_announcement_reads
        WHERE user_id = #{userId} AND announcement_id = #{announcementId}
    </select>
    
    <select id="countUnreadAnnouncements" resultType="int">
        SELECT COUNT(sa.id)
        FROM system_announcements sa
        LEFT JOIN user_announcement_reads uar ON sa.id = uar.announcement_id AND uar.user_id = #{userId}
        WHERE sa.status = 'PUBLISHED'
        AND sa.start_time &lt;= CURRENT_TIMESTAMP
        AND (sa.end_time IS NULL OR sa.end_time &gt;= CURRENT_TIMESTAMP)
        AND uar.id IS NULL
    </select>
</mapper>
