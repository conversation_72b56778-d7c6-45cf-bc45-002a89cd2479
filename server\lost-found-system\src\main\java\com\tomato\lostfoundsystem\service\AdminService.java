package com.tomato.lostfoundsystem.service;

import com.github.pagehelper.PageInfo;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.AddAdminDTO;
import com.tomato.lostfoundsystem.dto.FoundItemDetailDTO;
import com.tomato.lostfoundsystem.dto.ItemAuditDTO;
import com.tomato.lostfoundsystem.dto.LostItemDetailsDTO;

import java.util.Map;

public interface AdminService {
     Result<PageInfo<LostItemDetailsDTO>> queryAuditList(int page, int size, String keyword, String auditStatus, String status, String startDate, String endDate, Long userId);

     /**
      * 查询拾物列表
      * @param page 页码
      * @param size 每页数量
      * @param keyword 关键词
      * @param auditStatus 审核状态
      * @param status 状态
      * @param startDate 开始日期
      * @param endDate 结束日期
      * @param userId 用户ID
      * @return 拾物列表
      */
     Result<PageInfo<FoundItemDetailDTO>> queryFoundItemList(int page, int size, String keyword, String auditStatus, String status, String startDate, String endDate, Long userId);

    Result<?> auditLostItem(Long id, ItemAuditDTO itemAuditDTO, Long adminId);

    Result<?> auditFoundItem(Long id, ItemAuditDTO itemAuditDTO, Long adminId);

    Result<?> addAdmin(AddAdminDTO addAdminDTO, Long userId);

    Result<?> getUserList(int page, int size, String keyword,Boolean isActive,String role, Boolean deleted);

    Result<?> getUserById(Long id);

    Result<?> updateUserRole(Long id, String role);

    Result<?> updateUserStatus(Long id, Boolean isActive);

    Result<?> resetUserPassword(Long id, String newPassword, Long currentAdminId);

    Result<?> deleteUser(Long id);

    /**
     * 更新拾物状态
     * @param id 拾物ID
     * @param status 状态
     * @return 操作结果
     */
    Result<?> updateFoundItemStatus(Long id, String status);

    /**
     * 删除拾物
     * @param id 拾物ID
     * @return 操作结果
     */
    Result<?> deleteFoundItem(Long id);

    /**
     * 获取管理员统计数据
     * 包括失物统计、拾物统计和用户统计
     *
     * @return 统计数据
     */
    Result<Map<String, Object>> getAdminStatistics();
}
