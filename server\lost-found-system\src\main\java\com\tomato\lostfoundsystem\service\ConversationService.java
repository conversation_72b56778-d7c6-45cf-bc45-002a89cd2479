package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.dto.ContactDTO;
import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.entity.Conversation;

import java.util.List;

public interface ConversationService {
    /**
     * 创建会话
     * @param senderId 发送者ID
     * @param receiverId 接收者ID
     * @return 会话ID
     */
    Long createConversation(Long senderId, Long receiverId);

    /**
     * 获取用户的所有联系人
     * @param userId 用户ID
     * @return 联系人列表
     */
    List<ContactDTO> getContacts(Long userId);

    /**
     * 更新会话的最后一条消息信息
     * @param message 消息对象
     */
    void updateConversationWithMessage(ChatMessage message);

    /**
     * 重置会话未读计数
     * @param userId 用户ID
     * @param contactId 联系人ID
     */
    void resetUnreadCount(Long userId, Long contactId);

    /**
     * 增加会话未读计数
     * @param userId 用户ID
     * @param contactId 联系人ID
     */
    void incrementUnreadCount(Long userId, Long contactId);

    /**
     * 更新会话状态
     * @param conversationId 会话ID
     * @param status 状态
     */
    void updateStatus(Long conversationId, String status);

    /**
     * 更新会话置顶状态
     * @param conversationId 会话ID
     * @param isPinned 是否置顶
     */
    void updatePinned(Long conversationId, Boolean isPinned);

    /**
     * 更新会话静音状态
     * @param conversationId 会话ID
     * @param isMuted 是否静音
     */
    void updateMuted(Long conversationId, Boolean isMuted);

    /**
     * 获取或创建会话
     * @param user1Id 用户1ID
     * @param user2Id 用户2ID
     * @return 会话对象
     */
    Conversation getOrCreateConversation(Long user1Id, Long user2Id);
}
