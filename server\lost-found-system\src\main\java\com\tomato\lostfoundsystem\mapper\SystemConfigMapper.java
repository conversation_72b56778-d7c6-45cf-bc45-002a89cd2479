package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.SystemConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统配置Mapper接口
 */
public interface SystemConfigMapper {
    
    /**
     * 获取所有系统配置
     *
     * @return 系统配置列表
     */
    List<SystemConfig> getAllConfigs();
    
    /**
     * 根据配置键获取配置
     *
     * @param configKey 配置键
     * @return 系统配置
     */
    SystemConfig getConfigByKey(@Param("configKey") String configKey);
    
    /**
     * 更新配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 影响的行数
     */
    int updateConfigValue(@Param("configKey") String configKey, @Param("configValue") String configValue);
    
    /**
     * 添加配置
     *
     * @param config 系统配置
     * @return 影响的行数
     */
    int addConfig(SystemConfig config);
    
    /**
     * 删除配置
     *
     * @param configKey 配置键
     * @return 影响的行数
     */
    int deleteConfig(@Param("configKey") String configKey);
}
