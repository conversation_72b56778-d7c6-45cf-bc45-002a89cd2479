<template>
  <div class="profile-matches-container">
    <div class="page-title">
      <h2>匹配历史</h2>
      <div class="title-line"></div>
      <p class="subtitle">查看您的搜索历史和匹配结果</p>
    </div>

    <!-- 匹配详情对话框 -->
    <el-dialog
      v-model="matchDetailDialogVisible"
      title="匹配详情"
      width="80%"
      :before-close="handleCloseMatchDetailDialog"
    >
      <div v-if="loadingMatchDetail" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else-if="!matchDetailData || matchDetailData.length === 0" class="no-match-detail">
        <el-empty description="暂无匹配详情" />
      </div>
      <div v-else class="match-detail-list">
        <div v-for="(item, index) in matchDetailData" :key="index" class="match-detail-item">
          <div class="match-detail-card">
            <div class="match-detail-image" v-if="item.imageUrl">
              <el-image :src="item.imageUrl" fit="cover">
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <div class="similarity-badge" :style="{ backgroundColor: getSimilarityColor(item.similarity) }">
                匹配度: {{ (item.similarity * 100).toFixed(0) }}%
              </div>
              <div class="item-type-badge" :class="item.itemType === 'LOST' ? 'lost-badge' : 'found-badge'">
                {{ item.itemType === 'LOST' ? '失物' : '拾物' }}
              </div>
            </div>
            <div class="match-detail-content">
              <h4>{{ item.itemName || '未知物品' }}</h4>
              <p class="match-detail-desc">{{ item.description || '无描述' }}</p>
              <div class="match-detail-meta">
                <span v-if="item.location"><el-icon><Location /></el-icon> {{ item.location }}</span>
                <span v-if="item.time"><el-icon><Timer /></el-icon> {{ formatDate(item.time) }}</span>
                <span v-if="item.username"><el-icon><User /></el-icon> {{ item.username }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <div class="matches-tabs">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="我的失物搜索" name="lost">
          <div class="tab-header">
            <div class="tab-title">
              <el-icon><Search /></el-icon>
              <span>失物搜索历史</span>
            </div>
            <div class="tab-actions">
              <el-button type="primary" size="small" @click="refreshMatches">
                <el-icon><Refresh /></el-icon>
                刷新数据
              </el-button>
            </div>
          </div>

          <div v-if="loadingLost" class="loading-container">
            <el-skeleton :rows="3" animated />
          </div>
          <el-empty v-else-if="lostMatches.length === 0" description="暂无搜索历史，请尝试发布更多失物信息或进行搜索" />
          <div v-else class="matches-list">
            <el-collapse v-model="activeLostItems">
              <el-collapse-item v-for="item in lostMatches" :key="item.id" :name="item.id">
                <template #title>
                  <div class="collapse-header">
                    <div class="item-info">
                      <span class="item-name">{{ getSearchTypeName(item.queryType) }}</span>
                      <el-tag size="small" :type="getMatchStatusType(item.resultCount)">{{ getMatchStatusText(item.resultCount) }}</el-tag>
                      <span class="search-time">{{ formatDate(item.createdAt) }}</span>
                    </div>
                    <div class="match-count">
                      <el-badge :value="item.resultCount" type="primary" />
                    </div>
                  </div>
                </template>

                <div class="match-details">
                  <div class="search-info">
                    <div class="search-type">
                      <strong>搜索类型:</strong> {{ getSearchTypeName(item.queryType) }}
                    </div>
                    <div v-if="item.queryText" class="search-text">
                      <strong>搜索文本:</strong> {{ item.queryText }}
                    </div>
                    <div v-if="item.queryImageUrl" class="search-image">
                      <strong>搜索图片:</strong>
                      <el-image
                        :src="item.queryImageUrl"
                        fit="cover"
                        style="width: 100px; height: 100px; margin-top: 10px;"
                      >
                        <template #error>
                          <div class="image-placeholder">
                            <el-icon><Picture /></el-icon>
                          </div>
                        </template>
                      </el-image>
                    </div>
                  </div>

                  <div v-if="item.resultCount > 0" class="match-actions">
                    <el-button type="primary" size="small" @click="viewMatchDetail(item.id)">查看匹配结果</el-button>
                  </div>
                  <div v-else class="no-matches">
                    <el-empty description="未找到匹配结果" :image-size="100" />
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-tab-pane>

        <el-tab-pane label="我的拾物搜索" name="found">
          <div class="tab-header">
            <div class="tab-title">
              <el-icon><Search /></el-icon>
              <span>拾物搜索历史</span>
            </div>
            <div class="tab-actions">
              <el-button type="primary" size="small" @click="refreshMatches">
                <el-icon><Refresh /></el-icon>
                刷新数据
              </el-button>
            </div>
          </div>

          <div v-if="loadingFound" class="loading-container">
            <el-skeleton :rows="3" animated />
          </div>
          <el-empty v-else-if="foundMatches.length === 0" description="暂无搜索历史，请尝试发布更多拾物信息或进行搜索" />
          <div v-else class="matches-list">
            <el-collapse v-model="activeFoundItems">
              <el-collapse-item v-for="item in foundMatches" :key="item.id" :name="item.id">
                <template #title>
                  <div class="collapse-header">
                    <div class="item-info">
                      <span class="item-name">{{ getSearchTypeName(item.queryType) }}</span>
                      <el-tag size="small" :type="getMatchStatusType(item.resultCount)">{{ getMatchStatusText(item.resultCount) }}</el-tag>
                      <span class="search-time">{{ formatDate(item.createdAt) }}</span>
                    </div>
                    <div class="match-count">
                      <el-badge :value="item.resultCount" type="primary" />
                    </div>
                  </div>
                </template>

                <div class="match-details">
                  <div class="search-info">
                    <div class="search-type">
                      <strong>搜索类型:</strong> {{ getSearchTypeName(item.queryType) }}
                    </div>
                    <div v-if="item.queryText" class="search-text">
                      <strong>搜索文本:</strong> {{ item.queryText }}
                    </div>
                    <div v-if="item.queryImageUrl" class="search-image">
                      <strong>搜索图片:</strong>
                      <el-image
                        :src="item.queryImageUrl"
                        fit="cover"
                        style="width: 100px; height: 100px; margin-top: 10px;"
                      >
                        <template #error>
                          <div class="image-placeholder">
                            <el-icon><Picture /></el-icon>
                          </div>
                        </template>
                      </el-image>
                    </div>
                  </div>

                  <div v-if="item.resultCount > 0" class="match-actions">
                    <el-button type="primary" size="small" @click="viewMatchDetail(item.id)">查看匹配结果</el-button>
                  </div>
                  <div v-else class="no-matches">
                    <el-empty description="未找到匹配结果" :image-size="100" />
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  Refresh,
  Picture,
  Timer,
  User,
  Location
} from '@element-plus/icons-vue'
import { getMatchHistory, getMatchHistoryDetail } from '@/api/match'

// 当前激活的标签页
const activeTab = ref('lost')

// 展开的失物和拾物项
const activeLostItems = ref([])
const activeFoundItems = ref([])

// 加载状态
const loadingLost = ref(true)
const loadingFound = ref(true)

// 匹配数据
const lostMatches = ref([])
const foundMatches = ref([])

// 匹配详情对话框
const matchDetailDialogVisible = ref(false)
const loadingMatchDetail = ref(false)
const matchDetailData = ref([])

// 处理标签页切换
const handleTabClick = (tab) => {
  if (tab.props.name === 'lost') {
    if (lostMatches.value.length === 0) {
      fetchLostMatches()
    }
  } else {
    if (foundMatches.value.length === 0) {
      fetchFoundMatches()
    }
  }
}

// 获取失物匹配数据
const fetchLostMatches = async () => {
  loadingLost.value = true

  try {
    // 调用真实API
    const response = await getMatchHistory()

    if (response.code === 200 && response.data) {
      // 处理API返回的数据
      const matchData = response.data

      // 过滤出失物匹配数据 - 注意：后端返回的是小写的'lost'或大写的'LOST'
      lostMatches.value = matchData.filter(item =>
        item.itemType && (item.itemType.toLowerCase() === 'lost')
      ) || []

      // 默认展开第一项
      if (lostMatches.value.length > 0) {
        activeLostItems.value = [lostMatches.value[0].id]
      }

      console.log('获取失物匹配数据成功:', lostMatches.value)
    } else {
      ElMessage.error(response.message || '获取失物匹配数据失败')
      console.error('获取失物匹配数据失败:', response)
      lostMatches.value = []
    }
  } catch (error) {
    ElMessage.error('获取失物匹配数据失败，请稍后重试')
    console.error('获取失物匹配数据出错:', error)
    lostMatches.value = []
  } finally {
    loadingLost.value = false
  }
}

// 获取拾物匹配数据
const fetchFoundMatches = async () => {
  loadingFound.value = true

  try {
    // 调用真实API
    const response = await getMatchHistory()

    if (response.code === 200 && response.data) {
      // 处理API返回的数据
      const matchData = response.data

      // 过滤出拾物匹配数据 - 注意：后端返回的是小写的'found'或大写的'FOUND'
      foundMatches.value = matchData.filter(item =>
        item.itemType && (item.itemType.toLowerCase() === 'found')
      ) || []

      // 默认展开第一项
      if (foundMatches.value.length > 0) {
        activeFoundItems.value = [foundMatches.value[0].id]
      }

      console.log('获取拾物匹配数据成功:', foundMatches.value)
    } else {
      ElMessage.error(response.message || '获取拾物匹配数据失败')
      console.error('获取拾物匹配数据失败:', response)
      foundMatches.value = []
    }
  } catch (error) {
    ElMessage.error('获取拾物匹配数据失败，请稍后重试')
    console.error('获取拾物匹配数据出错:', error)
    foundMatches.value = []
  } finally {
    loadingFound.value = false
  }
}

// 刷新匹配
const refreshMatches = async () => {
  try {
    if (activeTab.value === 'lost') {
      await fetchLostMatches()
    } else {
      await fetchFoundMatches()
    }

    ElMessage.success('匹配数据已刷新')
  } catch (error) {
    console.error('刷新匹配数据失败:', error)
    ElMessage.error('刷新匹配数据失败，请稍后重试')
  }
}

// 获取匹配状态类型
const getMatchStatusType = (resultCount) => {
  if (resultCount > 0) {
    return 'success'
  } else {
    return 'info'
  }
}

// 获取匹配状态文本
const getMatchStatusText = (resultCount) => {
  if (resultCount > 0) {
    return '有匹配'
  } else {
    return '无匹配'
  }
}

// 获取搜索类型名称
const getSearchTypeName = (queryType) => {
  switch (queryType) {
    case 'TEXT':
      return '文本搜索'
    case 'IMAGE':
      return '图像搜索'
    case 'MIXED':
      return '混合搜索'
    default:
      return '未知搜索'
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''

  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 查看匹配详情
const viewMatchDetail = async (matchId) => {
  try {
    // 确保matchId是一个有效的数字
    if (!matchId || isNaN(Number(matchId))) {
      ElMessage.error('无效的匹配历史ID')
      return
    }

    // 显示加载状态
    loadingMatchDetail.value = true
    matchDetailDialogVisible.value = true
    matchDetailData.value = []

    console.log(`正在获取匹配历史ID: ${matchId} 的详情...`)

    // 调用获取匹配详情的API
    const response = await getMatchHistoryDetail(matchId)

    if (response.code === 200 && response.data) {
      // 获取匹配历史对象
      const matchHistory = response.data

      // 从匹配历史中提取结果数组
      if (matchHistory.results && Array.isArray(matchHistory.results)) {
        // 处理每个结果项，提取物品详情
        matchDetailData.value = matchHistory.results.map(result => {
          // 获取物品详情
          const itemDetail = result.itemDetail

          if (!itemDetail) {
            console.warn('结果项缺少物品详情:', result)
            return null
          }

          // 构建显示所需的数据结构
          return {
            id: result.itemId,
            itemId: result.itemId,
            itemType: result.itemType,
            similarity: result.similarityScore,
            itemName: itemDetail.itemName,
            description: itemDetail.description,
            location: itemDetail.lostLocation || itemDetail.foundLocation,
            time: itemDetail.lostTime || itemDetail.foundTime,
            imageUrl: itemDetail.imageUrl,
            username: itemDetail.username || '未知用户'
          }
        }).filter(item => item !== null) // 过滤掉无效的项
      } else {
        console.warn('匹配历史缺少结果数组或结果数组为空:', matchHistory)
        matchDetailData.value = []
      }

      // 显示匹配结果数量提示
      ElMessage({
        message: `匹配历史ID: ${matchId} 的详情已获取，共有 ${matchDetailData.value.length} 条匹配结果`,
        type: 'success',
        duration: 3000
      })

      console.log('匹配详情数据:', matchDetailData.value)
    } else {
      ElMessage.error(response.message || '获取匹配详情失败')
      matchDetailDialogVisible.value = false
    }
  } catch (error) {
    console.error('获取匹配详情出错:', error)
    ElMessage.error(`获取匹配详情失败: ${error.message || '未知错误'}`)
    matchDetailDialogVisible.value = false
  } finally {
    loadingMatchDetail.value = false
  }
}

// 关闭匹配详情对话框
const handleCloseMatchDetailDialog = () => {
  matchDetailDialogVisible.value = false
  matchDetailData.value = []
}

// 获取相似度颜色
const getSimilarityColor = (similarity) => {
  if (similarity >= 0.9) {
    return '#67c23a' // 绿色
  } else if (similarity >= 0.7) {
    return '#e6a23c' // 黄色
  } else {
    return '#909399' // 灰色
  }
}

// 初始化
onMounted(() => {
  fetchLostMatches()
})
</script>

<style scoped>
.profile-matches-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 页面标题样式 */
.page-title {
  text-align: center;
  margin-bottom: 2rem;
}

.page-title h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 8px;
}

.title-line {
  width: 60px;
  height: 3px;
  background-color: #722ed1; /* 匹配功能使用紫色 */
  margin: 0 auto;
  margin-bottom: 1rem;
}

.subtitle {
  color: #606266;
  font-size: 14px;
}

/* 标签页样式 */
.matches-tabs {
  margin-top: 20px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.tab-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: #722ed1;
}

.tab-actions {
  display: flex;
  gap: 10px;
}

/* 加载状态 */
.loading-container {
  padding: 20px;
}

/* 匹配列表样式 */
.matches-list {
  margin-bottom: 20px;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.item-name {
  font-weight: 500;
  color: #303133;
}

.search-time {
  color: #909399;
  font-size: 13px;
}

.match-count {
  margin-right: 20px;
}

.match-details {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 8px;
  margin-top: 10px;
}

.search-info {
  margin-bottom: 15px;
}

.search-type, .search-text {
  margin-bottom: 8px;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #909399;
}

.image-placeholder .el-icon {
  font-size: 32px;
}

.match-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.no-matches {
  margin-top: 15px;
  text-align: center;
}

/* 匹配详情样式 */
.match-detail-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.match-detail-item {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.match-detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.match-detail-card {
  display: flex;
  gap: 15px;
}

.match-detail-image {
  position: relative;
  width: 150px;
  height: 150px;
  flex-shrink: 0;
}

.match-detail-image .el-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.similarity-badge {
  position: absolute;
  bottom: 10px;
  left: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.item-type-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.lost-badge {
  background-color: #f56c6c; /* 红色 */
}

.found-badge {
  background-color: #67c23a; /* 绿色 */
}

.match-detail-content {
  flex: 1;
}

.match-detail-content h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #303133;
}

.match-detail-desc {
  color: #606266;
  margin-bottom: 15px;
  line-height: 1.5;
}

.match-detail-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  color: #909399;
  font-size: 13px;
}

.match-detail-meta span {
  display: flex;
  align-items: center;
}

.match-detail-meta .el-icon {
  margin-right: 5px;
}

.no-match-detail {
  padding: 30px 0;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .collapse-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .match-count {
    margin-top: 10px;
    margin-right: 0;
  }

  .item-info {
    flex-wrap: wrap;
  }

  .match-detail-card {
    flex-direction: column;
  }

  .match-detail-image {
    width: 100%;
    height: 200px;
  }
}
</style>
