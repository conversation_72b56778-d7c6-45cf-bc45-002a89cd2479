package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.service.CDNConfigService;
import com.tomato.lostfoundsystem.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * CDN配置服务实现类
 */
@Slf4j
@Service
public class CDNConfigServiceImpl implements CDNConfigService {

    private static final String CDN_DOMAIN_CONFIG_KEY = "aliyun.cdn.domain";
    private static final String CDN_ENABLED_CONFIG_KEY = "aliyun.cdn.enabled";
    private static final String CDN_LAST_UPDATED_CONFIG_KEY = "aliyun.cdn.last_updated";

    @Value("${aliyun.cdn.domain}")
    private String defaultCdnDomain;

    @Resource
    private SystemConfigService systemConfigService;

    @Override
    public String getCDNDomain() {
        // 从系统配置中获取CDN域名，如果不存在则使用默认值
        return systemConfigService.getConfigValue(CDN_DOMAIN_CONFIG_KEY, defaultCdnDomain);
    }

    @Override
    public boolean updateCDNDomain(String domain) {
        try {
            // 验证域名
            if (!validateCDNDomain(domain)) {
                log.warn("CDN域名验证失败: {}", domain);
                return false;
            }

            // 更新CDN域名配置
            systemConfigService.updateConfigValue(CDN_DOMAIN_CONFIG_KEY, domain);

            // 更新启用状态
            systemConfigService.updateConfigValue(CDN_ENABLED_CONFIG_KEY, "true");

            // 更新最后更新时间
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            systemConfigService.updateConfigValue(CDN_LAST_UPDATED_CONFIG_KEY, currentTime);

            log.info("CDN域名已更新: {}", domain);
            return true;
        } catch (Exception e) {
            log.error("更新CDN域名失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean validateCDNDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return false;
        }

        // 检查域名格式
        if (!domain.startsWith("http://") && !domain.startsWith("https://")) {
            log.warn("CDN域名必须以http://或https://开头: {}", domain);
            return false;
        }

        try {
            // 尝试连接域名
            URL url = new URL(domain);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);
            int responseCode = connection.getResponseCode();

            // 2xx或3xx状态码表示域名可访问
            return responseCode >= 200 && responseCode < 400;
        } catch (Exception e) {
            log.warn("CDN域名连接测试失败: {}, 错误: {}", domain, e.getMessage());
            // 如果是本地开发环境，可以放宽验证
            return domain.contains("localhost") || domain.contains("127.0.0.1");
        }
    }

    @Override
    public CDNStatusInfo getCDNStatus() {
        String domain = getCDNDomain();
        boolean enabled = Boolean.parseBoolean(
                systemConfigService.getConfigValue(CDN_ENABLED_CONFIG_KEY, "false"));
        String lastUpdated = systemConfigService.getConfigValue(
                CDN_LAST_UPDATED_CONFIG_KEY, LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        return new CDNStatusInfo(domain, enabled, lastUpdated);
    }
}
