-- 确保所有相似度信息都已迁移到元数据字段中
UPDATE match_notifications
SET metadata = JSON_OBJECT(
    'matchType', match_type,
    'similarityDetails', JSON_OBJECT(
        'TEXT_TO_TEXT', text_to_text_similarity,
        'TEXT_TO_IMAGE', text_to_image_similarity,
        'IMAGE_TO_TEXT', image_to_text_similarity,
        'IMAGE_TO_IMAGE', image_to_image_similarity
    )
)
WHERE metadata IS NULL OR metadata = '';

-- 添加索引以提高查询性能
CREATE INDEX idx_match_notifications_metadata ON match_notifications((JSON_EXTRACT(metadata, '$.matchType')));

-- 注意：我们不删除原有字段，以保持向后兼容性
-- 在应用程序层面，我们将优先使用元数据字段
