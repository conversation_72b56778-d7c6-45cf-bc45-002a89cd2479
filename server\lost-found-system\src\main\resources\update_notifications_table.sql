-- 通知表结构优化脚本
-- 为user_notifications表添加新字段

-- 添加通知类型字段
ALTER TABLE user_notifications 
ADD COLUMN type VARCHAR(20) NOT NULL DEFAULT 'SYSTEM' COMMENT '通知类型';

-- 添加关联物品ID字段
ALTER TABLE user_notifications 
ADD COLUMN related_item_id BIGINT NULL COMMENT '关联物品ID';

-- 添加关联物品类型字段
ALTER TABLE user_notifications 
ADD COLUMN related_item_type VARCHAR(10) NULL COMMENT '关联物品类型(LOST/FOUND)';

-- 添加元数据字段
ALTER TABLE user_notifications 
ADD COLUMN metadata TEXT NULL COMMENT '额外元数据(JSON格式)';

-- 添加审核员ID字段
ALTER TABLE user_notifications 
ADD COLUMN auditor_id BIGINT NULL COMMENT '审核员ID(仅审核通知)';

-- 添加索引以提高查询性能
CREATE INDEX idx_notifications_type ON user_notifications(type);
CREATE INDEX idx_notifications_related_item ON user_notifications(related_item_id, related_item_type);
CREATE INDEX idx_notifications_auditor_id ON user_notifications(auditor_id);

-- 更新现有数据，设置通知类型

-- 更新审核通过通知
UPDATE user_notifications
SET type = 'AUDIT_APPROVED',
    related_item_id = SUBSTRING_INDEX(SUBSTRING_INDEX(message, 'ID: ', -1), '）', 1),
    related_item_type = CASE 
        WHEN message LIKE '%失物%' THEN 'LOST'
        WHEN message LIKE '%拾物%' THEN 'FOUND'
        ELSE NULL
    END
WHERE title LIKE '%通过审核%';

-- 更新审核拒绝通知
UPDATE user_notifications
SET type = 'AUDIT_REJECTED',
    related_item_id = SUBSTRING_INDEX(SUBSTRING_INDEX(message, 'ID: ', -1), '）', 1),
    related_item_type = CASE 
        WHEN message LIKE '%失物%' THEN 'LOST'
        WHEN message LIKE '%拾物%' THEN 'FOUND'
        ELSE NULL
    END,
    metadata = JSON_OBJECT('rejectReason', SUBSTRING_INDEX(SUBSTRING_INDEX(message, '）:', -1), '未通过审核', 1))
WHERE title LIKE '%未通过审核%';

-- 更新认领通知
UPDATE user_notifications
SET type = 'CLAIM'
WHERE title LIKE '%已认领%' OR title LIKE '%认领申请%';

-- 更新系统公告通知
UPDATE user_notifications
SET type = 'ANNOUNCEMENT'
WHERE title LIKE '%系统公告%' OR title LIKE '%新公告%';

-- 更新管理员通知
UPDATE user_notifications
SET type = 'ADMIN'
WHERE (title NOT LIKE '%通过审核%' AND title NOT LIKE '%未通过审核%' 
       AND title NOT LIKE '%已认领%' AND title NOT LIKE '%认领申请%'
       AND title NOT LIKE '%系统公告%' AND title NOT LIKE '%新公告%')
      OR title IS NULL;




