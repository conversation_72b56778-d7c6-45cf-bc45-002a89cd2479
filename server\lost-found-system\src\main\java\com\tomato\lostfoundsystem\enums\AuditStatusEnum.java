package com.tomato.lostfoundsystem.enums;

/**
 * 审核状态枚举
 */
public enum AuditStatusEnum {
    /**
     * 待审核
     */
    PENDING("PENDING", "待审核"),
    
    /**
     * 已通过
     */
    APPROVED("APPROVED", "已通过"),
    
    /**
     * 已拒绝
     */
    REJECTED("REJECTED", "已拒绝");

    private final String code;
    private final String description;

    AuditStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取枚举
     * @param code 状态码
     * @return 枚举值
     */
    public static AuditStatusEnum getByCode(String code) {
        if (code == null) {
            return PENDING; // 默认为待审核
        }
        
        for (AuditStatusEnum status : AuditStatusEnum.values()) {
            if (status.getCode().equalsIgnoreCase(code)) {
                return status;
            }
        }
        
        // 兼容处理，处理可能的不规范状态值
        String upperCode = code.toUpperCase();
        if (upperCode.contains("PEND") || upperCode.contains("WAIT")) {
            return PENDING;
        } else if (upperCode.contains("APPROV") || upperCode.contains("PASS")) {
            return APPROVED;
        } else if (upperCode.contains("REJECT") || upperCode.contains("DENY")) {
            return REJECTED;
        }
        
        return PENDING; // 默认为待审核
    }
}
