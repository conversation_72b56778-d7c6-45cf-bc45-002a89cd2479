/**
 * WebSocket连接管理器
 * 统一管理WebSocket连接状态、初始化和重连逻辑
 */
import { Client } from '@stomp/stompjs';
import SockJS from 'sockjs-client/dist/sockjs.min.js';
import { useUserStore } from '@/stores';
import { ElMessage } from 'element-plus';

// 连接状态
const CONNECTION_STATE = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  RECONNECTING: 'reconnecting'
};

class ConnectionManager {
  constructor() {
    this.stompClient = null;
    this.state = CONNECTION_STATE.DISCONNECTED;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 10;
    this.reconnectDelay = 2000; // 初始重连延迟(ms)
    this.subscriptions = [];
    this.connectionListeners = [];
    this.connectionPromise = null;
    this.heartbeatTimer = null;
    this.processedMessages = new Set(); // 用于去重
  }

  /**
   * 获取当前连接状态
   * @returns {string} 连接状态
   */
  getState() {
    return this.state;
  }

  /**
   * 检查是否已连接
   * @returns {boolean} 是否已连接
   */
  isConnected() {
    return this.state === CONNECTION_STATE.CONNECTED &&
           this.stompClient &&
           this.stompClient.connected;
  }

  /**
   * 初始化WebSocket连接
   * @returns {Promise<boolean>} 连接是否成功
   */
  async connect() {
    // 如果已经在连接中，返回现有的Promise
    if (this.connectionPromise &&
        (this.state === CONNECTION_STATE.CONNECTING ||
         this.state === CONNECTION_STATE.RECONNECTING)) {
      console.log('WebSocket连接已在进行中，返回现有Promise');
      return this.connectionPromise;
    }

    // 如果已连接，直接返回成功
    if (this.isConnected()) {
      console.log('WebSocket已连接，无需重新连接');
      return Promise.resolve(true);
    }

    console.log('开始建立WebSocket连接...');

    // 创建新的连接Promise
    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        // 获取用户Token
        const userStore = useUserStore();
        const token = userStore.token;

        if (!token) {
          console.warn('未找到用户Token，无法建立WebSocket连接');
          this.state = CONNECTION_STATE.DISCONNECTED;
          resolve(false);
          return;
        }

        // 更新状态为连接中
        this.state = this.reconnectAttempts > 0 ?
          CONNECTION_STATE.RECONNECTING :
          CONNECTION_STATE.CONNECTING;

        // 触发状态变更事件
        this._notifyStateChange();

        // 创建STOMP客户端 - 使用统一的/ws端点
        this.stompClient = new Client({
          // 不使用brokerURL，而是使用webSocketFactory
          connectHeaders: {
            Authorization: `Bearer ${token}`,
            token: token
          },
          debug: function(str) {
            // 仅在开发环境输出调试信息
            if (process.env.NODE_ENV === 'development') {
              console.debug(str);
            }
          },
          reconnectDelay: 0, // 禁用内置重连，使用我们自己的重连逻辑
          heartbeatIncoming: 25000, // 25秒，与后端application.yml中的设置保持一致
          heartbeatOutgoing: 25000, // 25秒，与后端application.yml中的设置保持一致
          // 添加WebSocket连接参数
          webSocketFactory: () => {
            // 获取API基础URL，确保连接到后端服务器而不是前端开发服务器
            const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081';

            // 构建带token参数的URL
            const timestamp = new Date().getTime();
            const wsUrl = `${apiBaseUrl}/ws?token=${encodeURIComponent(token)}&t=${timestamp}`;

            // 创建SockJS实例，连接到后端服务器
            console.log('创建SockJS连接URL:', wsUrl.replace(token, '***'));
            const sockjs = new SockJS(wsUrl);
            console.log('创建SockJS连接:', sockjs);
            return sockjs;
          }
        });

        // 连接成功回调
        this.stompClient.onConnect = (frame) => {
          console.log('WebSocket连接成功', frame);
          this.state = CONNECTION_STATE.CONNECTED;
          this.reconnectAttempts = 0;

          // 启动心跳
          this._startHeartbeat();

          // 触发状态变更事件
          this._notifyStateChange();

          // 触发全局连接成功事件
          window.dispatchEvent(new CustomEvent('websocket-connected', {
            detail: { timestamp: Date.now() }
          }));

          resolve(true);
        };

        // 连接错误回调
        this.stompClient.onStompError = (frame) => {
          console.error('STOMP错误:', frame);
          this._handleConnectionError(new Error(`STOMP错误: ${frame.headers?.message || JSON.stringify(frame)}`));
          reject(new Error(`STOMP错误: ${frame.headers?.message || JSON.stringify(frame)}`));
        };

        // WebSocket错误回调
        this.stompClient.onWebSocketError = (event) => {
          console.error('WebSocket错误:', event);
          this._handleConnectionError(new Error(`WebSocket错误: ${event.message || '连接失败'}`));
          reject(new Error(`WebSocket错误: ${event.message || '连接失败'}`));
        };

        // 连接断开回调
        this.stompClient.onWebSocketClose = (event) => {
          console.warn('WebSocket连接关闭:', event);

          if (this.state === CONNECTION_STATE.CONNECTED) {
            this.state = CONNECTION_STATE.DISCONNECTED;

            // 触发状态变更事件
            this._notifyStateChange();

            // 触发全局断开连接事件
            window.dispatchEvent(new CustomEvent('websocket-disconnected', {
              detail: { timestamp: Date.now() }
            }));

            // 尝试重连
            this._reconnect();
          }
        };

        // 将stompClient暴露到window对象，供其他模块使用
        window.wsClient = window.wsClient || {};
        window.wsClient.stompClient = this.stompClient;

        // 启动连接
        this.stompClient.activate();
      } catch (error) {
        console.error('WebSocket连接初始化失败:', error);
        this._handleConnectionError(error);
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    if (this.stompClient && this.stompClient.connected) {
      // 清理心跳
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }

      // 清理订阅
      this.subscriptions.forEach(sub => {
        try {
          if (sub && typeof sub.unsubscribe === 'function') {
            sub.unsubscribe();
          }
        } catch (error) {
          console.warn('取消订阅失败:', error);
        }
      });
      this.subscriptions = [];

      // 断开连接
      this.stompClient.deactivate();
      this.stompClient = null;
    }

    // 更新状态
    this.state = CONNECTION_STATE.DISCONNECTED;
    this._notifyStateChange();

    // 触发全局断开连接事件
    window.dispatchEvent(new CustomEvent('websocket-disconnected', {
      detail: { timestamp: Date.now() }
    }));

    console.log('WebSocket连接已断开');
  }

  /**
   * 添加连接状态监听器
   * @param {Function} listener 监听器函数
   */
  addConnectionListener(listener) {
    if (typeof listener === 'function' &&
        !this.connectionListeners.includes(listener)) {
      this.connectionListeners.push(listener);
    }
  }

  /**
   * 移除连接状态监听器
   * @param {Function} listener 监听器函数
   */
  removeConnectionListener(listener) {
    const index = this.connectionListeners.indexOf(listener);
    if (index !== -1) {
      this.connectionListeners.splice(index, 1);
    }
  }

  /**
   * 发送心跳包
   * @private
   */
  _sendHeartbeat() {
    if (!this.isConnected()) {
      return;
    }

    try {
      const userStore = useUserStore();
      const userId = userStore.userInfo?.id;

      if (!userId) {
        return;
      }

      this.stompClient.publish({
        destination: '/app/user/heartbeat',
        body: String(userId)
      });
    } catch (error) {
      console.warn('发送心跳包失败:', error);
    }
  }

  /**
   * 启动心跳机制
   * @private
   */
  _startHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
    }

    this.heartbeatTimer = setInterval(() => {
      this._sendHeartbeat();
    }, 25000); // 每25秒发送一次心跳，与后端application.yml中的设置保持一致
  }

  /**
   * 处理连接错误
   * @param {Error} error 错误对象
   * @private
   */
  _handleConnectionError(error) {
    console.error('WebSocket连接错误:', error);
    this.state = CONNECTION_STATE.DISCONNECTED;
    this._notifyStateChange();

    // 触发全局断开连接事件
    window.dispatchEvent(new CustomEvent('websocket-disconnected', {
      detail: { error: error.message, timestamp: Date.now() }
    }));

    // 尝试重连
    this._reconnect();
  }

  /**
   * 重连逻辑
   * @private
   */
  _reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`WebSocket重连失败，已达到最大重试次数(${this.maxReconnectAttempts})`);
      ElMessage.error('网络连接失败，请刷新页面重试');
      return;
    }

    this.reconnectAttempts++;
    this.state = CONNECTION_STATE.RECONNECTING;
    this._notifyStateChange();

    // 触发全局重连事件
    window.dispatchEvent(new CustomEvent('websocket-reconnecting', {
      detail: {
        attempts: this.reconnectAttempts,
        maxAttempts: this.maxReconnectAttempts,
        timestamp: Date.now()
      }
    }));

    // 计算重连延迟（指数退避）
    const delay = Math.min(
      this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1),
      30000 // 最大30秒
    );

    console.log(`将在 ${delay/1000} 秒后进行第 ${this.reconnectAttempts} 次重连...`);

    // 延迟重连
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('WebSocket重连失败:', error);
      });
    }, delay);
  }

  /**
   * 通知所有监听器连接状态变更
   * @private
   */
  _notifyStateChange() {
    this.connectionListeners.forEach(listener => {
      try {
        listener(this.state);
      } catch (error) {
        console.error('执行连接状态监听器时出错:', error);
      }
    });
  }
}

// 创建单例实例
const connectionManager = new ConnectionManager();

export default connectionManager;
