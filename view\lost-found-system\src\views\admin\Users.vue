<template>
  <div class="admin-users">
    <!-- 顶部搜索和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h2>用户管理</h2>
        <el-tag type="info" class="user-count">共 {{ total }} 人</el-tag>
      </div>
      <div class="header-right">
        <el-input
          v-model="searchQuery"
          placeholder="搜索用户名/邮箱/手机号"
          class="search-input"
          clearable
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-select v-model="roleFilter" placeholder="角色筛选" clearable @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="普通用户" value="USER" />
          <el-option label="管理员" value="ADMIN" />
          <el-option label="超级管理员" value="SUPER_ADMIN" />
        </el-select>

        <el-select v-model="statusFilter" placeholder="状态筛选" clearable @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="启用" value="true" />
          <el-option label="禁用" value="false" />
        </el-select>

        <el-select v-model="deletedFilter" placeholder="删除状态" clearable @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="正常" value="false" />
          <el-option label="已删除" value="true" />
        </el-select>

        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
      </div>
    </div>

    <!-- 用户列表表格 -->
    <el-table
      v-loading="loading"
      :data="users"
      style="width: 100%"
      border
      stripe
      :header-cell-style="{
        background: '#f5f7fa',
        color: '#606266',
        fontWeight: '500',
        fontSize: '14px',
        height: '48px',
        padding: '0 12px'
      }"
      :cell-style="{
        fontSize: '14px',
        padding: '8px 12px',
        color: '#606266'
      }"
      :row-style="{ height: '56px' }"
    >
      <el-table-column label="头像" width="80" align="center">
        <template #default="{ row }">
          <el-avatar :size="36" :src="row.avatar" class="user-avatar">
            <template #default>
              {{ row.username?.charAt(0)?.toUpperCase() }}
            </template>
          </el-avatar>
        </template>
      </el-table-column>

      <el-table-column label="用户名" min-width="140">
        <template #default="{ row }">
          <div class="username-cell">
            <span class="username-text">{{ row.username }}</span>
            <el-tooltip
              v-if="isCurrentUser(row)"
              content="当前登录账号"
              placement="top"
              effect="light"
            >
              <el-icon class="current-user-icon"><User /></el-icon>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="邮箱" min-width="180">
        <template #default="{ row }">
          <span class="info-text">{{ formatEmail(row.email) || '未设置' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="手机号" min-width="130">
        <template #default="{ row }">
          <span class="info-text">{{ formatPhone(row.phone) || '未设置' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="角色" width="130" align="center">
        <template #default="{ row }">
          <el-tag :type="getRoleType(row.role)" class="role-tag" size="small">
            {{ getRoleLabel(row.role) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="90" align="center">
        <template #default="{ row }">
          <el-tag :type="row.isActive ? 'success' : 'danger'" class="status-tag" size="small">
            {{ row.isActive ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="删除状态" width="110" align="center">
        <template #default="{ row }">
          <el-tag :type="row.deleted ? 'danger' : 'success'" effect="plain" class="deleted-tag" size="small">
            {{ row.deleted ? '已删除' : '正常' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="220" fixed="right" align="center">
        <template #default="{ row }">
          <user-actions
            :user="row"
            @view="handleViewUser"
            @edit="handleEditUser"
            @reset-password="handleResetPassword"
            @toggle-status="handleToggleStatus"
            @edit-role="handleEditRole"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 用户详情抽屉 -->
    <user-detail-drawer
      v-model="detailDrawerVisible"
      :user="currentUser"
    />

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="editForm.id ? '编辑用户' : '新增用户'"
      width="500px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" />
        </el-form-item>
        <el-form-item v-if="isSuperAdmin()" label="角色" prop="role">
          <el-select v-model="editForm.role" placeholder="请选择角色">
            <el-option label="普通用户" value="USER" />
            <el-option label="管理员" value="ADMIN" />
            <el-option label="超级管理员" value="SUPER_ADMIN" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改角色对话框 -->
    <el-dialog
      v-model="showRoleDialog"
      title="修改用户角色"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="roleRules"
        label-width="80px"
      >
        <el-form-item label="用户名">
          <el-input v-model="selectedUser.username" disabled />
        </el-form-item>

        <el-form-item label="当前角色">
          <el-tag :type="getRoleType(selectedUser.role)">
            {{ getRoleLabel(selectedUser.role) }}
          </el-tag>
        </el-form-item>

        <el-form-item label="新角色" prop="role">
          <el-select v-model="roleForm.role" placeholder="请选择角色">
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRoleDialog = false">取消</el-button>
          <el-button type="primary" @click="handleRoleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Edit,
  Key,
  Lock,
  Unlock,
  User
} from '@element-plus/icons-vue'
import request from '@/utils/request'
import { isSuperAdmin } from '@/utils/auth'
import { formatDate, formatFriendlyDate } from '@/utils/date'
import UserDetailDrawer from '@/components/admin/UserDetailDrawer.vue'
import UserActions from '@/components/admin/UserActions.vue'
import { useUserStore } from '@/stores'

const loading = ref(false)
const users = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const roleFilter = ref('')
const statusFilter = ref('')
const deletedFilter = ref('')

// 用户详情抽屉
const detailDrawerVisible = ref(false)
const currentUser = ref(null)
const selectedUser = ref(null)

// 编辑对话框
const editDialogVisible = ref(false)
const editFormRef = ref(null)
const editForm = ref({
  id: '',
  username: '',
  email: '',
  phone: '',
  role: ''
})

const editRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 修改角色相关
const showRoleDialog = ref(false)
const roleFormRef = ref(null)
const roleForm = reactive({
  role: ''
})

const roleOptions = [
  { value: 'USER', label: '普通用户' },
  { value: 'ADMIN', label: '管理员' },
  { value: 'SUPER_ADMIN', label: '超级管理员' }
]

const roleRules = {
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const userStore = useUserStore()

// 获取用户列表
const fetchUsers = async (params) => {
  loading.value = true
  try {
    const defaultParams = {
      page: currentPage.value,
      size: pageSize.value,
      keyword: searchQuery.value,
      role: roleFilter.value,
      isActive: statusFilter.value,
      deleted: deletedFilter.value
    }

    const finalParams = { ...defaultParams, ...params }
    console.log('请求参数:', finalParams)

    const response = await request.get('/admin/users', { params: finalParams })
    console.log('API 原始响应数据:', response)

    if (!response || response.code !== 200) {
      throw new Error(response?.message || '返回数据格式不正确')
    }

    const responseData = response.data
    console.log('处理后的响应数据:', responseData)

    if (responseData.data && Array.isArray(responseData.data)) {
      users.value = responseData.data
      total.value = responseData.total || responseData.data.length
    } else {
      throw new Error('无法识别的数据格式')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error(error.message || '获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 查看用户详情
const handleViewUser = (user) => {
  currentUser.value = user
  detailDrawerVisible.value = true
}

// 编辑用户
const handleEditUser = (user) => {
  editForm.value = {
    id: user.id,
    username: user.username,
    email: user.email,
    phone: user.phone,
    role: user.role
  }
  editDialogVisible.value = true
}

// 保存编辑
const handleSaveEdit = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()

    if (!isSuperAdmin()) {
      ElMessage.warning('只有超级管理员可以修改用户角色')
      return
    }

    await request.put(`/admin/users/${editForm.value.id}/role`, {
      role: editForm.value.role
    })

    ElMessage.success('修改成功')
    editDialogVisible.value = false
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改失败:', error)
      ElMessage.error('修改失败')
    }
  }
}

// 重置密码
const handleResetPassword = async (user) => {
  try {
    await ElMessageBox.prompt('请输入新密码', '重置密码', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^.{6,20}$/,
      inputErrorMessage: '密码长度必须在6-20个字符之间',
      inputValue: '123456' // 设置默认密码
    }).then(async ({ value }) => {
      await request.put(`/admin/users/${user.id}/reset-password`, {
        password: value
      })
      ElMessage.success('密码重置成功')
    })
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
    }
  }
}

// 切换用户状态
const handleToggleStatus = async (user) => {
  try {
    const newStatus = !user.isActive
    await request.put(`/admin/users/${user.id}/status`, {
      isActive: newStatus
    })
    ElMessage.success(`用户状态已${newStatus ? '启用' : '禁用'}`)
    fetchUsers()
  } catch (error) {
    console.error('切换用户状态失败:', error)
    ElMessage.error('切换用户状态失败')
  }
}

// 修改用户角色
const handleEditRole = (user) => {
  selectedUser.value = user
  roleForm.role = user.role
  showRoleDialog.value = true
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  const params = {
    page: currentPage.value,
    size: pageSize.value,
    keyword: searchQuery.value,
    role: roleFilter.value,
    isActive: statusFilter.value,
    deleted: deletedFilter.value
  }
  console.log('搜索参数:', params)
  fetchUsers(params)
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchUsers()
}

// 处理每页数量变化
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchUsers()
}

// 格式化邮箱，中间部分用***代替
const formatEmail = (email) => {
  if (!email) return ''
  const [name, domain] = email.split('@')
  if (name.length <= 2) return email
  return `${name.slice(0, 1)}***${name.slice(-1)}@${domain}`
}

// 格式化手机号，中间4位用*代替
const formatPhone = (phone) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 获取角色标签类型
const getRoleType = (role) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return 'danger'
    case 'ADMIN':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取角色显示文本
const getRoleLabel = (role) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return '超级管理员'
    case 'ADMIN':
      return '管理员'
    default:
      return '普通用户'
  }
}

// 提交角色修改
const handleRoleSubmit = async () => {
  if (!roleFormRef.value) return

  try {
    await roleFormRef.value.validate()
    await request.put(`/admin/users/${selectedUser.value.id}/role`, {
      role: roleForm.role
    })
    ElMessage.success('角色修改成功')
    showRoleDialog.value = false
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改角色失败:', error)
      ElMessage.error('修改角色失败')
    }
  }
}

// 判断是否是当前登录用户
const isCurrentUser = (user) => {
  return userStore.userInfo?.id === user.id
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.admin-users {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  width: 200px;
}

.header-right :deep(.el-select) {
  width: 100px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-table) {
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.user-avatar {
  border: 1px solid #ebeef5;
  background: #f5f7fa;
  color: #909399;
  font-weight: 500;
}

.username-cell {
  display: flex;
  align-items: center;
  gap: 4px;
}

.username-text {
  font-weight: 500;
  color: #303133;
}

.current-user-icon {
  font-size: 14px;
  color: #67c23a;
  margin-top: 1px;
}

.info-text {
  color: #606266;
}

.role-tag, .status-tag, .deleted-tag {
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 11px;
  font-size: 12px;
  font-weight: normal;
}

:deep(.el-table__row) {
  transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa !important;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-pagination) {
  margin-top: 20px;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-button) {
  padding: 5px 8px;
  margin: 0 2px;
}

:deep(.el-button--text) {
  padding: 0 8px;
}
</style>