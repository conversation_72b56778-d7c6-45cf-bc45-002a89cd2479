package com.tomato.lostfoundsystem.service;

import java.util.Map;
public interface WebSocketService {

    // 用户上线：保存用户 WebSocket 会话
    void userOnline(String userId);

    // 用户下线：移除用户 WebSocket 会话
    void userOffline(String userId);

    // 判断用户是否在线
    boolean isUserOnline(String userId);

    // 发送消息给指定用户
    void sendMessage(Long userId, Map<String, Object> message);

    // 获取所有在线用户
    Map<String, Boolean> getOnlineUsers();
}
