package com.tomato.lostfoundsystem.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务配置
 */
@Slf4j
@Configuration
public class AsyncConfig implements AsyncConfigurer {

    /**
     * 配置异步任务执行器
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数：核心线程会一直存活，即使没有任务需要处理
        executor.setCorePoolSize(5);

        // 最大线程数：当队列中存放的任务达到队列容量时，当前可以同时执行的线程数量变为最大线程数
        executor.setMaxPoolSize(10);

        // 队列容量：存放待处理任务的队列大小
        executor.setQueueCapacity(100);

        // 线程名称前缀
        executor.setThreadNamePrefix("async-task-");

        // 当线程池关闭时等待所有任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间（默认为0，此时立即停止），并没等待xx秒后强制停止
        executor.setAwaitTerminationSeconds(60);

        // 拒绝策略：当线程池已满时，新任务的处理策略
        // CALLER_RUNS：在调用者线程中运行被拒绝的任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 初始化线程池
        executor.initialize();

        return executor;
    }

    /**
     * 智能搜索专用线程池
     * 针对计算密集型的特征提取和向量搜索任务
     */
    @Bean(name = "searchExecutor")
    public Executor searchExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 获取CPU核心数
        int processors = Runtime.getRuntime().availableProcessors();
        
        // 智能搜索是计算密集型，核心线程数设置为CPU核心数
        executor.setCorePoolSize(processors);
        
        // 最大线程数略大于核心线程数
        executor.setMaxPoolSize(processors + 2);
        
        // 队列容量设置较小，避免大量等待任务占用内存
        executor.setQueueCapacity(20);
        
        // 线程名称前缀，方便定位问题
        executor.setThreadNamePrefix("search-exec-");
        
        // 优先级设置为较高
        executor.setThreadPriority(Thread.MAX_PRIORITY);
        
        // 当线程池关闭时等待所有任务完成
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(120);
        
        // 拒绝策略：在调用者线程中运行被拒绝的任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 初始化线程池
        executor.initialize();
        
        log.info("已初始化智能搜索线程池，核心线程数: {}, 最大线程数: {}", processors, processors + 2);
        
        return executor;
    }

    /**
     * 指定默认的异步任务执行器
     */
    @Override
    public Executor getAsyncExecutor() {
        return taskExecutor();
    }

    /**
     * 异步任务异常处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> {
            log.error("异步任务执行异常 - 方法: {}, 参数: {}, 异常: {}",
                    method.getName(),
                    params,
                    ex.getMessage());
            log.error("异步任务异常堆栈", ex);
        };
    }
}
