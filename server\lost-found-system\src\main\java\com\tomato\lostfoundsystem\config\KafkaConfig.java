package com.tomato.lostfoundsystem.config;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
@ConditionalOnProperty(value = "spring.kafka.enabled", havingValue = "true", matchIfMissing = true)
public class KafkaConfig {

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    /**
     * Kafka 生产者配置
     * KafkaTemplate 用于生产消息，传递到 Kafka 队列
     */
    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());  // 使用 KafkaTemplate 来发送消息
    }

    /**
     * 生产者工厂
     * 通过 Spring Boot 自动读取 application.yml 中的配置，不需要手动指定属性
     */
    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    /**
     * 生产者配置
     * 显式设置序列化器配置，避免配置缺失问题
     */
    private Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();

        // 设置 Kafka 服务器地址
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);

        // 设置键和值的序列化器
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        // 设置确认模式
        props.put(ProducerConfig.ACKS_CONFIG, "all");

        // 设置重试次数
        props.put(ProducerConfig.RETRIES_CONFIG, 3);

        // 设置批处理大小
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);

        // 设置批处理延迟
        props.put(ProducerConfig.LINGER_MS_CONFIG, 5);

        // 设置缓冲区大小
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);

        return props;
    }
}

