package com.tomato.lostfoundsystem.service.kafka;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.model.kafka.ItemApprovedEvent;
import com.tomato.lostfoundsystem.service.FeatureExtractionService;
import com.tomato.lostfoundsystem.service.IntelligentMatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Service;

/**
 * 物品审核通过事件消费者
 * 作为兜底路径，处理Spring异步处理失败的任务
 */
@Slf4j
@Service
public class ItemApprovedEventConsumer {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private FeatureExtractionService featureExtractionService;

    @Autowired
    private IntelligentMatchService intelligentMatchService;

    /**
     * 消费物品审核通过事件（兜底路径）
     * 专注于特征提取和智能匹配的兜底处理
     *
     * @param message Kafka消息
     * @param partition 分区ID
     * @param offset 偏移量
     * @param acknowledgment 确认对象
     */
    @KafkaListener(topics = "${kafka.topic.item-approved:item-approved-topic}",
                  groupId = "${kafka.group.item-processor:item-processor-group}")
    public void consume(String message,
                       @Header("kafka_receivedPartitionId") int partition,
                       @Header("kafka_offset") long offset,
                       Acknowledgment acknowledgment) {
        try {
            log.info("【Kafka兜底】收到物品审核通过事件: {}, 分区: {}, 偏移量: {}", message, partition, offset);

            // 解析消息
            ItemApprovedEvent event = objectMapper.readValue(message, ItemApprovedEvent.class);

            log.info("【Kafka兜底】开始处理物品审核通过事件: 物品ID={}, 类型={}, 用户ID={}",
                    event.getItemId(), event.getItemType(), event.getUserId());

            // 提取并存储特征向量
            boolean extractionResult = featureExtractionService.extractAndStoreFeatures(
                    event.getItemId(), event.getItemType());

            if (!extractionResult) {
                log.error("【Kafka兜底】特征提取失败，跳过智能匹配: 物品ID={}, 类型={}",
                        event.getItemId(), event.getItemType());
                // 即使特征提取失败，也确认消息，避免无限重试
                acknowledgment.acknowledge();
                return;
            }

            log.info("【Kafka兜底】特征提取成功，开始执行智能匹配: 物品ID={}, 类型={}",
                    event.getItemId(), event.getItemType());

            // 执行智能匹配
            intelligentMatchService.performAutoMatch(
                    event.getUserId(), event.getItemId(), event.getItemType());

            log.info("【Kafka兜底】物品审核通过事件处理完成: 物品ID={}, 类型={}",
                    event.getItemId(), event.getItemType());

            // 确认消息
            acknowledgment.acknowledge();
        } catch (Exception e) {
            log.error("【Kafka兜底】处理物品审核通过事件时发生异常", e);
            // 确认消息，避免无限重试
            // 在实际生产环境中，可能需要更复杂的重试策略
            acknowledgment.acknowledge();
        }
    }
}
