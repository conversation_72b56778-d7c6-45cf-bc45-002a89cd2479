<template>
  <div class="intelligent-match-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>智能匹配</h2>
        <div class="title-line"></div>
        <p class="subtitle">使用CLIP+FAISS技术，通过图片或文字描述快速匹配失物与拾物</p>
      </div>

    <!-- 匹配流程指南 -->
    <div class="guide">
      <div class="guide-step">
        <div class="step-number">1</div>
        <div class="step-icon">
          <el-icon><Select /></el-icon>
        </div>
        <h3>选择匹配方式</h3>
        <p>选择图片匹配、文字匹配或混合匹配方式</p>
      </div>
      <div class="guide-step">
        <div class="step-number">2</div>
        <div class="step-icon">
          <el-icon><Upload /></el-icon>
        </div>
        <h3>提供匹配内容</h3>
        <p>上传物品图片或输入详细的物品描述</p>
      </div>
      <div class="guide-step">
        <div class="step-number">3</div>
        <div class="step-icon">
          <el-icon><Search /></el-icon>
        </div>
        <h3>开始智能匹配</h3>
        <p>系统将自动分析并找出最相似的物品</p>
      </div>
      <div class="guide-step">
        <div class="step-number">4</div>
        <div class="step-icon">
          <el-icon><ChatLineRound /></el-icon>
        </div>
        <h3>联系物品发布者</h3>
        <p>找到匹配物品后，可直接联系发布者</p>
      </div>
    </div>

    <!-- 匹配方式选择 -->
    <div class="section-title">
      <h2>匹配方式</h2>
      <div class="title-line"></div>
    </div>

    <div class="features">
      <div class="feature-card" :class="{ 'active-card': matchType === 'image' }" @click="matchType = 'image'">
        <div class="card-icon">
          <el-icon><Picture /></el-icon>
        </div>
        <h3>图片匹配</h3>
        <p>上传物品图片，系统将分析图像特征进行匹配</p>
      </div>
      <div class="feature-card" :class="{ 'active-card': matchType === 'text' }" @click="matchType = 'text'">
        <div class="card-icon">
          <el-icon><Document /></el-icon>
        </div>
        <h3>文字匹配</h3>
        <p>输入物品描述，系统将分析文本内容进行匹配</p>
      </div>
      <div class="feature-card" :class="{ 'active-card': matchType === 'mixed' }" @click="matchType = 'mixed'">
        <div class="card-icon">
          <el-icon><Files /></el-icon>
        </div>
        <h3>混合匹配</h3>
        <p>同时使用图片和文字，提高匹配精度和成功率</p>
      </div>
    </div>

    <!-- 匹配内容输入区域 -->
    <div class="section-title">
      <h2>匹配内容</h2>
      <div class="title-line"></div>
    </div>

    <div class="match-type-container">

    <el-card class="match-input-card">
      <!-- 图片上传区域 -->
      <div v-if="matchType === 'image' || matchType === 'mixed'" class="upload-area">
        <el-upload
          class="image-uploader"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleImageChange"
          accept="image/jpeg,image/jpg,image/png"
        >
          <img v-if="imageUrl" :src="imageUrl" class="uploaded-image" />
          <div v-else class="upload-placeholder">
            <el-icon class="upload-icon"><Plus /></el-icon>
            <div class="upload-text">点击上传图片</div>
            <div class="upload-hint">支持JPG、PNG格式，最大5MB</div>
          </div>
        </el-upload>
      </div>

      <!-- 文字描述区域 -->
      <div v-if="matchType === 'text' || matchType === 'mixed'" class="text-area">
        <el-form :model="matchForm" label-position="top">
          <el-form-item label="物品名称" required>
            <el-input
              v-model="matchForm.itemName"
              placeholder="请输入物品名称，如：黑色双肩背包、蓝色水杯等"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="物品描述">
            <el-input
              v-model="matchForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入物品详细描述，如：内有学生证和银行卡等"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <div class="description-example">
            <el-alert
              title="提示：请填写物品名称和详细描述，系统会自动提取关键词进行匹配"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </el-form>
      </div>

      <!-- 匹配类型选择 -->
      <div class="match-type-selection">
        <el-form :model="matchForm" label-position="top">
          <el-form-item label="匹配类型">
            <el-radio-group v-model="matchForm.itemType">
              <el-radio label="LOST">我丢失了物品，查找拾物信息</el-radio>
              <el-radio label="FOUND">我拾到了物品，查找失物信息</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 开始匹配按钮 -->
      <div class="match-actions">
        <el-button type="primary" size="large" class="match-button" :loading="loading" @click="startMatch">
          <el-icon><Search /></el-icon>
          开始匹配
        </el-button>
        <el-button size="large" @click="resetForm">
          <el-icon><RefreshRight /></el-icon>
          重置
        </el-button>
      </div>
    </el-card>

    <!-- 匹配结果展示区域 -->
    <div v-if="showResults" class="section-title">
      <h2>匹配结果</h2>
      <div class="title-line"></div>
      <div class="result-stats">
        <template v-if="matchType === 'image'">
          找到 <span class="highlight">{{ imageToImageResults.length + imageToTextResults.length }}</span> 条可能的匹配
          （图像-图像: {{ imageToImageResults.length }}，图像-文本: {{ imageToTextResults.length }}）
        </template>
        <template v-else-if="matchType === 'text'">
          找到 <span class="highlight">{{ textToTextResults.length + textToImageResults.length }}</span> 条可能的匹配
          （文本-文本: {{ textToTextResults.length }}，文本-图像: {{ textToImageResults.length }}）
        </template>
        <template v-else>
          找到 <span class="highlight">{{ displayResults.length }}</span> 条可能的匹配
        </template>
      </div>
    </div>

    <div v-if="showResults" class="match-results">
      <!-- 结果控制区域 -->
      <div class="results-controls">
        <div class="results-count">
          <template v-if="matchType === 'image'">
            找到 <span class="highlight">{{ imageToImageResults.length + imageToTextResults.length }}</span> 条可能的匹配
          </template>
          <template v-else-if="matchType === 'text'">
            找到 <span class="highlight">{{ textToTextResults.length + textToImageResults.length }}</span> 条可能的匹配
          </template>
          <template v-else>
            找到 <span class="highlight">{{ displayResults.length }}</span> 条可能的匹配
          </template>
        </div>
        <div class="results-options">
          <el-button v-if="!showAdvanced" type="text" @click="showAdvanced = true">
            高级选项 <el-icon><ArrowDown /></el-icon>
          </el-button>
          <el-button v-else type="text" @click="showAdvanced = false">
            收起高级选项 <el-icon><ArrowUp /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 匹配结果标签页 -->
      <el-tabs v-model="activeResultTab" class="result-tabs">
        <!-- 图像匹配结果标签页 -->
        <el-tab-pane v-if="matchType === 'image'" label="图像匹配结果" name="image">
          <el-tabs v-model="activeImageSubTab" class="sub-tabs">
            <el-tab-pane label="图像-图像匹配" name="image_to_image">
              <div class="tab-description">
                <p>图像-图像匹配展示了您上传的图片与物品图片的视觉相似度，适合寻找外观相似的物品。</p>
              </div>
              <el-row :gutter="20">
                <el-col v-for="item in imageToImageResults" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6">
                  <match-result-card
                    :item="item"
                    :target-type="matchForm.itemType.toUpperCase()"
                    @view-detail="viewDetail"
                    @contact-owner="contactOwner"
                  />
                </el-col>
              </el-row>
              <el-empty v-if="imageToImageResults.length === 0" description="未找到图像-图像匹配结果" />
            </el-tab-pane>
            <el-tab-pane label="图像-文本匹配" name="image_to_text">
              <div class="tab-description">
                <p>图像-文本匹配展示了您上传的图片与物品文字描述的相似度，适合通过图片寻找描述相符的物品。</p>
              </div>
              <el-row :gutter="20">
                <el-col v-for="item in imageToTextResults" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6">
                  <match-result-card
                    :item="item"
                    :target-type="matchForm.itemType.toUpperCase()"
                    @view-detail="viewDetail"
                    @contact-owner="contactOwner"
                  />
                </el-col>
              </el-row>
              <el-empty v-if="imageToTextResults.length === 0" description="未找到图像-文本匹配结果" />
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>

        <!-- 文本匹配结果标签页 -->
        <el-tab-pane v-if="matchType === 'text'" label="文本匹配结果" name="text">
          <el-tabs v-model="activeTextSubTab" class="sub-tabs">
            <el-tab-pane label="文本-文本匹配" name="text_to_text">
              <div class="tab-description">
                <p>文本-文本匹配展示了您输入的描述与物品文字描述的相似度，适合通过描述寻找相似物品。</p>
              </div>
              <el-row :gutter="20">
                <el-col v-for="item in textToTextResults" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6">
                  <match-result-card
                    :item="item"
                    :target-type="matchForm.itemType.toUpperCase()"
                    @view-detail="viewDetail"
                    @contact-owner="contactOwner"
                  />
                </el-col>
              </el-row>
              <el-empty v-if="textToTextResults.length === 0" description="未找到文本-文本匹配结果" />
            </el-tab-pane>
            <el-tab-pane label="文本-图像匹配" name="text_to_image">
              <div class="tab-description">
                <p>文本-图像匹配展示了您输入的描述与物品图片的视觉相似度，适合通过描述寻找外观相符的物品。</p>
              </div>
              <el-row :gutter="20">
                <el-col v-for="item in textToImageResults" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6">
                  <match-result-card
                    :item="item"
                    :target-type="matchForm.itemType.toUpperCase()"
                    @view-detail="viewDetail"
                    @contact-owner="contactOwner"
                  />
                </el-col>
              </el-row>
              <el-empty v-if="textToImageResults.length === 0" description="未找到文本-图像匹配结果" />
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>

        <!-- 混合匹配结果标签页 -->
        <el-tab-pane v-if="matchType === 'mixed'" label="混合匹配结果" name="mixed">
          <div class="tab-description">
            <p>混合匹配结果展示了综合考虑图像和文本信息后的匹配结果，每个结果都包含四种相似度的雷达图分析。</p>
            <p>雷达图中的四个维度分别是：图像-图像（1.0权重）、图像-文本（0.5权重）、文本-图像（0.5权重）和文本-文本（1.0权重）。</p>
          </div>
          <el-row :gutter="20">
            <el-col v-for="item in displayResults" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6">
              <el-card class="match-card">
                <div class="item-image">
                  <el-image
                    :src="item.imageUrl"
                    fit="cover"
                    :preview-src-list="[item.imageUrl]"
                    :initial-index="0"
                    alt="物品图片"
                  >
                    <template #error>
                      <div class="image-error">
                        <span>暂无图片</span>
                      </div>
                    </template>
                  </el-image>
                  <!-- 匹配度色块 -->
                  <div
                    class="similarity-badge"
                    :style="{ background: getSimilarityColor(item.similarity) }"
                  >
                    匹配度 {{ (item.similarity * 100).toFixed(0) }}%
                  </div>
                  <!-- 物品类型标签 -->
                  <div
                    class="item-tag"
                    :class="matchForm.itemType === 'FOUND' ? 'lost-tag' : 'found-tag'"
                  >
                    {{ matchForm.itemType === 'FOUND' ? '失物' : '拾物' }}
                  </div>
                </div>
                <div class="item-info">
                  <div class="item-title-row">
                    <div class="item-title">{{ item.name }}</div>
                    <div class="item-title-radar">
                      <similarity-radar-chart
                        v-if="item.match_details"
                        :similarities="{
                          image_to_image: item.match_details?.IMAGE_TO_IMAGE || 0,
                          image_to_text: item.match_details?.IMAGE_TO_TEXT || 0,
                          text_to_image: item.match_details?.TEXT_TO_IMAGE || 0,
                          text_to_text: item.match_details?.TEXT_TO_TEXT || 0
                        }"
                        width="80px"
                        height="80px"
                        title="匹配度分析"
                        :compact="true"
                      />
                    </div>
                  </div>
                  <div class="item-meta-row">
                    <span class="meta-item">
                      <el-icon><Location /></el-icon>
                      {{ item.location }}
                    </span>
                    <span class="meta-item">
                      <el-icon><Timer /></el-icon>
                      {{ item.time }}
                    </span>
                  </div>
                  <div class="item-desc">{{ item.description }}</div>
                  <div class="item-actions">
                    <el-button type="primary" size="small" @click="viewDetail(item)">查看详情</el-button>
                    <el-button type="success" size="small" @click="contactOwner(item)">联系对方</el-button>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-empty v-if="displayResults.length === 0" description="未找到混合匹配结果" />
        </el-tab-pane>
      </el-tabs>

      <!-- 高级选项 - 可折叠的额外结果 -->
      <el-collapse-transition>
        <div v-if="showAdvanced" class="advanced-results">
          <el-divider>匹配详情</el-divider>

          <!-- 匹配相似度说明 -->
          <div class="similarity-explanation">
            <h3>相似度说明</h3>
            <p>匹配结果中的相似度分数表示物品与您的描述或图片的匹配程度。相似度越高，表示匹配度越高。</p>
            <ul>
              <li><span class="similarity-high">高匹配度 (80%以上)</span>：非常可能是您要找的物品</li>
              <li><span class="similarity-medium">中等匹配度 (60%-80%)</span>：有一定可能是您要找的物品</li>
              <li><span class="similarity-low">低匹配度 (60%以下)</span>：匹配度较低，但仍有参考价值</li>
            </ul>
          </div>

          <!-- 匹配类型说明 -->
          <div class="match-type-explanation">
            <h3>匹配类型说明</h3>
            <div v-if="matchType === 'image'">
              <p>图像搜索包含两种不同的匹配类型：</p>
              <ul>
                <li><strong>图像-图像</strong>：您上传的图片与物品图片的视觉相似度，适合寻找外观相似的物品</li>
                <li><strong>图像-文本</strong>：您上传的图片与物品文字描述的相似度，适合通过图片寻找描述相符的物品</li>
              </ul>
            </div>
            <div v-else-if="matchType === 'text'">
              <p>文本搜索包含两种不同的匹配类型：</p>
              <ul>
                <li><strong>文本-文本</strong>：您输入的描述与物品文字描述的相似度，适合通过描述寻找相似物品</li>
                <li><strong>文本-图像</strong>：您输入的描述与物品图片的视觉相似度，适合通过描述寻找外观相符的物品</li>
              </ul>
            </div>
            <p>系统会同时展示这些匹配类型的结果，并按相似度排序。</p>
          </div>
        </div>
      </el-collapse-transition>

      <!-- 空结果提示已移至各标签页内 -->
    </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore, useAuthStore } from '@/stores'
import {
  Location,
  Timer
} from '@element-plus/icons-vue'
import { ElMessage, ElLoading } from 'element-plus'
import { matchByImage, matchByText, matchByMixed } from '@/api/match'
import MatchResultCard from '@/components/MatchResultCard.vue'
import SimilarityRadarChart from '@/components/SimilarityRadarChart.vue'

const router = useRouter()
const userStore = useUserStore()
const authStore = useAuthStore()

// 检查用户是否已登录
onMounted(() => {
  if (!userStore.isAuthenticated) {
    // 将当前路径保存到会话存储中，以便登录后重定向回来
    sessionStorage.setItem('loginRedirect', router.currentRoute.value.fullPath)
    // 显示登录对话框
    authStore.showLoginDialog({
      tab: 'login',
      onSuccess: () => {
        // 登录成功后会自动重定向回来，不需要额外处理
      },
      onCancel: () => {
        // 取消登录则返回首页
        router.push('/')
      }
    })
  }
})

// 匹配方式
const matchType = ref('image')

// 表单数据
const matchForm = reactive({
  itemName: '',      // 物品名称
  description: '',   // 详细描述
  itemType: 'LOST'   // 默认是寻找拾物信息（使用大写与后端匹配）
})

// 上传图片相关
const imageUrl = ref('')
const imageFile = ref(null)

// 加载状态
const loading = ref(false)

// 是否显示结果
const showResults = ref(false)

// 匹配结果
const matchResults = ref([])

// 高级选项状态
const showAdvanced = ref(false)
const activeResultTab = ref('image')

// 子标签页激活状态
const activeImageSubTab = ref('image_to_image')
const activeTextSubTab = ref('text_to_text')

// 计算属性 - 不同类型的匹配结果
const imageToImageResults = computed(() => {
  if (!matchResults.value || !matchResults.value.image_to_image) return []
  return matchResults.value.image_to_image || []
})

const imageToTextResults = computed(() => {
  if (!matchResults.value || !matchResults.value.image_to_text) return []
  return matchResults.value.image_to_text || []
})

const textToTextResults = computed(() => {
  if (!matchResults.value || !matchResults.value.text_to_text) return []
  return matchResults.value.text_to_text || []
})

const textToImageResults = computed(() => {
  if (!matchResults.value || !matchResults.value.text_to_image) return []
  return matchResults.value.text_to_image || []
})

// 默认显示所有结果的合并
const displayResults = computed(() => {
  // 根据匹配类型返回不同的结果
  if (matchType.value === 'image') {
    // 对于图像搜索，合并图像到图像和图像到文本的结果
    const allResults = [...imageToImageResults.value, ...imageToTextResults.value]
    // 按相似度排序
    return allResults.sort((a, b) => b.similarity - a.similarity)
  } else if (matchType.value === 'text') {
    // 对于文本搜索，合并文本到文本和文本到图像的结果
    const allResults = [...textToTextResults.value, ...textToImageResults.value]
    // 按相似度排序
    return allResults.sort((a, b) => b.similarity - a.similarity)
  } else if (matchType.value === 'mixed') {
    // 混合匹配结果
    const combinedResults = Array.isArray(matchResults.value.combined) ? matchResults.value.combined : [];
    // 确保每个结果都有 match_details 字段
    return combinedResults.map(item => {
      // 如果没有 match_details 字段，添加一个空对象
      if (!item.match_details) {
        console.warn('结果项缺少 match_details 字段:', item);
        item.match_details = {
          IMAGE_TO_IMAGE: 0,
          IMAGE_TO_TEXT: 0,
          TEXT_TO_IMAGE: 0,
          TEXT_TO_TEXT: 0
        };
      }
      return item;
    })
  } else {
    return []
  }
})

// 处理图片上传
const handleImageChange = (file) => {
  // 获取文件扩展名
  const fileName = file.name || '';
  const fileExtension = fileName.toLowerCase().split('.').pop();

  // 检查文件扩展名而不是MIME类型
  const isValidExtension = ['jpg', 'jpeg', 'png'].includes(fileExtension);
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isValidExtension) {
    ElMessage.error('只能上传JPG或PNG格式的图片!');
    return false;
  }

  if (!isLt5M) {
    ElMessage.error('图片大小不能超过5MB!');
    return false;
  }

  // 打印文件信息以便调试
  console.log('文件信息:', {
    name: file.name,
    type: file.type,
    extension: fileExtension,
    size: (file.size / 1024 / 1024).toFixed(2) + 'MB'
  });

  imageFile.value = file.raw;
  imageUrl.value = URL.createObjectURL(file.raw);
}

// 开始匹配
const startMatch = async () => {
  try {
    // 验证输入
    if (matchType.value === 'image' && !imageUrl.value) {
      ElMessage.warning('请上传图片')
      return
    }

    if (matchType.value === 'text' && !matchForm.itemName) {
      ElMessage.warning('请至少输入物品名称')
      return
    }

    if (matchType.value === 'mixed' && !imageUrl.value && !matchForm.itemName) {
      ElMessage.warning('请上传图片或至少输入物品名称')
      return
    }

    // 开始加载
    loading.value = true

    // 根据匹配类型调用不同的API
    let response

    if (matchType.value === 'image') {
      // 创建FormData对象
      const formData = new FormData()

      // 打印文件信息以便调试
      console.log('准备上传图片:', {
        name: imageFile.value.name,
        type: imageFile.value.type,
        size: (imageFile.value.size / 1024 / 1024).toFixed(2) + 'MB'
      });

      // 使用Blob创建新文件对象，确保MIME类型正确
      const blob = imageFile.value.slice(0, imageFile.value.size, 'image/jpeg');
      const newFile = new File([blob], imageFile.value.name, { type: 'image/jpeg' });

      formData.append('file', newFile);
      formData.append('itemType', matchForm.itemType);

      // 调用图片匹配API
      response = await matchByImage(formData);
    } else if (matchType.value === 'text') {
      // 构建结构化描述
      const structuredData = buildStructuredDescription();

      // 调用文本匹配API
      response = await matchByText({
        itemName: structuredData.itemName,
        itemDescription: structuredData.itemDescription,
        itemType: matchForm.itemType
      });
    } else {
      // 创建FormData对象
      const formData = new FormData();

      if (imageFile.value) {
        // 使用Blob创建新文件对象，确保MIME类型正确
        const blob = imageFile.value.slice(0, imageFile.value.size, 'image/jpeg');
        const newFile = new File([blob], imageFile.value.name, { type: 'image/jpeg' });

        formData.append('file', newFile);
      }

      // 构建结构化描述
      const structuredData = buildStructuredDescription();
      formData.append('itemName', structuredData.itemName);
      formData.append('itemDescription', structuredData.itemDescription);
      formData.append('itemType', matchForm.itemType);

      // 调用混合匹配API
      response = await matchByMixed(formData);
    }

    // 处理响应
    if (response.code === 200) {
      console.log('匹配结果:', response.data);

      // 设置匹配结果
      if (matchType.value === 'image') {
        // 图像匹配结果包含两个部分：image_to_image, image_to_text
        matchResults.value = response.data.results || {};
        console.log('图像匹配结果:', matchResults.value);
        // 默认显示图像匹配结果标签页
        activeResultTab.value = 'image';
      } else if (matchType.value === 'text') {
        // 文本匹配结果包含两个部分：text_to_text, text_to_image
        matchResults.value = response.data.results || {};
        console.log('文本匹配结果:', matchResults.value);
        // 默认显示文本匹配结果标签页
        activeResultTab.value = 'text';
      } else {
        // 混合匹配结果
        matchResults.value = response.data.results || {};
        console.log('混合匹配结果:', matchResults.value);
        console.log('混合匹配结果 combined:', matchResults.value.combined);

        // 检查结果中是否包含 match_details 字段
        if (Array.isArray(matchResults.value.combined)) {
          const firstItem = matchResults.value.combined[0];
          if (firstItem) {
            console.log('第一个混合匹配结果项:', firstItem);
            console.log('是否包含 match_details 字段:', !!firstItem.match_details);
            if (firstItem.match_details) {
              console.log('match_details 内容:', firstItem.match_details);
            }
          }
        }

        // 默认显示混合匹配结果标签页
        activeResultTab.value = 'mixed';
      }

      // 显示结果和高级选项
      showResults.value = true
      showAdvanced.value = true

      // 滚动到结果区域
      setTimeout(() => {
        const resultsSection = document.querySelector('.match-results')
        if (resultsSection) {
          resultsSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        }
      }, 100)
    } else {
      ElMessage.error(response.message || '匹配失败，请稍后重试')
    }
  } catch (error) {
    console.error('匹配过程中发生错误:', error)
    ElMessage.error('匹配失败，请稍后重试')
  } finally {
    loading.value = false
  }
}



// 重置表单
const resetForm = () => {
  matchForm.itemName = ''
  matchForm.description = ''
  imageUrl.value = ''
  imageFile.value = null
  showResults.value = false
}

// 查看详情
const viewDetail = (item) => {
  const route = matchForm.itemType === 'lost'
    ? `/found-items/detail/${item.id}`
    : `/lost-items/detail/${item.id}`

  router.push(route)
}

// 联系对方
const contactOwner = (item) => {
  // 模拟联系对方，实际应该跳转到聊天页面或显示联系方式
  ElMessage.success(`即将跳转到与物品发布者的聊天页面`)
  // 这里可以添加跳转到聊天页面的逻辑
  router.push('/chat')
}

// 构建结构化描述 - 只包含物品名称和物品描述
const buildStructuredDescription = () => {
  // 创建一个对象，只包含物品名称和物品描述
  const structuredData = {
    itemName: matchForm.itemName || '',
    itemDescription: matchForm.description || ''
  };

  // 检查物品名称和描述的长度
  if (structuredData.itemName.length > 20) {
    structuredData.itemName = structuredData.itemName.substring(0, 20);
    console.log('物品名称过长，已截断至20个字符:', structuredData.itemName);
  }

  if (structuredData.itemDescription.length > 50) {
    structuredData.itemDescription = structuredData.itemDescription.substring(0, 50);
    console.log('物品描述过长，已截断至50个字符:', structuredData.itemDescription);
  }

  return structuredData;
}

// 获取相似度颜色
const getSimilarityColor = (similarity) => {
  const sim = parseFloat(similarity)
  if (sim >= 0.8) return '#67C23A' // 高匹配度 - 绿色
  if (sim >= 0.6) return '#E6A23C' // 中等匹配度 - 黄色
  return '#F56C6C' // 低匹配度 - 红色
}

// 百分比格式化
const percentFormat = (percentage) => {
  return `${percentage.toFixed(0)}%`
}

// 在结果列表中查找匹配的项目
const findMatchingItem = (list, itemId) => {
  if (!list || !Array.isArray(list)) return null;
  return list.find(item => item.id === itemId);
}


</script>

<style scoped>
.intelligent-match-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

/* 页面标题样式 - 与首页一致 */
.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h2 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 8px;
}

.subtitle {
  color: #606266;
  font-size: 16px;
  margin-top: 0.5rem;
}

/* 匹配度分数与雷达图图标的样式 */
.similarity-score-with-chart {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.similarity-score {
  font-size: 14px;
  color: #606266;
  margin-right: 5px;
}

.similarity-score .score {
  font-weight: bold;
  font-size: 16px;
  color: #409EFF;
}

.title-line {
  width: 60px;
  height: 3px;
  background-color: #1890ff;
  margin: 0 auto;
  margin-bottom: 1rem;
}

/* 指南步骤样式 - 与首页一致 */
.guide {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3rem;
  gap: 1.5rem;
  width: 100%;
}

@media (max-width: 992px) {
  .guide {
    flex-wrap: wrap;
  }

  .guide-step {
    flex-basis: calc(50% - 1rem);
  }
}

@media (max-width: 576px) {
  .guide-step {
    flex-basis: 100%;
  }
}

.guide-step {
  background-color: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  text-align: center;
  position: relative;
  height: 100%;
  flex: 1;
  min-width: 180px;
}

.step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-color: #722ed1;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.step-icon {
  font-size: 2rem;
  color: #722ed1;
  margin-bottom: 1rem;
}

.guide-step h3 {
  margin-bottom: 0.5rem;
  color: #303133;
}

.guide-step p {
  color: #606266;
  font-size: 14px;
}

/* 功能卡片样式 - 与首页一致 */
.section-title {
  text-align: center;
  margin-bottom: 1.5rem;
}

.section-title h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 8px;
}

.features {
  display: flex;
  justify-content: space-between;
  gap: 2rem;
  margin-bottom: 3rem;
  width: 100%;
}

@media (max-width: 992px) {
  .features {
    flex-wrap: wrap;
  }

  .feature-card {
    flex-basis: calc(50% - 1rem);
  }
}

@media (max-width: 576px) {
  .feature-card {
    flex-basis: 100%;
  }
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: transform 0.3s, box-shadow 0.3s;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  flex: 1;
  min-width: 220px;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.active-card {
  border: 2px solid #722ed1;
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  margin-bottom: 1.5rem;
  font-size: 2.5rem;
  color: #722ed1;
  background-color: #f9f0ff;
}

.feature-card h3 {
  font-size: 18px;
  margin-bottom: 1rem;
  color: #303133;
}

.feature-card p {
  color: #606266;
  font-size: 14px;
}

/* 匹配类型容器样式 */
.match-type-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;
  width: 100%;
}

/* 匹配输入卡片样式 */
.match-input-card {
  margin-bottom: 3rem;
  border-radius: 8px;
}

.upload-area {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  width: 100%;
}

.image-uploader {
  width: 100%;
  max-width: 400px;
  height: 250px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  margin: 0 auto;
}

.image-uploader:hover {
  border-color: #722ed1;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.upload-icon {
  font-size: 3rem;
  color: #909399;
  margin-bottom: 1rem;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 0.5rem;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

.text-area {
  margin-bottom: 20px;
}

.match-type-selection {
  margin-bottom: 20px;
}

.match-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.match-button {
  background-color: #722ed1;
  border-color: #722ed1;
}

.match-button:hover {
  background-color: #9254de;
  border-color: #9254de;
}

/* 匹配结果样式 - 与首页一致 */
.result-stats {
  font-size: 16px;
  color: #606266;
  margin-top: 0.5rem;
}

.highlight {
  color: #722ed1;
  font-weight: bold;
}

.match-results {
  margin-bottom: 3rem;
  width: 100%;
}

.results-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.sub-tabs {
  margin-top: 20px;
}

.tab-description {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
}

/* 混合匹配结果卡片样式 */
.match-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
  height: 100%;
}
.match-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}
.item-image {
  position: relative;
  height: 160px;
  overflow: hidden;
}
.similarity-badge {
  position: absolute;
  left: 10px;
  bottom: 10px;
  padding: 4px 12px;
  border-radius: 16px;
  color: #fff;
  font-weight: bold;
  font-size: 13px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  z-index: 2;
  opacity: 0.95;
}
.item-tag {
  position: absolute;
  left: 10px;
  top: 10px;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  color: #fff;
  z-index: 2;
  opacity: 0.95;
}
.lost-tag { background: #52c41a; }
.found-tag { background: #1890ff; }
.item-info {
  padding: 12px 16px 10px 16px;
}
.item-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.item-title-radar {
  min-width: 80px;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.item-title {
  font-size: 17px;
  font-weight: bold;
  color: #222;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}
.item-meta-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
  color: #888;
}
.meta-item {
  display: flex;
  align-items: center;
  gap: 3px;
}
.item-desc {
  color: #666;
  font-size: 13px;
  margin-bottom: 10px;
  min-height: 32px;
}
.item-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  gap: 8px;
}
@media (max-width: 768px) {
  .item-meta-row { flex-direction: column; gap: 2px; }
  .item-actions { flex-direction: column; gap: 6px; }
}
</style>
