<template>
  <el-dialog
    v-model="dialogVisible"
    :title="notification.title"
    width="600px"
    destroy-on-close
  >
    <div class="notification-detail">
      <!-- 通知类型标签 -->
      <div class="notification-type">
        <el-tag :type="getNotificationTagType(notification)">
          {{ getNotificationTypeText(notification) }}
        </el-tag>
      </div>

      <!-- 通知内容 -->
      <div class="notification-content">
        {{ notification.message }}
      </div>

      <!-- 审核信息（仅审核通知） -->
      <div v-if="isAuditNotification" class="audit-info">
        <!-- 审核员信息 -->
        <div v-if="auditorName" class="auditor-info">
          <span class="label">审核员：</span>
          <span class="value">{{ auditorName }}</span>
        </div>

        <!-- 审核员角色 -->
        <div v-if="auditorRole" class="auditor-role">
          <span class="label">审核员角色：</span>
          <span class="value">{{ formatRole(auditorRole) }}</span>
        </div>

        <!-- 审核时间 -->
        <div v-if="auditTime" class="audit-time">
          <span class="label">审核时间：</span>
          <span class="value">{{ formatDateTime(auditTime) }}</span>
        </div>

        <!-- 拒绝原因 -->
        <div v-if="isRejectedAuditNotification" class="reject-reason">
          <span class="label">拒绝原因：</span>
          <span class="value">{{ rejectReason }}</span>
        </div>
      </div>

      <!-- 管理员通知信息 -->
      <div v-if="isAdminNotification && hasMetadata" class="admin-info">
        <!-- 管理员信息 -->
        <div v-if="adminName" class="admin-name">
          <span class="label">发送者：</span>
          <span class="value">{{ adminName }}</span>
        </div>

        <!-- 管理员角色 -->
        <div v-if="adminRole" class="admin-role">
          <span class="label">发送者角色：</span>
          <span class="value">{{ formatRole(adminRole) }}</span>
        </div>

        <!-- 发送时间 -->
        <div v-if="sendTime" class="send-time">
          <span class="label">发送时间：</span>
          <span class="value">{{ formatDateTime(sendTime) }}</span>
        </div>

        <!-- 重要程度 -->
        <div v-if="importance" class="importance">
          <span class="label">重要程度：</span>
          <span class="value">
            <el-tag :type="getImportanceType(importance)" size="small">
              {{ formatImportance(importance) }}
            </el-tag>
          </span>
        </div>
      </div>

      <!-- 关联物品 -->
      <div v-if="hasRelatedItem" class="related-item">
        <el-alert
          :title="relatedItemTitle"
          type="info"
          :closable="false"
        >
          <template #default>
            <div class="item-info-content">
              <p>您可以点击下方按钮查看相关物品的详细信息</p>
              <div class="detail-actions">
                <el-button
                  type="primary"
                  @click="viewRelatedItem"
                >
                  <el-icon><ArrowRight /></el-icon>
                  查看物品详情
                </el-button>

                <!-- 审核拒绝通知的重新提交按钮 -->
                <el-button
                  v-if="isRejectedAuditNotification"
                  type="success"
                  @click="handleResubmit"
                >
                  <el-icon><RefreshRight /></el-icon>
                  修改并重新提交
                </el-button>
              </div>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 通知时间 -->
      <div class="notification-time">
        <span class="label">通知时间：</span>
        <span class="value">{{ formatDateTime(notification.createdAt) }}</span>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">关闭</el-button>
        <el-button type="danger" @click="handleDelete">删除</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { ArrowRight, RefreshRight } from '@element-plus/icons-vue'
import {
  getNotificationTypeText,
  getNotificationTagType,
  NotificationType,
  getAuditorName,
  getAuditTime,
  getRejectReason,
  parseMetadata,
  getImportanceColor
} from '@/utils/notificationUtils'
import { deleteNotification } from '@/api/notification'

const props = defineProps({
  notification: {
    type: Object,
    required: true
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'deleted'])
const router = useRouter()
const dialogVisible = ref(false)

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})

// 监听对话框可见性变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 解析元数据
const metadata = computed(() => {
  return parseMetadata(props.notification)
})

// 计算属性：是否有元数据
const hasMetadata = computed(() => {
  return Object.keys(metadata.value).length > 0
})

// 计算属性：是否为审核通知
const isAuditNotification = computed(() => {
  const type = props.notification.type
  return type === NotificationType.AUDIT_APPROVED || type === NotificationType.AUDIT_REJECTED
})

// 计算属性：是否为管理员通知
const isAdminNotification = computed(() => {
  return props.notification.type === NotificationType.ADMIN ||
         props.notification.type === NotificationType.SYSTEM ||
         props.notification.type === NotificationType.ANNOUNCEMENT
})

// 计算属性：是否为审核拒绝通知
const isRejectedAuditNotification = computed(() => {
  return props.notification.type === NotificationType.AUDIT_REJECTED
})

// 计算属性：是否有关联物品
const hasRelatedItem = computed(() => {
  return props.notification.relatedItemId && props.notification.relatedItemType
})

// 计算属性：关联物品标题
const relatedItemTitle = computed(() => {
  if (!hasRelatedItem.value) return ''

  const itemType = props.notification.relatedItemType === 'LOST' ? '失物' : '拾物'
  return `相关${itemType}信息（ID: ${props.notification.relatedItemId}）`
})

// 计算属性：审核员名称
const auditorName = computed(() => {
  return getAuditorName(props.notification)
})

// 计算属性：审核员角色
const auditorRole = computed(() => {
  return metadata.value.auditorRole
})

// 计算属性：审核时间
const auditTime = computed(() => {
  return getAuditTime(props.notification)
})

// 计算属性：拒绝原因
const rejectReason = computed(() => {
  return getRejectReason(props.notification)
})

// 计算属性：管理员名称
const adminName = computed(() => {
  return metadata.value.adminName
})

// 计算属性：管理员角色
const adminRole = computed(() => {
  return metadata.value.adminRole
})

// 计算属性：发送时间
const sendTime = computed(() => {
  return metadata.value.sendTime
})

// 计算属性：重要程度
const importance = computed(() => {
  return metadata.value.importance
})

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''

  try {
    const date = new Date(dateTimeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (e) {
    return dateTimeStr
  }
}

// 查看关联物品
const viewRelatedItem = () => {
  if (!hasRelatedItem.value) return

  const path = props.notification.relatedItemType === 'LOST'
    ? `/lost-items/detail/${props.notification.relatedItemId}`
    : `/found-items/detail/${props.notification.relatedItemId}`

  closeDialog()
  router.push(path)
}

// 处理重新提交
const handleResubmit = () => {
  if (!hasRelatedItem.value || !isRejectedAuditNotification.value) return

  const path = props.notification.relatedItemType === 'LOST'
    ? `/lost-items/edit/${props.notification.relatedItemId}`
    : `/found-items/edit/${props.notification.relatedItemId}`

  closeDialog()
  router.push(path)
}

// 处理删除
const handleDelete = () => {
  ElMessageBox.confirm('确定要删除这条通知吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteNotification(props.notification.id)
      ElMessage.success('删除成功')
      closeDialog()
      emit('deleted', props.notification.id)
    } catch (error) {
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 格式化角色
const formatRole = (role) => {
  if (!role) return ''

  switch (role.toUpperCase()) {
    case 'ADMIN':
      return '管理员'
    case 'SUPER_ADMIN':
      return '超级管理员'
    case 'USER':
      return '普通用户'
    default:
      return role
  }
}

// 格式化重要程度
const formatImportance = (importance) => {
  if (!importance) return '普通'

  switch (importance.toUpperCase()) {
    case 'URGENT':
      return '紧急'
    case 'IMPORTANT':
      return '重要'
    case 'NORMAL':
      return '普通'
    default:
      return importance
  }
}

// 获取重要程度标签类型
const getImportanceType = (importance) => {
  if (!importance) return 'info'

  switch (importance.toUpperCase()) {
    case 'URGENT':
      return 'danger'
    case 'IMPORTANT':
      return 'warning'
    case 'NORMAL':
      return 'info'
    default:
      return 'info'
  }
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.notification-detail {
  padding: 10px;
}

.notification-type {
  margin-bottom: 15px;
}

.notification-content {
  margin-bottom: 20px;
  line-height: 1.6;
  white-space: pre-line;
}

.audit-info, .admin-info {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.audit-info .label, .admin-info .label {
  font-weight: bold;
  color: #606266;
  margin-right: 5px;
}

.audit-info .value, .admin-info .value {
  color: #303133;
}

.auditor-info, .audit-time, .reject-reason,
.admin-name, .admin-role, .send-time, .importance {
  margin-bottom: 8px;
}

.reject-reason .value {
  color: #F56C6C;
}

.related-item {
  margin-bottom: 20px;
}

.item-info-content {
  margin-top: 10px;
}

.detail-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.notification-time {
  margin-top: 20px;
  color: #909399;
  font-size: 0.9em;
}
</style>
