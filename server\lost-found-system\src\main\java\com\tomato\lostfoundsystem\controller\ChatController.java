package com.tomato.lostfoundsystem.controller;


import com.github.pagehelper.PageInfo;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.ChatMessageDTO;
import com.tomato.lostfoundsystem.dto.ContactDTO;
import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.dto.ReadReceiptDTO;
import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.enums.FileType;
import com.tomato.lostfoundsystem.enums.MessageType;
import com.tomato.lostfoundsystem.service.ChatMessageService;
import com.tomato.lostfoundsystem.service.ConversationService;
import com.tomato.lostfoundsystem.service.KafkaProducerService;
import com.tomato.lostfoundsystem.utils.AliyunOSSUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@Slf4j
@RestController
@RequestMapping("/api/chat")
public class ChatController {

    @Autowired
    private ChatMessageService chatMessageService;

    @Autowired
    private ConversationService conversationService;  // 用于创建会话和获取联系人信息

    @Autowired
    private KafkaProducerService kafkaProducerService; // 用于发送消息到Kafka

    @Autowired
    private org.springframework.messaging.simp.SimpMessagingTemplate messagingTemplate; // 用于WebSocket消息推送

    @Autowired
    private com.tomato.lostfoundsystem.mapper.UserMapper userMapper; // 用于获取用户名



    @PostMapping("/createConversation")
    public ResponseEntity<Result<?>> createConversation(@RequestParam Long senderId, @RequestParam Long receiverId) {
        log.info("创建会话请求 - senderId: {}, receiverId: {}", senderId, receiverId);

        // 参数验证
        if (senderId == null || receiverId == null) {
            log.warn("创建会话失败 - 参数无效");
            return ResponseEntity.badRequest().body(Result.fail("参数无效"));
        }

        try {
            // 调用服务层创建会话
            Long conversationId = conversationService.createConversation(senderId, receiverId);
            log.info("会话创建成功 - ID: {}", conversationId);

            // 返回成功响应，包含会话ID
            return ResponseEntity.ok(Result.success("会话创建成功", conversationId));
        } catch (Exception e) {
            log.error("创建会话失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.fail("创建会话失败: " + e.getMessage()));
        }
    }

    @GetMapping("/contacts/{userId}")
    public ResponseEntity<Result<List<ContactDTO>>> getContacts(@PathVariable Long userId) {
        log.info("开始获取用户ID: {} 的联系人列表", userId);
        try {
            // 获取联系人信息
            log.debug("调用conversationService.getContacts方法");
            List<ContactDTO> contacts = conversationService.getContacts(userId);
            log.info("成功获取用户ID: {} 的联系人列表，共 {} 个联系人", userId, contacts.size());
            // 详细记录每个联系人信息
            for (ContactDTO contact : contacts) {
                log.debug("联系人信息: id={}, name={}, messageType={}, lastMessage={}",
                        contact.getContactId(), contact.getName(),
                        contact.getMessageType(), contact.getLastMessage());
            }
            // 返回成功的结果集
            return ResponseEntity.ok(Result.success(contacts));
        } catch (Exception e) {
            // 记录详细的异常信息
            log.error("获取用户ID: {} 的联系人列表失败，异常信息: {}", userId, e.getMessage(), e);
            // 出现异常时返回失败的结果集
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.fail("获取联系人信息失败：" + e.getMessage()));
        }
    }

    @GetMapping("/chatHistory/{userId}/{otherUserId}")
    public ResponseEntity<Result<PageInfo<ChatMessageDTO>>> getChatHistory(@PathVariable Long userId,
                                                                        @PathVariable Long otherUserId,
                                                                        @RequestParam(defaultValue = "1") int page,  // 默认页码为1
                                                                        @RequestParam(defaultValue = "20") int size) {  // 默认每页加载20条
        log.info("历史消息的userId:{},otherUserId{},page{},size{}",userId,otherUserId,page,size);
        try {
            // 获取聊天记录
            PageInfo<ChatMessageDTO> chatHistory = chatMessageService.getChatHistory(userId, otherUserId, page, size);
            // 返回成功的结果集
            return ResponseEntity.ok(Result.success(chatHistory));
        } catch (Exception e) {
            // 发生异常时返回失败的结果集
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.fail("获取聊天记录失败：" + e.getMessage()));
        }
    }

    /**
     * 发送私聊消息 - REST API
     * 支持文本消息和带附件的消息
     */
    @PostMapping("/privateMessage")
    public ResponseEntity<Result<MessageDTO>> sendMessage(
            @RequestParam("senderId") Long senderId,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam(value = "message", required = false) String textMessage,
            @RequestParam(value = "messageType", defaultValue = "TEXT") String messageType,
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam(value = "audioDuration", required = false) Integer audioDuration,
            @RequestParam(value = "videoDuration", required = false) Integer videoDuration) {

        log.info("收到REST API消息: senderId={}, receiverId={}, messageType={}, hasFile={}",
                senderId, receiverId, messageType, (file != null));

        try {
            // 创建消息DTO
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setSenderId(senderId);
            messageDTO.setReceiverId(receiverId);
            messageDTO.setMessage(textMessage);
            messageDTO.setMessageType(MessageType.valueOf(messageType));
            messageDTO.setClientMessageId("rest-" + System.currentTimeMillis());

            // 设置媒体时长
            if ("AUDIO".equals(messageType) && audioDuration != null) {
                messageDTO.setAudioDuration(audioDuration);
            } else if ("VIDEO".equals(messageType) && videoDuration != null) {
                messageDTO.setVideoDuration(videoDuration);
            }

            // 保存消息到数据库
            MessageDTO savedMessage = chatMessageService.saveChatMessage(messageDTO, file);

            // 注意：不再在这里发送消息到Kafka，而是由ChatMessageServiceImpl的afterCommit回调处理
            log.info("消息将由事务提交后的回调发送到Kafka: {}", savedMessage.getId());

            // 推送回发送者做状态回显 - 使用标准路径 /queue/sent
            // 获取发送者的用户名
            String senderUsername = getUsernameById(senderId);
            if (senderUsername != null) {
                messagingTemplate.convertAndSendToUser(
                    senderUsername,  // 使用用户名而不是用户ID
                    "/queue/sent",
                    savedMessage
                );
                log.info("推送状态回显: 用户名={}", senderUsername);
            } else {
                log.error("无法获取发送者用户名，使用ID推送状态回显: {}", senderId);
                // 回退到使用ID
                messagingTemplate.convertAndSendToUser(
                    senderId.toString(),
                    "/queue/sent",
                    savedMessage
                );
            }

            return ResponseEntity.ok(Result.success(savedMessage));
        } catch (Exception e) {
            log.error("发送消息失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.fail("发送消息失败: " + e.getMessage()));
        }
    }

    /**
     * 发送私聊消息 - WebSocket
     * 仅支持文本消息
     */
    @MessageMapping("/privateMessage")
    public void sendPrivateMessage(MessageDTO message) {
        log.info("收到WebSocket消息: {}", message);

        try {
            // 确保消息类型为TEXT
            if (message.getMessageType() == null) {
                message.setMessageType(MessageType.TEXT);
            }

            // 保存客户端消息ID，用于前端消息状态更新
            String clientMessageId = message.getClientMessageId();
            if (clientMessageId == null || clientMessageId.isEmpty()) {
                clientMessageId = "ws-" + System.currentTimeMillis();
                message.setClientMessageId(clientMessageId);
            }
            log.info("客户端消息ID: {}", clientMessageId);

            // 明确记录发送者和接收者ID
            Long senderId = message.getSenderId();
            Long receiverId = message.getReceiverId();

            log.info("准备处理消息 - 发送者: {}, 接收者: {}, 客户端消息ID: {}",
                    senderId, receiverId, clientMessageId);

            // 保存消息到数据库
            MessageDTO savedMessage = chatMessageService.saveChatMessage(message, null);

            // 如果有客户端消息ID，保留它用于前端消息状态更新
            savedMessage.setClientMessageId(clientMessageId);
            log.info("保留客户端消息ID: {}", clientMessageId);

            log.info("WebSocket消息已保存: {}, 客户端消息ID: {}", savedMessage.getId(), clientMessageId);

            // 注意：不再在这里发送消息到Kafka，而是由ChatMessageServiceImpl的afterCommit回调处理
            log.info("消息将由事务提交后的回调发送到Kafka: {}", savedMessage.getId());

            // 推送回发送者做状态回显 - 使用标准路径 /queue/sent
            log.info("推送状态回显给发送者: {}, 路径: /user/queue/sent", senderId);

            // 获取发送者的用户名
            String senderUsername = getUsernameById(senderId);
            if (senderUsername != null) {
                messagingTemplate.convertAndSendToUser(
                    senderUsername,  // 使用用户名而不是用户ID
                    "/queue/sent",
                    savedMessage
                );
                log.info("推送状态回显: 用户名={}", senderUsername);
            } else {
                log.error("无法获取发送者用户名，使用ID推送状态回显: {}", senderId);
                // 回退到使用ID
                messagingTemplate.convertAndSendToUser(
                    senderId.toString(),
                    "/queue/sent",
                    savedMessage
                );
            }

            log.info("状态已推送回发送者: {}", senderId);
        } catch (Exception e) {
            log.error("处理WebSocket消息失败: {}", e.getMessage(), e);
        }
    }



    /**
     * 处理已读回执 - WebSocket
     * 这是唯一的标记消息已读接口，用于批量处理消息已读状态
     */
    @MessageMapping("/read-receipt")
    public void handleReadReceipt(ReadReceiptDTO readReceipt) {
        log.info("收到WebSocket已读回执: {}", readReceipt);

        try {
            // 1. 获取必要参数
            List<Long> messageIds = readReceipt.getMessageIds();
            Long readerId = readReceipt.getReaderId();
            Long senderId = readReceipt.getSenderId();

            if (messageIds == null || messageIds.isEmpty()) {
                log.warn("已读回执中没有消息ID: {}", readReceipt);
                return;
            }

            if (readerId == null) {
                log.warn("已读回执中没有读者ID: {}", readReceipt);
                return;
            }

            if (senderId == null) {
                log.warn("已读回执中没有发送者ID: {}", readReceipt);
                return;
            }

            // 2. 使用Kafka异步处理已读回执
            // 发送到Kafka主题，由消费者处理数据库更新和WebSocket通知
            log.info("将已读回执发送到Kafka: {}", readReceipt);
            kafkaProducerService.sendReadReceipt(readReceipt);
            log.info("已将已读回执发送到Kafka: {}", readReceipt);

            // 3. 同时在数据库中标记这些消息为已读
            try {
                // 对每个消息ID调用服务层方法进行批量处理
                for (Long messageId : messageIds) {
                    chatMessageService.markAsRead(messageId, readerId);
                }
                log.info("已在数据库中标记 {} 条消息为已读", messageIds.size());
            } catch (Exception e) {
                log.error("在数据库中标记消息已读失败: {}", e.getMessage(), e);
                // 不抛出异常，因为Kafka处理可能已经成功
            }
        } catch (Exception e) {
            log.error("处理WebSocket已读回执失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据用户ID获取用户名
     * @param userId 用户ID
     * @return 用户名，如果未找到则返回null
     */
    private String getUsernameById(Long userId) {
        try {
            if (userId == null) {
                log.warn("尝试获取用户名时收到空的用户ID");
                return null;
            }

            // 从数据库中查询用户
            com.tomato.lostfoundsystem.entity.User user = userMapper.findById(userId);
            if (user != null) {
                return user.getUsername();
            } else {
                log.warn("未找到用户ID对应的用户: {}", userId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取用户名时出错: {}", e.getMessage(), e);
            return null;
        }
    }
}

