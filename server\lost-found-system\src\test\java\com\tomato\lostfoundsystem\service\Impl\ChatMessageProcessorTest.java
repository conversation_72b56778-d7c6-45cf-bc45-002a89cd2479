package com.tomato.lostfoundsystem.service.Impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.enums.MessageType;
import com.tomato.lostfoundsystem.model.kafka.ChatMessageEnvelope;
import com.tomato.lostfoundsystem.service.ConversationService;
import com.tomato.lostfoundsystem.service.RedisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ChatMessageProcessorTest {

    @InjectMocks
    private ChatMessageProcessor chatMessageProcessor;

    @Mock
    private SimpMessagingTemplate messagingTemplate;

    @Mock
    private RedisService redisService;

    @Mock
    private MessageRetryService messageRetryService;

    @Mock
    private ConversationService conversationService;

    @Spy
    private ObjectMapper objectMapper = new ObjectMapper();

    @Mock
    private Acknowledgment acknowledgment;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testProcessChatMessage_ReceiverOnline() throws JsonProcessingException {
        // 准备测试数据
        Long senderId = 1L;
        Long receiverId = 2L;
        String message = "Hello";

        // 创建消息DTO
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setId(100L);
        messageDTO.setSenderId(senderId);
        messageDTO.setReceiverId(receiverId);
        messageDTO.setMessage(message);
        messageDTO.setMessageType(MessageType.TEXT);
        messageDTO.setTimestamp(System.currentTimeMillis());

        // 创建消息信封
        ChatMessageEnvelope envelope = ChatMessageEnvelope.create(messageDTO, "msg-100");
        envelope.setStatus(ChatMessageEnvelope.MessageStatus.PROCESSING);

        // 模拟 redisService.isUserOnline 返回 true，表示接收者在线
        when(redisService.isUserOnline(receiverId)).thenReturn(true);

        // 调用被测试方法
        String jsonMessage = objectMapper.writeValueAsString(envelope);
        chatMessageProcessor.processChatMessage(jsonMessage, 0, 0, acknowledgment);

        // 验证 messagingTemplate.convertAndSendToUser 被调用，表示消息被推送
        verify(messagingTemplate).convertAndSendToUser(
            eq(receiverId.toString()),
            eq("/queue/private"),
            any(String.class)
        );

        // 验证 conversationService.updateConversationWithMessage 被调用，表示会话被更新
        verify(conversationService).updateConversationWithMessage(any(ChatMessage.class));

        // 验证 acknowledgment.acknowledge 被调用，表示消息被确认
        verify(acknowledgment).acknowledge();
    }

    @Test
    void testProcessChatMessage_ReceiverOffline() throws JsonProcessingException {
        // 准备测试数据
        Long senderId = 1L;
        Long receiverId = 2L;
        String message = "Hello";

        // 创建消息DTO
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setId(100L);
        messageDTO.setSenderId(senderId);
        messageDTO.setReceiverId(receiverId);
        messageDTO.setMessage(message);
        messageDTO.setMessageType(MessageType.TEXT);
        messageDTO.setTimestamp(System.currentTimeMillis());

        // 创建消息信封
        ChatMessageEnvelope envelope = ChatMessageEnvelope.create(messageDTO, "msg-100");
        envelope.setStatus(ChatMessageEnvelope.MessageStatus.PROCESSING);

        // 模拟 redisService.isUserOnline 返回 false，表示接收者离线
        when(redisService.isUserOnline(receiverId)).thenReturn(false);

        // 调用被测试方法
        String jsonMessage = objectMapper.writeValueAsString(envelope);
        chatMessageProcessor.processChatMessage(jsonMessage, 0, 0, acknowledgment);

        // 验证 messagingTemplate.convertAndSendToUser 没有被调用，表示消息没有被推送
        verify(messagingTemplate, never()).convertAndSendToUser(
            anyString(),
            anyString(),
            any(String.class)
        );

        // 验证 conversationService.updateConversationWithMessage 没有被调用，表示会话没有被更新
        verify(conversationService, never()).updateConversationWithMessage(any(ChatMessage.class));

        // 验证 acknowledgment.acknowledge 没有被调用，表示消息没有被确认
        verify(acknowledgment, never()).acknowledge();
    }

    @Test
    void testProcessChatMessage_Exception() throws JsonProcessingException {
        // 准备测试数据
        Long senderId = 1L;
        Long receiverId = 2L;
        String message = "Hello";

        // 创建消息DTO
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setId(100L);
        messageDTO.setSenderId(senderId);
        messageDTO.setReceiverId(receiverId);
        messageDTO.setMessage(message);
        messageDTO.setMessageType(MessageType.TEXT);
        messageDTO.setTimestamp(System.currentTimeMillis());

        // 创建消息信封
        ChatMessageEnvelope envelope = ChatMessageEnvelope.create(messageDTO, "msg-100");
        envelope.setStatus(ChatMessageEnvelope.MessageStatus.PROCESSING);

        // 模拟 redisService.isUserOnline 返回 true，表示接收者在线
        when(redisService.isUserOnline(receiverId)).thenReturn(true);

        // 模拟 messagingTemplate.convertAndSendToUser 抛出异常
        doThrow(new RuntimeException("Test exception")).when(messagingTemplate).convertAndSendToUser(
            anyString(),
            anyString(),
            any(String.class)
        );

        // 调用被测试方法
        String jsonMessage = objectMapper.writeValueAsString(envelope);
        chatMessageProcessor.processChatMessage(jsonMessage, 0, 0, acknowledgment);

        // 验证 conversationService.updateConversationWithMessage 没有被调用，表示会话没有被更新
        verify(conversationService, never()).updateConversationWithMessage(any(ChatMessage.class));

        // 验证 acknowledgment.acknowledge 没有被调用，表示消息没有被确认
        verify(acknowledgment, never()).acknowledge();

        // 验证 messageRetryService.scheduleRetry 被调用，表示消息被安排重试
        verify(messageRetryService).scheduleRetry(anyString(), anyInt());
    }
}
