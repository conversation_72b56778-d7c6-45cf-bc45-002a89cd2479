package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.enums.AuditStatusEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test") // 使用测试环境配置
@Transactional // 确保测试后回滚事务，不影响数据库
public class LostItemMapperTest {

    @Autowired
    private LostItemMapper lostItemMapper;

    @Test
    void insertLostItem_ShouldInsertAndReturnOne() {
        // 准备测试数据
        LostItem lostItem = new LostItem();
        lostItem.setUserId(1L);
        lostItem.setItemName("测试物品");
        lostItem.setDescription("这是一个详细的物品描述，包含了物品的颜色、形状和特征。");
        lostItem.setLostTime("2023-05-01T10:00:00");
        lostItem.setLostLocation("图书馆");
        lostItem.setImageUrl("/default/no-image-available.png");
        lostItem.setStatus("lost");
        lostItem.setCreatedAt(LocalDateTime.now());
        lostItem.setAuditStatus(AuditStatusEnum.PENDING);

        // 执行测试
        int result = lostItemMapper.insertLostItem(lostItem);

        // 验证结果
        assertEquals(1, result);
        assertNotNull(lostItem.getId()); // 确保ID已生成
    }

    @Test
    void selectById_WithExistingId_ShouldReturnLostItem() {
        // 准备测试数据
        LostItem lostItem = new LostItem();
        lostItem.setUserId(1L);
        lostItem.setItemName("测试物品");
        lostItem.setDescription("这是一个详细的物品描述，包含了物品的颜色、形状和特征。");
        lostItem.setLostTime("2023-05-01T10:00:00");
        lostItem.setLostLocation("图书馆");
        lostItem.setImageUrl("/default/no-image-available.png");
        lostItem.setStatus("lost");
        lostItem.setCreatedAt(LocalDateTime.now());
        lostItem.setAuditStatus(AuditStatusEnum.PENDING);

        // 先插入数据
        lostItemMapper.insertLostItem(lostItem);
        Long id = lostItem.getId();

        // 执行测试
        LostItem result = lostItemMapper.selectById(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(id, result.getId());
        assertEquals("测试物品", result.getItemName());
        assertEquals("这是一个详细的物品描述，包含了物品的颜色、形状和特征。", result.getDescription());
    }

    @Test
    void selectById_WithNonExistingId_ShouldReturnNull() {
        // 执行测试
        LostItem result = lostItemMapper.selectById(999999L);

        // 验证结果
        assertNull(result);
    }

    @Test
    void updateById_WithExistingId_ShouldUpdateAndReturnOne() {
        // 准备测试数据
        LostItem lostItem = new LostItem();
        lostItem.setUserId(1L);
        lostItem.setItemName("测试物品");
        lostItem.setDescription("这是一个详细的物品描述，包含了物品的颜色、形状和特征。");
        lostItem.setLostTime("2023-05-01T10:00:00");
        lostItem.setLostLocation("图书馆");
        lostItem.setImageUrl("/default/no-image-available.png");
        lostItem.setStatus("lost");
        lostItem.setCreatedAt(LocalDateTime.now());
        lostItem.setAuditStatus(AuditStatusEnum.PENDING);

        // 先插入数据
        lostItemMapper.insertLostItem(lostItem);
        Long id = lostItem.getId();

        // 修改数据
        lostItem.setItemName("更新后的物品名称");
        lostItem.setDescription("更新后的物品描述");

        // 执行测试
        int result = lostItemMapper.updateById(lostItem);

        // 验证结果
        assertEquals(1, result);

        // 验证数据已更新
        LostItem updated = lostItemMapper.selectById(id);
        assertNotNull(updated);
        assertEquals("更新后的物品名称", updated.getItemName());
        assertEquals("更新后的物品描述", updated.getDescription());
    }

    @Test
    void deleteLostItem_WithExistingId_ShouldDeleteAndReturnOne() {
        // 准备测试数据
        LostItem lostItem = new LostItem();
        lostItem.setUserId(1L);
        lostItem.setItemName("测试物品");
        lostItem.setDescription("这是一个详细的物品描述，包含了物品的颜色、形状和特征。");
        lostItem.setLostTime("2023-05-01T10:00:00");
        lostItem.setLostLocation("图书馆");
        lostItem.setImageUrl("/default/no-image-available.png");
        lostItem.setStatus("lost");
        lostItem.setCreatedAt(LocalDateTime.now());
        lostItem.setAuditStatus(AuditStatusEnum.PENDING);

        // 先插入数据
        lostItemMapper.insertLostItem(lostItem);
        Long id = lostItem.getId();

        // 执行测试
        int result = lostItemMapper.deleteLostItem(id);

        // 验证结果
        assertEquals(1, result);

        // 验证数据已删除
        LostItem deleted = lostItemMapper.selectById(id);
        assertNull(deleted);
    }

    @Test
    void findLostItemsByUserId_ShouldReturnUserItems() {
        // 准备测试数据
        LostItem lostItem1 = new LostItem();
        lostItem1.setUserId(1L);
        lostItem1.setItemName("测试物品1");
        lostItem1.setDescription("这是一个详细的物品描述，包含了物品的颜色、形状和特征。");
        lostItem1.setLostTime("2023-05-01T10:00:00");
        lostItem1.setLostLocation("图书馆");
        lostItem1.setImageUrl("/default/no-image-available.png");
        lostItem1.setStatus("lost");
        lostItem1.setCreatedAt(LocalDateTime.now());
        lostItem1.setAuditStatus(AuditStatusEnum.PENDING);

        LostItem lostItem2 = new LostItem();
        lostItem2.setUserId(1L);
        lostItem2.setItemName("测试物品2");
        lostItem2.setDescription("这是另一个详细的物品描述。");
        lostItem2.setLostTime("2023-05-02T10:00:00");
        lostItem2.setLostLocation("食堂");
        lostItem2.setImageUrl("/default/no-image-available.png");
        lostItem2.setStatus("lost");
        lostItem2.setCreatedAt(LocalDateTime.now());
        lostItem2.setAuditStatus(AuditStatusEnum.PENDING);

        // 先插入数据
        lostItemMapper.insertLostItem(lostItem1);
        lostItemMapper.insertLostItem(lostItem2);

        // 执行测试
        List<LostItem> results = lostItemMapper.findLostItemsByUserId(1L);

        // 验证结果
        assertNotNull(results);
        assertTrue(results.size() >= 2); // 可能有其他测试插入的数据
        assertTrue(results.stream().anyMatch(item -> item.getItemName().equals("测试物品1")));
        assertTrue(results.stream().anyMatch(item -> item.getItemName().equals("测试物品2")));
    }
}
