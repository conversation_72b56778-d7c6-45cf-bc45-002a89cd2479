package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.SystemConfig;
import com.tomato.lostfoundsystem.service.SystemConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 系统配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/config")
public class SystemConfigController {
    
    @Resource
    private SystemConfigService systemConfigService;
    
    /**
     * 获取所有系统配置
     *
     * @return 系统配置列表
     */
    @GetMapping
    public Result<List<SystemConfig>> getAllConfigs() {
        log.info("获取所有系统配置");
        return systemConfigService.getAllConfigs();
    }
    
    /**
     * 根据配置键获取配置
     *
     * @param configKey 配置键
     * @return 系统配置
     */
    @GetMapping("/{configKey}")
    public Result<SystemConfig> getConfigByKey(@PathVariable String configKey) {
        log.info("获取系统配置: {}", configKey);
        return systemConfigService.getConfigByKey(configKey);
    }
    
    /**
     * 更新配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 更新结果
     */
    @PutMapping("/{configKey}")
    public Result<String> updateConfigValue(
            @PathVariable String configKey,
            @RequestBody Map<String, String> requestBody) {
        String configValue = requestBody.get("configValue");
        log.info("更新系统配置: {} = {}", configKey, configValue);
        return systemConfigService.updateConfigValue(configKey, configValue);
    }
    
    /**
     * 批量更新配置
     *
     * @param configs 配置键值对
     * @return 更新结果
     */
    @PutMapping("/batch")
    public Result<String> batchUpdateConfig(@RequestBody Map<String, String> configs) {
        log.info("批量更新系统配置: {}", configs);
        return systemConfigService.batchUpdateConfig(configs);
    }
    
    /**
     * 添加配置
     *
     * @param config 系统配置
     * @return 添加结果
     */
    @PostMapping
    public Result<String> addConfig(@RequestBody SystemConfig config) {
        log.info("添加系统配置: {}", config);
        return systemConfigService.addConfig(config);
    }
    
    /**
     * 删除配置
     *
     * @param configKey 配置键
     * @return 删除结果
     */
    @DeleteMapping("/{configKey}")
    public Result<String> deleteConfig(@PathVariable String configKey) {
        log.info("删除系统配置: {}", configKey);
        return systemConfigService.deleteConfig(configKey);
    }
    
    /**
     * 刷新配置缓存
     *
     * @return 刷新结果
     */
    @PostMapping("/refresh")
    public Result<String> refreshConfigCache() {
        log.info("刷新配置缓存");
        return systemConfigService.refreshConfigCache();
    }
}
