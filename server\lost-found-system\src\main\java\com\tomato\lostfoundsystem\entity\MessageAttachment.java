package com.tomato.lostfoundsystem.entity;

import com.tomato.lostfoundsystem.enums.FileType;
import lombok.Data;

/**
 * 消息附件实体类
 *
 * 注意：当前数据库表中没有 duration 字段，所以这里使用 transient 标记为瞬态字段
 * 未来可以考虑添加 duration 字段到数据库表：
 * ALTER TABLE message_attachments ADD COLUMN duration INT DEFAULT 0 COMMENT '媒体时长（秒），用于音频和视频';
 */
@Data
public class MessageAttachment {
    private Long id;          // 附件ID
    private Long messageId;   // 消息ID
    private String fileUrl;   // 文件URL
    private FileType fileType;  // 文件类型（IMAGE, AUDIO, VIDEO等）
    private Long fileSize;    // 文件大小

    // 瞬态字段，不会被持久化到数据库
    // 媒体时长目前存储在 chat_messages 表的 audio_duration 或 video_duration 字段中
    private transient Integer duration; // 媒体时长（秒），用于音频和视频
}
