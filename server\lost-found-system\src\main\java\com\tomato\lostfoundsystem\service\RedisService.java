package com.tomato.lostfoundsystem.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface RedisService {

    // 存储用户的 WebSocket 会话信息
    void storeSession(Long userId, String sessionId);

    // 添加用户的会话信息（单设备登录，会断开旧会话）
    void addUserSession(Long userId, String sessionId, String deviceInfo);

    // 获取用户的 WebSocket 会话信息
    String getSession(Long userId);

    // 获取用户的会话ID（单设备登录）
    Set<String> getUserSessions(Long userId);

    // 获取用户的活跃会话数量（单设备登录下应为0或1）
    int countUserSessions(Long userId);

    // 判断用户是否在线
    boolean isUserOnline(Long userId);

    // 检查特定会话是否在线
    boolean isSessionActive(String sessionId);

    // 更新用户会话的心跳时间
    void updateHeartbeat(Long userId, String sessionId);

    // 存储用户已读的消息
    void storeReadMessage(Long userId, Long messageId);

    // 判断用户是否已读某条消息
    boolean hasReadMessage(Long userId, Long messageId);

    // 设置用户与联系人之间的未读消息计数
    void setUnreadCount(Long userId, Long contactId, int count);

    // 获取用户与联系人之间的未读消息计数
    int getUnreadCount(Long userId, Long contactId);

    // 增加用户与联系人之间的未读消息计数
    int incrementUnreadCount(Long userId, Long contactId);

    // 重置用户与联系人之间的未读消息计数
    void resetUnreadCount(Long userId, Long contactId);

    // 存储最后一条聊天信息
    void storeLastMessage(Long userId, Long contactId, Long messageId, String content,
                         String messageType, Long timestamp, String fileUrl);

    // 获取最后一条聊天信息
    Map<String, String> getLastMessage(Long userId, Long contactId);

    // 批量删除缓存，支持按照模式删除
    void deleteByPattern(String pattern);

    // 移除特定的用户会话
    void removeUserSession(Long userId, String sessionId);

    // 清理过期的会话
    void cleanExpiredSessions();

    // 获取所有用户ID
    Set<Long> getAllUserIds();

    // 标记会话为临时离线状态
    void markSessionAsTemporaryOffline(Long userId, String sessionId);

    // 检查会话是否处于临时离线状态
    boolean isSessionTemporaryOffline(String sessionId);

    // 获取用户最后活跃时间
    long getLastActiveTime(Long userId);

    // 根据模式获取键
    Set<String> getKeysByPattern(String pattern);

    // 设置键值对，带过期时间（秒）
    void set(String key, String value, long expireSeconds);

    // 检查键是否存在
    boolean hasKey(String key);
}

