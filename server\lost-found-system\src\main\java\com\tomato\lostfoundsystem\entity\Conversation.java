package com.tomato.lostfoundsystem.entity;

import lombok.Data;

import java.util.Date;


@Data
public class Conversation {
    private Long id;                  // 会话ID
    private Long user1Id;             // 用户1ID
    private Long user2Id;             // 用户2ID
    private Long lastMessageId;       // 最后一条消息ID
    private String lastMessageContent; // 最后一条消息内容摘要
    private String lastMessageType;   // 最后一条消息类型
    private Date lastMessageTime;     // 最后一条消息的时间
    private Integer unreadCount;      // 未读消息计数
    private String status;            // 会话状态：ACTIVE, ARCHIVED, DELETED
    private Boolean isPinned;         // 是否置顶
    private Boolean isMuted;          // 是否静音
    private Date createdAt;           // 创建时间
    private Date updatedAt;           // 更新时间
}

