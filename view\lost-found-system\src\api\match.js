import request from '@/utils/request'
import axios from 'axios'

// 创建一个axios实例，专门用于智能匹配API
const matchRequest = axios.create({
  baseURL: '/api', // 添加/api前缀
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': 'any-value'
  }
})

// 复制请求拦截器
matchRequest.interceptors.request.use(
  config => {
    // 添加请求ID，用于跟踪请求-响应对
    config.requestId = Date.now() + Math.random().toString(36).substring(2, 9)

    console.log(`智能匹配请求: ${config.method.toUpperCase()} ${config.url}`)
    if (config.data) {
      console.log('请求数据类型:', config.data instanceof FormData ? 'FormData' : typeof config.data)
    }

    // 添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }

    return config
  },
  error => {
    console.error('请求错误：', error)
    return Promise.reject(error)
  }
)

// 复制响应拦截器
matchRequest.interceptors.response.use(
  response => {
    console.log(`智能匹配响应: ${response.config.method.toUpperCase()} ${response.config.url} - ${response.status}`)

    const res = response.data

    // 如果响应成功，直接返回数据
    if (res && res.code === 200) {
      return res
    }

    return res
  },
  error => {
    console.error('智能匹配响应错误:', error)
    return Promise.reject(error)
  }
)

/**
 * 使用图片进行智能匹配
 * @param {FormData} data 包含图片和匹配类型的表单数据
 * @returns {Promise} 匹配结果
 */
export function matchByImage(data) {
  return matchRequest({
    url: '/match/image',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 使用文本描述进行智能匹配
 * @param {Object} data 包含文本描述和匹配类型的数据
 * @returns {Promise} 匹配结果
 */
export function matchByText(data) {
  return matchRequest({
    url: '/match/text',
    method: 'post',
    data
  })
}

/**
 * 使用图片和文本混合进行智能匹配
 * @param {FormData} data 包含图片、文本描述和匹配类型的表单数据
 * @returns {Promise} 匹配结果
 */
export function matchByMixed(data) {
  return matchRequest({
    url: '/match/mixed',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取用户的匹配历史记录
 * @returns {Promise} 匹配历史记录
 */
export function getMatchHistory() {
  return matchRequest({
    url: '/match/history',
    method: 'get'
  })
}

/**
 * 确认匹配结果
 * @param {Object} data 包含匹配ID和确认状态的数据
 * @returns {Promise} 确认结果
 */
export function confirmMatch(data) {
  return matchRequest({
    url: '/match/confirm',
    method: 'post',
    data
  })
}

/**
 * 触发物品匹配
 * @param {number} itemId 物品ID
 * @param {string} itemType 物品类型（LOST/FOUND）
 * @returns {Promise} 匹配结果
 */
export function triggerItemMatch(itemId, itemType) {
  return matchRequest({
    url: '/match/trigger',
    method: 'post',
    params: { itemId, itemType }
  })
}

/**
 * 获取匹配历史详情
 * @param {number} historyId 匹配历史ID
 * @returns {Promise} 匹配历史详情
 */
export function getMatchHistoryDetail(historyId) {
  return matchRequest({
    url: `/match/history/${historyId}`,
    method: 'get'
  })
}
