<template>
  <div class="match-statistics">
    <el-card class="statistics-card">
      <template #header>
        <div class="card-header">
          <span>智能匹配统计</span>
          <el-button type="primary" size="small" @click="refreshData">刷新数据</el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6" v-for="(item, index) in summaryData" :key="index">
          <div class="stat-item" :class="item.class">
            <div class="stat-icon">
              <el-icon><component :is="item.icon" /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ item.value }}</div>
              <div class="stat-label">{{ item.label }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    
    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <el-card class="statistics-card">
          <template #header>
            <div class="card-header">
              <span>匹配成功率</span>
            </div>
          </template>
          <div class="chart-container" ref="successRateChartRef"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="statistics-card">
          <template #header>
            <div class="card-header">
              <span>匹配类型分布</span>
            </div>
          </template>
          <div class="chart-container" ref="matchTypeChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-card class="statistics-card">
      <template #header>
        <div class="card-header">
          <span>匹配趋势</span>
          <el-radio-group v-model="trendType" size="small" @change="updateTrendChart">
            <el-radio-button label="daily">日趋势</el-radio-button>
            <el-radio-button label="weekly">周趋势</el-radio-button>
            <el-radio-button label="monthly">月趋势</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <div class="chart-container" ref="trendChartRef"></div>
    </el-card>
    
    <el-card class="statistics-card">
      <template #header>
        <div class="card-header">
          <span>物品类别匹配分布</span>
          <el-select v-model="selectedCategory" placeholder="选择类别" @change="updateCategoryChart">
            <el-option label="全部类别" value="all" />
            <el-option v-for="category in categories" :key="category" :label="category" :value="category" />
          </el-select>
        </div>
      </template>
      <div class="chart-container" ref="categoryChartRef"></div>
    </el-card>
    
    <el-card class="statistics-card">
      <template #header>
        <div class="card-header">
          <span>最近匹配记录</span>
        </div>
      </template>
      <el-table :data="recentMatches" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="queryType" label="查询类型">
          <template #default="scope">
            <el-tag :type="scope.row.queryType === 'IMAGE' ? 'success' : scope.row.queryType === 'TEXT' ? 'warning' : 'info'">
              {{ getQueryTypeText(scope.row.queryType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="itemType" label="物品类型">
          <template #default="scope">
            <el-tag :type="scope.row.itemType === 'LOST' ? 'danger' : 'success'">
              {{ scope.row.itemType === 'LOST' ? '失物' : '拾物' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resultCount" label="结果数量" />
        <el-table-column prop="maxSimilarity" label="最高相似度">
          <template #default="scope">
            {{ (scope.row.maxSimilarity * 100).toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="匹配时间" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'SUCCESS' ? 'success' : 'info'">
              {{ scope.row.status === 'SUCCESS' ? '成功' : '未成功' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Search, PieChart, DataLine, Histogram } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { generateStatisticsData, generateMatchResults } from '@/utils/mockData'

// 状态变量
const loading = ref(false)
const trendType = ref('daily')
const selectedCategory = ref('all')
const categories = ['电子设备', '证件卡片', '生活用品', '学习用品', '服装配饰', '钱包钥匙', '图书资料', '其他']

// 图表引用
const successRateChartRef = ref(null)
const matchTypeChartRef = ref(null)
const trendChartRef = ref(null)
const categoryChartRef = ref(null)

// 图表实例
let successRateChart = null
let matchTypeChart = null
let trendChart = null
let categoryChart = null

// 数据
const statisticsData = ref({})
const recentMatches = ref([])

// 计算属性
const summaryData = ref([])

// 获取查询类型文本
const getQueryTypeText = (type) => {
  switch (type) {
    case 'IMAGE': return '图像查询'
    case 'TEXT': return '文本查询'
    case 'HYBRID': return '混合查询'
    default: return '未知类型'
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  
  try {
    // 模拟API请求
    setTimeout(() => {
      // 获取统计数据
      statisticsData.value = generateStatisticsData()
      
      // 更新摘要数据
      updateSummaryData()
      
      // 生成最近匹配记录
      generateRecentMatches()
      
      // 初始化图表
      initCharts()
      
      loading.value = false
    }, 1000)
  } catch (error) {
    console.error('获取数据失败:', error)
    loading.value = false
  }
}

// 更新摘要数据
const updateSummaryData = () => {
  const data = statisticsData.value
  
  summaryData.value = [
    {
      label: '总匹配次数',
      value: data.matchSuccessRate.totalMatches,
      icon: 'Search',
      class: 'blue'
    },
    {
      label: '成功匹配次数',
      value: data.matchSuccessRate.successMatches,
      icon: 'PieChart',
      class: 'green'
    },
    {
      label: '匹配成功率',
      value: data.matchSuccessRate.rate + '%',
      icon: 'DataLine',
      class: 'orange'
    },
    {
      label: '平均相似度',
      value: (Math.random() * 20 + 60).toFixed(2) + '%',
      icon: 'Histogram',
      class: 'purple'
    }
  ]
}

// 生成最近匹配记录
const generateRecentMatches = () => {
  recentMatches.value = []
  
  for (let i = 0; i < 10; i++) {
    const queryTypes = ['IMAGE', 'TEXT', 'HYBRID']
    const itemTypes = ['LOST', 'FOUND']
    const statuses = ['SUCCESS', 'PENDING']
    
    recentMatches.value.push({
      id: 10000 + i,
      queryType: queryTypes[Math.floor(Math.random() * queryTypes.length)],
      itemType: itemTypes[Math.floor(Math.random() * itemTypes.length)],
      resultCount: Math.floor(Math.random() * 10) + 1,
      maxSimilarity: Math.random() * 0.3 + 0.7,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10) * 24 * 60 * 60 * 1000).toLocaleString(),
      status: statuses[Math.floor(Math.random() * statuses.length)]
    })
  }
}

// 初始化图表
const initCharts = () => {
  initSuccessRateChart()
  initMatchTypeChart()
  updateTrendChart()
  updateCategoryChart()
}

// 初始化成功率图表
const initSuccessRateChart = () => {
  if (!successRateChartRef.value) return
  
  if (!successRateChart) {
    successRateChart = echarts.init(successRateChartRef.value)
  }
  
  const option = {
    series: [
      {
        type: 'gauge',
        startAngle: 90,
        endAngle: -270,
        pointer: {
          show: false
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#67C23A'
                },
                {
                  offset: 1,
                  color: '#E6A23C'
                }
              ]
            }
          }
        },
        axisLine: {
          lineStyle: {
            width: 20
          }
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        title: {
          fontSize: 14
        },
        detail: {
          width: 50,
          height: 14,
          fontSize: 28,
          color: '#303133',
          formatter: '{value}%'
        },
        data: [
          {
            value: parseFloat(statisticsData.value.matchSuccessRate.rate),
            name: '匹配成功率'
          }
        ]
      }
    ]
  }
  
  successRateChart.setOption(option)
}

// 初始化匹配类型图表
const initMatchTypeChart = () => {
  if (!matchTypeChartRef.value) return
  
  if (!matchTypeChart) {
    matchTypeChart = echarts.init(matchTypeChartRef.value)
  }
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: ['图像匹配', '文本匹配', '混合匹配']
    },
    series: [
      {
        name: '匹配类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: Math.floor(Math.random() * 200) + 100, name: '图像匹配' },
          { value: Math.floor(Math.random() * 150) + 50, name: '文本匹配' },
          { value: Math.floor(Math.random() * 100) + 30, name: '混合匹配' }
        ]
      }
    ]
  }
  
  matchTypeChart.setOption(option)
}

// 更新趋势图表
const updateTrendChart = () => {
  if (!trendChartRef.value) return
  
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value)
  }
  
  // 生成日期数据
  const dates = []
  const matchCounts = []
  const successCounts = []
  
  const now = new Date()
  const count = trendType.value === 'daily' ? 30 : trendType.value === 'weekly' ? 12 : 6
  const step = trendType.value === 'daily' ? 1 : trendType.value === 'weekly' ? 7 : 30
  
  for (let i = count - 1; i >= 0; i--) {
    const date = new Date(now.getTime() - i * step * 24 * 60 * 60 * 1000)
    const dateStr = trendType.value === 'daily' 
      ? `${date.getMonth() + 1}/${date.getDate()}`
      : trendType.value === 'weekly'
        ? `第${date.getWeek()}周`
        : `${date.getMonth() + 1}月`
    
    dates.push(dateStr)
    matchCounts.push(Math.floor(Math.random() * 50) + 10)
    successCounts.push(Math.floor(Math.random() * 30) + 5)
  }
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['匹配次数', '成功次数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '匹配次数',
        type: 'line',
        data: matchCounts,
        smooth: true
      },
      {
        name: '成功次数',
        type: 'line',
        data: successCounts,
        smooth: true
      }
    ]
  }
  
  trendChart.setOption(option)
}

// 更新类别图表
const updateCategoryChart = () => {
  if (!categoryChartRef.value) return
  
  if (!categoryChart) {
    categoryChart = echarts.init(categoryChartRef.value)
  }
  
  // 生成类别数据
  const categoryData = []
  
  if (selectedCategory.value === 'all') {
    // 所有类别的匹配分布
    categories.forEach(category => {
      categoryData.push({
        name: category,
        lostCount: Math.floor(Math.random() * 30) + 5,
        foundCount: Math.floor(Math.random() * 20) + 5,
        successCount: Math.floor(Math.random() * 15) + 3
      })
    })
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['失物匹配', '拾物匹配', '成功匹配']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: categoryData.map(item => item.name)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '失物匹配',
          type: 'bar',
          data: categoryData.map(item => item.lostCount)
        },
        {
          name: '拾物匹配',
          type: 'bar',
          data: categoryData.map(item => item.foundCount)
        },
        {
          name: '成功匹配',
          type: 'bar',
          data: categoryData.map(item => item.successCount)
        }
      ]
    }
    
    categoryChart.setOption(option)
  } else {
    // 单个类别的详细数据
    const timeData = []
    const lostData = []
    const foundData = []
    const successData = []
    
    for (let i = 0; i < 12; i++) {
      const month = i + 1
      timeData.push(`${month}月`)
      lostData.push(Math.floor(Math.random() * 20) + 3)
      foundData.push(Math.floor(Math.random() * 15) + 2)
      successData.push(Math.floor(Math.random() * 10) + 1)
    }
    
    const option = {
      title: {
        text: `${selectedCategory.value}类别匹配趋势`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['失物匹配', '拾物匹配', '成功匹配'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: timeData
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '失物匹配',
          type: 'line',
          data: lostData,
          smooth: true
        },
        {
          name: '拾物匹配',
          type: 'line',
          data: foundData,
          smooth: true
        },
        {
          name: '成功匹配',
          type: 'line',
          data: successData,
          smooth: true
        }
      ]
    }
    
    categoryChart.setOption(option)
  }
}

// 扩展Date原型，添加获取周数方法
Date.prototype.getWeek = function() {
  const firstDay = new Date(this.getFullYear(), 0, 1)
  return Math.ceil((((this - firstDay) / 86400000) + firstDay.getDay() + 1) / 7)
}

// 窗口大小变化时重绘图表
const handleResize = () => {
  successRateChart?.resize()
  matchTypeChart?.resize()
  trendChart?.resize()
  categoryChart?.resize()
}

// 组件挂载
onMounted(() => {
  // 加载数据
  refreshData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表实例
  successRateChart?.dispose()
  matchTypeChart?.dispose()
  trendChart?.dispose()
  categoryChart?.dispose()
})
</script>

<style scoped>
.match-statistics {
  padding: 20px;
}

.statistics-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  background-color: #f5f7fa;
  height: 100px;
  transition: all 0.3s;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-item.blue {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.stat-item.green {
  background: linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%);
}

.stat-item.orange {
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
}

.stat-item.purple {
  background: linear-gradient(135deg, #f9f0ff 0%, #d3adf7 100%);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  width: 100%;
}
</style>
