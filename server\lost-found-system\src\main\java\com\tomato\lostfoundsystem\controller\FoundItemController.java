
package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.annotation.RequireToken;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.FoundItemDTO;
import com.tomato.lostfoundsystem.entity.FoundItem;
import com.tomato.lostfoundsystem.service.FoundItemService;
import com.tomato.lostfoundsystem.service.ItemImageService;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping("/api/found-items")
public class FoundItemController {

    @Autowired
    private FoundItemService foundItemService;
    @Autowired
    private ItemImageService itemImageService;

    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @PostMapping(value = "/publish", consumes = "multipart/form-data")
    public Result<Object> publishFoundItem(
            @RequestParam("itemName") String itemName,
            @RequestParam("description") String description,
            @RequestParam("foundTime") String foundTime,
            @RequestParam("foundLocation") String foundLocation,
            @RequestParam(value = "image", required = false) MultipartFile image,
            @RequestParam(value = "images", required = false) List<MultipartFile> images,
            @RequestParam(value = "mainImageIndex", required = false, defaultValue = "0") Integer mainImageIndex,
            HttpServletRequest request) {

        log.info("收到发布拾物信息请求 - 物品名称: {}, 描述: {}, 时间: {}, 地点: {}", itemName, description, foundTime, foundLocation);
        log.info("图片数量: 单图={}, 多图={}, 主图索引={}", (image != null ? "有" : "无"), (images != null ? images.size() : 0), mainImageIndex);

        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.fail("用户未登录或登录已过期");
        }

        // 验证描述长度
        if (description.length() < 20) {
            return Result.fail("物品描述长度必须在20-500个字符之间，请提供详细描述，包括物品特征、颜色、品牌等信息");
        }

        // 验证图片必填
        if ((image == null || image.isEmpty()) && (images == null || images.isEmpty())) {
            return Result.fail("请至少上传一张拾物照片");
        }

        FoundItemDTO dto = new FoundItemDTO();
        dto.setUserId(userId);
        dto.setItemName(itemName);
        dto.setDescription(description);
        dto.setFoundTime(foundTime);
        dto.setFoundLocation(foundLocation);
        dto.setImage(image);
        dto.setImages(images);
        dto.setMainImageIndex(mainImageIndex);

        return foundItemService.publishFoundItem(dto);
    }

    @GetMapping("/search")
    public Result<Object> searchFoundItems(
            @RequestParam(required = false) String keyword,  // 关键字
            @RequestParam(required = false) String foundLocation,  // 拾取地点
            @RequestParam(required = false) String status,  // 状态
            @RequestParam(required = false) String timeRange,  // 时间范围（今天、昨天、三天内、一周内、一个月内）
            @RequestParam(required = false) String timeFilterType,  // 筛选时间类型（拾取时间或发布时间）
            @RequestParam(required = false) String startDate,  // 自定义开始时间
            @RequestParam(required = false) String endDate,  // 自定义结束时间
            @RequestParam(defaultValue = "1") int page,  // 页码
            @RequestParam(defaultValue = "12") int size) {  // 每页条数

        // 默认展示过去一周内丢失的物品
        if (timeRange == null || timeRange.isEmpty()) {
            timeRange = "lastWeek";  // 默认展示过去一周内丢失的物品
        }
        log.info("查询的参数{},{},{},{},{},{},{},{},{}",keyword,foundLocation,status,timeRange,timeFilterType,startDate,endDate,page,size);
        log.info("page:{},size:{}",page,size);

        return foundItemService.searchFoundItems(keyword, foundLocation, status, timeRange, timeFilterType, startDate, endDate, page, size);
    }

    /**
     * 获取拾物详情
     */
    @GetMapping("/detail/{id}")
    public Result<Object> getFoundItemDetails(@PathVariable Long id) {
        // 调用服务层方法获取拾物详情
        return foundItemService.getFoundItemDetails(id);
    }


    /**
     * 更新拾物信息
     */
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @PutMapping(value = "/update/{id}", consumes = "multipart/form-data")
    public Result<Object> updateFoundItem(
            @PathVariable("id") Long id,
            @RequestParam("itemName") String itemName,
            @RequestParam("description") String description,
            @RequestParam("foundTime") String foundTime,
            @RequestParam("foundLocation") String foundLocation,
            @RequestParam(value = "image", required = false) MultipartFile image,
            @RequestParam(value = "images", required = false) List<MultipartFile> images,
            @RequestParam(value = "mainImageIndex", required = false, defaultValue = "0") Integer mainImageIndex,
            @RequestParam(value = "keepExistingImages", required = false, defaultValue = "false") String keepExistingImages,
            @RequestParam(value = "mainImageChanged", required = false, defaultValue = "false") String mainImageChanged,
            HttpServletRequest request) {

        log.info("更新拾物信息请求 - ID: {}, 物品名称: {}, 时间: {}, 地点: {}", id, itemName, foundTime, foundLocation);
        log.info("图片数量: 单图={}, 多图={}, 主图索引={}, 保留原有图片={}, 主图索引是否变化={}",
                (image != null ? "有" : "无"),
                (images != null ? images.size() : 0),
                mainImageIndex,
                "true".equalsIgnoreCase(keepExistingImages) ? "是" : "否",
                "true".equalsIgnoreCase(mainImageChanged) ? "是" : "否");

        // 构建 FoundItemDTO
        FoundItemDTO foundItemDTO = new FoundItemDTO();
        foundItemDTO.setItemName(itemName);
        foundItemDTO.setDescription(description);
        foundItemDTO.setFoundTime(foundTime);
        foundItemDTO.setFoundLocation(foundLocation);
        foundItemDTO.setImage(image);
        foundItemDTO.setImages(images);
        foundItemDTO.setMainImageIndex(mainImageIndex);
        foundItemDTO.setKeepExistingImages("true".equalsIgnoreCase(keepExistingImages));
        foundItemDTO.setMainImageChanged("true".equalsIgnoreCase(mainImageChanged));

        // 调用服务层方法更新拾物信息
        return foundItemService.updateFoundItem(id, foundItemDTO);
    }


    /**
     * 删除拾物信息
     */
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @DeleteMapping("/delete/{id}")
    public Result<Object> deleteFoundItem(@PathVariable Long id, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.fail("用户未登录或登录已过期");
        }

        return foundItemService.deleteFoundItem(id, userId);
    }

    /**
     * 获取我的发布拾物信息
     */
    @Autowired
    private SecurityUtil securityUtil;
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @GetMapping("/my-published/found")
    public Result<Object> getMyPublishedFoundItems() {
        Long userId = securityUtil.getCurrentUserId();
        if (userId == null) {
            return Result.fail("用户未登录或登录已过期");
        }
        return foundItemService.getMyFoundItems(userId);
    }

    /**
     * 更新拾物状态（未认领/已认领）
     */
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @PutMapping("/{id}/status")
    public Result<Object> updateFoundItemStatus(@PathVariable Long id, @RequestBody Map<String, String> statusMap) {
        // 获取当前用户ID
        Long userId = securityUtil.getCurrentUserId();
        if (userId == null) {
            return Result.fail("用户未登录或登录已过期");
        }

        // 获取状态参数
        String status = statusMap.get("status");
        if (status == null || (!status.equals("UNCLAIMED") && !status.equals("RETURNED"))) {
            return Result.fail("无效的状态值，必须为 UNCLAIMED 或 RETURNED");
        }

        return foundItemService.updateFoundItemStatus(id, status, userId);
    }

    /**
     * 认领拾物
     */
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @PostMapping("/claim/{id}")
    public Result<Object> claimFoundItem(@PathVariable Long id, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.fail("用户未登录或登录已过期");
        }

        log.info("用户 {} 尝试认领拾物 {}", userId, id);
        return foundItemService.claimFoundItem(id, userId);
    }

    /**
     * 更新拾物图片
     */
    @PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    @PostMapping("/update-images/{id}")
    public Result<Object> updateFoundItemImages(
            @PathVariable Long id,
            @RequestParam(required = false) MultipartFile mainImage,
            @RequestParam(required = false) List<MultipartFile> additionalImages,
            @RequestParam(required = false) List<Long> retainImageIds,
            HttpServletRequest request) {

        // 获取当前用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.fail("用户未登录或登录已过期");
        }

        // 验证用户是否有权限更新该拾物信息
        FoundItem foundItem = foundItemService.getFoundItemById(id);
        if (foundItem == null) {
            return Result.fail("拾物信息不存在");
        }

        if (!foundItem.getUserId().equals(userId)) {
            return Result.fail("您无权更新此拾物信息");
        }

        // 调用服务更新图片
        return itemImageService.updateItemImages(id, "FOUND", mainImage, additionalImages, retainImageIds);
    }
}
