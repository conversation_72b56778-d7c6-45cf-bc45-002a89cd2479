package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.LostItemDTO;
import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.mapper.ItemAuditMapper;
import com.tomato.lostfoundsystem.mapper.LostItemMapper;
import com.tomato.lostfoundsystem.service.Impl.LostItemServiceImpl;
import com.tomato.lostfoundsystem.utils.AliyunOSSUtil;
import com.tomato.lostfoundsystem.utils.RedisUtil;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class LostItemServiceImplTest {

    @Mock
    private LostItemMapper lostItemMapper;

    @Mock
    private AliyunOSSUtil aliyunOSSUtil;

    @Mock
    private ItemAuditMapper itemAuditMapper;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private SecurityUtil securityUtil;

    @Mock
    private AutoMatchNotificationService autoMatchNotificationService;

    @Mock
    private ItemImageService itemImageService;

    @InjectMocks
    private LostItemServiceImpl lostItemService;

    private LostItemDTO validLostItemDTO;
    private LostItemDTO invalidLostItemDTO;
    private MockMultipartFile mockImage;
    private List<MultipartFile> mockImages;

    @BeforeEach
    void setUp() {
        // 初始化有效的LostItemDTO
        validLostItemDTO = new LostItemDTO();
        validLostItemDTO.setUserId(1L);
        validLostItemDTO.setItemName("测试物品");
        validLostItemDTO.setDescription("这是一个详细的物品描述，包含了物品的颜色、形状和特征，长度超过20个字符。");
        validLostItemDTO.setLostTime("2023-05-01T10:00:00");
        validLostItemDTO.setLostLocation("图书馆");

        // 初始化无效的LostItemDTO（描述太短）
        invalidLostItemDTO = new LostItemDTO();
        invalidLostItemDTO.setUserId(1L);
        invalidLostItemDTO.setItemName("测试物品");
        invalidLostItemDTO.setDescription("描述太短");
        invalidLostItemDTO.setLostTime("2023-05-01T10:00:00");
        invalidLostItemDTO.setLostLocation("图书馆");

        // 创建模拟图片文件
        mockImage = new MockMultipartFile(
                "image",
                "test.jpg",
                "image/jpeg",
                "test image content".getBytes()
        );

        // 创建模拟图片列表
        mockImages = new ArrayList<>();
        mockImages.add(mockImage);
    }

    @Test
    void publishLostItem_WithValidData_ShouldSucceed() throws Exception {
        // 准备测试数据
        validLostItemDTO.setImage(mockImage);

        // 模拟AliyunOSSUtil.uploadLostImage方法
        try (var mockedStatic = mockStatic(AliyunOSSUtil.class)) {
            mockedStatic.when(() -> AliyunOSSUtil.uploadLostImage(any(MultipartFile.class)))
                    .thenReturn("http://example.com/test.jpg");

            // 模拟LostItemMapper.insertLostItem方法
            when(lostItemMapper.insertLostItem(any(LostItem.class))).thenReturn(1);

            // 执行测试
            Result<Object> result = lostItemService.publishLostItem(validLostItemDTO);

            // 验证结果
            assertEquals(200, result.getCode());
            assertTrue(result.getMessage().contains("发布成功"));

            // 验证方法调用
            verify(lostItemMapper, times(1)).insertLostItem(any(LostItem.class));
        }
    }

    @Test
    void publishLostItem_WithoutImage_ShouldUseDefaultImage() throws Exception {
        // 准备测试数据 - 不包含图片

        // 模拟LostItemMapper.insertLostItem方法
        when(lostItemMapper.insertLostItem(any(LostItem.class))).thenReturn(1);

        // 执行测试
        Result<Object> result = lostItemService.publishLostItem(validLostItemDTO);

        // 验证结果
        assertEquals(200, result.getCode());
        assertTrue(result.getMessage().contains("发布成功"));

        // 验证方法调用
        verify(lostItemMapper, times(1)).insertLostItem(argThat(lostItem ->
            lostItem.getImageUrl().equals("/default/no-image-available.png")
        ));
    }

    @Test
    void publishLostItem_WithMultipleImages_ShouldSaveAdditionalImages() throws Exception {
        // 准备测试数据
        validLostItemDTO.setImages(mockImages);

        // 模拟AliyunOSSUtil.uploadLostImage方法
        try (var mockedStatic = mockStatic(AliyunOSSUtil.class)) {
            mockedStatic.when(() -> AliyunOSSUtil.uploadLostImage(any(MultipartFile.class)))
                    .thenReturn("http://example.com/test.jpg");

            // 模拟LostItemMapper.insertLostItem方法
            when(lostItemMapper.insertLostItem(any(LostItem.class))).thenAnswer(invocation -> {
                LostItem lostItem = invocation.getArgument(0);
                lostItem.setId(1L); // 设置ID，模拟数据库自增
                return 1;
            });

            // 执行测试
            Result<Object> result = lostItemService.publishLostItem(validLostItemDTO);

            // 验证结果
            assertEquals(200, result.getCode());
            assertTrue(result.getMessage().contains("发布成功"));

            // 验证方法调用
            verify(lostItemMapper, times(1)).insertLostItem(any(LostItem.class));
            // 由于只有一张图片，它会被用作主图，不会调用saveItemImages
            verify(itemImageService, never()).saveItemImages(anyLong(), anyString(), anyList());
        }
    }

    @Test
    void publishLostItem_WhenImageUploadFails_ShouldReturnError() throws Exception {
        // 准备测试数据
        validLostItemDTO.setImage(mockImage);

        // 模拟AliyunOSSUtil.uploadLostImage方法抛出异常
        try (var mockedStatic = mockStatic(AliyunOSSUtil.class)) {
            mockedStatic.when(() -> AliyunOSSUtil.uploadLostImage(any(MultipartFile.class)))
                    .thenThrow(new RuntimeException("Upload failed"));

            // 执行测试
            Result<Object> result = lostItemService.publishLostItem(validLostItemDTO);

            // 验证结果
            assertEquals(400, result.getCode());
            assertTrue(result.getMessage().contains("图片上传失败"));

            // 验证方法调用
            verify(lostItemMapper, never()).insertLostItem(any(LostItem.class));
        }
    }

    @Test
    void publishLostItem_WhenDatabaseInsertFails_ShouldReturnError() throws Exception {
        // 准备测试数据
        validLostItemDTO.setImage(mockImage);

        // 模拟AliyunOSSUtil.uploadLostImage方法
        try (var mockedStatic = mockStatic(AliyunOSSUtil.class)) {
            mockedStatic.when(() -> AliyunOSSUtil.uploadLostImage(any(MultipartFile.class)))
                    .thenReturn("http://example.com/test.jpg");

            // 模拟LostItemMapper.insertLostItem方法返回0（插入失败）
            when(lostItemMapper.insertLostItem(any(LostItem.class))).thenReturn(0);

            // 执行测试
            Result<Object> result = lostItemService.publishLostItem(validLostItemDTO);

            // 验证结果
            assertEquals(400, result.getCode());
            assertTrue(result.getMessage().contains("发布失败"));

            // 验证方法调用
            verify(lostItemMapper, times(1)).insertLostItem(any(LostItem.class));
        }
    }
}
