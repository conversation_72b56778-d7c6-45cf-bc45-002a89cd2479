<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomato.lostfoundsystem.mapper.MessageAttachmentMapper">
    <!-- 插入附件信息 -->
    <insert id="insertMessageAttachment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO message_attachments (file_url, file_type, file_size, message_id)
        VALUES (#{fileUrl}, #{fileType}, #{fileSize}, #{messageId})
    </insert>

    <!-- 根据 message_id 查询单个附件（向后兼容） -->
    <select id="getAttachmentByMessageId" resultType="com.tomato.lostfoundsystem.entity.MessageAttachment">
        SELECT * FROM message_attachments
        WHERE message_id = #{messageId}
        LIMIT 1  <!-- 只返回一个附件 -->
    </select>

    <!-- 根据 message_id 查询所有附件 -->
    <select id="getAllAttachmentsByMessageId" resultType="com.tomato.lostfoundsystem.entity.MessageAttachment">
        SELECT * FROM message_attachments
        WHERE message_id = #{messageId}
        ORDER BY id ASC  <!-- 按ID升序排序 -->
    </select>

    <!-- 根据附件ID获取附件 -->
    <select id="getAttachmentById" resultType="com.tomato.lostfoundsystem.entity.MessageAttachment">
        SELECT * FROM message_attachments
        WHERE id = #{id}
    </select>

    <!-- 更新附件信息 -->
    <update id="updateAttachment">
        UPDATE message_attachments
        <set>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="messageId != null">message_id = #{messageId},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 更新附件的文件URL -->
    <update id="updateAttachmentFileUrl">
        UPDATE message_attachments
        SET file_url = #{fileUrl}
        WHERE id = #{id}
    </update>
</mapper>
