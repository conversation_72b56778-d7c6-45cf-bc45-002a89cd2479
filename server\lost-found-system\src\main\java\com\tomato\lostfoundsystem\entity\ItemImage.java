package com.tomato.lostfoundsystem.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 物品图片实体类
 * 用于存储失物和拾物的多张图片
 */
@Data
public class ItemImage {
    /**
     * 图片ID
     */
    private Long id;
    
    /**
     * 物品ID
     */
    private Long itemId;
    
    /**
     * 物品类型（LOST/FOUND）
     */
    private String itemType;
    
    /**
     * 图片URL
     */
    private String imageUrl;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
