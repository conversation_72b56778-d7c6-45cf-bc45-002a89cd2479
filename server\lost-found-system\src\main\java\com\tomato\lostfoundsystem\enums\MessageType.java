package com.tomato.lostfoundsystem.enums;
public enum MessageType {
    TEXT(0),        // 文本消息
    IMAGE(1),       // 图片消息
    AUDIO(2),       // 音频消息
    VIDEO(3),       // 视频消息
    DOCUMENT(4);    // 文档消息

    private final int value;

    // 枚举构造器
    MessageType(int value) {
        this.value = value;
    }

    // 获取枚举值
    public int getValue() {
        return value;
    }

    // 根据值获取枚举类型
    public static MessageType fromValue(int value) {
        for (MessageType type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid message type: " + value);
    }

    // 获取附件文件类型
    public FileType getFileType() {
        switch (this) {
            case IMAGE:
                return FileType.IMAGE;
            case AUDIO:
                return FileType.AUDIO;
            case VIDEO:
                return FileType.VIDEO;
            case DOCUMENT:
                return FileType.DOCUMENT;
            default:
                throw new IllegalArgumentException("Invalid message type for file type: " + this);
        }
    }

    @Override
    public String toString() {
        return this.name();  // 返回枚举名称（TEXT, IMAGE等）
    }
}
