package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.MatchHistory;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 智能匹配服务接口
 */
public interface IntelligentMatchService {

    /**
     * 使用图片进行智能匹配
     *
     * @param userId 用户ID
     * @param imageFile 图片文件
     * @param itemType 物品类型（LOST表示查找拾物，FOUND表示查找失物）
     * @return 匹配结果
     */
    Result<Map<String, Object>> matchByImage(Long userId, MultipartFile imageFile, String itemType);

    /**
     * 使用文本描述进行智能匹配
     *
     * @param userId 用户ID
     * @param description 文本描述
     * @param itemType 物品类型（LOST表示查找拾物，FOUND表示查找失物）
     * @return 匹配结果
     */
    Result<Map<String, Object>> matchByText(Long userId, String description, String itemType);

    /**
     * 使用图片和文本混合进行智能匹配
     *
     * @param userId 用户ID
     * @param imageFile 图片文件
     * @param description 文本描述
     * @param itemType 物品类型（LOST表示查找拾物，FOUND表示查找失物）
     * @return 匹配结果
     */
    Result<Map<String, Object>> matchByMixed(Long userId, MultipartFile imageFile, String description, String itemType);


    /**
     * 获取用户的匹配历史记录
     *
     * @param userId 用户ID
     * @return 匹配历史记录列表
     */
    Result<List<MatchHistory>> getMatchHistory(Long userId);

    /**
     * 获取匹配历史详情
     *
     * @param matchHistoryId 匹配历史ID
     * @return 匹配历史详情
     */
    Result<MatchHistory> getMatchHistoryDetail(Long matchHistoryId);

    /**
     * 为所有物品生成特征向量
     *
     * @return 处理结果
     */
    Result<String> generateFeatureVectorsForAllItems();

    /**
     * 执行自动匹配
     *
     * @param userId 用户ID
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @return 匹配结果
     */
    Result<Map<String, Object>> performAutoMatch(Long userId, Long itemId, String itemType);
}
