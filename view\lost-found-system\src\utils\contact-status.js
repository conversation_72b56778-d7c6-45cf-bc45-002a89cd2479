/**
 * 联系人状态管理模块 (重构版)
 *
 * 该模块负责：
 * 1. 特定联系人在线状态的订阅和监控
 * 2. 联系人状态变化的通知
 * 3. 联系人状态的缓存管理
 * 4. 批量获取联系人状态
 *
 * 与其他模块的关系：
 * - onlineStatusService.js: 提供统一的在线状态管理服务
 *
 * 使用方式：
 * import { subscribeContactStatus, isContactOnline, getContactsStatus } from '@/utils/contact-status'
 */

import {
  isUserOnline,
  getUserLastActiveTime,
  checkUserOnlineStatus
} from '@/services/onlineStatusService'

// 获取WebSocket客户端
function getWebSocketClient() {
  // 从websocket.js中动态获取stompClient
  if (window.wsClient && window.wsClient.stompClient) {
    return window.wsClient.stompClient
  }
  return null
}

// 存储联系人在线状态
const contactStatus = new Map()

// 存储订阅状态
const subscriptions = new Map()

// 初始化状态
let initialized = false

/**
 * 初始化联系人状态管理
 */
export function initContactStatusManager() {
  if (initialized) {
    return
  }

  const stompClient = getWebSocketClient()
  if (!stompClient || !stompClient.connected) {
    // 监听WebSocket连接成功事件，连接成功后再初始化
    window.addEventListener('websocket-connected', initContactStatusManager, { once: true })
    return
  }

  // 创建防抖函数
  const debounceStatusUpdate = (function() {
    const debounceMap = new Map();

    return function(userId, callback, delay = 500) {
      // 如果已经有相同用户的状态更新在处理中，取消它
      if (debounceMap.has(userId)) {
        clearTimeout(debounceMap.get(userId));
      }

      // 设置新的延迟处理
      debounceMap.set(userId, setTimeout(() => {
        callback();
        debounceMap.delete(userId);
      }, delay));
    };
  })();

  // 监听在线状态变化事件 (新事件)
  window.addEventListener('online-status-changed', (event) => {
    const { userId, isOnline, timestamp } = event.detail

    // 使用防抖处理状态更新
    debounceStatusUpdate(userId, () => {
      // 更新联系人状态
      contactStatus.set(userId, {
        online: isOnline,
        lastActive: timestamp || Date.now()
      })

      // 只有当联系人已被订阅时才触发状态更新事件
      if (subscriptions.has(userId)) {
        console.log(`触发联系人 ${userId} 状态更新事件: ${isOnline ? '在线' : '离线'}`);

        // 触发状态更新事件
        window.dispatchEvent(new CustomEvent('contact-status-updated', {
          detail: {
            contactId: userId,
            status: contactStatus.get(userId)
          }
        }))
      }
    });
  })

  // 监听在线用户列表更新事件
  window.addEventListener('online-users-updated', (event) => {
    const { users, timestamp } = event.detail

    // 更新已订阅的联系人状态
    Array.from(subscriptions.keys()).forEach(contactId => {
      const isOnline = users.includes(contactId.toString())

      // 更新联系人状态
      contactStatus.set(contactId, {
        online: isOnline,
        lastActive: timestamp || Date.now()
      })

      // 触发状态更新事件
      window.dispatchEvent(new CustomEvent('contact-status-updated', {
        detail: {
          contactId,
          status: contactStatus.get(contactId)
        }
      }))
    })
  })

  // 标记为已初始化
  initialized = true

  console.log('联系人状态管理已初始化')
}

/**
 * 订阅联系人在线状态
 * @param {string} contactId 联系人ID
 * @returns {boolean} 是否成功订阅
 */
export function subscribeContactStatus(contactId) {
  if (!contactId) {
    console.warn('联系人ID为空，无法订阅状态')
    return false
  }

  // 确保contactId是字符串
  const contactIdStr = String(contactId)

  // 确保初始化
  if (!initialized) {
    initContactStatusManager()
  }

  // 检查是否已经订阅
  if (subscriptions.has(contactIdStr)) {
    // 已经订阅过，不再重复检查状态
    console.log(`联系人 ${contactIdStr} 已订阅，跳过重复订阅`)
    return true
  }

  const stompClient = getWebSocketClient()
  if (!stompClient || !stompClient.connected) {
    console.log(`WebSocket未连接，使用缓存检查联系人 ${contactIdStr} 状态`)

    // 先检查全局在线状态
    const isOnline = isUserOnline(contactIdStr)

    // 更新联系人状态
    contactStatus.set(contactIdStr, {
      online: isOnline,
      lastActive: Date.now()
    })

    // 记录订阅状态，即使WebSocket未连接
    subscriptions.set(contactIdStr, true)

    // 触发状态更新事件
    window.dispatchEvent(new CustomEvent('contact-status-updated', {
      detail: {
        contactId: contactIdStr,
        status: contactStatus.get(contactIdStr)
      }
    }))

    return true
  }

  try {
    console.log(`订阅联系人 ${contactIdStr} 的在线状态`)

    // 发送订阅请求
    stompClient.publish({
      destination: `/app/subscribeContactStatus/${contactIdStr}`,
      body: JSON.stringify({
        timestamp: Date.now()
      })
    })

    // 记录订阅状态
    subscriptions.set(contactIdStr, true)

    // 使用全局在线状态，不主动发送检查请求
    const isOnline = isUserOnline(contactIdStr)

    // 更新联系人状态
    contactStatus.set(contactIdStr, {
      online: isOnline,
      lastActive: Date.now()
    })

    // 触发状态更新事件
    window.dispatchEvent(new CustomEvent('contact-status-updated', {
      detail: {
        contactId: contactIdStr,
        status: contactStatus.get(contactIdStr)
      }
    }))

    return true
  } catch (error) {
    console.error(`订阅联系人 ${contactIdStr} 的在线状态时出错:`, error)
    return false
  }
}

/**
 * 取消订阅联系人在线状态
 * @param {string} contactId 联系人ID
 * @returns {boolean} 是否成功取消订阅
 */
export function unsubscribeContactStatus(contactId) {
  if (!subscriptions.has(contactId)) {
    return true
  }

  const stompClient = getWebSocketClient()
  if (stompClient && stompClient.connected) {
    try {
      // 发送取消订阅请求
      stompClient.publish({
        destination: `/app/unsubscribeContactStatus/${contactId}`,
        body: JSON.stringify({
          timestamp: Date.now()
        })
      })
    } catch (error) {
      console.error(`发送取消订阅请求时出错:`, error)
    }
  }

  // 移除订阅记录
  subscriptions.delete(contactId)

  return true
}

/**
 * 批量获取联系人在线状态
 * @param {string[]} contactIds 联系人ID数组
 * @returns {Promise<Map<string, object>>} 联系人在线状态Map
 */
export function getContactsStatus(contactIds) {
  return new Promise((resolve) => {
    if (!contactIds || contactIds.length === 0) {
      resolve(new Map())
      return
    }

    console.log(`批量获取 ${contactIds.length} 个联系人的在线状态`)

    // 确保初始化
    if (!initialized) {
      initContactStatusManager()
    }

    // 从服务中获取状态
    const statuses = new Map()

    contactIds.forEach(id => {
      // 确保id是字符串
      const contactIdStr = String(id)

      // 检查在线状态
      const isOnline = isUserOnline(contactIdStr)
      const lastActive = getUserLastActiveTime(contactIdStr) || Date.now()

      // 设置状态
      statuses.set(contactIdStr, {
        online: isOnline,
        lastActive: lastActive
      })

      // 更新本地缓存
      contactStatus.set(contactIdStr, {
        online: isOnline,
        lastActive: lastActive
      })

      // 订阅联系人状态，但不主动检查
      if (!subscriptions.has(contactIdStr)) {
        subscriptions.set(contactIdStr, true)
        console.log(`已订阅联系人 ${contactIdStr} 状态，使用缓存状态`)
      }

      // 触发状态更新事件
      window.dispatchEvent(new CustomEvent('contact-status-updated', {
        detail: {
          contactId: contactIdStr,
          status: contactStatus.get(contactIdStr)
        }
      }))
    })

    console.log(`已获取 ${statuses.size} 个联系人的在线状态`)
    resolve(statuses)
  })
}

/**
 * 检查联系人是否在线
 * @param {string} contactId 联系人ID
 * @returns {boolean} 是否在线
 */
export function isContactOnline(contactId) {
  if (!contactId) {
    return false
  }

  // 确保contactId是字符串
  const contactIdStr = String(contactId)

  // 先检查联系人状态缓存
  const status = contactStatus.get(contactIdStr)
  if (status !== undefined) {
    return Boolean(status.online)
  }

  // 如果缓存中没有，检查全局在线状态
  const isOnline = isUserOnline(contactIdStr)

  // 更新缓存
  contactStatus.set(contactIdStr, {
    online: isOnline,
    lastActive: Date.now()
  })

  return isOnline
}

/**
 * 获取联系人最后活跃时间
 * @param {string} contactId 联系人ID
 * @returns {number|null} 最后活跃时间
 */
export function getContactLastActiveTime(contactId) {
  // 先检查联系人状态缓存
  const status = contactStatus.get(contactId)
  if (status && status.lastActive) {
    return status.lastActive
  }

  // 如果缓存中没有，从服务中获取
  try {
    const lastActive = getUserLastActiveTime(contactId)

    // 如果获取到了最后活跃时间，更新缓存
    if (lastActive) {
      // 如果联系人状态缓存中已有该联系人，更新其最后活跃时间
      if (status) {
        status.lastActive = lastActive
        contactStatus.set(contactId, status)
      } else {
        // 否则创建新的联系人状态
        contactStatus.set(contactId, {
          online: isUserOnline(contactId),
          lastActive: lastActive
        })
      }

      return lastActive
    }
  } catch (error) {
    console.error(`获取联系人 ${contactId} 最后活跃时间失败:`, error)
  }

  // 如果都没有获取到，返回当前时间
  return Date.now()
}

// 监听WebSocket连接成功事件
window.addEventListener('websocket-connected', initContactStatusManager)

// 导出默认对象
export default {
  initContactStatusManager,
  subscribeContactStatus,
  unsubscribeContactStatus,
  getContactsStatus,
  isContactOnline,
  getContactLastActiveTime
}
