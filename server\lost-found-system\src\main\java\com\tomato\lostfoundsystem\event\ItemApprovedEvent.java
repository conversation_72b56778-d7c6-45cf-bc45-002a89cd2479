package com.tomato.lostfoundsystem.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 物品审核通过事件
 * 用于在应用内部传递物品审核通过的消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemApprovedEvent {
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 物品类型（lost/found）
     */
    private String itemType;
    
    /**
     * 物品ID
     */
    private Long itemId;
    
    /**
     * 创建一个物品审核通过事件
     * 
     * @param userId 用户ID
     * @param itemType 物品类型
     * @param itemId 物品ID
     * @return 物品审核通过事件
     */
    public static ItemApprovedEvent create(Long userId, String itemType, Long itemId) {
        return new ItemApprovedEvent(userId, itemType, itemId);
    }
}
