<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomato.lostfoundsystem.mapper.SystemAnnouncementMapper">
    <insert id="insertAnnouncement" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO system_announcements (
            title, content, importance, start_time, end_time,
            created_by, status
        ) VALUES (
            #{title}, #{content}, #{importance}, #{startTime}, #{endTime},
            #{createdBy}, #{status}
        )
    </insert>

    <update id="updateAnnouncement">
        UPDATE system_announcements
        SET title = #{title},
            content = #{content},
            importance = #{importance},
            start_time = #{startTime},
            end_time = #{endTime},
            status = #{status}
        WHERE id = #{id}
    </update>

    <delete id="deleteAnnouncement">
        DELETE FROM system_announcements WHERE id = #{id}
    </delete>

    <select id="selectById" resultType="com.tomato.lostfoundsystem.entity.SystemAnnouncement">
        SELECT
            id, title, content, importance, start_time as startTime,
            end_time as endTime, created_by as createdBy,
            created_at as createdAt, updated_at as updatedAt, status
        FROM system_announcements
        WHERE id = #{id}
    </select>

    <select id="selectValidAnnouncements" resultType="com.tomato.lostfoundsystem.entity.SystemAnnouncement">
        SELECT
            id, title, content, importance, start_time as startTime,
            end_time as endTime, created_by as createdBy,
            created_at as createdAt, updated_at as updatedAt, status
        FROM system_announcements
        WHERE status = 'PUBLISHED'
        AND start_time &lt;= #{currentTime}
        AND (end_time IS NULL OR end_time &gt;= #{currentTime})
        ORDER BY
            CASE importance
                WHEN 'URGENT' THEN 1
                WHEN 'IMPORTANT' THEN 2
                ELSE 3
            END,
            created_at DESC
    </select>

    <!-- 新增：获取有效公告并包含用户已读状态 -->
    <select id="selectValidAnnouncementsWithReadStatus" resultMap="announcementWithReadStatusMap">
        SELECT
            sa.id, sa.title, sa.content, sa.importance, sa.start_time,
            sa.end_time, sa.created_by, sa.created_at, sa.updated_at, sa.status,
            uar.id as read_id, uar.read_at as read_at,
            CASE WHEN uar.id IS NOT NULL THEN 1 ELSE 0 END as is_read_flag
        FROM system_announcements sa
        LEFT JOIN user_announcement_reads uar ON sa.id = uar.announcement_id AND uar.user_id = #{userId}
        WHERE sa.status = 'PUBLISHED'
        AND sa.start_time &lt;= #{currentTime}
        AND (sa.end_time IS NULL OR sa.end_time &gt;= #{currentTime})
        ORDER BY
            CASE sa.importance
                WHEN 'URGENT' THEN 1
                WHEN 'IMPORTANT' THEN 2
                ELSE 3
            END,
            sa.created_at DESC
    </select>

    <!-- 新增：结果映射，包含已读状态 -->
    <resultMap id="announcementWithReadStatusMap" type="com.tomato.lostfoundsystem.dto.SystemAnnouncementDTO">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="importance" column="importance"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="status" column="status"/>
        <result property="isRead" column="is_read_flag" javaType="java.lang.Integer" jdbcType="INTEGER"/>
        <result property="readAt" column="read_at"/>
    </resultMap>

    <select id="selectLatestAnnouncement" resultType="com.tomato.lostfoundsystem.entity.SystemAnnouncement">
        SELECT
            id, title, content, importance, start_time as startTime,
            end_time as endTime, created_by as createdBy,
            created_at as createdAt, updated_at as updatedAt, status
        FROM system_announcements
        WHERE status = 'PUBLISHED'
        AND start_time &lt;= #{currentTime}
        AND (end_time IS NULL OR end_time &gt;= #{currentTime})
        ORDER BY
            CASE importance
                WHEN 'URGENT' THEN 1
                WHEN 'IMPORTANT' THEN 2
                ELSE 3
            END,
            created_at DESC
        LIMIT 1
    </select>

    <select id="selectAllAnnouncements" resultType="com.tomato.lostfoundsystem.entity.SystemAnnouncement">
        SELECT
            id, title, content, importance, start_time as startTime,
            end_time as endTime, created_by as createdBy,
            created_at as createdAt, updated_at as updatedAt, status
        FROM system_announcements
        ORDER BY
            CASE status
                WHEN 'PUBLISHED' THEN 1
                WHEN 'DRAFT' THEN 2
                ELSE 3
            END,
            CASE importance
                WHEN 'URGENT' THEN 1
                WHEN 'IMPORTANT' THEN 2
                ELSE 3
            END,
            created_at DESC
    </select>

    <update id="updateStatus">
        UPDATE system_announcements
        SET status = #{status}
        WHERE id = #{id}
    </update>
</mapper>
