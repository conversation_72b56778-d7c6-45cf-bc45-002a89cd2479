import request from '@/utils/request'

/**
 * 获取首页统计数据
 */
export function getHomePageStatistics() {
  return request({
    url: '/statistics/home',
    method: 'get',
    // 添加错误处理
    errorHandler: (error) => {
      // 如果是401或403错误，静默处理
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        console.log('用户未登录或无权限，静默处理统计请求');
        return {
          code: 200,
          message: '获取统计数据失败，但不影响使用',
          data: {
            lostItemCount: 0,
            foundItemCount: 0,
            returnedItemCount: 0,
            successMatchCount: 0
          }
        };
      }
      console.error('获取首页统计数据时出错:', error);
      return {
        code: 200,
        message: '获取统计数据失败，但不影响使用',
        data: {
          lostItemCount: 0,
          foundItemCount: 0,
          returnedItemCount: 0,
          successMatchCount: 0
        }
      };
    }
  })
}

/**
 * 获取活跃用户统计数据
 */
export function getActiveUsersStatistics() {
  return request({
    url: '/statistics/active-users',
    method: 'get'
  })
}

/**
 * 获取统计数据趋势
 * @param {string} type 统计类型
 * @param {number} days 天数
 */
export function getStatisticsTrend(type, days = 30) {
  return request({
    url: '/statistics/trend',
    method: 'get',
    params: {
      type,
      days
    }
  })
}

/**
 * 手动更新统计数据缓存（仅管理员可用）
 */
export function updateStatisticsCache() {
  return request({
    url: '/statistics/update-cache',
    method: 'post'
  })
}
