-- 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `username` VARCHA<PERSON>(50) NOT NULL UNIQUE,
  `password` VARCHAR(100) NOT NULL,
  `email` VARCHAR(100) UNIQUE,
  `phone` VARCHAR(20) UNIQUE,
  `avatar_url` VARCHAR(255),
  `role` VARCHAR(20) NOT NULL DEFAULT 'ROLE_USER',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` TIMESTAMP
);

-- 创建失物表
CREATE TABLE IF NOT EXISTS `lost_items` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `user_id` BIGINT NOT NULL,
  `item_name` VARCHAR(100) NOT NULL,
  `description` TEXT NOT NULL,
  `lost_time` VARCHAR(50) NOT NULL,
  `lost_location` VARCHAR(100) NOT NULL,
  `image_url` VARCHAR(255),
  `status` VARCHAR(20) NOT NULL DEFAULT 'lost',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `audit_status` VARCHAR(20) NOT NULL DEFAULT 'PENDING',
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
);

-- 创建物品图片表
CREATE TABLE IF NOT EXISTS `item_images` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `item_id` BIGINT NOT NULL,
  `item_type` VARCHAR(20) NOT NULL,
  `image_url` VARCHAR(255) NOT NULL,
  `sort_order` INT NOT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建物品审核表
CREATE TABLE IF NOT EXISTS `item_audits` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
  `item_id` BIGINT NOT NULL,
  `item_type` VARCHAR(20) NOT NULL,
  `audit_status` VARCHAR(20) NOT NULL,
  `remarks` TEXT,
  `auditor_id` BIGINT NOT NULL,
  `audit_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`auditor_id`) REFERENCES `users` (`id`)
);

-- 插入测试用户数据
INSERT INTO `users` (`id`, `username`, `password`, `email`, `phone`, `role`, `status`)
VALUES 
(1, 'testuser', '$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG', '<EMAIL>', '13800138000', 'ROLE_USER', 'ACTIVE'),
(2, 'testadmin', '$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HZWzG3YB1tlRy.fqvM/BG', '<EMAIL>', '13900139000', 'ROLE_ADMIN', 'ACTIVE');
