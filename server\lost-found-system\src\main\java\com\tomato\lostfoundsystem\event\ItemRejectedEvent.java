package com.tomato.lostfoundsystem.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 物品审核拒绝事件
 * 用于在应用内部传递物品审核拒绝的消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemRejectedEvent {
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 物品类型（lost/found）
     */
    private String itemType;
    
    /**
     * 物品ID
     */
    private Long itemId;
    
    /**
     * 拒绝原因
     */
    private String remarks;
    
    /**
     * 创建一个物品审核拒绝事件
     * 
     * @param userId 用户ID
     * @param itemType 物品类型
     * @param itemId 物品ID
     * @param remarks 拒绝原因
     * @return 物品审核拒绝事件
     */
    public static ItemRejectedEvent create(Long userId, String itemType, Long itemId, String remarks) {
        return new ItemRejectedEvent(userId, itemType, itemId, remarks);
    }
}
