package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.ItemImage;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 物品图片服务接口
 */
public interface ItemImageService {

    /**
     * 保存物品图片
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @param images 图片文件列表
     * @return 保存的图片URL列表
     */
    List<String> saveItemImages(Long itemId, String itemType, List<MultipartFile> images);

    /**
     * 保存物品图片URLs（不需要重新上传图片）
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @param imageUrls 已上传的图片URL列表
     * @return 保存的图片URL列表
     */
    List<String> saveItemImageUrls(Long itemId, String itemType, List<String> imageUrls);

    /**
     * 保存单张物品图片
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @param image 图片文件
     * @param sortOrder 排序顺序
     * @return 保存的图片URL
     */
    String saveItemImage(Long itemId, String itemType, MultipartFile image, Integer sortOrder);

    /**
     * 获取物品图片列表
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @return 图片列表
     */
    List<ItemImage> getItemImages(Long itemId, String itemType);

    /**
     * 获取物品图片URL列表
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @return 图片URL列表
     */
    List<String> getItemImageUrls(Long itemId, String itemType);

    /**
     * 删除物品图片
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @return 是否删除成功
     */
    boolean deleteItemImages(Long itemId, String itemType);

    /**
     * 删除单张物品图片
     *
     * @param imageId 图片ID
     * @return 是否删除成功
     */
    boolean deleteItemImage(Long imageId);

    /**
     * 根据ID列表批量删除图片
     *
     * @param imageIds 图片ID列表
     * @return 是否删除成功
     */
    boolean deleteImagesByIds(List<Long> imageIds);

    /**
     * 更新图片排序
     *
     * @param imageId 图片ID
     * @param sortOrder 排序顺序
     * @return 是否更新成功
     */
    boolean updateImageSortOrder(Long imageId, int sortOrder);

    /**
     * 更新物品图片
     * 优化的图片更新逻辑，支持部分更新和排序
     *
     * @param itemId 物品ID
     * @param itemType 物品类型（LOST/FOUND）
     * @param mainImage 主图
     * @param additionalImages 额外图片
     * @param retainImageIds 需要保留的图片ID列表
     * @return 更新结果
     */
    Result<Object> updateItemImages(Long itemId, String itemType,
                                  MultipartFile mainImage,
                                  List<MultipartFile> additionalImages,
                                  List<Long> retainImageIds);
}
