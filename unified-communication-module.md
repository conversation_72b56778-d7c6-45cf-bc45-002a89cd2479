# 5.4 即时通讯与离线消息模块实现

本章介绍校园失物招领系统中的即时通讯与离线消息模块，该模块整合了WebSocket实时通信和Kafka离线消息推送机制，为用户提供高效、可靠的消息传递服务，支持匹配通知、系统公告和用户间即时通讯等多种场景。

## 5.4.1 通信架构设计

系统采用了双层通信架构，结合WebSocket的实时性和Kafka的可靠性，实现了在线用户的即时通知和离线用户的消息存储与推送。

![通信架构图](https://i.imgur.com/YKLfDmh.png)

### 核心组件

1. **WebSocket服务**：基于STOMP协议，处理实时消息传递
2. **Kafka消息队列**：负责离线消息存储和异步处理
3. **消息分发中心**：根据用户在线状态选择合适的通信渠道
4. **通知状态管理**：跟踪消息的发送、接收和已读状态

### 消息流转过程

```mermaid
flowchart TD
    A[消息产生] --> B{用户是否在线?}
    B -->|是| C[WebSocket实时推送]
    B -->|否| D[Kafka消息队列]
    D --> E[用户上线]
    E --> F[消费离线消息]
    F --> G[WebSocket推送]
    C --> H[消息状态更新]
    G --> H
```

## 5.4.2 WebSocket实时通信实现

WebSocket通信基于Spring的STOMP协议实现，提供了可靠的双向通信通道。

### 服务端配置

```java
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {
    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        // 配置消息代理
        config.enableSimpleBroker("/topic", "/queue")  // 消息代理前缀
              .setHeartbeatValue(new long[]{25000, 25000})  // 心跳间隔25秒
              .setTaskScheduler(taskScheduler());

        config.setApplicationDestinationPrefixes("/app");  // 消息处理前缀
        config.setUserDestinationPrefix("/user");  // 用户目标前缀
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*")
                .withSockJS()
                .setInterceptors(webSocketHandshakeInterceptor);
    }
}
```

### 消息发送服务

```java
@Service
public class WebSocketServiceImpl implements WebSocketService {
    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Override
    public void sendMessage(Long userId, Map<String, Object> message) {
        try {
            // 获取用户名
            String username = getUsernameById(userId);

            if (username != null) {
                // 使用用户名发送消息到用户的特定主题
                messagingTemplate.convertAndSendToUser(
                    username,
                    "/queue/notifications",
                    message
                );
            } else {
                // 回退到使用ID
                messagingTemplate.convertAndSendToUser(
                    userId.toString(),
                    "/queue/notifications",
                    message
                );
            }
        } catch (Exception e) {
            log.error("发送WebSocket消息失败: {}", e.getMessage());
        }
    }
}
```

## 5.4.3 Kafka离线消息机制

Kafka消息队列用于处理离线消息和异步通知，确保消息的可靠传递。

### 消息生产者

```java
@Service
public class KafkaProducerServiceImpl implements KafkaProducerService {
    private static final String CHAT_TOPIC = "chat-topic";
    private static final String NOTIFICATION_TOPIC = "notification-topic";
    
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    
    @Override
    public boolean sendChatMessage(MessageDTO messageDTO) {
        try {
            // 创建消息信封
            ChatMessageEnvelope envelope = new ChatMessageEnvelope();
            envelope.setMessageId(messageDTO.getId().toString());
            envelope.setMessage(messageDTO);
            envelope.setStatus(ChatMessageEnvelope.MessageStatus.CREATED);
            
            // 将消息信封转换为JSON字符串
            String jsonMessage = objectMapper.writeValueAsString(envelope);
            
            // 发送消息到聊天主题
            kafkaTemplate.send(CHAT_TOPIC, envelope.getMessageId(), jsonMessage);
            return true;
        } catch (Exception e) {
            log.error("发送聊天消息失败: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public void sendNotification(String message) {
        sendToTopic(NOTIFICATION_TOPIC, message);
    }
}
```

### 消息消费者

```java
@Service
public class KafkaConsumerServiceImpl implements KafkaConsumerService {
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @KafkaListener(topics = "notification-topic", groupId = "notification-consumer-group")
    public void consumeNotification(String message, Acknowledgment acknowledgment) {
        try {
            // 解析消息
            JsonNode node = objectMapper.readTree(message);
            Long receiverId = node.get("receiverId").asLong();
            
            // 检查用户是否在线
            boolean isUserOnline = redisService.isUserOnline(receiverId);
            
            if (isUserOnline) {
                // 用户在线，通过WebSocket推送通知
                NotificationDTO notification = objectMapper.treeToValue(
                    node, NotificationDTO.class);
                
                // 推送通知
                notificationWebSocketHandler.sendNotification(receiverId, notification);
                
                // 确认消息
                acknowledgment.acknowledge();
            } else {
                // 用户离线，保留消息在队列中
                log.info("用户离线，保留通知在队列中: {}", receiverId);
                // 不确认消息，让它重新消费
            }
        } catch (Exception e) {
            log.error("处理通知消息失败: {}", e.getMessage());
            acknowledgment.acknowledge(); // 出错时也确认，避免无限重试
        }
    }
}
```

## 5.4.4 消息状态管理

系统实现了完整的消息状态管理，包括发送状态、接收状态和已读状态的跟踪。

### 通知状态管理

对于系统通知和匹配通知，使用数据库表记录通知状态：

```java
// 标记通知为已读
@Override
public Result markNotificationAsRead(Long notificationId, Long userId) {
    try {
        // 查询通知
        MatchNotification notification = matchNotificationMapper.selectById(notificationId);
        
        // 验证通知所有者
        if (!notification.getUserId().equals(userId)) {
            return Result.fail("无权操作此通知");
        }
        
        // 更新已读状态
        notification.setIsRead(true);
        notification.setReadAt(LocalDateTime.now());
        matchNotificationMapper.updateById(notification);
        
        return Result.success("标记已读成功");
    } catch (Exception e) {
        log.error("标记通知已读失败", e);
        return Result.fail("操作失败: " + e.getMessage());
    }
}
```

## 5.4.5 应用效果

通过整合WebSocket和Kafka，系统实现了高效可靠的消息传递机制，支持以下场景：

1. **匹配通知推送**：当系统发现高相似度匹配结果时，通过WebSocket实时推送给在线用户，或通过Kafka存储离线消息
2. **系统公告发布**：管理员发布的系统公告通过相同的机制推送给所有用户
3. **用户间即时通讯**：支持用户之间的实时聊天，包括消息状态跟踪和离线消息存储

![通信模块效果图](https://i.imgur.com/2pFLDmh.png)

通过这种统一的通信架构，系统大大提高了用户体验和物品找回效率，同时确保了消息传递的可靠性和实时性。
