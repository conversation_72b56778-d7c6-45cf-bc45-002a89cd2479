package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.dto.ReadReceiptDTO;
import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.mapper.ChatMessageMapper;
import com.tomato.lostfoundsystem.mapper.MessageAttachmentMapper;
import com.tomato.lostfoundsystem.model.kafka.ItemApprovedEvent;
import com.tomato.lostfoundsystem.service.AsyncTaskService;
import com.tomato.lostfoundsystem.service.FeatureExtractionService;
import com.tomato.lostfoundsystem.service.IntelligentMatchService;
import com.tomato.lostfoundsystem.service.KafkaProducerService;
import com.tomato.lostfoundsystem.entity.MessageAttachment;
import com.tomato.lostfoundsystem.utils.AliyunOSSUtil;
import com.tomato.lostfoundsystem.utils.FileValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import com.tomato.lostfoundsystem.enums.*;
import com.tomato.lostfoundsystem.utils.FFmpegUtils;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class AsyncTaskServiceImpl implements AsyncTaskService {

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;  // 用于推送消息

    @Autowired
    private MessageAttachmentMapper messageAttachmentMapper;

    @Autowired
    private ChatMessageMapper chatMessageMapper;

    @Autowired
    private FileValidationUtil fileValidationUtil;

    @Autowired
    private MessageRetryService messageRetryService;

    @Autowired
    private FeatureExtractionService featureExtractionService;

    @Autowired
    private IntelligentMatchService intelligentMatchService;

    /**
     * 异步上传文件到阿里云OSS
     * 注意：此方法已被 processFileAsync 替代，保留此方法是为了兼容性
     */
    @Async
    public void uploadFileToOSS(MultipartFile file, MessageAttachment messageAttachment) {
        try {
            log.info("开始上传文件到阿里云OSS: {}", file.getOriginalFilename());
            String fileUrl = AliyunOSSUtil.uploadFile(file, messageAttachment.getFileType());
            messageAttachment.setFileUrl(fileUrl);
            log.info("文件上传成功，URL: {}", fileUrl);
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 异步获取媒体时长
     * 注意：此方法已被 processFileAsync 替代，保留此方法是为了兼容性
     */
    @Async
    public CompletableFuture<Integer> getMediaDuration(MessageAttachment messageAttachment) {
        try {
            if (messageAttachment.getFileUrl() == null || messageAttachment.getFileUrl().isEmpty()) {
                log.warn("无法获取媒体时长，文件URL为空");
                return CompletableFuture.completedFuture(0);
            }

            int duration = 0;
            if (messageAttachment.getFileType() == FileType.AUDIO) {
                log.info("开始获取音频时长: {}", messageAttachment.getFileUrl());
                duration = (int) FFmpegUtils.getMediaDuration(messageAttachment.getFileUrl());
                log.info("音频时长: {} 秒", duration);
            } else if (messageAttachment.getFileType() == FileType.VIDEO) {
                log.info("开始获取视频时长: {}", messageAttachment.getFileUrl());
                duration = (int) FFmpegUtils.getMediaDuration(messageAttachment.getFileUrl());
                log.info("视频时长: {} 秒", duration);
            }
            return CompletableFuture.completedFuture(duration);
        } catch (Exception e) {
            log.error("获取媒体时长失败: {}", e.getMessage(), e);
            return CompletableFuture.completedFuture(0);
        }
    }

    /**
     * 异步存储离线消息到Kafka
     * 注意：此方法已被 sendChatMessage 替代，保留此方法是为了兼容性
     */
    @Async
    public void storeOfflineMessageAsync(String message) {
        try {
            log.info("开始存储离线消息到Kafka: {}", message);
            kafkaProducerService.storeOfflineMessage(message);
            log.info("离线消息存储成功");
        } catch (Exception e) {
            log.error("存储离线消息失败: {}", e.getMessage(), e);

            // 尝试使用重试服务
            try {
                messageRetryService.scheduleRetry(message, 0);
                log.info("离线消息已调度重试");
            } catch (Exception retryError) {
                log.error("调度离线消息重试失败: {}", retryError.getMessage(), retryError);
            }
        }
    }

    /**
     * 异步存储完整的离线消息对象到Kafka
     * 用于前端离线消息同步
     * 注意：此方法已被 sendChatMessage 替代，保留此方法是为了兼容性
     */
    @Async
    public void storeOfflineMessageObjectAsync(MessageDTO messageDTO) {
        try {
            log.info("开始存储完整离线消息对象到Kafka: {}", messageDTO);

            // 使用新的统一方法发送消息到Kafka
            boolean success = kafkaProducerService.sendChatMessage(messageDTO);

            if (success) {
                log.info("完整消息对象已发送到Kafka");
            } else {
                log.warn("发送消息到Kafka失败，尝试使用旧方法");

                // 如果新方法失败，尝试使用旧方法
                String messageJson = new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(messageDTO);
                kafkaProducerService.storeOfflineMessage(messageJson);
                log.info("使用旧方法存储完整消息对象成功");
            }
        } catch (Exception e) {
            log.error("存储完整离线消息对象失败: {}", e.getMessage(), e);

            // 尝试使用重试服务
            try {
                String messageJson = new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(messageDTO);
                messageRetryService.scheduleRetry(messageJson, 0);
                log.info("完整离线消息对象已调度重试");
            } catch (Exception retryError) {
                log.error("调度完整离线消息对象重试失败: {}", retryError.getMessage(), retryError);
            }
        }
    }

    /**
     * 异步推送已读回执到WebSocket
     */
    @Async
    public void pushReadReceiptToWebSocket(Long senderId, ReadReceiptDTO readReceiptDTO) {
        int retryCount = 0;
        int maxRetries = 3;
        boolean success = false;

        while (!success && retryCount < maxRetries) {
            try {
                log.info("推送已读回执到用户 {}, 尝试 #{}", senderId, retryCount + 1);
                log.info("已读回执内容: {}", readReceiptDTO);

                // 推送到发送者的已读回执队列 - 使用标准路径 /queue/read-receipts
                log.info("推送已读回执到用户私人队列 - 用户ID: {}, 路径: /user/{}/queue/read-receipts", senderId, senderId);
                log.info("已读回执详情: {}", readReceiptDTO);

                messagingTemplate.convertAndSendToUser(
                    senderId.toString(),
                    "/queue/read-receipts",
                    readReceiptDTO
                );

                success = true;
                log.info("成功推送已读回执到用户 {}, 推送路径: /user/{}/queue/read-receipts", senderId, senderId);
                log.info("已读回执详情 - 读者ID: {}, 发送者ID: {}, 消息IDs: {}",
                    readReceiptDTO.getReaderId(),
                    readReceiptDTO.getSenderId(),
                    readReceiptDTO.getMessageIds());
            } catch (Exception e) {
                retryCount++;
                log.error("推送已读回执失败 (尝试 #{}/{}): {}", retryCount, maxRetries, e.getMessage());
                log.error("推送失败详情", e);

                if (retryCount < maxRetries) {
                    try {
                        // 指数退避策略
                        TimeUnit.MILLISECONDS.sleep(1000 * (1 << retryCount));
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("推送已读回执重试等待被中断", ie);
                        break;
                    }
                }
            }
        }

        if (!success) {
            log.error("推送已读回执到用户 {} 失败，已达到最大重试次数", senderId);
            log.error("失败的已读回执详情 - 读者ID: {}, 发送者ID: {}, 消息IDs: {}",
                readReceiptDTO.getReaderId(),
                readReceiptDTO.getSenderId(),
                readReceiptDTO.getMessageIds());
        }
    }

    /**
     * 异步推送消息到WebSocket
     * 注意：此方法已被 sendChatMessage 替代，保留此方法是为了兼容性
     */
    @Async
    public void pushMessageToWebSocket(Long receiverId, MessageDTO messageDTO) {
        try {
            log.info("准备推送消息到用户 {}, 消息ID: {}, 发送者: {}, 内容: {}",
                receiverId, messageDTO.getId(), messageDTO.getSenderId(), messageDTO.getMessage());

            // 检查接收者ID是否有效
            if (receiverId == null) {
                log.error("接收者ID为空，无法推送消息");
                return;
            }

            // 使用新的统一方法发送消息到Kafka
            boolean success = kafkaProducerService.sendChatMessage(messageDTO);

            if (success) {
                log.info("消息已发送到Kafka: {}", messageDTO.getId());

                // 推送回发送者做状态回显 - 使用标准路径 /queue/sent
                if (messageDTO.getSenderId() != null) {
                    log.info("推送消息回发送者 {} 的路径: /user/{}/queue/sent", messageDTO.getSenderId(), messageDTO.getSenderId());
                    messagingTemplate.convertAndSendToUser(
                        messageDTO.getSenderId().toString(),
                        "/queue/sent",
                        messageDTO
                    );
                    log.info("已推送消息回发送者 {} 的路径", messageDTO.getSenderId());
                }

                log.info("✅ 消息处理完成: ID={}, 接收者={}, 内容={}",
                    messageDTO.getId(), receiverId, messageDTO.getMessage());
            } else {
                log.error("❌ 发送消息到Kafka失败，尝试直接推送");

                // 如果发送到Kafka失败，尝试直接推送
                messagingTemplate.convertAndSendToUser(
                    receiverId.toString(),
                    "/queue/private",
                    messageDTO
                );

                // 推送回发送者做状态回显
                if (messageDTO.getSenderId() != null) {
                    messagingTemplate.convertAndSendToUser(
                        messageDTO.getSenderId().toString(),
                        "/queue/sent",
                        messageDTO
                    );
                }

                log.info("✅ 直接推送完成: ID={}, 接收者={}", messageDTO.getId(), receiverId);
            }
        } catch (Exception e) {
            log.error("❌ 消息处理失败: {}", e.getMessage(), e);
            log.error("处理失败详情", e);

            // 重试逻辑...
            int retryCount = 0;
            int maxRetries = 3;
            boolean success = false;

            while (!success && retryCount < maxRetries) {
                try {
                    retryCount++;
                    log.info("重试发送消息 #{}/{} - 接收者: {}, 消息ID: {}",
                        retryCount, maxRetries, receiverId, messageDTO.getId());

                    // 等待一段时间后重试
                    TimeUnit.MILLISECONDS.sleep(1000 * (1 << retryCount));

                    // 重试发送到Kafka
                    success = kafkaProducerService.sendChatMessage(messageDTO);

                    if (success) {
                        log.info("✅ 重试发送到Kafka成功: ID={}, 接收者={}", messageDTO.getId(), receiverId);
                    }
                } catch (Exception retryEx) {
                    log.error("❌ 重试发送失败 #{}/{}: {}",
                        retryCount, maxRetries, retryEx.getMessage());
                }
            }

            // 如果重试失败，使用重试服务
            if (!success) {
                try {
                    log.info("所有重试都失败，将消息调度到重试服务");
                    String json = new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(messageDTO);
                    messageRetryService.scheduleRetry(json, 0);
                    log.info("消息已调度到重试服务");
                } catch (Exception ex) {
                    log.error("调度到重试服务失败: {}", ex.getMessage(), ex);
                    storeOfflineMessageAsync(messageDTO.getMessage());
                }
            }
        }
    }

    /**
     * 异步处理文件上传和媒体时长计算
     * 适用于校园失物招领平台的简化版本，包含文件验证
     */
    @Async
    public void processFileAsync(MultipartFile file, Long attachmentId, Long messageId) {
        try {
            log.info("开始异步处理文件: {}, 大小: {} 字节, 附件ID: {}, 消息ID: {}",
                    file.getOriginalFilename(), file.getSize(), attachmentId, messageId);

            // 1. 获取附件信息
            MessageAttachment attachment = messageAttachmentMapper.getAttachmentById(attachmentId);
            if (attachment == null) {
                log.error("附件不存在: {}", attachmentId);
                return;
            }

            // 2. 验证文件大小
            if (!fileValidationUtil.validateFileSize(file)) {
                log.error("文件大小超过限制: {}, 最大允许: {}",
                        file.getSize(), fileValidationUtil.getReadableMaxFileSize());
                return;
            }

            // 3. 验证文件类型
            if (!fileValidationUtil.validateFileType(file, attachment.getFileType())) {
                log.error("文件类型不允许: {}, 期望类型: {}",
                        file.getOriginalFilename(), attachment.getFileType());
                return;
            }

            // 4. 如果是音频或视频，获取时长
            int duration = 0;
            if (attachment.getFileType() == FileType.AUDIO || attachment.getFileType() == FileType.VIDEO) {
                try {
                    // 尝试获取媒体时长
                    duration = (int) FFmpegUtils.getMediaDurationFromLocalFile(file);
                    log.info("获取媒体时长: {} 秒", duration);
                } catch (Exception e) {
                    log.warn("获取媒体时长失败: {}", e.getMessage());
                }
            }

            // 5. 上传文件到OSS
            log.info("开始上传文件到阿里云OSS: {}, 大小: {} MB",
                    file.getOriginalFilename(), file.getSize() / (1024.0 * 1024.0));

            // 记录上传开始时间
            long startTime = System.currentTimeMillis();

            String fileUrl = AliyunOSSUtil.uploadFile(file, attachment.getFileType());

            // 计算上传耗时
            long endTime = System.currentTimeMillis();
            double uploadTime = (endTime - startTime) / 1000.0;

            if (fileUrl == null || fileUrl.isEmpty()) {
                log.error("文件上传失败，URL为空");
                return;
            }

            log.info("文件上传成功，URL: {}, 耗时: {} 秒", fileUrl, uploadTime);

            // 6. 更新附件的文件URL
            messageAttachmentMapper.updateAttachmentFileUrl(attachmentId, fileUrl);
            log.info("已更新附件的文件URL");

            // 7. 如果之前没获取到时长，再尝试从OSS获取
            if (duration == 0 && (attachment.getFileType() == FileType.AUDIO || attachment.getFileType() == FileType.VIDEO)) {
                try {
                    duration = (int) FFmpegUtils.getMediaDuration(fileUrl);
                    log.info("从OSS获取媒体时长: {} 秒", duration);
                } catch (Exception e) {
                    log.warn("从OSS获取媒体时长失败: {}", e.getMessage());
                    // 设置默认时长为1秒，避免显示为0
                    duration = 1;
                }
            }

            // 8. 更新附件对象的媒体时长（仅在内存中）
            attachment.setDuration(duration);

            // 9. 更新消息的时长信息
            ChatMessage chatMessage = chatMessageMapper.getMessageById(messageId);
            if (chatMessage != null) {
                if (attachment.getFileType() == FileType.AUDIO) {
                    chatMessage.setAudioDuration(duration);
                } else if (attachment.getFileType() == FileType.VIDEO) {
                    chatMessage.setVideoDuration(duration);
                }
                chatMessageMapper.updateChatMessageWithDuration(chatMessage);
                log.info("已更新消息的时长信息: {} 秒", duration);
            }

            log.info("文件处理完成: {}", file.getOriginalFilename());
        } catch (Exception e) {
            log.error("处理文件异步任务失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 异步处理物品审核通过后的特征提取和智能匹配
     * 主路径：使用Spring异步处理
     *
     * @param itemId 物品ID
     * @param userId 用户ID
     * @param itemType 物品类型（LOST/FOUND）
     */
    @Async("taskExecutor")
    public void processItemAutoMatchAsync(Long itemId, Long userId, String itemType) {
        try {
            log.info("【审核后处理】开始处理物品审核通过后的特征提取和智能匹配: 物品ID={}, 用户ID={}, 类型={}",
                    itemId, userId, itemType);
            log.info("【审核后处理】当前线程: {}", Thread.currentThread().getName());

            long startTime = System.currentTimeMillis();

            // 1. 提取并存储特征向量
            log.info("【审核后处理】开始提取并存储特征向量: 物品ID={}, 类型={}", itemId, itemType);
            long extractStartTime = System.currentTimeMillis();
            boolean extractionResult = featureExtractionService.extractAndStoreFeatures(itemId, itemType);
            long extractEndTime = System.currentTimeMillis();

            if (!extractionResult) {
                log.error("【审核后处理】特征提取失败，跳过智能匹配: 物品ID={}, 类型={}, 耗时: {} 毫秒",
                        itemId, itemType, (extractEndTime - extractStartTime));
                // 特征提取失败，通过Kafka兜底
                fallbackToKafka(itemId, userId, itemType, "特征提取失败");
                return;
            }

            log.info("【审核后处理】特征提取成功: 物品ID={}, 类型={}, 耗时: {} 毫秒",
                    itemId, itemType, (extractEndTime - extractStartTime));

            // 2. 执行智能匹配
            log.info("【审核后处理】开始执行智能匹配: 物品ID={}, 类型={}, 用户ID={}", itemId, itemType, userId);
            long matchStartTime = System.currentTimeMillis();
            intelligentMatchService.performAutoMatch(userId, itemId, itemType);
            long matchEndTime = System.currentTimeMillis();
            log.info("【审核后处理】智能匹配完成: 物品ID={}, 类型={}, 耗时: {} 毫秒",
                    itemId, itemType, (matchEndTime - matchStartTime));

            long endTime = System.currentTimeMillis();
            log.info("【审核后处理】物品审核通过后的处理完成: 物品ID={}, 类型={}, 总耗时: {} 毫秒",
                    itemId, itemType, (endTime - startTime));
            log.info("【审核后处理】处理详情 - 特征提取耗时: {} 毫秒, 智能匹配耗时: {} 毫秒",
                    (extractEndTime - extractStartTime), (matchEndTime - matchStartTime));
        } catch (Exception e) {
            log.error("【审核后处理】处理物品审核通过后的任务时发生异常: 物品ID={}, 类型={}, 异常: {}",
                    itemId, itemType, e.getMessage(), e);
            // 发生异常，通过Kafka兜底
            fallbackToKafka(itemId, userId, itemType, e.getMessage());
        }
    }

    /**
     * 失败时通过Kafka兜底
     * 备用路径：将任务发送到Kafka，由消费者重新处理
     */
    private void fallbackToKafka(Long itemId, Long userId, String itemType, String errorReason) {
        try {
            log.info("【Kafka兜底】主路径处理失败，启动Kafka兜底机制: 物品ID={}, 用户ID={}, 类型={}, 原因={}",
                    itemId, userId, itemType, errorReason);
            log.info("【Kafka兜底】当前线程: {}", Thread.currentThread().getName());

            // 创建物品审核通过事件
            ItemApprovedEvent event = ItemApprovedEvent.create(itemId, itemType, userId);
            log.info("【Kafka兜底】已创建物品审核通过事件: 物品ID={}, 类型={}, 用户ID={}",
                    event.getItemId(), event.getItemType(), event.getUserId());

            // 发送到Kafka兜底主题
            long startTime = System.currentTimeMillis();
            boolean sent = kafkaProducerService.sendItemApprovedEvent(event);
            long endTime = System.currentTimeMillis();

            if (sent) {
                log.info("【Kafka兜底】成功发送到Kafka兜底主题: 物品ID={}, 类型={}, 耗时: {} 毫秒",
                        itemId, itemType, (endTime - startTime));
                log.info("【Kafka兜底】事件将由Kafka消费者异步处理");
            } else {
                log.error("【Kafka兜底】发送到Kafka兜底主题失败: 物品ID={}, 类型={}, 耗时: {} 毫秒",
                        itemId, itemType, (endTime - startTime));
                log.error("【Kafka兜底】兜底机制失败，物品特征向量可能未被正确提取和索引");
            }
        } catch (Exception e) {
            log.error("【Kafka兜底】通过Kafka兜底时发生异常: 物品ID={}, 类型={}, 异常: {}",
                    itemId, itemType, e.getMessage(), e);
            log.error("【Kafka兜底】兜底机制异常详情", e);
            log.error("【Kafka兜底】兜底机制失败，物品特征向量可能未被正确提取和索引");
        }
    }
}
