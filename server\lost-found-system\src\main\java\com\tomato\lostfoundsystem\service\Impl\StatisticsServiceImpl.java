package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.HomeStatisticsDTO;
import com.tomato.lostfoundsystem.entity.StatisticsHistory;
import com.tomato.lostfoundsystem.mapper.StatisticsMapper;
import com.tomato.lostfoundsystem.service.StatisticsService;
import com.tomato.lostfoundsystem.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private StatisticsMapper statisticsMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void recordUserActivity(Long userId) {
        redisUtil.recordUserActivity(userId);
    }

    @Override
    public Result<HomeStatisticsDTO> getHomePageStatistics() {
        try {
            // 先尝试从Redis缓存获取
            Map<String, Object> cachedStats = redisUtil.getHomeStatistics();

            // 如果缓存中有完整数据，直接返回
            if (!cachedStats.isEmpty() &&
                cachedStats.containsKey("activeUsers") &&
                cachedStats.containsKey("totalItems") &&
                cachedStats.containsKey("returnedItems") &&
                cachedStats.containsKey("successMatches")) {

                HomeStatisticsDTO dto = new HomeStatisticsDTO();
                dto.setActiveUsers((Integer) cachedStats.get("activeUsers"));
                dto.setTotalItems((Integer) cachedStats.get("totalItems"));
                dto.setReturnedItems((Integer) cachedStats.get("returnedItems"));
                dto.setSuccessMatches((Integer) cachedStats.get("successMatches"));

                return Result.success(dto);
            }

            // 缓存中没有完整数据，重新计算并缓存
            HomeStatisticsDTO dto = new HomeStatisticsDTO();

            // 获取活跃用户数（使用月活跃用户数）
            long activeUsers = redisUtil.getMonthlyActiveUsers();
            dto.setActiveUsers((int) activeUsers);

            // 从数据库获取物品统计数据
            int totalItems = statisticsMapper.getTotalItems();
            dto.setTotalItems(totalItems);

            int returnedItems = statisticsMapper.getReturnedItems();
            dto.setReturnedItems(returnedItems);

            int successMatches = statisticsMapper.getSuccessMatches();
            dto.setSuccessMatches(successMatches);

            // 更新缓存
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("activeUsers", activeUsers);
            statistics.put("totalItems", totalItems);
            statistics.put("returnedItems", returnedItems);
            statistics.put("successMatches", successMatches);
            redisUtil.updateHomeStatistics(statistics);

            return Result.success(dto);
        } catch (Exception e) {
            log.error("获取首页统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getActiveUsersStatistics() {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 获取各时间段的活跃用户数
            statistics.put("daily", redisUtil.getDailyActiveUsers());
            statistics.put("weekly", redisUtil.getWeeklyActiveUsers());
            statistics.put("monthly", redisUtil.getMonthlyActiveUsers());

            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取活跃用户统计数据失败", e);
            return Result.error("获取活跃用户统计失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getStatisticsTrend(String statType, int days) {
        try {
            LocalDate endDate = LocalDate.now().minusDays(1); // 截止到昨天
            LocalDate startDate = endDate.minusDays(days - 1); // 往前推N天

            List<Map<String, Object>> trendData = statisticsMapper.getStatisticsTrend(
                    statType, startDate, endDate);

            Map<String, Object> result = new HashMap<>();
            result.put("type", statType);
            result.put("data", trendData);

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取统计数据趋势失败", e);
            return Result.error("获取统计数据趋势失败: " + e.getMessage());
        }
    }

    @Override
    @Scheduled(cron = "0 0 * * * *") // 每小时更新一次缓存
    public void updateStatisticsCache() {
        try {
            log.info("开始更新统计数据缓存...");

            // 获取最新的统计数据
            int totalItems = statisticsMapper.getTotalItems();
            int returnedItems = statisticsMapper.getReturnedItems();
            int successMatches = statisticsMapper.getSuccessMatches();
            long activeUsers = redisUtil.getMonthlyActiveUsers();

            // 更新Redis中的统计数据
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("activeUsers", activeUsers);
            statistics.put("totalItems", totalItems);
            statistics.put("returnedItems", returnedItems);
            statistics.put("successMatches", successMatches);

            // 更新缓存
            redisUtil.updateHomeStatistics(statistics);

            log.info("统计数据缓存更新完成");
        } catch (Exception e) {
            log.error("更新统计数据缓存失败", e);
        }
    }

    @Override
    @Scheduled(cron = "0 5 0 * * *") // 每天凌晨0:05执行
    public void saveStatisticsToDatabase() {
        try {
            log.info("开始保存统计数据到数据库...");

            LocalDate yesterday = LocalDate.now().minusDays(1);

            // 保存日活跃用户数
            String dailyKey = "active:users:daily:" + yesterday.format(DateTimeFormatter.ISO_LOCAL_DATE);
            Long dau = redisTemplate.opsForHyperLogLog().size(dailyKey);
            if (dau != null) {
                StatisticsHistory dauHistory = new StatisticsHistory();
                dauHistory.setStatDate(yesterday);
                dauHistory.setStatType("DAU");
                dauHistory.setStatValue(dau.intValue());
                statisticsMapper.saveStatisticsHistory(dauHistory);
            }

            // 保存周活跃用户数
            String weeklyKey = "active:users:weekly:" + yesterday.format(DateTimeFormatter.ofPattern("yyyy-ww"));
            Long wau = redisTemplate.opsForHyperLogLog().size(weeklyKey);
            if (wau != null) {
                StatisticsHistory wauHistory = new StatisticsHistory();
                wauHistory.setStatDate(yesterday);
                wauHistory.setStatType("WAU");
                wauHistory.setStatValue(wau.intValue());
                statisticsMapper.saveStatisticsHistory(wauHistory);
            }

            // 保存月活跃用户数
            String monthlyKey = "active:users:monthly:" + yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            Long mau = redisTemplate.opsForHyperLogLog().size(monthlyKey);
            if (mau != null) {
                StatisticsHistory mauHistory = new StatisticsHistory();
                mauHistory.setStatDate(yesterday);
                mauHistory.setStatType("MAU");
                mauHistory.setStatValue(mau.intValue());
                statisticsMapper.saveStatisticsHistory(mauHistory);
            }

            // 保存总物品数
            int totalItems = statisticsMapper.getTotalItems();
            StatisticsHistory totalItemsHistory = new StatisticsHistory();
            totalItemsHistory.setStatDate(yesterday);
            totalItemsHistory.setStatType("TOTAL_ITEMS");
            totalItemsHistory.setStatValue(totalItems);
            statisticsMapper.saveStatisticsHistory(totalItemsHistory);

            // 保存已归还物品数
            int returnedItems = statisticsMapper.getReturnedItems();
            StatisticsHistory returnedItemsHistory = new StatisticsHistory();
            returnedItemsHistory.setStatDate(yesterday);
            returnedItemsHistory.setStatType("RETURNED_ITEMS");
            returnedItemsHistory.setStatValue(returnedItems);
            statisticsMapper.saveStatisticsHistory(returnedItemsHistory);

            // 保存已找回物品数
            int successMatches = statisticsMapper.getSuccessMatches();
            StatisticsHistory successMatchesHistory = new StatisticsHistory();
            successMatchesHistory.setStatDate(yesterday);
            successMatchesHistory.setStatType("SUCCESS_MATCHES");
            successMatchesHistory.setStatValue(successMatches);
            statisticsMapper.saveStatisticsHistory(successMatchesHistory);

            log.info("统计数据已成功保存到数据库");
        } catch (Exception e) {
            log.error("保存统计数据到数据库失败", e);
        }
    }
}
