package com.tomato.lostfoundsystem.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.util.Collection;


@Component
public class SecurityUtil {

    // 手动定义日志对象
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(SecurityUtil.class);

    // 获取当前登录用户的ID
    public Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("获取到的请求头是{}", authentication);

        if (authentication != null && authentication.getDetails() instanceof Long) {
            log.info("security获取用户ID: {}", authentication.getDetails());
            return (Long) authentication.getDetails();  // 获取存储在 details 中的 userId
        }
        log.info("security中没有获取到信息");
        return null;  // 如果没有认证信息，返回 null
    }

    // 获取当前登录用户的用户名
    public String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication != null) {
            return authentication.getName();  // 获取用户名
        }

        return null;  // 如果没有认证信息，返回 null
    }

    // 获取当前登录用户的角色
    @SuppressWarnings("unchecked")  // 抑制未检查的类型转换警告
    public String getCurrentUserRole() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication != null) {
            Collection<SimpleGrantedAuthority> authorities = (Collection<SimpleGrantedAuthority>) authentication.getAuthorities();
            for (SimpleGrantedAuthority authority : authorities) {
                return authority.getAuthority();  // 返回用户角色
            }
        }

        return null;  // 如果没有认证信息，返回 null
    }
}
