# 失物招领系统服务启动脚本使用说明

本目录包含了一系列批处理脚本，用于一键启动和停止失物招领系统所需的各种服务，包括Redis、Kafka和智能匹配服务。

## 脚本文件说明

1. **config.bat** - 集中配置文件，所有配置都在这里管理
2. **edit_config.bat** - 配置编辑器，方便直接编辑配置文件
3. **sync_config.bat** - 配置同步工具，帮助保持脚本配置与数据库配置一致
4. **start_all_services.bat** - 一键启动所有服务
5. **stop_all_services.bat** - 一键停止所有服务
6. **start_redis.bat** - 仅启动Redis服务
7. **start_kafka.bat** - 仅启动Kafka服务（包括Zookeeper）
8. **start_clip_service.bat** - 仅启动智能匹配服务
9. **create_shortcuts.bat** - 在桌面创建快捷方式

## 使用前配置

在使用这些脚本之前，请先根据你的实际安装路径修改配置文件：

1. 运行 `edit_config.bat` 脚本，打开配置文件
2. 根据实际情况修改各个服务的安装路径和配置

### 重要：配置同步

**注意：** 此配置文件中的设置必须与数据库中的system_config表保持一致，特别是智能匹配服务的配置。这是因为后台管理界面使用数据库中的配置来检测和控制服务。

如果您在后台管理界面中修改了智能匹配服务的配置（如服务URL或脚本路径），请确保同步更新此配置文件中的对应设置，反之亦然。

#### 使用配置同步工具

为了帮助您保持配置一致性，我们提供了配置同步工具：

1. 运行 `sync_config.bat` 脚本
2. 工具会显示当前脚本配置和对应的数据库配置项
3. 您可以选择打开配置文件进行编辑，或在后台管理界面中修改数据库配置

#### 主要需要同步的配置项

| config.bat中的配置 | 数据库中的配置项 | 说明 |
|-------------------|-----------------|------|
| CLIP_SERVICE_PATH + CLIP_API_SCRIPT | autodl.clip.service.script-path | 智能匹配服务脚本路径 |
| CLIP_API_PORT | autodl.clip.api.url | 智能匹配服务URL（包含端口） |

### 配置文件说明（config.bat）

```batch
:: Redis配置
set REDIS_PATH=D:\redis                  # Redis安装路径
set REDIS_CONFIG=redis.windows.conf      # Redis配置文件名
set REDIS_PORT=6379                      # Redis端口

:: Kafka配置
set KAFKA_PATH=D:\kafka                  # Kafka安装路径
set ZOOKEEPER_CONFIG=config\zookeeper.properties  # Zookeeper配置文件路径
set KAFKA_CONFIG=config\server.properties         # Kafka配置文件路径
set ZOOKEEPER_PORT=2181                  # Zookeeper端口
set KAFKA_PORT=9092                      # Kafka端口

:: 智能匹配服务配置
set CLIP_SERVICE_PATH=D:\clip_faiss_service  # 智能匹配服务路径
set CLIP_API_SCRIPT=clip_faiss_api.py        # API脚本名称
set CLIP_ENV_PATH=clip_faiss_env             # 虚拟环境路径
set CLIP_API_PORT=8000                       # API端口

:: 系统配置
set SYSTEM_NAME=失物招领系统              # 系统名称
set LOG_PATH=logs                        # 日志路径
set WAIT_TIME=10                         # 等待时间（秒）
```

## 使用方法

### 创建桌面快捷方式

运行 `create_shortcuts.bat` 脚本，它会在桌面创建两个快捷方式：
- "启动所有服务" - 一键启动所有服务
- "停止所有服务" - 一键停止所有服务

### 启动所有服务

双击运行 `start_all_services.bat` 文件，或者使用桌面上的"启动所有服务"快捷方式，将依次启动Redis、Kafka和智能匹配服务。

### 停止所有服务

双击运行 `stop_all_services.bat` 文件，或者使用桌面上的"停止所有服务"快捷方式，将依次停止所有服务。

### 单独启动某个服务

如果只需要启动某个特定的服务，可以双击运行对应的启动脚本：
- `start_redis.bat` - 启动Redis
- `start_kafka.bat` - 启动Kafka
- `start_clip_service.bat` - 启动智能匹配服务

## 注意事项

1. **配置同步非常重要**：确保脚本配置与数据库配置保持一致，特别是在使用后台管理界面控制智能匹配服务时。
2. 这些脚本会在启动服务时检查服务是否已经在运行，如果已经运行则不会重复启动。
3. 服务启动后，命令窗口会保持打开状态，显示服务的运行状态。
4. 停止服务时会强制终止相关进程，可能会导致数据丢失，请确保在适当的时候停止服务。
5. 如果服务启动失败，请检查配置路径是否正确，以及相关服务是否已正确安装。
6. **系统初始启动流程建议**：
   - 首次启动系统时，使用`start_all_services.bat`启动所有服务
   - 系统正常运行后，优先使用后台管理界面来控制智能匹配服务
   - 只有在需要重启Redis或Kafka时，才使用对应的单独启动脚本

## 常见问题

### Q: 脚本运行时提示"系统找不到指定的路径"

A: 请检查脚本中设置的路径是否正确，确保Redis、Kafka和智能匹配服务已正确安装在指定路径。

### Q: 服务启动失败

A: 请检查相关服务的配置文件是否正确，以及是否有其他程序占用了相同的端口。

### Q: 如何知道服务是否成功启动？

A: 脚本会在启动服务后检查相关进程是否存在，并显示服务状态。你也可以通过查看命令窗口中的输出信息来判断服务是否成功启动。
