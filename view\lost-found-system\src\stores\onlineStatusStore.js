/**
 * 在线状态管理 Store
 * 集中管理在线用户状态，避免状态管理混乱和循环依赖
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useOnlineStatusStore = defineStore('onlineStatus', () => {
  // 状态
  const onlineUsers = ref(new Map()) // 在线用户Map: userId => { lastActive, status }
  const onlineContacts = ref(new Map()) // 在线联系人Map: userId => { lastActive, status }
  const connectionStatus = ref({
    connected: false,
    connecting: false,
    lastConnected: null,
    reconnectAttempts: 0
  })
  const serviceInitialized = ref(false) // 服务是否已初始化
  const onlineCount = ref(0) // 在线用户数量，从服务器获取

  // 计算属性
  const onlineUserCount = computed(() => {
    // 如果服务器返回了在线用户数量，优先使用服务器返回的数量
    if (onlineCount.value > 0) {
      return onlineCount.value;
    }

    // 否则使用本地计算的数量，确保至少返回1（当前用户）
    const count = onlineUsers.value.size;
    return count > 0 ? count : 1;
  })
  const isConnected = computed(() => connectionStatus.value.connected)
  const isConnecting = computed(() => connectionStatus.value.connecting)
  const onlineUsersList = computed(() => Array.from(onlineUsers.value.keys()))
  const onlineContactsList = computed(() => Array.from(onlineContacts.value.keys()))
  const isServiceInitialized = computed(() => serviceInitialized.value)

  // 方法
  /**
   * 更新连接状态
   * @param {boolean} connected 是否已连接
   * @param {boolean} connecting 是否正在连接
   */
  function updateConnectionStatus(connected, connecting) {
    connectionStatus.value.connected = connected
    connectionStatus.value.connecting = connecting

    if (connected) {
      connectionStatus.value.lastConnected = Date.now()
      connectionStatus.value.reconnectAttempts = 0
    }
  }

  /**
   * 增加重连尝试次数
   */
  function incrementReconnectAttempts() {
    connectionStatus.value.reconnectAttempts++
  }

  /**
   * 更新在线用户列表
   * @param {Array} users 在线用户ID数组
   * @returns {Object} 包含新上线和下线的用户
   */
  function updateOnlineUsers(users) {
    if (!Array.isArray(users)) {
      console.error('updateOnlineUsers: users不是数组', users)
      return { newUsers: [], offlineUsers: [] }
    }

    const newUsers = []
    const currentUsers = new Set(onlineUsers.value.keys())
    const incomingUsers = new Set(users)

    // 找出新上线的用户
    for (const userId of incomingUsers) {
      if (!currentUsers.has(userId)) {
        newUsers.push(userId)
        onlineUsers.value.set(userId, {
          lastActive: Date.now(),
          status: 'online'
        })
      }
    }

    // 找出下线的用户
    const offlineUsers = []
    for (const userId of currentUsers) {
      if (!incomingUsers.has(userId)) {
        offlineUsers.push(userId)
        onlineUsers.value.delete(userId)
      }
    }

    return { newUsers, offlineUsers }
  }

  /**
   * 更新单个用户的在线状态
   * @param {string} userId 用户ID
   * @param {boolean} isOnline 是否在线
   * @returns {boolean} 状态是否变化
   */
  function updateUserStatus(userId, isOnline) {
    const currentStatus = onlineUsers.value.has(userId)

    if (isOnline === currentStatus) {
      // 状态没有变化
      if (isOnline) {
        // 如果用户在线，更新最后活跃时间
        const userData = onlineUsers.value.get(userId)
        if (userData) {
          userData.lastActive = Date.now()
          onlineUsers.value.set(userId, userData)
        }
      }
      return false
    }

    if (isOnline) {
      // 用户上线
      onlineUsers.value.set(userId, {
        lastActive: Date.now(),
        status: 'online'
      })
    } else {
      // 用户下线
      onlineUsers.value.delete(userId)
    }

    return true
  }

  /**
   * 检查用户是否在线
   * @param {string} userId 用户ID
   * @returns {boolean} 是否在线
   */
  function isUserOnline(userId) {
    return onlineUsers.value.has(userId)
  }

  /**
   * 获取用户最后活跃时间
   * @param {string} userId 用户ID
   * @returns {number|null} 最后活跃时间
   */
  function getUserLastActiveTime(userId) {
    const userData = onlineUsers.value.get(userId)
    return userData ? userData.lastActive : null
  }

  /**
   * 重置状态
   */
  function reset() {
    onlineUsers.value.clear()
    connectionStatus.value = {
      connected: false,
      connecting: false,
      lastConnected: null,
      reconnectAttempts: 0
    }
  }

  /**
   * 设置连接状态
   * @param {boolean} connected 是否已连接
   * @param {boolean} reconnecting 是否正在重连
   * @param {number} attempts 重连尝试次数
   */
  function setConnectionStatus(connected, reconnecting, attempts = 0) {
    connectionStatus.value.connected = connected
    connectionStatus.value.connecting = reconnecting

    if (attempts > 0) {
      connectionStatus.value.reconnectAttempts = attempts
    }

    if (connected) {
      connectionStatus.value.lastConnected = Date.now()
      connectionStatus.value.reconnectAttempts = 0
    }
  }

  /**
   * 设置服务初始化状态
   * @param {boolean} initialized 是否已初始化
   */
  function setServiceInitialized(initialized) {
    serviceInitialized.value = initialized
  }

  /**
   * 设置在线用户数量
   * @param {number} count 在线用户数量
   */
  function setOnlineCount(count) {
    if (typeof count === 'number' && count >= 0) {
      onlineCount.value = count
    }
  }

  /**
   * 更新联系人在线状态
   * @param {Array} contactIds 在线联系人ID数组
   * @returns {Object} 包含新上线和下线的联系人
   */
  function updateContactOnlineStatus(contactIds) {
    if (!Array.isArray(contactIds)) {
      console.error('updateContactOnlineStatus: contactIds不是数组', contactIds)
      return { newOnline: [], newOffline: [] }
    }

    const newOnline = []
    const currentOnline = new Set(onlineContacts.value.keys())
    const incomingOnline = new Set(contactIds)

    // 找出新上线的联系人
    for (const contactId of incomingOnline) {
      if (!currentOnline.has(contactId)) {
        newOnline.push(contactId)
        onlineContacts.value.set(contactId, {
          lastActive: Date.now(),
          status: 'online'
        })
      }
    }

    // 找出下线的联系人
    const newOffline = []
    for (const contactId of currentOnline) {
      if (!incomingOnline.has(contactId)) {
        newOffline.push(contactId)
        onlineContacts.value.delete(contactId)
      }
    }

    return { newOnline, newOffline }
  }

  /**
   * 检查联系人是否在线
   * @param {string} contactId 联系人ID
   * @returns {boolean} 是否在线
   */
  function isContactOnline(contactId) {
    return onlineContacts.value.has(contactId)
  }

  return {
    // 状态
    onlineUsers,
    onlineContacts,
    connectionStatus,
    serviceInitialized,
    onlineCount,

    // 计算属性
    onlineUserCount,
    isConnected,
    isConnecting,
    onlineUsersList,
    onlineContactsList,
    isServiceInitialized,

    // 方法
    updateConnectionStatus,
    incrementReconnectAttempts,
    updateOnlineUsers,
    updateUserStatus,
    isUserOnline,
    getUserLastActiveTime,
    setConnectionStatus,
    setServiceInitialized,
    setOnlineCount,
    updateContactOnlineStatus,
    isContactOnline,
    reset
  }
})
