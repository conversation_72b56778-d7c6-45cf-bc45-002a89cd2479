package com.tomato.lostfoundsystem.dto;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserProfileDTO {
    private Long id;
    private String username;
    private String password;
    private String email;
    private String phone;
    private LocalDateTime createTime;
    private String role;
    private Boolean isActive;
    private Boolean deleted;
    private String avatar;

    // Get<PERSON> and Setters
}
