import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  server: {
    host: true, // 👈 允许外部设备（ngrok）访问
    port: 5173,
    strictPort: true,
    cors: true, // 启用 CORS
    allowedHosts: ['.ngrok-free.app', '.ngrok.io', '.ngrok.app'], // 👈 允许所有 ngrok 域名
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        rewrite: (path) => path,
        secure: false,
        timeout: 10000, // 设置10秒超时
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('发送代理请求:', {
              method: req.method,
              url: req.url,
              headers: proxyReq.getHeaders()
            })
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('收到代理响应:', {
              statusCode: proxyRes.statusCode,
              headers: proxyRes.headers,
              url: req.url
            })
          })
        }
      },
      '/ws': {
        target: 'http://localhost:8081',
        changeOrigin: true,
        ws: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('WebSocket代理错误:', err)
            console.log('错误详情:', err.stack || err.message || err)
          })

          // 处理WebSocket升级请求
          proxy.on('upgrade', (req, socket, head) => {
            console.log('WebSocket升级请求:', req.url)

            // 从URL参数中获取token
            try {
              const url = new URL(req.url, 'http://localhost')
              const token = url.searchParams.get('token')
              if (token) {
                // 设置Authorization头
                req.headers['authorization'] = `Bearer ${token}`
                // 保留原始URL中的token参数，确保后端可以从URL中获取
                console.log('WebSocket升级请求: 从URL参数中获取并设置token')
              } else {
                console.warn('WebSocket升级请求: URL中未找到token参数')
              }
            } catch (e) {
              console.error('解析WebSocket URL参数时出错:', e)
            }
          })
        }
      }
    }
  },
  // 移除全局传输设置，让SockJS自行选择最佳传输方式
  build: {
    // 启用源码映射（仅在开发环境）
    sourcemap: process.env.NODE_ENV !== 'production',
    // 设置构建目标浏览器
    target: 'es2015',
    // 启用CSS代码分割
    cssCodeSplit: true,
    // 设置chunk大小警告阈值
    chunkSizeWarningLimit: 1000,
    // 自定义构建选项
    rollupOptions: {
      output: {
        // 自定义chunk文件名格式
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
        // 手动拆分chunks
        manualChunks(id) {
          // 将 node_modules 中的代码分割成单独的 chunk
          if (id.includes('node_modules')) {
            // 将 Element Plus 相关的模块打包在一起
            if (id.includes('element-plus') || id.includes('@element-plus')) {
              return 'element-plus';
            }
            // 将 Vue 相关模块打包在一起
            if (id.includes('vue') || id.includes('pinia') || id.includes('vue-router')) {
              return 'vue-vendor';
            }
            // 将 ECharts 相关模块打包在一起
            if (id.includes('echarts')) {
              return 'echarts';
            }
            // 其他第三方库
            return 'vendor';
          }
          // 将工具函数打包在一起
          if (id.includes('/utils/')) {
            return 'utils';
          }
        }
      }
    },
    // 启用CSS压缩
    cssMinify: true,
    // 启用JS压缩
    minify: 'esbuild',
  }
})
