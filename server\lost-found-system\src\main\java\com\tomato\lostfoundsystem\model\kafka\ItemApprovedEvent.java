package com.tomato.lostfoundsystem.model.kafka;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 物品审核通过事件
 * 用于在Kafka中传递物品审核通过的消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemApprovedEvent {
    /**
     * 物品ID
     */
    private Long itemId;
    
    /**
     * 物品类型（LOST/FOUND）
     */
    private String itemType;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 审核时间戳
     */
    private Long approvedTimestamp;
    
    /**
     * 创建一个物品审核通过事件
     * 
     * @param itemId 物品ID
     * @param itemType 物品类型
     * @param userId 用户ID
     * @return 物品审核通过事件
     */
    public static ItemApprovedEvent create(Long itemId, String itemType, Long userId) {
        return new ItemApprovedEvent(itemId, itemType, userId, System.currentTimeMillis());
    }
}
