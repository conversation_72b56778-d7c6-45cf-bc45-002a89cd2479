<template>
  <div class="avatar-cropper">
    <el-dialog
      v-model="dialogVisible"
      title="裁剪头像"
      width="500px"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <div class="cropper-container">
        <vue-cropper
          ref="cropperRef"
          :img="imgSrc"
          :outputSize="1"
          :outputType="outputType"
          :info="true"
          :full="true"
          :canMove="true"
          :canMoveBox="true"
          :original="false"
          :autoCrop="true"
          :autoCropWidth="300"
          :autoCropHeight="300"
          :fixedBox="true"
          :centerBox="true"
          :infoTrue="true"
          mode="contain"
        />
      </div>
      <div class="cropper-controls">
        <div class="zoom-controls">
          <el-button @click="changeScale(1)">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button @click="changeScale(-1)">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
        </div>
        <div class="rotate-controls">
          <el-button @click="rotateLeft">
            <el-icon><RefreshLeft /></el-icon>
          </el-button>
          <el-button @click="rotateRight">
            <el-icon><RefreshRight /></el-icon>
          </el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="cropImage">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { VueCropper } from 'vue-cropper'
import { ElMessage } from 'element-plus'
import { ZoomIn, ZoomOut, RefreshLeft, RefreshRight } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  imgFile: {
    type: [File, null],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'crop-success', 'crop-cancel'])

const dialogVisible = ref(false)
const imgSrc = ref('')
const cropperRef = ref(null)
const outputType = ref('png')

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.imgFile) {
    loadImage(props.imgFile)
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 加载图片
const loadImage = (file) => {
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    imgSrc.value = e.target.result
  }
  reader.readAsDataURL(file)
}

// 缩放图片
const changeScale = (num) => {
  if (!cropperRef.value) return
  cropperRef.value.changeScale(num)
}

// 向左旋转
const rotateLeft = () => {
  if (!cropperRef.value) return
  cropperRef.value.rotateLeft()
}

// 向右旋转
const rotateRight = () => {
  if (!cropperRef.value) return
  cropperRef.value.rotateRight()
}

// 裁剪图片
const cropImage = () => {
  if (!cropperRef.value) {
    ElMessage.error('裁剪器未初始化')
    return
  }

  cropperRef.value.getCropBlob((blob) => {
    // 创建一个新的File对象
    const croppedFile = new File([blob], `avatar_${Date.now()}.${outputType.value}`, {
      type: `image/${outputType.value}`
    })

    emit('crop-success', {
      file: croppedFile,
      dataUrl: URL.createObjectURL(blob)
    })

    dialogVisible.value = false
  })
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  emit('crop-cancel')
}
</script>

<style scoped>
.avatar-cropper {
  font-family: Arial, sans-serif;
}

.cropper-container {
  height: 350px;
  width: 100%;
  margin-bottom: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.cropper-controls {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.zoom-controls, .rotate-controls {
  display: flex;
  gap: 10px;
}

:deep(.vue-cropper) {
  background-color: #f5f7fa !important;
}

:deep(.cropper-view-box) {
  border-radius: 50%;
  outline: 2px solid #409eff;
  outline-offset: -2px;
}

:deep(.cropper-face) {
  background-color: inherit !important;
}

:deep(.cropper-modal) {
  background-color: rgba(0, 0, 0, 0.5) !important;
}
</style>
