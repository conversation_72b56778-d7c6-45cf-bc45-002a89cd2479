package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.service.WebSocketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class WebSocketServiceImpl implements WebSocketService {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private com.tomato.lostfoundsystem.mapper.UserMapper userMapper;

    // 存储所有在线用户的状态
    private final Map<String, Boolean> onlineUsers = new ConcurrentHashMap<>();

    @Override
    public void userOnline(String userId) {
        // 将用户标记为在线
        onlineUsers.put(userId, true);
    }

    @Override
    public void userOffline(String userId) {
        // 将用户标记为离线
        onlineUsers.put(userId, false);
    }

    @Override
    public boolean isUserOnline(String userId) {
        // 判断用户是否在线
        return onlineUsers.getOrDefault(userId, false); // 默认为离线状态
    }

    @Override
    public Map<String, Boolean> getOnlineUsers() {
        return onlineUsers;
    }

    @Override
    public void sendMessage(Long userId, Map<String, Object> message) {
        try {
            // 获取用户名
            String username = getUsernameById(userId);

            if (username != null) {
                // 使用用户名发送消息到用户的特定主题
                messagingTemplate.convertAndSendToUser(
                    username,  // 使用用户名而不是用户ID
                    "/queue/notifications",
                    message
                );
                log.info("使用用户名发送WebSocket消息: 用户名={}, 用户ID={}", username, userId);
            } else {
                log.error("无法获取用户名，使用ID发送WebSocket消息: {}", userId);
                // 回退到使用ID
                messagingTemplate.convertAndSendToUser(
                    userId.toString(),
                    "/queue/notifications",
                    message
                );
            }
        } catch (Exception e) {
            log.error("发送WebSocket消息失败, 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
        }
    }

    /**
     * 根据用户ID获取用户名
     * @param userId 用户ID
     * @return 用户名，如果未找到则返回null
     */
    private String getUsernameById(Long userId) {
        try {
            if (userId == null) {
                log.warn("尝试获取用户名时收到空的用户ID");
                return null;
            }

            // 从数据库中查询用户
            com.tomato.lostfoundsystem.entity.User user = userMapper.findById(userId);
            if (user != null) {
                return user.getUsername();
            } else {
                log.warn("未找到用户ID对应的用户: {}", userId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取用户名时出错: {}", e.getMessage(), e);
            return null;
        }
    }
}
