<template>
  <div class="edit-found-item">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <h2 class="title">修改拾物信息</h2>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        class="edit-form"
        v-loading="loading"
      >
        <el-form-item label="物品名称" prop="itemName">
          <el-input v-model="form.itemName" placeholder="请输入物品名称" />
        </el-form-item>

        <el-form-item label="拾取地点" prop="foundLocation">
          <el-input v-model="form.foundLocation" placeholder="请输入拾取地点" />
        </el-form-item>

        <el-form-item label="拾取时间" prop="foundTime">
          <el-date-picker
            v-model="form.foundTime"
            type="datetime"
            placeholder="请选择拾取时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
          />
        </el-form-item>

        <el-form-item label="物品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入物品描述"
          />
        </el-form-item>

        <el-form-item label="物品图片">
          <el-upload
            class="found-image-uploader"
            :auto-upload="false"
            list-type="picture-card"
            :on-change="handleImageChange"
            :before-upload="beforeUpload"
            :limit="5"
            :on-exceed="handleExceed"
            :on-remove="handleRemove"
            multiple
            :file-list="fileList"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="upload-tip">
                <p>点击选择拾物图片，最多上传5张（可选）</p>
                <p class="upload-tip-small">上传图片可以提高认领物品的几率</p>
                <p class="upload-tip-small">支持 jpg、png 格式，每张大小不超过 2MB</p>
              </div>
            </template>
          </el-upload>

          <!-- 图片管理区域 -->
          <div v-if="fileList && fileList.length > 0" class="image-management">
            <div class="image-management-header">
              <h4>图片管理</h4>
              <el-tooltip content="拖动图片可以调整顺序，点击星标可以设置为主图">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </div>

            <!-- 图片列表 -->
            <div class="image-list">
              <draggable
                v-model="fileList"
                item-key="uid"
                @end="handleDragEnd"
                handle=".drag-handle"
                :animation="150"
              >
                <template #item="{element, index}">
                  <div
                    class="image-item"
                    :class="{ 'is-main': index === mainImageIndex }"
                  >
                    <div class="image-preview">
                      <el-image
                        :src="element.url"
                        fit="cover"
                        :preview-src-list="getPreviewList()"
                        :initial-index="index"
                      />
                      <div class="image-actions">
                        <el-button
                          v-if="index !== mainImageIndex"
                          type="warning"
                          circle
                          @click="setAsMainImage(index)"
                        >
                          <el-icon><Star /></el-icon>
                        </el-button>
                        <el-button
                          v-else
                          type="warning"
                          circle
                          disabled
                        >
                          <el-icon><StarFilled /></el-icon>
                        </el-button>
                        <el-button
                          type="danger"
                          circle
                          @click="removeImage(index)"
                        >
                          <el-icon><Close /></el-icon>
                        </el-button>
                      </div>
                      <div v-if="index === mainImageIndex" class="main-badge">主图</div>
                    </div>
                    <div class="image-controls">
                      <div class="drag-handle">
                        <el-icon><Rank /></el-icon>
                        <span>拖动排序</span>
                      </div>
                      <div class="order-buttons">
                        <el-button
                          type="primary"
                          circle
                          size="small"
                          :disabled="index === 0"
                          @click="moveImage(index, 'up')"
                        >
                          <el-icon><ArrowUp /></el-icon>
                        </el-button>
                        <el-button
                          type="primary"
                          circle
                          size="small"
                          :disabled="index === fileList.length - 1"
                          @click="moveImage(index, 'down')"
                        >
                          <el-icon><ArrowDown /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </el-form-item>

        <!-- 上传进度条 -->
        <el-form-item v-if="showProgress">
          <div class="upload-progress">
            <span class="progress-text">上传进度: {{ uploadProgress }}%</span>
            <el-progress :percentage="uploadProgress" :stroke-width="15" status="success" />
          </div>
        </el-form-item>

        <el-form-item>
          <div class="form-buttons">
            <template v-if="isOwner">
              <el-button type="primary" @click="handleSubmit" :loading="isSubmitting">修改</el-button>
              <el-button @click="goBack">取消</el-button>
            </template>
            <template v-else>
              <el-button @click="goBack">返回</el-button>
            </template>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Picture,
  Delete,
  Plus,
  Star,
  StarFilled,
  ArrowUp,
  ArrowDown,
  Rank,
  InfoFilled,
  Close
} from '@element-plus/icons-vue'
import { getFoundItemDetail, updateFoundItem, updateFoundItemImages } from '@/api/found'
import draggable from 'vuedraggable'

const route = useRoute()
const router = useRouter()
const formRef = ref(null)
const fileList = ref([])
const loading = ref(false)
const mainImageIndex = ref(0)
const isSubmitting = ref(false)
const isUpdatingImages = ref(false)
const uploadProgress = ref(0)
const showProgress = ref(false)

// 获取当前用户ID
const currentUserId = ref(JSON.parse(localStorage.getItem('userInfo'))?.id || '')

// 表单数据
const form = reactive({
  id: '',
  itemName: '',
  description: '',
  foundLocation: '',
  foundTime: '',
  imageUrl: '',
  userId: '',
  username: '',
  createdAt: '',
  imageUrls: [], // 所有图片URL列表
  itemImages: []  // 额外图片对象列表
})

// 图片管理数据
const imageManagement = reactive({
  newImages: [], // 新上传的图片
  retainImageIds: [], // 需要保留的图片ID
  deletedImageIds: [] // 需要删除的图片ID
})

// 判断是否为发布者
const isOwner = computed(() => {
  return form.userId === currentUserId.value
})

// 表单验证规则
const rules = {
  itemName: [
    { required: true, message: '请输入物品名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入物品描述', trigger: 'blur' }
  ],
  foundLocation: [
    { required: true, message: '请输入拾取地点', trigger: 'blur' }
  ],
  foundTime: [
    { required: true, message: '请选择拾取时间', trigger: 'change' }
  ]
}

// 获取拾物详情
const fetchFoundItemDetail = async () => {
  try {
    loading.value = true
    const response = await getFoundItemDetail(route.params.id)
    if (response.code === 200) {
      Object.assign(form, response.data)

      // 处理图片数据
      if (form.itemImages && form.itemImages.length > 0) {
        // 初始化保留图片ID列表
        imageManagement.retainImageIds = form.itemImages.map(img => img.id)

        // 确保主图不重复显示
        let imageUrls = [...form.imageUrls]
        if (form.imageUrl) {
          // 过滤掉与主图相同的URL，避免重复
          imageUrls = form.imageUrls.filter(url => url !== form.imageUrl)
          // 将主图放在第一位
          imageUrls = [form.imageUrl, ...imageUrls]
        }

        // 转换图片数据为文件列表格式
        fileList.value = imageUrls.map((url, index) => {
          return {
            name: `image-${index}.jpg`,
            url: url,
            uid: `existing-${index}`,
            status: 'success'
          }
        })

        // 主图已经放在第一位，所以索引为0
        if (form.imageUrl) {
          mainImageIndex.value = 0
        }
      }
    } else {
      ElMessage.error(response.message || '获取拾物详情失败')
    }
  } catch (error) {
    console.error('获取拾物详情失败：', error)
    ElMessage.error('获取拾物详情失败')
  } finally {
    loading.value = false
  }
}

// 文件上传前的验证
const beforeUpload = (file) => {
  const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJPGOrPNG) {
    ElMessage.error('只能上传 JPG 或 PNG 格式的图片！')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB！')
    return false
  }
  return true
}

// 处理图片选择
const handleImageChange = (file, fileList) => {
  // 更新图片列表
  fileList.value = fileList

  // 添加到新图片列表
  if (file.status === 'ready') {
    imageManagement.newImages.push(file)
  }
}

// 处理超出限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传5张图片')
}

// 处理移除图片
const handleRemove = (file, fileList) => {
  fileList.value = fileList

  // 如果是新上传的图片，从新图片列表中移除
  if (file.status === 'ready') {
    const index = imageManagement.newImages.findIndex(img => img.uid === file.uid)
    if (index !== -1) {
      imageManagement.newImages.splice(index, 1)
    }
  }
  // 如果是已有的图片，添加到删除列表
  else if (file.uid.startsWith('existing-')) {
    const index = parseInt(file.uid.split('-')[1])
    if (form.itemImages[index]) {
      imageManagement.deletedImageIds.push(form.itemImages[index].id)

      // 从保留列表中移除
      const retainIndex = imageManagement.retainImageIds.indexOf(form.itemImages[index].id)
      if (retainIndex !== -1) {
        imageManagement.retainImageIds.splice(retainIndex, 1)
      }
    }
  }

  // 如果删除的是主图，重置主图索引为0
  if (mainImageIndex.value >= fileList.value.length) {
    mainImageIndex.value = fileList.value.length > 0 ? 0 : 0
  }
}

// 手动移除图片
const removeImage = (index) => {
  // 如果删除的是主图，重置主图索引
  if (index === mainImageIndex.value) {
    mainImageIndex.value = fileList.value.length > 1 ? 0 : 0
  } else if (index < mainImageIndex.value) {
    // 如果删除的图片在主图之前，主图索引需要减1
    mainImageIndex.value--
  }

  // 处理删除逻辑
  const file = fileList.value[index]
  if (file.uid.startsWith('existing-')) {
    const existingIndex = parseInt(file.uid.split('-')[1])
    if (form.itemImages[existingIndex]) {
      imageManagement.deletedImageIds.push(form.itemImages[existingIndex].id)

      // 从保留列表中移除
      const retainIndex = imageManagement.retainImageIds.indexOf(form.itemImages[existingIndex].id)
      if (retainIndex !== -1) {
        imageManagement.retainImageIds.splice(retainIndex, 1)
      }
    }
  } else {
    // 如果是新上传的图片，从新图片列表中移除
    const newIndex = imageManagement.newImages.findIndex(img => img.uid === file.uid)
    if (newIndex !== -1) {
      imageManagement.newImages.splice(newIndex, 1)
    }
  }

  // 从列表中移除图片
  fileList.value.splice(index, 1)
}

// 设置主图
const setAsMainImage = (index) => {
  mainImageIndex.value = index
  ElMessage.success('已设置为主图')
}

// 移动图片位置
const moveImage = (index, direction) => {
  if (direction === 'up' && index > 0) {
    // 向上移动
    const temp = fileList.value[index]
    fileList.value[index] = fileList.value[index - 1]
    fileList.value[index - 1] = temp

    // 如果移动的是主图或移动到主图位置，更新主图索引
    if (index === mainImageIndex.value) {
      mainImageIndex.value--
    } else if (index - 1 === mainImageIndex.value) {
      mainImageIndex.value++
    }
  } else if (direction === 'down' && index < fileList.value.length - 1) {
    // 向下移动
    const temp = fileList.value[index]
    fileList.value[index] = fileList.value[index + 1]
    fileList.value[index + 1] = temp

    // 如果移动的是主图或移动到主图位置，更新主图索引
    if (index === mainImageIndex.value) {
      mainImageIndex.value++
    } else if (index + 1 === mainImageIndex.value) {
      mainImageIndex.value--
    }
  }
}

// 拖拽结束后处理
const handleDragEnd = () => {
  // 找到主图的新位置
  const mainImage = fileList.value[mainImageIndex.value]
  const newIndex = fileList.value.findIndex(img => img.uid === mainImage.uid)
  if (newIndex !== -1 && newIndex !== mainImageIndex.value) {
    mainImageIndex.value = newIndex
  }
}

// 获取预览图片列表
const getPreviewList = () => {
  return fileList.value.map(file => file.url)
}



// 更新图片
const updateImages = async () => {
  if (fileList.value.length === 0 && imageManagement.deletedImageIds.length === 0) {
    return true // 没有图片变更，直接返回成功
  }

  try {
    isUpdatingImages.value = true

    // 显示上传进度条
    showProgress.value = true
    uploadProgress.value = 0

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.floor(Math.random() * 10) + 1
      }
    }, 200)

    // 准备图片更新数据
    const formData = new FormData()

    // 添加主图
    if (fileList.value.length > 0) {
      const mainImage = fileList.value[mainImageIndex.value]
      if (mainImage.raw) {
        formData.append('mainImage', mainImage.raw)
      }
    }

    // 添加额外图片（除主图外的所有图片）
    fileList.value.forEach((file, index) => {
      if (index !== mainImageIndex.value && file.raw) {
        formData.append('additionalImages', file.raw)
      }
    })

    // 添加需要保留的图片ID
    imageManagement.retainImageIds.forEach(id => {
      formData.append('retainImageIds', id)
    })

    try {
      // 更新图片
      const response = await updateFoundItemImages(form.id, formData)

      // 完成进度
      uploadProgress.value = 100

      if (response.code === 200) {
        ElMessage.success('图片更新成功')
        return true
      } else {
        ElMessage.error(response.message || '图片更新失败')
        return false
      }
    } finally {
      // 清除进度条定时器
      clearInterval(progressInterval)

      // 3秒后隐藏进度条
      setTimeout(() => {
        showProgress.value = false
      }, 3000)
    }
  } catch (error) {
    console.error('图片更新失败：', error)
    ElMessage.error('图片更新失败')
    return false
  } finally {
    isUpdatingImages.value = false
  }
}

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    isSubmitting.value = true

    // 准备更新数据
    const formData = new FormData()
    formData.append('itemName', form.itemName)
    formData.append('description', form.description)
    formData.append('foundLocation', form.foundLocation)
    formData.append('foundTime', form.foundTime)

    // 更新基本信息
    const response = await updateFoundItem(form.id, formData)
    if (response.code === 200) {
      // 更新图片
      const imageUpdateSuccess = await updateImages()

      if (imageUpdateSuccess) {
        ElMessage.success('修改成功')
        router.push('/found-items')
      }
    } else {
      ElMessage.error(response.message || '修改失败')
    }
  } catch (error) {
    console.error('修改失败：', error)
    ElMessage.error(error.message || '修改失败')
  } finally {
    isSubmitting.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.push('/found-items')
}

onMounted(() => {
  fetchFoundItemDetail()
})
</script>

<style scoped>
.edit-found-item {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  margin: 0;
  font-size: 20px;
  color: #303133;
}

.edit-form {
  padding: 20px;
}

.found-image-uploader :deep(.el-upload-list--picture-card .el-upload-list__item-thumbnail) {
  object-fit: cover;
}

.upload-tip {
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
  text-align: left;
}

.upload-tip-small {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

/* 图片管理区域样式 */
.image-management {
  margin-top: 20px;
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  padding: 15px;
  background-color: #f8f9fa;
}

.image-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.image-management-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.image-item {
  width: 200px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  background: white;
  transition: all 0.3s;
}

.image-item.is-main {
  border: 2px solid #E6A23C;
  box-shadow: 0 4px 16px 0 rgba(230, 162, 60, 0.3);
}

.image-preview {
  height: 150px;
  position: relative;
}

.image-preview .el-image {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  padding: 8px;
  display: flex;
  justify-content: space-around;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-actions {
  opacity: 1;
}

.main-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #E6A23C;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.image-controls {
  padding: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f5f7fa;
}

.drag-handle {
  display: flex;
  align-items: center;
  cursor: move;
  color: #606266;
  font-size: 12px;
}

.drag-handle .el-icon {
  margin-right: 5px;
}

.order-buttons {
  display: flex;
  gap: 5px;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #909399;
}

.image-placeholder .el-icon {
  font-size: 24px;
}

@media screen and (max-width: 768px) {
  .edit-form {
    padding: 16px;
  }

  .image-item {
    width: 100%;
  }

  .edit-found-item {
    padding: 12px;
  }

  .form-card {
    margin: 0;
  }

  :deep(.el-form-item__label) {
    width: 80px !important;
  }
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 上传进度条样式 */
.upload-progress {
  margin: 15px 0;
  width: 100%;
}

.progress-text {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}
</style>