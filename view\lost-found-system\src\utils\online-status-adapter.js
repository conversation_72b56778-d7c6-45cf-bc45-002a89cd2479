/**
 * 在线状态适配器
 * 
 * 这个文件提供了与旧代码兼容的接口，但内部使用新的在线状态服务
 * 用于平滑过渡到新的在线状态管理方式
 */

import { 
  isUserOnline as serviceIsUserOnline,
  getOnlineUsers as serviceGetOnlineUsers,
  getOnlineUserCount as serviceGetOnlineUserCount,
  getUserLastActiveTime as serviceGetUserLastActiveTime,
  checkUserOnlineStatus as serviceCheckUserOnlineStatus
} from '@/services/onlineStatusService'

/**
 * 检查用户是否在线
 * @param {string} userId 用户ID
 * @returns {boolean} 是否在线
 */
export function isUserOnline(userId) {
  return serviceIsUserOnline(userId)
}

/**
 * 获取所有在线用户
 * @returns {Object} 在线用户对象 {userId: true}
 */
export function getOnlineUsers() {
  const usersList = serviceGetOnlineUsers()
  
  // 转换为旧格式 {userId: true}
  const result = {}
  usersList.forEach(userId => {
    result[userId] = true
  })
  
  return result
}

/**
 * 获取在线用户数量
 * @returns {number} 在线用户数量
 */
export function getOnlineUserCount() {
  return serviceGetOnlineUserCount()
}

/**
 * 获取用户最后活跃时间
 * @param {string} userId 用户ID
 * @returns {number|null} 最后活跃时间
 */
export function getUserLastActiveTime(userId) {
  return serviceGetUserLastActiveTime(userId)
}

/**
 * 检查用户在线状态
 * @param {string} userId 用户ID
 * @returns {Promise<boolean>} 是否在线
 */
export function checkUserOnline(userId) {
  // 先检查本地缓存
  const isOnline = isUserOnline(userId)
  
  // 同时发送请求更新状态
  serviceCheckUserOnlineStatus(userId)
  
  // 返回当前状态
  return Promise.resolve(isOnline)
}

// 导出默认对象，兼容旧的导入方式
export default {
  isUserOnline,
  getOnlineUsers,
  getOnlineUserCount,
  getUserLastActiveTime,
  checkUserOnline
}
