package com.tomato.lostfoundsystem.event;

import com.tomato.lostfoundsystem.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 通知事件监听器
 * 用于异步处理通知相关事件
 */
@Slf4j
@Component
public class NotificationEventListener {
    
    @Autowired
    private NotificationService notificationService;
    
    /**
     * 处理物品审核通过事件
     * 异步发送审核通过通知
     * 
     * @param event 物品审核通过事件
     */
    @Async
    @EventListener
    public void handleItemApprovedEvent(ItemApprovedEvent event) {
        try {
            log.info("【通知】开始异步处理审核通过通知 - 用户ID: {}, 物品类型: {}, 物品ID: {}", 
                    event.getUserId(), event.getItemType(), event.getItemId());
            
            notificationService.sendApprovalNotification(
                    event.getUserId(), event.getItemType(), event.getItemId());
            
            log.info("【通知】异步发送审核通过通知成功 - 用户ID: {}, 物品类型: {}, 物品ID: {}", 
                    event.getUserId(), event.getItemType(), event.getItemId());
        } catch (Exception e) {
            log.error("【通知】异步发送审核通过通知失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理物品审核拒绝事件
     * 异步发送审核拒绝通知
     * 
     * @param event 物品审核拒绝事件
     */
    @Async
    @EventListener
    public void handleItemRejectedEvent(ItemRejectedEvent event) {
        try {
            log.info("【通知】开始异步处理审核拒绝通知 - 用户ID: {}, 物品类型: {}, 物品ID: {}, 原因: {}", 
                    event.getUserId(), event.getItemType(), event.getItemId(), event.getRemarks());
            
            notificationService.sendRejectionNotification(
                    event.getUserId(), event.getItemType(), event.getItemId(), event.getRemarks());
            
            log.info("【通知】异步发送审核拒绝通知成功 - 用户ID: {}, 物品类型: {}, 物品ID: {}", 
                    event.getUserId(), event.getItemType(), event.getItemId());
        } catch (Exception e) {
            log.error("【通知】异步发送审核拒绝通知失败: {}", e.getMessage(), e);
        }
    }
}
