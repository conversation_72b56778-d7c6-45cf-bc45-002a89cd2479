package com.tomato.lostfoundsystem.entity;

import com.tomato.lostfoundsystem.enums.MessageType;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ChatMessage {
    private Long id;               // 消息ID
    private Long senderId;         // 发送者ID
    private Long receiverId;       // 接收者ID
    private String message;        // 消息内容
    private Date timestamp;        // 消息时间戳
    private MessageType messageType;       // 消息类型（0: TEXT, 1: IMAGE, 2: AUDIO, 3: VIDEO 等）
    private Integer audioDuration; // 音频时长（秒）
    private Integer videoDuration; // 视频时长（秒）

    // 单个附件（向后兼容）
    private MessageAttachment Attachment;

    // 多个附件
    private List<MessageAttachment> attachments;
}