# 服务启动脚本说明

**注意：** 所有服务启动脚本已移至项目根目录下的`scripts`文件夹中。

请使用`scripts`目录中的脚本来启动和停止Redis、Kafka和智能匹配服务。

## 脚本位置

所有服务启动脚本现在位于与`server`和`view`同级的`scripts`目录中：

```
项目根目录
├── scripts/           # 服务启动脚本目录
│   ├── config.bat     # 配置文件
│   ├── edit_config.bat
│   ├── start_redis.bat
│   ├── start_kafka.bat
│   ├── start_clip_service.bat
│   ├── start_all_services.bat
│   ├── stop_all_services.bat
│   ├── create_shortcuts.bat
│   └── 服务启动说明.md
├── server/            # 后端服务目录
└── view/              # 前端视图目录
    └── lost-found-system/
```

## 使用方法

1. 进入`scripts`目录
2. 运行`edit_config.bat`修改配置
3. 运行`create_shortcuts.bat`创建桌面快捷方式
4. 使用桌面快捷方式或直接运行`start_all_services.bat`启动所有服务

详细说明请参阅`scripts`目录中的`服务启动说明.md`文件。
