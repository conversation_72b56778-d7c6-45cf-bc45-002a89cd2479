import request from '@/utils/request'

// 失物管理相关接口
export const lostItemAPI = {
  // 获取失物列表
  getList: (params) => request({
    url: '/admin/lost-items',
    method: 'get',
    params: {
      page: params.page,
      size: params.size,
      keyword: params.keyword,
      status: params.status,
      auditStatus: params.auditStatus,
      startDate: params.startDate,
      endDate: params.endDate
    }
  }),

  // 审核通过
  audit: (id, data) => request({
    url: `/admin/audit/lost-item/${id}`,
    method: 'put',
    data
  }),

  // 标记找回状态
  updateStatus: (id, data) => request({
    url: `/admin/lost-items/${id}/status`,
    method: 'put',
    data
  }),

  // 删除失物
  delete: (id) => request({
    url: `/admin/lost-items/${id}`,
    method: 'delete'
  })
}

// 拾物管理相关接口
export const foundItemAPI = {
  // 获取拾物列表
  getList: (params) => request({
    url: '/admin/found-items',
    method: 'get',
    params: {
      page: params.page,
      size: params.size,
      keyword: params.keyword,
      status: params.status,
      auditStatus: params.auditStatus,
      startDate: params.startDate,
      endDate: params.endDate
    }
  }),

  // 审核拾物
  audit: (id, data) => request({
    url: `/admin/audit/found-item/${id}`,
    method: 'put',
    data,
    // 添加错误处理器，记录更详细的错误信息
    errorHandler: (error) => {
      console.error('审核拾物失败详情:', error);
      if (error.response) {
        console.error('错误状态码:', error.response.status);
        console.error('错误数据:', error.response.data);
      }
      return Promise.reject(error);
    }
  }),

  // 标记认领状态
  updateStatus: (id, data) => request({
    url: `/admin/found-items/${id}/status`,
    method: 'put',
    data
  }),

  // 删除拾物
  delete: (id) => request({
    url: `/admin/found-items/${id}`,
    method: 'delete'
  })
}

// 用户管理相关接口
export const userAPI = {
  // 获取用户列表
  getList: (params) => request({
    url: '/admin/users',
    method: 'get',
    params
  }),

  // 更新用户状态
  updateStatus: (id, data) => request({
    url: `/admin/users/${id}/status`,
    method: 'put',
    data
  }),

  // 更新用户角色
  updateRole: (id, data) => request({
    url: `/admin/users/${id}/role`,
    method: 'put',
    data
  }),

  // 重置密码
  resetPassword: (id, data) => request({
    url: `/admin/users/${id}/reset-password`,
    method: 'put',
    data
  })
}