/**
 * WebSocket 消息发送修复工具
 * 用于解决 WebSocket 消息发送问题
 */
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores';
import { Client } from '@stomp/stompjs';
import SockJS from 'sockjs-client/dist/sockjs.min.js';
import { sendPrivateMessage } from '@/api/chat';

// 状态
let stompClient = null;
let connected = false;
let subscribed = false;
let subscriptions = [];

/**
 * 初始化 WebSocket 连接
 * @returns {Promise<boolean>} 是否连接成功
 */
export async function initializeWebSocket() {
  console.log('开始初始化 WebSocket 连接...');

  // 如果已连接，直接返回
  if (connected && stompClient?.connected) {
    console.log('WebSocket 已连接，无需重新初始化');
    return true;
  }

  try {
    // 获取用户 Token
    const userStore = useUserStore();
    const token = userStore.token;

    if (!token) {
      console.error('未找到用户 Token，无法连接 WebSocket');
      ElMessage.error('未找到用户 Token，请先登录');
      return false;
    }

    // 创建 SockJS 实例
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081';
    const wsUrl = `${apiBaseUrl}/ws?token=${encodeURIComponent(token)}&t=${Date.now()}`;
    console.log('WebSocket URL:', wsUrl.replace(token, '***'));
    const socket = new SockJS(wsUrl);

    // 创建 STOMP 客户端
    stompClient = new Client({
      webSocketFactory: () => socket,
      connectHeaders: {
        Authorization: `Bearer ${token}`
      },
      debug: (str) => {
        if (process.env.NODE_ENV === 'development') {
          console.debug(str);
        }
      },
      reconnectDelay: 0, // 禁用内置重连，使用我们自己的重连逻辑
      heartbeatIncoming: 25000, // 25秒，与后端application.yml中的设置保持一致
      heartbeatOutgoing: 25000 // 25秒，与后端application.yml中的设置保持一致
    });

    // 创建连接 Promise
    return new Promise((resolve) => {
      // 连接成功回调
      stompClient.onConnect = (frame) => {
        console.log('WebSocket 连接成功:', frame);
        connected = true;

        // 订阅主题
        subscribeToTopics()
          .then(() => {
            console.log('WebSocket 主题订阅成功');
            subscribed = true;
            resolve(true);
          })
          .catch((error) => {
            console.error('WebSocket 主题订阅失败:', error);
            resolve(false);
          });
      };

      // 连接错误回调
      stompClient.onStompError = (frame) => {
        console.error('STOMP 错误:', frame);
        connected = false;
        subscribed = false;
        resolve(false);
      };

      // 连接断开回调
      stompClient.onWebSocketClose = (event) => {
        console.warn('WebSocket 连接关闭:', event);
        connected = false;
        subscribed = false;
      };

      // 激活连接
      stompClient.activate();
      console.log('WebSocket 连接请求已发送');
    });
  } catch (error) {
    console.error('初始化 WebSocket 连接失败:', error);
    connected = false;
    return false;
  }
}

/**
 * 订阅 WebSocket 主题
 * @returns {Promise<boolean>} 是否订阅成功
 */
async function subscribeToTopics() {
  if (!stompClient || !stompClient.connected) {
    console.error('WebSocket 未连接，无法订阅主题');
    return false;
  }

  try {
    // 取消之前的订阅
    subscriptions.forEach(sub => {
      try {
        sub.unsubscribe();
      } catch (error) {
        console.warn('取消订阅失败:', error);
      }
    });
    subscriptions = [];

    // 获取用户 ID
    const userStore = useUserStore();
    const userId = userStore.userInfo?.id;

    if (!userId) {
      console.error('未找到用户 ID，无法订阅主题');
      return false;
    }

    console.log('开始订阅主题...');

    // 订阅私人消息
    const privateSubscription = stompClient.subscribe('/user/queue/private', (message) => {
      console.log('收到私人消息:', message.headers.destination);
      try {
        const data = JSON.parse(message.body);
        console.log('私人消息内容:', data);

        // 触发全局事件
        window.dispatchEvent(new CustomEvent('chat-message', {
          detail: data
        }));
      } catch (error) {
        console.error('解析私人消息失败:', error);
      }
    });
    subscriptions.push(privateSubscription);
    console.log('已订阅私人消息: /user/queue/private');

    // 订阅发送确认
    const sentSubscription = stompClient.subscribe('/user/queue/sent', (message) => {
      console.log('收到发送确认:', message.headers.destination);
      try {
        const data = JSON.parse(message.body);
        console.log('发送确认内容:', data);

        // 触发全局事件
        window.dispatchEvent(new CustomEvent('message-sent', {
          detail: {
            message: data,
            tempId: data.clientMessageId
          }
        }));
      } catch (error) {
        console.error('解析发送确认失败:', error);
      }
    });
    subscriptions.push(sentSubscription);
    console.log('已订阅发送确认: /user/queue/sent');

    return true;
  } catch (error) {
    console.error('订阅主题失败:', error);
    return false;
  }
}

/**
 * 发送聊天消息
 * @param {Object} message 消息对象
 * @param {File} file 文件对象（可选）
 * @returns {Promise<Object>} 发送结果
 */
export async function sendChatMessage(message, file = null) {
  console.log('准备发送聊天消息:', message);

  // 确保 WebSocket 已连接
  if (!connected || !stompClient?.connected) {
    console.log('WebSocket 未连接，尝试重新连接...');
    const initialized = await initializeWebSocket();
    if (!initialized) {
      console.error('WebSocket 连接失败，无法发送消息');
      return {
        code: 500,
        message: 'WebSocket 连接失败，无法发送消息',
        data: null
      };
    }
  }

  try {
    // 如果有文件，使用 FormData 上传
    if (file) {
      console.log('检测到文件，使用 FormData 上传');

      // 检查WebSocket连接状态
      if (!stompClient || !stompClient.connected) {
        console.error('WebSocket未连接，无法上传文件');
        return {
          code: 500,
          message: 'WebSocket未连接，无法上传文件',
          data: null
        };
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('senderId', message.senderId);
      formData.append('receiverId', message.receiverId);
      formData.append('message', message.message || '');
      formData.append('messageType', message.messageType || 'FILE');

      if (message.clientMessageId) {
        formData.append('clientMessageId', message.clientMessageId);
      }

      // 添加标记，表示这是文件上传，避免创建重复的离线消息
      window._fileMessageSent = true;
      // 添加上传中标记，防止错误提示覆盖成功状态
      window._uploadingFile = true;
      console.log('设置文件消息已发送标记和上传中标记');

      // 延迟清除标记
      setTimeout(() => {
        window._fileMessageSent = false;
        console.log('清除文件消息已发送标记');
      }, 5000); // 增加到5秒，确保有足够时间完成上传

      // 使用chat.js中的sendPrivateMessage函数
      console.log('使用chat.js中的sendPrivateMessage函数发送文件消息');
      try {
        const result = await sendPrivateMessage(formData);
        // 清除上传中标记
        setTimeout(() => {
          window._uploadingFile = false;
          console.log('文件上传完成，清除上传中标记');
        }, 500);
        return result;
      } catch (error) {
        // 清除上传中标记
        window._uploadingFile = false;
        console.log('文件上传失败，立即清除上传中标记');
        throw error; // 重新抛出错误，让外层catch捕获
      }
    } else {
      // 普通文本消息，使用 WebSocket 发送
      console.log('发送文本消息');

      // 添加时间戳和客户端消息 ID
      const messageToSend = {
        ...message,
        // 强制确保 timestamp 是数字类型（毫秒时间戳）
        timestamp: Date.now(), // 始终使用当前时间的毫秒时间戳
        clientMessageId: message.clientMessageId || `temp-${Date.now()}`
      };

      // 记录详细日志
      console.log('发送消息前的时间戳处理:', {
        originalTimestamp: message.timestamp,
        originalType: typeof message.timestamp,
        newTimestamp: messageToSend.timestamp,
        newType: typeof messageToSend.timestamp
      });

      // 发送消息
      stompClient.publish({
        destination: '/app/privateMessage',
        headers: {
          'content-type': 'application/json'
        },
        body: JSON.stringify(messageToSend)
      });

      console.log('文本消息已发送');

      // 返回成功响应
      return {
        code: 200,
        message: '消息已发送',
        data: {
          id: messageToSend.clientMessageId,
          timestamp: messageToSend.timestamp,
          ...messageToSend
        }
      };
    }
  } catch (error) {
    console.error('发送消息失败:', error);
    // 清除上传中标记
    if (window._uploadingFile) {
      setTimeout(() => {
        window._uploadingFile = false;
        console.log('由于错误，清除上传中标记');
      }, 500); // 延迟清除，确保错误处理完成
    }
    return {
      code: 500,
      message: error.message || '发送消息失败',
      data: null
    };
  }
}
