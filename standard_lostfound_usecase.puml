@startuml 校园失物招领系统用例图

' 设置全局样式 - 参考标准图
skinparam backgroundColor white
skinparam defaultFontName SimSun
skinparam defaultFontSize 12
skinparam usecase {
  BackgroundColor white
  BorderColor black
  ArrowColor black
  FontName SimSun
  FontSize 12
  shadowing false
}
skinparam actor {
  BackgroundColor white
  BorderColor black
  FontName SimSun
  FontSize 12
  shadowing false
}
skinparam rectangle {
  FontName SimSun
  FontSize 12
  shadowing false
  BorderColor black
}
skinparam defaultTextAlignment center
skinparam linetype polyline

' 定义布局方向
left to right direction

' 系统边界
rectangle "校园失物招领系统" {
  ' 用例 - 简洁命名
  usecase "注册" as Register
  usecase "登录" as Login
  usecase "发布失物信息" as PostLost
  usecase "发布拾物信息" as PostFound
  usecase "查询匹配结果" as QueryMatch
  usecase "即时通讯" as Chat
  usecase "用户管理" as UserManage
  usecase "物品审核" as ItemReview
  usecase "系统公告管理" as AnnouncementManage
}

' 左侧角色
actor "普通用户" as User

' 右侧角色
actor "管理员" as Admin
actor "超级管理员" as SuperAdmin

' 普通用户关系
User --> Register
User --> Login
User --> PostLost
User --> PostFound
User --> QueryMatch
User --> Chat

' 管理员关系
Admin --> ItemReview
Admin --> UserManage
Admin --> AnnouncementManage

' 超级管理员继承管理员（子类指向父类）
SuperAdmin --|> Admin

' 包含和扩展关系
PostLost ..> QueryMatch : <<include>>
PostFound ..> QueryMatch : <<include>>
QueryMatch ..> Chat : <<extend>>
Login ..> UserManage : <<include>>

@enduml
