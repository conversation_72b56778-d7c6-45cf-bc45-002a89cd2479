package com.tomato.lostfoundsystem.service.Impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.model.kafka.ChatMessageEnvelope;
import com.tomato.lostfoundsystem.service.ConversationService;
import com.tomato.lostfoundsystem.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 聊天消息处理器
 * 负责从 Kafka 消费消息并处理
 */
@Slf4j
@Service
public class ChatMessageProcessor {

    private final SimpMessagingTemplate messagingTemplate;
    private final ObjectMapper objectMapper;
    private final RedisService redisService;
    private final MessageRetryService messageRetryService;
    private final ConversationService conversationService;

    @Autowired
    private com.tomato.lostfoundsystem.mapper.UserMapper userMapper;

    // 用于存储已处理的消息ID，防止重复处理
    private final Map<String, Long> processedMessages = new ConcurrentHashMap<>();

    @Autowired
    public ChatMessageProcessor(
            SimpMessagingTemplate messagingTemplate,
            RedisService redisService,
            MessageRetryService messageRetryService,
            ConversationService conversationService) {
        this.messagingTemplate = messagingTemplate;
        this.redisService = redisService;
        this.messageRetryService = messageRetryService;
        this.conversationService = conversationService;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 监听聊天消息主题
     */
    @KafkaListener(topics = "chat-topic", groupId = "chat-processor-group")
    public void processChatMessage(
            String message,
            @Header("kafka_receivedPartitionId") int partition,
            @Header("kafka_offset") long offset,
            Acknowledgment acknowledgment) {
        try {
            log.info("【聊天处理器】收到消息: 分区={}, 偏移量={}", partition, offset);

            // 首先尝试判断消息是否为纯文本
            if (message.trim().startsWith("{")) {
                // 看起来是JSON格式，尝试解析
                try {
                    // 首先尝试解析为ChatMessageEnvelope
                    ChatMessageEnvelope envelope;
                    MessageDTO messageDTO;

                    try {
                        // 尝试解析为ChatMessageEnvelope
                        envelope = objectMapper.readValue(message, ChatMessageEnvelope.class);
                        log.info("【聊天处理器】成功解析为ChatMessageEnvelope格式");

                        // 从信封中提取payload
                        messageDTO = envelope.getPayload();
                        if (messageDTO == null) {
                            log.error("【聊天处理器】ChatMessageEnvelope中的payload为空");
                            throw new IllegalStateException("ChatMessageEnvelope中的payload为空");
                        }
                    } catch (Exception envelopeException) {
                        log.info("【聊天处理器】消息不是ChatMessageEnvelope格式，尝试直接解析为MessageDTO: {}", envelopeException.getMessage());

                        // 如果解析为ChatMessageEnvelope失败，尝试直接解析为MessageDTO
                        messageDTO = objectMapper.readValue(message, MessageDTO.class);
                        log.info("【聊天处理器】成功解析为MessageDTO格式");

                        // 如果成功解析为MessageDTO，创建一个新的ChatMessageEnvelope
                        envelope = ChatMessageEnvelope.create(messageDTO);
                    }

                    // 检查消息是否已处理（幂等性检查）
                    // 添加空值检查和日志记录
                    String messageId = envelope.getMessageId();
                    if (messageId == null) {
                        log.warn("【聊天处理器】消息ID为空，无法进行幂等性检查");
                        // 生成一个临时ID用于日志
                        messageId = "temp-" + System.currentTimeMillis();
                        envelope.setMessageId(messageId);
                    }

                    try {
                        // 检查消息ID是否已处理
                        if (isMessageProcessed(messageId)) {
                            log.info("【聊天处理器】消息已处理，跳过: messageId={}", messageId);
                            acknowledgment.acknowledge();
                            return;
                        }

                        // 检查客户端消息ID是否已处理
                        String clientMessageId = messageDTO.getClientMessageId();
                        if (clientMessageId != null && !clientMessageId.isEmpty()) {
                            String clientIdempotencyKey = "message:processed:client:" + clientMessageId;
                            if (redisService.hasKey(clientIdempotencyKey)) {
                                log.info("【聊天处理器】客户端消息ID已处理，跳过: clientMessageId={}", clientMessageId);

                                // 标记服务器消息ID为已处理，避免重复处理
                                markMessageAsProcessed(messageId);

                                acknowledgment.acknowledge();
                                return;
                            }
                        }
                    } catch (Exception e) {
                        log.error("【聊天处理器】检查消息是否已处理时出错: {}", e.getMessage(), e);
                        // 继续处理，不要因为幂等性检查失败而阻止消息处理
                    }

                    // 更新消息状态为处理中
                    envelope.setStatus(ChatMessageEnvelope.MessageStatus.PROCESSING);

                    // messageDTO 已经在前面的代码中获取

                    // 检查接收者是否在线
                    Long receiverId = messageDTO.getReceiverId();
                    boolean isReceiverOnline = redisService.isUserOnline(receiverId);

                    if (isReceiverOnline) {
                        // 接收者在线，推送消息
                        log.info("【聊天处理器】接收者在线，推送消息: 接收者={}", receiverId);

                        // 推送给接收者 - 使用标准路径 /queue/private
                        // 获取接收者的用户名
                        String receiverUsername = getUsernameById(receiverId);
                        if (receiverUsername != null) {
                            messagingTemplate.convertAndSendToUser(
                                receiverUsername,  // 使用用户名而不是用户ID
                                "/queue/private",
                                messageDTO
                            );
                            log.info("【聊天处理器】推送消息: 用户名={}", receiverUsername);
                        } else {
                            log.error("【聊天处理器】无法获取接收者用户名，使用ID推送: {}", receiverId);
                            // 回退到使用ID
                            messagingTemplate.convertAndSendToUser(
                                receiverId.toString(),
                                "/queue/private",
                                messageDTO
                            );
                        }

                        // 更新消息状态为已送达
                        envelope.setStatus(ChatMessageEnvelope.MessageStatus.DELIVERED);

                        // 更新会话信息
                        try {
                            // 创建 ChatMessage 对象
                            ChatMessage chatMessage = new ChatMessage();
                            chatMessage.setId(messageDTO.getId());
                            chatMessage.setSenderId(messageDTO.getSenderId());
                            chatMessage.setReceiverId(messageDTO.getReceiverId());
                            chatMessage.setMessage(messageDTO.getMessage());
                            chatMessage.setMessageType(messageDTO.getMessageType());
                            chatMessage.setTimestamp(new Date(messageDTO.getTimestamp()));

                            // 更新会话的最后一条消息信息
                            conversationService.updateConversationWithMessage(chatMessage);
                            log.info("【聊天处理器】会话信息已更新");
                        } catch (Exception e) {
                            log.error("【聊天处理器】更新会话信息失败: {}", e.getMessage(), e);
                        }

                        // 标记消息为已处理
                        try {
                            // 获取客户端消息ID
                            String clientMessageId = messageDTO.getClientMessageId();
                            if (clientMessageId != null && !clientMessageId.isEmpty()) {
                                // 同时标记服务器消息ID和客户端消息ID
                                markMessageAsProcessed(envelope.getMessageId(), clientMessageId);
                                log.info("【聊天处理器】已标记消息和客户端消息ID为已处理: messageId={}, clientMessageId={}",
                                    envelope.getMessageId(), clientMessageId);
                            } else {
                                // 只标记服务器消息ID
                                markMessageAsProcessed(envelope.getMessageId());
                                log.info("【聊天处理器】已标记消息为已处理: messageId={}", envelope.getMessageId());
                            }
                        } catch (Exception e) {
                            log.error("【聊天处理器】标记消息为已处理时出错: {}", e.getMessage(), e);
                            // 继续处理，不要因为标记失败而阻止消息确认
                        }

                        // 确认消息
                        acknowledgment.acknowledge();

                        log.info("【聊天处理器】消息推送成功: 消息ID={}, 接收者={}", envelope.getMessageId(), receiverId);
                    } else {
                        // 接收者离线，保留消息在Kafka中
                        log.info("【聊天处理器】接收者离线，保留消息在Kafka中: 接收者={}", receiverId);

                        // 不确认消息，让它重新消费
                        // 但是我们需要防止无限重试，所以增加重试次数
                        if (envelope.getRetryCount() >= 5) {
                            log.warn("【聊天处理器】消息重试次数过多，确认消息: 消息ID={}, 重试次数={}",
                                    envelope.getMessageId(), envelope.getRetryCount());

                            // 标记消息为已处理
                            try {
                                // 获取客户端消息ID
                                String clientMessageId = messageDTO.getClientMessageId();
                                if (clientMessageId != null && !clientMessageId.isEmpty()) {
                                    // 同时标记服务器消息ID和客户端消息ID
                                    markMessageAsProcessed(envelope.getMessageId(), clientMessageId);
                                    log.info("【聊天处理器】重试次数过多，已标记消息和客户端消息ID为已处理: messageId={}, clientMessageId={}",
                                        envelope.getMessageId(), clientMessageId);
                                } else {
                                    // 只标记服务器消息ID
                                    markMessageAsProcessed(envelope.getMessageId());
                                    log.info("【聊天处理器】重试次数过多，已标记消息为已处理: messageId={}", envelope.getMessageId());
                                }
                            } catch (Exception e) {
                                log.error("【聊天处理器】标记消息为已处理时出错: {}", e.getMessage(), e);
                                // 继续处理，不要因为标记失败而阻止消息确认
                            }

                            // 确认消息
                            acknowledgment.acknowledge();
                        } else {
                            // 增加重试次数
                            envelope.setRetryCount(envelope.getRetryCount() + 1);

                            // 使用重试服务进行延迟重试
                            try {
                                String jsonMessage = objectMapper.writeValueAsString(envelope);
                                messageRetryService.scheduleRetry(jsonMessage, envelope.getRetryCount());

                                // 确认当前消息，因为我们已经调度了重试
                                acknowledgment.acknowledge();

                                log.info("【聊天处理器】消息已调度重试: 消息ID={}, 重试次数={}",
                                        envelope.getMessageId(), envelope.getRetryCount());
                            } catch (Exception e) {
                                log.error("【聊天处理器】调度重试失败: {}", e.getMessage(), e);
                                // 不确认消息，让Kafka重新消费
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("【聊天处理器】处理消息失败: {}", e.getMessage(), e);

                    // 检查是否是JSON解析错误或未知字段错误
                    if (e instanceof com.fasterxml.jackson.core.JsonParseException ||
                        e instanceof com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException) {
                        // 这可能是旧格式的消息或纯文本消息，直接确认并跳过
                        log.warn("【聊天处理器】检测到旧格式消息或无效消息，确认并跳过: {}", message);
                        acknowledgment.acknowledge();
                    }
                    // 根据错误类型决定是否确认
                    else if (isRecoverableError(e)) {
                        log.warn("【聊天处理器】可恢复错误，消息将重新消费");
                        // 不确认消息，让它重新消费
                    } else {
                        log.error("【聊天处理器】不可恢复错误，确认消息并记录失败");
                        acknowledgment.acknowledge();
                    }
                }
            }
        } catch (Exception e) {
            log.error("【聊天处理器】处理消息时发生未捕获的异常: {}", e.getMessage(), e);
            // 确认消息，避免无限重试
            acknowledgment.acknowledge();
        }
    }

    /**
     * 检查消息是否已处理
     * @param messageId 消息ID
     * @return 是否已处理
     */
    private boolean isMessageProcessed(String messageId) {
        if (messageId == null) {
            log.warn("【聊天处理器】检查消息是否已处理时收到空的消息ID");
            return false;
        }

        try {
            // 构建Redis键
            String redisKey = "message:processed:" + messageId;

            // 检查Redis中是否存在该键
            boolean exists = redisService.hasKey(redisKey);

            if (exists) {
                log.info("【聊天处理器】消息已在Redis中标记为已处理: {}", messageId);
                return true;
            }

            // 同时检查内存缓存（兼容旧逻辑）
            // 清理过期的已处理消息记录
            long now = System.currentTimeMillis();
            processedMessages.entrySet().removeIf(entry -> now - entry.getValue() > 3600000); // 1小时过期

            // 检查消息是否已处理
            boolean inMemory = processedMessages.containsKey(messageId);
            if (inMemory) {
                log.info("【聊天处理器】消息已在内存中标记为已处理: {}", messageId);

                // 同步到Redis
                redisService.set(redisKey, "1", 3600); // 1小时过期
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("【聊天处理器】检查消息是否已处理时出错: {}", e.getMessage(), e);
            return false; // 出错时默认为未处理，允许消息继续处理
        }
    }

    /**
     * 标记消息为已处理
     * @param messageId 消息ID
     */
    private void markMessageAsProcessed(String messageId) {
        if (messageId == null) {
            log.warn("【聊天处理器】尝试标记空消息ID为已处理");
            return;
        }

        try {
            // 构建Redis键
            String redisKey = "message:processed:" + messageId;

            // 在Redis中标记为已处理，设置1小时过期时间
            redisService.set(redisKey, "1", 3600); // 1小时过期

            // 同时在内存中标记（兼容旧逻辑）
            processedMessages.put(messageId, System.currentTimeMillis());

            log.debug("【聊天处理器】已标记消息为已处理: {}", messageId);
        } catch (Exception e) {
            log.error("【聊天处理器】标记消息为已处理时出错: {}", e.getMessage(), e);
            // 不抛出异常，让调用者决定如何处理
        }
    }

    /**
     * 标记消息为已处理（包含客户端消息ID）
     * @param messageId 消息ID
     * @param clientMessageId 客户端消息ID
     */
    private void markMessageAsProcessed(String messageId, String clientMessageId) {
        // 标记服务器消息ID
        markMessageAsProcessed(messageId);

        // 标记客户端消息ID
        if (clientMessageId != null && !clientMessageId.isEmpty()) {
            try {
                // 构建Redis键
                String clientRedisKey = "message:processed:client:" + clientMessageId;

                // 在Redis中标记为已处理，设置1小时过期时间
                redisService.set(clientRedisKey, "1", 3600); // 1小时过期

                log.debug("【聊天处理器】已标记客户端消息为已处理: clientMessageId={}", clientMessageId);
            } catch (Exception e) {
                log.error("【聊天处理器】标记客户端消息为已处理时出错: {}", e.getMessage(), e);
                // 不抛出异常，让调用者决定如何处理
            }
        }
    }

    /**
     * 判断错误是否可恢复
     */
    private boolean isRecoverableError(Exception e) {
        // 网络错误、连接超时等通常是可恢复的
        // 但JSON解析错误和未知字段错误不应该被视为可恢复的
        if (e instanceof com.fasterxml.jackson.core.JsonParseException ||
            e instanceof com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException) {
            return false;
        }

        return e instanceof java.io.IOException ||
               e instanceof java.util.concurrent.TimeoutException ||
               e instanceof java.net.ConnectException;
    }

    /**
     * 根据用户ID获取用户名
     * @param userId 用户ID
     * @return 用户名，如果未找到则返回null
     */
    private String getUsernameById(Long userId) {
        try {
            if (userId == null) {
                log.warn("【聊天处理器】尝试获取用户名时收到空的用户ID");
                return null;
            }

            // 从数据库中查询用户
            com.tomato.lostfoundsystem.entity.User user = userMapper.findById(userId);
            if (user != null) {
                return user.getUsername();
            } else {
                log.warn("【聊天处理器】未找到用户ID对应的用户: {}", userId);
                return null;
            }
        } catch (Exception e) {
            log.error("【聊天处理器】获取用户名时出错: {}", e.getMessage(), e);
            return null;
        }
    }
}
