<template>
  <div class="chat-header">
    <div class="contact-info" v-if="contact">
      <el-avatar :size="40" :src="contact.avatar">
        <span class="avatar-text" :style="{ backgroundColor: avatarColor }">
          {{ nameInitial }}
        </span>
      </el-avatar>
      <div class="contact-details">
        <div class="contact-name">{{ contact.name }}</div>
        <div :class="['contact-status', contact.online ? 'online-text' : 'offline-text']">
          {{ contact.online ? '在线' : '离线' }}
        </div>
      </div>
    </div>
    <div class="header-actions">
      <el-tooltip content="刷新" placement="bottom">
        <el-button circle @click="$emit('refresh')" :loading="loading">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip content="诊断" placement="bottom">
        <el-button circle @click="$emit('diagnose')">
          <el-icon><Monitor /></el-icon>
        </el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Refresh, Monitor } from '@element-plus/icons-vue'
import { generateAvatarColor, getNameInitial } from '@/utils/chat-utils'

const props = defineProps({
  contact: {
    type: Object,
    default: () => null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh', 'diagnose'])

// 计算头像颜色
const avatarColor = computed(() => {
  return generateAvatarColor(props.contact?.name)
})

// 计算用户名首字符
const nameInitial = computed(() => {
  return getNameInitial(props.contact?.name)
})
</script>

<style scoped>
.chat-header {
  padding: 10px 16px;
  border-bottom: 1px solid #e9edef;
  background-color: #f0f2f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  z-index: 1;
  height: 60px;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 6px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.contact-info:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.contact-details {
  display: flex;
  flex-direction: column;
}

.contact-name {
  font-size: 16px;
  font-weight: 600;
  color: #111b21;
}

.contact-status {
  font-size: 12px;
  margin-top: 2px;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 4px;
}

.contact-status::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 2px;
}

.online-text {
  color: #00a884;
}

.online-text::before {
  background-color: #00a884;
}

.offline-text {
  color: #8696a0;
}

.offline-text::before {
  background-color: #8696a0;
}

.avatar-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 18px;
  font-weight: bold;
  color: white;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-actions :deep(.el-button) {
  color: #54656f;
  border: none;
  background: transparent;
  padding: 8px;
  transition: all 0.2s;
  font-size: 18px;
}

.header-actions :deep(.el-button:hover) {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}

.header-actions :deep(.el-button:active) {
  transform: scale(0.95);
}
</style>
