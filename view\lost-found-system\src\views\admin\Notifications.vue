<template>
  <div class="admin-notifications">
    <div class="page-header">
      <h2>系统通知管理</h2>
    </div>

    <!-- 发送通知表单 -->
    <el-card class="send-notification-card">
      <template #header>
        <div class="card-header">
          <span>发送系统通知</span>
        </div>
      </template>

      <el-form
        ref="notificationFormRef"
        :model="notificationForm"
        :rules="notificationRules"
        label-width="100px"
      >
        <el-form-item label="通知标题" prop="title">
          <el-input
            v-model="notificationForm.title"
            placeholder="请输入通知标题"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="通知内容" prop="content">
          <el-input
            v-model="notificationForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入通知内容"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="通知类型" prop="notificationType">
          <el-select v-model="notificationForm.notificationType" placeholder="请选择通知类型">
            <el-option label="系统通知" value="SYSTEM" />
            <el-option label="管理员通知" value="ADMIN" />
            <el-option label="公告通知" value="ANNOUNCEMENT" />
          </el-select>
        </el-form-item>

        <el-form-item label="重要程度" prop="importance">
          <el-select v-model="notificationForm.importance" placeholder="请选择重要程度">
            <el-option label="普通" value="NORMAL" />
            <el-option label="重要" value="IMPORTANT" />
            <el-option label="紧急" value="URGENT" />
          </el-select>
        </el-form-item>

        <el-form-item label="接收用户" prop="targetType">
          <el-select
            v-model="notificationForm.targetType"
            placeholder="请选择接收用户类型"
            style="width: 100%"
            @change="handleTargetTypeChange"
          >
            <el-option label="全部用户" value="ALL" />
            <el-option label="普通用户" value="NORMAL" />
            <el-option label="管理员" value="ADMIN" />
            <el-option label="特定用户" value="SPECIFIC" />
          </el-select>
        </el-form-item>

        <!-- 特定用户选择 -->
        <el-form-item
          v-if="notificationForm.targetType === 'SPECIFIC'"
          label="选择用户"
          prop="specificUsers"
        >
          <el-select
            v-model="notificationForm.specificUsers"
            multiple
            placeholder="请选择用户"
            style="width: 100%"
          >
            <el-option
              v-for="user in allUsers"
              :key="user.id"
              :label="`${user.username} (ID: ${user.id})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="sending"
            @click="handleSendNotification"
          >
            发送通知
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const notificationFormRef = ref(null)
const sending = ref(false)
const allUsers = ref([])

const notificationForm = reactive({
  title: '',
  content: '',
  targetType: '',
  specificUsers: [],
  notificationType: 'ADMIN',
  importance: 'NORMAL'
})

const notificationRules = {
  title: [
    { required: true, message: '请输入通知标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入通知内容', trigger: 'blur' },
    { min: 2, max: 500, message: '内容长度在 2 到 500 个字符', trigger: 'blur' }
  ],
  notificationType: [
    { required: true, message: '请选择通知类型', trigger: 'change' }
  ],
  importance: [
    { required: true, message: '请选择重要程度', trigger: 'change' }
  ],
  targetType: [
    { required: true, message: '请选择接收用户类型', trigger: 'change' }
  ],
  specificUsers: [
    {
      validator: (_, value, callback) => {
        if (notificationForm.targetType === 'SPECIFIC' && (!value || value.length === 0)) {
          callback(new Error('请选择特定用户'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 获取所有用户列表
const fetchAllUsers = async () => {
  try {
    const response = await request.get('/admin/users', {
      params: {
        page: 1,
        size: 1000 // 获取足够多的用户
      }
    })
    if (response.code === 200) {
      allUsers.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 处理目标类型变化
const handleTargetTypeChange = (value) => {
  if (value !== 'SPECIFIC') {
    notificationForm.specificUsers = []
  }
}

// 重置表单
const resetForm = () => {
  if (notificationFormRef.value) {
    notificationFormRef.value.resetFields()
    notificationForm.specificUsers = []
  }
}

// 发送通知
const handleSendNotification = async () => {
  if (!notificationFormRef.value) return

  try {
    // 表单验证
    await notificationFormRef.value.validate()

    // 开始发送
    sending.value = true

    // 构建元数据
    const metadata = {
      importance: notificationForm.importance,
      sendTime: new Date().toISOString()
    }

    // 构建请求数据
    const notificationData = {
      title: notificationForm.title,
      message: notificationForm.content,
      role: notificationForm.targetType,
      status: 'UNREAD',
      type: notificationForm.notificationType,
      metadata: JSON.stringify(metadata)
    }

    // 如果是特定用户，添加用户ID
    if (notificationForm.targetType === 'SPECIFIC') {
      // 支持多个特定用户
      if (notificationForm.specificUsers.length === 1) {
        // 单个用户
        notificationData.userId = notificationForm.specificUsers[0]

        // 发送请求
        const response = await request.post('/notifications/send', notificationData)

        if (response.code === 200) {
          ElMessage.success('通知发送成功')
          resetForm()
        } else {
          throw new Error(response.message || '发送失败')
        }
      } else {
        // 多个用户，需要循环发送
        let successCount = 0
        let failCount = 0

        for (const userId of notificationForm.specificUsers) {
          try {
            const userData = { ...notificationData, userId }
            const response = await request.post('/notifications/send', userData)

            if (response.code === 200) {
              successCount++
            } else {
              failCount++
              console.error(`发送给用户 ${userId} 失败: ${response.message}`)
            }
          } catch (err) {
            failCount++
            console.error(`发送给用户 ${userId} 出错:`, err)
          }
        }

        if (successCount > 0 && failCount === 0) {
          ElMessage.success(`成功发送通知给 ${successCount} 个用户`)
          resetForm()
        } else if (successCount > 0 && failCount > 0) {
          ElMessage.warning(`成功发送通知给 ${successCount} 个用户，失败 ${failCount} 个用户`)
        } else {
          ElMessage.error('发送通知失败')
        }
      }
    } else {
      // 发送给所有用户、普通用户或管理员
      const response = await request.post('/notifications/send', notificationData)

      if (response.code === 200) {
        ElMessage.success('通知发送成功')
        resetForm()
      } else {
        throw new Error(response.message || '发送失败')
      }
    }
  } catch (error) {
    console.error('发送通知失败:', error)
    ElMessage.error(error.message || '发送通知失败')
  } finally {
    sending.value = false
  }
}

onMounted(() => {
  fetchAllUsers()
})
</script>

<style scoped>
.admin-notifications {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #303133;
}

.send-notification-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-card__header) {
  padding: 12px 20px;
  border-bottom: 1px solid #ebeef5;
  box-sizing: border-box;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-button) {
  padding: 8px 20px;
  margin-right: 10px;
}

:deep(.el-input__count) {
  line-height: 1;
}
</style>