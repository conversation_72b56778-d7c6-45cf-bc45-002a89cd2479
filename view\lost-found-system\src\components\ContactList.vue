<template>
  <div class="contact-list">
    <div v-if="loading" class="loading">
      <el-skeleton :rows="3" animated />
    </div>
    <div v-else-if="contacts.length === 0" class="empty-list">
      <el-empty description="暂无联系人" />
    </div>
    <div v-else class="contacts-container">
      <div 
        v-for="contact in contacts" 
        :key="contact.id" 
        class="contact-item"
        :class="{ 'online': isContactOnline(contact.id.toString()), 'selected': selectedContactId === contact.id }"
        @click="selectContact(contact)"
      >
        <div class="avatar">
          <el-avatar :size="40" :src="contact.avatar || defaultAvatar">
            {{ getAvatarText(contact.username) }}
          </el-avatar>
        </div>
        <div class="contact-info">
          <div class="username">{{ contact.username }}</div>
          <div class="status">
            <ContactStatus :contact-id="contact.id" :show-last-active="true" />
          </div>
        </div>
        <div class="unread-badge" v-if="contact.unreadCount > 0">
          <el-badge :value="contact.unreadCount" type="danger" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import ContactStatus from './ContactStatus.vue';
import { getContactsStatus, isContactOnline } from '../utils/contact-status';
import defaultAvatar from '../assets/default-avatar.png';

const props = defineProps({
  contacts: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['select-contact']);

const selectedContactId = ref(null);

// 获取头像文本（用户名首字母）
function getAvatarText(username) {
  return username ? username.charAt(0).toUpperCase() : '?';
}

// 选择联系人
function selectContact(contact) {
  selectedContactId.value = contact.id;
  emit('select-contact', contact);
}

// 批量获取联系人在线状态
async function fetchContactsStatus() {
  if (props.contacts.length === 0) return;
  
  try {
    const contactIds = props.contacts.map(contact => contact.id.toString());
    await getContactsStatus(contactIds);
  } catch (error) {
    console.error('获取联系人在线状态失败:', error);
  }
}

onMounted(async () => {
  // 获取联系人在线状态
  await fetchContactsStatus();
});
</script>

<style scoped>
.contact-list {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-right: 1px solid #e6e6e6;
}

.loading, .empty-list {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.contacts-container {
  padding: 8px 0;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
  border-bottom: 1px solid #f0f0f0;
}

.contact-item:hover {
  background-color: #f0f2f5;
}

.contact-item.selected {
  background-color: #e6f7ff;
}

.contact-item.online {
  background-color: rgba(103, 194, 58, 0.05);
}

.avatar {
  margin-right: 12px;
}

.contact-info {
  flex: 1;
  min-width: 0;
}

.username {
  font-weight: bold;
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status {
  font-size: 12px;
  color: #909399;
}

.unread-badge {
  margin-left: 8px;
}

/* 深色主题样式 */
.contact-item.online {
  background-color: rgba(0, 128, 0, 0.1);
}

.contact-item.selected.online {
  background-color: rgba(0, 128, 0, 0.15);
}
</style>
