package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.MessageAttachment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MessageAttachmentMapper {
    // 插入附件信息
    void insertMessageAttachment(MessageAttachment messageAttachment);

    // 根据消息ID获取单个附件（向后兼容）
    MessageAttachment getAttachmentByMessageId(@Param("messageId") Long messageId);

    // 根据消息ID获取所有附件
    List<MessageAttachment> getAllAttachmentsByMessageId(@Param("messageId") Long messageId);

    // 根据附件ID获取附件
    MessageAttachment getAttachmentById(@Param("id") Long id);

    // 更新附件信息
    void updateAttachment(MessageAttachment messageAttachment);

    // 更新附件的文件URL
    void updateAttachmentFileUrl(@Param("id") Long id, @Param("fileUrl") String fileUrl);
}
