package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.LoginRequestDTO;
import com.tomato.lostfoundsystem.dto.RegisterRequestDTO;
import com.tomato.lostfoundsystem.dto.UserProfileDTO;
import com.tomato.lostfoundsystem.entity.User;
import com.tomato.lostfoundsystem.mapper.ChatMessageMapper;
import com.tomato.lostfoundsystem.mapper.FoundItemMapper;
import com.tomato.lostfoundsystem.mapper.LostItemMapper;
import com.tomato.lostfoundsystem.mapper.UserMapper;
import com.tomato.lostfoundsystem.utils.AliyunOSSAvatarUtil;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.mock.web.MockMultipartFile;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import javax.imageio.ImageIO;
import com.tomato.lostfoundsystem.service.RedisService;
import com.tomato.lostfoundsystem.service.UserService;
import com.tomato.lostfoundsystem.utils.ImageUtils;
import com.tomato.lostfoundsystem.utils.JWTUtil;
import com.tomato.lostfoundsystem.utils.PasswordUtil;
import com.tomato.lostfoundsystem.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private JWTUtil jwtUtil;

    @Resource
    private RedisService redisService;  // 注入 RedisService

    @Resource
    private LostItemMapper lostItemMapper;

    @Resource
    private FoundItemMapper foundItemMapper;

    @Resource
    private ChatMessageMapper chatMessageMapper;

    @Resource
    private AliyunOSSAvatarUtil aliyunOSSAvatarUtil;

    @jakarta.annotation.Resource
    private com.tomato.lostfoundsystem.service.CDNCacheService cdnCacheService;

    @Value("${jwt.expiration-time}")
    private long expirationTime;

    @Override
    public Result<?> register(RegisterRequestDTO dto) {
        try {
            log.info("开始处理用户注册请求 - 用户名: {}, 邮箱: {}, 手机号: {}", dto.getUsername(), dto.getEmail(), dto.getPhone());

            // 1. 参数校验
            Result<String> validationResult = validateRegisterParams(dto);
            if (validationResult != null) {
                log.warn("注册参数校验失败 - 用户名: {}, 原因: {}", dto.getUsername(), validationResult.getMessage());
                return validationResult;
            }

            log.info("注册参数校验通过 - 用户名: {}", dto.getUsername());

            // 2. 创建用户对象
            User user = createUserFromDTO(dto);
            log.info("已创建用户对象 - 用户名: {}", user.getUsername());

            // 3. 保存用户
            Result<?> result = saveUser(user);
            if (result.getCode() == 200) {
                log.info("用户注册成功 - 用户名: {}", user.getUsername());
            } else {
                log.error("用户保存失败 - 用户名: {}, 原因: {}", user.getUsername(), result.getMessage());
            }
            return result;
        } catch (Exception e) {
            log.error("用户注册过程中发生异常: {}", e.getMessage(), e);
            return Result.fail("注册失败，请稍后重试");
        }
    }

    @Override
    public Result<?> login(LoginRequestDTO dto) {
        try {
            log.info("开始处理用户登录请求 - 用户名: {}, 邮箱: {}, 手机号: {}",
                    dto.getUsername(), dto.getEmail(), dto.getPhone());

            // 1. 参数校验
            Result<String> validationResult = validateLoginParams(dto);
            if (validationResult != null) {
                log.warn("登录参数校验失败 - 原因: {}", validationResult.getMessage());
                return validationResult;
            }

            log.info("登录参数校验通过");

            // 2. 验证码登录
            if (isVerificationCodeLogin(dto)) {
                log.info("使用验证码方式登录 - 邮箱: {}, 手机号: {}", dto.getEmail(), dto.getPhone());
                return handleVerificationCodeLogin(dto);
            }

            // 3. 密码登录
            if (isPasswordLogin(dto)) {
                log.info("使用密码方式登录 - 用户名: {}", dto.getUsername());
                return handlePasswordLogin(dto);
            }

            log.warn("不支持的登录方式");
            return Result.fail("不支持的登录方式");
        } catch (Exception e) {
            log.error("登录过程发生异常: {}", e.getMessage(), e);
            return Result.fail("登录失败，请稍后重试");
        }
    }
    @Override
    public boolean logout(String token) {
        try {
            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId != null) {
                String redisKey = "jwt:user:" + userId;
                redisUtil.delete(redisKey);
                return true;
            }
        } catch (Exception e) {
            // 可记录日志但不抛出
            log.error("token删除操作失败");
        }
        return false;
    }

    @Override
    public User getUserById(Long userId) {
        return userMapper.findById(userId);
    }

    @Override
    public boolean updateUserProfile(UserProfileDTO userProfileDTO) {
        // 查询当前用户
        User currentUser = userMapper.findById(userProfileDTO.getId());
        if (currentUser == null) {
            return false;  // 用户不存在
        }

        // 校验新邮箱是否需要更新并且是否符合格式
        if (StringUtils.hasText(userProfileDTO.getEmail())) {
            if (!userProfileDTO.getEmail().equals(currentUser.getEmail())) {
                // 校验邮箱格式
                if (!isValidEmail(userProfileDTO.getEmail())) {
                    return false;  // 邮箱格式不正确
                }
                // 校验新邮箱是否已被其他用户绑定
                User existingEmailUser = userMapper.findByEmail(userProfileDTO.getEmail());
                if (existingEmailUser != null) {
                    return false;  // 邮箱已被其他用户绑定
                }
            }
        }

        // 校验新手机号是否需要更新并且是否符合格式
        if (StringUtils.hasText(userProfileDTO.getPhone())) {
            if (!userProfileDTO.getPhone().equals(currentUser.getPhone())) {
                // 校验手机号格式
                if (!isValidPhone(userProfileDTO.getPhone())) {
                    return false;  // 手机号格式不正确
                }
                // 校验新手机号是否已被其他用户绑定
                User existingPhoneUser = userMapper.findByPhone(userProfileDTO.getPhone());
                if (existingPhoneUser != null) {
                    return false;  // 手机号已被其他用户绑定
                }
            }
        }


        // 更新用户信息
        currentUser.setEmail(userProfileDTO.getEmail());
        currentUser.setPhone(userProfileDTO.getPhone());

        // 执行更新操作
        int rowsAffected = userMapper.updateUser(currentUser);

        if (rowsAffected > 0) {
            //更新成功
            return true;
        }

        return false;
    }

    /**
     * 验证注册参数
     */
    private Result<String> validateRegisterParams(RegisterRequestDTO dto) {
        // 校验手机号和邮箱至少一个不为空
        if (!StringUtils.hasText(dto.getPhone()) && !StringUtils.hasText(dto.getEmail())) {
            return Result.fail("手机号或邮箱必须填写一个");
        }

        // 校验密码一致
        if (!dto.getPassword().equals(dto.getConfirmPassword())) {
            return Result.fail("两次输入的密码不一致");
        }

        // 校验用户名是否已存在
        if (userMapper.findByUsername(dto.getUsername()) != null) {
            return Result.fail("用户名已存在");
        }

        // 校验邮箱唯一性
        if (StringUtils.hasText(dto.getEmail()) && userMapper.findByEmail(dto.getEmail()) != null) {
            return Result.fail("该邮箱已被注册");
        }

        // 校验手机号唯一性
        if (StringUtils.hasText(dto.getPhone())) {
            User existingUser = userMapper.findByPhone(dto.getPhone());
            if (existingUser != null) {
                String existingEmail = userMapper.getEmailByPhone(dto.getPhone());
                return Result.fail("该手机号已绑定邮箱 " + existingEmail + "，无法直接注册");
            }
        }

        return null;
    }

    /**
     * 从DTO创建用户对象
     */
    private User createUserFromDTO(RegisterRequestDTO dto) {
        User user = new User();
        user.setUsername(dto.getUsername());
        user.setPassword(PasswordUtil.encode(dto.getPassword()));
        user.setPhone(dto.getPhone());
        user.setEmail(dto.getEmail());
        user.setRole("USER");
        user.setCreateTime(LocalDateTime.now());
        user.setIsActive(true);
        user.setDeleted(false);

        // 初始设置为null，在saveUser方法中生成默认头像并更新
        user.setAvatar(null);

        return user;
    }

    /**
     * 保存用户并生成token
     */
    private Result<?> saveUser(User user) {
        try {
            log.info("开始保存用户数据 - 用户名: {}", user.getUsername());
            int inserted = userMapper.insertUser(user);

            if (inserted > 0) {
                log.info("用户数据保存成功 - 用户名: {}, 影响行数: {}", user.getUsername(), inserted);

                // 获取插入后的用户ID（如果需要）
                User savedUser = userMapper.findByUsername(user.getUsername());
                if (savedUser == null) {
                    log.error("用户保存成功但无法获取用户信息 - 用户名: {}", user.getUsername());
                    return Result.success("注册成功");
                }

                // 生成默认头像
                try {
                    // 生成默认头像图像
                    BufferedImage avatarImage = ImageUtils.generateDefaultAvatar(savedUser.getUsername(), savedUser.getId(), 200);

                    // 将图像转换为输入流
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    ImageIO.write(avatarImage, "png", baos);
                    byte[] imageBytes = baos.toByteArray();

                    // 创建MultipartFile对象
                    MultipartFile avatarFile = new MockMultipartFile(
                        "avatar_" + savedUser.getId() + "_default.png",
                        "avatar_" + savedUser.getId() + "_default.png",
                        "image/png",
                        imageBytes
                    );

                    // 上传到阿里云OSS
                    Map<String, String> avatarUrls = aliyunOSSAvatarUtil.uploadAvatar(avatarFile, savedUser.getId());

                    // 使用中等尺寸的头像作为主头像URL
                    String avatarUrl = avatarUrls.getOrDefault("medium", avatarUrls.get("original"));
                    savedUser.setAvatar(avatarUrl);

                    // 更新用户头像URL
                    userMapper.updateUserAvatar(savedUser.getId(), avatarUrl);
                    log.info("为用户生成默认头像成功 - 用户名: {}, 头像URL: {}", savedUser.getUsername(), avatarUrl);
                } catch (Exception e) {
                    log.error("生成默认头像失败: {}", e.getMessage(), e);
                    // 头像生成失败不影响用户注册流程
                }

                // 生成token并返回（实现注册即登录）
                String token = generateAndStoreToken(savedUser);
                log.info("用户注册成功并生成token - 用户名: {}", user.getUsername());

                // 创建包含token和用户信息的返回数据
                Map<String, Object> resultData = createUserInfoMap(savedUser, token);
                return Result.success("注册成功", resultData);
            } else {
                log.error("用户数据保存失败 - 用户名: {}, 影响行数: {}", user.getUsername(), inserted);
                return Result.fail("注册失败，数据库操作未成功");
            }
        } catch (Exception e) {
            log.error("保存用户数据时发生异常 - 用户名: {}, 异常: {}", user.getUsername(), e.getMessage(), e);
            return Result.fail("注册失败，数据库操作异常");
        }
    }

    /**
     * 验证登录参数
     * @param dto 登录请求DTO
     * @return 验证失败返回错误结果，验证成功返回null
     */
    private Result<String> validateLoginParams(LoginRequestDTO dto) {
        // 检查是否提供了有效的登录方式
        boolean hasPasswordLogin = StringUtils.hasText(dto.getUsername()) && StringUtils.hasText(dto.getPassword());
        boolean hasPhoneLogin = StringUtils.hasText(dto.getPhone()) && StringUtils.hasText(dto.getCode());
        boolean hasEmailLogin = StringUtils.hasText(dto.getEmail()) && StringUtils.hasText(dto.getCode());

        if (!hasPasswordLogin && !hasPhoneLogin && !hasEmailLogin) {
            return Result.fail("请提供完整的登录信息");
        }

        // 密码登录参数校验
        if (hasPasswordLogin) {
            // 用户名长度校验
            if (dto.getUsername().length() < 3 || dto.getUsername().length() > 20) {
                return Result.fail("用户名长度应在3-20个字符之间");
            }

            // 密码长度校验
            if (dto.getPassword().length() < 6 || dto.getPassword().length() > 20) {
                return Result.fail("密码长度应在6-20个字符之间");
            }

            // 图形验证码校验
            if (!StringUtils.hasText(dto.getVerifyCode())) {
                return Result.fail("图形验证码不能为空");
            }

            // 验证码ID校验
            if (!StringUtils.hasText(dto.getCaptchaId())) {
                return Result.fail("验证码标识符不能为空，请刷新验证码");
            }
        }

        // 手机验证码登录参数校验
        if (hasPhoneLogin) {
            // 手机号格式校验
            if (!dto.getPhone().matches("^1[3-9]\\d{9}$")) {
                return Result.fail("手机号格式不正确");
            }

            // 验证码长度校验
            if (dto.getCode().length() != 6) {
                return Result.fail("验证码长度应为6位");
            }
        }

        // 邮箱验证码登录参数校验
        if (hasEmailLogin) {
            // 邮箱格式校验
            if (!dto.getEmail().matches("^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$")) {
                return Result.fail("邮箱格式不正确");
            }

            // 验证码长度校验
            if (dto.getCode().length() != 6) {
                return Result.fail("验证码长度应为6位");
            }
        }

        return null; // 验证通过
    }

    /**
     * 判断是否为验证码登录
     */
    private boolean isVerificationCodeLogin(LoginRequestDTO dto) {
        return (StringUtils.hasText(dto.getPhone()) || StringUtils.hasText(dto.getEmail())) &&
                StringUtils.hasText(dto.getCode());
    }

    /**
     * 判断是否为密码登录
     */
    private boolean isPasswordLogin(LoginRequestDTO dto) {
        return StringUtils.hasText(dto.getUsername()) && StringUtils.hasText(dto.getPassword());
    }

    /**
     * 处理验证码登录
     */
    private Result<?> handleVerificationCodeLogin(LoginRequestDTO dto) {
        // 查找用户
        User user = findUserByPhoneOrEmail(dto);
        if (user == null) {
            log.info("验证码登录失败，用户不存在，手机号：{}，邮箱：{}", dto.getPhone(), dto.getEmail());
            if (StringUtils.hasText(dto.getPhone())) {
                return Result.fail("该手机号未注册，请先注册");
            } else {
                return Result.fail("该邮箱未注册，请先注册");
            }
        }

        // 检查用户状态
        if (user.getDeleted()) {
            log.info("验证码登录失败，用户已被删除，手机号：{}，邮箱：{}", dto.getPhone(), dto.getEmail());
            return Result.fail("该账号已注销，无法登录");
        }

        if (!user.getIsActive()) {
            log.info("验证码登录失败，用户已被封禁，手机号：{}，邮箱：{}", dto.getPhone(), dto.getEmail());
            return Result.fail("账号已被封禁，请联系管理员解封");
        }

        // 生成并存储 token
        String token = generateAndStoreToken(user);
        log.info("验证码登录成功，用户：{}", user.getUsername());

        // 创建包含用户信息的返回数据
        Map<String, Object> userInfoMap = createUserInfoMap(user, token);
        return Result.success("登录成功", userInfoMap);
    }

    /**
     * 处理密码登录
     */
    private Result<?> handlePasswordLogin(LoginRequestDTO dto) {
        // 查找用户
        User user = userMapper.findByUsername(dto.getUsername());

        // 检查用户是否存在
        if (user == null) {
            log.info("密码登录失败，用户不存在，用户名：{}", dto.getUsername());
            return Result.fail("用户名不存在，请检查用户名或注册新账号");
        }

        // 检查用户状态
        if (user.getDeleted()) {
            log.info("密码登录失败，用户已被删除，用户名：{}", dto.getUsername());
            return Result.fail("该账号已注销，无法登录");
        }

        if (!user.getIsActive()) {
            log.info("密码登录失败，用户已被封禁，用户名：{}", dto.getUsername());
            return Result.fail("账号已被封禁，请联系管理员解封");
        }

        // 验证密码
        if (!PasswordUtil.matches(dto.getPassword(), user.getPassword())) {
            log.info("密码登录失败，密码错误，用户名：{}", dto.getUsername());
            return Result.fail("密码错误，请重新输入");
        }

        // 生成并存储 token
        String token = generateAndStoreToken(user);
        log.info("密码登录成功，用户：{}", user.getUsername());

        // 创建包含用户信息的返回数据
        Map<String, Object> userInfoMap = createUserInfoMap(user, token);
        return Result.success("登录成功", userInfoMap);
    }

    /**
     * 根据手机号或邮箱查找用户
     */
    private User findUserByPhoneOrEmail(LoginRequestDTO dto) {
        if (StringUtils.hasText(dto.getPhone())) {
            return userMapper.findByPhone(dto.getPhone());
        }
        if (StringUtils.hasText(dto.getEmail())) {
            return userMapper.findByEmail(dto.getEmail());
        }
        return null;
    }


    // 校验邮箱格式是否正确
    private boolean isValidEmail(String email) {
        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
        return email.matches(emailRegex);
    }

    // 校验手机号格式是否正确
    private boolean isValidPhone(String phone) {
        String phoneRegex = "^(1[3-9])\\d{9}$";  // 中国大陆手机号正则，简化版
        return phone.matches(phoneRegex);
    }

    /**
     * 生成并存储token
     */
    private String generateAndStoreToken(User user) {
        String token = jwtUtil.createToken(user.getId(), user.getUsername(), user.getRole());
        String redisKey = "jwt:user:" + user.getId();
        redisUtil.set(redisKey, token, expirationTime);
        return token;
    }

    /**
     * 创建用户信息Map，包含token和用户基本信息
     */
    private Map<String, Object> createUserInfoMap(User user, String token) {
        Map<String, Object> userInfoMap = new HashMap<>();
        userInfoMap.put("token", token);

        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", user.getId());
        userInfo.put("username", user.getUsername());
        userInfo.put("email", user.getEmail());
        userInfo.put("phone", user.getPhone());
        userInfo.put("role", user.getRole());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("isActive", user.getIsActive());

        userInfoMap.put("userInfo", userInfo);
        return userInfoMap;
    }

    /**
     * 用户注销账号
     *
     * @param userId 用户ID
     * @param password 用户密码（用于验证）
     * @return 注销结果
     */
    @Override
    @Transactional
    public Result<String> deactivateAccount(Long userId, String password) {
        try {
            // 1. 验证用户是否存在
            User user = userMapper.findById(userId);
            if (user == null) {
                return Result.fail("用户不存在");
            }

            // 2. 验证密码是否正确
            if (!PasswordUtil.matches(password, user.getPassword())) {
                return Result.fail("密码错误，无法注销账号");
            }

            // 3. 检查用户角色，不允许管理员通过此方式注销
            if ("ADMIN".equals(user.getRole()) || "SUPER_ADMIN".equals(user.getRole())) {
                return Result.fail("管理员账号不能通过此方式注销，请联系超级管理员");
            }

            // 4. 执行注销操作
            // 4.1 标记用户为已删除
            userMapper.logicalDeleteUser(userId);

            // 4.2 清除用户的登录状态（Redis中的token）
            String redisKey = "jwt:user:" + userId;
            redisUtil.delete(redisKey);

            // 4.3 匿名化用户数据
            anonymizeUserData(user);

            log.info("用户 {} 已成功注销账号", user.getUsername());
            return Result.success("账号已成功注销");
        } catch (Exception e) {
            log.error("用户注销账号失败: {}", e.getMessage(), e);
            return Result.fail("注销账号失败，请稍后重试");
        }
    }

    /**
     * 匿名化用户数据
     *
     * @param user 用户对象
     */
    private void anonymizeUserData(User user) {
        // 生成匿名用户名
        String anonymousUsername = "deleted_user_" + user.getId();

        // 更新用户信息为匿名
        user.setUsername(anonymousUsername);
        user.setEmail(null);
        user.setPhone(null);
        user.setDeleted(true);
        user.setIsActive(false);

        // 更新用户信息
        userMapper.updateUser(user);

        // 注意：这里我们不删除用户的失物/拾物信息和聊天记录
        // 因为这些信息可能对其他用户有用
        // 但在实际应用中，可能需要根据隐私政策决定是否删除或匿名化这些数据
    }

    /**
     * 上传用户头像
     *
     * @param userId 用户ID
     * @param avatarFile 头像文件
     * @return 上传结果
     */
    @Override
    public Result<String> uploadAvatar(Long userId, MultipartFile avatarFile) {
        try {
            // 1. 验证用户是否存在
            User user = userMapper.findById(userId);
            if (user == null) {
                return Result.fail("用户不存在");
            }

            // 2. 验证文件是否为空
            if (avatarFile == null || avatarFile.isEmpty()) {
                return Result.fail("请选择要上传的头像文件");
            }

            // 3. 验证文件类型
            String contentType = avatarFile.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.fail("只能上传图片文件");
            }

            // 4. 删除旧头像并刷新CDN缓存
            List<String> oldAvatarUrls = new ArrayList<>();
            if (user.getAvatar() != null && !user.getAvatar().isEmpty()) {
                // 收集旧头像的所有尺寸URL
                String avatarUrl = user.getAvatar();
                oldAvatarUrls.add(avatarUrl);

                // 根据中等尺寸URL推断其他尺寸URL
                if (avatarUrl.contains("_medium")) {
                    String baseUrl = avatarUrl.replace("_medium", "");
                    oldAvatarUrls.add(baseUrl.replace(".jpg", "_original.jpg").replace(".png", "_original.png").replace(".gif", "_original.gif"));
                    oldAvatarUrls.add(baseUrl.replace(".jpg", "_large.jpg").replace(".png", "_large.png").replace(".gif", "_large.gif"));
                    oldAvatarUrls.add(baseUrl.replace(".jpg", "_small.jpg").replace(".png", "_small.png").replace(".gif", "_small.gif"));
                }

                // 删除旧头像
                aliyunOSSAvatarUtil.deleteAvatar(avatarUrl);

                // 刷新旧头像的CDN缓存
                try {
                    boolean refreshSuccess = cdnCacheService.refreshUrls(oldAvatarUrls) > 0;
                    if (refreshSuccess) {
                        log.info("成功刷新旧头像CDN缓存");
                    } else {
                        log.warn("刷新旧头像CDN缓存失败，将依赖CDN自动过期机制");
                    }
                } catch (Exception e) {
                    log.warn("刷新旧头像CDN缓存失败: {}", e.getMessage());
                    // 继续执行，不影响头像上传
                }
            }

            // 5. 上传新头像到阿里云OSS
            Map<String, String> avatarUrls = aliyunOSSAvatarUtil.uploadAvatar(avatarFile, userId);

            // 使用中等尺寸的头像作为主头像URL
            String avatarUrl = avatarUrls.getOrDefault("medium", avatarUrls.get("original"));

            // 6. 更新用户头像URL
            userMapper.updateUserAvatar(userId, avatarUrl);

            // 7. 刷新新头像的CDN缓存
            try {
                List<String> newAvatarUrls = new ArrayList<>(avatarUrls.values());
                boolean refreshSuccess = cdnCacheService.refreshUrls(newAvatarUrls) > 0;
                if (refreshSuccess) {
                    log.info("成功刷新新头像CDN缓存");

                    // 预热新头像的CDN缓存
                    boolean preloadSuccess = cdnCacheService.preloadUrls(newAvatarUrls) > 0;
                    if (preloadSuccess) {
                        log.info("成功预热新头像CDN缓存");
                    } else {
                        log.warn("预热新头像CDN缓存失败，可能会导致首次访问较慢");
                    }
                } else {
                    log.warn("刷新新头像CDN缓存失败，将依赖CDN自动过期机制");
                }
            } catch (Exception e) {
                log.warn("刷新/预热新头像CDN缓存失败: {}", e.getMessage());
                // 继续执行，不影响头像上传结果
            }

            // 8. 添加时间戳参数，避免浏览器缓存
            String timestampedUrl = avatarUrl + "?t=" + System.currentTimeMillis();

            return Result.success("头像上传成功", timestampedUrl);
        } catch (Exception e) {
            log.error("上传头像失败: {}", e.getMessage(), e);
            return Result.fail("上传头像失败: " + e.getMessage());
        }
    }
}