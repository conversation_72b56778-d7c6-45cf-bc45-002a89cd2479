package com.tomato.lostfoundsystem.controller.admin;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.service.CDNConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * CDN配置控制器
 * 用于管理CDN相关配置
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/cdn")
public class CDNConfigController {

    @Resource
    private CDNConfigService cdnConfigService;

    /**
     * 获取CDN配置信息
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getCDNConfig() {
        try {
            CDNConfigService.CDNStatusInfo statusInfo = cdnConfigService.getCDNStatus();

            Map<String, Object> result = new HashMap<>();
            result.put("domain", statusInfo.getDomain());
            result.put("enabled", statusInfo.isEnabled());
            result.put("lastUpdated", statusInfo.getLastUpdated());

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取CDN配置失败: {}", e.getMessage(), e);
            return Result.fail("获取CDN配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新CDN域名
     */
    @PostMapping("/update-domain")
    public Result<String> updateCDNDomain(@RequestParam String domain) {
        try {
            // 验证域名
            if (!cdnConfigService.validateCDNDomain(domain)) {
                return Result.fail("CDN域名验证失败，请确保域名可访问");
            }

            // 更新域名
            boolean success = cdnConfigService.updateCDNDomain(domain);
            if (success) {
                return Result.success("CDN域名已更新");
            } else {
                return Result.fail("更新CDN域名失败");
            }
        } catch (Exception e) {
            log.error("更新CDN域名失败: {}", e.getMessage(), e);
            return Result.fail("更新CDN域名失败: " + e.getMessage());
        }
    }

    /**
     * 验证CDN域名
     */
    @GetMapping("/validate-domain")
    public Result<Boolean> validateCDNDomain(@RequestParam String domain) {
        try {
            boolean valid = cdnConfigService.validateCDNDomain(domain);
            if (valid) {
                return Result.success("CDN域名有效", true);
            } else {
                return Result.success("CDN域名无效", false);
            }
        } catch (Exception e) {
            log.error("验证CDN域名失败: {}", e.getMessage(), e);
            return Result.fail("验证CDN域名失败: " + e.getMessage());
        }
    }
}
