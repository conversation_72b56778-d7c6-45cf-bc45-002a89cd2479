@echo off
echo ===================================
echo 配置编辑器
echo ===================================

:: 直接使用当前目录
set CONFIG_FILE=config.bat

:: 检查配置文件是否存在
if not exist "%CONFIG_FILE%" (
    echo 错误：配置文件 %CONFIG_FILE% 不存在！
    pause
    exit /b 1
)

:: 使用记事本打开配置文件
start notepad "%CONFIG_FILE%"

echo.
echo 配置文件已在记事本中打开，请根据实际情况修改配置。
echo 保存后关闭记事本即可。
echo.
echo ===================================

pause
