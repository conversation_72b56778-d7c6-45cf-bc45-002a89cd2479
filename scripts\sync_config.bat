@echo off
echo ===================================
echo 配置同步工具
echo ===================================
echo 此工具帮助您保持脚本配置与数据库配置的一致性
echo.

:: 直接使用当前目录
set CONFIG_FILE=config.bat

:: 加载配置
call "%CONFIG_FILE%"

echo 当前脚本配置:
echo --------------------------------------
echo 智能匹配服务路径: %CLIP_SERVICE_PATH%
echo 智能匹配服务脚本: %CLIP_API_SCRIPT%
echo 智能匹配服务端口: %CLIP_API_PORT%
echo.

echo 请确保以下数据库配置项与脚本配置保持一致:
echo --------------------------------------
echo 1. autodl.clip.api.url = http://服务器IP:%CLIP_API_PORT%
echo 2. autodl.clip.service.script-path = %CLIP_SERVICE_PATH%\%CLIP_API_SCRIPT%
echo.

echo 您可以:
echo 1. 在后台管理界面中修改数据库配置，使其与脚本配置一致
echo 2. 修改脚本配置，使其与数据库配置一致
echo.

set /p choice=是否打开配置文件进行编辑? (Y/N):
if /i "%choice%"=="Y" (
    start notepad "%CONFIG_FILE%"
)

echo.
echo ===================================
echo 配置同步提示:
echo 1. 首次设置系统时，建议先配置脚本，再将相同的配置导入数据库
echo 2. 后续使用中，如果在后台管理界面修改了配置，请同步更新脚本配置
echo 3. 保持配置一致可确保脚本和后台管理界面能正常协同工作
echo ===================================

pause
