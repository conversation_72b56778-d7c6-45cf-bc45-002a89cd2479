<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="!uploading"
    :show-close="!uploading"
    @closed="handleClosed"
  >
    <div class="upload-dialog-content">
      <!-- 文件预览 -->
      <div class="file-preview">
        <template v-if="fileType === 'IMAGE'">
          <el-image
            :src="previewUrl"
            fit="contain"
            class="preview-image"
          />
        </template>
        
        <template v-else>
          <div class="file-icon-container">
            <el-icon class="file-icon"><Document /></el-icon>
            <div class="file-name">{{ fileName }}</div>
            <div class="file-size">{{ formatFileSize(fileSize) }}</div>
          </div>
        </template>
      </div>
      
      <!-- 消息输入 -->
      <el-input
        v-model="message"
        type="textarea"
        :rows="2"
        :placeholder="placeholder"
        :disabled="uploading"
        resize="none"
      />
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="uploading">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm" 
          :loading="uploading"
          :disabled="!canSend"
        >
          发送
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Document } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  file: {
    type: Object,
    default: null
  },
  fileType: {
    type: String,
    default: 'DOCUMENT'
  },
  previewUrl: {
    type: String,
    default: ''
  },
  uploading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'cancel', 'confirm'])

// 对话框状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 消息内容
const message = ref('')

// 监听对话框关闭
const handleClosed = () => {
  message.value = ''
}

// 取消上传
const handleCancel = () => {
  if (props.uploading) return
  emit('cancel')
}

// 确认上传
const handleConfirm = () => {
  if (!canSend.value || props.uploading) return
  emit('confirm', {
    message: message.value,
    file: props.file
  })
}

// 是否可以发送
const canSend = computed(() => {
  return props.file && !props.uploading
})

// 文件名
const fileName = computed(() => {
  if (!props.file) return ''
  return props.file.name
})

// 文件大小
const fileSize = computed(() => {
  if (!props.file) return 0
  return props.file.size
})

// 对话框标题
const title = computed(() => {
  if (props.fileType === 'IMAGE') {
    return '发送图片'
  } else {
    return '发送文件'
  }
})

// 输入框占位符
const placeholder = computed(() => {
  if (props.fileType === 'IMAGE') {
    return '添加图片说明...'
  } else {
    return '添加文件说明...'
  }
})

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  if (bytes < 1024) return bytes + ' B'
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB'
  if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB'
  return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
}
</script>

<style scoped>
.upload-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.file-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  min-height: 200px;
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: 4px;
}

.file-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 64px;
  color: #409eff;
}

.file-name {
  font-weight: 500;
  text-align: center;
  word-break: break-word;
  max-width: 100%;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
