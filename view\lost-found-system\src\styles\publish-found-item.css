/* 响应式调整 */
@media screen and (max-width: 900px) {
  .content-form {
    max-width: 100%;
  }
}

@media screen and (max-width: 768px) {
  .publish-found-container {
    padding: 15px;
  }

  .page-header {
    margin-bottom: 15px;
  }

  .content-form {
    padding: 20px;
  }

  .two-column-layout {
    flex-direction: column;
    gap: 20px;
  }

  .left-column,
  .right-column {
    width: 100%;
  }

  .upload-content-wrapper {
    flex-direction: column;
  }

  .upload-button-area {
    width: 100%;
    max-width: 100%;
    order: 2;
  }

  .image-preview-area {
    order: 1;
    margin-bottom: 15px;
  }

  .upload-button-content {
    padding: 15px;
    width: 100%;
  }

  .upload-icon {
    font-size: 24px;
  }

  .upload-primary-text {
    font-size: 15px;
  }

  .image-carousel-container {
    margin-bottom: 15px;
    padding: 10px;
  }

  .carousel-actions {
    flex-direction: row;
    justify-content: center;
    gap: 8px;
    padding: 8px;
  }

  .carousel-actions .el-button {
    font-size: 12px;
    padding: 6px 10px;
  }
}

@media screen and (max-width: 480px) {
  .publish-found-container {
    padding: 10px;
  }

  .content-form {
    padding: 15px;
  }

  .form-header h3 {
    font-size: 15px;
  }

  .section-title {
    font-size: 14px;
  }

  .upload-button-content {
    padding: 15px;
  }

  .upload-icon {
    font-size: 22px;
  }

  .el-carousel {
    height: 180px;
  }

  .carousel-actions {
    padding: 5px;
  }

  .carousel-actions .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  .carousel-indicator {
    margin-top: 5px;
    flex-direction: column;
    gap: 5px;
  }
}
