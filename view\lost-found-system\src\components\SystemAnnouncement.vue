<template>
  <transition name="slide-down">
    <div v-if="visible && announcement" class="announcement-bar">
      <div class="announcement-content">
        <el-icon class="announcement-icon"><Bell /></el-icon>
        <div class="announcement-text">
          <span class="announcement-title">{{ announcement.title }}</span>
          <span class="announcement-message">{{ announcement.content }}</span>
        </div>
      </div>
      <div class="announcement-actions">
        <!-- TODO: 当升级到 Element Plus 3.0.0 时，将 type="text" 改为 type="link" -->
        <el-button
          type="text"
          class="view-more-btn"
          @click="viewMoreAnnouncements"
        >
          查看更多
        </el-button>
        <!-- TODO: 当升级到 Element Plus 3.0.0 时，将 type="text" 改为 type="link" -->
        <el-button
          type="text"
          class="close-btn"
          @click="closeAnnouncement"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Bell, Close } from '@element-plus/icons-vue';
import { getLatestAnnouncement } from '@/api/announcement';

const router = useRouter();
const visible = ref(false);
const announcement = ref(null);

// 获取最新公告
const fetchLatestAnnouncement = async () => {
  try {
    const res = await getLatestAnnouncement();
    if (res.code === 200 && res.data) {
      announcement.value = res.data;

      // 检查用户是否已经关闭过这条公告
      const closedAnnouncements = JSON.parse(localStorage.getItem('closedAnnouncements') || '[]');
      if (!closedAnnouncements.includes(announcement.value.id)) {
        visible.value = true;
      }
    }
  } catch (error) {
    console.error('获取系统公告失败:', error);
  }
};

// 关闭公告
const closeAnnouncement = () => {
  visible.value = false;

  // 记录用户已关闭的公告ID
  if (announcement.value) {
    const closedAnnouncements = JSON.parse(localStorage.getItem('closedAnnouncements') || '[]');
    if (!closedAnnouncements.includes(announcement.value.id)) {
      closedAnnouncements.push(announcement.value.id);
      localStorage.setItem('closedAnnouncements', JSON.stringify(closedAnnouncements));
    }
  }
};

// 查看更多公告
const viewMoreAnnouncements = () => {
  router.push('/profile/notifications');
};

// 监听公告显示状态，添加/移除类名
const updateAppClass = (isVisible) => {
  if (isVisible) {
    document.body.classList.add('has-announcement');
  } else {
    document.body.classList.remove('has-announcement');
  }
};

// 监听visible变化
watch(visible, (newValue) => {
  updateAppClass(newValue);
});

onMounted(() => {
  fetchLatestAnnouncement();
});
</script>

<style scoped>
.announcement-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ecf5ff;
  border-bottom: 1px solid #d9ecff;
  padding: 10px 20px;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 999;
  position: fixed;
  top: 64px; /* Header的高度 */
  left: 0;
}

.announcement-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.announcement-icon {
  color: var(--el-color-primary);
  font-size: 18px;
  margin-right: 12px;
}

.announcement-text {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.announcement-title {
  font-weight: 600;
  color: var(--el-color-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.announcement-message {
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.announcement-actions {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.view-more-btn {
  color: var(--el-color-primary);
  font-size: 14px;
  margin-right: 16px;
}

.close-btn {
  color: #909399;
  font-size: 14px;
}

.close-btn:hover {
  color: #606266;
}

/* 动画效果 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from {
  transform: translateY(-100%);
  opacity: 0;
}

.slide-down-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .announcement-bar {
    padding: 8px 12px;
  }

  .announcement-message {
    max-width: 200px;
  }

  .view-more-btn {
    margin-right: 8px;
  }
}
</style>
