package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.SystemConfig;

import java.util.List;
import java.util.Map;

/**
 * 系统配置服务接口
 */
public interface SystemConfigService {
    
    /**
     * 获取所有系统配置
     *
     * @return 系统配置列表
     */
    Result<List<SystemConfig>> getAllConfigs();
    
    /**
     * 根据配置键获取配置
     *
     * @param configKey 配置键
     * @return 系统配置
     */
    Result<SystemConfig> getConfigByKey(String configKey);
    
    /**
     * 更新配置值
     *
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 更新结果
     */
    Result<String> updateConfigValue(String configKey, String configValue);
    
    /**
     * 批量更新配置
     *
     * @param configs 配置键值对
     * @return 更新结果
     */
    Result<String> batchUpdateConfig(Map<String, String> configs);
    
    /**
     * 添加配置
     *
     * @param config 系统配置
     * @return 添加结果
     */
    Result<String> addConfig(SystemConfig config);
    
    /**
     * 删除配置
     *
     * @param configKey 配置键
     * @return 删除结果
     */
    Result<String> deleteConfig(String configKey);
    
    /**
     * 获取配置值
     *
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String configKey, String defaultValue);
    
    /**
     * 刷新配置缓存
     *
     * @return 刷新结果
     */
    Result<String> refreshConfigCache();
}
