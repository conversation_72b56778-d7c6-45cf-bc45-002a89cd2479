@echo off
echo ===================================
echo 正在启动Kafka服务...
echo ===================================

:: 直接使用当前目录
set CONFIG_FILE=config.bat

:: 加载配置
call "%CONFIG_FILE%"

:: 检查Zookeeper服务是否已经运行
tasklist /FI "IMAGENAME eq java.exe" 2>NUL | find /I "QuorumPeerMain">NUL
if "%ERRORLEVEL%"=="0" (
    echo Zookeeper服务已经在运行中！
) else (
    :: 启动Zookeeper服务
    echo 正在启动Zookeeper服务...
    start "Zookeeper Server" /min cmd /c "cd /d %KAFKA_PATH% && bin\windows\zookeeper-server-start.bat %KAFKA_PATH%\%ZOOKEEPER_CONFIG%"

    :: 等待Zookeeper启动
    echo 等待Zookeeper服务启动...
    timeout /t %WAIT_TIME% /nobreak > nul
)

:: 检查Kafka服务是否已经运行
tasklist /FI "IMAGENAME eq java.exe" 2>NUL | find /I "Kafka">NUL
if "%ERRORLEVEL%"=="0" (
    echo Kafka服务已经在运行中！
) else (
    :: 启动Kafka服务
    echo 正在启动Kafka服务...
    start "Kafka Server" /min cmd /c "cd /d %KAFKA_PATH% && bin\windows\kafka-server-start.bat %KAFKA_PATH%\%KAFKA_CONFIG%"

    :: 等待Kafka启动
    echo 等待Kafka服务启动...
    timeout /t %WAIT_TIME% /nobreak > nul

    :: 检查服务是否成功启动
    tasklist /FI "IMAGENAME eq java.exe" 2>NUL | find /I "Kafka">NUL
    if "%ERRORLEVEL%"=="0" (
        echo Kafka服务启动成功！
    ) else (
        echo Kafka服务启动失败，请检查配置！
    )
)

echo.
echo Kafka相关服务状态：
tasklist /FI "IMAGENAME eq java.exe" | find /I "java.exe"
echo.
echo ===================================

:: 不自动关闭窗口
pause
