/**
 * 联系人通知管理器
 * 
 * 该模块负责管理联系人上线通知，避免重复通知和过度打扰用户
 */
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'

// 存储最近通知的联系人及时间
const recentNotifications = new Map()

// 通知冷却时间（毫秒）- 同一联系人在此时间内只通知一次
const NOTIFICATION_COOLDOWN = 5 * 60 * 1000 // 5分钟

// 页面活跃状态
let isPageActive = true

// 监听页面可见性变化
document.addEventListener('visibilitychange', () => {
  isPageActive = document.visibilityState === 'visible'
})

/**
 * 显示联系人上线通知
 * @param {Object} contact 联系人对象
 * @param {Object|null} selectedContact 当前选中的联系人
 * @returns {boolean} 是否显示了通知
 */
export function showContactOnlineNotification(contact, selectedContact = null) {
  if (!contact || !contact.id) {
    console.warn('无效的联系人对象')
    return false
  }

  // 获取当前用户信息
  const userStore = useUserStore()
  const currentUserId = userStore.userInfo?.id

  // 如果是当前用户自己，不显示通知
  if (String(contact.id) === String(currentUserId)) {
    console.log('当前用户自己上线，不显示通知')
    return false
  }

  // 如果是当前选中的联系人，不显示通知
  if (selectedContact && String(selectedContact.id) === String(contact.id)) {
    console.log('当前选中的联系人上线，不显示通知')
    return false
  }

  // 如果页面不活跃，不显示通知
  if (!isPageActive) {
    console.log('页面不活跃，不显示通知')
    return false
  }

  // 检查是否在冷却期内
  const contactId = String(contact.id)
  const now = Date.now()
  const lastNotified = recentNotifications.get(contactId)

  if (lastNotified && (now - lastNotified) < NOTIFICATION_COOLDOWN) {
    console.log(`联系人 ${contact.name} 在冷却期内，不重复通知`)
    return false
  }

  // 显示通知
  console.log(`显示联系人 ${contact.name} 上线通知`)
  ElMessage({
    message: `${contact.name} 已上线`,
    type: 'success',
    duration: 3000
  })

  // 更新最近通知时间
  recentNotifications.set(contactId, now)

  // 清理过期的通知记录
  cleanupOldNotifications()

  return true
}

/**
 * 清理过期的通知记录
 */
function cleanupOldNotifications() {
  const now = Date.now()
  const expireTime = now - NOTIFICATION_COOLDOWN

  // 删除过期的通知记录
  for (const [contactId, timestamp] of recentNotifications.entries()) {
    if (timestamp < expireTime) {
      recentNotifications.delete(contactId)
    }
  }
}

/**
 * 重置特定联系人的通知冷却时间
 * @param {string} contactId 联系人ID
 */
export function resetNotificationCooldown(contactId) {
  if (contactId) {
    recentNotifications.delete(String(contactId))
  }
}

/**
 * 重置所有联系人的通知冷却时间
 */
export function resetAllNotificationCooldowns() {
  recentNotifications.clear()
}

export default {
  showContactOnlineNotification,
  resetNotificationCooldown,
  resetAllNotificationCooldowns
}
