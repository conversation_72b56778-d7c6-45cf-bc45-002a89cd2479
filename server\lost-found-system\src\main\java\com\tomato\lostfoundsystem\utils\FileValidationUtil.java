package com.tomato.lostfoundsystem.utils;

import com.tomato.lostfoundsystem.enums.FileType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件验证工具类
 * 用于验证上传文件的大小和类型
 */
@Component
public class FileValidationUtil {

    @Value("${file.upload.max-size:524288000}")  // 默认500MB
    private long maxFileSize;

    @Value("${file.upload.allowed-types.image:jpg,jpeg,png,gif,webp,bmp,tiff}")
    private String allowedImageTypes;

    @Value("${file.upload.allowed-types.video:mp4,mov,avi,wmv,flv,mkv,webm,m4v,3gp}")
    private String allowedVideoTypes;

    @Value("${file.upload.allowed-types.audio:mp3,wav,ogg,m4a,aac,flac,wma}")
    private String allowedAudioTypes;

    @Value("${file.upload.allowed-types.document:pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv,rtf,zip,rar,7z}")
    private String allowedDocumentTypes;

    private Map<FileType, List<String>> allowedTypeMap;

    /**
     * 初始化允许的文件类型映射
     */
    public void initAllowedTypeMap() {
        allowedTypeMap = new HashMap<>();
        allowedTypeMap.put(FileType.IMAGE, Arrays.asList(allowedImageTypes.split(",")));
        allowedTypeMap.put(FileType.VIDEO, Arrays.asList(allowedVideoTypes.split(",")));
        allowedTypeMap.put(FileType.AUDIO, Arrays.asList(allowedAudioTypes.split(",")));
        allowedTypeMap.put(FileType.DOCUMENT, Arrays.asList(allowedDocumentTypes.split(",")));
    }

    /**
     * 验证文件大小
     * @param file 上传的文件
     * @return 是否符合大小限制
     */
    public boolean validateFileSize(MultipartFile file) {
        return file.getSize() <= maxFileSize;
    }

    /**
     * 验证文件类型
     * @param file 上传的文件
     * @param fileType 期望的文件类型
     * @return 是否是允许的文件类型
     */
    public boolean validateFileType(MultipartFile file, FileType fileType) {
        if (allowedTypeMap == null) {
            initAllowedTypeMap();
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            return false;
        }

        String extension = getFileExtension(originalFilename).toLowerCase();
        List<String> allowedExtensions = allowedTypeMap.get(fileType);

        return allowedExtensions != null && allowedExtensions.contains(extension);
    }

    /**
     * 获取文件扩展名（不包含点）
     * @param filename 文件名
     * @return 文件扩展名
     */
    public static String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty() || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1);
    }

    /**
     * 获取最大文件大小（字节）
     * @return 最大文件大小
     */
    public long getMaxFileSize() {
        return maxFileSize;
    }

    /**
     * 获取最大文件大小的可读形式
     * @return 可读形式的最大文件大小（如：500MB）
     */
    public String getReadableMaxFileSize() {
        if (maxFileSize < 1024) {
            return maxFileSize + " B";
        } else if (maxFileSize < 1024 * 1024) {
            return String.format("%.2f KB", maxFileSize / 1024.0);
        } else if (maxFileSize < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", maxFileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", maxFileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
