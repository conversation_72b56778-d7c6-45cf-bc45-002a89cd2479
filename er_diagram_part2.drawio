<mxfile host="app.diagrams.net" modified="2023-06-15T12:00:00.000Z" agent="5.0 (Windows NT 10.0; Win64; x64)" etag="your-etag" version="14.7.7" type="device">
  <diagram id="lost-found-er-diagram" name="失物招领系统ER图">
    <mxGraphModel dx="1422" dy="762" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 实体：用户 -->
        <mxCell id="user" value="用户" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="200" y="200" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 用户属性 -->
        <mxCell id="user_id" value="用户ID" style="ellipse;whiteSpace=wrap;html=1;align=center;fontStyle=4;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="80" y="120" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="username" value="用户名" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="170" y="90" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="password" value="密码" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="90" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="phone" value="手机号" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="350" y="120" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：失物信息 -->
        <mxCell id="lost_item" value="失物信息" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="120" y="400" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 失物信息属性 -->
        <mxCell id="lost_id" value="失物ID" style="ellipse;whiteSpace=wrap;html=1;align=center;fontStyle=4;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="20" y="350" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="lost_title" value="标题" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="20" y="400" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="lost_place" value="丢失地点" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="20" y="450" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：招领信息 -->
        <mxCell id="found_item" value="招领信息" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="400" y="400" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 招领信息属性 -->
        <mxCell id="found_id" value="招领ID" style="ellipse;whiteSpace=wrap;html=1;align=center;fontStyle=4;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="520" y="350" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="found_title" value="标题" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="520" y="400" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="found_place" value="拾取地点" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="520" y="450" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：物品图片 -->
        <mxCell id="item_image" value="物品图片" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="550" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 物品图片属性 -->
        <mxCell id="image_id" value="图片ID" style="ellipse;whiteSpace=wrap;html=1;align=center;fontStyle=4;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="180" y="600" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="image_url" value="图片URL" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="270" y="600" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="is_main" value="是否主图" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="360" y="600" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：特征向量 -->
        <mxCell id="feature_vector" value="特征向量" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="650" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 特征向量属性 -->
        <mxCell id="vector_id" value="向量ID" style="ellipse;whiteSpace=wrap;html=1;align=center;fontStyle=4;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="180" y="700" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="vector_data" value="特征数据" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="270" y="700" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：匹配记录 -->
        <mxCell id="match_record" value="匹配记录" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="260" y="450" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 匹配记录属性 -->
        <mxCell id="match_id" value="匹配ID" style="ellipse;whiteSpace=wrap;html=1;align=center;fontStyle=4;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="220" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="match_score" value="匹配分数" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="320" y="500" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：聊天会话 -->
        <mxCell id="chat_session" value="聊天会话" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="500" y="200" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 聊天会话属性 -->
        <mxCell id="session_id" value="会话ID" style="ellipse;whiteSpace=wrap;html=1;align=center;fontStyle=4;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="620" y="170" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="create_time" value="创建时间" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="620" y="220" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：聊天消息 -->
        <mxCell id="chat_message" value="聊天消息" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="500" y="300" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 聊天消息属性 -->
        <mxCell id="message_id" value="消息ID" style="ellipse;whiteSpace=wrap;html=1;align=center;fontStyle=4;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="620" y="270" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="content" value="消息内容" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="620" y="320" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- 实体：系统公告 -->
        <mxCell id="system_announcement" value="系统公告" style="whiteSpace=wrap;html=1;align=center;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="350" y="100" width="100" height="40" as="geometry" />
        </mxCell>
        
        <!-- 系统公告属性 -->
        <mxCell id="announcement_id" value="公告ID" style="ellipse;whiteSpace=wrap;html=1;align=center;fontStyle=4;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="470" y="70" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="title" value="标题" style="ellipse;whiteSpace=wrap;html=1;align=center;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="470" y="120" width="80" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
