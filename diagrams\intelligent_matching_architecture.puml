@startuml 智能匹配系统架构

skinparam backgroundColor white
skinparam defaultFontName Microsoft YaHei
skinparam defaultFontSize 12
skinparam shadowing false
skinparam ArrowColor #5D6D7E
skinparam ArrowThickness 1.2

skinparam rectangle {
  BackgroundColor #F8F9F9
  BorderColor #5D6D7E
  FontColor #2C3E50
  BorderThickness 1
  Shadowing false
  RoundCorner 10
}

skinparam database {
  BackgroundColor #EBF5FB
  BorderColor #3498DB
}

skinparam cloud {
  BackgroundColor #E8F8F5
  BorderColor #16A085
}

skinparam queue {
  BackgroundColor #FEF9E7
  BorderColor #F39C12
}

skinparam component {
  BackgroundColor #F4ECF7
  BorderColor #8E44AD
}

title <font size=18><b>基于CLIP+FAISS的智能匹配系统架构</b></font>

rectangle "前端应用" as Frontend {
  rectangle "图像上传" as ImageUpload
  rectangle "文本描述" as TextInput
  rectangle "匹配结果展示" as ResultDisplay
  rectangle "实时通知" as Notification
}

rectangle "后端服务" as Backend {
  rectangle "物品管理服务" as ItemService
  rectangle "用户服务" as UserService
  rectangle "匹配历史服务" as HistoryService
  
  rectangle "智能匹配服务" as MatchService {
    component "特征提取模块" as FeatureExtraction
    component "向量搜索模块" as VectorSearch
    component "多模态融合模块" as ModalFusion
    component "相似度计算模块" as SimilarityCalculation
  }
}

cloud "CLIP+FAISS API服务" as ClipFaissAPI {
  component "CLIP模型" as ClipModel
  component "FAISS索引" as FaissIndex
  component "图像特征提取" as ImageFeature
  component "文本特征提取" as TextFeature
  component "特征向量存储" as VectorStorage
}

database "数据存储" as Storage {
  database "物品信息" as ItemDB
  database "用户数据" as UserDB
  database "匹配历史" as HistoryDB
  database "向量索引" as VectorDB
}

queue "消息队列" as MessageQueue {
  queue "匹配任务队列" as MatchQueue
  queue "通知队列" as NotifyQueue
}

' 前端与后端交互
Frontend --> Backend : HTTP/WebSocket请求
Backend --> Frontend : 返回结果/推送通知

' 后端服务内部交互
ItemService <--> MatchService : 物品信息交互
UserService <--> MatchService : 用户权限验证
HistoryService <--> MatchService : 记录匹配历史

' 匹配服务内部组件
FeatureExtraction --> ModalFusion : 传递特征向量
ModalFusion --> VectorSearch : 融合后的特征向量
VectorSearch --> SimilarityCalculation : 候选结果
SimilarityCalculation --> MatchService : 排序后的匹配结果

' 后端与CLIP+FAISS交互
MatchService --> ClipFaissAPI : 特征提取/向量搜索请求
ClipFaissAPI --> MatchService : 返回特征向量/搜索结果

' CLIP+FAISS内部组件
ImageFeature --> ClipModel : 图像输入
TextFeature --> ClipModel : 文本输入
ClipModel --> VectorStorage : 生成特征向量
VectorStorage --> FaissIndex : 存储向量
FaissIndex --> ClipFaissAPI : 相似度搜索

' 数据存储交互
Backend <--> Storage : 数据读写
ClipFaissAPI <--> VectorDB : 向量索引读写

' 消息队列交互
Backend --> MessageQueue : 发布消息
MessageQueue --> Backend : 消费消息

note right of ClipFaissAPI
  CLIP模型负责将图像和文本转换为统一的特征空间
  FAISS提供高效的向量相似度搜索
end note

note right of ModalFusion
  多模态融合模块将图像和文本特征
  按照不同权重进行融合，提高匹配精度
end note

note bottom of MessageQueue
  消息队列实现异步处理和实时通知，
  提高系统响应速度和用户体验
end note

@enduml