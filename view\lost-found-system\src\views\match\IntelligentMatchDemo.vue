<template>
  <div class="intelligent-match">
    <AuthCheck title="需要登录" message="智能匹配演示功能需要登录后才能使用，请先登录">
      <div class="page-header">
        <h2>智能匹配演示</h2>
        <p class="subtitle">基于CLIP+FAISS的多模态物品匹配系统</p>
      </div>

    <el-card class="search-card">
      <div class="search-form">
        <div class="search-tabs">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="图像匹配" name="image">
              <div class="image-upload">
                <el-upload
                  class="upload-container"
                  action="#"
                  :auto-upload="false"
                  :show-file-list="false"
                  :on-change="handleImageChange"
                  accept="image/jpeg,image/jpg,image/png"
                >
                  <div v-if="!imageUrl" class="upload-placeholder">
                    <el-icon class="upload-icon"><Plus /></el-icon>
                    <div class="upload-text">点击上传图片</div>
                    <div class="upload-hint">支持jpg、png、jpeg格式，不超过5MB</div>
                  </div>
                  <img v-else :src="imageUrl" class="preview-image" />
                </el-upload>

                <div v-if="imageUrl" class="image-actions">
                  <el-button type="danger" @click="removeImage">删除图片</el-button>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="文本匹配" name="text">
              <div class="text-search-form">
                <div class="form-item">
                  <div class="form-label">物品名称：</div>
                  <el-input
                    v-model="itemName"
                    placeholder="请输入物品名称，如：黑色长款钱包"
                  />
                </div>
                <div class="form-item">
                  <div class="form-label">物品描述：</div>
                  <el-input
                    v-model="itemDescription"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入物品详细描述，如：内有学生证和银行卡"
                  />
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="混合匹配" name="hybrid">
              <div class="hybrid-search">
                <div class="image-section">
                  <h4>上传物品图片</h4>
                  <el-upload
                    class="upload-container small"
                    action="#"
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="handleImageChange"
                    accept="image/jpeg,image/jpg,image/png"
                  >
                    <div v-if="!imageUrl" class="upload-placeholder">
                      <el-icon class="upload-icon"><Plus /></el-icon>
                      <div class="upload-text">点击上传图片</div>
                    </div>
                    <img v-else :src="imageUrl" class="preview-image" />
                  </el-upload>
                </div>

                <div class="text-section">
                  <h4>输入物品信息</h4>
                  <div class="form-item">
                    <div class="form-label">物品名称：</div>
                    <el-input
                      v-model="itemName"
                      placeholder="请输入物品名称，如：黑色长款钱包"
                    />
                  </div>
                  <div class="form-item">
                    <div class="form-label">物品描述：</div>
                    <el-input
                      v-model="itemDescription"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入物品详细描述，如：内有学生证和银行卡"
                    />
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div class="search-options">
          <div class="option-group">
            <span class="option-label">物品类型：</span>
            <el-radio-group v-model="itemType">
              <el-radio-button label="LOST">寻找失物</el-radio-button>
              <el-radio-button label="FOUND">寻找拾物</el-radio-button>
            </el-radio-group>
          </div>

          <div class="option-group">
            <span class="option-label">物品类别：</span>
            <el-select v-model="category" placeholder="选择类别" clearable>
              <el-option label="全部类别" value="" />
              <el-option label="电子设备" value="电子设备" />
              <el-option label="证件卡片" value="证件卡片" />
              <el-option label="生活用品" value="生活用品" />
              <el-option label="学习用品" value="学习用品" />
              <el-option label="服装配饰" value="服装配饰" />
              <el-option label="钱包钥匙" value="钱包钥匙" />
              <el-option label="图书资料" value="图书资料" />
              <el-option label="其他" value="其他" />
            </el-select>
          </div>
        </div>

        <div class="search-actions">
          <el-button type="primary" :loading="loading" @click="handleSearch">开始匹配</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>
    </el-card>

    <div v-if="searched" class="result-section">
      <MatchResultList
        :results="matchResults"
        :loading="loading"
        :has-more="hasMore"
        :loading-more="loadingMore"
        @load-more="loadMoreResults"
        @view-detail="viewItemDetail"
        @contact-owner="contactItemOwner"
      />
    </div>
    </AuthCheck>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import MatchResultList from '@/components/MatchResultList.vue'
import { generateMatchResults } from '@/utils/mockData'
import AuthCheck from '@/components/AuthCheck.vue'

// 搜索参数
const activeTab = ref('image')
const imageUrl = ref('')
const itemName = ref('')
const itemDescription = ref('')
const itemType = ref('LOST')
const category = ref('')

// 结果状态
const loading = ref(false)
const searched = ref(false)
const matchResults = ref([])
const hasMore = ref(false)
const loadingMore = ref(false)

// 处理图片上传
const handleImageChange = (file) => {
  // 获取文件扩展名
  const fileName = file.name || '';
  const fileExtension = fileName.toLowerCase().split('.').pop();

  // 检查文件扩展名而不是MIME类型
  const isValidExtension = ['jpg', 'jpeg', 'png'].includes(fileExtension);
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isValidExtension) {
    ElMessage.error('只能上传JPG或PNG格式的图片！');
    return false;
  }

  if (!isLt5M) {
    ElMessage.error('图片大小不能超过5MB！');
    return false;
  }

  // 打印文件信息以便调试
  console.log('文件信息:', {
    name: file.name,
    type: file.type,
    extension: fileExtension,
    size: (file.size / 1024 / 1024).toFixed(2) + 'MB'
  });

  imageUrl.value = URL.createObjectURL(file.raw);
};

// 删除图片
const removeImage = () => {
  imageUrl.value = ''
};

// 重置搜索
const resetSearch = () => {
  imageUrl.value = ''
  itemName.value = ''
  itemDescription.value = ''
  category.value = ''
  searched.value = false
  matchResults.value = []
};

// 执行搜索
const handleSearch = () => {
  // 验证搜索条件
  if (activeTab.value === 'image' && !imageUrl.value) {
    ElMessage.warning('请上传物品图片')
    return
  }

  if (activeTab.value === 'text' && !itemName.value && !itemDescription.value) {
    ElMessage.warning('请至少输入物品名称或物品描述')
    return
  }

  if (activeTab.value === 'hybrid' && !imageUrl.value && !itemName.value && !itemDescription.value) {
    ElMessage.warning('请上传图片或输入物品信息')
    return
  }

  // 开始搜索
  loading.value = true
  searched.value = true
  matchResults.value = []

  // 模拟API请求延迟
  setTimeout(() => {
    // 使用模拟数据
    matchResults.value = generateMatchResults(Math.floor(Math.random() * 10) + 5, itemType.value)

    // 如果有类别筛选，进行过滤
    if (category.value) {
      matchResults.value = matchResults.value.filter(item => item.category === category.value)
    }

    loading.value = false
    hasMore.value = matchResults.value.length >= 5
  }, 1500)
};

// 加载更多结果
const loadMoreResults = () => {
  loadingMore.value = true

  // 模拟加载更多
  setTimeout(() => {
    const moreResults = generateMatchResults(Math.floor(Math.random() * 5) + 3, itemType.value)

    // 如果有类别筛选，进行过滤
    const filteredResults = category.value
      ? moreResults.filter(item => item.category === category.value)
      : moreResults

    matchResults.value = [...matchResults.value, ...filteredResults]
    loadingMore.value = false
    hasMore.value = Math.random() > 0.5 // 随机决定是否还有更多
  }, 1000)
};

// 查看物品详情
const viewItemDetail = (item) => {
  ElMessage.success(`查看物品详情：${item.itemName}`)
  // 实际应用中应该跳转到物品详情页
};

// 联系物主
const contactItemOwner = (item) => {
  ElMessage.success(`联系${item.itemType === 'LOST' ? '失主' : '拾主'}：${item.contactPerson}`)
  // 实际应用中应该跳转到聊天页面
};
</script>

<style scoped>
.intelligent-match {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.page-header {
  text-align: center;
  margin-bottom: 24px;
}

.page-header h2 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 8px;
}

.subtitle {
  font-size: 16px;
  color: #909399;
  margin: 0;
}

.search-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.upload-container {
  width: 100%;
  height: 200px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.upload-container:hover {
  border-color: #409EFF;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 4px;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-actions {
  margin-top: 12px;
  text-align: center;
}

.hybrid-search {
  display: flex;
  gap: 24px;
}

.image-section, .text-section {
  flex: 1;
}

.image-section h4, .text-section h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #606266;
}

.upload-container.small {
  height: 150px;
}

.search-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.option-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-label {
  font-size: 14px;
  color: #606266;
}

.search-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.result-section {
  margin-top: 24px;
}

.text-search-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
}

.form-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}
</style>
