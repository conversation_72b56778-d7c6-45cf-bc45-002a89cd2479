-- 为match_notifications表添加元数据和相似度详情字段

-- 添加元数据字段
ALTER TABLE match_notifications 
ADD COLUMN metadata TEXT NULL COMMENT '元数据(JSON格式)';

-- 添加匹配类型字段
ALTER TABLE match_notifications 
ADD COLUMN match_type VARCHAR(20) NULL COMMENT '匹配类型(IMAGE_TO_IMAGE/TEXT_TO_TEXT等)';

-- 添加文本-文本相似度字段
ALTER TABLE match_notifications 
ADD COLUMN text_to_text_similarity FLOAT NULL COMMENT '文本-文本相似度';

-- 添加文本-图像相似度字段
ALTER TABLE match_notifications 
ADD COLUMN text_to_image_similarity FLOAT NULL COMMENT '文本-图像相似度';

-- 添加图像-文本相似度字段
ALTER TABLE match_notifications 
ADD COLUMN image_to_text_similarity FLOAT NULL COMMENT '图像-文本相似度';

-- 添加图像-图像相似度字段
ALTER TABLE match_notifications 
ADD COLUMN image_to_image_similarity FLOAT NULL COMMENT '图像-图像相似度';

-- 添加索引以提高查询性能
CREATE INDEX idx_match_notifications_match_type ON match_notifications(match_type);
