package com.tomato.lostfoundsystem.dto;

import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.entity.MessageAttachment;
import com.tomato.lostfoundsystem.enums.MessageType;
import lombok.Data;


import java.util.Date;
import java.util.List;

@Data
public class ChatMessageDTO {

    private Long messageId;
    private Long senderId;
    private Long receiverId;
    private String message;
    private MessageType messageType;
    private Date timestamp;

    private Integer isRead;
    private MessageAttachment attachment;  // 单个附件信息（向后兼容）
    private List<MessageAttachment> attachments;  // 多个附件信息

    public ChatMessageDTO(ChatMessage chatMessage, MessageAttachment attachment) {
        if (chatMessage != null) {
            this.messageId = chatMessage.getId();
            this.senderId = chatMessage.getSenderId();
            this.receiverId = chatMessage.getReceiverId();
            this.message = chatMessage.getMessage();
            this.messageType = chatMessage.getMessageType();
            this.timestamp = chatMessage.getTimestamp();
        }
        this.attachment = attachment;
    }

    public ChatMessageDTO(ChatMessage chatMessage, List<MessageAttachment> attachments) {
        if (chatMessage != null) {
            this.messageId = chatMessage.getId();
            this.senderId = chatMessage.getSenderId();
            this.receiverId = chatMessage.getReceiverId();
            this.message = chatMessage.getMessage();
            this.messageType = chatMessage.getMessageType();
            this.timestamp = chatMessage.getTimestamp();
        }
        this.attachments = attachments;
        // 为了向后兼容，如果有附件，设置第一个附件为单个附件
        if (attachments != null && !attachments.isEmpty()) {
            this.attachment = attachments.get(0);
        }
    }
}

