/*
Navicat MySQL Data Transfer

Source Server         : localhost_3306
Source Server Version : 80013
Source Host           : localhost:3306
Source Database       : lost_found

Target Server Type    : MYSQL
Target Server Version : 80013
File Encoding         : 65001

Date: 2025-05-08 22:44:33
*/

SET FOREIGN_KEY_CHECKS=0;
-- ----------------------------
-- Table structure for `chat_messages`1
-- ----------------------------
DROP TABLE IF EXISTS `chat_messages`;
CREATE TABLE `chat_messages` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sender_id` bigint(20) NOT NULL,
  `receiver_id` bigint(20) NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `timestamp` datetime DEFAULT CURRENT_TIMESTAMP,
  `message_type` enum('TEXT','IMA<PERSON>','VIDEO','DOCUMENT','AUDIO') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `audio_duration` int(11) DEFAULT NULL,
  `video_duration` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `chat_messages_ibfk_1` (`sender_id`),
  KEY `chat_messages_ibfk_2` (`receiver_id`),
  CONSTRAINT `chat_messages_ibfk_1` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`),
  CONSTRAINT `chat_messages_ibfk_2` FOREIGN KEY (`receiver_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of chat_messages
-- ----------------------------
INSERT INTO chat_messages VALUES ('1', '5', '8', '?', '2025-04-28 17:12:09', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('2', '5', '8', '?', '2025-04-28 22:51:54', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('3', '5', '8', '?', '2025-04-28 22:56:38', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('4', '5', '8', '?', '2025-04-28 23:09:57', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('5', '8', '5', '你好', '2025-04-28 23:31:22', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('6', '5', '8', '请为这是你的失物吗？', '2025-04-29 00:00:32', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('7', '5', '8', '这不是我的，谢谢你的提醒?', '2025-04-29 00:07:12', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('8', '5', '8', '好的，谢谢夸奖?', '2025-04-29 00:16:20', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('9', '8', '5', 'hello,老弟?', '2025-04-29 01:09:30', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('10', '8', '5', '老弟，今天吃啥呀', '2025-04-29 01:10:08', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('11', '8', '5', '嘻嘻?', '2025-04-29 01:13:46', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('12', '5', '8', '哈哈哈·?', '2025-04-29 01:17:43', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('13', '5', '8', '还不睡觉吗？', '2025-04-29 01:48:59', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('14', '5', '8', '还装', '2025-04-29 01:51:26', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('15', '5', '8', '在吗？', '2025-04-29 10:55:10', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('16', '5', '8', '中午好呀', '2025-04-29 11:01:40', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('17', '8', '5', '你好', '2025-04-29 11:11:21', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('18', '5', '8', '在吗？', '2025-04-29 11:31:36', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('19', '8', '5', 'hello，laodi', '2025-04-29 11:32:13', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('20', '5', '8', '你好呀', '2025-04-29 11:53:48', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('21', '5', '8', '你好', '2025-04-29 12:02:08', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('22', '8', '5', '你好', '2025-04-29 12:11:37', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('23', '8', '5', 'how are you', '2025-04-29 12:47:48', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('24', '5', '8', '在吗？', '2025-05-02 01:30:01', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('25', '5', '8', '在吗？', '2025-05-02 02:01:05', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('26', '8', '5', 'x嘻嘻', '2025-05-02 02:01:45', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('27', '8', '5', 'hello', '2025-05-02 02:22:03', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('28', '5', '8', '???', '2025-05-02 02:50:15', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('29', '5', '8', '哈哈哈哈', '2025-05-02 02:52:33', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('30', '8', '5', '嘻嘻嘻', '2025-05-02 02:53:15', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('31', '5', '8', '?', '2025-05-04 18:52:06', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('32', '8', '5', '?', '2025-05-04 18:53:03', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('33', '8', '5', '略略略', '2025-05-04 22:58:48', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('34', '5', '8', '哈哈哈', '2025-05-04 22:59:12', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('35', '8', '5', '嘻嘻', '2025-05-04 23:00:13', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('36', '8', '5', '哈喽老弟', '2025-05-04 23:13:58', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('37', '5', '8', '略略', '2025-05-04 23:14:57', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('38', '8', '5', '哈喽，老弟', '2025-05-04 23:28:34', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('39', '5', '8', '怎么回事', '2025-05-04 23:30:44', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('40', '8', '5', '你在吗？', '2025-05-04 23:37:12', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('41', '5', '8', '你在啊', '2025-05-04 23:37:24', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('42', '8', '5', '嘻嘻', '2025-05-04 23:37:39', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('43', '8', '5', '哈哈哈', '2025-05-04 23:38:15', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('44', '5', '8', '略略', '2025-05-04 23:38:33', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('45', '5', '8', '在吗？', '2025-05-05 00:00:33', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('46', '8', '5', '不在，你呢', '2025-05-05 00:00:57', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('47', '8', '5', 'yturieop', '2025-05-05 00:51:56', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('48', '5', '8', 'yturieow', '2025-05-05 00:52:37', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('49', '5', '8', '呃呃呃呃', '2025-05-05 00:54:39', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('50', '8', '5', '嘻嘻', '2025-05-05 00:56:54', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('51', '5', '8', '哈哈哈', '2025-05-05 01:03:14', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('52', '8', '5', '嘻嘻', '2025-05-05 01:04:39', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('53', '5', '8', '略略略', '2025-05-05 01:04:57', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('54', '8', '5', '哈哈', '2025-05-05 01:17:14', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('55', '5', '8', 'flafhgrioirig', '2025-05-05 01:18:04', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('56', '8', '5', '安抚七二九', '2025-05-05 01:22:24', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('57', '8', '5', '嘻嘻', '2025-05-05 01:40:51', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('58', '5', '8', '哈哈哈', '2025-05-05 04:11:23', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('59', '8', '5', '略略略', '2025-05-05 04:11:40', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('60', '5', '8', '在吗？', '2025-05-06 09:54:12', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('61', '8', '5', '呜呜呜', '2025-05-06 09:54:35', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('62', '8', '5', '1', '2025-05-06 10:24:04', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('63', '8', '5', '嘻嘻', '2025-05-07 11:34:37', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('64', '8', '5', '你好', '2025-05-07 12:08:36', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('65', '5', '8', '你好', '2025-05-07 12:09:26', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('66', '5', '8', '你好', '2025-05-07 15:22:35', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('67', '8', '5', '不好', '2025-05-07 15:23:45', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('68', '8', '5', '你好', '2025-05-07 16:38:04', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('69', '5', '8', '哈哈哈', '2025-05-07 16:43:07', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('70', '5', '8', '哈哈哈', '2025-05-07 17:27:45', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('71', '8', '5', '你好', '2025-05-07 17:27:58', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('72', '8', '5', '再见啦', '2025-05-07 17:28:12', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('73', '8', '5', '?', '2025-05-07 17:28:27', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('74', '8', '5', '哈哈哈哈', '2025-05-07 17:28:47', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('75', '8', '5', '哈哈哈', '2025-05-07 18:29:12', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('76', '8', '5', '在吗', '2025-05-07 18:29:51', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('77', '5', '8', '在的，在的，有什么问题吗？', '2025-05-07 18:30:29', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('78', '5', '8', '嘻嘻嘻', '2025-05-07 18:30:56', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('79', '8', '5', '你今晚吃啥呀', '2025-05-07 18:31:14', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('80', '5', '8', '不知道啊', '2025-05-07 18:31:36', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('81', '8', '5', '你吃了吗?', '2025-05-07 18:31:48', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('82', '8', '5', '没呢，刚刚下班，累死了?', '2025-05-07 18:33:22', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('83', '5', '8', '我也是啊，真的烦死了，心累啊', '2025-05-07 18:34:06', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('84', '5', '8', '哈哈哈', '2025-05-07 18:34:27', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('85', '5', '8', '嘻嘻', '2025-05-07 18:34:29', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('86', '5', '8', '略略略', '2025-05-07 18:34:32', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('87', '5', '8', '嘻嘻', '2025-05-07 18:34:36', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('88', '8', '5', 'FNVJDSGDNL', '2025-05-07 18:34:56', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('89', '8', '5', 'FHGDKCKNFNRKVJV', '2025-05-07 18:35:00', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('90', '8', '5', 'VVFJFNVBKBL', '2025-05-07 18:35:04', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('91', '8', '5', 'DKFJEOWAA', '2025-05-07 18:35:06', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('92', '8', '5', 'CLFJWOIEA', '2025-05-07 18:35:09', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('93', '5', '8', 'RHFHVVBV', '2025-05-07 18:35:14', 'TEXT', '0', '0');
INSERT INTO chat_messages VALUES ('94', '5', '8', 'CODDXBVFVNB', '2025-05-07 18:35:18', 'TEXT', '0', '0');

-- ----------------------------
-- Table structure for `chat_sessions`2
-- ----------------------------
DROP TABLE IF EXISTS `chat_sessions`;
CREATE TABLE `chat_sessions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user1_id` bigint(20) NOT NULL,
  `user2_id` bigint(20) NOT NULL,
  `last_message_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `chat_sessions_ibfk_1` (`user1_id`),
  KEY `chat_sessions_ibfk_2` (`user2_id`),
  CONSTRAINT `chat_sessions_ibfk_1` FOREIGN KEY (`user1_id`) REFERENCES `users` (`id`),
  CONSTRAINT `chat_sessions_ibfk_2` FOREIGN KEY (`user2_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of chat_sessions
-- ----------------------------
INSERT INTO chat_sessions VALUES ('1', '5', '8', '2025-05-07 19:25:21');

-- ----------------------------
-- Table structure for `found_items`3
-- ----------------------------
DROP TABLE IF EXISTS `found_items`;
CREATE TABLE `found_items` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '拾取人 ID',
  `item_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '物品描述',
  `found_time` datetime DEFAULT NULL COMMENT '拾取时间',
  `found_location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拾取地点',
  `image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片 URL',
  `status` enum('UNCLAIMED','RETURNED') COLLATE utf8mb4_unicode_ci DEFAULT 'UNCLAIMED' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `audit_status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `found_items_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of found_items
-- ----------------------------
INSERT INTO found_items VALUES ('1', '5', '水杯', '红色保温杯，未损坏', '2025-04-04 15:30:00', '教学楼B区一楼', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1743761801221_红色保温杯.jpg', 'UNCLAIMED', '2025-04-04 18:16:42', 'PENDING');
INSERT INTO found_items VALUES ('3', '8', 'ipad', 'ipadAir6，海绵宝宝外壳', '2025-04-15 15:51:20', '图书馆', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1744703525329_ipad.jpg', 'UNCLAIMED', '2025-04-15 15:52:06', 'PENDING');

-- ----------------------------
-- Table structure for `found_item_audit`4
-- ----------------------------
DROP TABLE IF EXISTS `found_item_audit`;
CREATE TABLE `found_item_audit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) NOT NULL,
  `audit_status` enum('PENDING','APPROVED','REJECTED') COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  `audit_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `auditor_id` bigint(20) NOT NULL,
  `remarks` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `fk_found_item` (`item_id`),
  CONSTRAINT `fk_found_item` FOREIGN KEY (`item_id`) REFERENCES `found_items` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of found_item_audit
-- ----------------------------

-- ----------------------------
-- Table structure for `item_attachments`
-- ----------------------------
DROP TABLE IF EXISTS `item_attachments`;
CREATE TABLE `item_attachments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `lost_item_id` bigint(20) NOT NULL,
  `found_item_id` bigint(20) NOT NULL,
  `image_similarity` decimal(5,4) NOT NULL,
  `text_similarity` decimal(5,4) NOT NULL,
  `total_similarity` decimal(5,4) NOT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `lost_item_id` (`lost_item_id`),
  KEY `found_item_id` (`found_item_id`),
  CONSTRAINT `item_attachments_ibfk_1` FOREIGN KEY (`lost_item_id`) REFERENCES `lost_items` (`id`),
  CONSTRAINT `item_attachments_ibfk_2` FOREIGN KEY (`found_item_id`) REFERENCES `found_items` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of item_attachments
-- ----------------------------

-- ----------------------------
-- Table structure for `item_feature_vectors`5
-- ----------------------------
DROP TABLE IF EXISTS `item_feature_vectors`;
CREATE TABLE `item_feature_vectors` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) NOT NULL COMMENT '物品ID',
  `item_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品类型（LOST/FOUND）',
  `text_vector` blob COMMENT '文本特征向量',
  `image_vector` blob COMMENT '图像特征向量',
  `vector_version` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '向量版本',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item_id_type` (`item_id`,`item_type`),
  KEY `idx_item_type` (`item_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物品特征向量表';

-- ----------------------------
-- Records of item_feature_vectors
-- ----------------------------

-- ----------------------------
-- Table structure for `item_images`6
-- ----------------------------
DROP TABLE IF EXISTS `item_images`;
CREATE TABLE `item_images` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) NOT NULL COMMENT '物品ID',
  `item_type` varchar(10) NOT NULL COMMENT '物品类型(LOST/FOUND)',
  `image_url` varchar(255) NOT NULL COMMENT '图片URL',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_item_id_type` (`item_id`,`item_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='存储失物和拾物的多张图片';

-- ----------------------------
-- Records of item_images
-- ----------------------------

-- ----------------------------
-- Table structure for `lost_items`7
-- ----------------------------
DROP TABLE IF EXISTS `lost_items`;
CREATE TABLE `lost_items` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '发布人 ID',
  `item_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '物品描述',
  `lost_time` datetime DEFAULT NULL COMMENT '丢失时间',
  `lost_location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '丢失地点',
  `image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片 URL',
  `status` enum('LOST','FOUND') COLLATE utf8mb4_unicode_ci DEFAULT 'LOST' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `audit_status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `lost_items_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of lost_items
-- ----------------------------
INSERT INTO lost_items VALUES ('1', '5', '手机', '黑色手机，屏幕破损', '2025-04-02 12:00:00', ' 教学楼', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1743598138595_blackPhone.jpg', 'LOST', '2025-04-02 20:48:59', 'APPROVED');
INSERT INTO lost_items VALUES ('12', '5', '黑色钱包', '黑色钱包，内有身份证和银行卡', '2025-04-01 14:30:00', '图书馆', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/images/lost/2025/05/08/1746713112599_黑色钱包.png', 'LOST', '2025-04-01 15:00:00', 'APPROVED');
INSERT INTO lost_items VALUES ('13', '7', '蓝色雨伞', '蓝色雨伞，丢失在餐厅', '2025-04-02 18:00:00', '餐厅', 'https://example.com/image2.jpg', 'LOST', '2025-04-02 18:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('14', '8', '红色手提包', '红色手提包，内有化妆品', '2025-03-29 10:00:00', '学校门口', 'https://example.com/image3.jpg', 'LOST', '2025-03-29 10:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('15', '5', '黑色背包', '黑色背包，内有笔记本和手机', '2025-04-01 09:00:00', '教室', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/images/lost/2025/05/08/1746713171777_黑色背包.jpg', 'LOST', '2025-04-01 09:30:00', 'APPROVED');
INSERT INTO lost_items VALUES ('16', '7', '白色耳机', '白色耳机，丢失在图书馆', '2025-03-28 15:00:00', '图书馆', 'https://example.com/image5.jpg', 'LOST', '2025-03-28 15:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('17', '8', '绿色水杯', '绿色水杯，丢失在宿舍楼', '2025-03-30 20:00:00', '宿舍楼', 'https://example.com/image6.jpg', 'LOST', '2025-03-30 20:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('18', '5', '蓝色牛仔裤', '蓝色牛仔裤，丢失在超市', '2025-03-25 12:30:00', '超市', 'https://example.com/image7.jpg', 'LOST', '2025-03-25 13:00:00', 'PENDING');
INSERT INTO lost_items VALUES ('19', '7', '黑色钱包', '黑色钱包，丢失在停车场', '2025-03-27 17:00:00', '停车场', 'https://example.com/image8.jpg', 'LOST', '2025-03-27 17:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('20', '8', '红色手表', '红色手表，丢失在咖啡厅', '2025-03-31 08:00:00', '咖啡厅', 'https://example.com/image9.jpg', 'LOST', '2025-03-31 08:30:00', 'PENDING');
INSERT INTO lost_items VALUES ('21', '5', '黑色拉杆箱', '黑色拉杆箱，丢失在机场', '2025-04-03 13:30:00', '机场', 'https://example.com/image10.jpg', 'LOST', '2025-04-03 14:00:00', 'APPROVED');
INSERT INTO lost_items VALUES ('22', '8', '苹果耳机', '耳机只剩一只了，耳机仓有龙头图标', '2025-04-07 14:54:00', '篮球场', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1744008861945_苹果蓝牙耳机.jpg', 'LOST', '2025-04-07 14:54:22', 'APPROVED');
INSERT INTO lost_items VALUES ('24', '8', '保温杯', '红色外观，500ml', '2025-04-07 15:04:00', '饭堂', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1744009473956_红色保温杯.jpg', 'LOST', '2025-04-07 15:04:34', 'REJECTED');
INSERT INTO lost_items VALUES ('27', '8', '平板', 'ipad air 5,z紫色，爆屏', '2025-04-14 17:33:00', '教学楼', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/1744623199978_ipad.jpg', 'LOST', '2025-04-14 17:33:20', 'APPROVED');
INSERT INTO lost_items VALUES ('28', '5', '钱包', '哆啦A梦卡通钱包，里面人民币若干元，还有我的证件，很重要', '2025-05-08 21:55:00', '操场', 'https://laofanqi-service.oss-cn-guangzhou.aliyuncs.com/images/lost/2025/05/08/1746712702370_哆啦A梦钱包.jpg', 'LOST', '2025-05-08 21:58:23', 'PENDING');

-- ----------------------------
-- Table structure for `lost_item_audit`8
-- ----------------------------
DROP TABLE IF EXISTS `lost_item_audit`;
CREATE TABLE `lost_item_audit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) NOT NULL,
  `audit_status` enum('PENDING','APPROVED','REJECTED') COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING',
  `audit_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `auditor_id` bigint(20) NOT NULL,
  `remarks` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `fk_lost_item` (`item_id`),
  CONSTRAINT `fk_lost_item` FOREIGN KEY (`item_id`) REFERENCES `lost_items` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of lost_item_audit
-- ----------------------------
INSERT INTO lost_item_audit VALUES ('14', '27', 'APPROVED', '2025-04-22 15:15:57', '5', '');
INSERT INTO lost_item_audit VALUES ('16', '24', 'REJECTED', '2025-04-22 15:30:37', '5', '内容涉嫌违规');
INSERT INTO lost_item_audit VALUES ('17', '22', 'APPROVED', '2025-04-25 00:30:30', '5', '');
INSERT INTO lost_item_audit VALUES ('18', '21', 'APPROVED', '2025-04-25 02:04:17', '5', '');
INSERT INTO lost_item_audit VALUES ('19', '1', 'APPROVED', '2025-04-25 02:08:30', '5', '');
INSERT INTO lost_item_audit VALUES ('20', '12', 'APPROVED', '2025-04-25 02:43:38', '5', '');
INSERT INTO lost_item_audit VALUES ('21', '15', 'APPROVED', '2025-04-25 13:03:55', '5', '');

-- ----------------------------
-- Table structure for `match_history`9
-- ----------------------------
DROP TABLE IF EXISTS `match_history`;
CREATE TABLE `match_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `query_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '查询类型（TEXT/IMAGE/MIXED）',
  `query_image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '查询图片URL',
  `query_text` text COLLATE utf8mb4_unicode_ci COMMENT '查询文本',
  `item_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品类型（LOST/FOUND）',
  `result_count` int(11) DEFAULT '0' COMMENT '结果数量',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配历史表';

-- ----------------------------
-- Records of match_history
-- ----------------------------

-- ----------------------------
-- Table structure for `match_notifications`10
-- ----------------------------
DROP TABLE IF EXISTS `match_notifications`;
CREATE TABLE `match_notifications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '接收通知的用户ID',
  `match_history_id` bigint(20) DEFAULT NULL COMMENT '匹配历史ID',
  `item_id` bigint(20) NOT NULL COMMENT '匹配物品ID',
  `item_type` varchar(10) NOT NULL COMMENT '匹配物品类型（LOST/FOUND）',
  `similarity` float NOT NULL COMMENT '匹配相似度',
  `title` varchar(100) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '是否已读',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_match_history_id` (`match_history_id`),
  KEY `idx_item_id_type` (`item_id`,`item_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='匹配通知表';

-- ----------------------------
-- Records of match_notifications11
-- ----------------------------

-- ----------------------------
-- Table structure for `match_results`12
-- ----------------------------
DROP TABLE IF EXISTS `match_results`;
CREATE TABLE `match_results` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `match_history_id` bigint(20) NOT NULL COMMENT '匹配历史ID',
  `item_id` bigint(20) NOT NULL COMMENT '物品ID',
  `item_type` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品类型（LOST/FOUND）',
  `similarity_score` decimal(10,4) NOT NULL COMMENT '相似度分数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_match_history_id` (`match_history_id`),
  KEY `idx_item_id_type` (`item_id`,`item_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配结果表';

-- ----------------------------
-- Records of match_results
-- ----------------------------

-- ----------------------------
-- Table structure for `message_attachments`13
-- ----------------------------
DROP TABLE IF EXISTS `message_attachments`;
CREATE TABLE `message_attachments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` bigint(20) NOT NULL,
  `file_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_type` enum('IMAGE','VIDEO','DOCUMENT','AUDIO') COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` bigint(20) DEFAULT NULL,
  `duration` int(11) DEFAULT '0' COMMENT '媒体时长（秒），用于音频和视频',
  PRIMARY KEY (`id`),
  KEY `message_id` (`message_id`),
  CONSTRAINT `message_attachments_ibfk_1` FOREIGN KEY (`message_id`) REFERENCES `chat_messages` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of message_attachments
-- ----------------------------

-- ----------------------------
-- Table structure for `message_read_status`14
-- ----------------------------
DROP TABLE IF EXISTS `message_read_status`;
CREATE TABLE `message_read_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `message_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `read_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `message_id` (`message_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `message_read_status_ibfk_1` FOREIGN KEY (`message_id`) REFERENCES `chat_messages` (`id`),
  CONSTRAINT `message_read_status_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of message_read_status
-- ----------------------------
INSERT INTO message_read_status VALUES ('1', '1', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('2', '2', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('3', '3', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('4', '4', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('5', '5', '5', '1', '2025-05-01 22:50:52');
INSERT INTO message_read_status VALUES ('6', '6', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('7', '7', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('8', '8', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('9', '9', '5', '1', '2025-05-01 22:50:52');
INSERT INTO message_read_status VALUES ('10', '10', '5', '1', '2025-05-01 22:50:52');
INSERT INTO message_read_status VALUES ('11', '11', '5', '1', '2025-05-01 22:50:52');
INSERT INTO message_read_status VALUES ('12', '12', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('13', '13', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('14', '14', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('15', '15', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('16', '16', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('17', '17', '5', '1', '2025-05-01 22:50:52');
INSERT INTO message_read_status VALUES ('18', '18', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('19', '19', '5', '1', '2025-05-01 22:50:52');
INSERT INTO message_read_status VALUES ('20', '20', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('21', '21', '8', '1', '2025-05-02 01:13:28');
INSERT INTO message_read_status VALUES ('22', '22', '5', '1', '2025-05-01 22:50:52');
INSERT INTO message_read_status VALUES ('23', '23', '5', '1', '2025-05-01 22:50:52');
INSERT INTO message_read_status VALUES ('24', '24', '8', '1', '2025-05-02 01:30:28');
INSERT INTO message_read_status VALUES ('25', '25', '8', '1', '2025-05-02 02:01:24');
INSERT INTO message_read_status VALUES ('26', '26', '5', '1', '2025-05-02 02:01:54');
INSERT INTO message_read_status VALUES ('27', '27', '5', '1', '2025-05-02 02:22:14');
INSERT INTO message_read_status VALUES ('28', '28', '8', '1', '2025-05-02 02:50:28');
INSERT INTO message_read_status VALUES ('29', '29', '8', '1', '2025-05-02 02:52:57');
INSERT INTO message_read_status VALUES ('30', '30', '5', '1', '2025-05-02 02:56:20');
INSERT INTO message_read_status VALUES ('31', '31', '8', '1', '2025-05-04 18:52:22');
INSERT INTO message_read_status VALUES ('32', '32', '5', '1', '2025-05-04 18:54:39');
INSERT INTO message_read_status VALUES ('33', '33', '5', '1', '2025-05-04 23:00:33');
INSERT INTO message_read_status VALUES ('34', '34', '8', '1', '2025-05-04 22:59:18');
INSERT INTO message_read_status VALUES ('35', '35', '5', '1', '2025-05-04 23:00:33');
INSERT INTO message_read_status VALUES ('36', '36', '5', '1', '2025-05-04 23:21:59');
INSERT INTO message_read_status VALUES ('37', '37', '8', '1', '2025-05-04 23:17:42');
INSERT INTO message_read_status VALUES ('38', '38', '5', '1', '2025-05-04 23:28:46');
INSERT INTO message_read_status VALUES ('39', '39', '8', '1', '2025-05-04 23:32:26');
INSERT INTO message_read_status VALUES ('40', '40', '5', '1', '2025-05-04 23:38:05');
INSERT INTO message_read_status VALUES ('41', '41', '8', '1', '2025-05-04 23:38:09');
INSERT INTO message_read_status VALUES ('42', '42', '5', '1', '2025-05-04 23:37:39');
INSERT INTO message_read_status VALUES ('43', '43', '5', '1', '2025-05-04 23:41:47');
INSERT INTO message_read_status VALUES ('44', '44', '8', '1', '2025-05-04 23:40:29');
INSERT INTO message_read_status VALUES ('45', '45', '8', '1', '2025-05-05 00:00:44');
INSERT INTO message_read_status VALUES ('46', '46', '5', '1', '2025-05-05 00:01:08');
INSERT INTO message_read_status VALUES ('47', '47', '5', '1', '2025-05-05 00:52:11');
INSERT INTO message_read_status VALUES ('48', '48', '8', '1', '2025-05-05 01:04:20');
INSERT INTO message_read_status VALUES ('49', '49', '8', '1', '2025-05-05 01:04:20');
INSERT INTO message_read_status VALUES ('50', '50', '5', '1', '2025-05-05 01:02:28');
INSERT INTO message_read_status VALUES ('51', '51', '8', '1', '2025-05-05 01:04:20');
INSERT INTO message_read_status VALUES ('52', '52', '5', '1', '2025-05-05 01:11:02');
INSERT INTO message_read_status VALUES ('53', '53', '8', '1', '2025-05-05 01:11:02');
INSERT INTO message_read_status VALUES ('54', '54', '5', '1', '2025-05-05 01:17:53');
INSERT INTO message_read_status VALUES ('55', '55', '8', '1', '2025-05-05 01:27:40');
INSERT INTO message_read_status VALUES ('56', '56', '5', '1', '2025-05-05 01:27:40');
INSERT INTO message_read_status VALUES ('57', '57', '5', '1', '2025-05-05 01:56:02');
INSERT INTO message_read_status VALUES ('58', '58', '8', '1', '2025-05-05 04:11:32');
INSERT INTO message_read_status VALUES ('59', '59', '5', '1', '2025-05-05 04:11:46');
INSERT INTO message_read_status VALUES ('60', '60', '8', '1', '2025-05-06 09:54:26');
INSERT INTO message_read_status VALUES ('61', '61', '5', '1', '2025-05-06 09:54:51');
INSERT INTO message_read_status VALUES ('62', '62', '5', '1', '2025-05-06 10:24:13');
INSERT INTO message_read_status VALUES ('63', '63', '5', '1', '2025-05-07 11:40:44');
INSERT INTO message_read_status VALUES ('64', '64', '5', '1', '2025-05-07 12:08:50');
INSERT INTO message_read_status VALUES ('65', '65', '8', '1', '2025-05-07 12:09:36');
INSERT INTO message_read_status VALUES ('66', '66', '8', '1', '2025-05-07 15:22:57');
INSERT INTO message_read_status VALUES ('67', '67', '5', '1', '2025-05-07 15:27:26');
INSERT INTO message_read_status VALUES ('68', '68', '5', '1', '2025-05-07 16:39:18');
INSERT INTO message_read_status VALUES ('69', '69', '8', '1', '2025-05-07 16:43:25');
INSERT INTO message_read_status VALUES ('70', '70', '8', '1', '2025-05-07 17:28:30');
INSERT INTO message_read_status VALUES ('71', '71', '5', '1', '2025-05-07 17:27:57');
INSERT INTO message_read_status VALUES ('72', '72', '5', '1', '2025-05-07 17:28:12');
INSERT INTO message_read_status VALUES ('73', '73', '5', '1', '2025-05-07 17:28:30');
INSERT INTO message_read_status VALUES ('74', '74', '5', '1', '2025-05-07 17:29:05');
INSERT INTO message_read_status VALUES ('75', '75', '5', '1', '2025-05-07 18:30:03');
INSERT INTO message_read_status VALUES ('76', '76', '5', '1', '2025-05-07 18:30:03');
INSERT INTO message_read_status VALUES ('77', '77', '8', '1', '2025-05-07 18:30:45');
INSERT INTO message_read_status VALUES ('78', '78', '8', '1', '2025-05-07 18:30:55');
INSERT INTO message_read_status VALUES ('79', '79', '5', '1', '2025-05-07 18:31:14');
INSERT INTO message_read_status VALUES ('80', '80', '8', '1', '2025-05-07 18:31:35');
INSERT INTO message_read_status VALUES ('81', '81', '5', '1', '2025-05-07 18:31:48');
INSERT INTO message_read_status VALUES ('82', '82', '5', '0', null);
INSERT INTO message_read_status VALUES ('83', '83', '8', '1', '2025-05-07 19:25:20');
INSERT INTO message_read_status VALUES ('84', '84', '8', '1', '2025-05-07 19:25:20');
INSERT INTO message_read_status VALUES ('85', '85', '8', '1', '2025-05-07 18:34:29');
INSERT INTO message_read_status VALUES ('86', '86', '8', '1', '2025-05-07 18:34:32');
INSERT INTO message_read_status VALUES ('87', '87', '8', '1', '2025-05-07 19:25:20');
INSERT INTO message_read_status VALUES ('88', '88', '5', '0', null);
INSERT INTO message_read_status VALUES ('89', '89', '5', '1', '2025-05-07 18:35:00');
INSERT INTO message_read_status VALUES ('90', '90', '5', '1', '2025-05-07 18:35:03');
INSERT INTO message_read_status VALUES ('91', '91', '5', '1', '2025-05-07 18:35:06');
INSERT INTO message_read_status VALUES ('92', '92', '5', '1', '2025-05-07 18:35:08');
INSERT INTO message_read_status VALUES ('93', '93', '8', '1', '2025-05-07 18:35:14');
INSERT INTO message_read_status VALUES ('94', '94', '8', '1', '2025-05-07 18:35:18');

-- ----------------------------
-- Table structure for `statistics_history`15
-- ----------------------------
DROP TABLE IF EXISTS `statistics_history`;
CREATE TABLE `statistics_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '统计类型：DAU, WAU, MAU, TOTAL_ITEMS, etc.',
  `stat_value` int(11) NOT NULL COMMENT '统计值',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_type` (`stat_date`,`stat_type`) COMMENT '确保每天每种类型只有一条记录'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统计数据历史表';

-- ----------------------------
-- Records of statistics_history
-- ----------------------------

-- ----------------------------
-- Table structure for `system_announcements`16
-- ----------------------------
DROP TABLE IF EXISTS `system_announcements`;
CREATE TABLE `system_announcements` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告标题',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告内容',
  `importance` enum('NORMAL','IMPORTANT','URGENT') COLLATE utf8mb4_unicode_ci DEFAULT 'NORMAL' COMMENT '重要程度',
  `start_time` datetime NOT NULL COMMENT '生效时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `created_by` bigint(20) NOT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` enum('DRAFT','PUBLISHED','EXPIRED') COLLATE utf8mb4_unicode_ci DEFAULT 'DRAFT' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `idx_status_time` (`status`,`start_time`,`end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of system_announcements
-- ----------------------------
INSERT INTO system_announcements VALUES ('1', '系统维护通知', '尊敬的用户，我们将于2023年12月15日凌晨2:00-4:00进行系统维护，期间系统将暂停服务。给您带来的不便，敬请谅解。', 'URGENT', '2023-12-09 08:00:00', '2026-12-24 08:00:00', '1', '2025-05-04 00:34:43', '2025-05-06 20:29:03', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('2', '失物招领平台使用指南', '为了提高失物招领效率，我们更新了平台使用指南。请所有用户仔细阅读，了解如何正确发布失物和招领信息，以及如何使用智能匹配功能。', 'IMPORTANT', '2023-11-01 00:00:00', null, '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('3', '校园失物招领活动', '我们将于本月20日在学生中心举办校园失物招领专场活动，欢迎所有同学参加。活动现场将展示近期收集的失物，并提供现场认领服务。', 'NORMAL', '2023-11-15 00:00:00', '2023-12-20 00:00:00', '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('4', '系统升级完成通知', '系统升级已于昨日完成，新版本增加了多项功能，包括智能匹配算法优化、界面美化等。感谢您的支持与理解。', 'NORMAL', '2023-10-01 00:00:00', '2023-10-31 00:00:00', '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('5', '新功能预告', '我们即将推出全新的移动端应用，敬请期待！', 'IMPORTANT', '2024-01-01 00:00:00', null, '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'DRAFT');
INSERT INTO system_announcements VALUES ('6', '2024年春季学期失物招领须知', '新学期开始，请大家注意保管好自己的物品。如有丢失，请及时在平台发布信息。', 'NORMAL', '2024-02-01 00:00:00', null, '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('7', '关于加强校园失物招领管理的通知', '为进一步规范校园失物招领管理，提高服务质量，特制定以下规定：\n\n\n     1. 所有拾获的物品应在24小时内上交至失物招领中心或在平台发布信息；\n\n     2. 贵重物品（如手机、电脑、钱包等）必须由管理员验证后才能发布；\n\n     3. 认领物品时，认领人需提供有效证件和物品特征描述；\n\n     4. 发布虚假信息者将被平台禁言处理；\n\n     5. 平台保留对违规行为进行处理的权利。\n\n\n     请所有用户遵守以上规定，共同维护良好的校园环境。', 'IMPORTANT', '2023-11-10 00:00:00', null, '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');
INSERT INTO system_announcements VALUES ('8', '重要安全提醒', '近期校园内出现多起财物丢失事件，请同学们妥善保管个人物品，特别是在图书馆、食堂等公共场所。', 'URGENT', '2025-05-04 00:00:00', '2025-05-11 00:00:00', '1', '2025-05-04 00:34:43', '2025-05-04 00:34:43', 'PUBLISHED');

-- ----------------------------
-- Table structure for `system_configs`17
-- ----------------------------
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text COLLATE utf8mb4_unicode_ci COMMENT '配置值',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ----------------------------
-- Records of system_configs
-- ----------------------------
INSERT INTO system_configs VALUES ('1', 'match.notification.similarity-threshold', '0.7', '触发匹配通知的相似度阈值', '2025-05-03 17:25:49', '2025-05-03 17:25:49');
INSERT INTO system_configs VALUES ('2', 'match.notification.auto-notify', 'true', '是否启用自动匹配通知功能', '2025-05-03 17:25:49', '2025-05-03 17:25:49');

-- ----------------------------
-- Table structure for `users`18
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码（加密）',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `role` enum('USER','ADMIN','SUPER_ADMIN') COLLATE utf8mb4_unicode_ci DEFAULT 'USER',
  `create_time` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '是否逻辑删除',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户头像URL',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_email` (`email`),
  UNIQUE KEY `unique_phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO users VALUES ('5', 'testuser1', '$2a$10$.6rE8HL3kRnp/E8KTdzrFOnvZ2j/Oa8Nt4G6qzzIRWrZdKxkmBVcC', '<EMAIL>', null, 'SUPER_ADMIN', '2025-04-02 00:00:49', '1', '0', '2025-04-17 21:07:36', null);
INSERT INTO users VALUES ('7', '王小明', '$2a$10$mmeDopvidBDsDVdGCcTFYOYkMnCsT.nvlWpdTKJrmY17zG3QiVdtS', '<EMAIL>', null, 'USER', '2025-04-03 16:47:14', '1', '0', '2025-04-17 16:07:36', null);
INSERT INTO users VALUES ('8', 'wangyang', '$2a$10$pEhmQJdcvSgXTXjIxMte5uM7dZMeeDUBnTAMuM3LymO/V7gehSJ0u', null, '18929741873', 'USER', '2025-04-03 17:27:28', '1', '0', '2025-04-17 21:06:45', null);
INSERT INTO users VALUES ('9', 'admin1', '$2a$10$nDmB0kD60DLfcd5attSlBOepRuGqg3KHoAUeMbdNPjnrHwHlNvG.q', null, null, 'ADMIN', null, '1', '0', '2025-04-17 16:07:36', null);

-- ----------------------------
-- Table structure for `user_announcement_reads`19
-- ----------------------------
DROP TABLE IF EXISTS `user_announcement_reads`;
CREATE TABLE `user_announcement_reads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `announcement_id` bigint(20) NOT NULL COMMENT '公告ID',
  `read_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_announcement` (`user_id`,`announcement_id`),
  KEY `idx_announcement_id` (`announcement_id`),
  CONSTRAINT `fk_user_announcement_reads_announcement` FOREIGN KEY (`announcement_id`) REFERENCES `system_announcements` (`id`),
  CONSTRAINT `fk_user_announcement_reads_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of user_announcement_reads
-- ----------------------------
INSERT INTO user_announcement_reads VALUES ('4', '5', '8', '2025-05-06 11:17:22');
INSERT INTO user_announcement_reads VALUES ('5', '5', '2', '2025-05-06 11:17:22');
INSERT INTO user_announcement_reads VALUES ('6', '5', '7', '2025-05-06 11:17:22');
INSERT INTO user_announcement_reads VALUES ('7', '5', '6', '2025-05-06 11:17:22');

-- ----------------------------
-- Table structure for `user_notifications`20
-- ----------------------------
DROP TABLE IF EXISTS `user_notifications`;
CREATE TABLE `user_notifications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('UNREAD','READ') COLLATE utf8mb4_unicode_ci DEFAULT 'UNREAD',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of user_notifications
-- ----------------------------
INSERT INTO user_notifications VALUES ('13', '8', '您的发布已通过审核', '您的失物信息（ID: 27）已经审核通过，并成功发布。', 'READ', '2025-04-22 15:15:57', '2025-04-22 15:16:25');
INSERT INTO user_notifications VALUES ('14', '8', '您的发布未通过审核', '您的失物信息（ID: 24）:内容涉嫌违规未通过审核，发布失败。', 'READ', '2025-04-22 15:30:37', '2025-04-22 15:30:59');
INSERT INTO user_notifications VALUES ('15', '8', '您的发布已通过审核', '您的失物信息（ID: 22）已经审核通过，并成功发布。', 'READ', '2025-04-25 00:30:30', '2025-04-25 15:45:51');
INSERT INTO user_notifications VALUES ('20', '8', '1111', '11111', 'READ', '2025-04-25 15:41:24', '2025-04-25 15:45:51');
INSERT INTO user_notifications VALUES ('21', '8', '2222', '11111', 'READ', '2025-04-25 15:44:04', '2025-04-25 15:45:51');
INSERT INTO user_notifications VALUES ('30', '8', '123456', '12345', 'READ', '2025-04-25 15:54:00', '2025-04-27 09:10:41');
INSERT INTO user_notifications VALUES ('31', '8', '1234', '2345', 'READ', '2025-04-25 15:55:21', '2025-04-27 09:10:41');
INSERT INTO user_notifications VALUES ('36', '8', '·12345', '1234567', 'READ', '2025-04-25 16:04:42', '2025-04-27 09:10:41');
INSERT INTO user_notifications VALUES ('42', '5', '234567', '234567', 'READ', '2025-04-25 17:39:04', '2025-04-25 17:39:10');
INSERT INTO user_notifications VALUES ('43', '5', '1234', '123456', 'READ', '2025-04-25 17:42:13', '2025-04-25 17:42:20');
INSERT INTO user_notifications VALUES ('44', '5', '12345', '23456', 'READ', '2025-04-25 19:43:19', '2025-05-06 11:17:22');
