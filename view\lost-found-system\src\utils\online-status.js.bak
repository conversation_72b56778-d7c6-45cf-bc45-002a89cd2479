/**
 * 在线状态管理模块 (优化版)
 *
 * 该模块负责：
 * 1. 全局在线用户状态的监控和管理
 * 2. 在线用户数量的统计和更新
 * 3. 在线状态变化的通知
 *
 * 与其他模块的关系：
 * - websocket.js: 提供基础的WebSocket连接和消息处理功能
 * - contact-status.js: 使用本模块的在线状态信息，专注于联系人状态管理
 *
 * 使用方式：
 * import { initOnlineStatusMonitor, getOnlineUserCount, isUserOnline } from '@/utils/online-status'
 *
 * 重构说明：
 * 使用Pinia Store集中管理状态，避免状态管理混乱和循环依赖
 */

import { getWebSocketClient, initWebSocketClient } from './websocket'
import { isConnected as checkConnectionStatus, addConnectionListener } from './connection-status'
import { useOnlineStatusStore } from '../stores'

// 订阅状态
let globalSubscription = null
let initialized = false

/**
 * 初始化在线状态监控
 * @param {string} token 用户令牌
 */
export function initOnlineStatusMonitor(token) {
  if (!token) {
    console.warn('未找到token，无法初始化在线状态监控')
    return
  }

  if (initialized) {
    console.log('在线状态监控已初始化')
    // 即使已初始化，也请求最新的在线用户列表
    setTimeout(() => {
      try {
        requestOnlineUsers()
      } catch (e) {
        console.warn('请求在线用户列表失败:', e)
      }
    }, 2000)
    return
  }

  console.log('初始化在线状态监控...')

  // 延迟获取WebSocket客户端，确保WebSocket已初始化
  setTimeout(() => {
    try {
      // 获取WebSocket客户端
      const stompClient = getWebSocketClient()

      if (!stompClient) {
        console.warn('WebSocket客户端不存在，尝试初始化WebSocket客户端')
        // 尝试初始化WebSocket客户端
        initWebSocketClient()

        // 再次尝试获取WebSocket客户端
        const retryStompClient = getWebSocketClient()
        if (!retryStompClient) {
          console.error('初始化后WebSocket客户端仍不存在，无法初始化在线状态监控')
          // 监听WebSocket连接成功事件，连接成功后再初始化
          window.addEventListener('websocket-connected', () => {
            initOnlineStatusMonitor(token)
          }, { once: true })
          return
        }
      }

      if (!stompClient.connected) {
        console.warn('WebSocket未连接，无法初始化在线状态监控')
        // 监听WebSocket连接成功事件，连接成功后再初始化
        window.addEventListener('websocket-connected', () => {
          initOnlineStatusMonitor(token)
        }, { once: true })
        return
      }

      // 继续初始化过程...
      subscribeOnlineUserCount()
      subscribeOnlineUsers()
      requestOnlineUsers()

      // 监听来自websocket.js的用户状态更新事件
      window.addEventListener('user-online-status', (event) => {
        const { userId, isOnline } = event.detail
        console.log(`收到来自websocket.js的状态更新: 用户 ${userId} 状态为 ${isOnline ? '在线' : '离线'}`)

        // 获取Pinia Store
        const store = useOnlineStatusStore()

        // 更新Store中的用户状态
        store.updateUserStatus(userId, isOnline)

        // 触发在线用户列表更新事件
        window.dispatchEvent(new CustomEvent('online-users-updated', {
          detail: {
            users: store.onlineUsersList,
            count: store.onlineUserCount,
            timestamp: new Date().getTime()
          }
        }))
      })

      // 使用统一的连接状态管理
      if (checkConnectionStatus()) {
        console.log('在线状态监控: WebSocket已连接')
      } else {
        console.warn('在线状态监控: WebSocket未连接')
      }

      // 添加连接状态监听器
      addConnectionListener(() => {
        if (checkConnectionStatus()) {
          // 连接成功，尝试订阅
          subscribeOnlineUserCount()
          subscribeOnlineUsers()
          requestOnlineUsers()
        }
      })

      // 标记为已初始化
      initialized = true

      // 触发初始化完成事件
      window.dispatchEvent(new Event('online-status-initialized'))
    } catch (e) {
      console.error('初始化在线状态监控时出错:', e)
    }
  }, 2000) // 延迟2秒，确保WebSocket已初始化
}

/**
 * 订阅在线用户数量
 */
function subscribeOnlineUserCount() {
  const stompClient = getWebSocketClient()
  if (!stompClient || !stompClient.connected) {
    console.warn('WebSocket未连接，无法订阅在线用户数量')
    return false
  }

  try {
    console.log('正在订阅在线用户数量主题...')

    // 如果已有订阅，先取消
    if (window._onlineCountSubscription) {
      try {
        window._onlineCountSubscription.unsubscribe()
        console.log('已取消旧的在线用户数量订阅')
      } catch (e) {
        console.warn('取消旧订阅时出错:', e)
      }
    }

    // 订阅在线用户数量主题
    window._onlineCountSubscription = stompClient.subscribe('/topic/onlineCount', (message) => {
      try {
        console.log('收到在线用户数量消息:', message.body)
        const countData = JSON.parse(message.body)

        // 获取Pinia Store
        const store = useOnlineStatusStore()

        // 更新在线用户数量 - 使用Store中的方法
        const count = countData.onlineCount || 0

        // 创建一个临时用户列表，包含count个空用户
        const tempUsers = Array.from({ length: count }, (_, i) => `temp-${i}`)
        store.updateOnlineUsers(tempUsers)

        console.log('更新在线用户数量:', store.onlineUserCount)

        // 触发在线用户数量更新事件
        const event = new CustomEvent('online-user-count', {
          detail: {
            count: store.onlineUserCount,
            timestamp: countData.timestamp || new Date().getTime()
          }
        })
        console.log('触发在线用户数量更新事件:', event.detail)
        window.dispatchEvent(event)

        // 手动更新 OnlineUserCount 组件
        if (window._updateOnlineUserCount) {
          window._updateOnlineUserCount(store.onlineUserCount)
        }
      } catch (error) {
        console.error('处理在线用户数量消息时出错:', error)
      }
    })

    console.log('在线用户数量订阅成功，订阅ID:', window._onlineCountSubscription.id)

    // 主动请求一次在线用户数量
    setTimeout(() => {
      try {
        stompClient.publish({
          destination: '/app/getAllOnlineUsers',
          body: JSON.stringify({
            timestamp: new Date().getTime()
          })
        })
        console.log('已主动请求在线用户数量')
      } catch (e) {
        console.warn('主动请求在线用户数量失败:', e)
      }
    }, 1000)

    return true
  } catch (error) {
    console.error('订阅在线用户数量失败:', error)
    return false
  }
}

/**
 * 订阅在线用户列表
 */
function subscribeOnlineUsers() {
  const stompClient = getWebSocketClient()
  if (!stompClient || !stompClient.connected) {
    console.warn('WebSocket未连接，无法订阅在线用户列表')
    return false
  }

  try {
    console.log('正在订阅在线用户列表主题...')

    // 如果已有订阅，先取消
    if (globalSubscription) {
      try {
        globalSubscription.unsubscribe()
        console.log('已取消旧的在线用户列表订阅')
      } catch (e) {
        console.warn('取消旧订阅时出错:', e)
      }
    }

    // 订阅在线用户列表主题
    globalSubscription = stompClient.subscribe('/topic/onlineUsers', (message) => {
      try {
        console.log('收到在线用户列表消息:', message.body.substring(0, 100) + '...')
        handleOnlineUsersMessage(message.body)
      } catch (error) {
        console.error('处理在线用户列表消息时出错:', error)
      }
    })

    console.log('在线用户列表订阅成功，订阅ID:', globalSubscription.id)
    return true
  } catch (error) {
    console.error('订阅在线用户列表失败:', error)
    return false
  }
}

/**
 * 请求获取在线用户列表
 */
export function requestOnlineUsers() {
  const stompClient = getWebSocketClient()
  if (!stompClient || !stompClient.connected) {
    console.warn('WebSocket未连接，无法请求在线用户列表')
    return false
  }

  try {
    console.log('正在请求在线用户列表...')

    // 确保已订阅相应的主题
    let userQueueSubscription = null
    try {
      // 检查是否已有订阅
      if (window._userQueueSubscription) {
        console.log('已存在用户队列订阅，使用现有订阅')
      } else {
        // 检查是否已订阅 /user/queue/onlineUsers
        userQueueSubscription = stompClient.subscribe('/user/queue/onlineUsers', (message) => {
          console.log('收到用户队列在线用户列表消息:', message.body)
          handleOnlineUsersMessage(message.body)
        })
        window._userQueueSubscription = userQueueSubscription
        console.log('成功订阅 /user/queue/onlineUsers, 订阅ID:', userQueueSubscription.id)
      }
    } catch (e) {
      console.warn('订阅 /user/queue/onlineUsers 失败:', e)
    }

    // 发送请求
    stompClient.publish({
      destination: '/app/getAllOnlineUsers',
      body: JSON.stringify({
        timestamp: new Date().getTime()
      }),
      headers: {
        // 添加额外的头信息，确保请求能被正确处理
        'content-type': 'application/json'
      }
    })
    console.log('在线用户列表请求已发送')

    // 确保全局主题已订阅
    try {
      if (!window._onlineCountSubscription) {
        subscribeOnlineUserCount()
      }

      if (!globalSubscription) {
        subscribeOnlineUsers()
      }
    } catch (e) {
      console.warn('确保全局主题订阅失败:', e)
    }

    return true
  } catch (error) {
    console.error('请求在线用户列表失败:', error)
    return false
  }
}

/**
 * 处理在线用户列表消息
 * @param {string} message 消息内容
 */
function handleOnlineUsersMessage(message) {
  try {
    // 获取Pinia Store
    const store = useOnlineStatusStore()

    // 尝试解析为JSON
    const usersData = JSON.parse(message)

    // 准备用户列表
    let usersList = []

    // 解析用户列表
    if (usersData.users && Array.isArray(usersData.users)) {
      // 用户对象数组格式
      usersList = usersData.users.map(user => user.userId)
    } else if (usersData.onlineUsers) {
      // 用户ID -> 在线状态映射格式
      usersList = Object.keys(usersData.onlineUsers).filter(userId =>
        usersData.onlineUsers[userId]
      )
    }

    // 更新Store中的在线用户列表
    const { newUsers, offlineUsers } = store.updateOnlineUsers(usersList)

    // 更新最后一次在线用户列表更新时间
    localStorage.setItem('lastOnlineUsersUpdate', new Date().getTime().toString())

    // 如果有新用户上线，播放联系人上线音效
    if (newUsers.length > 0) {
      try {
        // 获取WebSocket客户端
        const wsClient = getWebSocketClient();
        // 检查wsClient是否存在且有playNotificationSound方法
        if (wsClient && typeof wsClient.playNotificationSound === 'function') {
          // 播放联系人上线音效
          wsClient.playNotificationSound('contact');
        } else {
          console.log('联系人上线，但无法播放音效（方法不可用）');
        }
      } catch (error) {
        console.warn('尝试播放联系人上线音效时出错:', error);
      }
    }

    // 触发在线用户列表更新事件
    window.dispatchEvent(new CustomEvent('online-users-updated', {
      detail: {
        users: store.onlineUsersList,
        count: store.onlineUserCount,
        newUsers,
        offlineUsers,
        timestamp: new Date().getTime()
      }
    }))
  } catch (error) {
    console.error('处理在线用户列表消息时出错:', error)
  }
}

/**
 * 获取在线用户数量
 * @returns {number} 在线用户数量
 */
export function getOnlineUserCount() {
  const store = useOnlineStatusStore()
  return store.onlineUserCount
}

/**
 * 获取在线用户列表
 * @returns {string[]} 在线用户ID数组
 */
export function getOnlineUsers() {
  const store = useOnlineStatusStore()
  return store.onlineUsersList
}

/**
 * 检查用户是否在线
 * @param {string} userId 用户ID
 * @returns {boolean} 是否在线
 */
export function isUserOnline(userId) {
  const store = useOnlineStatusStore()
  return store.isUserOnline(userId)
}

/**
 * 获取用户最后活跃时间
 * @param {string} userId 用户ID
 * @returns {number|null} 最后活跃时间
 */
export function getUserLastActiveTime(userId) {
  const store = useOnlineStatusStore()
  return store.getUserLastActiveTime(userId)
}

/**
 * 获取WebSocket连接状态
 * @returns {boolean} 是否已连接
 */
export function isConnected() {
  const store = useOnlineStatusStore()
  return store.isConnected
}

// 导出默认对象
export default {
  initOnlineStatusMonitor,
  getOnlineUserCount,
  getOnlineUsers,
  isUserOnline,
  getUserLastActiveTime,
  isConnected,
  requestOnlineUsers
}
