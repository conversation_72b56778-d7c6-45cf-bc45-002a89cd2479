<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="26.2.14">
  <diagram id="login-flow-diagram" name="用户登录流程图">
    <mxGraphModel dx="946" dy="563" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="start" value="开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="360" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="input_login_info" value="用户输入登录信息" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="360" y="140" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="validate_params" value="参数校验" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="360" y="240" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="is_valid_params" value="参数是否有效？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="360" y="340" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="login_type" value="登录方式？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="360" y="460" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="code_login" value="验证码登录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="200" y="560" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="password_login" value="密码登录" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="520" y="560" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="find_user_by_phone_email" value="根据手机号/邮箱查找用户" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="200" y="660" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="find_user_by_username" value="根据用户名查找用户" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="520" y="660" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="user_exists_code" value="用户是否存在？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="200" y="760" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="user_exists_password" value="用户是否存在且未删除？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="520" y="760" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="user_active" value="账号是否激活？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="520" y="880" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="password_correct" value="密码是否正确？" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="520" y="1000" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="generate_token" value="生成JWT令牌" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="360" y="1120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="store_token" value="存储令牌到Redis" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="360" y="1220" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="return_success" value="返回登录成功和令牌" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1">
          <mxGeometry x="360" y="1320" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="return_fail" value="返回登录失败" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" vertex="1">
          <mxGeometry x="40" y="880" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end" value="结束" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="360" y="1420" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="start_to_input" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="start" target="input_login_info" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="input_to_validate" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="input_login_info" target="validate_params" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="validate_to_is_valid" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="validate_params" target="is_valid_params" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="is_valid_to_login_type" value="是" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="is_valid_params" target="login_type" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="is_valid_to_return_fail" value="否" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="is_valid_params" target="return_fail" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="100" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="login_type_to_code" value="验证码" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="login_type" target="code_login" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="260" y="500" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="login_type_to_password" value="密码" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="login_type" target="password_login" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="580" y="500" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="code_to_find_user" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="code_login" target="find_user_by_phone_email" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="password_to_find_user" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="password_login" target="find_user_by_username" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="find_user_to_exists_code" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="find_user_by_phone_email" target="user_exists_code" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="find_user_to_exists_password" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="find_user_by_username" target="user_exists_password" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="exists_code_to_generate" value="是" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="user_exists_code" target="generate_token" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="260" y="1150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="exists_code_to_fail" value="否" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="user_exists_code" target="return_fail" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="180" y="800" />
              <mxPoint x="180" y="910" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="exists_password_to_active" value="是" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="user_exists_password" target="user_active" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="exists_password_to_fail" value="否" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="user_exists_password" target="return_fail" edge="1">
          <mxGeometry x="-0.2766" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="180" y="800" />
              <mxPoint x="180" y="910" />
            </Array>
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="active_to_password_correct" value="是" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="user_active" target="password_correct" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="active_to_fail" value="否" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="user_active" target="return_fail" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="180" y="920" />
              <mxPoint x="180" y="910" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="password_correct_to_generate" value="是" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="password_correct" target="generate_token" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="580" y="1150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="password_correct_to_fail" value="否" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="password_correct" target="return_fail" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="180" y="1040" />
              <mxPoint x="180" y="910" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="generate_to_store" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="generate_token" target="store_token" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="store_to_return" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="store_token" target="return_success" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="return_success_to_end" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="return_success" target="end" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="return_fail_to_end" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="return_fail" target="end" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="100" y="1450" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
