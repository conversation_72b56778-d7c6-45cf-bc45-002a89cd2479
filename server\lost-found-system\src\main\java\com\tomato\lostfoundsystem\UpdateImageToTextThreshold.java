package com.tomato.lostfoundsystem;

import com.tomato.lostfoundsystem.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * 更新图像到文本匹配的过滤阈值
 * 这个类在应用启动时运行，将图像到文本匹配的过滤阈值从0.45降低到0.3
 */
@Slf4j
@Component
public class UpdateImageToTextThreshold implements CommandLineRunner {

    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public void run(String... args) throws Exception {
        // 配置键
        String configKey = "match.filter.image-to-text-threshold";
        
        // 新的阈值
        String newValue = "0.3";
        
        // 获取当前值
        String currentValue = systemConfigService.getConfigValue(configKey, "0.45");
        
        log.info("当前图像到文本匹配的过滤阈值: {}", currentValue);
        
        // 更新阈值
        systemConfigService.updateConfigValue(configKey, newValue);
        
        // 验证更新是否成功
        String updatedValue = systemConfigService.getConfigValue(configKey, "0.45");
        
        log.info("已将图像到文本匹配的过滤阈值从 {} 更新为 {}", currentValue, updatedValue);
    }
}
