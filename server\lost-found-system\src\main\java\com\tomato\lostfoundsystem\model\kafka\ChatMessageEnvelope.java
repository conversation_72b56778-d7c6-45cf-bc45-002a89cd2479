package com.tomato.lostfoundsystem.model.kafka;

import com.tomato.lostfoundsystem.dto.MessageDTO;
import lombok.Data;

import java.util.UUID;

/**
 * 聊天消息信封，用于包装消息并添加元数据
 * 用于在 Kafka 中传递消息
 */
@Data
public class ChatMessageEnvelope {
    // 消息ID，用于幂等性处理
    private String messageId;
    
    // 消息版本号
    private String version;
    
    // 消息类型
    private String messageType;
    
    // 消息状态
    private MessageStatus status;
    
    // 消息创建时间
    private long createdAt;
    
    // 消息重试次数
    private int retryCount;
    
    // 消息内容
    private MessageDTO payload;
    
    // 消息状态枚举
    public enum MessageStatus {
        CREATED,    // 消息创建
        QUEUED,     // 消息已入队
        PROCESSING, // 消息处理中
        DELIVERED,  // 消息已送达
        FAILED,     // 消息处理失败
        RETRYING    // 消息重试中
    }
    
    /**
     * 创建一个新的消息信封
     * @param payload 消息内容
     * @return 消息信封
     */
    public static ChatMessageEnvelope create(MessageDTO payload) {
        ChatMessageEnvelope envelope = new ChatMessageEnvelope();
        envelope.setMessageId(UUID.randomUUID().toString());
        envelope.setVersion("1.0");
        envelope.setMessageType("CHAT");
        envelope.setStatus(MessageStatus.CREATED);
        envelope.setCreatedAt(System.currentTimeMillis());
        envelope.setRetryCount(0);
        envelope.setPayload(payload);
        return envelope;
    }
    
    /**
     * 创建一个新的消息信封，使用指定的消息ID
     * @param payload 消息内容
     * @param messageId 消息ID
     * @return 消息信封
     */
    public static ChatMessageEnvelope create(MessageDTO payload, String messageId) {
        ChatMessageEnvelope envelope = new ChatMessageEnvelope();
        envelope.setMessageId(messageId);
        envelope.setVersion("1.0");
        envelope.setMessageType("CHAT");
        envelope.setStatus(MessageStatus.CREATED);
        envelope.setCreatedAt(System.currentTimeMillis());
        envelope.setRetryCount(0);
        envelope.setPayload(payload);
        return envelope;
    }
}
