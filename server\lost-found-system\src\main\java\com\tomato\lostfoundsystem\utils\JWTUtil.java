package com.tomato.lostfoundsystem.utils;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Component
public class JWTUtil {

    private static final Logger logger = LoggerFactory.getLogger(JWTUtil.class);

    @Value("${jwt.secret-key}")
    private String secretKey;

    @Value("${jwt.expiration-time}")
    private long expirationTime;

    /**
     * 获取签名密钥
     * @return SecretKey 签名密钥
     */
    private  SecretKey getSigningKey() {
        if (secretKey == null || secretKey.isEmpty()) {
            logger.warn("JWT密钥未配置，使用默认生成的密钥");
            return Keys.secretKeyFor(SignatureAlgorithm.HS256); // 使用默认生成的密钥
        } else {
            return new SecretKeySpec(secretKey.getBytes(), SignatureAlgorithm.HS256.getJcaName()); // 从配置中获取密钥
        }
    }

    /**
     * 创建JWT令牌
     * @param userId 用户ID
     * @param username 用户名
     * @param roles 用户角色
     * @return String JWT令牌
     */
    public String createToken(long userId, String username, String roles) {
        logger.debug("开始创建JWT令牌，用户ID: {}, 用户名: {}", userId, username);
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("roles", roles);        // 用户角色信息
        return createToken(claims); // 使用自定义的claims生成token
    }

    /**
     * 创建包含头像URL的JWT令牌
     * @param userId 用户ID
     * @param username 用户名
     * @param roles 用户角色
     * @param avatar 用户头像URL
     * @return String JWT令牌
     */
    public String createToken(long userId, String username, String roles, String avatar) {
        logger.debug("开始创建JWT令牌(含头像)，用户ID: {}, 用户名: {}", userId, username);
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("username", username);
        claims.put("roles", roles);        // 用户角色信息
        claims.put("avatar", avatar);      // 用户头像URL
        return createToken(claims); // 使用自定义的claims生成token
    }

    /**
     * 使用Claims创建JWT令牌
     * @param claims 令牌中的声明信息
     * @return String JWT令牌
     */
    private String createToken(Map<String, Object> claims) {
        long now = System.currentTimeMillis();
        Date issuedAt = new Date(now);
        Date expiration = new Date(now + expirationTime);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject((String) claims.get("username"))  // 将用户名设置为 subject
                .setIssuedAt(issuedAt)
                .setExpiration(expiration)
                .signWith(getSigningKey())
                .compact();
    }

    /**
     * 解析JWT令牌
     * @param token JWT令牌
     * @return Claims 令牌中的声明信息
     */
    public Claims parseToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (JwtException e) {
            logger.error("解析JWT令牌失败: {}", e.getMessage());
            return null;  // 返回null表示解析失败
        }
    }

    /**
     * 从令牌中获取用户ID
     * @param token JWT令牌
     * @return Long 用户ID
     */
    public Long getUserIdFromToken(String token) {
        return getClaimFromToken(token, "userId", Long.class);
    }

    /**
     * 从令牌中获取用户名
     * @param token JWT令牌
     * @return String 用户名
     */
    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, "username", String.class);
    }

    /**
     * 从令牌中获取用户角色
     * @param token JWT令牌
     * @return String 用户角色
     */
    public String getRolesFromToken(String token) {
        return getClaimFromToken(token, "roles", String.class);
    }

    /**
     * 从令牌中获取指定的声明信息
     * @param token JWT令牌
     * @param claimKey 声明的键
     * @param claimType 声明的类型
     * @param <T> 声明的返回类型
     * @return T 声明信息
     */
    private <T> T getClaimFromToken(String token, String claimKey, Class<T> claimType) {
        Claims claims = parseToken(token);
        if (claims != null) {
            return claims.get(claimKey, claimType);
        }
        logger.warn("无法从令牌中获取{}信息", claimKey);
        return null;
    }

    /**
     * 验证令牌是否有效
     * @param token JWT令牌
     * @return boolean 是否有效
     */
    public boolean isTokenValid(String token) {
        Claims claims = parseToken(token);
        return claims != null && !isTokenExpired(token);
    }

    /**
     * 检查令牌是否过期
     * @param token JWT令牌
     * @return boolean 是否过期
     */
    public boolean isTokenExpired(String token) {
        Date expiration = getExpirationDateFromToken(token);
        return expiration != null && expiration.before(new Date());
    }

    /**
     * 获取令牌的过期时间
     * @param token JWT令牌
     * @return Date 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        Claims claims = parseToken(token);
        if (claims != null) {
            return claims.getExpiration();
        }
        logger.warn("无法获取令牌过期时间");
        return null;
    }

    /**
     * 获取令牌的剩余有效时间（毫秒）
     * @param token JWT令牌
     * @return long 剩余有效时间
     */
    public long getRemainingTime(String token) {
        Date expiration = getExpirationDateFromToken(token);
        return expiration != null ? expiration.getTime() - System.currentTimeMillis() : 0;
    }

    /**
     * 检查令牌是否可以刷新
     * @param token JWT令牌
     * @return boolean 是否可以刷新
     */
    public boolean canTokenBeRefreshed(String token) {
        return !isTokenExpired(token) && getRemainingTime(token) > 0;
    }

    /**
     * 刷新令牌
     * @param token JWT令牌
     * @return String 新的JWT令牌
     */
    public String refreshToken(String token) {
        Claims claims = parseToken(token);
        if (claims != null) {
            claims.setIssuedAt(new Date());
            claims.setExpiration(new Date(System.currentTimeMillis() + expirationTime));
            return createToken(claims);  // 生成新的令牌
        }
        logger.warn("令牌刷新失败：无法解析原令牌");
        return null;
    }

    /**
     * 从请求中获取用户ID
     * @param request HTTP请求
     * @return Long 用户ID
     */
    public static Long getUserId(HttpServletRequest request) {
        // 从请求属性中获取用户ID
        Object userIdObj = request.getAttribute("userId");
        if (userIdObj instanceof Long) {
            return (Long) userIdObj;
        }

        // 如果请求属性中没有用户ID，则尝试从Authorization头中获取
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            // 由于这是静态方法，无法直接使用实例方法解析token
            // 在实际应用中，建议在Controller中使用注入的JWTUtil实例，而不是静态方法
            return null;
        }

        return null;
    }

    /**
     * 从请求中获取用户角色
     * @param request HTTP请求
     * @return String 用户角色
     */
    public static String getUserRole(HttpServletRequest request) {
        // 从请求属性中获取用户角色
        Object roleObj = request.getAttribute("userRole");
        if (roleObj instanceof String) {
            return (String) roleObj;
        }

        // 如果请求属性中没有用户角色，则返回null
        return null;
    }
}
