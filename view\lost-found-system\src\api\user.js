import request from '../utils/request'

// 获取图形验证码
export function getCaptcha() {
  return request({
    url: '/user/generateCaptcha',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json, text/plain, */*'
    },
    timeout: 10000
  })
}

// 发送邮箱验证码
export function sendEmailCode(data) {
  return request({
    url: '/verify/sendEmailCode',
    method: 'post',
    data
  })
}

// 发送手机验证码
export function sendPhoneCode(data) {
  return request({
    url: '/verify/sendPhoneCode',
    method: 'post',
    data
  })
}

// 用户登录
export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

// 用户注册
export function register(data) {
  return request({
    url: '/user/register',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/user/profile',
    method: 'get'
  })
}

// 更新用户信息
export function updateProfile(data) {
  return request({
    url: '/user/update-profile',
    method: 'put',
    data
  })
}

// 退出登录
export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}

// 注销账号
export function deactivateAccount(data) {
  return request({
    url: '/user/deactivate',
    method: 'post',
    data
  })
}

// 上传头像
export function uploadAvatar(file) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/user/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}