package com.tomato.lostfoundsystem.interceptors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.utils.CustomHttpServletRequestWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.util.Map;

@Component
@Slf4j
public class LoginVerifyInterceptor implements HandlerInterceptor {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
            String url = request.getRequestURI();  // 拿到接口路径
            // 只处理登录相关的请求
            if (!url.contains("/login")) {
                return true;
            }

            try {
                CustomHttpServletRequestWrapper customRequest = new CustomHttpServletRequestWrapper(request);
                String requestBody = customRequest.getBody();

                ObjectMapper objectMapper = new ObjectMapper();
                @SuppressWarnings("unchecked")
                Map<String, String> bodyMap = objectMapper.readValue(requestBody, Map.class);

                String verifyCode = bodyMap.get("verifyCode");  // 图形验证码
                String phone = bodyMap.get("phone");
                String email = bodyMap.get("email");
                String code = bodyMap.get("code");

                // 校验图形验证码（账号密码登录）
                if (bodyMap.get("username") != null && bodyMap.get("password") != null) {
                    // 检查用户名和密码是否为空
                    String username = bodyMap.get("username");
                    String password = bodyMap.get("password");

                    if (username == null || username.isEmpty()) {
                        log.warn("用户名为空");
                        respondWithJson(response, Result.fail("用户名不能为空").toString());
                        return false;
                    }

                    if (password == null || password.isEmpty()) {
                        log.warn("密码为空");
                        respondWithJson(response, Result.fail("密码不能为空").toString());
                        return false;
                    }

                    // 检查验证码
                    if (verifyCode == null || verifyCode.isEmpty()) {
                        log.warn("验证码为空");
                        respondWithJson(response, Result.fail("图形验证码不能为空").toString());
                        return false;
                    }

                    // 获取验证码标识符
                    String captchaId = bodyMap.get("captchaId");
                    if (captchaId == null || captchaId.isEmpty()) {
                        log.warn("验证码标识符为空");
                        respondWithJson(response, Result.fail("验证码标识符不能为空，请刷新验证码").toString());
                        return false;
                    }

                    String captchaKey = "captcha:answer:" + captchaId;

                    // 从Redis获取验证码
                    String redisCaptcha = redisTemplate.opsForValue().get(captchaKey);
                    log.info("验证码校验 - 用户输入: {}, 标识符: {}, 键名: {}, 存储答案: {}",
                            verifyCode, captchaId, captchaKey, redisCaptcha);

                    if (redisCaptcha == null) {
                        log.warn("验证码不存在或已过期 - 标识符: {}, 键名: {}", captchaId, captchaKey);
                        respondWithJson(response, Result.fail("验证码已过期，请重新获取验证码").toString());
                        return false;
                    }

                    if (!verifyCode.equals(redisCaptcha)) {
                        log.warn("验证码错误 - 用户输入: {}, 存储答案: {}", verifyCode, redisCaptcha);
                        respondWithJson(response, Result.fail("图形验证码错误，请重新输入").toString());
                        return false;
                    }

                    // 验证成功，删除验证码
                    redisTemplate.delete(captchaKey);
                    log.info("验证码验证成功，已从Redis删除 - 键名: {}", captchaKey);
                }

                // 手机/邮箱验证码方式（验证码登录）
                if ((phone != null || email != null) && code != null) {
                    // 检查验证码是否为空
                    if (code.isEmpty()) {
                        log.warn("验证码为空");
                        respondWithJson(response, Result.fail("验证码不能为空").toString());
                        return false;
                    }

                    // 检查邮箱/手机号是否为空
                    if (email != null && email.isEmpty()) {
                        log.warn("邮箱为空");
                        respondWithJson(response, Result.fail("邮箱不能为空").toString());
                        return false;
                    }

                    if (phone != null && phone.isEmpty()) {
                        log.warn("手机号为空");
                        respondWithJson(response, Result.fail("手机号不能为空").toString());
                        return false;
                    }

                    // 验证邮箱格式
                    if (email != null && !email.isEmpty() && !email.matches("^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$")) {
                        log.warn("邮箱格式错误: {}", email);
                        respondWithJson(response, Result.fail("邮箱格式不正确").toString());
                        return false;
                    }

                    // 验证手机号格式
                    if (phone != null && !phone.isEmpty() && !phone.matches("^1[3-9]\\d{9}$")) {
                        log.warn("手机号格式错误: {}", phone);
                        respondWithJson(response, Result.fail("手机号格式不正确").toString());
                        return false;
                    }

                    String redisKey = (email != null) ? "login:email:code:" + email : "login:phone:code:" + phone;
                    log.info("验证码键名: {}", redisKey);
                    String storedCode = redisTemplate.opsForValue().get(redisKey);
                    log.info("存储的验证码: {}", storedCode);

                    if (storedCode == null) {
                        log.warn("验证码不存在或已过期 - 键名: {}", redisKey);
                        respondWithJson(response, Result.fail("验证码已过期，请重新获取验证码").toString());
                        return false;
                    }

                    if (!code.equals(storedCode)) {
                        log.warn("验证码错误 - 用户输入: {}, 存储答案: {}", code, storedCode);
                        respondWithJson(response, Result.fail("验证码错误，请重新输入").toString());
                        return false;
                    }

                    // 验证成功，删除验证码
                    redisTemplate.delete(redisKey);
                    log.info("验证码验证成功，已从Redis删除 - 键名: {}", redisKey);
                }
            } catch (IOException e) {
                log.error("处理登录请求时发生错误", e);
                respondWithJson(response, Result.fail("处理请求时发生错误").toString());
                return false;
            }
        return true;
    }

    private void respondWithJson(HttpServletResponse response, String jsonStr) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.getWriter().write(jsonStr);
    }
}