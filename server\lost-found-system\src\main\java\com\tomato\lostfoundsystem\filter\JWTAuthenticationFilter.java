    package com.tomato.lostfoundsystem.filter;

    import com.tomato.lostfoundsystem.utils.JWTUtil;
    import com.tomato.lostfoundsystem.utils.RedisUtil;
    import io.jsonwebtoken.Claims;
    import jakarta.servlet.FilterChain;
    import jakarta.servlet.ServletException;
    import jakarta.servlet.http.HttpServletRequest;
    import jakarta.servlet.http.HttpServletResponse;
    import lombok.extern.slf4j.Slf4j;
    import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
    import org.springframework.security.core.authority.SimpleGrantedAuthority;
    import org.springframework.security.core.context.SecurityContextHolder;
    import org.springframework.web.filter.OncePerRequestFilter;
    import java.io.IOException;
    import java.util.ArrayList;
    import java.util.List;


    @Slf4j
    public class JWTAuthenticationFilter extends OncePerRequestFilter {

        private final JWTUtil jwtUtil;
        private final RedisUtil redisUtil;

        private static final String AUTH_HEADER = "Authorization";

        public JWTAuthenticationFilter(JWTUtil jwtUtil, RedisUtil redisUtil) {
            this.jwtUtil = jwtUtil;
            this.redisUtil = redisUtil;
        }

        @Override
        protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
                throws ServletException, IOException {

            // 首先从请求头中获取token
            String token = request.getHeader(AUTH_HEADER);

            // 如果请求头中没有token，尝试从URL参数中获取
            if (token == null || !token.startsWith("Bearer ")) {
                String tokenParam = request.getParameter("token");
                if (tokenParam != null && !tokenParam.isEmpty()) {
                    token = "Bearer " + tokenParam;
                    log.info("从URL参数中获取到token");
                }
            }

            log.debug("获取请求头中的token: {}", token);
            log.debug("Request received for: {}", request.getRequestURI());

            // 如果请求路径是公开的，直接放行
            String requestURI = request.getRequestURI();
            if (isPublicEndpoint(requestURI)) {
                log.debug("公开端点，无需验证: {}", requestURI);
                filterChain.doFilter(request, response);
                return;
            }

            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);  // 去掉 "Bearer " 前缀

                // 获取用户ID
                Long userId = jwtUtil.getUserIdFromToken(token);

                if (userId != null) {
                    // 检查 Redis 中的 Token 是否匹配
                    String redisKey = "jwt:user:" + userId;
                    String storedToken = redisUtil.get(redisKey);

                    if (storedToken != null && storedToken.equals(token)) {  // 验证 Token 是否匹配
                        Claims claims = jwtUtil.parseToken(token);
                        if (claims != null && !jwtUtil.isTokenExpired(token)) {
                            String username = claims.getSubject();
                            String roles = (String) claims.get("roles");  // 获取角色信息
                            log.info("用户角色:{}",roles);

                            // 将角色信息转换为 GrantedAuthority
                            List<SimpleGrantedAuthority> authorities = new ArrayList<>();
                            for (String role : roles.split(",")) {
                                authorities.add(new SimpleGrantedAuthority(role));  // 将角色转换为权限
                            }

                            if (username != null) {
                                // 设置认证信息到 SecurityContext，包括 userId、username 和 roles
                                UsernamePasswordAuthenticationToken authentication =
                                        new UsernamePasswordAuthenticationToken(username, null, authorities);
                                authentication.setDetails(userId);  // 将 userId 设置到 authentication 的 details

                                SecurityContextHolder.getContext().setAuthentication(authentication);

                                // 将 userId 存储到请求属性中（供后续访问）
                                request.setAttribute("userId", userId);  // 将 userId 存储到请求中
                                log.info("jwt过滤器验证成功: {}", username);
                            }
                        } else {
                            log.warn("Token 已过期");
                            writeUnauthorizedResponse(response, "登录状态已过期，请重新登录");
                            return;
                        }
                    } else {
                        log.warn("Redis 中不存在 token 或已被替换");
                        writeUnauthorizedResponse(response, "登录信息无效，请重新登录");
                        return;
                    }
                }
            }

            filterChain.doFilter(request, response);  // 继续过滤链
        }


        /**
         * 登录失败统一 JSON 响应
         */
        private void writeUnauthorizedResponse(HttpServletResponse response, String message) throws IOException {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            String body = String.format("{\"success\":false,\"code\":401,\"message\":\"%s\",\"data\":null}", message);
            response.getWriter().write(body);
        }

        /**
         * 判断请求路径是否是公开端点
         * @param requestURI 请求路径
         * @return 是否是公开端点
         */
        private boolean isPublicEndpoint(String requestURI) {
            // 定义公开的端点列表
            String[] publicEndpoints = {
                // 基本公开接口
                "/api/user/login",
                "/api/user/register",
                "/api/user/generateCaptcha",
                // 验证码相关接口
                "/api/verify/sendEmailCode",
                "/api/verify/sendPhoneCode",
                // 物品查询相关公开接口
                "/api/lost-items/search",
                "/api/found-items/search",
                "/api/lost-items/list",
                "/api/found-items/list",
                "/api/lost-items/detail/",
                "/api/found-items/detail/",
                // 系统公告相关公开接口
                "/api/announcements/latest",
                "/api/announcements/valid",
                // 统计数据相关公开接口
                "/api/statistics/home",
                // 测试接口
                "/api/test/",
                // WebSocket端点 - 统一使用/ws
                "/ws/"
            };

            // 检查请求路径是否匹配公开端点
            for (String endpoint : publicEndpoints) {
                if (requestURI.startsWith(endpoint)) {
                    return true;
                }
            }

            return false;
        }
    }
