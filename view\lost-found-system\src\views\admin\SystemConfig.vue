<template>
  <div class="system-config">
    <div class="page-header">
      <h2>系统配置管理</h2>
      <p class="description">
        在这里您可以管理系统的各项配置，包括智能匹配服务的URL、连接超时时间等。
      </p>
    </div>
    
    <div class="action-bar">
      <el-button type="primary" @click="refreshConfigs" :loading="loading.refresh">
        <el-icon><Refresh /></el-icon>
        刷新配置
      </el-button>
      <el-button type="success" @click="openAddConfigDialog">
        <el-icon><Plus /></el-icon>
        添加配置
      </el-button>
      <el-button type="warning" @click="refreshConfigCache" :loading="loading.refreshCache">
        <el-icon><RefreshRight /></el-icon>
        刷新缓存
      </el-button>
    </div>
    
    <!-- 配置列表 -->
    <el-card shadow="hover" class="config-card">
      <el-table
        :data="configList"
        style="width: 100%"
        v-loading="loading.table"
        border
      >
        <el-table-column prop="configKey" label="配置键" min-width="200" />
        <el-table-column prop="configValue" label="配置值" min-width="200">
          <template #default="{ row }">
            <div v-if="row.editing">
              <el-input v-model="row.editValue" />
            </div>
            <div v-else>
              {{ row.configValue }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="updatedAt" label="更新时间" min-width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.updatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div v-if="row.editing">
              <el-button type="primary" size="small" @click="saveConfig(row)">
                保存
              </el-button>
              <el-button size="small" @click="cancelEdit(row)">
                取消
              </el-button>
            </div>
            <div v-else>
              <el-button type="primary" size="small" @click="editConfig(row)">
                编辑
              </el-button>
              <el-popconfirm
                title="确定要删除此配置吗？"
                @confirm="deleteConfigItem(row.configKey)"
              >
                <template #reference>
                  <el-button type="danger" size="small">
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 添加配置对话框 -->
    <el-dialog
      v-model="addConfigDialog.visible"
      title="添加配置"
      width="500px"
    >
      <el-form
        :model="addConfigDialog.form"
        :rules="addConfigDialog.rules"
        ref="addConfigFormRef"
        label-width="100px"
      >
        <el-form-item label="配置键" prop="configKey">
          <el-input v-model="addConfigDialog.form.configKey" placeholder="请输入配置键" />
        </el-form-item>
        <el-form-item label="配置值" prop="configValue">
          <el-input v-model="addConfigDialog.form.configValue" placeholder="请输入配置值" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="addConfigDialog.form.description" placeholder="请输入配置描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addConfigDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="submitAddConfig" :loading="loading.add">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 智能匹配服务配置卡片 -->
    <el-card shadow="hover" class="config-card service-config-card">
      <template #header>
        <div class="card-header">
          <h3>智能匹配服务配置</h3>
          <el-button type="primary" size="small" @click="saveServiceConfig" :loading="loading.saveService">
            保存配置
          </el-button>
        </div>
      </template>
      
      <el-form :model="serviceConfig" label-width="150px">
        <el-form-item label="服务API地址">
          <el-input v-model="serviceConfig.apiUrl" placeholder="请输入智能匹配服务API地址" />
        </el-form-item>
        <el-form-item label="启用服务检查">
          <el-switch v-model="serviceConfig.checkEnabled" />
        </el-form-item>
        <el-form-item label="连接超时时间(毫秒)">
          <el-input-number v-model="serviceConfig.connectionTimeout" :min="1000" :max="10000" :step="500" />
        </el-form-item>
        <el-form-item label="服务脚本路径">
          <el-input v-model="serviceConfig.scriptPath" placeholder="请输入服务脚本路径" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Plus, RefreshRight } from '@element-plus/icons-vue'
import { 
  getAllConfigs, 
  updateConfigValue, 
  addConfig as apiAddConfig, 
  deleteConfig as apiDeleteConfig,
  refreshConfigCache as apiRefreshConfigCache,
  batchUpdateConfig
} from '@/api/config'

// 配置列表
const configList = ref([])

// 加载状态
const loading = reactive({
  table: false,
  refresh: false,
  add: false,
  refreshCache: false,
  saveService: false
})

// 添加配置对话框
const addConfigDialog = reactive({
  visible: false,
  form: {
    configKey: '',
    configValue: '',
    description: ''
  },
  rules: {
    configKey: [
      { required: true, message: '请输入配置键', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9._-]+$/, message: '配置键只能包含字母、数字、下划线、点和横杠', trigger: 'blur' }
    ],
    configValue: [
      { required: true, message: '请输入配置值', trigger: 'blur' }
    ]
  }
})

// 智能匹配服务配置
const serviceConfig = reactive({
  apiUrl: '',
  checkEnabled: true,
  connectionTimeout: 3000,
  scriptPath: './clip_faiss_service/start_clip_service.sh'
})

const addConfigFormRef = ref(null)

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  
  const date = new Date(dateTime)
  return date.toLocaleString()
}

// 获取所有配置
const fetchConfigs = async () => {
  loading.table = true
  try {
    const res = await getAllConfigs()
    if (res.code === 200 && res.data) {
      configList.value = res.data.map(item => ({
        ...item,
        editing: false,
        editValue: item.configValue
      }))
      
      // 更新智能匹配服务配置
      updateServiceConfigFromList()
    } else {
      ElMessage.error(res.message || '获取配置失败')
    }
  } catch (error) {
    console.error('获取配置失败:', error)
    ElMessage.error('获取配置失败')
  } finally {
    loading.table = false
  }
}

// 从配置列表更新智能匹配服务配置
const updateServiceConfigFromList = () => {
  const configs = configList.value
  
  // 查找智能匹配服务相关配置
  const apiUrlConfig = configs.find(item => item.configKey === 'autodl.clip.api.url')
  const checkEnabledConfig = configs.find(item => item.configKey === 'autodl.clip.service.check-enabled')
  const connectionTimeoutConfig = configs.find(item => item.configKey === 'autodl.clip.service.connection-timeout')
  const scriptPathConfig = configs.find(item => item.configKey === 'autodl.clip.service.script-path')
  
  // 更新服务配置
  if (apiUrlConfig) {
    serviceConfig.apiUrl = apiUrlConfig.configValue
  }
  
  if (checkEnabledConfig) {
    serviceConfig.checkEnabled = checkEnabledConfig.configValue === 'true'
  }
  
  if (connectionTimeoutConfig) {
    serviceConfig.connectionTimeout = parseInt(connectionTimeoutConfig.configValue) || 3000
  }
  
  if (scriptPathConfig) {
    serviceConfig.scriptPath = scriptPathConfig.configValue
  }
}

// 刷新配置
const refreshConfigs = async () => {
  loading.refresh = true
  try {
    await fetchConfigs()
    ElMessage.success('配置已刷新')
  } catch (error) {
    console.error('刷新配置失败:', error)
  } finally {
    loading.refresh = false
  }
}

// 编辑配置
const editConfig = (row) => {
  row.editing = true
  row.editValue = row.configValue
}

// 取消编辑
const cancelEdit = (row) => {
  row.editing = false
  row.editValue = row.configValue
}

// 保存配置
const saveConfig = async (row) => {
  try {
    const res = await updateConfigValue(row.configKey, row.editValue)
    if (res.code === 200) {
      row.configValue = row.editValue
      row.editing = false
      ElMessage.success('配置已更新')
      
      // 如果是智能匹配服务相关配置，更新服务配置
      if (row.configKey.startsWith('autodl.clip')) {
        updateServiceConfigFromList()
      }
    } else {
      ElMessage.error(res.message || '更新配置失败')
    }
  } catch (error) {
    console.error('更新配置失败:', error)
    ElMessage.error('更新配置失败')
  }
}

// 删除配置
const deleteConfigItem = async (configKey) => {
  try {
    const res = await apiDeleteConfig(configKey)
    if (res.code === 200) {
      configList.value = configList.value.filter(item => item.configKey !== configKey)
      ElMessage.success('配置已删除')
    } else {
      ElMessage.error(res.message || '删除配置失败')
    }
  } catch (error) {
    console.error('删除配置失败:', error)
    ElMessage.error('删除配置失败')
  }
}

// 打开添加配置对话框
const openAddConfigDialog = () => {
  addConfigDialog.form = {
    configKey: '',
    configValue: '',
    description: ''
  }
  addConfigDialog.visible = true
}

// 提交添加配置
const submitAddConfig = async () => {
  if (!addConfigFormRef.value) return
  
  addConfigFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.add = true
      try {
        const res = await apiAddConfig(addConfigDialog.form)
        if (res.code === 200) {
          ElMessage.success('配置已添加')
          addConfigDialog.visible = false
          fetchConfigs()
        } else {
          ElMessage.error(res.message || '添加配置失败')
        }
      } catch (error) {
        console.error('添加配置失败:', error)
        ElMessage.error('添加配置失败')
      } finally {
        loading.add = false
      }
    }
  })
}

// 刷新配置缓存
const refreshConfigCache = async () => {
  try {
    loading.refreshCache = true
    const res = await apiRefreshConfigCache()
    if (res.code === 200) {
      ElMessage.success('配置缓存已刷新')
    } else {
      ElMessage.error(res.message || '刷新配置缓存失败')
    }
  } catch (error) {
    console.error('刷新配置缓存失败:', error)
    ElMessage.error('刷新配置缓存失败')
  } finally {
    loading.refreshCache = false
  }
}

// 保存智能匹配服务配置
const saveServiceConfig = async () => {
  try {
    loading.saveService = true
    
    // 构建配置对象
    const configs = {
      'autodl.clip.api.url': serviceConfig.apiUrl,
      'autodl.clip.service.check-enabled': serviceConfig.checkEnabled.toString(),
      'autodl.clip.service.connection-timeout': serviceConfig.connectionTimeout.toString(),
      'autodl.clip.service.script-path': serviceConfig.scriptPath
    }
    
    // 批量更新配置
    const res = await batchUpdateConfig(configs)
    if (res.code === 200) {
      ElMessage.success('智能匹配服务配置已保存')
      fetchConfigs()
    } else {
      ElMessage.error(res.message || '保存智能匹配服务配置失败')
    }
  } catch (error) {
    console.error('保存智能匹配服务配置失败:', error)
    ElMessage.error('保存智能匹配服务配置失败')
  } finally {
    loading.saveService = false
  }
}

// 组件挂载时获取配置
onMounted(() => {
  fetchConfigs()
})
</script>

<style scoped>
.system-config {
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 12px 0;
  font-size: 22px;
  font-weight: 500;
  color: #303133;
}

.description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.action-bar {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
  gap: 10px;
}

.config-card {
  margin-bottom: 20px;
}

.service-config-card {
  margin-top: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
  }
}
</style>
