@echo off
echo ===================================
echo 正在创建桌面快捷方式...
echo ===================================

:: 获取当前目录的完整路径
set CURRENT_DIR=%cd%

:: 获取桌面路径
for /f "tokens=2* delims= " %%a in ('reg query "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop') do set DESKTOP_DIR=%%b

:: 创建启动所有服务的快捷方式
echo 创建"启动所有服务"快捷方式...
powershell "$s=(New-Object -COM WScript.Shell).CreateShortcut('%DESKTOP_DIR%\启动所有服务.lnk');$s.TargetPath='%CURRENT_DIR%\start_all_services.bat';$s.WorkingDirectory='%CURRENT_DIR%';$s.IconLocation='%SystemRoot%\System32\shell32.dll,22';$s.Save()"

:: 创建停止所有服务的快捷方式
echo 创建"停止所有服务"快捷方式...
powershell "$s=(New-Object -COM WScript.Shell).CreateShortcut('%DESKTOP_DIR%\停止所有服务.lnk');$s.TargetPath='%CURRENT_DIR%\stop_all_services.bat';$s.WorkingDirectory='%CURRENT_DIR%';$s.IconLocation='%SystemRoot%\System32\shell32.dll,27';$s.Save()"

echo.
echo ===================================
echo 桌面快捷方式创建完成！
echo ===================================

:: 不自动关闭窗口
pause
