/**
 * 联系人状态管理模块 (优化版)
 *
 * 该模块负责：
 * 1. 特定联系人在线状态的订阅和监控
 * 2. 联系人状态变化的通知
 * 3. 联系人状态的缓存管理
 * 4. 批量获取联系人状态
 *
 * 与其他模块的关系：
 * - websocket.js: 提供WebSocket客户端实例，用于订阅和发送消息
 * - online-status.js: 提供全局在线用户状态信息，本模块专注于特定联系人
 *
 * 使用方式：
 * import { subscribeContactStatus, isContactOnline, getContactsStatus } from '@/utils/contact-status'
 */

import { getWebSocketClient } from './websocket'
import { isUserOnline } from './online-status'

// 存储联系人在线状态
const contactStatus = new Map()

// 存储订阅状态
const subscriptions = new Map()

// 全局订阅
let globalSubscription = null
let initialized = false

/**
 * 初始化联系人状态管理
 */
export function initContactStatusManager() {
  if (initialized) {
    return
  }

  const stompClient = getWebSocketClient()
  if (!stompClient || !stompClient.connected) {
    // 监听WebSocket连接成功事件，连接成功后再初始化
    window.addEventListener('websocket-connected', initContactStatusManager, { once: true })
    return
  }

  // 订阅全局在线状态主题
  setupGlobalStatusListener()

  // 标记为已初始化
  initialized = true
}

/**
 * 订阅联系人在线状态
 * @param {string} contactId 联系人ID
 * @returns {boolean} 是否成功订阅
 */
export function subscribeContactStatus(contactId) {
  if (!contactId) {
    console.warn('联系人ID为空，无法订阅状态')
    return false
  }

  // 确保初始化
  if (!initialized) {
    initContactStatusManager()
  }

  // 检查是否已经订阅
  if (subscriptions.has(contactId)) {
    return true
  }

  const stompClient = getWebSocketClient()
  if (!stompClient || !stompClient.connected) {
    // 先检查全局在线状态
    const isOnline = isUserOnline(contactId)
    if (isOnline) {
      // 如果用户在全局在线列表中，更新联系人状态
      contactStatus.set(contactId, {
        online: true,
        lastActive: new Date().getTime()
      })
    }

    return false
  }

  try {
    // 发送订阅请求
    stompClient.publish({
      destination: `/app/subscribeContactStatus/${contactId}`,
      body: JSON.stringify({
        timestamp: new Date().getTime()
      })
    })

    // 记录订阅状态
    subscriptions.set(contactId, true)

    return true
  } catch (error) {
    console.error(`订阅联系人 ${contactId} 的在线状态时出错:`, error)
    return false
  }
}

/**
 * 取消订阅联系人在线状态
 * @param {string} contactId 联系人ID
 * @returns {boolean} 是否成功取消订阅
 */
export function unsubscribeContactStatus(contactId) {
  if (!subscriptions.has(contactId)) {
    return true
  }

  const stompClient = getWebSocketClient()
  if (stompClient && stompClient.connected) {
    try {
      // 发送取消订阅请求
      stompClient.publish({
        destination: `/app/unsubscribeContactStatus/${contactId}`,
        body: JSON.stringify({
          timestamp: new Date().getTime()
        })
      })
    } catch (error) {
      console.error(`发送取消订阅请求时出错:`, error)
    }
  }

  // 移除订阅记录
  subscriptions.delete(contactId)

  return true
}

/**
 * 批量获取联系人在线状态
 * @param {string[]} contactIds 联系人ID数组
 * @returns {Promise<Map<string, object>>} 联系人在线状态Map
 */
export function getContactsStatus(contactIds) {
  return new Promise((resolve, reject) => {
    if (!contactIds || contactIds.length === 0) {
      resolve(new Map())
      return
    }

    // 先从缓存中获取
    const cachedStatuses = new Map()
    const uncachedIds = []

    contactIds.forEach(id => {
      if (contactStatus.has(id)) {
        cachedStatuses.set(id, contactStatus.get(id))
      } else {
        // 检查全局在线状态
        const isOnline = isUserOnline(id)
        if (isOnline) {
          // 如果用户在全局在线列表中，更新联系人状态
          const status = {
            online: true,
            lastActive: new Date().getTime()
          }
          contactStatus.set(id, status)
          cachedStatuses.set(id, status)
        } else {
          uncachedIds.push(id)
        }
      }
    })

    // 如果所有ID都有缓存，直接返回
    if (uncachedIds.length === 0) {
      resolve(cachedStatuses)
      return
    }

    const stompClient = getWebSocketClient()
    if (!stompClient || !stompClient.connected) {
      // 如果WebSocket未连接，返回缓存的结果
      resolve(cachedStatuses)
      return
    }

    try {
      // 创建一个唯一的回调ID
      const callbackId = 'contacts-status-' + new Date().getTime()

      // 订阅一次性响应主题
      const subscription = stompClient.subscribe(`/user/queue/contactsStatus`, (message) => {
        try {
          const response = JSON.parse(message.body)

          // 更新联系人状态缓存
          if (response.contactsStatus) {
            Object.entries(response.contactsStatus).forEach(([id, status]) => {
              contactStatus.set(id, status)
              cachedStatuses.set(id, status)
            })
          }

          // 取消订阅
          subscription.unsubscribe()

          // 返回结果
          resolve(cachedStatuses)
        } catch (error) {
          console.error('处理联系人在线状态响应时出错:', error)
          // 即使出错，也返回缓存的结果
          resolve(cachedStatuses)
        }
      })

      // 发送请求
      stompClient.publish({
        destination: '/app/getContactsStatus',
        body: JSON.stringify({
          contactIds: uncachedIds,
          timestamp: new Date().getTime()
        })
      })
    } catch (error) {
      console.error('获取联系人在线状态时出错:', error)
      // 出错时返回缓存的结果
      resolve(cachedStatuses)
    }
  })
}

/**
 * 监听全局在线状态更新
 */
function setupGlobalStatusListener() {
  const stompClient = getWebSocketClient()
  if (!stompClient || !stompClient.connected) {
    return false
  }

  try {
    // 如果已有订阅，先取消
    if (globalSubscription) {
      try {
        globalSubscription.unsubscribe()
      } catch (e) {
        console.warn('取消旧订阅时出错:', e)
      }
    }

    // 订阅全局在线状态主题
    globalSubscription = stompClient.subscribe('/topic/onlineStatus', (message) => {
      try {
        // 尝试解析为JSON格式
        try {
          const data = JSON.parse(message.body)
          const contactId = data.userId || data.contactId

          if (contactId) {
            // 更新联系人状态
            contactStatus.set(contactId, {
              online: data.online,
              lastActive: data.lastActive || new Date().getTime()
            })

            // 触发状态更新事件
            window.dispatchEvent(new CustomEvent('contact-status-updated', {
              detail: {
                contactId: contactId,
                status: contactStatus.get(contactId)
              }
            }))
            return
          }
        } catch (e) {
          // 不是JSON格式，继续尝试文本格式
          console.debug('消息不是JSON格式，尝试解析文本格式')
        }

        // 解析文本格式: "User {userId} is online/offline/back online"
        const match = message.body.match(/User (\d+) is (online|offline|back online)/)
        if (match) {
          const contactId = match[1]
          const status = match[2]
          const isOnline = status === 'online' || status === 'back online'

          // 更新联系人状态
          contactStatus.set(contactId, {
            online: isOnline,
            lastActive: new Date().getTime()
          })

          // 触发状态更新事件
          window.dispatchEvent(new CustomEvent('contact-status-updated', {
            detail: {
              contactId: contactId,
              status: contactStatus.get(contactId)
            }
          }))
        }
      } catch (error) {
        console.error('处理全局在线状态消息时出错:', error)
      }
    })

    return true
  } catch (error) {
    console.error('订阅全局在线状态主题时出错:', error)
    return false
  }
}

/**
 * 检查联系人是否在线
 * @param {string} contactId 联系人ID
 * @returns {boolean} 是否在线
 */
export function isContactOnline(contactId) {
  // 先检查联系人状态缓存
  const status = contactStatus.get(contactId)
  if (status) {
    return status.online
  }

  // 如果缓存中没有，检查全局在线状态
  return isUserOnline(contactId)
}

/**
 * 获取联系人最后活跃时间
 * @param {string} contactId 联系人ID
 * @returns {number|null} 最后活跃时间
 */
export function getContactLastActiveTime(contactId) {
  const status = contactStatus.get(contactId)
  return status ? status.lastActive : null
}

// 监听WebSocket连接成功事件
window.addEventListener('websocket-connected', initContactStatusManager)

// 导出默认对象
export default {
  initContactStatusManager,
  subscribeContactStatus,
  unsubscribeContactStatus,
  getContactsStatus,
  isContactOnline,
  getContactLastActiveTime
}
