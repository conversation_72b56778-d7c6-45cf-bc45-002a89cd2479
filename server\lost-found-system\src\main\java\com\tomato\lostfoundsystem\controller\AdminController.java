package com.tomato.lostfoundsystem.controller;

import com.github.pagehelper.PageInfo;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.AddAdminDTO;
import com.tomato.lostfoundsystem.dto.FoundItemDetailDTO;
import com.tomato.lostfoundsystem.dto.ItemAuditDTO;
import com.tomato.lostfoundsystem.dto.LostItemDetailsDTO;
import com.tomato.lostfoundsystem.dto.StatusDTO;
import com.tomato.lostfoundsystem.dto.UserProfileDTO;
import com.tomato.lostfoundsystem.service.AdminService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/admin")
public class AdminController {

    private final AdminService adminService;

    @Autowired
    public AdminController(AdminService adminService) {
        this.adminService = adminService;
    }

    /**
     * 获取失物列表
     * @param page 页码
     * @param size 每页数量
     * @param keyword 关键词
     * @param auditStatus 审核状态
     * @param status 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userId 用户ID
     * @return 失物列表
     */
    @GetMapping("/lost-items")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result<PageInfo<LostItemDetailsDTO>> queryAuditList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String auditStatus,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Long userId
    ) {
        return adminService.queryAuditList(page, size, keyword, auditStatus, status, startDate, endDate, userId);
    }

    /**
     * 获取拾物列表
     * @param page 页码
     * @param size 每页数量
     * @param keyword 关键词
     * @param auditStatus 审核状态
     * @param status 状态
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param userId 用户ID
     * @return 拾物列表
     */
    @GetMapping("/found-items")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result<PageInfo<FoundItemDetailDTO>> queryFoundItemList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String auditStatus,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Long userId
    ) {
        return adminService.queryFoundItemList(page, size, keyword, auditStatus, status, startDate, endDate, userId);
    }


    /**
     * 审核失物信息
     * @param id          失物ID
     * @param itemAuditDTO 审核状态和备注
     * @param userId     管理员ID
     * @return 操作结果
     */

    @PutMapping("/audit/lost-item/{id}")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result auditLostItem(@PathVariable Long id,
                                @RequestBody ItemAuditDTO itemAuditDTO,
                                @RequestAttribute Long userId) {
        return adminService.auditLostItem(id, itemAuditDTO, userId);
    }

    /**
     * 审核拾物信息
     * @param id          拾物ID
     * @param itemAuditDTO 审核状态和备注
     * @return 操作结果
     */
    @PutMapping("/audit/found-item/{id}")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result<?> auditFoundItem(@PathVariable Long id,
                                 @RequestBody ItemAuditDTO itemAuditDTO,
                                 @RequestAttribute(value = "userId", required = false) Long adminId) {
        // 添加日志记录
        log.info("审核拾物信息 - ID: {}, 状态: {}, 管理员ID: {}", id, itemAuditDTO.getAuditStatus(), adminId);

        // 如果 adminId 为空，记录错误并返回错误信息
        if (adminId == null) {
            log.error("审核拾物失败 - 管理员ID为空");
            return Result.fail("审核失败，管理员身份验证失败");
        }

        return adminService.auditFoundItem(id, itemAuditDTO, adminId);
    }

    /**
     * 更新拾物状态
     * @param id 拾物ID
     * @param status 状态信息
     * @return 操作结果
     */
    @PutMapping("/found-items/{id}/status")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result<?> updateFoundItemStatus(@PathVariable Long id, @RequestBody StatusDTO status) {
        return adminService.updateFoundItemStatus(id, status.getStatus());
    }

    /**
     * 删除拾物
     * @param id 拾物ID
     * @return 操作结果
     */
    @DeleteMapping("/found-items/{id}")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result<?> deleteFoundItem(@PathVariable Long id) {
        return adminService.deleteFoundItem(id);
    }

    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    @PostMapping("/add-admin")
    public Result addAdmin(@RequestBody AddAdminDTO addAdminDTO, @RequestAttribute Long userId) {
        return adminService.addAdmin(addAdminDTO, userId);
    }

    /**获取用户列表
     *
     * @param page
     * @param size
     * @param keyword
     * @return
     */
    @GetMapping("/users")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result getUserList(@RequestParam(defaultValue = "1") int page,
                              @RequestParam(defaultValue = "10") int size,
                              @RequestParam(required = false) String keyword,
                              @RequestParam(required = false) Boolean isActive,
                              @RequestParam(required = false) String role,
                              @RequestParam(required = false) Boolean deleted) {
        log.info("{关键词:}",keyword);
        return adminService.getUserList(page, size, keyword,isActive,role,deleted);
    }

    /**
     * 获取用户详情
     * @param id
     * @return
     */
    @GetMapping("/users/{id}")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result getUserById(@PathVariable Long id) {
        return adminService.getUserById(id);
    }

    /**
     * 修改用户角色
     * @param id
     * @param
     * @return
     */

    @PutMapping("/users/{id}/role")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public Result updateUserRole(@PathVariable Long id,@RequestBody UserProfileDTO dto) {
        return adminService.updateUserRole(id, dto.getRole());
    }

    /**
     * 启用和禁用用户
     * @param id
     * @param
     * @return
     */
    @PutMapping("/users/{id}/status")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result updateUserStatus(@PathVariable Long id, @RequestBody UserProfileDTO dto) {
        return adminService.updateUserStatus(id, dto.getIsActive());
    }

    /**
     * 管理员重置密码
     * @param id
     * @param
     * @param currentAdminId
     * @return
     */


    @PutMapping("/users/{id}/reset-password")
    @PreAuthorize("hasAnyAuthority('ADMIN') or hasAnyAuthority('SUPER_ADMIN')")
    public Result resetUserPassword(@PathVariable Long id, @RequestBody UserProfileDTO dto,
                                    @RequestAttribute("userId") Long currentAdminId) {
        return adminService.resetUserPassword(id, dto.getPassword(), currentAdminId);
    }



    @DeleteMapping("/users/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<?> deleteUser(@PathVariable Long id) {
        return adminService.deleteUser(id);
    }

    /**
     * 获取管理员统计数据
     * 包括失物统计、拾物统计和用户统计
     *
     * @return 统计数据
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('ADMIN') or hasAuthority('SUPER_ADMIN')")
    public Result<Map<String, Object>> getAdminStatistics() {
        try {
            log.info("获取管理员统计数据");
            return adminService.getAdminStatistics();
        } catch (Exception e) {
            log.error("获取管理员统计数据失败", e);
            return Result.fail("获取统计数据失败: " + e.getMessage());
        }
    }

}



