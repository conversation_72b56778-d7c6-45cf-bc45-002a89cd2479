import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import axios from 'axios';
import { useUserStore } from './userStore';

export const useConversationStore = defineStore('conversation', () => {
  // 状态
  const conversations = ref([]);
  const loading = ref(false);
  const error = ref(null);
  
  // 计算属性
  const sortedConversations = computed(() => {
    return [...conversations.value].sort((a, b) => {
      // 先按置顶状态排序
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      
      // 再按最后消息时间排序
      const timeA = new Date(a.lastTime).getTime();
      const timeB = new Date(b.lastTime).getTime();
      return timeB - timeA;
    });
  });
  
  const totalUnreadCount = computed(() => {
    return conversations.value.reduce((total, conv) => total + (conv.unreadCount || 0), 0);
  });
  
  // 方法
  async function fetchConversations() {
    const userStore = useUserStore();
    if (!userStore.user) return;
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await axios.get('/api/chat/contacts', {
        params: { userId: userStore.user.id }
      });
      
      if (response.data.code === 200) {
        conversations.value = response.data.data;
      } else {
        error.value = response.data.message || '获取会话列表失败';
      }
    } catch (err) {
      console.error('获取会话列表失败:', err);
      error.value = err.message || '获取会话列表失败';
    } finally {
      loading.value = false;
    }
  }
  
  async function updateConversationStatus(conversationId, status) {
    try {
      const response = await axios.put(`/api/chat/session/${conversationId}/status`, null, {
        params: { status }
      });
      
      if (response.data.code === 200) {
        // 如果是删除操作，从列表中移除
        if (status === 'DELETED') {
          conversations.value = conversations.value.filter(conv => conv.conversationId !== conversationId);
        } 
        // 如果是归档操作，更新状态
        else if (status === 'ARCHIVED') {
          const conversation = conversations.value.find(conv => conv.conversationId === conversationId);
          if (conversation) {
            conversation.status = status;
          }
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error(`更新会话状态失败 (${status}):`, err);
      return false;
    }
  }
  
  async function updateConversationPinned(conversationId, isPinned) {
    try {
      const response = await axios.put(`/api/chat/session/${conversationId}/pin`, null, {
        params: { isPinned }
      });
      
      if (response.data.code === 200) {
        const conversation = conversations.value.find(conv => conv.conversationId === conversationId);
        if (conversation) {
          conversation.isPinned = isPinned;
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error(`更新会话置顶状态失败:`, err);
      return false;
    }
  }
  
  async function updateConversationMuted(conversationId, isMuted) {
    try {
      const response = await axios.put(`/api/chat/session/${conversationId}/mute`, null, {
        params: { isMuted }
      });
      
      if (response.data.code === 200) {
        const conversation = conversations.value.find(conv => conv.conversationId === conversationId);
        if (conversation) {
          conversation.isMuted = isMuted;
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error(`更新会话静音状态失败:`, err);
      return false;
    }
  }
  
  async function resetUnreadCount(userId, contactId) {
    try {
      const response = await axios.put('/api/chat/session/reset-unread', null, {
        params: { userId, contactId }
      });
      
      if (response.data.code === 200) {
        const conversation = conversations.value.find(conv => conv.contactId === contactId);
        if (conversation) {
          conversation.unreadCount = 0;
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error('重置未读计数失败:', err);
      return false;
    }
  }
  
  function updateConversationWithMessage(message) {
    const userStore = useUserStore();
    if (!userStore.user) return;
    
    const userId = userStore.user.id;
    const isCurrentUser = message.senderId === userId;
    const contactId = isCurrentUser ? message.receiverId : message.senderId;
    
    // 查找现有会话
    let conversation = conversations.value.find(conv => conv.contactId === contactId);
    
    if (conversation) {
      // 更新现有会话
      conversation.lastMessage = message.message;
      conversation.lastTime = new Date(message.timestamp).toISOString();
      conversation.messageType = message.messageType;
      
      // 如果不是当前用户发送的消息，增加未读计数
      if (!isCurrentUser) {
        conversation.unreadCount = (conversation.unreadCount || 0) + 1;
      }
    } else {
      // 创建新会话
      const newConversation = {
        contactId,
        conversationId: null, // 后端会分配
        name: message.senderName || `用户${contactId}`,
        avatar: message.senderAvatar || '',
        lastMessage: message.message,
        lastTime: new Date(message.timestamp).toISOString(),
        messageType: message.messageType,
        unreadCount: isCurrentUser ? 0 : 1,
        isPinned: false,
        isMuted: false,
        status: 'ACTIVE'
      };
      
      conversations.value.push(newConversation);
    }
  }
  
  return {
    conversations,
    loading,
    error,
    sortedConversations,
    totalUnreadCount,
    fetchConversations,
    updateConversationStatus,
    updateConversationPinned,
    updateConversationMuted,
    resetUnreadCount,
    updateConversationWithMessage
  };
});
