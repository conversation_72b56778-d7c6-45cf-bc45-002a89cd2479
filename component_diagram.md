```mermaid
graph TB
    %% 标题
    title[校园失物招领系统组件图]
    style title fill:none,stroke:none
    
    %% 前端组件
    subgraph Frontend[前端应用]
        UI[用户界面]
        Router[路由管理]
        State[状态管理]
        APIClient[API客户端]
        Validation[表单验证]
    end
    style Frontend fill:#D5F5E3,stroke:#27AE60
    
    %% 后端组件
    subgraph Backend[后端服务]
        Controller[控制器层]
        Service[服务层]
        DAO[数据访问层]
        SecurityFilter[安全过滤器]
        ExceptionHandler[异常处理]
        WebSocketHandler[WebSocket处理器]
    end
    style Backend fill:#F9E79F,stroke:#F1C40F
    
    %% 业务模块
    subgraph BusinessModules[业务模块]
        UserModule[用户管理]
        LostItemModule[失物管理]
        FoundItemModule[招领管理]
        MatchModule[智能匹配]
        ChatModule[即时通讯]
        AnnouncementModule[系统公告]
    end
    style BusinessModules fill:#D6EAF8,stroke:#2E86C1
    
    %% 外部服务
    OSS[阿里云OSS]
    CLIP[CLIP+FAISS服务]
    MySQL[(MySQL)]
    Redis[(Redis)]
    Kafka[(Kafka)]
    style OSS fill:#D6DBDF,stroke:#2C3E50
    style CLIP fill:#D6DBDF,stroke:#2C3E50
    style MySQL fill:#F5B7B1,stroke:#E74C3C
    style Redis fill:#F5B7B1,stroke:#E74C3C
    style Kafka fill:#FADBD8,stroke:#E74C3C
    
    %% 接口定义
    RestAPI((RESTful API))
    WsAPI((WebSocket API))
    DBAPI((数据库接口))
    CacheAPI((缓存接口))
    FileAPI((文件存储接口))
    MatchAPI((智能匹配接口))
    style RestAPI fill:#F9E79F,stroke:#F1C40F
    style WsAPI fill:#F9E79F,stroke:#F1C40F
    style DBAPI fill:#F5B7B1,stroke:#E74C3C
    style CacheAPI fill:#F5B7B1,stroke:#E74C3C
    style FileAPI fill:#D6DBDF,stroke:#2C3E50
    style MatchAPI fill:#D6DBDF,stroke:#2C3E50
    
    %% 连接关系
    UI --> Router
    Router --> State
    State --> APIClient
    UI --> Validation
    APIClient --> RestAPI
    APIClient --> WsAPI
    
    Controller --- RestAPI
    WebSocketHandler --- WsAPI
    SecurityFilter --> Service
    Controller --> SecurityFilter
    Controller --> ExceptionHandler
    Service --> DAO
    Service --> WebSocketHandler
    WebSocketHandler --> Kafka
    
    DAO --> DBAPI
    Service --> CacheAPI
    Service --> FileAPI
    Service --> MatchAPI
    
    MySQL --- DBAPI
    Redis --- CacheAPI
    OSS --- FileAPI
    CLIP --- MatchAPI
    
    UserModule --> Service
    LostItemModule --> Service
    FoundItemModule --> Service
    MatchModule --> Service
    ChatModule --> Service
    AnnouncementModule --> Service
    
    Service --> OSS
    Service --> CLIP
    Service --> Kafka
    DAO --> MySQL
    Service --> Redis
    
    %% 连接标签
    linkStyle 22 stroke:#2E86C1,stroke-dasharray:5 5
    linkStyle 23 stroke:#2E86C1,stroke-dasharray:5 5
    linkStyle 24 stroke:#2E86C1,stroke-dasharray:5 5
    linkStyle 25 stroke:#2E86C1,stroke-dasharray:5 5
```
