package com.tomato.lostfoundsystem.service.Impl;

import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.service.VerifyCodeService;
import com.tomato.lostfoundsystem.utils.AliyunSmsUtil;
import com.tomato.lostfoundsystem.utils.VerifyCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;

import java.util.concurrent.TimeUnit;

/**
 * 验证码服务实现类
 */
@Slf4j
@Service
public class VerifyCodeServiceImpl implements VerifyCodeService {

    private static final String EMAIL_SUBJECT = "【校园失物招领系统】验证码";
    private static final String EMAIL_CONTENT = "您的验证码是：%s，有效期5分钟，请尽快填写。";
    private static final long CODE_EXPIRE_MINUTES = 5;
    private static final String REGISTER_PREFIX = "register";
    private static final String LOGIN_PREFIX = "login";

    @Value("${spring.mail.username}")
    private String senderEmail;

    private final JavaMailSender javaMailSender;
    private final StringRedisTemplate redisTemplate;
    private final AliyunSmsUtil aliyunSmsUtil;

    public VerifyCodeServiceImpl(JavaMailSender javaMailSender,
                                 StringRedisTemplate redisTemplate,
                                 AliyunSmsUtil aliyunSmsUtil) {
        this.javaMailSender = javaMailSender;
        this.redisTemplate = redisTemplate;
        this.aliyunSmsUtil = aliyunSmsUtil;
    }

    @Override
    public Result<String> sendEmailVerifyCode(String email, boolean isRegister) {
        try {
            // 1. 参数校验
            if (!StringUtils.hasText(email)) {
                return Result.fail("邮箱地址不能为空");
            }

            // 2. 生成验证码
            String code = VerifyCodeUtil.generateCode();
            log.info("生成邮箱验证码: {}", code);

            // 3. 存储验证码
            String redisKey = generateRedisKey(email, isRegister, true);
            storeVerifyCode(redisKey, code);

            // 4. 发送邮件
            sendEmail(email, code);

            return Result.success("验证码已发送至邮箱");
        } catch (Exception e) {
            log.error("发送邮箱验证码失败: {}", e.getMessage(), e);
            return Result.fail("验证码发送失败，请稍后重试");
        }
    }

    @Override
    public Result<String> sendPhoneVerifyCode(String phone, boolean isRegister) {
        log.info("开始处理发送手机验证码请求 - 手机号: {}, 是否注册场景: {}", phone, isRegister);

        try {
            // 1. 参数校验
            if (!StringUtils.hasText(phone)) {
                log.warn("手机号为空，请求被拒绝");
                return Result.fail("手机号不能为空");
            }

            // 验证手机号格式
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                log.warn("手机号格式不正确 - 手机号: {}", phone);
                return Result.fail("手机号格式不正确");
            }

            // 2. 生成验证码
            String code = VerifyCodeUtil.generateCode();
            log.info("生成手机验证码: {}, 手机号: {}", code, phone);

            // 打印验证码（仅开发环境）
            System.out.println("\n==================================================");
            System.out.println("手机号 " + phone + " 的验证码: " + code);
            System.out.println("==================================================\n");

            // 3. 存储验证码
            String redisKey = generateRedisKey(phone, isRegister, false);
            log.info("准备将验证码存储到Redis - Key: {}, 验证码: {}", redisKey, code);

            try {
                storeVerifyCode(redisKey, code);
                log.info("验证码已成功存储到Redis - Key: {}", redisKey);
            } catch (Exception e) {
                log.error("存储验证码到Redis失败 - Key: {}", redisKey, e);
                return Result.fail("验证码生成失败，请稍后重试");
            }

            // 4. 发送短信
            log.info("准备发送短信 - 手机号: {}, 验证码: {}", phone, code);
            try {
                sendSms(phone, code);
                log.info("短信发送成功 - 手机号: {}", phone);
            } catch (Exception e) {
                log.error("短信发送失败 - 手机号: {}, 错误: {}", phone, e.getMessage(), e);

                // 返回成功，但在日志中记录错误（仅开发环境）
                return Result.success("验证码已生成: " + code + "（仅开发环境显示）");
            }

            return Result.success("验证码已发送至手机号");
        } catch (Exception e) {
            log.error("发送手机验证码过程中发生未知异常", e);
            return Result.fail("验证码发送失败，请稍后重试");
        }
    }

    /**
     * 生成Redis key
     */
    private String generateRedisKey(String target, boolean isRegister, boolean isEmail) {
        String prefix = isRegister ? REGISTER_PREFIX : LOGIN_PREFIX;
        String type = isEmail ? "email" : "phone";
        return String.format("%s:%s:code:%s", prefix, type, target);
    }

    /**
     * 存储验证码到Redis
     */
    private void storeVerifyCode(String redisKey, String code) {
        redisTemplate.opsForValue().set(redisKey, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        log.info("验证码已存储到Redis，key: {}, code: {}", redisKey, code);
    }

    /**
     * 发送邮件
     */
    private void sendEmail(String email, String code) throws MessagingException {
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        helper.setFrom(senderEmail);
        helper.setTo(email);
        helper.setSubject(EMAIL_SUBJECT);
        helper.setText(String.format(EMAIL_CONTENT, code), true);
        javaMailSender.send(message);
        log.info("邮件发送成功，接收邮箱: {}", email);
    }

    /**
     * 发送短信
     */
    private void sendSms(String phone, String code) throws ClientException {
        SendSmsResponse response = aliyunSmsUtil.sendCode(phone, code);
        if (!"OK".equals(response.getCode())) {
            log.error("短信发送失败，手机号: {}, 错误信息: {}", phone, response.getMessage());
            throw new RuntimeException("短信发送失败: " + response.getMessage());
        }
        log.info("短信发送成功，接收手机号: {}", phone);
    }
}