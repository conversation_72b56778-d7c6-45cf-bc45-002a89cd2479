package com.tomato.lostfoundsystem.websocket;

import com.tomato.lostfoundsystem.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WebSocketEventListener {

    @Autowired
    private RedisService redisService;  // 用于操作 Redis
    @Autowired
    private SimpMessagingTemplate messagingTemplate;  // 用于推送消息

    // 监听 WebSocket STOMP 连接成功事件
    @EventListener
    public void handleWebSocketConnectedListener(SessionConnectedEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();

        // 获取用户信息
        Long userId = null;
        String username = null;

        if (headerAccessor.getSessionAttributes() != null) {
            userId = (Long) headerAccessor.getSessionAttributes().get("userId");
            username = (String) headerAccessor.getSessionAttributes().get("username");
        }

        if (userId != null && userId != 0) {
            log.info("WebSocket STOMP连接成功 - 用户: {}, 用户名: {}, 会话ID: {}", userId, username, sessionId);

            // 可以在这里添加其他连接成功后的处理逻辑
            // 例如：发送欢迎消息、更新在线状态等
            messagingTemplate.convertAndSend("/topic/onlineStatus", "User " + userId + " is online");
        } else {
            log.info("WebSocket STOMP连接成功 - 匿名用户, 会话ID: {}", sessionId);
        }
    }

    // 监听 WebSocket 会话断开事件
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        SimpMessageHeaderAccessor headerAccessor = SimpMessageHeaderAccessor.wrap(event.getMessage());

        // 获取用户ID（可以在会话属性中保存用户ID）
        Long userId = (Long) headerAccessor.getSessionAttributes().get("userId");

        if (userId != null|| userId !=0) {
            try {
                // 清理 Redis 中的会话信息
                // 删除指定用户的会话信息
                String pattern = "user:" + userId + ":session";
                redisService.deleteByPattern(pattern);
                // 推送用户下线状态到全局广播主题
                messagingTemplate.convertAndSend("/topic/onlineStatus", "User " + userId + " is offline");
                // 可以考虑添加针对特定用户的消息
                // messagingTemplate.convertAndSendToUser(userId.toString(), "/topic/onlineStatus", "You are now offline");

                log.info("User {} disconnected and marked as offline.", userId);
            } catch (Exception e) {
                log.error("Failed to handle offline status for user {}: {}", userId, e.getMessage());
            }
        }
    }
}

