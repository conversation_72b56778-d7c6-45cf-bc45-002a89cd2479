<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//MyBatis//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomato.lostfoundsystem.mapper.UserNotificationMapper">

    <insert id="insertNotification">
        INSERT INTO user_notifications (
            user_id, title, message, status,
            type, related_item_id, related_item_type, metadata, auditor_id
        )
        VALUES (
            #{userId}, #{title}, #{message}, #{status},
            #{type}, #{relatedItemId}, #{relatedItemType}, #{metadata}, #{auditorId}
        );
    </insert>

    <!-- 根据用户ID和通知状态查询通知 -->
    <select id="getNotificationsByStatus" resultType="com.tomato.lostfoundsystem.entity.Notification">
        SELECT
            id, user_id as userId, title, message, status,
            created_at as createdAt, updated_at as updatedAt,
            type, related_item_id as relatedItemId, related_item_type as relatedItemType,
            metadata, auditor_id as auditorId
        FROM user_notifications
        WHERE user_id = #{userId}
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID获取所有通知 -->
    <select id="getNotifications" resultType="com.tomato.lostfoundsystem.entity.Notification">
        SELECT
            id, user_id as userId, title, message, status,
            created_at as createdAt, updated_at as updatedAt,
            type, related_item_id as relatedItemId, related_item_type as relatedItemType,
            metadata, auditor_id as auditorId
        FROM user_notifications
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
    </select>

    <!-- 获取用户未读通知数量 -->
    <select id="getUnreadCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM user_notifications
        WHERE user_id = #{userId}
          AND status = 'UNREAD'
    </select>

    <!-- 根据通知ID更新通知为已读 -->
    <update id="updateNotificationStatus">
        UPDATE user_notifications
        SET status = #{status}
        WHERE id = #{notificationId}
    </update>

    <!-- 删除通知 -->
    <delete id="deleteNotification">
        DELETE FROM user_notifications
        WHERE id = #{notificationId}
    </delete>

</mapper>
