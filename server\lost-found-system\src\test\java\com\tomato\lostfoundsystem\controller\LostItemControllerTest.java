package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.LostItemDTO;
import com.tomato.lostfoundsystem.service.LostItemService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.containsString;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.xpath;

@ExtendWith(MockitoExtension.class)
public class LostItemControllerTest {

    @Mock
    private LostItemService lostItemService;

    @InjectMocks
    private LostItemController lostItemController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(lostItemController)
                .setControllerAdvice(new GlobalExceptionHandler())
                .build();
    }

    // 添加全局异常处理器
    public static class GlobalExceptionHandler {
        @ExceptionHandler(Exception.class)
        public Result<Object> handleException(Exception e) {
            return Result.fail(e.getMessage());
        }
    }

    @Test
    void publishLostItem_WithValidData_ShouldReturnSuccess() throws Exception {
        // 准备测试数据
        MockMultipartFile image = new MockMultipartFile(
                "image",
                "test.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test image content".getBytes()
        );

        // 模拟服务层返回成功结果
        when(lostItemService.publishLostItem(any(LostItemDTO.class)))
                .thenReturn(Result.success("发布成功,等待审核", null));

        // 执行测试
        mockMvc.perform(multipart("/api/lost-items/publish")
                        .file(image)
                        .param("itemName", "测试物品")
                        .param("description", "这是一个详细的物品描述，包含了物品的颜色、形状和特征，长度超过20个字符。")
                        .param("lostTime", "2023-05-01T10:00:00")
                        .param("lostLocation", "图书馆")
                        .requestAttr("userId", 1L)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(content().contentType("application/xml;charset=UTF-8"))
                .andExpect(xpath("/r/code").string("200"))
                .andExpect(xpath("/r/message").string("发布成功,等待审核"));

        // 验证服务层方法被调用
        verify(lostItemService, times(1)).publishLostItem(any(LostItemDTO.class));
    }

    @Test
    void publishLostItem_WithoutImage_ShouldReturnSuccess() throws Exception {
        // 模拟服务层返回成功结果
        when(lostItemService.publishLostItem(any(LostItemDTO.class)))
                .thenReturn(Result.success("发布成功,等待审核", null));

        // 执行测试
        mockMvc.perform(multipart("/api/lost-items/publish")
                        .param("itemName", "测试物品")
                        .param("description", "这是一个详细的物品描述，包含了物品的颜色、形状和特征，长度超过20个字符。")
                        .param("lostTime", "2023-05-01T10:00:00")
                        .param("lostLocation", "图书馆")
                        .requestAttr("userId", 1L)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(content().contentType("application/xml;charset=UTF-8"))
                .andExpect(xpath("/r/code").string("200"))
                .andExpect(xpath("/r/message").string("发布成功,等待审核"));

        // 验证服务层方法被调用
        verify(lostItemService, times(1)).publishLostItem(any(LostItemDTO.class));
    }

    @Test
    void publishLostItem_WithShortDescription_ShouldReturnError() throws Exception {
        // 执行测试
        mockMvc.perform(multipart("/api/lost-items/publish")
                        .param("itemName", "测试物品")
                        .param("description", "描述太短")
                        .param("lostTime", "2023-05-01T10:00:00")
                        .param("lostLocation", "图书馆")
                        .requestAttr("userId", 1L)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(content().contentType("application/xml;charset=UTF-8"))
                .andExpect(xpath("/r/code").string("400"))
                .andExpect(xpath("/r/message").string(containsString("物品描述长度必须在20-500个字符之间")));

        // 验证服务层方法未被调用
        verify(lostItemService, never()).publishLostItem(any(LostItemDTO.class));
    }

    @Test
    void publishLostItem_WithoutUserId_ShouldReturnError() throws Exception {
        // 执行测试（不设置userId属性）
        mockMvc.perform(multipart("/api/lost-items/publish")
                        .param("itemName", "测试物品")
                        .param("description", "这是一个详细的物品描述，包含了物品的颜色、形状和特征，长度超过20个字符。")
                        .param("lostTime", "2023-05-01T10:00:00")
                        .param("lostLocation", "图书馆")
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(content().contentType("application/xml;charset=UTF-8"))
                .andExpect(xpath("/r/code").string("400"))
                .andExpect(xpath("/r/message").string("用户未登录或登录已过期"));

        // 验证服务层方法未被调用
        verify(lostItemService, never()).publishLostItem(any(LostItemDTO.class));
    }

    @Test
    void publishLostItem_WhenServiceThrowsException_ShouldReturnError() throws Exception {
        // 模拟服务层抛出异常
        when(lostItemService.publishLostItem(any(LostItemDTO.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试
        mockMvc.perform(multipart("/api/lost-items/publish")
                        .param("itemName", "测试物品")
                        .param("description", "这是一个详细的物品描述，包含了物品的颜色、形状和特征，长度超过20个字符。")
                        .param("lostTime", "2023-05-01T10:00:00")
                        .param("lostLocation", "图书馆")
                        .requestAttr("userId", 1L)
                        .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(content().contentType("application/xml;charset=UTF-8"))
                .andExpect(xpath("/r/code").string("400"))
                .andExpect(xpath("/r/message").string("发布失败，请稍后重试"));

        // 验证服务层方法被调用
        verify(lostItemService, times(1)).publishLostItem(any(LostItemDTO.class));
    }
}
