package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.dto.ContactDTO;
import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.entity.Conversation;
import com.tomato.lostfoundsystem.entity.MessageAttachment;
import com.tomato.lostfoundsystem.entity.User;
import com.tomato.lostfoundsystem.enums.FileType;
import com.tomato.lostfoundsystem.enums.MessageType;
import com.tomato.lostfoundsystem.mapper.ConversationMapper;
import com.tomato.lostfoundsystem.mapper.MessageAttachmentMapper;
import com.tomato.lostfoundsystem.mapper.UserMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ConversationServiceImplTest {

    @InjectMocks
    private ConversationServiceImpl conversationService;

    @Mock
    private ConversationMapper conversationMapper;

    @Mock
    private UserMapper userMapper;

    @Mock
    private MessageAttachmentMapper messageAttachmentMapper;

    @Mock
    private RedisServiceImpl redisService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateConversation_NewConversation() {
        // 准备测试数据
        Long senderId = 1L;
        Long receiverId = 2L;

        // 模拟 conversationMapper.getConversation 返回 null，表示不存在会话
        when(conversationMapper.getConversation(senderId, receiverId)).thenReturn(null);

        // 模拟 conversationMapper.insert 方法，捕获参数
        doAnswer(invocation -> {
            Conversation conversation = invocation.getArgument(0);
            conversation.setId(100L); // 模拟数据库生成ID
            return null;
        }).when(conversationMapper).insert(any(Conversation.class));

        // 调用被测试方法
        Long conversationId = conversationService.createConversation(senderId, receiverId);

        // 验证结果
        assertNotNull(conversationId);
        assertEquals(100L, conversationId);

        // 验证 conversationMapper.getConversation 被调用
        verify(conversationMapper).getConversation(senderId, receiverId);

        // 验证 conversationMapper.insert 被调用，并且参数正确
        verify(conversationMapper).insert(argThat(conversation -> {
            assertEquals(senderId, conversation.getUser1Id());
            assertEquals(receiverId, conversation.getUser2Id());
            assertNotNull(conversation.getLastMessageTime());
            assertEquals(0, conversation.getUnreadCount());
            assertEquals("ACTIVE", conversation.getStatus());
            assertFalse(conversation.getIsPinned());
            assertFalse(conversation.getIsMuted());
            assertNotNull(conversation.getCreatedAt());
            assertNotNull(conversation.getUpdatedAt());
            return true;
        }));
    }

    @Test
    void testCreateConversation_ExistingConversation() {
        // 准备测试数据
        Long senderId = 1L;
        Long receiverId = 2L;

        // 创建一个已存在的会话
        Conversation existingConversation = new Conversation();
        existingConversation.setId(100L);
        existingConversation.setUser1Id(senderId);
        existingConversation.setUser2Id(receiverId);

        // 模拟 conversationMapper.getConversation 返回已存在的会话
        when(conversationMapper.getConversation(senderId, receiverId)).thenReturn(existingConversation);

        // 调用被测试方法
        Long conversationId = conversationService.createConversation(senderId, receiverId);

        // 验证结果
        assertNotNull(conversationId);
        assertEquals(100L, conversationId);

        // 验证 conversationMapper.getConversation 被调用
        verify(conversationMapper).getConversation(senderId, receiverId);

        // 验证 conversationMapper.insert 没有被调用
        verify(conversationMapper, never()).insert(any(Conversation.class));
    }

    @Test
    void testGetOrCreateConversation_NewConversation() {
        // 准备测试数据
        Long user1Id = 1L;
        Long user2Id = 2L;

        // 模拟 conversationMapper.getConversation 返回 null，表示不存在会话
        when(conversationMapper.getConversation(user1Id, user2Id)).thenReturn(null);

        // 模拟 conversationMapper.insert 方法，捕获参数
        doAnswer(invocation -> {
            Conversation conversation = invocation.getArgument(0);
            conversation.setId(100L); // 模拟数据库生成ID
            return null;
        }).when(conversationMapper).insert(any(Conversation.class));

        // 调用被测试方法
        Conversation conversation = conversationService.getOrCreateConversation(user1Id, user2Id);

        // 验证结果
        assertNotNull(conversation);
        assertEquals(100L, conversation.getId());
        assertEquals(user1Id, conversation.getUser1Id());
        assertEquals(user2Id, conversation.getUser2Id());

        // 验证 conversationMapper.getConversation 被调用
        verify(conversationMapper).getConversation(user1Id, user2Id);

        // 验证 conversationMapper.insert 被调用
        verify(conversationMapper).insert(any(Conversation.class));
    }

    @Test
    void testGetOrCreateConversation_ExistingConversation() {
        // 准备测试数据
        Long user1Id = 1L;
        Long user2Id = 2L;

        // 创建一个已存在的会话
        Conversation existingConversation = new Conversation();
        existingConversation.setId(100L);
        existingConversation.setUser1Id(user1Id);
        existingConversation.setUser2Id(user2Id);

        // 模拟 conversationMapper.getConversation 返回已存在的会话
        when(conversationMapper.getConversation(user1Id, user2Id)).thenReturn(existingConversation);

        // 调用被测试方法
        Conversation conversation = conversationService.getOrCreateConversation(user1Id, user2Id);

        // 验证结果
        assertNotNull(conversation);
        assertEquals(100L, conversation.getId());

        // 验证 conversationMapper.getConversation 被调用
        verify(conversationMapper).getConversation(user1Id, user2Id);

        // 验证 conversationMapper.insert 没有被调用
        verify(conversationMapper, never()).insert(any(Conversation.class));
    }

    @Test
    void testUpdateConversationWithMessage() {
        // 准备测试数据
        ChatMessage message = new ChatMessage();
        message.setId(1L);
        message.setSenderId(1L);
        message.setReceiverId(2L);
        message.setMessage("Test message");
        message.setMessageType(MessageType.TEXT);
        message.setTimestamp(new Date());

        // 创建一个已存在的会话
        Conversation existingConversation = new Conversation();
        existingConversation.setId(100L);
        existingConversation.setUser1Id(1L);
        existingConversation.setUser2Id(2L);

        // 模拟 getOrCreateConversation 方法返回已存在的会话
        when(conversationMapper.getConversation(anyLong(), anyLong())).thenReturn(existingConversation);

        // 调用被测试方法
        conversationService.updateConversationWithMessage(message);

        // 验证 updateLastMessage 被调用，并且参数正确
        verify(conversationMapper).updateLastMessage(
            eq(100L),
            eq(1L),
            contains("Test message"),
            eq("TEXT"),
            any(Date.class)
        );
    }

    @Test
    void testResetUnreadCount() {
        // 准备测试数据
        Long userId = 1L;
        Long contactId = 2L;

        // 调用被测试方法
        conversationService.resetUnreadCount(userId, contactId);

        // 验证 resetUnreadCount 被调用，并且参数正确
        verify(conversationMapper).resetUnreadCount(userId, contactId);
    }

    @Test
    void testIncrementUnreadCount() {
        // 准备测试数据
        Long userId = 1L;
        Long contactId = 2L;

        // 调用被测试方法
        conversationService.incrementUnreadCount(userId, contactId);

        // 验证 incrementUnreadCount 被调用，并且参数正确
        verify(conversationMapper).incrementUnreadCount(userId, contactId);
    }

    @Test
    void testUpdateStatus() {
        // 准备测试数据
        Long conversationId = 100L;
        String status = "ARCHIVED";

        // 调用被测试方法
        conversationService.updateStatus(conversationId, status);

        // 验证 updateStatus 被调用，并且参数正确
        verify(conversationMapper).updateStatus(conversationId, status);
    }

    @Test
    void testUpdatePinned() {
        // 准备测试数据
        Long conversationId = 100L;
        Boolean isPinned = true;

        // 调用被测试方法
        conversationService.updatePinned(conversationId, isPinned);

        // 验证 updatePinned 被调用，并且参数正确
        verify(conversationMapper).updatePinned(conversationId, isPinned);
    }

    @Test
    void testUpdateMuted() {
        // 准备测试数据
        Long conversationId = 100L;
        Boolean isMuted = true;

        // 调用被测试方法
        conversationService.updateMuted(conversationId, isMuted);

        // 验证 updateMuted 被调用，并且参数正确
        verify(conversationMapper).updateMuted(conversationId, isMuted);
    }

    @Test
    void testGetContacts_WithConversations() {
        // 准备测试数据
        Long userId = 1L;

        // 创建会话列表
        List<Conversation> conversations = new ArrayList<>();

        // 会话1：用户1和用户2
        Conversation conversation1 = new Conversation();
        conversation1.setId(100L);
        conversation1.setUser1Id(userId);
        conversation1.setUser2Id(2L);
        conversation1.setLastMessageId(1L);
        conversation1.setLastMessageContent("Hello");
        conversation1.setLastMessageType("TEXT");
        conversation1.setLastMessageTime(new Date());
        conversation1.setUnreadCount(2);
        conversation1.setStatus("ACTIVE");
        conversation1.setIsPinned(true);
        conversation1.setIsMuted(false);
        conversations.add(conversation1);

        // 会话2：用户1和用户3
        Conversation conversation2 = new Conversation();
        conversation2.setId(101L);
        conversation2.setUser1Id(3L);
        conversation2.setUser2Id(userId);
        conversation2.setLastMessageId(2L);
        conversation2.setLastMessageContent("How are you?");
        conversation2.setLastMessageType("TEXT");
        conversation2.setLastMessageTime(new Date());
        conversation2.setUnreadCount(0);
        conversation2.setStatus("ACTIVE");
        conversation2.setIsPinned(false);
        conversation2.setIsMuted(true);
        conversations.add(conversation2);

        // 模拟 conversationMapper.getConversationsByUserId 返回会话列表
        when(conversationMapper.getConversationsByUserId(userId)).thenReturn(conversations);

        // 创建用户2
        User user2 = new User();
        user2.setId(2L);
        user2.setUsername("User2");
        user2.setAvatar("avatar2.jpg");

        // 创建用户3
        User user3 = new User();
        user3.setId(3L);
        user3.setUsername("User3");
        user3.setAvatar("avatar3.jpg");

        // 模拟 userMapper.findById 返回用户
        when(userMapper.findById(2L)).thenReturn(user2);
        when(userMapper.findById(3L)).thenReturn(user3);

        // 模拟 messageAttachmentMapper.getAttachmentByMessageId 返回附件
        MessageAttachment attachment = new MessageAttachment();
        attachment.setId(1L);
        attachment.setMessageId(1L);
        attachment.setFileUrl("file.jpg");
        attachment.setFileType(FileType.IMAGE);
        attachment.setFileSize(1024L);
        when(messageAttachmentMapper.getAttachmentByMessageId(1L)).thenReturn(attachment);
        when(messageAttachmentMapper.getAttachmentByMessageId(2L)).thenReturn(null);

        // 调用被测试方法
        List<ContactDTO> contacts = conversationService.getContacts(userId);

        // 验证结果
        assertNotNull(contacts);
        assertEquals(2, contacts.size());

        // 验证第一个联系人（用户2）
        ContactDTO contact1 = contacts.get(0);
        assertEquals(2L, contact1.getContactId());
        assertEquals("User2", contact1.getName());
        assertEquals("avatar2.jpg", contact1.getAvatar());
        assertEquals("U", contact1.getAvatarText());
        assertEquals("Hello", contact1.getLastMessage());
        assertNotNull(contact1.getLastTime());
        assertEquals(MessageType.TEXT, contact1.getMessageType());
        assertEquals(2, contact1.getUnreadCount());
        assertNotNull(contact1.getMessageAttachmentDTO());
        assertEquals("file.jpg", contact1.getMessageAttachmentDTO().getFileUrl());

        // 验证第二个联系人（用户3）
        ContactDTO contact2 = contacts.get(1);
        assertEquals(3L, contact2.getContactId());
        assertEquals("User3", contact2.getName());
        assertEquals("avatar3.jpg", contact2.getAvatar());
        assertEquals("U", contact2.getAvatarText());
        assertEquals("How are you?", contact2.getLastMessage());
        assertNotNull(contact2.getLastTime());
        assertEquals(MessageType.TEXT, contact2.getMessageType());
        assertEquals(0, contact2.getUnreadCount());
        assertNull(contact2.getMessageAttachmentDTO());

        // 验证 conversationMapper.getConversationsByUserId 被调用
        verify(conversationMapper).getConversationsByUserId(userId);

        // 验证 userMapper.findById 被调用
        verify(userMapper).findById(2L);
        verify(userMapper).findById(3L);

        // 验证 messageAttachmentMapper.getAttachmentByMessageId 被调用
        verify(messageAttachmentMapper).getAttachmentByMessageId(1L);
        verify(messageAttachmentMapper).getAttachmentByMessageId(2L);
    }

    @Test
    void testGetContacts_NoConversations() {
        // 准备测试数据
        Long userId = 1L;

        // 模拟 conversationMapper.getConversationsByUserId 返回空列表
        when(conversationMapper.getConversationsByUserId(userId)).thenReturn(new ArrayList<>());

        // 调用被测试方法
        List<ContactDTO> contacts = conversationService.getContacts(userId);

        // 验证结果
        assertNotNull(contacts);
        assertTrue(contacts.isEmpty());

        // 验证 conversationMapper.getConversationsByUserId 被调用
        verify(conversationMapper).getConversationsByUserId(userId);

        // 验证 userMapper.findById 没有被调用
        verify(userMapper, never()).findById(anyLong());

        // 验证 messageAttachmentMapper.getAttachmentByMessageId 没有被调用
        verify(messageAttachmentMapper, never()).getAttachmentByMessageId(anyLong());
    }
}
