package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.dto.LostItemDTO;
import com.tomato.lostfoundsystem.dto.LostItemDetailsDTO;
import com.tomato.lostfoundsystem.entity.LostItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface LostItemMapper {
    int insertLostItem(LostItem lostItem);

    /**
     * 按照多个条件查询失物信息
     */
    List<LostItem> selectLostItems(@Param("keyword") String keyword,
                                   @Param("lostLocation") String lostLocation,
                                   @Param("status") String status,
                                   @Param("startDateTime") LocalDateTime startDateTime,
                                   @Param("endDateTime") LocalDateTime endDateTime,
                                   @Param("timeFilterType") String timeFilterType);

    LostItemDetailsDTO selectLostItemDetailsById(Long id);

    LostItem selectById(Long id);

    int updateById(LostItem lostItem);

    int updateAuditStatus(LostItem lostItem);

    /**
     * 更新失物状态
     * @param id 失物ID
     * @param status 状态值
     * @return 影响的行数
     */
    int updateLostItemStatus(@Param("id") Long id, @Param("status") String status);

    LostItem selectLostItemById(Long id);

    int deleteLostItem(Long id);

    List<LostItem> findLostItemsByUserId(Long userId);

    List<LostItemDetailsDTO> selectAuditList(@Param("keyword") String keyword,
                                             @Param("auditStatus") String auditStatus,
                                             @Param("status") String status,
                                             @Param("startDate") LocalDateTime startDate,
                                             @Param("endDate") LocalDateTime endDate,
                                             @Param("userId") Long userId);

    /**
     * 根据审核状态统计失物数量
     *
     * @param auditStatus 审核状态
     * @return 失物数量
     */
    int countByAuditStatus(@Param("auditStatus") String auditStatus);

    /**
     * 根据状态和审核状态统计失物数量
     *
     * @param status 状态
     * @param auditStatus 审核状态
     * @return 失物数量
     */
    int countByStatusAndAuditStatus(@Param("status") String status, @Param("auditStatus") String auditStatus);

}
