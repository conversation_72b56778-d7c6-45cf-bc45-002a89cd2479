<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <div class="logo-container">
          <!-- 暂时移除 logo 图片 -->
        </div>
        <h1 class="auth-title">用户注册</h1>
      </div>

      <div class="auth-content">
        <el-form
          ref="formRef"
          :model="registerForm"
          :rules="rules"
          class="auth-form"
          @submit.prevent="handleRegister"
        >
          <!-- 用户名 -->
          <el-form-item prop="username">
            <div class="input-container">
              <el-icon class="input-icon"><User /></el-icon>
              <el-input
                v-model="registerForm.username"
                placeholder="请输入用户名"
                class="auth-input"
              />
            </div>
          </el-form-item>

          <!-- 密码 -->
          <el-form-item prop="password">
            <div class="input-container">
              <el-icon class="input-icon"><Lock /></el-icon>
              <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="请输入密码"
                class="auth-input"
                show-password
              />
            </div>
          </el-form-item>

          <!-- 确认密码 -->
          <el-form-item prop="confirmPassword">
            <div class="input-container">
              <el-icon class="input-icon"><Lock /></el-icon>
              <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="请再次输入密码"
                class="auth-input"
                show-password
              />
            </div>
          </el-form-item>

          <!-- 注册方式 -->
          <el-form-item prop="verifyType">
            <div class="verify-type-selector">
              <div
                class="verify-type-option"
                :class="{ 'active': registerForm.verifyType === 'email' }"
                @click="registerForm.verifyType = 'email'"
              >
                <el-icon><Message /></el-icon>
                <span>邮箱注册</span>
              </div>
              <div
                class="verify-type-option"
                :class="{ 'active': registerForm.verifyType === 'phone' }"
                @click="registerForm.verifyType = 'phone'"
              >
                <el-icon><Phone /></el-icon>
                <span>手机号注册</span>
              </div>
            </div>
          </el-form-item>

          <!-- 邮箱 -->
          <el-form-item
            v-if="registerForm.verifyType === 'email'"
            prop="email"
          >
            <div class="input-container">
              <el-icon class="input-icon"><Message /></el-icon>
              <el-input
                v-model="registerForm.email"
                placeholder="请输入邮箱"
                class="auth-input"
              />
            </div>
          </el-form-item>

          <!-- 手机号 -->
          <el-form-item
            v-if="registerForm.verifyType === 'phone'"
            prop="phone"
          >
            <div class="input-container">
              <el-icon class="input-icon"><Phone /></el-icon>
              <el-input
                v-model="registerForm.phone"
                placeholder="请输入手机号"
                class="auth-input"
              />
            </div>
          </el-form-item>

          <!-- 验证码 -->
          <el-form-item prop="code">
            <div class="captcha-container">
              <div class="input-container captcha-input">
                <el-icon class="input-icon"><Key /></el-icon>
                <el-input
                  v-model="registerForm.code"
                  placeholder="请输入验证码"
                  class="auth-input"
                />
              </div>
              <el-button
                type="primary"
                class="code-btn"
                :disabled="codeTimer > 0"
                @click="handleSendCode"
              >
                {{ codeTimer > 0 ? `${codeTimer}秒后重试` : '获取验证码' }}
              </el-button>
            </div>
          </el-form-item>

          <!-- 用户协议 -->
          <div class="auth-options">
            <el-checkbox v-model="registerForm.agreement">
              我已阅读并同意
              <el-link type="primary" @click="showAgreement">《用户协议》</el-link>
              和
              <el-link type="primary" @click="showPrivacy">《隐私政策》</el-link>
            </el-checkbox>
          </div>

          <!-- 注册按钮 -->
          <el-button
            type="primary"
            native-type="submit"
            class="submit-btn"
            :loading="loading"
          >
            立即注册
          </el-button>

          <div class="auth-divider">
            <span>或者</span>
          </div>

          <!-- 返回登录 -->
          <div class="auth-links">
            <router-link to="/login" class="login-link">
              <el-icon><ArrowLeft /></el-icon> 返回登录
            </router-link>
          </div>
        </el-form>
      </div>

      <div class="auth-footer">
        <p>© {{ new Date().getFullYear() }} 校园失物招领系统 - 让失物找到归属</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { register, sendEmailCode, sendPhoneCode } from '../api/user'
import { ElMessage } from 'element-plus'
import {
  User,
  Lock,
  Key,
  Message,
  Phone,
  ArrowLeft
} from '@element-plus/icons-vue'

const router = useRouter()
const formRef = ref(null)
const loading = ref(false)
const codeTimer = ref(0)
let codeTimerInstance = null

const registerForm = ref({
  username: '',
  password: '',
  confirmPassword: '',
  verifyType: '', // 注册方式：email 或 phone
  email: '',
  phone: '',
  code: '',
  agreement: false
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.value.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  verifyType: [
    { required: true, message: '请选择注册方式', trigger: 'change' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ],
  agreement: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请阅读并同意用户协议和隐私政策'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 发送验证码
const handleSendCode = async () => {
  // 验证用户名和密码
  if (!registerForm.value.username || !registerForm.value.password || !registerForm.value.confirmPassword) {
    ElMessage.warning('请先填写用户名和密码')
    return
  }

  // 验证密码一致性
  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    ElMessage.warning('两次输入的密码不一致')
    return
  }

  // 验证注册方式
  if (!registerForm.value.verifyType) {
    ElMessage.warning('请选择注册方式')
    return
  }

  // 验证邮箱或手机号
  const field = registerForm.value.verifyType
  const value = registerForm.value[field]
  if (!value) {
    ElMessage.warning(`请先输入${field === 'email' ? '邮箱' : '手机号'}`)
    return
  }

  // 验证用户协议
  if (!registerForm.value.agreement) {
    ElMessage.warning('请阅读并同意用户协议和隐私政策')
    return
  }

  // 验证邮箱格式
  if (field === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    ElMessage.warning('请输入正确的邮箱格式')
    return
  }

  // 验证手机号格式
  if (field === 'phone' && !/^1[3-9]\d{9}$/.test(value)) {
    ElMessage.warning('请输入正确的手机号格式')
    return
  }

  // 构建验证码请求数据
  const verifyCodeData = {
    isRegister: true // 注册场景
  }

  // 根据注册方式设置邮箱或手机号
  if (field === 'email') {
    verifyCodeData.email = value
    verifyCodeData.phone = null
  } else {
    verifyCodeData.phone = value
    verifyCodeData.email = null
  }

  console.log('发送验证码请求数据:', verifyCodeData)

  const api = field === 'email' ? sendEmailCode : sendPhoneCode
  const res = await api(verifyCodeData)
  console.log('验证码发送响应:', res)

  ElMessage.success('验证码已发送')
  codeTimer.value = 60
  codeTimerInstance = setInterval(() => {
    if (codeTimer.value > 0) {
      codeTimer.value--
    } else {
      clearInterval(codeTimerInstance)
    }
  }, 1000)
}

// 显示用户协议
const showAgreement = () => {
  ElMessage.info('用户协议内容')
}

// 显示隐私政策
const showPrivacy = () => {
  ElMessage.info('隐私政策内容')
}

// 处理注册
const handleRegister = async () => {
  if (!formRef.value) return

  try {
    loading.value = true
    await formRef.value.validate()

    // 构建注册数据
    const registerData = {
      username: registerForm.value.username,
      password: registerForm.value.password,
      confirmPassword: registerForm.value.confirmPassword,
      code: registerForm.value.code
    }

    // 根据注册方式添加邮箱或手机号
    if (registerForm.value.verifyType === 'email') {
      registerData.email = registerForm.value.email
    } else {
      registerData.phone = registerForm.value.phone
    }

    console.log('注册请求数据:', registerData)

    const res = await register(registerData)
    console.log('注册响应:', res)

    ElMessage.success({
      message: '注册成功，即将跳转到登录页面',
      duration: 2000
    })

    // 延迟跳转，让用户看到成功消息
    setTimeout(() => {
      router.push('/login')
    }, 1500)
  } catch (error) {
    console.error('注册失败:', error)
    // 重置验证码计时器
    codeTimer.value = 0
    if (codeTimerInstance) {
      clearInterval(codeTimerInstance)
    }

    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('注册失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 主容器样式 */
.auth-container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

/* 卡片样式 */
.auth-card {
  width: 100%;
  max-width: 480px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 头部样式 */
.auth-header {
  padding: 30px 20px;
  text-align: center;
  background: linear-gradient(to right, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.logo-container {
  margin-bottom: 15px;
}

.auth-logo {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.auth-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

/* 内容区域样式 */
.auth-content {
  padding: 30px;
}

/* 表单样式 */
.auth-form {
  margin-top: 20px;
}

.input-container {
  position: relative;
  margin-bottom: 5px;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #909399;
  z-index: 1;
  font-size: 18px;
}

.auth-input :deep(.el-input__wrapper) {
  padding-left: 40px;
  height: 50px;
  border-radius: 8px;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.3s;
}

.auth-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.auth-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

/* 验证码容器样式 */
.captcha-container {
  display: flex;
  gap: 10px;
}

.captcha-input {
  flex: 1;
}

/* 验证码按钮样式 */
.code-btn {
  min-width: 120px;
  height: 50px;
  border-radius: 8px;
}

/* 验证方式选择器样式 */
.verify-type-selector {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.verify-type-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  transition: all 0.3s;
}

.verify-type-option .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #909399;
}

.verify-type-option span {
  font-size: 14px;
  color: #606266;
}

.verify-type-option.active {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.verify-type-option.active .el-icon,
.verify-type-option.active span {
  color: #409EFF;
}

/* 选项样式 */
.auth-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin: 15px 0;
}

:deep(.el-checkbox) {
  margin-bottom: 0;
}

:deep(.el-link) {
  margin: 0 4px;
}

/* 提交按钮样式 */
.submit-btn {
  width: 100%;
  height: 50px;
  font-size: 16px;
  border-radius: 8px;
  margin-top: 10px;
  transition: all 0.3s;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

/* 分隔线样式 */
.auth-divider {
  display: flex;
  align-items: center;
  margin: 25px 0;
  color: #909399;
}

.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: #ebeef5;
}

.auth-divider span {
  padding: 0 15px;
  font-size: 14px;
}

/* 链接样式 */
.auth-links {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.login-link {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #409EFF;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s;
}

.login-link:hover {
  color: #66b1ff;
  transform: translateY(-2px);
}

/* 页脚样式 */
.auth-footer {
  padding: 15px;
  text-align: center;
  font-size: 12px;
  color: #909399;
  border-top: 1px solid #f0f0f0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .auth-container {
    padding: 10px;
  }

  .auth-content {
    padding: 20px 15px;
  }

  .auth-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 表单项动画 */
.el-form-item {
  animation: slideUp 0.5s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

.el-form-item:nth-child(1) { animation-delay: 0.1s; }
.el-form-item:nth-child(2) { animation-delay: 0.2s; }
.el-form-item:nth-child(3) { animation-delay: 0.3s; }
.el-form-item:nth-child(4) { animation-delay: 0.4s; }
.el-form-item:nth-child(5) { animation-delay: 0.5s; }
.el-form-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>