package com.tomato.lostfoundsystem.filter;

import com.tomato.lostfoundsystem.utils.CustomHttpServletRequestWrapper;
import org.springframework.beans.factory.annotation.Value;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)  // 确保在所有过滤器之前执行
public class RepeatableRequestFilter implements Filter {

    // 从配置文件中读取需要重包装的路径
    @Value("${request.body.wrap.paths}")
    private String requestBodyWrapPaths;

    private List<String> wrapPaths;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 加载配置文件中的路径，并将其拆分为路径列表
        if (requestBodyWrapPaths != null) {
            wrapPaths = Arrays.asList(requestBodyWrapPaths.split(","));
        }
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;

            // 判断是否是 POST 请求
            if ("POST".equalsIgnoreCase(httpServletRequest.getMethod())||"PUT".equalsIgnoreCase(httpServletRequest.getMethod())) {
                String requestURI = httpServletRequest.getRequestURI();

                // 判断请求路径是否需要重包装
                boolean shouldWrap = false;
                for (String path : wrapPaths) {
                    if (requestURI.contains(path)) {
                        shouldWrap = true;
                        break;
                    }
                }

                // 记录路径匹配结果
                log.debug("请求路径: {}, 是否需要包装: {}", requestURI, shouldWrap);

                if (shouldWrap) {

                    // 检查是否为 multipart/form-data 请求（文件上传）
                    if (isMultipartContent(httpServletRequest)) {
                        log.info("Skipping request body wrapping for multipart/form-data request to URI: {}", requestURI);
                        chain.doFilter(request, response);  // 直接传递，不进行包装
                        return;
                    }

                    log.info("Wrapping request body for POST request to URI: {}", requestURI);
                    CustomHttpServletRequestWrapper wrappedRequest = new CustomHttpServletRequestWrapper(httpServletRequest);
                    chain.doFilter(wrappedRequest, response);  // 传递包装后的请求
                    return;
                }
            }
        }

        // 对其他请求直接传递
        chain.doFilter(request, response);
    }

    // 判断是否为 multipart/form-data 类型请求
    private boolean isMultipartContent(HttpServletRequest request) {
        String contentType = request.getContentType();
        return contentType != null && contentType.toLowerCase().startsWith("multipart/form-data");
    }

    @Override
    public void destroy() {
        // 清理资源
    }
}
