<template>
  <el-dialog
    v-model="dialogVisible"
    :title="notification?.title || '匹配通知详情'"
    width="500px"
    destroy-on-close
    class="match-notification-dialog"
    top="10vh"
  >
    <div class="match-notification-detail">
      <!-- 匹配基本信息 -->
      <div class="match-header">
        <div class="match-info">
          <el-tag :type="getMatchTypeTagType(notification.matchType)" class="match-type-tag">
            {{ getMatchTypeLabel(notification.matchType) }}
          </el-tag>
          <el-tag type="success" effect="plain" class="similarity-tag">
            匹配度: {{ (notification.similarity * 100).toFixed(0) }}%
          </el-tag>
        </div>

        <!-- 简要物品信息 -->
        <div class="item-brief">
          <span class="item-name">{{ notification.itemName || '蓝色保温杯' }}</span>
          <span class="item-location">{{ notification.itemLocation || '学校食堂一楼' }}</span>
        </div>
      </div>

      <!-- 相似度雷达图 -->
      <div v-if="hasSimilarityDetails" class="similarity-radar">
        <h4>匹配度详情分析</h4>
        <similarity-radar-chart
          :similarities="similarityDetails"
          height="250px"
          title=""
        />
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" size="small" @click="viewRelatedItem">
          查看物品详情
        </el-button>
        <el-button type="success" size="small" @click="contactOwner">
          联系{{ notification.itemType === 'LOST' ? '失主' : '拾主' }}
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button size="small" @click="closeDialog">关闭</el-button>
        <el-button type="danger" size="small" @click="handleDelete">删除通知</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox, ElMessage } from 'element-plus'
import SimilarityRadarChart from '@/components/SimilarityRadarChart.vue'
import { deleteMatchNotification } from '@/api/notification'

const props = defineProps({
  notification: {
    type: Object,
    required: true,
    default: () => ({})
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'deleted'])
const router = useRouter()
const dialogVisible = ref(false)

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  console.log('【匹配通知调试】visible属性变化:', newVal);
  dialogVisible.value = newVal;
})

// 监听对话框可见性变化
watch(dialogVisible, (newVal) => {
  console.log('【匹配通知调试】对话框可见性变化:', newVal);
  emit('update:visible', newVal);
})

// 计算属性：相似度详情
const similarityDetails = computed(() => {
  return {
    image_to_image: props.notification.imageToImageSimilarity || 0,
    image_to_text: props.notification.imageToTextSimilarity || 0,
    text_to_image: props.notification.textToImageSimilarity || 0,
    text_to_text: props.notification.textToTextSimilarity || 0
  }
})

// 不再需要格式化后的相似度详情

// 计算属性：是否有相似度详情
const hasSimilarityDetails = computed(() => {
  return props.notification.imageToImageSimilarity !== null ||
         props.notification.imageToTextSimilarity !== null ||
         props.notification.textToImageSimilarity !== null ||
         props.notification.textToTextSimilarity !== null
})

// 简化版本中不再需要关联物品标题

// 获取匹配类型的标签类型
const getMatchTypeTagType = (matchType) => {
  if (!matchType) return 'info'

  const types = {
    'IMAGE_TO_IMAGE': 'success',
    'IMAGE_TO_TEXT': 'warning',
    'TEXT_TO_IMAGE': 'warning',
    'TEXT_TO_TEXT': 'primary',
    'MIXED': 'danger'
  }
  return types[matchType] || 'info'
}

// 获取匹配类型的友好标签
const getMatchTypeLabel = (matchType) => {
  if (!matchType) return '综合匹配'

  const labels = {
    'IMAGE_TO_IMAGE': '外观匹配',
    'IMAGE_TO_TEXT': '图像-描述匹配',
    'TEXT_TO_IMAGE': '描述-图像匹配',
    'TEXT_TO_TEXT': '描述匹配',
    'MIXED': '混合匹配'
  }
  return labels[matchType] || '综合匹配'
}

// 简化版本中不再需要这些方法

// 简化版本中不再需要格式化日期时间方法

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
}

// 查看关联物品
const viewRelatedItem = () => {
  try {
    console.log('【匹配通知调试】查看关联物品，通知对象:', props.notification);
    console.log('【匹配通知调试】物品ID:', props.notification.itemId);
    console.log('【匹配通知调试】物品类型:', props.notification.itemType);

    // 确保物品ID和类型存在
    if (!props.notification.itemId) {
      console.error('【匹配通知调试】错误: 物品ID不存在');
      ElMessage.error('无法查看物品详情：物品ID不存在');
      return;
    }

    if (!props.notification.itemType) {
      console.error('【匹配通知调试】错误: 物品类型不存在');
      ElMessage.error('无法查看物品详情：物品类型不存在');
      return;
    }

    // 构建路由路径
    const path = props.notification.itemType === 'LOST'
      ? `/lost-items/detail/${props.notification.itemId}`
      : `/found-items/detail/${props.notification.itemId}`;

    console.log('【匹配通知调试】跳转路径:', path);

    // 关闭对话框
    closeDialog();

    // 使用setTimeout确保对话框完全关闭后再跳转
    setTimeout(() => {
      // 使用try-catch包装路由跳转
      try {
        console.log('【匹配通知调试】开始路由跳转到:', path);
        router.push(path);
        console.log('【匹配通知调试】路由跳转成功');
      } catch (error) {
        console.error('【匹配通知调试】路由跳转失败:', error);
        // 尝试使用window.location作为备选方案
        console.log('【匹配通知调试】尝试使用window.location跳转');
        window.location.href = path;
      }
    }, 200);
  } catch (error) {
    console.error('【匹配通知调试】查看关联物品失败:', error);
    ElMessage.error('查看物品详情失败，请稍后重试');
  }
}

// 联系物主
const contactOwner = () => {
  // 这里可以添加跳转到聊天页面的逻辑
  ElMessage.success(`即将跳转到与物品发布者的聊天页面`)
  closeDialog()
  router.push('/chat')
}

// 处理删除
const handleDelete = () => {
  ElMessageBox.confirm('确定要删除这条通知吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteMatchNotification(props.notification.id)
      ElMessage.success('删除成功')
      closeDialog()
      emit('deleted', props.notification.id)
    } catch (error) {
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 用户取消操作
  })
}
</script>

<style scoped>
.match-notification-dialog :deep(.el-dialog__header) {
  padding: 12px 20px;
  margin-right: 0;
  border-bottom: 1px solid #ebeef5;
}

.match-notification-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.match-notification-detail {
  padding: 0;
}

.match-header {
  margin-bottom: 20px;
}

.match-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.match-type-tag {
  font-size: 13px;
}

.similarity-tag {
  font-weight: bold;
}

.item-brief {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.item-name {
  font-weight: bold;
  color: #303133;
}

.item-location {
  color: #606266;
  font-size: 13px;
}

.similarity-radar {
  margin: 15px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.similarity-radar h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #303133;
  font-size: 15px;
  text-align: center;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .match-notification-dialog :deep(.el-dialog) {
    width: 95% !important;
    max-width: 100%;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 10px;
  }
}
</style>
