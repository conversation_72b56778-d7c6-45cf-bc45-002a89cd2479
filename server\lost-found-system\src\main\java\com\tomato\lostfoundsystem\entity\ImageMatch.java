package com.tomato.lostfoundsystem.entity;


import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ImageMatch {
    private Long id;
    private Long lostItemId;
    private Long foundItemId;
    private BigDecimal imageSimilarity;
    private BigDecimal textSimilarity;
    private BigDecimal totalSimilarity;
    private String status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Getters and Setters
}
