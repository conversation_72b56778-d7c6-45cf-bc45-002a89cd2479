package com.tomato.lostfoundsystem.utils;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.dictionary.stopword.CoreStopWordDictionary;
import com.hankcs.hanlp.seg.common.Term;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 关键词提取工具类
 * 使用HanLP提取文本描述中的关键词
 */
public class KeywordExtractor {

    private static final Logger log = LoggerFactory.getLogger(KeywordExtractor.class);

    // 最大关键词数量
    private static final int MAX_KEYWORD_COUNT = 10;

    // 最大关键词总长度（字符数）
    private static final int MAX_KEYWORD_LENGTH = 20;

    /**
     * 从文本中提取关键词，总长度不超过20个字
     *
     * @param itemName 物品名称
     * @param description 物品描述
     * @return 提取的关键词字符串（空格分隔）
     */
    public static String extractKeywords(String itemName, String description) {
        if ((description == null || description.isEmpty()) && (itemName == null || itemName.isEmpty())) {
            return "";
        }

        try {
            List<String> keywordList = new ArrayList<>();

            // 1. 确保物品名称被包含
            if (itemName != null && !itemName.isEmpty()) {
                keywordList.add(itemName);
            }

            if (description == null || description.isEmpty()) {
                return String.join(" ", keywordList);
            }

            // 2. 使用HanLP提取关键词
            List<String> hanlpKeywords = HanLP.extractKeyword(description, MAX_KEYWORD_COUNT);

            log.debug("HanLP提取的关键词: {}", hanlpKeywords);

            // 3. 使用HanLP分词，提取名词、形容词等
            List<Term> termList = HanLP.segment(description);
            List<String> nounAndAdjList = new ArrayList<>();

            for (Term term : termList) {
                String nature = term.nature.toString();
                String word = term.word;

                // 过滤停用词
                if (CoreStopWordDictionary.contains(word)) {
                    continue;
                }

                // 提取名词、形容词、颜色词等
                if ((nature.startsWith("n") || nature.startsWith("a") || nature.equals("bg"))
                        && word.length() >= 2) {
                    nounAndAdjList.add(word);
                }
            }
            log.debug("HanLP分词提取的名词和形容词: {}", nounAndAdjList);

            // 4. 过滤和去重关键词
            Set<String> combinedKeywords = new LinkedHashSet<>();

            // 首先添加物品名称（只添加一次）
            combinedKeywords.addAll(keywordList);  // 物品名称

            // 过滤HanLP关键词，去除与物品名称相似的词
            for (String keyword : hanlpKeywords) {
                boolean shouldAdd = true;

                // 检查是否与已有关键词（特别是物品名称）相似或包含关系
                for (String existingKeyword : combinedKeywords) {
                    if (existingKeyword.contains(keyword) || keyword.contains(existingKeyword)) {
                        shouldAdd = false;
                        break;
                    }
                }

                if (shouldAdd) {
                    combinedKeywords.add(keyword);
                }
            }

            // 过滤名词和形容词，去除与已有关键词相似的词
            for (String word : nounAndAdjList) {
                boolean shouldAdd = true;

                // 检查是否与已有关键词相似或包含关系
                for (String existingKeyword : combinedKeywords) {
                    if (existingKeyword.contains(word) || word.contains(existingKeyword)) {
                        shouldAdd = false;
                        break;
                    }
                }

                if (shouldAdd) {
                    combinedKeywords.add(word);
                }
            }

            // 5. 控制关键词总长度不超过30个字
            List<String> finalKeywords = new ArrayList<>();
            int totalLength = 0;

            for (String keyword : combinedKeywords) {
                if (totalLength + keyword.length() <= MAX_KEYWORD_LENGTH) {
                    finalKeywords.add(keyword);
                    totalLength += keyword.length();
                } else {
                    break;
                }
            }

            // 6. 返回关键词字符串（空格分隔）
            String result = String.join(" ", finalKeywords);

            // 记录详细的关键词信息
            log.info("【关键词提取】原始描述: {}", description);
            log.info("【关键词提取】提取结果: {}", result);
            log.info("【关键词提取】关键词数量: {}, 总字符长度: {}, 关键词列表: {}",
                    finalKeywords.size(), result.length(), finalKeywords);

            // 记录token数量估计（粗略估计，每个汉字约1.5个token）
            int estimatedTokens = (int) (result.length() * 1.5);
            log.info("【关键词提取】估计token数量: {} (CLIP模型限制为77)", estimatedTokens);

            return result;

        } catch (Exception e) {
            log.error("提取关键词时发生错误: {}", e.getMessage(), e);
            return itemName != null ? itemName : "";  // 出错时返回物品名称
        }
    }
}
