<template>
  <div class="profile-notifications-container">
    <div class="page-header">
      <div class="page-header-content">
        <h2>通知中心</h2>
        <div class="header-description">查看您收到的所有系统通知、匹配结果和重要公告</div>
      </div>
      <div class="page-header-actions">
        <el-button type="primary" @click="markAllAsRead" :disabled="!hasUnread">
          <el-icon><Check /></el-icon>
          全部标为已读
        </el-button>
        <el-button type="danger" @click="clearAllNotifications" :disabled="filteredNotifications.length === 0">
          <el-icon><Delete /></el-icon>
          清空通知
        </el-button>
      </div>
    </div>

    <el-card class="notification-card" shadow="hover">
      <div class="notifications-controls">
        <div class="filter-tabs">
          <!-- 状态过滤 -->
          <div class="filter-section">
            <div class="filter-label">状态：</div>
            <el-radio-group v-model="filterStatus" size="small" @change="filterNotifications">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="unread">未读</el-radio-button>
              <el-radio-button label="read">已读</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 类型过滤 -->
          <div class="filter-section">
            <div class="filter-label">类型：</div>
            <el-radio-group v-model="filterType" size="small" @change="filterNotifications">
              <el-radio-button label="all">全部</el-radio-button>
              <el-radio-button label="system">系统</el-radio-button>
              <el-radio-button label="match">匹配</el-radio-button>
              <el-radio-button label="announcement">公告</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="search-section">
          <el-input v-model="searchQuery" placeholder="搜索通知..." clearable @input="filterNotifications">
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <el-empty v-if="filteredNotifications.length === 0" description="暂无通知" />

      <div v-else class="notifications-list">
        <transition-group name="fade-list" tag="div">
          <div
            v-for="notification in filteredNotifications"
            :key="notification.id"
            class="notification-item"
            :class="{
              'unread': !notification.isRead,
              'notification-system': notification.type === 'system',
              'notification-match': notification.type === 'match',
              'notification-announcement': notification.type === 'announcement',
              'notification-audit': notification.type === 'audit',
              'notification-claim': notification.type === 'claim'
            }"
            @click="markAsRead(notification)"
          >
            <div class="notification-left">
              <div class="notification-icon" :class="getNotificationTypeClass(notification.type)">
                <el-icon>
                  <component :is="getNotificationIcon(notification.type)" />
                </el-icon>
              </div>
            </div>

            <div class="notification-content">
              <div class="notification-header">
                <div class="notification-title">
                  <span v-if="notification.title">{{ notification.title }}</span>
                  <span v-else>{{ getNotificationTypeText(notification.type) }}</span>

                  <el-tag
                    v-if="!notification.isRead"
                    type="danger"
                    effect="plain"
                    size="small"
                    class="status-tag"
                  >
                    未读
                  </el-tag>
                </div>
                <div class="notification-time">
                  {{ formatDate(notification.createdAt || notification.time) }}
                </div>
              </div>

              <div class="notification-message">
                {{ notification.message || notification.content }}
              </div>

              <div class="notification-actions">
                <el-button
                  type="primary"
                  size="small"
                  text
                  v-if="!notification.isRead"
                  @click.stop="markAsRead(notification)"
                >
                  标为已读
                </el-button>
                <el-button
                  v-if="notification.type !== 'announcement'"
                  type="danger"
                  size="small"
                  text
                  @click.stop="deleteNotification(notification)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </transition-group>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="filteredNotifications.length > 0">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="totalNotifications"
          layout="total, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 匹配通知详情对话框 -->
    <match-notification-detail
      :notification="currentMatchNotification || {}"
      v-model:visible="matchNotificationDetailVisible"
      @deleted="handleMatchNotificationDeleted"
      append-to-body
      :modal-append-to-body="false"
      :z-index="9999"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Search, Bell, ChatDotRound, Star, Check, Delete, Message, InfoFilled, Warning } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useNotificationStore } from '@/stores/notificationStore'
import { formatDate } from '@/utils/formatters'
import MatchNotificationDetail from '@/components/MatchNotificationDetail.vue'

// 初始化Pinia store
const notificationStore = useNotificationStore()

// 响应式状态
const currentPage = ref(1)
const pageSize = ref(10)
const totalNotifications = ref(0)
const filterStatus = ref('all')
const filterType = ref('all')
const searchQuery = ref('')
const notifications = ref([])

// 匹配通知详情对话框
const matchNotificationDetailVisible = ref(false)
const currentMatchNotification = ref(null)

// 计算属性：过滤后的通知
const filteredNotifications = computed(() => {
  // 首先应用过滤条件
  let filtered = notifications.value.filter(notification => {
    // 状态过滤
    if (filterStatus.value === 'read' && !notification.isRead) return false
    if (filterStatus.value === 'unread' && notification.isRead) return false

    // 类型过滤
    if (filterType.value !== 'all' && notification.type !== filterType.value) return false

    // 搜索过滤
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase()
      const title = (notification.title || '').toLowerCase()
      const message = (notification.message || notification.content || '').toLowerCase()
      return title.includes(query) || message.includes(query)
    }

    return true
  })

  // 应用分页
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value

  // 更新总数
  totalNotifications.value = filtered.length

  // 返回当前页的通知
  return filtered.slice(startIndex, endIndex)
})

// 计算属性：是否有未读通知
const hasUnread = computed(() => {
  return notifications.value.some(notification => !notification.isRead)
})

// 同步通知数据
const syncNotificationsFromStore = () => {
  console.log('🔄 同步通知数据')

  // 合并所有类型的通知
  const allNotifications = [
    ...notificationStore.systemNotifications.map(n => ({
      ...n,
      type: 'system',
      isRead: !!n.isRead // 强制转换为布尔值
    })),
    ...notificationStore.matchNotifications.map(n => ({
      ...n,
      type: 'match',
      isRead: !!n.isRead // 强制转换为布尔值
    })),
    ...notificationStore.announcements.map(n => {
      // 打印调试信息
      console.log(`🔄 公告同步，ID: ${n.id}, 原始isRead:`, n.isRead, `(类型: ${typeof n.isRead})`);

      // 严格处理isRead属性
      let isReadValue = false;
      if (n.isRead === true ||
          n.isRead === 1 ||
          n.isRead === "1" ||
          n.isRead === "true") {
        isReadValue = true;
      }

      console.log(`🔄 公告同步，ID: ${n.id}, 处理后isRead:`, isReadValue, `(类型: ${typeof isReadValue})`);

      return {
      ...n,
      type: 'announcement',
        isRead: isReadValue // 使用严格转换的布尔值
      };
    })
  ]

  // 按时间降序排序
  allNotifications.sort((a, b) => {
    const timeA = new Date(a.createdAt || a.time || 0)
    const timeB = new Date(b.createdAt || b.time || 0)
    return timeB - timeA
  })

  notifications.value = allNotifications
  console.log('🔔🔔🔔 通知列表同步完成，总数:', allNotifications.length,
              '已读:', allNotifications.filter(n => n.isRead).length,
              '未读:', allNotifications.filter(n => !n.isRead).length)
}

// 标记通知为已读
const markAsRead = async (notification) => {
  try {
    console.log('🔔🔔🔔 处理通知点击:', notification.id, '类型:', notification.type)

    // 如果是未读通知，标记为已读
    if (!notification.isRead) {
      console.log('🔔🔔🔔 标记通知为已读:', notification.id, '类型:', notification.type)

      // 使用Store方法标记为已读
      if (notification.type === 'match') {
        console.log('🔔🔔🔔 标记匹配通知为已读')
        await notificationStore.markMatchAsRead(notification.id)
      } else if (notification.type === 'announcement') {
        console.log('🔔🔔🔔 标记公告为已读')
        const result = await notificationStore.markAnnouncementRead(notification.id)
        console.log('🔔🔔🔔 标记公告已读结果:', result)
      } else {
        console.log('🔔🔔🔔 标记系统通知为已读')
        await notificationStore.markSystemAsRead(notification.id)
      }

      // 更新本地通知状态 - 确保直接使用isRead属性
      notification.isRead = true
      ElMessage.success('已标记为已读')

      // 只更新未读计数，不重新获取通知列表或同步状态
      setTimeout(() => {
        notificationStore.fetchAllUnreadCounts()
        // 不再调用syncNotificationsFromStore，避免循环
      }, 1000)
    }

    // 根据通知类型显示不同的详情
    if (notification.type === 'match') {
      // 创建一个完整的通知对象副本，确保所有必要的字段都存在
      const completeNotification = {
        ...notification,
        // 确保这些关键字段存在
        id: notification.id,
        itemId: notification.itemId,
        itemType: notification.itemType,
        similarity: notification.similarity || 0,
        matchType: notification.matchType || 'UNKNOWN',
        // 如果有相似度详情，也包含它们
        imageToImageSimilarity: notification.imageToImageSimilarity,
        imageToTextSimilarity: notification.imageToTextSimilarity,
        textToImageSimilarity: notification.textToImageSimilarity,
        textToTextSimilarity: notification.textToTextSimilarity
      };

      console.log('【匹配通知调试】个人中心-创建完整通知对象:', completeNotification);

      // 显示匹配通知详情对话框
      currentMatchNotification.value = completeNotification;
      console.log('【匹配通知调试】个人中心-已设置当前匹配通知:', currentMatchNotification.value);

      // 确保在DOM更新后再显示对话框
      setTimeout(() => {
        // 显示对话框
        matchNotificationDetailVisible.value = true;
        console.log('【匹配通知调试】个人中心-已设置对话框可见性为true:', matchNotificationDetailVisible.value);

        // 强制重新渲染对话框
        document.body.style.overflow = 'hidden';
        setTimeout(() => {
          document.body.style.overflow = '';
        }, 10);

        // 记录对话框状态
        console.log('【匹配通知调试】个人中心-对话框状态:', {
          visible: matchNotificationDetailVisible.value,
          notification: currentMatchNotification.value,
          bodyHasDialog: document.querySelector('.el-dialog__wrapper') !== null
        });
      }, 50);
    } else if (notification.type === 'announcement') {
      // 显示公告详情
      ElMessageBox.alert(notification.content || notification.message, notification.title, {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true
      })
    } else {
      // 显示系统通知详情
      ElMessageBox.alert(notification.content || notification.message, notification.title, {
        confirmButtonText: '确定'
      })
    }
  } catch (error) {
    console.error('🔔🔔🔔 处理通知点击失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    console.log('🔔🔔🔔 标记所有通知为已读')
    const unreadNotifications = notifications.value.filter(item => !item.isRead)

    if (unreadNotifications.length === 0) {
      ElMessage.info('没有未读通知')
      return
    }

    // 使用Store方法标记全部为已读
    await notificationStore.markAllAsRead()

    // 更新本地通知状态
    notifications.value.forEach(notification => {
      notification.isRead = true
    })

    // 不再强制同步，避免循环
    // 只更新未读计数
    setTimeout(() => {
      notificationStore.fetchAllUnreadCounts()
    }, 500)

    ElMessage.success('已将所有通知标记为已读')
  } catch (error) {
    console.error('🔔🔔🔔 标记所有通知为已读失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 删除单个通知
const deleteNotification = (notification) => {
  // 确保公告类型的通知不能被删除
  if (notification.type === 'announcement') {
    ElMessage.warning('系统公告只能由管理员删除')
    return
  }

  ElMessageBox.confirm(
    '确定要删除这条通知吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      console.log('🔔🔔🔔 删除通知:', notification.id)

      if (notification.type === 'system') {
        await notificationStore.deleteSystemNotification(notification.id)
      } else if (notification.type === 'match') {
        await notificationStore.deleteMatchNotification(notification.id)
      }
      // 已移除公告删除逻辑

      // 从本地列表中移除
      const index = notifications.value.findIndex(item => item.id === notification.id)
      if (index !== -1) {
        notifications.value.splice(index, 1)
        // 不再调用syncNotificationsFromStore，避免循环
        ElMessage.success('删除成功')
      }
    } catch (error) {
      console.error('🔔🔔🔔 删除通知失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }).catch(() => {})
}

// 清空所有通知
const clearAllNotifications = () => {
  ElMessageBox.confirm(
    '确定要清空所有通知吗？此操作不可恢复！\n注意：系统公告将保留，只能由管理员删除。',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await notificationStore.clearAllNotifications()

      // 保留公告类型的通知
      const announcementNotifications = notifications.value.filter(n => n.type === 'announcement')
      notifications.value = announcementNotifications
      totalNotifications.value = announcementNotifications.length

      ElMessage.success('已清空通知（系统公告已保留）')
    } catch (error) {
      console.error('🔔🔔🔔 清空通知失败:', error)
      ElMessage.error('操作失败，请稍后重试')
    }
  }).catch(() => {})
}

// 过滤通知
const filterNotifications = () => {
  // 重置分页到第一页
  currentPage.value = 1
  // 过滤逻辑在computed中实现
}

// 分页处理
const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 获取通知类型的图标
const getNotificationIcon = (type) => {
  switch (type) {
    case 'system': return InfoFilled
    case 'match': return Star
    case 'announcement': return Bell
    case 'audit': return Warning
    case 'claim': return ChatDotRound
    default: return Message
  }
}

// 获取通知类型的CSS类名
const getNotificationTypeClass = (type) => {
  return `notification-icon-${type || 'default'}`
}

// 获取通知类型显示文本
const getNotificationTypeText = (type) => {
  switch (type) {
    case 'system': return '系统通知'
    case 'match': return '匹配通知'
    case 'announcement': return '系统公告'
    case 'audit': return '审核通知'
    case 'claim': return '认领通知'
    default: return '通知'
  }
}

// 处理匹配通知删除
const handleMatchNotificationDeleted = (notificationId) => {
  console.log('🔔🔔🔔 匹配通知已删除:', notificationId)

  // 从列表中移除该通知
  const index = notifications.value.findIndex(n => n.id === notificationId)
  if (index !== -1) {
    notifications.value.splice(index, 1)
    ElMessage.success('通知已删除')
  }
}

// 生命周期钩子
onMounted(async () => {
  // 加载通知数据
  await notificationStore.fetchAllNotifications(true)
  syncNotificationsFromStore()
})

// 使用watch替代watchEffect，并添加防抖

// 创建一个防抖函数
const debounce = (fn, delay) => {
  let timer = null
  return function() {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, arguments)
    }, delay)
  }
}

// 防抖版本的同步函数
const debouncedSync = debounce(() => {
  console.log('🔄 防抖后同步通知数据')
  syncNotificationsFromStore()
}, 500)

// 使用watch替代watchEffect，并添加选项避免立即执行
watch(
  () => [
    notificationStore.systemNotifications.length,
    notificationStore.matchNotifications.length,
    notificationStore.announcements.length
  ],
  () => {
    debouncedSync()
  },
  { deep: true }
)
</script>

<style scoped>
.profile-notifications-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header-content h2 {
  margin: 0;
  font-size: 22px;
  color: var(--el-color-primary);
}

.header-description {
  margin-top: 5px;
  color: #606266;
  font-size: 14px;
}

.notification-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.notifications-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.filter-tabs {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-label {
  font-weight: 500;
  color: #606266;
}

.search-section {
  width: 250px;
}

.notifications-list {
  margin-top: 10px;
}

.notification-item {
  display: flex;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 8px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.notification-item:hover {
  background-color: #f0f7ff;
}

.notification-item.unread {
  background-color: #eef5fe;
  border-left-color: var(--el-color-primary);
}

.notification-left {
  margin-right: 15px;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.notification-icon-system {
  background-color: #e1f3ff;
  color: #409EFF;
}

.notification-icon-match {
  background-color: #ffefd5;
  color: #ff9900;
}

.notification-icon-announcement {
  background-color: #f0f9eb;
  color: #67c23a;
}

.notification-icon-audit {
  background-color: #fef0f0;
  color: #f56c6c;
}

.notification-icon-claim {
  background-color: #f5f7fa;
  color: #909399;
}

.notification-icon-default {
  background-color: #ebeef5;
  color: #606266;
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.notification-title {
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification-time {
  color: #909399;
  font-size: 13px;
}

.notification-message {
  margin-bottom: 10px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.notification-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 通知类型颜色 */
.notification-system {
  background-color: #f0faff;
}

.notification-match {
  background-color: #fffbf0;
}

.notification-announcement {
  background-color: #f0f9eb;
}

.notification-audit {
  background-color: #fff9f9;
}

.notification-claim {
  background-color: #f5f7fa;
}

/* 标签样式 */
.status-tag {
  font-size: 12px;
  height: 20px;
  line-height: 18px;
  margin-left: 8px;
}

/* 过渡动画 */
.fade-list-enter-active,
.fade-list-leave-active {
  transition: all 0.5s ease;
}

.fade-list-enter-from,
.fade-list-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 确保对话框显示在最顶层 */
:deep(.el-dialog) {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin-top: 0 !important;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

:deep(.el-dialog__wrapper) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  z-index: 9999 !important;
}

:deep(.v-modal) {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  background: #000;
  z-index: 9998 !important;
}
</style>
