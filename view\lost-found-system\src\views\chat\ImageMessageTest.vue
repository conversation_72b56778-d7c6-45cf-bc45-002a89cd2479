<template>
  <div class="image-message-test">
    <h2>图片消息测试</h2>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>发送图片消息</span>
        </div>
      </template>

      <div class="form-container">
        <el-form :model="form" label-width="120px">
          <el-form-item label="发送者ID">
            <el-input v-model.number="form.senderId" type="number" />
          </el-form-item>

          <el-form-item label="接收者ID">
            <el-input v-model.number="form.receiverId" type="number" />
          </el-form-item>

          <el-form-item label="消息内容">
            <el-input v-model="form.message" type="textarea" :rows="2" placeholder="可选的图片描述" />
          </el-form-item>

          <el-form-item label="图片">
            <div class="upload-container">
              <el-upload
                action="#"
                list-type="picture-card"
                :auto-upload="false"
                :limit="1"
                :on-change="handleImageChange"
                :on-remove="handleImageRemove"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="sendImageMessage" :loading="sending">发送图片</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card class="test-card" v-if="result">
      <template #header>
        <div class="card-header">
          <span>发送结果</span>
        </div>
      </template>

      <div class="result-container">
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
    </el-card>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>接收消息测试</span>
        </div>
      </template>

      <div class="websocket-container">
        <div class="websocket-status">
          <span>WebSocket状态: </span>
          <el-tag :type="connected ? 'success' : 'danger'">{{ connected ? '已连接' : '未连接' }}</el-tag>
          <el-button size="small" @click="connect" :disabled="connected">连接</el-button>
          <el-button size="small" @click="disconnect" :disabled="!connected">断开</el-button>
        </div>

        <div class="messages-container">
          <h3>接收到的消息</h3>
          <div v-if="messages.length === 0" class="no-messages">
            暂无消息
          </div>
          <div v-else class="message-list">
            <div v-for="(msg, index) in messages" :key="index" class="message-item">
              <div class="message-header">
                <span class="message-type">{{ msg.messageType }}</span>
                <span class="message-time">{{ formatTime(msg.timestamp) }}</span>
              </div>
              <div class="message-content">
                <template v-if="msg.messageType === 'IMAGE'">
                  <div class="image-message">
                    <el-image
                      :src="msg.fileUrl"
                      :preview-src-list="[msg.fileUrl]"
                      fit="cover"
                      class="message-image"
                    />
                    <div class="image-text">{{ msg.message }}</div>
                  </div>
                </template>
                <template v-else>
                  {{ msg.message }}
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import SockJS from 'sockjs-client'
import { Client } from '@stomp/stompjs'
import { sendPrivateMessage } from '@/api/chat'

// 表单数据
const form = ref({
  senderId: null,
  receiverId: null,
  message: '',
  messageType: 'IMAGE'
})

// 选中的图片
const selectedImage = ref(null)

// 发送状态
const sending = ref(false)

// 发送结果
const result = ref(null)

// WebSocket状态
const connected = ref(false)
const stompClient = ref(null)
const messages = ref([])

// 处理图片选择
const handleImageChange = (file) => {
  selectedImage.value = file.raw
}

// 处理图片移除
const handleImageRemove = () => {
  selectedImage.value = null
}

// 发送图片消息
const sendImageMessage = async () => {
  if (!form.value.senderId || !form.value.receiverId) {
    ElMessage.warning('请填写发送者ID和接收者ID')
    return
  }

  if (!selectedImage.value) {
    ElMessage.warning('请选择一张图片')
    return
  }

  try {
    sending.value = true

    // 创建FormData对象
    const formData = new FormData()
    formData.append('senderId', form.value.senderId)
    formData.append('receiverId', form.value.receiverId)
    formData.append('message', form.value.message)
    formData.append('messageType', 'IMAGE')
    formData.append('file', selectedImage.value)

    // 使用chat.js中的sendPrivateMessage函数
    console.log('使用chat.js中的sendPrivateMessage函数发送图片消息');

    // 发送请求
    const response = await sendPrivateMessage(formData)

    result.value = response

    if (response && response.code === 200) {
      ElMessage.success('图片发送成功')
    } else {
      ElMessage.error(`图片发送失败: ${response?.message || '未知错误'}`)
    }
  } catch (error) {
    console.error('发送图片消息出错:', error)
    ElMessage.error(`发送出错: ${error.message}`)
  } finally {
    sending.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    senderId: null,
    receiverId: null,
    message: '',
    messageType: 'IMAGE'
  }
  selectedImage.value = null
  result.value = null
}

// 连接WebSocket
const connect = () => {
  try {
    // 获取Token
    const token = localStorage.getItem('token')
    if (!token) {
      ElMessage.error('未找到用户Token，无法连接')
      return
    }

    // 创建SockJS实例
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081'
    const wsUrl = `${apiBaseUrl}/ws?token=${encodeURIComponent(token)}&t=${Date.now()}`
    const socket = new SockJS(wsUrl)

    // 创建STOMP客户端
    stompClient.value = new Client({
      webSocketFactory: () => socket,
      connectHeaders: {
        Authorization: `Bearer ${token}`
      },
      debug: (str) => {
        console.log(str)
      },
      reconnectDelay: 5000,
      heartbeatIncoming: 25000,
      heartbeatOutgoing: 25000
    })

    // 连接成功回调
    stompClient.value.onConnect = (frame) => {
      console.log('WebSocket连接成功:', frame)
      connected.value = true

      // 订阅私人消息
      stompClient.value.subscribe('/user/queue/private', message => {
        console.log('收到消息:', message)

        try {
          const data = JSON.parse(message.body)
          messages.value.unshift(data)
        } catch (error) {
          console.error('解析消息失败:', error)
        }
      })
    }

    // 连接错误回调
    stompClient.value.onStompError = (frame) => {
      console.error('WebSocket连接错误:', frame)
      connected.value = false
    }

    // 连接断开回调
    stompClient.value.onWebSocketClose = (event) => {
      console.log('WebSocket连接断开:', event)
      connected.value = false
    }

    // 激活连接
    stompClient.value.activate()
  } catch (error) {
    console.error('WebSocket连接失败:', error)
    ElMessage.error(`WebSocket连接失败: ${error.message}`)
    connected.value = false
  }
}

// 断开WebSocket连接
const disconnect = () => {
  try {
    if (stompClient.value) {
      stompClient.value.deactivate()
      console.log('WebSocket连接已断开')
    }
    connected.value = false
  } catch (error) {
    console.error('断开连接失败:', error)
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  return date.toLocaleString()
}

// 生命周期钩子
onMounted(() => {
  // 自动填充当前用户ID
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  if (userInfo.id) {
    form.value.senderId = userInfo.id
  }
})

onUnmounted(() => {
  disconnect()
})
</script>

<style scoped>
.image-message-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-container {
  padding: 10px;
}

.result-container {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}

.websocket-container {
  padding: 10px;
}

.websocket-status {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.messages-container {
  margin-top: 15px;
}

.no-messages {
  color: #909399;
  text-align: center;
  padding: 20px;
}

.message-list {
  max-height: 400px;
  overflow-y: auto;
}

.message-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 10px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.message-type {
  font-weight: bold;
  color: #409eff;
}

.message-time {
  color: #909399;
}

.message-content {
  word-break: break-all;
}

.image-message {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.message-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
  margin-bottom: 5px;
}

.image-text {
  color: #606266;
  font-size: 14px;
}

.upload-container {
  display: flex;
  align-items: center;
  min-height: 100px;
}
</style>
