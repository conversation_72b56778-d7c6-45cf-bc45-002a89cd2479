<template>
  <div class="lost-items-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h2>失物信息</h2>
      <div class="title-line"></div>
    </div>

    <div class="page-header">
      <div class="search-area">
        <div class="search-form">
          <div class="input-group">
            <el-input
              v-model="searchForm.keyword"
              placeholder="物品名称/描述"
              clearable
              @keyup.enter="handleSearch"
            />
            <el-input
              v-model="searchForm.lostLocation"
              placeholder="丢失地点"
              clearable
              @keyup.enter="handleSearch"
            />
            <el-select v-model="searchForm.status" placeholder="状态" clearable>
              <el-option label="未找回" value="LOST" />
              <el-option label="已找回" value="FOUND" />
            </el-select>
          </div>

          <div class="time-group">
            <el-select v-model="searchForm.timeFilterType" class="time-type">
              <el-option label="丢失时间" value="lostTime" />
              <el-option label="发布时间" value="createdAt" />
            </el-select>
            <el-select v-model="timeRange" class="time-range" @change="handleTimeRangeChange">
              <el-option label="请选择" value="" />
              <el-option label="今天" value="today" />
              <el-option label="昨天" value="yesterday" />
              <el-option label="最近三天" value="lastThreeDays" />
              <el-option label="最近一周" value="lastWeek" />
              <el-option label="最近一月" value="lastMonth" />
              <el-option label="自定义" value="custom" />
            </el-select>
            <el-date-picker
              v-if="timeRange === 'custom'"
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DDTHH:mm:ss"
              :default-time="[
                new Date(2000, 1, 1, 0, 0, 0),
                new Date(2000, 1, 1, 23, 59, 59)
              ]"
              @change="handleDateRangeChange"
            />
          </div>
        </div>

        <div class="button-group">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="router.push('/lost-items/publish')">
            <el-icon><Plus /></el-icon>
            发布失物
          </el-button>
        </div>
      </div>

      <div class="view-controls">
        <div class="active-filters" v-if="hasActiveFilters">
          <span class="filter-label">当前筛选：</span>
          <el-tag
            v-if="searchForm.keyword"
            closable
            @close="clearKeyword"
          >关键词: {{ searchForm.keyword }}</el-tag>

          <el-tag
            v-if="searchForm.status"
            closable
            @close="clearStatus"
          >状态: {{ searchForm.status === 'LOST' ? '未找回' : '已找回' }}</el-tag>
          <el-tag
            v-if="activeTimeFilter"
            closable
            @close="clearTimeFilter"
          >{{ activeTimeFilter }}</el-tag>
        </div>

        <div class="view-switcher">
          <el-radio-group v-model="viewMode" size="default">
            <el-radio-button value="list">
              <el-icon><List /></el-icon>
              列表视图
            </el-radio-button>
            <el-radio-button value="card">
              <el-icon><Grid /></el-icon>
              卡片视图
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>

    <!-- 空状态提示 -->
    <div
      v-if="!loading && (!lostItems || lostItems.length === 0)"
      class="empty-state"
    >
      <el-empty :description="getEmptyDescription">
        <template #image>
          <div class="empty-icon-container">
            <el-icon :size="64" class="empty-icon"><Box /></el-icon>
          </div>
        </template>
        <template #default>
          <p class="empty-hint">您可以尝试调整筛选条件或发布一条新的失物信息</p>
          <div class="empty-actions">
            <el-button type="primary" @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置筛选
            </el-button>
            <el-button type="success" @click="router.push('/lost-items/publish')">
              <el-icon><Plus /></el-icon>
              发布失物
            </el-button>
          </div>
        </template>
      </el-empty>
    </div>

    <!-- 列表内容保持不变 -->
    <div v-else>
      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="list-view" v-loading="loading">
        <el-table :data="lostItems" border style="width: 100%">
          <el-table-column label="图片" width="120">
            <template #default="{ row }">
              <el-image
                class="lost-item-image"
                :src="row.imageUrl || 'default-image.jpg'"
                fit="cover"
                :preview-src-list="row.imageUrl ? [row.imageUrl] : []"
                :initial-index="0"
                preview-teleported
                @click="row.imageUrl && $event.stopPropagation()"
              >
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="itemName" label="物品名称" min-width="120" />
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="lostLocation" label="丢失地点" min-width="120" />
          <el-table-column prop="lostTime" label="丢失时间" min-width="160" />
          <el-table-column prop="username" label="发布人" min-width="100">
            <template #default="{ row }">
              {{ row.username || '匿名用户' }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">{{ row.status === 'LOST' ? '未找回' : '已找回' }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="showDetail(row)">
                <el-tooltip content="查看详情" placement="top">
                  <el-icon><View /></el-icon>
                </el-tooltip>
              </el-button>
              <el-button
                v-if="row.userId === currentUserId && row.status === 'LOST'"
                type="primary"
                link
                @click="showEditDialog(row)"
              >
                <el-tooltip content="编辑" placement="top">
                  <el-icon><Edit /></el-icon>
                </el-tooltip>
              </el-button>
              <el-button
                v-if="row.userId === currentUserId && row.status === 'LOST'"
                type="danger"
                link
                @click="handleDelete(row)"
              >
                <el-tooltip content="删除" placement="top">
                  <el-icon><Delete /></el-icon>
                </el-tooltip>
              </el-button>

            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view" v-loading="loading">
        <el-row :gutter="16">
          <el-col v-for="item in lostItems" :key="item.id" :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-card class="lost-item-card" :body-style="{ padding: '0px' }">
              <div class="card-image">
                <el-image
                  :src="item.imageUrl || 'default-image.jpg'"
                  fit="contain"
                  :preview-src-list="item.imageUrl ? [item.imageUrl] : []"
                  :initial-index="0"
                  preview-teleported
                  @click="item.imageUrl && $event.stopPropagation()"
                >
                  <template #error>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div class="card-status">
                  <el-tag :type="getStatusType(item.status)" size="small">{{ item.status === 'LOST' ? '未找回' : '已找回' }}</el-tag>
                </div>
              </div>
              <div class="card-content">
                <h3 class="card-title text-ellipsis">{{ item.itemName }}</h3>
                <p class="card-desc">{{ item.description }}</p>
                <div class="card-info">
                  <span class="info-item text-ellipsis">
                    <el-icon><Location /></el-icon>
                    {{ item.lostLocation }}
                  </span>
                  <span class="info-item text-ellipsis">
                    <el-icon><Timer /></el-icon>
                    {{ item.lostTime }}
                  </span>
                  <span class="info-item text-ellipsis user-info">
                    <el-avatar :size="20" :src="item.avatar" class="user-avatar">
                      {{ item.username ? item.username.charAt(0).toUpperCase() : 'U' }}
                    </el-avatar>
                    {{ item.username || '匿名用户' }}
                  </span>
                </div>
                <div class="card-actions">
                  <el-button type="primary" link @click="showDetail(item)">
                    <el-tooltip content="查看详情" placement="top">
                      <el-icon><View /></el-icon>
                    </el-tooltip>
                  </el-button>
                  <el-button
                    v-if="item.userId === currentUserId && item.status === 'LOST'"
                    type="primary"
                    link
                    @click="showEditDialog(item)"
                  >
                    <el-tooltip content="编辑" placement="top">
                      <el-icon><Edit /></el-icon>
                    </el-tooltip>
                  </el-button>
                  <el-button
                    v-if="item.userId === currentUserId && item.status === 'LOST'"
                    type="danger"
                    link
                    @click="handleDelete(item)"
                  >
                    <el-tooltip content="删除" placement="top">
                      <el-icon><Delete /></el-icon>
                    </el-tooltip>
                  </el-button>

                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[4, 8, 12, 16]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  List,
  Grid,
  Location,
  Timer,
  Search,
  Refresh,
  Box,
  Picture,
  View,
  Edit,
  Delete,
  Check,
  ZoomIn,
  User
} from '@element-plus/icons-vue'
import {
  getLostItems,
  deleteLostItem
} from '../../api/lost'
import { useUserStore } from '../../stores'
import { useAuth } from '../../composables/useAuth'
import { useRouter } from 'vue-router'

const userStore = useUserStore()
const currentUserId = computed(() => {
  if (userStore.userInfo && userStore.userInfo.id) {
    console.log('计算 currentUserId:', {
      userInfo: userStore.userInfo,
      id: userStore.userInfo?.id,
      store: userStore
    })
  }
  return userStore.userInfo?.id
})

// 获取权限检查方法
const { canPerformAction } = useAuth()

// 视图模式
const viewMode = ref('list')

// 数据状态
const loading = ref(false)
const lostItems = ref([])
const total = ref(0)
const totalPages = ref(0)
const currentPage = ref(1)
const pageSize = ref(4)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  lostLocation: '',
  status: '',
  timeFilterType: 'lostTime',
  startDate: '',
  endDate: ''
})

// 时间范围
const timeRange = ref('lastWeek')
const dateRange = ref(null)

// 计算属性：是否有激活的筛选条件
const hasActiveFilters = computed(() => {
  return searchForm.keyword ||
         searchForm.lostLocation ||
         searchForm.status ||
         timeRange.value
})

// 计算属性：当前激活的时间筛选器显示文本
const activeTimeFilter = computed(() => {
  if (!timeRange.value) return ''
  const timeRangeMap = {
    today: '今天',
    yesterday: '昨天',
    threeDays: '最近三天',
    lastWeek: '最近一周',
    lastMonth: '最近一月',
    custom: `${searchForm.startDate} 至 ${searchForm.endDate}`
  }
  return `时间: ${timeRangeMap[timeRange.value] || ''}`
})

// 清除筛选条件的方法
const clearKeyword = () => searchForm.keyword = ''
const clearStatus = () => searchForm.status = ''
const clearTimeFilter = () => {
  timeRange.value = ''
  dateRange.value = null
  searchForm.startDate = ''
  searchForm.endDate = ''
}

// 处理日期范围变化
const handleDateRangeChange = (value) => {
  if (value) {
    searchForm.timeRange = 'custom'
    searchForm.startDate = value[0]
    searchForm.endDate = value[1]

    console.log('设置自定义时间范围：', {
      startDate: searchForm.startDate,
      endDate: searchForm.endDate
    })
  } else {
    // 如果清空了日期范围，也清空开始和结束时间
    searchForm.startDate = ''
    searchForm.endDate = ''
  }
}

// 处理时间范围变化
const handleTimeRangeChange = (value) => {
  if (value === 'custom') {
    // 如果是自定义，不设置时间范围，等待日期选择器的值
    return
  }

  if (value) {
    searchForm.timeRange = value
    const startDate = new Date()
    const endDate = new Date()

    switch (value) {
      case 'today':
        startDate.setHours(0, 0, 0, 0)
        endDate.setHours(23, 59, 59, 999)
        break
      case 'yesterday':
        startDate.setDate(startDate.getDate() - 1)
        startDate.setHours(0, 0, 0, 0)
        endDate.setDate(endDate.getDate() - 1)
        endDate.setHours(23, 59, 59, 999)
        break
      case 'lastThreeDays':
        startDate.setDate(startDate.getDate() - 3)
        startDate.setHours(0, 0, 0, 0)
        endDate.setHours(23, 59, 59, 999)
        break
      case 'lastWeek':
        startDate.setDate(startDate.getDate() - 7)
        startDate.setHours(0, 0, 0, 0)
        endDate.setHours(23, 59, 59, 999)
        break
      case 'lastMonth':
        startDate.setMonth(startDate.getMonth() - 1)
        startDate.setHours(0, 0, 0, 0)
        endDate.setHours(23, 59, 59, 999)
        break
    }

    searchForm.startDate = startDate.toISOString().split('.')[0]
    searchForm.endDate = endDate.toISOString().split('.')[0]

    console.log('设置时间范围：', {
      range: value,
      startDate: searchForm.startDate,
      endDate: searchForm.endDate
    })
  } else {
    // 如果清空了时间范围，也清空开始和结束时间
    searchForm.startDate = ''
    searchForm.endDate = ''
  }
}

// 搜索方法
const handleSearch = async () => {
  try {
    // 重置分页到第一页
    currentPage.value = 1

    loading.value = true

    // 如果没有输入任何筛选条件，仅传递分页参数
    // 后端会默认返回一周内的数据
    const params = {
      ...searchForm,
      page: currentPage.value,
      size: pageSize.value
    }

    console.log('发送搜索请求，参数：', params)
    const res = await getLostItems(params)
    console.log('搜索结果：', res)

    // 处理响应数据
    if (res.code === 200) {
      if (!res.data) {
        // 当data为null时，显示后端返回的message
        ElMessage.info({ message: res.message || '暂无符合条件的数据', duration: 2000 })
        lostItems.value = []
        total.value = 0
        totalPages.value = 0
        return
      }

      // 处理数据格式
      const { data, total: totalCount, totalPages: totalPagesCount } = res.data
      const items = Array.isArray(data) ? data : []
      lostItems.value = items.map(item => ({
        ...item,
        status: item.status,
        location: item.lostLocation,
        images: item.imageUrl ? [item.imageUrl] : [],
        createTime: item.createdAt
      }))
      total.value = totalCount || 0  // 设置总记录数，确保有默认值
      totalPages.value = totalPagesCount || 0  // 设置总页数，确保有默认值
      console.log('更新列表数据：', {
        items: lostItems.value.length,
        total: total.value,
        pages: totalPages.value
      })

      // 修改搜索成功提示，显示总记录数
      ElMessage.success({ message: `搜索成功，共找到 ${total.value} 条记录`, duration: 2000 })
    } else {
      console.warn('获取失物列表失败：', res)
      // 根据不同的状态码显示不同的提示
      if (res.code === 400) {
        // 对于400状态码，使用info类型提示，因为这通常表示没有查询到数据
        ElMessage.info({ message: res.message || '暂无符合条件的数据', duration: 2000 })
      } else {
        // 其他错误状态码使用error类型提示
        ElMessage.error({ message: res.message || '获取失物列表失败', duration: 2000 })
      }
      // 清空数据
      lostItems.value = []
      total.value = 0
      totalPages.value = 0
    }
  } catch (error) {
    // 如果error对象中包含response，说明是后端返回的错误
    if (error.response) {
      console.error('搜索失败，后端返回：', error.response)
      ElMessage.info({ message: error.response.data?.message || '暂无符合条件的数据', duration: 2000 })
    } else {
      console.error('搜索失败：', error)
      ElMessage.error({ message: '搜索失败，请稍后重试', duration: 2000 })
    }
    lostItems.value = []
    total.value = 0
    totalPages.value = 0
  } finally {
    loading.value = false
  }
}

// 重置方法
const handleReset = () => {
  // 仅保留必要的默认值，其他全部清空
  searchForm.keyword = ''
  searchForm.lostLocation = ''
  searchForm.status = ''
  searchForm.timeFilterType = 'lostTime'
  timeRange.value = 'lastWeek'
  dateRange.value = null

  // 设置最近一周的时间范围
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 7)
  startDate.setHours(0, 0, 0, 0)

  const endDate = new Date()
  endDate.setHours(23, 59, 59, 999)

  searchForm.startDate = startDate.toISOString().split('.')[0]
  searchForm.endDate = endDate.toISOString().split('.')[0]

  // 重置页码
  currentPage.value = 1

  console.log('重置筛选条件，准备获取数据')

  // 直接调用API，传递时间范围参数
  loading.value = true

  getLostItems({
    page: 1,
    size: pageSize.value,
    timeFilterType: 'lostTime',
    startDate: searchForm.startDate,
    endDate: searchForm.endDate
  })
    .then(res => {
      console.log('重置后获取数据成功：', res)
      if (res.code === 200) {
        // 处理数据格式
        const { data, total: totalCount, totalPages: totalPagesCount } = res.data
        const items = Array.isArray(data) ? data : []
        lostItems.value = items.map(item => ({
          ...item,
          status: item.status,
          location: item.lostLocation,
          images: item.imageUrl ? [item.imageUrl] : [],
          createTime: item.createdAt
        }))
        total.value = totalCount || 0  // 设置总记录数，确保有默认值
        totalPages.value = totalPagesCount || 0  // 设置总页数，确保有默认值

        // 添加重置成功提示
        ElMessage.success({ message: `重置成功，显示最近一周的 ${items.length} 条记录`, duration: 2000 })
      } else {
        ElMessage.error({ message: res.message || '获取数据失败', duration: 2000 })
      }
    })
    .catch(error => {
      console.error('重置后获取数据失败：', error)
      ElMessage.error({ message: '获取数据失败', duration: 2000 })
      lostItems.value = []
      total.value = 0
    })
    .finally(() => {
      loading.value = false
    })
}

// 获取失物列表
const fetchLostItems = async () => {
  try {
    loading.value = true

    // 如果没有输入任何筛选条件，仅传递分页参数
    // 后端会默认返回一周内的数据
    const params = {
      ...searchForm,
      page: currentPage.value,
      size: pageSize.value
    }

    console.log('获取失物列表，参数：', params)
    const res = await getLostItems(params)
    console.log('获取失物列表响应：', res)

    if (res.code === 200) {
      // 处理数据格式
      const { data, total: totalCount, totalPages: totalPagesCount } = res.data
      const items = Array.isArray(data) ? data : []
      lostItems.value = items.map(item => ({
        ...item,
        status: item.status,
        location: item.lostLocation,
        images: item.imageUrl ? [item.imageUrl] : [],
        createTime: item.createdAt
      }))
      total.value = totalCount || 0  // 设置总记录数
      totalPages.value = totalPagesCount || 0  // 设置总页数
      console.log('更新列表数据：', {
        items: lostItems.value.length,
        total: total.value,
        pages: totalPages.value
      })
    } else {
      console.warn('获取失物列表失败：', res.message)
      ElMessage.error({ message: res.message || '获取失物列表失败', duration: 2000 })
    }
  } catch (error) {
    console.error('获取失物列表异常：', error)
    ElMessage.error({ message: '获取失物列表失败', duration: 2000 })
  } finally {
    loading.value = false
  }
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchLostItems()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchLostItems()
}

// 查看详情
const showDetail = (item) => {
  router.push(`/lost-items/detail/${item.id}`)
}

// 编辑失物
const showEditDialog = (item) => {
  router.push(`/lost-items/edit/${item.id}`)
}

// 处理删除
const handleDelete = async (item) => {
  console.log('删除物品：', {
    item,
    currentUserId: currentUserId.value,
    userInfo: userStore.userInfo,
    isOwner: item.userId === currentUserId.value
  })
  try {
    await ElMessageBox.confirm(
      '确定要删除这条失物信息吗？此操作不可恢复',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteLostItem(item.id)
    ElMessage.success({ message: '删除成功', duration: 2000 })
    fetchLostItems() // 重新加载列表
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error({ message: '删除失败：' + (error.message || '未知错误'), duration: 2000 })
    }
  }
}



// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'LOST':
      return 'danger';  // 未找回用红色
    case 'FOUND':
      return 'success';  // 已找回用绿色
    default:
      return 'info';
  }
}

// 获取空状态描述
const getEmptyDescription = computed(() => {
  if (hasActiveFilters.value) {
    const timeDesc = searchForm.timeRange === 'custom'
      ? `${searchForm.startDate} 至 ${searchForm.endDate}`
      : {
          today: '今天',
          yesterday: '昨天',
          threeDays: '最近三天',
          lastWeek: '最近一周',
          lastMonth: '最近一个月'
        }[searchForm.timeRange]

    return `在${timeDesc}内没有找到相关的失物信息`
  }
  return '暂无失物信息'
})

// 初始加载
onMounted(() => {
  console.log('组件挂载，初始化数据')
  console.log('当前用户ID：', currentUserId.value)

  // 设置默认的时间范围为最近一周
  handleTimeRangeChange('lastWeek')

  fetchLostItems()
})

const router = useRouter()
</script>

<style scoped>
.lost-items-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面标题样式 - 与拾物信息页面一致 */
.page-title {
  text-align: center;
  margin-bottom: 2rem;
}

.title-line {
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, #52c41a, #87d068);
  margin: 0 auto;
  margin-bottom: 1rem;
  border-radius: 2px;
  transition: all 0.3s;
}

.page-title:hover .title-line {
  width: 120px;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.page-header {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.page-header:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.search-area {
  padding: 24px;
  border-bottom: 1px solid #f0f2f5;
  background: linear-gradient(to right, #f9f9f9, #ffffff);
}

.search-form {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.input-group {
  display: flex;
  gap: 16px;
  flex: 2;
  flex-wrap: wrap;
}

.input-group :deep(.el-input),
.input-group :deep(.el-select) {
  width: 180px;
  transition: all 0.3s ease;
}

.input-group :deep(.el-input:hover),
.input-group :deep(.el-select:hover) {
  transform: translateY(-2px);
}

.input-group :deep(.el-input__wrapper),
.input-group :deep(.el-select__wrapper) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 8px;
}

.input-group :deep(.el-input__wrapper:hover),
.input-group :deep(.el-select__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.time-group {
  display: flex;
  gap: 16px;
  flex: 2;
  flex-wrap: wrap;
}

.time-type,
.time-range {
  width: 130px;
}

.time-group :deep(.el-date-editor) {
  width: 280px;
}

.time-group :deep(.el-input__wrapper),
.time-group :deep(.el-select__wrapper) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.time-group :deep(.el-input__wrapper:hover),
.time-group :deep(.el-select__wrapper:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 8px;
}

.button-group .el-button {
  transition: all 0.3s;
  border-radius: 8px;
  font-weight: 500;
  padding: 10px 20px;
  height: auto;
  line-height: 1.5;
}

.button-group .el-button--primary {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.button-group .el-button--success {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  border: none;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.button-group .el-button--default {
  border: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #ffffff, #f5f5f5);
}

.button-group .el-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.button-group .el-button--primary:hover {
  background: linear-gradient(135deg, #40a9ff, #1890ff);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.button-group .el-button--success:hover {
  background: linear-gradient(135deg, #73d13d, #52c41a);
  box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
}

.button-group .el-button--default:hover {
  background: #ffffff;
  border-color: #d9d9d9;
}

.view-controls {
  padding: 18px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  background-color: #fafafa;
  border-top: 1px solid #f0f2f5;
}

.active-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
}

.filter-label {
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  background-color: #f0f2f5;
  padding: 6px 12px;
  border-radius: 20px;
}

.view-switcher {
  white-space: nowrap;
  margin-left: auto;
}

.view-switcher :deep(.el-radio-button__inner) {
  padding: 8px 16px;
  transition: all 0.3s;
}

.view-switcher :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.view-switcher :deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-radius: 8px 0 0 8px;
}

.view-switcher :deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 0 8px 8px 0;
}

:deep(.el-tag) {
  margin: 3px;
  border-radius: 20px;
  transition: all 0.3s;
  padding: 0 12px;
  height: 28px;
  line-height: 26px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

:deep(.el-tag:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

:deep(.el-tag .el-tag__close) {
  right: -2px;
  color: #909399;
  font-weight: bold;
  background-color: transparent;
  transition: all 0.3s;
}

:deep(.el-tag .el-tag__close:hover) {
  background-color: #f56c6c;
  color: #fff;
  transform: scale(1.2);
}

@media screen and (max-width: 1200px) {
  .search-form {
    flex-direction: column;
  }

  .input-group,
  .time-group {
    flex-wrap: wrap;
  }

  .input-group :deep(.el-input),
  .input-group :deep(.el-select),
  .time-group :deep(.el-select),
  .time-group :deep(.el-date-editor) {
    width: 100%;
  }

  .button-group {
    justify-content: flex-start;
  }
}

@media screen and (max-width: 768px) {
  .view-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .view-switcher {
    display: flex;
    justify-content: flex-end;
  }
}

.list-view {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
  padding: 24px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.list-view:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.list-view :deep(.el-table) {
  --el-table-border-color: #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
}

.list-view :deep(.el-table::before) {
  display: none;
}

.list-view :deep(.el-table__row) {
  transition: all 0.3s;
}

.list-view :deep(.el-table__row:hover) {
  background-color: #f0f7ff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  z-index: 1;
  position: relative;
}

.list-view :deep(.el-table__header-wrapper) {
  background-color: #f5f7fa;
}

.list-view :deep(.el-table__header) {
  background-color: #f5f7fa;
}

.list-view :deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #303133;
  padding: 16px 0;
  font-size: 14px;
}

.list-view :deep(.el-table td) {
  padding: 16px 0;
}

.card-view {
  margin: 0 -10px 20px;
}

.lost-item-card {
  margin-bottom: 24px;
  transition: all 0.3s ease;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  background-color: #fff;
  border: none;
}

.lost-item-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.1);
}

.card-image {
  position: relative;
  height: 200px;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.card-image .el-image {
  width: 100%;
  height: 100%;
}

/* 图片样式优化 */
.card-image :deep(.el-image__inner) {
  object-fit: cover !important;
  transition: transform 0.6s ease;
}

.card-image:hover :deep(.el-image__inner) {
  transform: scale(1.08);
}

.image-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  color: #909399;
}

.image-placeholder .el-icon {
  font-size: 48px;
  opacity: 0.7;
}

.card-status {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1;
}

.card-status :deep(.el-tag) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: none;
  font-weight: 500;
  padding: 4px 8px;
}

.card-content {
  padding: 18px;
  position: relative;
}

.card-title {
  margin: 0 0 12px;
  font-size: 18px;
  color: #303133;
  line-height: 1.4;
  font-weight: 600;
  transition: color 0.3s;
}

.lost-item-card:hover .card-title {
  color: #1890ff;
}

.card-desc {
  margin: 0 0 14px;
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 45px;
  transition: color 0.3s;
}

.lost-item-card:hover .card-desc {
  color: #303133;
}

.card-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  background-color: #f9fafc;
  padding: 10px;
  border-radius: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
  transition: transform 0.3s;
}

.info-item:hover {
  transform: translateX(4px);
  color: #1890ff;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  margin-right: 4px;
  border: 1px solid #eee;
  background-color: #f5f7fa;
}

.info-item .el-icon {
  color: #909399;
  font-size: 16px;
  transition: transform 0.3s, color 0.3s;
}

.info-item:hover .el-icon {
  transform: scale(1.2);
  color: #1890ff;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f2f5;
}

.card-actions .el-button {
  transition: all 0.3s;
}

.card-actions .el-button:hover {
  transform: scale(1.15);
}

.empty-state {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  padding: 40px 20px;
  margin-bottom: 24px;
  text-align: center;
}

.empty-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 120px;
  height: 120px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 100%);
  border-radius: 50%;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
}

.empty-icon {
  color: #1890ff;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.empty-hint {
  color: #606266;
  font-size: 16px;
  margin: 16px 0;
}

.empty-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.empty-actions .el-button {
  min-width: 120px;
  transition: all 0.3s;
}

.empty-actions .el-button:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.pagination {
  background: #fff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: flex-end;
  transition: all 0.3s;
}

.pagination:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.pagination :deep(.el-pagination) {
  font-weight: 500;
}

.pagination :deep(.el-pagination__sizes) {
  margin-right: 16px;
}

.pagination :deep(.el-pagination .el-select .el-input) {
  width: 110px;
}

.pagination :deep(.el-pagination__jump) {
  margin-left: 16px;
}

.pagination :deep(.el-pager li) {
  transition: all 0.3s;
}

.pagination :deep(.el-pager li:hover) {
  transform: translateY(-2px);
}

.pagination :deep(.el-pager li.is-active) {
  background-color: #1890ff;
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  max-width: 100%;
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 500;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
  margin-right: 0;
}

:deep(.el-card) {
  border: none;
  overflow: hidden;
  transition: all 0.3s;
}

/* 表格中的图片样式 */
.lost-item-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  display: block;
  margin: 0 auto;
  transition: transform 0.3s;
}

.lost-item-image:hover {
  transform: scale(1.05);
}

.image-placeholder {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 4px;
  margin: 0 auto;
}

.image-placeholder .el-icon {
  font-size: 24px;
  color: #909399;
}

.detail-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.detail-layout {
  display: flex;
  gap: 20px;
  min-height: 400px;
}

.detail-left {
  flex: 0 0 400px;
  background-color: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.detail-images {
  width: 100%;
  height: 400px;
}

.detail-image {
  width: 100%;
  height: 100%;
}

:deep(.el-carousel__container) {
  height: 400px;
}

:deep(.el-image__inner) {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.detail-actions {
  margin-top: auto;
  padding-top: 20px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.detail-actions .el-button {
  min-width: 100px;
}

/* 原有图片预览样式 */
.existing-images {
  margin-bottom: 16px;
}

.image-preview {
  position: relative;
  width: 148px;
  height: 148px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-actions {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 0 0 0 8px;
}

.image-actions .el-button {
  color: #fff;
  padding: 4px;
}

.image-actions .el-button:hover {
  color: #f56c6c;
}
</style>