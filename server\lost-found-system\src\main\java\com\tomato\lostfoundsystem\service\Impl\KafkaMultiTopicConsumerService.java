package com.tomato.lostfoundsystem.service.Impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.ConnectException;
import java.util.Date;
import java.util.concurrent.TimeoutException;

@Slf4j
@Service
public class KafkaMultiTopicConsumerService {

    private final SimpMessagingTemplate messagingTemplate;
    private final ObjectMapper objectMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private MessageRetryService messageRetryService;

    // 构造函数注入依赖
    public KafkaMultiTopicConsumerService(SimpMessagingTemplate messagingTemplate) {
        this.messagingTemplate = messagingTemplate;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 监听重试主题
     */
    @KafkaListener(topics = "retry-topic", groupId = "retry-consumer-group")
    public void consumeRetryMessage(
            String message,
            @Header("kafka_receivedPartitionId") int partition,
            @Header("kafka_offset") long offset,
            Acknowledgment acknowledgment) {
        try {
            log.info("从retry-topic消费消息: {}, 分区: {}, 偏移量: {}", message, partition, offset);

            // 提取重试次数
            JsonNode node = objectMapper.readTree(message);
            int retryCount = node.has("retryCount") ? node.get("retryCount").asInt() : 0;

            // 尝试提取消息类型
            String messageType = node.has("messageType") ? node.get("messageType").asText() : null;

            // 根据消息类型进行不同处理
            if ("NOTIFICATION".equals(messageType)) {
                // 处理通知消息
                processNotificationMessage(message);
            } else if ("SYSTEM".equals(messageType)) {
                // 处理系统消息
                processSystemMessage(message);
            } else {
                // 默认处理为聊天消息
                processChatMessage(message);
            }

            // 确认消息
            acknowledgment.acknowledge();
            log.info("重试消息处理成功");
        } catch (Exception e) {
            log.error("处理重试消息失败: {}", e.getMessage(), e);

            try {
                // 提取重试次数
                JsonNode node = objectMapper.readTree(message);
                int retryCount = node.has("retryCount") ? node.get("retryCount").asInt() : 0;

                // 调用重试服务进行下一次重试
                messageRetryService.scheduleRetry(message, retryCount);
            } catch (Exception ex) {
                log.error("解析重试消息失败: {}", ex.getMessage(), ex);
            }

            // 确认当前消息，因为我们已经调度了重试
            acknowledgment.acknowledge();
        }
    }

    /**
     * 监听死信队列
     */
    @KafkaListener(topics = "dead-letter-queue", groupId = "dlq-consumer-group")
    public void consumeDeadLetterQueue(
            String message,
            @Header("kafka_receivedPartitionId") int partition,
            @Header("kafka_offset") long offset,
            Acknowledgment acknowledgment) {
        try {
            log.warn("从dead-letter-queue消费消息: {}, 分区: {}, 偏移量: {}", message, partition, offset);

            // 记录失败消息
            JsonNode node = objectMapper.readTree(message);
            int retryCount = node.has("retryCount") ? node.get("retryCount").asInt() : 0;
            long failedAt = node.has("failedAt") ? node.get("failedAt").asLong() : System.currentTimeMillis();

            log.warn("消息处理失败，重试次数: {}, 失败时间: {}", retryCount, new Date(failedAt));

            // 这里可以添加告警逻辑，例如发送邮件或短信通知
            // 也可以将失败消息保存到数据库中，供后续分析

            // 确认消息
            acknowledgment.acknowledge();
        } catch (Exception e) {
            log.error("处理死信队列消息失败: {}", e.getMessage(), e);
            // 即使处理失败，也确认消息，避免无限循环
            acknowledgment.acknowledge();
        }
    }

    /**
     * 监听通知主题
     */
    @KafkaListener(topics = "notification-topic", groupId = "notification-consumer-group")
    public void consumeNotification(
            String message,
            @Header("kafka_receivedPartitionId") int partition,
            @Header("kafka_offset") long offset,
            Acknowledgment acknowledgment) {
        try {
            log.info("从notification-topic消费消息: {}, 分区: {}, 偏移量: {}", message, partition, offset);

            // 处理通知消息
            processNotificationMessage(message);

            // 确认消息
            acknowledgment.acknowledge();
        } catch (Exception e) {
            log.error("处理通知消息失败: {}", e.getMessage(), e);

            // 根据错误类型决定是否确认
            if (isRecoverableError(e)) {
                log.warn("可恢复错误，通知消息将重新消费");
                // 不确认消息，让它重新消费
            } else {
                log.error("不可恢复错误，确认通知消息并记录失败");
                acknowledgment.acknowledge();

                // 尝试使用重试服务
                try {
                    messageRetryService.scheduleRetry(message, 0);
                } catch (Exception retryError) {
                    log.error("调度通知消息重试失败: {}", retryError.getMessage(), retryError);
                }
            }
        }
    }

    /**
     * 监听系统主题
     */
    @KafkaListener(topics = "system-topic", groupId = "system-consumer-group")
    public void consumeSystemMessage(
            String message,
            @Header("kafka_receivedPartitionId") int partition,
            @Header("kafka_offset") long offset,
            Acknowledgment acknowledgment) {
        try {
            log.info("从system-topic消费消息: {}, 分区: {}, 偏移量: {}", message, partition, offset);

            // 处理系统消息
            processSystemMessage(message);

            // 确认消息
            acknowledgment.acknowledge();
        } catch (Exception e) {
            log.error("处理系统消息失败: {}", e.getMessage(), e);

            // 根据错误类型决定是否确认
            if (isRecoverableError(e)) {
                log.warn("可恢复错误，系统消息将重新消费");
                // 不确认消息，让它重新消费
            } else {
                log.error("不可恢复错误，确认系统消息并记录失败");
                acknowledgment.acknowledge();

                // 尝试使用重试服务
                try {
                    messageRetryService.scheduleRetry(message, 0);
                } catch (Exception retryError) {
                    log.error("调度系统消息重试失败: {}", retryError.getMessage(), retryError);
                }
            }
        }
    }

    /**
     * 处理聊天消息
     */
    private void processChatMessage(String message) throws Exception {
        // 提取接收者ID
        String receiverId = extractReceiverId(message);
        if (receiverId == null || receiverId.isEmpty()) {
            log.error("无法从消息中提取接收者ID，消息内容: {}", message);
            throw new IllegalArgumentException("无法从消息中提取接收者ID");
        }

        log.info("从消息中提取到接收者ID: {}", receiverId);

        // 检查用户是否在线
        try {
            Long receiverIdLong = Long.parseLong(receiverId);
            if (!redisService.isUserOnline(receiverIdLong)) {
                log.warn("接收者离线: {}, 消息将被保留在Kafka中", receiverId);
                throw new IllegalStateException("接收者离线: " + receiverId);
            }

            log.info("接收者在线: {}, 准备推送消息", receiverId);
        } catch (NumberFormatException e) {
            log.error("接收者ID格式错误: {}", receiverId);
            throw new IllegalArgumentException("接收者ID格式错误: " + receiverId);
        }

        // 尝试解析消息，确定是否需要提取payload
        try {
            JsonNode jsonNode = objectMapper.readTree(message);

            // 如果消息包含payload字段，且payload是一个对象，则提取payload作为实际消息内容
            if (jsonNode.has("payload") && jsonNode.get("payload").isObject()) {
                log.info("消息包含payload字段，提取payload作为实际消息内容");
                String payload = jsonNode.get("payload").toString();

                // 推送payload - 只使用标准路径 /queue/private
                messagingTemplate.convertAndSendToUser(receiverId, "/queue/private", payload);
                log.info("成功推送payload到用户: {}", receiverId);
            } else {
                // 推送原始消息 - 只使用标准路径 /queue/private
                messagingTemplate.convertAndSendToUser(receiverId, "/queue/private", message);
                log.info("成功推送原始消息到用户: {}", receiverId);
            }
        } catch (Exception e) {
            log.warn("解析消息失败，将推送原始消息: {}", e.getMessage());
            // 如果解析失败，推送原始消息
            messagingTemplate.convertAndSendToUser(receiverId, "/queue/private", message);
            log.info("成功推送原始消息到用户: {}", receiverId);
        }
    }

    /**
     * 处理通知消息
     */
    private void processNotificationMessage(String message) throws Exception {
        // 提取接收者ID
        String receiverId = extractReceiverId(message);
        if (receiverId == null || receiverId.isEmpty()) {
            throw new IllegalArgumentException("无法从通知消息中提取接收者ID");
        }

        // 推送通知 - 使用标准路径 /queue/notifications
        messagingTemplate.convertAndSendToUser(receiverId, "/queue/notifications", message);

        log.info("成功推送通知消息到用户: {}", receiverId);
    }

    /**
     * 处理系统消息
     */
    private void processSystemMessage(String message) throws Exception {
        // 系统消息可能需要广播给所有用户
        // 或者发送给特定用户组

        // 尝试提取目标用户组
        JsonNode node = objectMapper.readTree(message);
        if (node.has("targetGroup")) {
            String targetGroup = node.get("targetGroup").asText();

            // 根据目标用户组推送消息
            // 这里简化处理，直接广播
            messagingTemplate.convertAndSend("/topic/system", message);

            log.info("成功推送系统消息到用户组: {}", targetGroup);
        } else if (node.has("receiverId")) {
            // 发送给特定用户
            String receiverId = node.get("receiverId").asText();
            messagingTemplate.convertAndSendToUser(receiverId, "/queue/system", message);

            log.info("成功推送系统消息到用户: {}", receiverId);
        } else {
            // 广播给所有用户
            messagingTemplate.convertAndSend("/topic/system", message);

            log.info("成功广播系统消息给所有用户");
        }
    }

    // 从消息中提取接收者ID
    private String extractReceiverId(String message) {
        try {
            // 尝试解析JSON
            JsonNode jsonNode = objectMapper.readTree(message);

            // 尝试获取根级别的receiverId字段
            if (jsonNode.has("receiverId")) {
                return jsonNode.get("receiverId").asText();
            }

            // 尝试从payload字段中获取receiverId
            if (jsonNode.has("payload") && jsonNode.get("payload").isObject()) {
                JsonNode payload = jsonNode.get("payload");
                if (payload.has("receiverId")) {
                    return payload.get("receiverId").asText();
                }
            }

            // 如果没有receiverId字段，尝试其他可能的字段名
            if (jsonNode.has("to")) {
                return jsonNode.get("to").asText();
            }

            if (jsonNode.has("receiver")) {
                return jsonNode.get("receiver").asText();
            }

            // 尝试从payload字段中获取其他可能的字段名
            if (jsonNode.has("payload") && jsonNode.get("payload").isObject()) {
                JsonNode payload = jsonNode.get("payload");
                if (payload.has("to")) {
                    return payload.get("to").asText();
                }
                if (payload.has("receiver")) {
                    return payload.get("receiver").asText();
                }
            }

            // 如果都没有找到，返回null
            log.warn("无法从JSON消息中提取接收者ID: {}", message);
            return null;
        } catch (Exception e) {
            // 如果不是JSON格式，尝试其他方式提取
            log.warn("消息不是JSON格式，尝试其他方式提取接收者ID: {}, 错误: {}", message, e.getMessage());

            // 这里可以添加其他提取方式，例如正则表达式等
            // 简单起见，这里返回一个默认值
            return null;
        }
    }

    // 判断错误是否可恢复
    private boolean isRecoverableError(Exception e) {
        // 网络错误、连接超时等通常是可恢复的
        return e instanceof IOException ||
               e instanceof TimeoutException ||
               e instanceof ConnectException;
    }
}
