@startuml 失物招领系统部署图

!define RECTANGLE node
!define DATABASE database
!define CLOUD cloud
!define QUEUE queue

skinparam backgroundColor white
skinparam defaultTextAlignment center
skinparam monochrome false
skinparam shadowing false

title 校园失物招领系统部署图

' 客户端设备
node "用户设备" as UserDevice {
    [Web浏览器] as Browser
}

' 服务器节点
node "Web服务器" as WebServer {
    [Nginx] as Nginx
    [静态资源] as StaticFiles
}

node "应用服务器" as AppServer {
    [Spring Boot应用] as SpringBoot
    [Tomcat] as Tomcat
}

node "数据库服务器" as DBServer {
    database "MySQL" as MySQL {
        [用户数据] as UserDB
        [物品数据] as ItemDB
        [聊天数据] as ChatDB
        [匹配数据] as MatchDB
    }
    database "Redis" as Redis {
        [缓存] as Cache
        [会话] as Session
    }
}

node "消息队列服务器" as MQServer {
    queue "Kafka" as <PERSON><PERSON><PERSON> {
        [消息主题] as Topics
    }
    [Zookeeper] as Zookeeper
}

cloud "云服务" as CloudServices {
    [阿里云OSS] as OSS
}

node "AI服务器 (AutoDL)" as AIServer {
    [CLIP+FAISS服务] as CLIP
    [GPU资源] as GPU
}

' 连接关系
UserDevice --> WebServer : HTTPS
WebServer --> AppServer : HTTP/1.1
AppServer --> DBServer : TCP/IP
AppServer --> MQServer : TCP/IP
AppServer --> CloudServices : HTTPS
AppServer --> AIServer : HTTPS

' 详细连接
Browser --> Nginx : HTTPS (443)
Nginx --> StaticFiles : 文件系统
Nginx --> Tomcat : 反向代理 (8080)
Tomcat --> SpringBoot : 容器
SpringBoot --> MySQL : JDBC (3306)
SpringBoot --> Redis : Redis协议 (6379)
SpringBoot --> Kafka : Kafka协议 (9092)
Kafka --> Zookeeper : ZK协议 (2181)
SpringBoot --> OSS : HTTPS (443)
SpringBoot --> CLIP : RESTful API (8000)
CLIP --> GPU : CUDA

' 网络分区
rectangle "公网区域" {
    UserDevice
    CloudServices
}

rectangle "内网区域" {
    WebServer
    AppServer
    DBServer
    MQServer
    AIServer
}

@enduml
