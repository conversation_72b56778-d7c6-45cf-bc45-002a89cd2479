package com.tomato.lostfoundsystem.utils;

import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.entity.FoundItem;
import lombok.extern.slf4j.Slf4j;

/**
 * 物品描述构建工具类
 * 用于构建物品的文本描述，只包含名称和地点信息
 * 注意：由于CLIP模型token限制(77)，我们只保留最关键的信息
 * 超过30个字符自动截断
 */
@Slf4j
public class ItemDescriptionBuilder {

    /**
     * 最大文本长度限制
     */
    private static final int MAX_TEXT_LENGTH = 30;

    /**
     * 构建失物的简化文本描述，包含名称、地点和时间
     * @param lostItem 失物对象
     * @return 构造好的描述文本
     */
    public static String buildLostItemDescription(LostItem lostItem) {
        StringBuilder descBuilder = new StringBuilder();

        // 添加物品名称（最高优先级）
        if (lostItem.getItemName() != null) {
            descBuilder.append(lostItem.getItemName()).append(" ");
        }

        // 添加地点信息（不添加前缀）
        if (lostItem.getLostLocation() != null && !lostItem.getLostLocation().isBlank()) {
            descBuilder.append(lostItem.getLostLocation()).append(" ");
        }

        // 添加简化的时间信息
        if (lostItem.getLostTime() != null) {
            // 只使用月日和时间，格式为 MM-DD HH:MM
            String simpleTime = formatSimpleTime(lostItem.getLostTime());
            descBuilder.append(simpleTime);
        }

        // 截断过长的文本
        String result = truncateText(descBuilder.toString().trim());
        log.info("【智能匹配】生成简化失物描述文本: {}", result);
        return result;
    }

    /**
     * 构建拾物的简化文本描述，包含名称、地点和时间
     * @param foundItem 拾物对象
     * @return 构造好的描述文本
     */
    public static String buildFoundItemDescription(FoundItem foundItem) {
        StringBuilder descBuilder = new StringBuilder();

        // 添加物品名称（最高优先级）
        if (foundItem.getItemName() != null) {
            descBuilder.append(foundItem.getItemName()).append(" ");
        }

        // 添加地点信息（不添加前缀）
        if (foundItem.getFoundLocation() != null && !foundItem.getFoundLocation().isBlank()) {
            descBuilder.append(foundItem.getFoundLocation()).append(" ");
        }

        // 添加简化的时间信息
        if (foundItem.getFoundTime() != null) {
            // 只使用月日和时间，格式为 MM-DD HH:MM
            String simpleTime = formatSimpleTime(foundItem.getFoundTime());
            descBuilder.append(simpleTime);
        }

        // 截断过长的文本
        String result = truncateText(descBuilder.toString().trim());
        log.info("【智能匹配】生成简化拾物描述文本: {}", result);
        return result;
    }

    /**
     * 格式化时间为简化格式 MM-DD HH:MM
     * @param date 日期对象
     * @return 格式化后的时间字符串
     */
    private static String formatSimpleTime(java.util.Date date) {
        if (date == null) {
            return "";
        }

        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("MM-dd HH:mm");
        return sdf.format(date);
    }

    /**
     * 格式化时间字符串为简化格式 MM-DD HH:MM
     * @param dateStr 日期字符串，格式为 yyyy-MM-dd HH:mm:ss
     * @return 格式化后的时间字符串
     */
    private static String formatSimpleTime(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return "";
        }

        try {
            // 解析输入的日期字符串
            java.text.SimpleDateFormat inputFormat = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            java.util.Date date = inputFormat.parse(dateStr);

            // 格式化为简化格式
            java.text.SimpleDateFormat outputFormat = new java.text.SimpleDateFormat("MM-dd HH:mm");
            return outputFormat.format(date);
        } catch (Exception e) {
            log.error("【智能匹配】格式化时间字符串失败: {}", dateStr, e);

            // 尝试提取月日和时间部分
            try {
                // 假设格式为 yyyy-MM-dd HH:mm:ss 或类似格式
                String[] parts = dateStr.split(" ");
                if (parts.length >= 2) {
                    String datePart = parts[0]; // yyyy-MM-dd
                    String timePart = parts[1]; // HH:mm:ss

                    String[] dateParts = datePart.split("-");
                    if (dateParts.length >= 3) {
                        String month = dateParts[1];
                        String day = dateParts[2];

                        String[] timeParts = timePart.split(":");
                        if (timeParts.length >= 2) {
                            String hour = timeParts[0];
                            String minute = timeParts[1];

                            return month + "-" + day + " " + hour + ":" + minute;
                        }
                    }
                }
            } catch (Exception ex) {
                log.error("【智能匹配】提取时间部分失败: {}", dateStr, ex);
            }

            // 如果所有尝试都失败，返回原始字符串
            return dateStr;
        }
    }

    /**
     * 截断过长的文本
     * @param text 原始文本
     * @return 截断后的文本
     */
    private static String truncateText(String text) {
        if (text.length() > MAX_TEXT_LENGTH) {
            String truncated = text.substring(0, MAX_TEXT_LENGTH);
            log.info("【智能匹配】文本过长，已截断: {} -> {}", text, truncated);
            return truncated;
        }
        return text;
    }
}
