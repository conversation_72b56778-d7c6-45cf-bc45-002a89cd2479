package com.tomato.lostfoundsystem.websocket;

import com.tomato.lostfoundsystem.dto.NotificationDTO;
import com.tomato.lostfoundsystem.service.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

/**
 * 通知WebSocket处理器
 *
 * 该类负责通过WebSocket发送通知消息，并确保通知被保存到数据库中
 * 修复了通知不显示在通知列表中的问题
 */
@Slf4j
@Component
public class NotificationWebSocketHandler {

    private final SimpMessagingTemplate messagingTemplate;
    private final NotificationService notificationService;

    @Autowired
    private com.tomato.lostfoundsystem.mapper.UserMapper userMapper;

    @Autowired
    public NotificationWebSocketHandler(SimpMessagingTemplate messagingTemplate, NotificationService notificationService) {
        this.messagingTemplate = messagingTemplate;
        this.notificationService = notificationService;
    }

    /**
     * 推送通知到特定的用户
     * @param userId 用户ID
     * @param notification 通知对象
     */
    public void sendNotification(Long userId, NotificationDTO notification) {
        try {
            // 获取通知ID
            Long notificationId = notification.getId();
            if (notificationId == null) {
                log.warn("【通知调试】通知没有ID，这可能导致通知无法正确跟踪");
            } else {
                log.info("【通知调试】准备发送通知 - ID: {}", notificationId);
            }

            // 添加调试信息
            log.info("【通知调试】准备发送通知 - 用户ID: {}, 标题: {}, 通知内容: {}",
                    userId, notification.getTitle(), notification);

            // 只发送到用户特定队列，使用标准路径 /user/queue/notifications
            log.info("【通知调试】发送到用户队列 - 路径: /user/queue/notifications");

            // 获取用户名
            String username = getUsernameById(userId);
            if (username != null) {
                messagingTemplate.convertAndSendToUser(username, "/queue/notifications", notification);
                log.info("【通知调试】使用用户名发送通知: 用户名={}, 用户ID={}", username, userId);
            } else {
                log.error("【通知调试】无法获取用户名，使用ID发送通知: {}", userId);
                messagingTemplate.convertAndSendToUser(userId.toString(), "/queue/notifications", notification);
            }

            log.info("成功发送通知 - 用户ID: {}, 标题: {}, 通知ID: {}",
                    userId, notification.getTitle(), notificationId);
        } catch (Exception e) {
            log.error("发送通知失败 - 用户ID: {}, 标题: {}, 错误: {}",
                    userId, notification.getTitle(), e.getMessage(), e);
        }
    }

    /**
     * 推送通知到特定的用户（简化版本，直接使用标题和消息）
     * @param userId 用户ID
     * @param title 通知标题
     * @param message 通知内容
     */
    public void sendSimpleNotification(Long userId, String title, String message) {
        try {
            // 创建通知对象
            NotificationDTO notification = new NotificationDTO();
            notification.setUserId(userId);
            notification.setTitle(title);
            notification.setMessage(message);
            notification.setStatus("UNREAD");
            notification.setType("SYSTEM"); // 设置默认类型

            log.info("【通知调试】准备发送简单通知 - 用户ID: {}, 标题: {}", userId, title);

            // 只发送到用户特定队列，使用标准路径 /user/queue/notifications
            log.info("【通知调试】发送到用户队列 - 路径: /user/queue/notifications");

            // 获取用户名
            String username = getUsernameById(userId);
            if (username != null) {
                messagingTemplate.convertAndSendToUser(username, "/queue/notifications", notification);
                log.info("【通知调试】使用用户名发送简单通知: 用户名={}, 用户ID={}", username, userId);
            } else {
                log.error("【通知调试】无法获取用户名，使用ID发送简单通知: {}", userId);
                messagingTemplate.convertAndSendToUser(userId.toString(), "/queue/notifications", notification);
            }

            log.info("成功发送简单通知 - 用户ID: {}, 标题: {}", userId, title);
        } catch (Exception e) {
            log.error("发送简单通知失败 - 用户ID: {}, 标题: {}, 错误: {}",
                    userId, title, e.getMessage(), e);
        }
    }

    /**
     * 根据用户ID获取用户名
     * @param userId 用户ID
     * @return 用户名，如果未找到则返回null
     */
    private String getUsernameById(Long userId) {
        try {
            if (userId == null) {
                log.warn("【通知调试】尝试获取用户名时收到空的用户ID");
                return null;
            }

            // 从数据库中查询用户
            com.tomato.lostfoundsystem.entity.User user = userMapper.findById(userId);
            if (user != null) {
                return user.getUsername();
            } else {
                log.warn("【通知调试】未找到用户ID对应的用户: {}", userId);
                return null;
            }
        } catch (Exception e) {
            log.error("【通知调试】获取用户名时出错: {}", e.getMessage(), e);
            return null;
        }
    }
}

