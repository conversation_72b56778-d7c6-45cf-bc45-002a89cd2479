<template>
  <el-card class="match-result-card" shadow="hover" @click="viewDetail">
    <!-- 图片区域 -->
    <div class="item-image">
      <el-image :src="item.imageUrl" fit="cover">
        <template #error>
          <div class="image-placeholder">
            <el-icon><Picture /></el-icon>
          </div>
        </template>
      </el-image>

      <!-- 物品类型标签 -->
      <div class="item-tag" :class="targetType === 'LOST' ? 'found-tag' : 'lost-tag'">
        {{ targetType === 'LOST' ? '拾物' : '失物' }}
      </div>

      <!-- 综合相似度标签 -->
      <div class="similarity-badge" :style="{ backgroundColor: getSimilarityColor(item.similarity) }">
        匹配度: {{ (item.similarity * 100).toFixed(0) }}%
      </div>

      <!-- 匹配类型标签 -->
      <div v-if="item.match_type" class="match-type-badge">
        {{ getMatchTypeLabel(item.match_type) }}
      </div>
    </div>

    <!-- 物品信息 -->
    <div class="item-info">
      <h3>{{ item.name }}</h3>
      <p class="item-description">{{ item.description }}</p>
      <div class="item-meta">
        <span><el-icon><Location /></el-icon> {{ item.location }}</span>
        <span><el-icon><Timer /></el-icon> {{ formatDate(item.time) }}</span>
      </div>

      <!-- 匹配类型说明 -->
      <div v-if="item.match_type" class="match-type-info">
        <el-tag :type="getMatchTypeTagType(item.match_type)" size="small">
          {{ getMatchTypeLabel(item.match_type) }}
        </el-tag>
      </div>

      <!-- 操作按钮 -->
      <div class="item-actions">
        <el-button type="primary" size="small" @click.stop="viewDetail">查看详情</el-button>
        <el-button type="success" size="small" @click.stop="contactOwner">联系{{ targetType === 'LOST' ? '拾主' : '失主' }}</el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { Picture, Location, Timer } from '@element-plus/icons-vue'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  targetType: {
    type: String,
    default: 'LOST'
  }
})

const emit = defineEmits(['view-detail', 'contact-owner'])

// 获取相似度颜色
const getSimilarityColor = (similarity) => {
  if (similarity >= 0.8) return '#67C23A' // 高相似度 - 绿色
  if (similarity >= 0.6) return '#E6A23C' // 中等相似度 - 橙色
  if (similarity >= 0.4) return '#F56C6C' // 低相似度 - 红色
  return '#909399' // 很低相似度 - 灰色
}

// 获取匹配类型的友好标签
const getMatchTypeLabel = (matchType) => {
  const types = {
    'IMAGE_TO_IMAGE': '外观匹配',
    'IMAGE_TO_TEXT': '图像-描述匹配',
    'TEXT_TO_IMAGE': '描述-图像匹配',
    'TEXT_TO_TEXT': '描述匹配'
  }
  return types[matchType] || '未知匹配类型'
}

// 获取匹配类型的标签类型
const getMatchTypeTagType = (matchType) => {
  const types = {
    'IMAGE_TO_IMAGE': 'success',
    'IMAGE_TO_TEXT': 'warning',
    'TEXT_TO_IMAGE': 'warning',
    'TEXT_TO_TEXT': 'primary'
  }
  return types[matchType] || 'info'
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知时间'
  const date = new Date(dateString)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
}

// 查看详情
const viewDetail = () => {
  emit('view-detail', props.item)
}

// 联系物品所有者
const contactOwner = () => {
  emit('contact-owner', props.item)
}


</script>

<style scoped>
.match-result-card {
  margin-bottom: 20px;
  transition: all 0.3s;
  height: 100%;
}

.match-result-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.item-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.item-image .el-image {
  width: 100%;
  height: 100%;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 30px;
}

.item-tag {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
}

.found-tag {
  background-color: #409EFF;
}

.lost-tag {
  background-color: #F56C6C;
}

.similarity-badge {
  position: absolute;
  bottom: 10px;
  right: 10px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  background-color: rgba(0, 0, 0, 0.7);
}

.match-type-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  background-color: rgba(0, 0, 0, 0.7);
}

.item-info {
  padding: 15px;
}

.item-info h3 {
  margin: 0 0 10px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.item-description {
  margin: 0 0 10px;
  font-size: 14px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-bottom: 15px;
}

.match-type-info {
  margin: 15px 0;
  display: flex;
  align-items: center;
}

.item-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}
</style>
