package com.tomato.lostfoundsystem.entity;


import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户实体类，对应数据库中的 user 表
 */
@Data
public class User {

    /**
     * 用户ID，自增主键
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码（建议加密存储）
     */
    private String password;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 角色（user 或 admin）
     */
    private String role;

    /**
     * 注册时间
     */
    private LocalDateTime createTime;

    /**
     * 用户头像URL
     */
    private String avatar;

    /**
     * 是否已删除（逻辑删除）
     */
    private Boolean deleted;

    /**
     * 账号是否激活
     */
    private Boolean isActive;

}
