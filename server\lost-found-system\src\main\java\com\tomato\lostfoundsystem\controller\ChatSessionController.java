package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.service.ConversationService;
import com.tomato.lostfoundsystem.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 聊天会话控制器
 * 用于管理聊天会话的状态、置顶、静音等
 */
@Slf4j
@RestController
@RequestMapping("/api/chat/session")
public class ChatSessionController {

    @Autowired
    private ConversationService conversationService;

    @Autowired
    private RedisService redisService;

    /**
     * 更新会话状态
     * @param conversationId 会话ID
     * @param status 状态：ACTIVE, ARCHIVED, DELETED
     * @return 操作结果
     */
    @PutMapping("/{conversationId}/status")
    public ResponseEntity<Result<Void>> updateStatus(
            @PathVariable Long conversationId,
            @RequestParam String status) {
        try {
            log.info("更新会话状态 - 会话ID: {}, 状态: {}", conversationId, status);
            conversationService.updateStatus(conversationId, status);
            return ResponseEntity.ok(Result.success());
        } catch (Exception e) {
            log.error("更新会话状态失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Result.fail(e.getMessage()));
        }
    }

    /**
     * 更新会话置顶状态
     * @param conversationId 会话ID
     * @param isPinned 是否置顶
     * @return 操作结果
     */
    @PutMapping("/{conversationId}/pin")
    public ResponseEntity<Result<Void>> updatePinned(
            @PathVariable Long conversationId,
            @RequestParam Boolean isPinned) {
        try {
            log.info("更新会话置顶状态 - 会话ID: {}, 是否置顶: {}", conversationId, isPinned);
            conversationService.updatePinned(conversationId, isPinned);
            return ResponseEntity.ok(Result.success());
        } catch (Exception e) {
            log.error("更新会话置顶状态失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Result.fail(e.getMessage()));
        }
    }

    /**
     * 更新会话静音状态
     * @param conversationId 会话ID
     * @param isMuted 是否静音
     * @return 操作结果
     */
    @PutMapping("/{conversationId}/mute")
    public ResponseEntity<Result<Void>> updateMuted(
            @PathVariable Long conversationId,
            @RequestParam Boolean isMuted) {
        try {
            log.info("更新会话静音状态 - 会话ID: {}, 是否静音: {}", conversationId, isMuted);
            conversationService.updateMuted(conversationId, isMuted);
            return ResponseEntity.ok(Result.success());
        } catch (Exception e) {
            log.error("更新会话静音状态失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Result.fail(e.getMessage()));
        }
    }

    /**
     * 重置会话未读计数
     * @param userId 用户ID
     * @param contactId 联系人ID
     * @return 操作结果
     */
    @PutMapping("/reset-unread")
    public ResponseEntity<Result<Void>> resetUnreadCount(
            @RequestParam Long userId,
            @RequestParam Long contactId) {
        try {
            log.info("重置会话未读计数 - 用户ID: {}, 联系人ID: {}", userId, contactId);
            conversationService.resetUnreadCount(userId, contactId);

            // 同时重置Redis中的未读计数
            redisService.resetUnreadCount(userId, contactId);
            log.info("Redis中的未读计数已重置 - 用户ID: {}, 联系人ID: {}", userId, contactId);

            return ResponseEntity.ok(Result.success());
        } catch (Exception e) {
            log.error("重置会话未读计数失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Result.fail(e.getMessage()));
        }
    }

    /**
     * 增加会话未读计数
     * @param userId 用户ID
     * @param contactId 联系人ID
     * @return 操作结果
     */
    @PutMapping("/increment-unread")
    public ResponseEntity<Result<Void>> incrementUnreadCount(
            @RequestParam Long userId,
            @RequestParam Long contactId) {
        try {
            log.info("增加会话未读计数 - 用户ID: {}, 联系人ID: {}", userId, contactId);

            // 更新数据库中的未读计数
            conversationService.incrementUnreadCount(userId, contactId);

            // 同时更新Redis中的未读计数
            int newCount = redisService.incrementUnreadCount(userId, contactId);
            log.info("Redis中的未读计数已增加 - 用户ID: {}, 联系人ID: {}, 新计数: {}", userId, contactId, newCount);

            return ResponseEntity.ok(Result.success());
        } catch (Exception e) {
            log.error("增加会话未读计数失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(Result.fail(e.getMessage()));
        }
    }
}
