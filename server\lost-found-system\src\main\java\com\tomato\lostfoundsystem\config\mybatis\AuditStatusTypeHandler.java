package com.tomato.lostfoundsystem.config.mybatis;

import com.tomato.lostfoundsystem.enums.AuditStatusEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 审核状态枚举类型处理器
 * 用于在 MyBatis 中将 AuditStatusEnum 枚举类型与数据库字段进行转换
 */
@MappedTypes(AuditStatusEnum.class)
public class AuditStatusTypeHandler extends BaseTypeHandler<AuditStatusEnum> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, AuditStatusEnum parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getCode());
    }

    @Override
    public AuditStatusEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String code = rs.getString(columnName);
        return rs.wasNull() ? null : AuditStatusEnum.getByCode(code);
    }

    @Override
    public AuditStatusEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String code = rs.getString(columnIndex);
        return rs.wasNull() ? null : AuditStatusEnum.getByCode(code);
    }

    @Override
    public AuditStatusEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String code = cs.getString(columnIndex);
        return cs.wasNull() ? null : AuditStatusEnum.getByCode(code);
    }
}
