<template>
  <div class="admin-dashboard">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>失物统计</span>
            </div>
          </template>
          <div class="card-content">
            <div class="statistic-item">
              <div class="label">总数</div>
              <div class="value">{{ lostStats.total }}</div>
            </div>
            <div class="statistic-item">
              <div class="label">未找回</div>
              <div class="value">{{ lostStats.lost }}</div>
            </div>
            <div class="statistic-item">
              <div class="label">已找回</div>
              <div class="value">{{ lostStats.found }}</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>拾物统计</span>
            </div>
          </template>
          <div class="card-content">
            <div class="statistic-item">
              <div class="label">总数</div>
              <div class="value">{{ foundStats.total }}</div>
            </div>
            <div class="statistic-item">
              <div class="label">未认领</div>
              <div class="value">{{ foundStats.unclaimed }}</div>
            </div>
            <div class="statistic-item">
              <div class="label">已认领</div>
              <div class="value">{{ foundStats.claimed }}</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>用户统计</span>
            </div>
          </template>
          <div class="card-content">
            <div class="statistic-item">
              <div class="label">总用户数</div>
              <div class="value">{{ userStats.total }}</div>
            </div>
            <div class="statistic-item">
              <div class="label">今日新增</div>
              <div class="value">{{ userStats.today }}</div>
            </div>
            <div class="statistic-item">
              <div class="label">本月新增</div>
              <div class="value">{{ userStats.month }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 服务状态监控 -->
    <el-row style="margin-top: 20px">
      <el-col :span="24">
        <ServiceStatus />
      </el-col>
    </el-row>

    <!-- 最近失物和最近拾物部分已移除 -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import request from '@/utils/request'
import ServiceStatus from '@/components/admin/ServiceStatus.vue'

// 统计数据
const lostStats = ref({
  total: 0,
  lost: 0,
  found: 0
})

const foundStats = ref({
  total: 0,
  unclaimed: 0,
  claimed: 0
})

const userStats = ref({
  total: 0,
  today: 0,
  month: 0
})

// 最近数据已移除

// 获取统计数据
const fetchStats = async () => {
  try {
    const res = await request({
      url: '/admin/statistics',
      method: 'get'
    })

    if (res.code === 200) {
      const { lost, found, user } = res.data
      lostStats.value = lost
      foundStats.value = found
      userStats.value = user
    }
  } catch (error) {
    console.error('获取统计数据失败：', error)
  }
}

// 获取最近失物和拾物数据的函数已移除

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  padding: 10px 0;
}

.statistic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.statistic-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #606266;
}

.value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  font-weight: bold;
}

:deep(.el-table) {
  margin: -12px;
}
</style>