<template>
  <div class="log-viewer" :class="{ 'log-viewer-expanded': expanded }">
    <div class="log-viewer-header" @click="toggleExpanded">
      <span class="log-viewer-title">日志查看器</span>
      <span class="log-viewer-count">{{ filteredLogs.length }}</span>
      <!-- TODO: 当升级到 Element Plus 3.0.0 时，将 type="text" 改为 type="link" -->
      <el-button
        type="text"
        size="small"
        class="log-viewer-clear"
        @click.stop="clearLogs"
      >
        清除
      </el-button>
      <!-- TODO: 当升级到 Element Plus 3.0.0 时，将 type="text" 改为 type="link" -->
      <el-button
        type="text"
        size="small"
        class="log-viewer-toggle"
        :icon="expanded ? 'ArrowDown' : 'ArrowUp'"
      >
        {{ expanded ? '收起' : '展开' }}
      </el-button>
    </div>
    <div v-if="expanded" class="log-viewer-content">
      <div class="log-viewer-filters">
        <el-select v-model="levelFilter" placeholder="日志级别" clearable teleported>
          <el-option
            v-for="level in logLevels"
            :key="level.value"
            :label="level.label"
            :value="level.value"
          />
        </el-select>
        <el-input
          v-model="searchText"
          placeholder="搜索日志内容"
          clearable
          class="log-search"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="contextFilter" placeholder="上下文" clearable teleported>
          <el-option
            v-for="context in availableContexts"
            :key="context"
            :label="context"
            :value="context"
          />
        </el-select>
      </div>
      <div class="log-viewer-list">
        <div
          v-for="(log, index) in filteredLogs"
          :key="index"
          class="log-item"
          :class="'log-level-' + log.level.toLowerCase()"
        >
          <div class="log-item-header">
            <span class="log-timestamp">{{ formatTime(log.timestamp) }}</span>
            <span class="log-level" :class="'log-level-' + log.level.toLowerCase()">
              {{ log.level }}
            </span>
            <span v-if="log.context" class="log-context">{{ log.context }}</span>
          </div>
          <div class="log-message">
            <template v-for="(part, partIndex) in log.parts" :key="partIndex">
              <pre v-if="typeof part === 'object'" class="log-object">{{ JSON.stringify(part, null, 2) }}</pre>
              <span v-else>{{ part }}</span>
            </template>
          </div>
        </div>
        <div v-if="filteredLogs.length === 0" class="log-empty">
          <el-empty description="暂无日志" :image-size="60" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import logger, { LogLevel } from '@/utils/logger'

// 状态
const logs = ref([])
const expanded = ref(false)
const levelFilter = ref('')
const searchText = ref('')
const contextFilter = ref('')

// 日志级别选项
const logLevels = [
  { label: '调试', value: 'DEBUG' },
  { label: '信息', value: 'INFO' },
  { label: '警告', value: 'WARN' },
  { label: '错误', value: 'ERROR' }
]

// 可用的上下文
const availableContexts = computed(() => {
  const contexts = new Set()
  logs.value.forEach(log => {
    if (log.context) {
      contexts.add(log.context)
    }
  })
  return Array.from(contexts)
})

// 过滤后的日志
const filteredLogs = computed(() => {
  return logs.value.filter(log => {
    // 级别过滤
    if (levelFilter.value && log.level !== levelFilter.value) {
      return false
    }

    // 上下文过滤
    if (contextFilter.value && log.context !== contextFilter.value) {
      return false
    }

    // 文本搜索
    if (searchText.value) {
      const searchLower = searchText.value.toLowerCase()
      const messageStr = log.parts.map(part =>
        typeof part === 'object' ? JSON.stringify(part) : String(part)
      ).join(' ').toLowerCase()

      return messageStr.includes(searchLower)
    }

    return true
  })
})

// 切换展开/收起状态
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

// 清除日志
const clearLogs = () => {
  logs.value = []
}

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }) + '.' + String(date.getMilliseconds()).padStart(3, '0')
}

// 日志拦截器
const originalConsole = {
  log: console.log,
  info: console.info,
  warn: console.warn,
  error: console.error,
  debug: console.debug
}

// 拦截控制台日志
const interceptConsole = () => {
  console.log = (...args) => {
    addLog('INFO', args)
    originalConsole.log(...args)
  }

  console.info = (...args) => {
    addLog('INFO', args)
    originalConsole.info(...args)
  }

  console.warn = (...args) => {
    addLog('WARN', args)
    originalConsole.warn(...args)
  }

  console.error = (...args) => {
    addLog('ERROR', args)
    originalConsole.error(...args)
  }

  console.debug = (...args) => {
    addLog('DEBUG', args)
    originalConsole.debug(...args)
  }
}

// 添加日志
const addLog = (level, args, context = '') => {
  // 提取上下文
  let logContext = context

  // 限制日志数量，防止内存泄漏
  if (logs.value.length > 1000) {
    logs.value = logs.value.slice(-500)
  }

  // 使用普通对象而不是响应式对象，避免递归更新
  const newLog = {
    timestamp: Date.now(),
    level,
    context: logContext,
    parts: args.map(arg => {
      // 对于对象类型，进行深拷贝以避免响应式引用
      if (typeof arg === 'object' && arg !== null) {
        try {
          return JSON.parse(JSON.stringify(arg))
        } catch (e) {
          return String(arg)
        }
      }
      return arg
    })
  }

  // 使用非响应式方法添加日志
  setTimeout(() => {
    logs.value.push(newLog)
  }, 0)
}

// 监听自定义日志事件
const handleLogEvent = (event) => {
  const { level, context, args } = event.detail
  // 使用setTimeout避免递归更新
  setTimeout(() => {
    addLog(level, args, context)
  }, 0)
}

// 生命周期钩子
onMounted(() => {
  // 拦截控制台日志
  interceptConsole()

  // 监听自定义日志事件
  window.addEventListener('custom-log', handleLogEvent)

  // 添加日志监听器
  logger.setLogListener((level, context, args) => {
    addLog(level, args, context)
  })
})

onUnmounted(() => {
  // 恢复原始控制台方法
  console.log = originalConsole.log
  console.info = originalConsole.info
  console.warn = originalConsole.warn
  console.error = originalConsole.error
  console.debug = originalConsole.debug

  // 移除事件监听器
  window.removeEventListener('custom-log', handleLogEvent)

  // 移除日志监听器
  logger.removeLogListener()
})
</script>

<style scoped>
.log-viewer {
  position: fixed;
  bottom: 0;
  right: 20px;
  width: 300px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-bottom: none;
  border-radius: 4px 4px 0 0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  transition: all 0.3s;
}

.log-viewer-expanded {
  width: 80%;
  max-width: 1200px;
  height: 400px;
}

.log-viewer-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  cursor: pointer;
  user-select: none;
}

.log-viewer-title {
  font-weight: bold;
  flex: 1;
}

.log-viewer-count {
  background-color: #409eff;
  color: white;
  border-radius: 10px;
  padding: 2px 8px;
  font-size: 12px;
  margin-right: 8px;
}

.log-viewer-content {
  height: calc(100% - 37px);
  display: flex;
  flex-direction: column;
}

.log-viewer-filters {
  padding: 8px;
  display: flex;
  gap: 8px;
  border-bottom: 1px solid #ebeef5;
}

.log-search {
  flex: 1;
}

.log-viewer-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  background-color: #f8f9fa;
}

.log-item {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: #fff;
  border-left: 4px solid #909399;
}

.log-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.log-timestamp {
  color: #909399;
  margin-right: 8px;
}

.log-level {
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
  margin-right: 8px;
}

.log-context {
  background-color: #ecf5ff;
  color: #409eff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.log-message {
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: 13px;
}

.log-object {
  background-color: #f8f9fa;
  padding: 4px;
  border-radius: 2px;
  margin: 2px 0;
}

.log-level-debug {
  border-left-color: #909399;
}

.log-level-info {
  border-left-color: #67c23a;
}

.log-level-warn {
  border-left-color: #e6a23c;
}

.log-level-error {
  border-left-color: #f56c6c;
}

.log-level-debug.log-level {
  background-color: #f4f4f5;
  color: #909399;
}

.log-level-info.log-level {
  background-color: #f0f9eb;
  color: #67c23a;
}

.log-level-warn.log-level {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.log-level-error.log-level {
  background-color: #fef0f0;
  color: #f56c6c;
}

.log-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
}
</style>
