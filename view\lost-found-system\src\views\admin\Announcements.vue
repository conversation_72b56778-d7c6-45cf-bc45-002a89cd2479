<template>
  <div class="admin-announcements">
    <div class="page-header">
      <h2>系统公告管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        新建公告
      </el-button>
    </div>
    
    <el-card class="announcement-list-card">
      <el-table
        v-loading="loading"
        :data="announcements"
        style="width: 100%"
        border
      >
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="expand-content">
              <div class="content-preview" v-html="formatContent(row.content)"></div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="标题" min-width="200">
          <template #default="{ row }">
            <div class="title-cell">
              <span>{{ row.title }}</span>
              <el-tag 
                :type="getImportanceType(row)" 
                size="small"
                effect="plain"
              >
                {{ getImportanceText(row.importance) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="有效期" width="280">
          <template #default="{ row }">
            <div>
              <div>{{ formatDate(row.startTime) }}</div>
              <div>至</div>
              <div>{{ row.endTime ? formatDate(row.endTime) : '长期有效' }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="row.status === 'DRAFT'"
              type="success" 
              size="small"
              @click="handlePublish(row)"
            >
              发布
            </el-button>
            <el-button 
              type="primary" 
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 创建/编辑公告弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑公告' : '新建公告'"
      width="700px"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入公告标题" />
        </el-form-item>
        
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="6"
            placeholder="请输入公告内容"
          />
        </el-form-item>
        
        <el-form-item label="重要程度" prop="importance">
          <el-select v-model="form.importance" placeholder="请选择重要程度" style="width: 100%">
            <el-option label="普通" value="NORMAL" />
            <el-option label="重要" value="IMPORTANT" />
            <el-option label="紧急" value="URGENT" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="生效时间" prop="startTime">
          <el-date-picker
            v-model="form.startTime"
            type="datetime"
            placeholder="选择生效时间"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            type="datetime"
            placeholder="选择结束时间（可选）"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getAllAnnouncements, 
  createAnnouncement, 
  updateAnnouncement, 
  deleteAnnouncement,
  publishAnnouncement
} from '@/api/announcement'

const announcements = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref(null)

// 表单数据
const form = ref({
  id: null,
  title: '',
  content: '',
  importance: 'NORMAL',
  startTime: new Date(),
  endTime: null,
  status: 'DRAFT'
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度应在2-100个字符之间', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' }
  ],
  importance: [
    { required: true, message: '请选择重要程度', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择生效时间', trigger: 'change' }
  ]
}

// 获取公告列表
const fetchAnnouncements = async () => {
  loading.value = true
  try {
    const res = await getAllAnnouncements()
    if (res.code === 200) {
      announcements.value = res.data || []
    }
  } catch (error) {
    console.error('获取系统公告失败:', error)
    ElMessage.error('获取系统公告失败')
  } finally {
    loading.value = false
  }
}

// 显示创建公告弹窗
const showCreateDialog = () => {
  isEdit.value = false
  form.value = {
    id: null,
    title: '',
    content: '',
    importance: 'NORMAL',
    startTime: new Date(),
    endTime: null,
    status: 'DRAFT'
  }
  dialogVisible.value = true
}

// 处理编辑公告
const handleEdit = (row) => {
  isEdit.value = true
  form.value = {
    id: row.id,
    title: row.title,
    content: row.content,
    importance: row.importance,
    startTime: new Date(row.startTime),
    endTime: row.endTime ? new Date(row.endTime) : null,
    status: row.status
  }
  dialogVisible.value = true
}

// 处理删除公告
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除公告"${row.title}"吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteAnnouncement(row.id)
      if (res.code === 200) {
        ElMessage.success('删除成功')
        fetchAnnouncements()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      console.error('删除公告失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 处理发布公告
const handlePublish = (row) => {
  ElMessageBox.confirm(`确定要发布公告"${row.title}"吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(async () => {
    try {
      const res = await publishAnnouncement(row.id)
      if (res.code === 200) {
        ElMessage.success('发布成功')
        fetchAnnouncements()
      } else {
        ElMessage.error(res.message || '发布失败')
      }
    } catch (error) {
      console.error('发布公告失败:', error)
      ElMessage.error('发布失败')
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    submitting.value = true
    
    try {
      // 格式化日期
      const formData = {
        ...form.value,
        startTime: form.value.startTime.toISOString().replace('T', ' ').substring(0, 19),
        endTime: form.value.endTime ? form.value.endTime.toISOString().replace('T', ' ').substring(0, 19) : null
      }
      
      let res
      if (isEdit.value) {
        res = await updateAnnouncement(formData)
      } else {
        res = await createAnnouncement(formData)
      }
      
      if (res.code === 200) {
        ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
        dialogVisible.value = false
        fetchAnnouncements()
      } else {
        ElMessage.error(res.message || (isEdit.value ? '更新失败' : '创建失败'))
      }
    } catch (error) {
      console.error(isEdit.value ? '更新公告失败:' : '创建公告失败:', error)
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    } finally {
      submitting.value = false
    }
  })
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString()
}

// 格式化内容（将换行符转换为<br>）
const formatContent = (content) => {
  if (!content) return ''
  return content.replace(/\n/g, '<br>')
}

// 根据重要程度获取标签类型
const getImportanceType = (announcement) => {
  if (!announcement) return ''
  switch (announcement.importance) {
    case 'URGENT':
      return 'danger'
    case 'IMPORTANT':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取重要程度文本
const getImportanceText = (importance) => {
  switch (importance) {
    case 'URGENT':
      return '紧急'
    case 'IMPORTANT':
      return '重要'
    default:
      return '普通'
  }
}

// 根据状态获取标签类型
const getStatusType = (status) => {
  switch (status) {
    case 'PUBLISHED':
      return 'success'
    case 'DRAFT':
      return 'info'
    case 'EXPIRED':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'PUBLISHED':
      return '已发布'
    case 'DRAFT':
      return '草稿'
    case 'EXPIRED':
      return '已过期'
    default:
      return '未知'
  }
}

onMounted(() => {
  fetchAnnouncements()
})
</script>

<style scoped>
.admin-announcements {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.announcement-list-card {
  margin-bottom: 20px;
}

.title-cell {
  display: flex;
  align-items: center;
}

.title-cell span {
  margin-right: 10px;
}

.expand-content {
  padding: 15px;
}

.content-preview {
  line-height: 1.6;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>
