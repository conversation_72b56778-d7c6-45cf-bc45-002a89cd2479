# 失物招领系统服务管理指南

本文档提供了管理失物招领系统各项服务的命令和说明，包括启动、停止和状态检查等操作。

## 目录

- [服务概览](#服务概览)
- [启动服务](#启动服务)
- [停止服务](#停止服务)
- [检查服务状态](#检查服务状态)
- [日志查看](#日志查看)
- [常见问题](#常见问题)

## 服务概览

失物招领系统包含以下几个主要服务组件：

1. **后端服务**：基于Spring Boot的RESTful API服务
2. **前端服务**：基于Vue.js的Web应用
3. **CLIP+FAISS服务**：用于图像匹配的API服务
4. **Kafka**：用于消息队列和事件处理
5. **Redis**：用于缓存和会话管理

## 启动服务

### 启动所有服务

如果你想一次性启动所有服务，可以使用以下命令：

```bash
# 进入项目根目录
cd /path/to/your/project

# 运行综合启动脚本
./start_services.sh
```

### 单独启动各服务

#### 启动后端服务

```bash
cd /path/to/your/project/server/lost-found-system
nohup mvn spring-boot:run > backend.log 2>&1 &
echo "Backend service started with PID: $!"
```

#### 启动前端服务

```bash
cd /path/to/your/project/view/lost-found-system
nohup npm run dev > frontend.log 2>&1 &
echo "Frontend service started with PID: $!"
```

#### 启动CLIP+FAISS服务

```bash
cd /path/to/your/project/clip_faiss_service
source clip_faiss_env/bin/activate
nohup python clip_faiss_api.py > clip_faiss.log 2>&1 &
echo "CLIP+FAISS service started with PID: $!"
```

#### 启动Kafka服务

```bash
# 先启动Zookeeper
cd /path/to/your/project/kafka
nohup bin/zookeeper-server-start.sh config/zookeeper.properties > zookeeper.log 2>&1 &
echo "Zookeeper started with PID: $!"

# 等待Zookeeper启动
sleep 10

# 启动Kafka
nohup bin/kafka-server-start.sh config/server.properties > kafka.log 2>&1 &
echo "Kafka started with PID: $!"
```

#### 启动Redis服务

```bash
# 如果使用系统服务
sudo systemctl start redis-server

# 或者手动启动
redis-server /path/to/your/redis.conf
```

## 停止服务

### 停止所有服务

如果你想一次性停止所有服务，可以使用以下命令：

```bash
# 进入项目根目录
cd /path/to/your/project

# 运行综合停止脚本
./stop_services.sh
```

### 单独停止各服务

#### 停止后端服务

```bash
pkill -f "java.*lost-found-system"
```

#### 停止前端服务

```bash
pkill -f "node.*dev"
```

#### 停止CLIP+FAISS服务

```bash
pkill -f "python.*clip_faiss_api.py"
```

#### 停止Kafka服务

```bash
# 停止Kafka
cd /path/to/your/project/kafka
bin/kafka-server-stop.sh

# 等待Kafka完全停止
sleep 5

# 停止Zookeeper
bin/zookeeper-server-stop.sh
```

#### 停止Redis服务

```bash
# 如果使用系统服务
sudo systemctl stop redis-server

# 或者使用Redis CLI
redis-cli shutdown
```

## 检查服务状态

### 检查所有服务状态

```bash
# 进入项目根目录
cd /path/to/your/project

# 运行状态检查脚本
./check_services.sh
```

### 单独检查各服务状态

#### 检查后端服务

```bash
ps aux | grep "java.*lost-found-system"
netstat -tuln | grep 8081
```

#### 检查前端服务

```bash
ps aux | grep "node.*dev"
netstat -tuln | grep 5173
```

#### 检查CLIP+FAISS服务

```bash
ps aux | grep "python.*clip_faiss_api.py"
netstat -tuln | grep 5000  # 假设CLIP+FAISS服务使用5000端口
```

#### 检查Kafka服务

```bash
# 检查Zookeeper
ps aux | grep "QuorumPeerMain"
netstat -tuln | grep 2181

# 检查Kafka
ps aux | grep "kafka.Kafka"
netstat -tuln | grep 9092
```

#### 检查Redis服务

```bash
# 如果使用系统服务
systemctl status redis-server

# 或者使用Redis CLI
redis-cli ping  # 如果返回PONG，则表示Redis正在运行
```

## 日志查看

### 查看实时日志

```bash
# 后端日志
tail -f /path/to/your/project/logs/backend.log

# 前端日志
tail -f /path/to/your/project/logs/frontend.log

# CLIP+FAISS日志
tail -f /path/to/your/project/logs/clip_faiss.log

# Kafka日志
tail -f /path/to/your/project/logs/kafka.log

# Zookeeper日志
tail -f /path/to/your/project/logs/zookeeper.log
```

### 查看最近的错误

```bash
# 后端错误
grep "ERROR" /path/to/your/project/logs/backend.log | tail -n 50

# 前端错误
grep "error" /path/to/your/project/logs/frontend.log | tail -n 50

# CLIP+FAISS错误
grep "ERROR\|Error\|error" /path/to/your/project/logs/clip_faiss.log | tail -n 50
```

## 常见问题

### 服务无法启动

1. **检查端口占用**：
   ```bash
   netstat -tuln | grep <port_number>
   ```
   如果端口已被占用，可以终止占用该端口的进程或修改服务配置使用其他端口。

2. **检查日志**：
   查看服务的日志文件，了解启动失败的原因。

3. **检查依赖服务**：
   确保所有依赖服务（如数据库、Redis、Kafka等）都已正常运行。

### 服务意外停止

1. **检查系统资源**：
   ```bash
   top
   df -h
   free -m
   ```
   确保系统有足够的CPU、磁盘空间和内存。

2. **检查日志**：
   查看服务的日志文件，了解服务停止的原因。

3. **设置自动重启**：
   考虑使用systemd服务或其他监控工具，在服务意外停止时自动重启。

### 无法连接到服务

1. **检查服务状态**：
   确保服务正在运行。

2. **检查网络配置**：
   确保防火墙和安全组设置允许相应的端口访问。

3. **检查服务绑定地址**：
   确保服务绑定到正确的地址（如0.0.0.0而不是127.0.0.1，以允许远程访问）。

---

**注意**：请根据实际部署情况，替换上述命令中的路径和端口号。

**最后更新**：2025年5月3日
