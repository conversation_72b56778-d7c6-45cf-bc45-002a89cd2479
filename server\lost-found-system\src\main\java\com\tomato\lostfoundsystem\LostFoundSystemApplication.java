package com.tomato.lostfoundsystem;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@MapperScan("com.tomato.lostfoundsystem.mapper")
@EnableAsync  // 启用异步方法支持
@EnableScheduling  // 启用定时任务支持
public class LostFoundSystemApplication {

    public static void main(String[] args) {
        SpringApplication.run(LostFoundSystemApplication.class, args);
    }

}
