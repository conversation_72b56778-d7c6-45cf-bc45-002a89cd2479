spring:
  datasource:
    url: jdbc:h2:mem:testdb;MODE=MySQL;DB_CLOSE_DELAY=-1
    username: sa
    password:
    driver-class-name: org.h2.Driver
  sql:
    init:
      mode: always
      schema-locations: classpath:schema-test.sql

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.tomato.lostfoundsystem.entity
  configuration:
    map-underscore-to-camel-case: true

# 阿里云OSS配置（测试环境使用模拟值）
aliyun:
  oss:
    endpoint: test-endpoint
    access-key-id: test-key
    access-key-secret: test-secret
    bucket-name: test-bucket

# 文件上传配置
file:
  upload:
    max-size: 524288000  # 500MB，单位字节
    allowed-types:
      image: jpg,jpeg,png,gif,webp,bmp,tiff
      video: mp4,mov,avi,wmv,flv,mkv,webm,m4v,3gp
      audio: mp3,wav,ogg,m4a,aac,flac,wma
      document: pdf,doc,docx,xls,xlsx,ppt,pptx,txt,csv,rtf,zip,rar,7z
