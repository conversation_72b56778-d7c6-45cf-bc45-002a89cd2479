import request from '@/utils/request'

// 获取智能匹配服务状态
export function getClipServiceStatus() {
  return request({
    url: '/admin/services/clip/status',
    method: 'get'
  })
}

// 启动智能匹配服务
export function startClipService() {
  return request({
    url: '/admin/services/clip/start',
    method: 'post'
  })
}

// 停止智能匹配服务
export function stopClipService() {
  return request({
    url: '/admin/services/clip/stop',
    method: 'post'
  })
}

// 重启智能匹配服务
export function restartClipService() {
  return request({
    url: '/admin/services/clip/restart',
    method: 'post'
  })
}

// 生成特征向量并构建索引
export function regenerateAllFeatureVectors() {
  return request({
    url: '/admin/services/clip/regenerate-vectors',
    method: 'post'
  })
}
