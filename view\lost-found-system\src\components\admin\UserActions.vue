<template>
  <div class="user-actions">
    <el-button-group>
      <el-tooltip content="查看详情" placement="top">
        <el-button
          type="primary"
          :icon="View"
          circle
          @click="$emit('view', user)"
        />
      </el-tooltip>

      <el-tooltip v-if="canResetPassword" content="重置密码" placement="top">
        <el-button
          type="warning"
          :icon="Key"
          circle
          @click="$emit('reset-password', user)"
        />
      </el-tooltip>

      <el-tooltip v-if="canEditRole" content="修改角色" placement="top">
        <el-button
          type="info"
          :icon="UserFilled"
          circle
          @click="$emit('edit-role', user)"
        />
      </el-tooltip>

      <el-tooltip v-if="canToggleStatus" :content="user.isActive ? '禁用账号' : '启用账号'" placement="top">
        <el-button
          :type="user.isActive ? 'danger' : 'success'"
          :icon="user.isActive ? Lock : Unlock"
          circle
          @click="$emit('toggle-status', user)"
        />
      </el-tooltip>
    </el-button-group>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessageBox } from 'element-plus'
import {
  View,
  UserFilled,
  Key,
  Lock,
  Unlock
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores'
import { isSuperAdmin } from '@/utils/auth'

const props = defineProps({
  user: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'view',
  'reset-password',
  'toggle-status',
  'edit-role'
])

const userStore = useUserStore()

// 是否是当前登录用户
const isCurrentUser = computed(() => userStore.userInfo?.id === props.user.id)

// 是否是超级管理员
const isTargetSuperAdmin = computed(() => props.user.role === 'SUPER_ADMIN')

// 是否可以重置密码
const canResetPassword = computed(() => {
  // 超级管理员不能被修改
  if (isTargetSuperAdmin.value) return false
  // 普通用户可以重置密码
  return props.user.role === 'USER'
})

// 是否可以修改角色
const canEditRole = computed(() => {
  // 超级管理员不能被修改
  if (isTargetSuperAdmin.value) return false
  // 不能修改自己的角色
  if (isCurrentUser.value) return false
  // 只有超级管理员可以修改角色
  return isSuperAdmin()
})

// 是否可以切换状态
const canToggleStatus = computed(() => {
  // 超级管理员不能被修改
  if (isTargetSuperAdmin.value) return false
  // 不能修改自己的状态
  if (isCurrentUser.value) return false
  // 只有超级管理员可以切换状态
  return isSuperAdmin()
})
</script>

<style scoped>
.user-actions {
  display: flex;
  gap: 8px;
}

.el-button-group {
  display: flex;
  gap: 8px;
}

:deep(.el-button) {
  padding: 8px;
}

:deep(.el-button.is-circle) {
  padding: 8px;
}
</style>