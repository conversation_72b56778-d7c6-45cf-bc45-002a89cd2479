package com.tomato.lostfoundsystem.service.Impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.dto.NotificationDTO;
import com.tomato.lostfoundsystem.dto.ReadReceiptDTO;
import com.tomato.lostfoundsystem.service.KafkaConsumerService;
import com.tomato.lostfoundsystem.service.NotificationEventService;
import com.tomato.lostfoundsystem.service.RedisService;
import com.tomato.lostfoundsystem.websocket.NotificationWebSocketHandler;
import org.springframework.messaging.simp.user.SimpUser;
import org.springframework.messaging.simp.user.SimpUserRegistry;

import java.util.List;

import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.ConnectException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;

@Slf4j
@Service
public class KafkaConsumerServiceImpl implements KafkaConsumerService {

    private final SimpMessagingTemplate messagingTemplate;
    private final ObjectMapper objectMapper;
    private final NotificationWebSocketHandler notificationWebSocketHandler;

    @Autowired
    private RedisService redisService;

    @Autowired
    private NotificationEventService notificationEventService;

    @Autowired
    private com.tomato.lostfoundsystem.mapper.UserMapper userMapper;

    @Autowired
    private SimpUserRegistry simpUserRegistry;





    // 构造函数注入依赖
    public KafkaConsumerServiceImpl(
            SimpMessagingTemplate messagingTemplate,
            NotificationWebSocketHandler notificationWebSocketHandler) {
        this.messagingTemplate = messagingTemplate;
        this.notificationWebSocketHandler = notificationWebSocketHandler;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    // 禁用此监听器，避免重复消费
    // @KafkaListener(topics = "chat-topic", groupId = "chat-consumer-group")
    public void consumeOfflineMessage(
            String message,
            @Header("kafka_receivedPartitionId") int partition,
            @Header("kafka_offset") long offset,
            Acknowledgment acknowledgment) {
        try {
            log.info("从Kafka主题消费离线消息: {}, 分区: {}, 偏移量: {}", message, partition, offset);

            // 尝试将消息解析为JSON
            try {
                // 首先尝试解析为ChatMessageEnvelope对象
                try {
                    // 尝试解析为ChatMessageEnvelope
                    JsonNode jsonNode = objectMapper.readTree(message);

                    // 检查是否是ChatMessageEnvelope格式
                    if (jsonNode.has("payload") && jsonNode.get("payload").isObject()) {
                        log.info("检测到ChatMessageEnvelope格式消息");

                        // 从信封中提取payload
                        JsonNode payload = jsonNode.get("payload");
                        String payloadStr = payload.toString();

                        // 解析payload为MessageDTO
                        MessageDTO messageDTO = objectMapper.readValue(payloadStr, MessageDTO.class);

                        if (messageDTO != null && messageDTO.getReceiverId() != null) {
                            String receiverId = messageDTO.getReceiverId().toString();

                            // 获取接收者的用户名
                            String receiverUsername = getUsernameById(messageDTO.getReceiverId());

                            // 检查用户是否在线 - 首先检查Redis
                            boolean isUserOnline = redisService.isUserOnline(messageDTO.getReceiverId());

                            // 如果Redis显示用户在线，再检查WebSocket连接
                            boolean isWebSocketConnected = false;
                            if (isUserOnline && receiverUsername != null) {
                                try {
                                    // 获取所有在线用户
                                    Set<SimpUser> users = simpUserRegistry.getUsers();
                                    log.info("【Kafka消费者】当前WebSocket在线用户数量: {}", users.size());

                                    // 检查接收者是否在WebSocket在线用户列表中
                                    for (SimpUser user : users) {
                                        log.info("【Kafka消费者】WebSocket在线用户: {}", user.getName());
                                        if (receiverUsername.equals(user.getName())) {
                                            isWebSocketConnected = true;
                                            break;
                                        }
                                    }

                                    log.info("【Kafka消费者】接收者 {} 是否WebSocket在线: {}", receiverUsername, isWebSocketConnected);
                                } catch (Exception e) {
                                    log.error("【Kafka消费者】检查WebSocket在线状态出错", e);
                                }
                            }

                            // 只有当Redis和WebSocket都显示用户在线时，才发送消息
                            if (isUserOnline && isWebSocketConnected) {
                                log.info("【Kafka消费者】接收者已上线，推送ChatMessageEnvelope的payload到用户: {}", receiverId);

                                if (receiverUsername != null) {
                                    // 使用用户名推送payload到标准路径
                                    messagingTemplate.convertAndSendToUser(receiverUsername, "/queue/private", messageDTO);
                                    log.info("【Kafka消费者】推送消息: 用户名={}", receiverUsername);
                                } else {
                                    log.error("【Kafka消费者】无法获取接收者用户名，使用ID推送: {}", receiverId);
                                    // 回退到使用ID
                                    messagingTemplate.convertAndSendToUser(receiverId, "/queue/private", messageDTO);
                                }

                                log.info("【Kafka消费者】成功推送ChatMessageEnvelope的payload到用户: {}", receiverId);

                                // 处理成功，确认消息
                                acknowledgment.acknowledge();
                                return;
                            } else {
                                log.info("【Kafka消费者】接收者仍然离线，保留消息在Kafka中: {}, Redis在线: {}, WebSocket在线: {}",
                                    receiverId, isUserOnline, isWebSocketConnected);
                                // 不确认消息，让它重新消费
                                return;
                            }
                        }
                    }
                } catch (Exception envelopeException) {
                    log.debug("消息不是ChatMessageEnvelope格式，尝试直接解析为MessageDTO: {}", envelopeException.getMessage());
                }

                // 如果不是ChatMessageEnvelope格式，尝试直接解析为MessageDTO对象
                MessageDTO messageDTO = objectMapper.readValue(message, MessageDTO.class);

                // 如果解析成功，说明是完整的消息对象
                if (messageDTO != null && messageDTO.getReceiverId() != null) {
                    String receiverId = messageDTO.getReceiverId().toString();

                    // 获取接收者的用户名
                    String receiverUsername = getUsernameById(messageDTO.getReceiverId());

                    // 检查用户是否在线 - 首先检查Redis
                    boolean isUserOnline = redisService.isUserOnline(messageDTO.getReceiverId());

                    // 如果Redis显示用户在线，再检查WebSocket连接
                    boolean isWebSocketConnected = false;
                    if (isUserOnline && receiverUsername != null) {
                        try {
                            // 获取所有在线用户
                            Set<SimpUser> users = simpUserRegistry.getUsers();
                            log.info("【Kafka消费者】当前WebSocket在线用户数量: {}", users.size());

                            // 检查接收者是否在WebSocket在线用户列表中
                            for (SimpUser user : users) {
                                log.info("【Kafka消费者】WebSocket在线用户: {}", user.getName());
                                if (receiverUsername.equals(user.getName())) {
                                    isWebSocketConnected = true;
                                    break;
                                }
                            }

                            log.info("【Kafka消费者】接收者 {} 是否WebSocket在线: {}", receiverUsername, isWebSocketConnected);
                        } catch (Exception e) {
                            log.error("【Kafka消费者】检查WebSocket在线状态出错", e);
                        }
                    }

                    // 只有当Redis和WebSocket都显示用户在线时，才发送消息
                    if (isUserOnline && isWebSocketConnected) {
                        log.info("【Kafka消费者】接收者已上线，推送完整消息对象到用户: {}", receiverId);

                        if (receiverUsername != null) {
                            // 使用用户名推送到标准路径
                            messagingTemplate.convertAndSendToUser(receiverUsername, "/queue/private", messageDTO);
                            log.info("【Kafka消费者】推送完整消息对象: 用户名={}", receiverUsername);
                        } else {
                            log.error("【Kafka消费者】无法获取接收者用户名，使用ID推送完整消息对象: {}", receiverId);
                            // 回退到使用ID
                            messagingTemplate.convertAndSendToUser(receiverId, "/queue/private", messageDTO);
                        }

                        log.info("【Kafka消费者】成功推送完整消息对象到用户: {}", receiverId);

                        // 处理成功，确认消息
                        acknowledgment.acknowledge();
                    } else {
                        log.info("【Kafka消费者】接收者仍然离线，保留消息在Kafka中: {}, Redis在线: {}, WebSocket在线: {}",
                            receiverId, isUserOnline, isWebSocketConnected);
                        // 不确认消息，让它重新消费
                    }
                    return;
                }
            } catch (Exception e) {
                // 解析失败，可能是简单文本消息，继续处理
                log.debug("消息不是完整的MessageDTO对象，尝试提取接收者ID: {}", e.getMessage());
            }

            // 解析消息，获取接收者ID
            String receiverId = extractReceiverId(message);
            if (receiverId == null || receiverId.isEmpty()) {
                log.error("无法从消息中提取接收者ID: {}", message);
                // 无法处理的消息，确认并记录错误
                acknowledgment.acknowledge();
                return;
            }

            // 检查用户是否在线
            try {
                Long receiverIdLong = Long.parseLong(receiverId);
                if (!redisService.isUserOnline(receiverIdLong)) {
                    log.info("接收者离线，保留消息在Kafka中: {}", receiverId);
                    // 不确认消息，让它重新消费
                    return;
                }
            } catch (NumberFormatException e) {
                log.error("接收者ID格式错误: {}", receiverId);
                // 无法处理的消息，确认并记录错误
                acknowledgment.acknowledge();
                return;
            }

            log.info("推送简单文本消息到用户: {}", receiverId);

            try {
                // 尝试获取接收者的用户名
                Long receiverIdLong = Long.parseLong(receiverId);
                String receiverUsername = getUsernameById(receiverIdLong);

                if (receiverUsername != null) {
                    // 使用用户名推送到标准路径
                    messagingTemplate.convertAndSendToUser(receiverUsername, "/queue/private", message);
                    log.info("推送简单文本消息: 用户名={}", receiverUsername);
                } else {
                    log.error("无法获取接收者用户名，使用ID推送简单文本消息: {}", receiverId);
                    // 回退到使用ID
                    messagingTemplate.convertAndSendToUser(receiverId, "/queue/private", message);
                }
            } catch (Exception e) {
                log.error("推送简单文本消息时出错: {}", e.getMessage(), e);
                // 回退到使用ID
                messagingTemplate.convertAndSendToUser(receiverId, "/queue/private", message);
            }

            log.info("成功推送简单文本消息到用户: {}", receiverId);

            // 处理成功，确认消息
            acknowledgment.acknowledge();
        } catch (Exception e) {
            log.error("从Kafka主题消费离线消息时出错: {}. 错误: {}", message, e.getMessage(), e);

            // 根据错误类型决定是否确认
            if (isRecoverableError(e)) {
                log.warn("可恢复错误，消息将重新消费");
                // 不确认消息，让它重新消费
            } else {
                log.error("不可恢复错误，确认消息并记录失败");
                acknowledgment.acknowledge();
            }
        }
    }

    // 判断错误是否可恢复
    private boolean isRecoverableError(Exception e) {
        // 网络错误、连接超时等通常是可恢复的
        return e instanceof IOException ||
               e instanceof TimeoutException ||
               e instanceof ConnectException;
    }

    /**
     * 根据用户ID获取用户名
     * @param userId 用户ID
     * @return 用户名，如果未找到则返回null
     */
    private String getUsernameById(Long userId) {
        try {
            if (userId == null) {
                log.warn("尝试获取用户名时收到空的用户ID");
                return null;
            }

            // 从数据库中查询用户
            com.tomato.lostfoundsystem.entity.User user = userMapper.findById(userId);
            if (user != null) {
                return user.getUsername();
            } else {
                log.warn("未找到用户ID对应的用户: {}", userId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取用户名时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    // 从消息中提取接收者ID
    private String extractReceiverId(String message) {
        try {
            // 尝试解析JSON
            JsonNode jsonNode = objectMapper.readTree(message);

            // 首先检查是否是ChatMessageEnvelope格式
            if (jsonNode.has("payload") && jsonNode.get("payload").isObject()) {
                log.info("检测到ChatMessageEnvelope格式消息");
                JsonNode payload = jsonNode.get("payload");

                // 从payload中提取receiverId
                if (payload.has("receiverId")) {
                    return payload.get("receiverId").asText();
                }

                // 从payload中尝试其他可能的字段名
                if (payload.has("to")) {
                    return payload.get("to").asText();
                }

                if (payload.has("receiver")) {
                    return payload.get("receiver").asText();
                }
            }

            // 如果不是ChatMessageEnvelope格式，尝试直接获取receiverId字段
            if (jsonNode.has("receiverId")) {
                return jsonNode.get("receiverId").asText();
            }

            // 如果没有receiverId字段，尝试其他可能的字段名
            if (jsonNode.has("to")) {
                return jsonNode.get("to").asText();
            }

            if (jsonNode.has("receiver")) {
                return jsonNode.get("receiver").asText();
            }

            // 如果都没有找到，返回null
            log.warn("无法从JSON消息中提取接收者ID: {}", message);
            return null;
        } catch (Exception e) {
            // 如果不是JSON格式，尝试其他方式提取
            log.warn("消息不是JSON格式，尝试其他方式提取接收者ID: {}", e.getMessage());

            // 这里可以添加其他提取方式，例如正则表达式等
            // 简单起见，这里返回null
            return null;
        }
    }

    /**
     * 监听已读回执主题
     */
    @Override
    @KafkaListener(topics = "chat-read-receipts", groupId = "read-receipt-consumer-group")
    public void consumeReadReceipt(
            String message,
            @Header("kafka_receivedPartitionId") int partition,
            @Header("kafka_offset") long offset,
            Acknowledgment acknowledgment) {
        try {
            log.info("从chat-read-receipts消费已读回执: {}, 分区: {}, 偏移量: {}", message, partition, offset);

            // 解析已读回执
            com.tomato.lostfoundsystem.dto.ReadReceiptDTO readReceipt =
                objectMapper.readValue(message, com.tomato.lostfoundsystem.dto.ReadReceiptDTO.class);

            // 获取必要参数
            List<Long> messageIds = readReceipt.getMessageIds();
            Long readerId = readReceipt.getReaderId();
            Long senderId = readReceipt.getSenderId();

            if (messageIds == null || messageIds.isEmpty()) {
                log.warn("已读回执中没有消息ID: {}", readReceipt);
                acknowledgment.acknowledge();
                return;
            }

            if (readerId == null) {
                log.warn("已读回执中没有读者ID: {}", readReceipt);
                acknowledgment.acknowledge();
                return;
            }

            if (senderId == null) {
                log.warn("已读回执中没有发送者ID: {}", readReceipt);
                acknowledgment.acknowledge();
                return;
            }

            log.info("处理已读回执 - 读者ID: {}, 发送者ID: {}, 消息数量: {}",
                    readerId, senderId, messageIds.size());

            // 1. 批量更新数据库中的已读状态
            try {
                for (Long messageId : messageIds) {
                    // 调用服务层方法标记消息为已读
                    redisService.storeReadMessage(readerId, messageId);
                }
                log.info("已更新Redis缓存中的已读状态 - 读者ID: {}, 消息数量: {}",
                        readerId, messageIds.size());
            } catch (Exception e) {
                log.error("更新已读状态失败: {}", e.getMessage(), e);
                // 继续处理，因为即使数据库更新失败，我们仍然可以尝试发送已读回执
            }

            // 2. 检查发送者是否在线
            boolean isSenderOnline = redisService.isUserOnline(senderId);
            boolean isWebSocketConnected = false;

            // 检查WebSocket连接状态
            try {
                // 获取发送者的用户名
                String senderUsername = getUsernameById(senderId);
                if (senderUsername != null) {
                    // 检查用户是否通过WebSocket连接
                    SimpUser simpUser = simpUserRegistry.getUser(senderUsername);
                    isWebSocketConnected = (simpUser != null && !simpUser.getSessions().isEmpty());
                    log.info("发送者WebSocket连接状态 - 用户名: {}, 已连接: {}",
                            senderUsername, isWebSocketConnected);
                } else {
                    log.warn("无法获取发送者用户名 - 发送者ID: {}", senderId);
                }
            } catch (Exception e) {
                log.error("检查WebSocket连接状态失败: {}", e.getMessage(), e);
            }

            // 3. 如果发送者在线，通过WebSocket发送已读回执
            if (isSenderOnline && isWebSocketConnected) {
                try {
                    // 获取发送者的用户名
                    String senderUsername = getUsernameById(senderId);
                    if (senderUsername != null) {
                        // 使用用户名发送已读回执
                        messagingTemplate.convertAndSendToUser(
                            senderUsername,
                            "/queue/read-receipts",
                            readReceipt
                        );
                        log.info("已通过WebSocket发送已读回执 - 发送者用户名: {}", senderUsername);
                    } else {
                        // 回退到使用ID
                        messagingTemplate.convertAndSendToUser(
                            senderId.toString(),
                            "/queue/read-receipts",
                            readReceipt
                        );
                        log.info("已通过WebSocket发送已读回执 - 发送者ID: {}", senderId);
                    }
                } catch (Exception e) {
                    log.error("发送已读回执失败: {}", e.getMessage(), e);
                }
            } else {
                log.info("发送者离线或WebSocket未连接，已读回执将在用户上线时发送 - 发送者ID: {}, Redis在线: {}, WebSocket在线: {}",
                        senderId, isSenderOnline, isWebSocketConnected);
                // 可以将已读回执存储在Redis中，等用户上线时发送
            }

            // 确认消息
            acknowledgment.acknowledge();
        } catch (Exception e) {
            log.error("处理已读回执失败: {}", e.getMessage(), e);

            // 根据错误类型决定是否确认
            if (isRecoverableError(e)) {
                log.warn("可恢复错误，已读回执将重新消费");
                // 不确认消息，让它重新消费
            } else {
                log.error("不可恢复错误，确认已读回执并记录失败");
                acknowledgment.acknowledge();
            }
        }
    }

    /**
     * 监听通知主题
     */
    @KafkaListener(topics = "notification-topic", groupId = "notification-consumer-group")
    public void consumeNotification(
            String message,
            @Header("kafka_receivedPartitionId") int partition,
            @Header("kafka_offset") long offset,
            Acknowledgment acknowledgment) {
        try {
            log.info("从notification-topic消费通知消息: {}, 分区: {}, 偏移量: {}", message, partition, offset);

            // 解析消息
            JsonNode node = objectMapper.readTree(message);

            // 提取接收者ID
            Long receiverId = null;
            if (node.has("receiverId")) {
                receiverId = node.get("receiverId").asLong();
            }

            if (receiverId == null) {
                log.error("通知消息中没有接收者ID: {}", message);
                acknowledgment.acknowledge();
                return;
            }

            // 检查用户是否在线
            boolean isUserOnline = redisService.isUserOnline(receiverId);

            if (!isUserOnline) {
                log.info("用户仍然离线，保留通知消息在Kafka中: {}", receiverId);
                // 不确认消息，让它重新消费
                return;
            }

            // 用户在线，推送通知
            String title = node.has("title") ? node.get("title").asText() : "系统通知";
            String content = node.has("message") ? node.get("message").asText() : "";

            // 获取通知ID（如果有）
            Long notificationId = null;
            if (node.has("notificationId")) {
                notificationId = node.get("notificationId").asLong();
            }

            // 检查通知是否已经发送过
            if (notificationId != null && notificationEventService.isNotificationSent(receiverId, notificationId)) {
                log.info("通知已经发送过，跳过重复发送 - 用户ID: {}, 通知ID: {}", receiverId, notificationId);
                acknowledgment.acknowledge();
                return;
            }

            // 创建通知对象
            NotificationDTO notification = new NotificationDTO();
            notification.setUserId(receiverId);
            notification.setTitle(title);
            notification.setMessage(content);
            notification.setStatus("UNREAD");
            notification.setId(notificationId);

            // 通过WebSocket推送通知
            notificationWebSocketHandler.sendNotification(receiverId, notification);

            // 如果提供了通知ID，标记为已发送
            if (notificationId != null) {
                notificationEventService.markNotificationAsSent(receiverId, notificationId, 300); // 5分钟过期时间
            }

            log.info("成功推送通知到用户: {}, 通知ID: {}", receiverId, notificationId != null ? notificationId : "未提供");

            // 确认消息
            acknowledgment.acknowledge();
        } catch (Exception e) {
            log.error("处理通知消息失败: {}", e.getMessage(), e);

            // 根据错误类型决定是否确认
            if (isRecoverableError(e)) {
                log.warn("可恢复错误，通知消息将重新消费");
                // 不确认消息，让它重新消费
            } else {
                log.error("不可恢复错误，确认通知消息并记录失败");
                acknowledgment.acknowledge();
            }
        }
    }


}

