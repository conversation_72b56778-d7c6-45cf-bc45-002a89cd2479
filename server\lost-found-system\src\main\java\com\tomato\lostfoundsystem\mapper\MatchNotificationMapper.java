package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.MatchNotification;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 匹配通知Mapper接口
 */
@Mapper
public interface MatchNotificationMapper {

    /**
     * 插入匹配通知
     *
     * @param notification 通知对象
     * @return 影响的行数
     */
    @Insert("INSERT INTO match_notifications(user_id, match_history_id, item_id, item_type, similarity, title, content, is_read, " +
            "metadata, match_type, text_to_text_similarity, text_to_image_similarity, image_to_text_similarity, image_to_image_similarity) " +
            "VALUES(#{userId}, #{matchHistoryId}, #{itemId}, #{itemType}, #{similarity}, #{title}, #{content}, #{isRead}, " +
            "#{metadata}, #{matchType}, #{textToTextSimilarity}, #{textToImageSimilarity}, #{imageToTextSimilarity}, #{imageToImageSimilarity})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertNotification(MatchNotification notification);

    /**
     * 根据用户ID查询通知列表
     *
     * @param userId 用户ID
     * @return 通知列表
     */
    @Select("SELECT * FROM match_notifications WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<MatchNotification> getNotificationsByUserId(Long userId);

    /**
     * 标记通知为已读
     *
     * @param notificationId 通知ID
     * @return 影响的行数
     */
    @Update("UPDATE match_notifications SET is_read = true, updated_at = NOW() WHERE id = #{notificationId}")
    int markAsRead(Long notificationId);

    /**
     * 获取用户未读通知数量
     *
     * @param userId 用户ID
     * @return 未读通知数量
     */
    @Select("SELECT COUNT(*) FROM match_notifications WHERE user_id = #{userId} AND is_read = false")
    int getUnreadCount(Long userId);

    /**
     * 检查是否已经为该匹配结果发送过通知
     *
     * @param userId 用户ID
     * @param itemId 物品ID
     * @param itemType 物品类型
     * @return 是否已发送通知
     */
    @Select("SELECT COUNT(*) FROM match_notifications WHERE user_id = #{userId} AND item_id = #{itemId} AND item_type = #{itemType}")
    int checkNotificationExists(@Param("userId") Long userId, @Param("itemId") Long itemId, @Param("itemType") String itemType);
}
