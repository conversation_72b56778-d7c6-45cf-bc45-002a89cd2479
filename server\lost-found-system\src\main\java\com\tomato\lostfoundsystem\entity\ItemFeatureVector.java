package com.tomato.lostfoundsystem.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 物品特征向量实体类
 * 用于存储CLIP模型提取的图像和文本特征向量
 */
@Data
public class ItemFeatureVector {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 物品ID
     */
    private Long itemId;
    
    /**
     * 物品类型（LOST/FOUND）
     */
    private String itemType;
    
    /**
     * 图像特征向量（二进制存储）
     */
    private byte[] imageVector;
    
    /**
     * 文本特征向量（二进制存储）
     */
    private byte[] textVector;
    
    /**
     * 向量版本，用于兼容不同版本的模型
     */
    private String vectorVersion;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
