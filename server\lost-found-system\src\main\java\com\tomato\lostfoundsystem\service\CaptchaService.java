package com.tomato.lostfoundsystem.service;

import java.io.ByteArrayOutputStream;
import java.util.Map;

public interface CaptchaService {
    /**
     * 生成图形验证码
     * @return 包含验证码图像和验证码ID的Map
     * @throws Exception 如果生成验证码时发生错误
     */
    Map<String, Object> generateCaptchaImage() throws Exception;

    /**
     * 校验用户输入的验证码答案
     * @param userAnswer 用户输入的答案
     * @param captchaId 验证码ID
     * @return 验证结果
     */
    boolean validateCaptchaAnswer(String userAnswer, String captchaId);
}
