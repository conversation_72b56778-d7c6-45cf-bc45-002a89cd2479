```mermaid
flowchart TD
    subgraph "消息产生"
        A1[匹配通知] --> A[消息分发中心]
        A2[系统公告] --> A
        A3[用户聊天] --> A
    end
    
    A --> B{用户是否在线?}
    
    subgraph "实时通信"
        B -->|是| C[WebSocket推送]
        C --> D[消息状态更新]
    end
    
    subgraph "离线存储"
        B -->|否| E[Kafka消息队列]
        E --> F[等待用户上线]
        F --> G[消费离线消息]
        G --> C
    end
    
    subgraph "客户端"
        D --> H[通知中心显示]
        D --> I[消息已读标记]
    end
```
