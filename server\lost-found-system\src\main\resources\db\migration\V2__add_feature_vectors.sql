-- 添加特征向量存储表
CREATE TABLE IF NOT EXISTS `item_feature_vectors` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `item_id` bigint(20) NOT NULL COMMENT '物品ID',
  `item_type` enum('LOST','FOUND') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '物品类型',
  `image_vector` MEDIUMBLOB COMMENT '图像特征向量',
  `text_vector` MEDIUMBLOB COMMENT '文本特征向量',
  `vector_version` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'v1' COMMENT '向量版本',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_item_type_id` (`item_type`, `item_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加匹配历史表
CREATE TABLE IF NOT EXISTS `match_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `query_type` enum('IMAGE','TEXT','MIXED') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '查询类型',
  `query_image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '查询图片URL',
  `query_text` text COLLATE utf8mb4_unicode_ci COMMENT '查询文本',
  `item_type` enum('LOST','FOUND') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '查询物品类型',
  `result_count` int(11) DEFAULT 0 COMMENT '结果数量',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加匹配结果表
CREATE TABLE IF NOT EXISTS `match_results` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `match_history_id` bigint(20) NOT NULL COMMENT '匹配历史ID',
  `item_id` bigint(20) NOT NULL COMMENT '匹配到的物品ID',
  `item_type` enum('LOST','FOUND') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '匹配到的物品类型',
  `similarity_score` decimal(5,4) NOT NULL COMMENT '相似度分数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_match_history_id` (`match_history_id`),
  KEY `idx_item_type_id` (`item_type`, `item_id`),
  CONSTRAINT `fk_match_history` FOREIGN KEY (`match_history_id`) REFERENCES `match_history` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
