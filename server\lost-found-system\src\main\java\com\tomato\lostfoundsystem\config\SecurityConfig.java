package com.tomato.lostfoundsystem.config;

import com.tomato.lostfoundsystem.filter.JWTAuthenticationFilter;
import com.tomato.lostfoundsystem.filter.UserActivityFilter;
import com.tomato.lostfoundsystem.utils.JWTUtil;
import com.tomato.lostfoundsystem.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;  // 引入 SLF4J 注解
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

@Configuration
@EnableWebSecurity
@Slf4j  // 添加日志注解
public class SecurityConfig {

    private final JWTUtil jwtUtil;
    private final RedisUtil redisUtil;
    private final UserActivityFilter userActivityFilter;

    // 构造函数注入 RedisUtil、JWTUtil 和 UserActivityFilter
    public SecurityConfig(JWTUtil jwtUtil, RedisUtil redisUtil, UserActivityFilter userActivityFilter) {
        this.jwtUtil = jwtUtil;
        this.redisUtil = redisUtil;
        this.userActivityFilter = userActivityFilter;
    }


    /**
     * 密码加密器：用于注册和登录密码加密
     */
    @Bean
    public BCryptPasswordEncoder passwordEncoder() {
        log.info("Initializing BCryptPasswordEncoder bean for password encryption");
        return new BCryptPasswordEncoder();
    }

    /**
     * Spring Security 主配置
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        log.info("Configuring Spring Security filter chain");

        // 手动创建 JWTAuthenticationFilter 实例，并传入所需的依赖
        JWTAuthenticationFilter jwtAuthenticationFilter = new JWTAuthenticationFilter(jwtUtil, redisUtil);

        return http
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))  // 启用跨域配置
                .csrf(csrf -> csrf.disable())  // 禁用 CSRF
                .authorizeHttpRequests(auth -> auth
                        // 基本公开接口
                        .requestMatchers("/api/user/login", "/api/user/register", "/api/user/generateCaptcha").permitAll()
                        // 验证码相关接口
                        .requestMatchers("/api/verify/sendEmailCode", "/api/verify/sendPhoneCode").permitAll()
                        // 物品查询相关公开接口
                        .requestMatchers("/api/lost-items/search", "/api/found-items/search", "/api/lost-items/list", "/api/found-items/list").permitAll()
                        .requestMatchers("/api/lost-items/detail/**", "/api/found-items/detail/**").permitAll()
                        // 系统公告相关公开接口
                        .requestMatchers("/api/announcements/latest", "/api/announcements/valid").permitAll()
                        // 统计数据相关公开接口
                        .requestMatchers("/api/statistics/home").permitAll()
                        // WebSocket端点
                        .requestMatchers("/ws/**").permitAll()
                        // 测试接口
                        .requestMatchers("/api/test/**").permitAll()
                        // 管理员接口
                        .requestMatchers("/api/admin/**").hasAnyAuthority("ADMIN","SUPER_ADMIN")
                        // 其他接口需要认证
                        .anyRequest().authenticated()
                )
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)  // 将 JWT 认证过滤器添加到过滤链中
                .addFilterAfter(userActivityFilter, UsernamePasswordAuthenticationFilter.class)  // 添加用户活动过滤器
                .build();
    }



    /**
     * Spring Security 自定义跨域配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        log.info("Configuring CORS settings");

        CorsConfiguration config = new CorsConfiguration();
        config.setAllowedOriginPatterns(Arrays.asList(
            "http://localhost:5173",
            "http://127.0.0.1:5173",
            "http://***********:5173",
            "https://*.ngrok-free.app",
            "https://*.ngrok.io",
            "https://*.ngrok.app"
        )); // 允许前端地址和 ngrok 地址
        config.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        config.setAllowedHeaders(Arrays.asList("*"));
        config.setExposedHeaders(Arrays.asList("Authorization", "Content-Type"));
        config.setAllowCredentials(true); // 允许携带 cookie 或 token
        config.setMaxAge(3600L); // 预检请求有效期（秒）

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);  // 应用于所有接口
        return source;
    }
}
