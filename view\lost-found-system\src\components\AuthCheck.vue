<template>
  <div class="auth-check-wrapper">
    <slot v-if="isAuthenticated"></slot>
    <div v-else class="auth-check-container">
      <div class="auth-check-content">
        <el-icon class="auth-check-icon"><Lock /></el-icon>
        <h3>{{ title }}</h3>
        <p>{{ message }}</p>
        <el-button type="primary" @click="showLoginDialog">立即登录</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useUserStore, useAuthStore } from '@/stores'
import { Lock } from '@element-plus/icons-vue'

const props = defineProps({
  title: {
    type: String,
    default: '需要登录'
  },
  message: {
    type: String,
    default: '请登录后访问此功能'
  }
})

const userStore = useUserStore()
const authStore = useAuthStore()

// 检查用户是否已登录
const isAuthenticated = computed(() => userStore.isAuthenticated)

// 显示登录对话框
const showLoginDialog = () => {
  authStore.showLoginDialog({
    tab: 'login',
    onSuccess: (userInfo) => {
      console.log('登录成功，用户信息:', userInfo)
    }
  })
}
</script>

<style scoped>
.auth-check-wrapper {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
}

.auth-check-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 20px;
  width: 100%;
}

.auth-check-content {
  text-align: center;
  max-width: 400px;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.auth-check-icon {
  font-size: 48px;
  color: #909399;
  margin-bottom: 20px;
}

h3 {
  font-size: 20px;
  margin-bottom: 10px;
  color: #303133;
}

p {
  color: #606266;
  margin-bottom: 20px;
}
</style>
