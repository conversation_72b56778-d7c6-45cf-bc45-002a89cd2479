<template>
  <div class="chat-container">
    <AuthCheck title="需要登录" message="聊天功能需要登录后才能使用，请先登录">
      <!-- 左侧联系人列表 -->
      <ChatList
        v-model:contacts="contacts"
        v-model:loading="loadingContacts"
        :selected-contact="currentContact"
        :userId="userInfo.id"
        @select="selectContact"
        @create-chat="createNewChat"
        @refresh="fetchContacts"
        @conversation-action="handleConversationAction"
        ref="chatListRef"
      />

      <!-- 右侧聊天区域 -->
      <div class="chat-area" v-loading="loadingContacts && contacts.length === 0">
      <!-- 网络状态提示 -->
      <div v-if="!networkStatus.connected" class="network-status-alert" :class="{ 'reconnecting': networkStatus.reconnecting, 'offline': !networkStatus.reconnecting }">
        <el-icon><i class="el-icon-warning" /></el-icon>
        <span v-if="networkStatus.reconnecting">
          正在尝试重新连接... (第 {{ networkStatus.attempts }} 次尝试)
        </span>
        <span v-else>
          网络连接已断开，消息将在网络恢复后发送
        </span>
      </div>

      <!-- 离线消息提示 -->
      <div v-if="offlineMessageCount > 0 && networkStatus.connected" class="network-status-alert offline-message">
        <el-icon><i class="el-icon-info" /></el-icon>
        <span>{{ offlineMessageCount }}条消息等待发送</span>
        <el-button size="small" type="primary" @click="processOfflineMessages">立即发送</el-button>
      </div>

      <!-- 空状态提示 -->
      <el-empty v-if="!currentContact && !loadingContacts" description="请选择一个联系人开始聊天" />

      <template v-else-if="currentContact">
        <div class="chat-header">
        <div class="contact-info">
            <el-avatar
              :size="40"
              :src="currentContact?.avatar"
              :style="!currentContact?.avatar ? {
                backgroundColor: getContactAvatarColor(currentContact?.id),
                color: '#ffffff',
                fontWeight: '500',
                fontSize: '20px',
                textTransform: 'uppercase'
              } : {}"
            >
              <span>{{ currentContact?.avatarText || currentContact?.name?.substring(0, 1) }}</span>
            </el-avatar>
          <div class="contact-details">
              <span class="contact-name">{{ currentContact?.name }}</span>
              <span class="contact-status" :class="{ 'online-text': isContactOnline(currentContact?.id), 'offline-text': !isContactOnline(currentContact?.id) }">
                {{ isContactOnline(currentContact?.id) ? '在线' : '离线' }}
              </span>
            </div>
          </div>
          <div class="header-actions">
            <el-tooltip content="诊断连接" placement="bottom">
              <el-button link :icon="Monitor" @click="runWebSocketDiagnostics" />
            </el-tooltip>
            <el-tooltip content="WebSocket调试" placement="bottom">
              <el-button link :icon="View" @click="openWebSocketDebug" />
            </el-tooltip>
            <el-dropdown trigger="click" @command="(cmd) => handleConversationAction({ action: cmd, contact: currentContact })">
              <el-tooltip content="更多操作" placement="bottom">
                <el-button link :icon="More" />
              </el-tooltip>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="currentContact?.isPinned ? 'unpin' : 'pin'">
                    {{ currentContact?.isPinned ? '取消置顶' : '置顶' }}
                  </el-dropdown-item>
                  <el-dropdown-item :command="currentContact?.isMuted ? 'unmute' : 'mute'">
                    {{ currentContact?.isMuted ? '取消静音' : '静音' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="archive">归档</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <div class="chat-messages" ref="messagesContainer">
          <el-scrollbar
            v-loading="loadingMessages"
            element-loading-text="加载消息中..."
            ref="messageScrollbar"
            @scroll="handleMessageScroll"
          >
            <template v-if="messages.length === 0 && !loadingMessages">
              <div class="no-messages">
                <el-empty description="暂无聊天记录" :image-size="80">
                  <template #description>
                    <div class="empty-description">
                      <p>暂无聊天记录</p>
                      <p class="empty-tip">您可以发送消息开始聊天</p>
                    </div>
                  </template>
                </el-empty>
              </div>
            </template>

            <template v-else>
              <div v-if="messages.length > 0" class="load-more-button">
                <el-button v-if="!loadingMore" type="text" size="small" @click="loadMoreMessages">
                  <span>查看更多历史记录</span>
                </el-button>
                <div v-else class="loading-more-skeleton">
                  <el-skeleton :rows="1" animated style="padding: 10px; width: 150px;" />
                </div>
              </div>

              <div v-if="noMoreMessages && messages.length > 0" class="no-more-messages">
                <span>没有更多消息了</span>
              </div>

              <div v-for="(group, date) in groupedMessages" :key="date">
                <div class="message-date-divider">
                  <span>{{ date }}</span>
                </div>

                <ChatMessage
                  v-for="message in group"
                  :key="message.id"
                  :message="message"
                  :is-self="message.isSelf"
                  :avatar="message.isSelf ? userInfo.avatar : currentContact.avatar"
                  :avatar-text="message.isSelf ? (userInfo.username?.substring(0, 1).toUpperCase() || '我') : currentContact.avatarText"
                  :contact-id="message.isSelf ? userInfo.id : currentContact.id"
                  :is-anchor="message.id === anchorMessageId"
                  @resend="resendMessage"
                />
              </div>
            </template>
          </el-scrollbar>
        </div>

      <div class="wechat-chat-input">
        <!-- 工具栏图标上移 -->
        <div class="toolbar toolbar-top">
          <el-button link :icon="SmileFace" @click.stop="toggleEmojiPicker" />
          <el-button link :icon="Picture" @click="triggerImageUpload" />
          <el-button link :icon="Folder" @click="triggerFileUpload" />
          <el-button link :icon="Microphone" @click="triggerAudioUpload" />
          <el-button link :icon="VideoCamera" @click="triggerVideoUpload" />
          <input type="file" ref="imageInput" style="display:none" @change="handleImageUploadDirect" accept="image/*" multiple />
          <input type="file" ref="fileInput" style="display:none" @change="handleFileUploadDirect" multiple />
          <input type="file" ref="audioInput" style="display:none" @change="handleAudioUploadDirect" accept="audio/*" multiple />
          <input type="file" ref="videoInput" style="display:none" @change="handleVideoUploadDirect" accept="video/*" multiple />
        </div>
        <div v-if="previewList.length" class="preview-list">
          <div v-for="(item, idx) in previewList" :key="idx" class="preview-item">
            <img v-if="item.type==='IMAGE'" :src="item.url" class="preview-img" />
            <audio v-else-if="item.type==='AUDIO'" :src="item.url" controls class="preview-audio" />
            <video v-else-if="item.type==='VIDEO'" :src="item.url" controls class="preview-video" />
            <div v-else class="file-preview">
              <el-icon><Document /></el-icon>
              <span>{{ item.file.name }}</span>
            </div>
            <el-progress v-if="item.status==='uploading'" :percentage="item.progress" status="active" style="width:40px;margin:2px auto;" />
            <div v-if="item.status==='error'" class="upload-error">
              <span style="color:#f56c6c;font-size:12px;">{{ item.errorMsg }}</span>
              <el-button size="small" type="danger" @click="retryUpload(item, idx)">重试</el-button>
            </div>
            <el-button link class="preview-remove" @click="removePreview(idx)">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
        <div class="input-row">
        <el-input
          v-model="inputMessage"
          type="textarea"
            :autosize="{ minRows: 1, maxRows: 4 }"
            placeholder="输入消息…"
          resize="none"
            class="wechat-textarea"
            @keydown.enter.prevent="handleEnterPress"
          />
          <el-button
            type="primary"
            class="send-btn"
            :disabled="!inputMessage.trim() && !previewList.length"
            @click="sendMessage"
          >发送</el-button>
        </div>
        <div v-if="showEmojiPicker" class="emoji-picker-fix">
          <EmojiPicker @select="onEmojiSelect" v-if="showEmojiPicker" />
        </div>
      </div>
      </template>
    </div>

    <!-- 图片上传对话框 -->
    <el-dialog
      v-model="imageUploadVisible"
      title="发送图片"
      width="400px"
    >
      <el-upload
        action="#"
        list-type="picture-card"
        :auto-upload="false"
        :limit="1"
        :on-change="handleImageSelected"
      >
        <el-icon><Plus /></el-icon>
      </el-upload>
      <template #footer>
        <span>
          <el-button @click="imageUploadVisible = false">取消</el-button>
          <el-button type="primary" @click="sendImageMessage" :disabled="!selectedImage">
            发送
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 文件上传对话框 -->
    <el-dialog
      v-model="fileUploadVisible"
      title="发送文件"
      width="400px"
    >
      <el-upload
        action="#"
        :auto-upload="false"
        :limit="1"
        :on-change="handleFileSelected"
      >
        <el-button type="primary">选择文件</el-button>
      </el-upload>
      <div v-if="selectedFile" class="selected-file">
        <div class="file-name">{{ selectedFile.name }}</div>
        <div class="file-size">{{ formatFileSize(selectedFile.size) }}</div>
      </div>
      <template #footer>
        <span>
          <el-button @click="fileUploadVisible = false">取消</el-button>
          <el-button type="primary" @click="sendFileMessage" :disabled="!selectedFile">
            发送
          </el-button>
        </span>
      </template>
    </el-dialog>
    </AuthCheck>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, computed, watch, inject } from 'vue'
import { debounce } from 'lodash-es'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getChatHistory
} from '@/api/chat'
import { useUserStore, useOnlineStatusStore, useConversationStore } from '@/stores'
// 使用动态导入WebSocket服务，避免循环依赖
// WebSocket相关函数将在需要时通过import()动态导入
import { isContactOnline, subscribeContactStatus } from '@/utils/contact-status'
import { diagnoseWebSocketIssues } from '@/utils/websocket-diagnostics'
import logger from '@/utils/logger'
import IndexedDBManager from '@/utils/IndexedDB'
import OfflineMessageHandler from '@/utils/OfflineMessageHandler'
// 导入头像工具函数
import { getContactAvatarColor } from '@/utils/avatar-utils'
import { showContactOnlineNotification, resetNotificationCooldown } from '@/utils/contactNotificationManager'

// 创建聊天组件专用日志记录器
const chatLogger = logger.createLogger('Chat')
import ChatList from '@/components/chat/ChatList.vue'
import ChatMessage from '@/components/chat/ChatMessage.vue'
import EmojiPicker from 'vue3-emoji-picker'
import 'vue3-emoji-picker/css'
import AuthCheck from '@/components/AuthCheck.vue'
import {
  More,
  Picture,
  Document,
  Plus,
  View,
  Edit,
  Menu as List,
  Folder,
  Phone,
  MoreFilled,
  ChatDotSquare,
  ChatRound,
  Close,
  Microphone,
  VideoCamera,
  Monitor
} from '@element-plus/icons-vue'

const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

// 获取在线状态存储
const onlineStatusStore = useOnlineStatusStore()

// 获取会话管理存储
const conversationStore = useConversationStore()

// 数据
const contacts = ref([])
const currentContact = ref(null)
const messages = ref([])
const inputMessage = ref('')
const messagesContainer = ref(null)
const messageScrollbar = ref(null) // 消息滚动条引用
const chatListRef = ref(null)

// 加载状态
const loadingContacts = ref(true)
const loadingMessages = ref(false)
const loadingMore = ref(false)
const sending = ref(false)
const noMoreMessages = ref(false) // 是否没有更多消息
const currentPage = ref(1)
const pageSize = ref(20)
const anchorMessageId = ref(null) // 用于记录锚点消息ID
const refreshIntervalId = ref(null) // 定时刷新定时器ID
const isScrolling = ref(false) // 是否正在滚动
const scrollTimer = ref(null) // 滚动定时器

// 网络状态
const networkStatus = ref({
  connected: true,
  reconnecting: false,
  attempts: 0,
  lastConnected: Date.now()
})

// 初始化IndexedDB管理器
const dbManager = new IndexedDBManager('chat_db', 1)

// 创建离线消息处理器工厂函数
const createOfflineMessageHandler = async () => {
  // 动态导入修复版本的WebSocket服务
  const { sendChatMessage } = await import('@/utils/websocket-fix-sender');

  return new OfflineMessageHandler({
    dbManager,
    sendFunction: async (messages) => {
      // 实现批量发送消息
      return Promise.all(messages.map(message => {
        return sendChatMessage({
          senderId: message.sender,
          receiverId: message.receiver,
          message: message.content,
          messageType: message.messageType
        }, message.file)
      }))
    },
    onSuccess: (message, result) => {
      // 更新消息状态为已发送
      updateMessageStatus(message.id, false, {
        id: result.data?.id || message.id,
        time: result.data?.timestamp || new Date().toISOString(),
        fileUrl: result.data?.fileUrl || null
      })

      // 更新联系人列表中的最新消息
      debouncedUpdateContactLastMessage({
        ...message,
        id: result.data?.id || message.id
      })
    },
    onError: (message, error) => {
      // 更新消息状态为发送失败
      updateMessageStatus(message.id, false, {
        errorMessage: error.message || '发送失败'
      })
    }
  });
};

// 初始化离线消息处理器
const offlineMessageHandler = ref(null);

// 表情相关
const showEmojiPicker = ref(false)

// 图片和文件上传相关
const imageUploadVisible = ref(false)
const fileUploadVisible = ref(false)
const selectedImage = ref(null)
const selectedFile = ref(null)

// 新增多文件/多图预览逻辑
const previewList = ref([])

// 离线消息计数
const offlineMessageCount = ref(0)

// 限制配置
const MAX_IMAGE_SIZE_MB = 10
const MAX_VIDEO_SIZE_MB = 50
const MAX_AUDIO_SIZE_MB = 20
const MAX_FILE_SIZE_MB = 20
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif']
const ALLOWED_VIDEO_TYPES = ['video/mp4', 'video/webm', 'video/ogg']
const ALLOWED_AUDIO_TYPES = ['audio/mpeg', 'audio/wav', 'audio/ogg']

// 分组消息
const groupedMessages = computed(() => {
  const groups = {}
  messages.value.forEach(message => {
    const date = formatDate(message.time)
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(message)
  })
  return groups
})

// 监听消息变化，自动滚动到底部
watch(messages, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, { deep: true })

// 监听当前联系人变化，自动检查在线状态
watch(currentContact, async (newContact) => {
  if (newContact) {
    // 更新URL参数
    const url = new URL(window.location.href);
    url.searchParams.set('contactId', newContact.id);
    window.history.replaceState({}, '', url.toString());

    // 立即检查并更新联系人在线状态
    if (newContact.id) {
      // 订阅联系人状态
      subscribeContactStatus(newContact.id.toString());

      // 添加更详细的调试日志
      chatLogger.info(`准备检查联系人 ${newContact.name} (ID: ${newContact.id}) 的在线状态...`);
      const isOnline = isContactOnline(newContact.id);
      chatLogger.info(`当前联系人 ${newContact.name} (ID: ${newContact.id}) 在线状态: ${isOnline ? '在线' : '离线'}`);
      chatLogger.info(`联系人对象的原始在线状态: ${newContact.online ? '在线' : '离线'}`);

      // 输出更多调试信息
      console.log('isContactOnline 函数:', isContactOnline);
      console.log('联系人ID类型:', typeof newContact.id);
      console.log('联系人ID值:', newContact.id);

      // 确保联系人对象的online属性与实际状态一致
      if (newContact.online !== isOnline) {
        newContact.online = isOnline;
        chatLogger.info(`已更新当前联系人 ${newContact.name} 的在线状态为: ${isOnline ? '在线' : '离线'}`);
      }
    }
  }
});

// 监听消息发送事件，更新消息状态
const handleMessageSent = (event) => {
  try {
    const { message, status, tempId } = event.detail;

    chatLogger.info('收到消息发送事件:', { message, status, tempId });

    // 增加更详细的日志，帮助诊断问题
    chatLogger.debug('当前消息列表:', messages.value.map(m => ({
      id: m.id,
      content: m.content?.substring(0, 20) + (m.content?.length > 20 ? '...' : ''),
      status: m.status
    })));

    // 查找消息 - 优先使用clientMessageId或tempId匹配临时消息
    let existingMessage = null;
    let existingMessageIndex = -1;

    // 如果有tempId，先尝试用它查找临时消息
    if (tempId) {
      existingMessageIndex = messages.value.findIndex(m => String(m.id) === String(tempId));
      if (existingMessageIndex !== -1) {
        existingMessage = messages.value[existingMessageIndex];
        chatLogger.info('通过tempId查找消息:', tempId, '找到，索引:', existingMessageIndex);
      } else {
        chatLogger.info('通过tempId查找消息:', tempId, '未找到');
      }
    }

    // 如果有clientMessageId，也尝试用它查找
    if (existingMessageIndex === -1 && message.clientMessageId) {
      existingMessageIndex = messages.value.findIndex(m => String(m.id) === String(message.clientMessageId));
      if (existingMessageIndex !== -1) {
        existingMessage = messages.value[existingMessageIndex];
        chatLogger.info('通过clientMessageId查找消息:', message.clientMessageId, '找到，索引:', existingMessageIndex);
      } else {
        chatLogger.info('通过clientMessageId查找消息:', message.clientMessageId, '未找到');
      }
    }

    // 如果仍未找到，尝试通过内容和时间戳模糊匹配
    if (existingMessageIndex === -1 && (message.content || message.message)) {
      // 查找最近5分钟内发送的、内容相同的、状态为"发送中"的消息
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).getTime();

      existingMessageIndex = messages.value.findIndex(m =>
        m.status === 'SENDING' &&
        m.content === (message.content || message.message) &&
        m.isSelf === true &&
        new Date(m.time).getTime() > fiveMinutesAgo
      );

      if (existingMessageIndex !== -1) {
        existingMessage = messages.value[existingMessageIndex];
        chatLogger.info('通过内容和时间戳模糊匹配找到消息:', existingMessage.id);
      }
    }

    // 如果上面都没找到，使用常规方式查找
    if (existingMessageIndex === -1) {
      existingMessageIndex = messages.value.findIndex(m =>
        (String(m.id) === String(message.id)) ||
        (String(m.senderId) === String(message.senderId) &&
         String(m.receiverId) === String(message.receiverId) &&
         m.content === (message.message || message.content))
      );

      if (existingMessageIndex !== -1) {
        existingMessage = messages.value[existingMessageIndex];
        chatLogger.info('通过常规方式查找消息:', '找到，索引:', existingMessageIndex);
      } else {
        chatLogger.info('通过常规方式查找消息:', '未找到');
      }
    }

    if (existingMessage) {
      // 更新现有消息状态
      const additionalProps = {};

      // 如果消息有真实ID，更新临时ID为真实ID
      if (message.id && String(message.id) !== String(existingMessage.id) && !String(message.id).startsWith('temp-')) {
        additionalProps.id = message.id;
        chatLogger.info('更新临时ID为真实ID:', existingMessage.id, '->', message.id);
      }

      // 如果有时间戳，更新时间
      if (message.timestamp || message.time) {
        additionalProps.time = message.timestamp || message.time;
      }

      // 如果有文件URL，更新文件URL
      if (message.fileUrl) {
        additionalProps.fileUrl = message.fileUrl;
      }

      // 更新消息状态和其他属性
      updateMessageStatus(existingMessage.id, status, additionalProps);
      chatLogger.info('已更新现有消息状态:', existingMessage.id, status, additionalProps);

      // 如果ID已更新，确保在消息列表中也更新
      if (additionalProps.id) {
        // 创建一个新的消息对象，确保响应式更新
        const updatedMessage = { ...messages.value[existingMessageIndex], id: additionalProps.id };
        messages.value.splice(existingMessageIndex, 1, updatedMessage);
        chatLogger.info('已在消息列表中更新消息ID');
      }
    } else {
      // 如果消息不存在，可能是新消息，添加到消息列表
      const newMessage = {
        id: message.id || `temp-${Date.now()}`,
        senderId: message.senderId,
        receiverId: message.receiverId,
        content: message.message || message.content,
        messageType: message.messageType || 'TEXT',
        time: message.timestamp || message.time || new Date().toISOString(),
        isSelf: message.senderId === userInfo.value.id,
        status: status || 'SENDING'
      };

      messages.value.push(newMessage);
      chatLogger.info('已添加新消息:', newMessage.id);
    }
  } catch (error) {
    chatLogger.error('处理消息发送事件错误:', error);
    console.error('错误详情:', error);
  }
};

// 处理聊天消息 - 优化版，修复消息路由问题
const handleChatMessage = (event) => {
  try {
    console.log('【调试】handleChatMessage 被调用，事件类型:', event.type);
    const message = event.detail
    chatLogger.info('处理实时聊天消息:', message)
    console.log('【调试】消息原始数据:', message);
    console.log('【调试】消息字段:', Object.keys(message).join(', '));
    chatLogger.info('消息详情:', JSON.stringify(message, null, 2))

    if (!message || !message.receiverId || !message.senderId) {
      chatLogger.error('无效的消息格式:', message)
      console.error('【调试】消息缺少必要字段 receiverId 或 senderId');
      return
    }

    // 确保ID是字符串类型，便于比较
    const messageReceiverId = String(message.receiverId)
    const messageSenderId = String(message.senderId)
    const currentUserId = String(userInfo.value.id)

    // 判断消息是发给当前用户的还是当前用户发出的
    const isSelfMessage = messageSenderId === currentUserId
    const isReceivedMessage = messageReceiverId === currentUserId

    console.log('【调试】消息路由分析:');
    console.log('【调试】- 消息发送者ID:', messageSenderId, '(类型:', typeof message.senderId, ')');
    console.log('【调试】- 消息接收者ID:', messageReceiverId, '(类型:', typeof message.receiverId, ')');
    console.log('【调试】- 当前用户ID:', currentUserId, '(类型:', typeof userInfo.value.id, ')');
    console.log('【调试】- 是自己发送的消息:', isSelfMessage);
    console.log('【调试】- 是发给当前用户的消息:', isReceivedMessage);

    chatLogger.info('消息分析 - 发送者:', messageSenderId, '接收者:', messageReceiverId,
                   '当前用户:', currentUserId, '是自己发送的:', isSelfMessage,
                   '是接收到的:', isReceivedMessage)

    // 检查消息是否与当前用户相关
    if (!isSelfMessage && !isReceivedMessage) {
      chatLogger.warn('消息与当前用户无关，忽略消息')
      console.warn('【调试】消息与当前用户无关，忽略消息');
      return
    }

    console.log('【调试】消息与当前用户相关，继续处理');

    // 如果是接收到的消息（发给当前用户的）
    if (isReceivedMessage && !isSelfMessage) {
      chatLogger.info('收到他人发送的消息，播放提示音')
      // 播放提示音
      playNotificationSound()

      // 记录详细的消息信息
      chatLogger.info('收到的消息详情:', {
        id: message.id,
        senderId: messageSenderId,
        receiverId: messageReceiverId,
        content: message.content || message.message,
        messageType: message.messageType,
        time: message.time || message.timestamp,
        isSelf: isSelfMessage
      })

      // 更新联系人列表
      const contact = contacts.value.find(c => String(c.id) === messageSenderId)
      if (contact) {
        contact.lastMessage = message.content || message.message
        contact.lastTime = message.time || new Date().toISOString()

        // 确保当前联系人ID也是字符串类型
        const currentContactId = currentContact.value ? String(currentContact.value.id) : null

        // 如果不是当前聊天窗口，增加未读计数
        if (!currentContactId || currentContactId !== messageSenderId) {
          contact.unreadCount = (contact.unreadCount || 0) + 1
          chatLogger.debug('增加未读计数:', contact.name, contact.unreadCount)
        } else {
          // 如果是当前聊天窗口，确保未读数为0并立即标记为已读
          contact.unreadCount = 0

          // 如果消息有ID且不是自己发送的，立即标记为已读
          if (message.id && !isSelfMessage) {
            chatLogger.debug('当前聊天窗口收到新消息，立即标记为已读:', message.id)
            markMessageAsRead(message.id, currentUserId, messageSenderId)
              .then(response => {
                chatLogger.debug('消息已标记为已读响应:', response)
              })
              .catch(err => {
                chatLogger.error('标记消息已读失败:', err)
              })
          }
        }
      }
    }
    // 如果是自己发送的消息，不增加未读计数
    else if (isSelfMessage) {
      chatLogger.info('这是自己发送的消息，不增加未读计数')

      // 更新联系人列表中的最新消息
      const contactId = messageReceiverId
      const contact = contacts.value.find(c => String(c.id) === contactId)

      if (contact) {
        contact.lastMessage = message.content || message.message
        contact.lastTime = message.time || new Date().toISOString()
        // 确保未读计数不变
        chatLogger.debug('自己发送的消息，保持联系人未读计数不变:', contact.unreadCount)
      }

    }

    // 处理消息显示逻辑 - 无论是收到的还是发送的消息
    // 如果当前正在与消息相关的联系人聊天，则直接添加消息到聊天窗口
    const relevantContactId = isSelfMessage ? messageReceiverId : messageSenderId
    const currentContactId = currentContact.value ? String(currentContact.value.id) : null

    console.log('【调试】当前联系人信息:', currentContact.value ? {
      id: currentContact.value.id,
      name: currentContact.value.name,
      idType: typeof currentContact.value.id
    } : '无当前联系人');

    console.log('【调试】相关联系人ID:', relevantContactId);
    console.log('【调试】当前联系人ID:', currentContactId);
    console.log('【调试】是否应该添加到聊天窗口:', currentContactId && currentContactId === relevantContactId);

    chatLogger.info('消息路由分析:', {
      relevantContactId: relevantContactId,
      currentContactId: currentContactId,
      isSelfMessage: isSelfMessage,
      isReceivedMessage: isReceivedMessage,
      shouldAddToChat: currentContactId && currentContactId === relevantContactId
    })

    if (currentContactId && currentContactId === relevantContactId) {
      console.log('【调试】当前正在与消息相关的联系人聊天，准备添加消息到聊天窗口');
      chatLogger.info('当前正在与消息相关的联系人聊天，添加消息到聊天窗口')

      // 检查消息是否已存在
      const existingMessage = messages.value.find(m => String(m.id) === String(message.id))
      console.log('【调试】检查消息是否已存在:', existingMessage ? '已存在' : '不存在');

      if (existingMessage) {
        console.log('【调试】消息已存在，ID:', existingMessage.id);
      }

      if (!existingMessage) {
        console.log('【调试】准备添加新消息到聊天窗口');

        // 创建格式化的消息对象
        const newMessage = {
          ...message,
          id: message.id || `temp-${Date.now()}`,
          content: message.content || message.message || '[空消息]',
          isSelf: isSelfMessage,
          time: message.time || new Date().toISOString(),
          isRead: isReceivedMessage // 如果是收到的消息，标记为已读
        };

        console.log('【调试】格式化后的新消息:', newMessage);

        // 添加新消息
        messages.value.push(newMessage);

        console.log('【调试】已添加新消息到聊天窗口，当前消息数量:', messages.value.length);
        chatLogger.debug('已添加新消息到聊天窗口')

        // 如果是收到的消息且指定了消息ID，标记为已读
        if (isReceivedMessage && message.id) {
          chatLogger.debug('准备标记收到的消息为已读 - 消息ID:', message.id, '用户ID:', currentUserId, '发送者ID:', messageSenderId)
          markMessageAsRead(message.id, currentUserId, messageSenderId)
            .then(response => {
              chatLogger.debug('消息已标记为已读响应:', response)
              chatLogger.info('消息已标记为已读成功 - 消息ID:', message.id)

              // 更新本地消息状态
              const msgIndex = messages.value.findIndex(m => String(m.id) === String(message.id))
              if (msgIndex !== -1) {
                messages.value[msgIndex].isRead = true
                chatLogger.debug('本地消息状态已更新 - 消息ID:', message.id)

                // 发送已读回执
                (async () => {
                  try {
                    // 检查用户是否在线
                    const isOnline = navigator.onLine;

                    if (!isOnline) {
                      console.log('用户离线，不发送已读回执');
                      return;
                    }

                    // 检查WebSocket连接状态
                    let wsConnected = false;
                    try {
                      const { isWebSocketConnected } = await import('@/utils/websocket/index');
                      wsConnected = isWebSocketConnected();
                      console.log('WebSocket连接状态:', wsConnected ? '已连接' : '未连接');
                    } catch (error) {
                      console.error('检查WebSocket连接状态失败:', error);
                    }

                    if (!wsConnected) {
                      console.log('WebSocket未连接，不发送已读回执');
                      return;
                    }

                    // 使用WebSocket发送已读回执
                    const { sendReadReceipt } = await import('@/utils/websocket/index');
                    await sendReadReceipt([message.id], messageSenderId, userInfo.value.id);
                    console.log('已发送已读回执，消息ID:', message.id);
                  } catch (error) {
                    console.error('发送已读回执失败:', error);
                  }
                })()
              }
            })
            .catch(err => {
              chatLogger.error('标记消息已读失败 - 消息ID:', message.id, '错误:', err)
              chatLogger.error('错误详情:', err.response || err.message || err)
            })
        }

        // 滚动到底部
        nextTick(() => {
          scrollToBottom()
        })
      } else {
        chatLogger.debug('消息已存在，不重复添加:', message.id)
      }
    } else {
      chatLogger.debug('当前未与消息相关的联系人聊天，不添加消息到聊天窗口')
    }
  } catch (error) {
    chatLogger.error('处理聊天消息错误:', error)
    console.error('错误详情:', error)
  }
}

// 更新消息状态（只保留已读状态）
const updateMessageStatus = async (messageId, isRead = false, additionalProps = {}) => {
  if (!messageId) {
    console.warn('updateMessageStatus: messageId不能为空');
    return;
  }

  // 查找消息
  const messageIndex = messages.value.findIndex(m => String(m.id) === String(messageId));
  if (messageIndex !== -1) {
    // 记录更新前的状态
    const beforeUpdate = {
      id: messages.value[messageIndex].id,
      status: messages.value[messageIndex].status,
      fileUrl: messages.value[messageIndex].fileUrl,
      _imageLoading: messages.value[messageIndex]._imageLoading
    };

    // 检查是否正在上传文件或图片
    const isUploading = window._uploadingFile || window._uploadingImage;

    // 检查是否是错误状态更新，且消息已经成功发送或正在发送中
    if (additionalProps.errorMessage &&
        (messages.value[messageIndex].status === 'SENT' ||
         (messages.value[messageIndex].status === 'SENDING' && isUploading))) {
      console.log('消息已成功发送或正在发送中，忽略错误状态更新');
      return;
    }

    // 更新已读状态
    messages.value[messageIndex].isRead = isRead;

    // 添加任何额外的属性
    if (additionalProps && typeof additionalProps === 'object') {
      Object.keys(additionalProps).forEach(key => {
        messages.value[messageIndex][key] = additionalProps[key];
      });
    }

    // 记录更新后的状态
    const afterUpdate = {
      id: messages.value[messageIndex].id,
      status: messages.value[messageIndex].status,
      fileUrl: messages.value[messageIndex].fileUrl,
      _imageLoading: messages.value[messageIndex]._imageLoading
    };

    // 如果是设置了状态为SENT，确保清除任何错误消息
    if (additionalProps.status === 'SENT') {
      messages.value[messageIndex].errorMessage = null;
    }

    console.log('消息状态更新:', {
      before: beforeUpdate,
      after: afterUpdate,
      changes: Object.keys(additionalProps),
      isUploading: isUploading
    });

    // 创建一个新的消息对象，触发Vue的响应式更新
    const updatedMessage = { ...messages.value[messageIndex] };

    // 使用数组的splice方法替换原消息，确保触发响应式更新
    messages.value.splice(messageIndex, 1, updatedMessage);

    // 使用nextTick确保视图更新
    nextTick(() => {
      console.log('消息状态更新后视图已刷新:', messageId);
    });

    // 检查是否需要更新ID
    const oldId = messageId;
    const newId = additionalProps.id;

    // 如果ID发生变化，需要特殊处理IndexedDB
    if (newId && String(oldId) !== String(newId) && String(oldId).startsWith('temp-')) {
      try {
        console.log(`检测到ID变更: ${oldId} -> ${newId}, 执行IndexedDB特殊处理`);

        // 1. 获取完整的消息对象
        const completeMessage = { ...updatedMessage };

        // 2. 从IndexedDB中删除旧消息
        await dbManager.deleteMessage(oldId);
        console.log(`已从IndexedDB中删除临时消息: ${oldId}`);

        // 3. 使用新ID保存消息
        await dbManager.saveMessage(completeMessage);
        console.log(`已使用新ID保存消息到IndexedDB: ${newId}`);
      } catch (error) {
        console.error('更新IndexedDB中的消息ID失败:', error);
      }
    } else {
      // 常规更新IndexedDB中的消息状态
      try {
        await dbManager.updateMessageStatus(messageId, isRead ? 'READ' : 'UNREAD', additionalProps);
        console.log(`IndexedDB中的消息状态已更新 - ID: ${messageId}`);
      } catch (error) {
        // 捕获并记录错误，但不中断流程
        console.error('更新IndexedDB中的消息状态失败:', error);
      }
    }
  } else {
    console.warn(`未找到ID为 ${messageId} 的消息，无法更新状态`);
  }
};

// 重发消息
const resendMessage = async (messageId) => {
  console.log('尝试重发消息:', messageId);

  // 查找消息
  const message = messages.value.find(m => String(m.id) === String(messageId));
  if (!message) {
    console.warn(`未找到ID为 ${messageId} 的消息，无法重发`);
    ElMessage.warning('无法找到要重发的消息');
    return;
  }

  // 检查WebSocket连接状态
  try {
    const { isWebSocketConnected } = await import('@/utils/websocket/index');
    const connected = await isWebSocketConnected(); // 检查连接状态
    if (!connected) {
      ElMessage.error('网络连接已断开，无法重发消息');
      return;
    }
  } catch (error) {
    console.error('检查WebSocket连接状态失败:', error);
    ElMessage.error('网络连接状态检查失败，无法重发消息');
    return;
  }

  // 更新消息状态，清除错误信息
  updateMessageStatus(messageId, false, { errorMessage: null });

  try {
    // 准备消息DTO
    const messageDTO = {
      senderId: userInfo.value.id,
      receiverId: message.receiverId || message.receiver,
      message: message.content || message.message,
      messageType: message.messageType || 'TEXT',
      clientMessageId: `resend-${Date.now()}-${messageId}` // 添加客户端消息ID用于跟踪
    };

    console.log('重发消息DTO:', messageDTO);

    // 发送消息
    const { sendChatMessage } = await import('@/utils/websocket/index');
    const res = await sendChatMessage(messageDTO);
    console.log('重发消息后端响应:', res);

    if (res && res.code === 200) {
      if (res.data && res.data.id) {
        // 更新消息ID和时间
        updateMessageStatus(messageId, false, {
          id: res.data.id,
          time: res.data.timestamp || message.time
        });

        // 更新联系人列表中的最新消息
        debouncedUpdateContactLastMessage({
          ...message,
          id: res.data.id
        });

        ElMessage.success('消息重发成功');
      } else {
        // 如果响应中没有ID，保持原ID
        updateMessageStatus(messageId, false);

        // 更新联系人列表中的最新消息
        debouncedUpdateContactLastMessage({
          ...message
        });

        ElMessage.success('消息重发成功');
      }
    } else {
      throw new Error(`服务器返回错误: ${res?.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('重发消息失败:', error);
    updateMessageStatus(messageId, false, {
      errorMessage: error.message || '重发失败，请稍后再试'
    });
    ElMessage.error('消息重发失败，请稍后再试');
  }
};

// 直接更新消息已读状态 (保留向后兼容性)
const updateMessageReadStatus = (messageId) => {
  updateMessageStatus(messageId, true);
};

// 处理已读回执
const handleReadReceipt = (event) => {
  try {
    const data = event.detail;
    chatLogger.info('收到已读回执:', data);

    if (data && data.messageIds && Array.isArray(data.messageIds)) {
      // 更新多条消息的已读状态
      chatLogger.debug('批量更新消息已读状态, 消息数量:', data.messageIds.length);
      data.messageIds.forEach(messageId => {
        updateMessageReadStatus(messageId);
      });
    } else if (data && data.messageId) {
      // 更新单条消息的已读状态
      chatLogger.debug('更新单条消息已读状态, 消息ID:', data.messageId);
      updateMessageReadStatus(data.messageId);
    } else {
      chatLogger.warn('收到无效的已读回执数据:', data);
    }
  } catch (error) {
    chatLogger.error('处理已读回执出错:', error);
  }
};

// 播放通知音效
const playNotificationSound = async () => {
  try {
    // 检查用户是否已与页面交互
    const hasInteracted = document.documentElement.hasAttribute('data-user-interacted') ||
                          document.visibilityState === 'visible';

    if (!hasInteracted) {
      console.warn('用户尚未与页面交互，无法直接播放通知音效');
      // 标记需要播放音效，但不立即播放
      window.pendingAudioPlay = window.pendingAudioPlay || [];
      window.pendingAudioPlay.push('chat');
      return;
    }

    const globalMessageHandler = await import('@/utils/globalMessageHandler')
    globalMessageHandler.default.playNotificationSound('chat')
  } catch (error) {
    console.error('播放通知音效失败:', error)
  }
}

// 选择联系人
const selectContact = async (contact) => {
  if (currentContact.value && currentContact.value.id === contact.id) return

  // 保存之前选中的联系人ID
  const previousContactId = currentContact.value?.id

  currentContact.value = contact
  messages.value = []

  // 立即将未读计数设为0，并触发响应式更新
  contact.unreadCount = 0

  // 同步更新联系人列表中的未读计数，并确保触发响应式更新
  const contactIndex = contacts.value.findIndex(c =>
    String(c.id) === String(contact.id) ||
    (c.contactId && String(c.contactId) === String(contact.id))
  )

  if (contactIndex !== -1) {
    // 先记录原来的未读计数，用于日志
    const oldUnreadCount = contacts.value[contactIndex].unreadCount || 0;

    // 重置本地未读计数
    contacts.value[contactIndex].unreadCount = 0

    // 创建一个新的数组，触发Vue的响应式更新
    contacts.value = [...contacts.value]
    console.log(`已重置联系人 ${contact.name} 的未读计数: ${oldUnreadCount} -> 0，并触发响应式更新`)

    // 无论是否有会话ID，都尝试重置未读计数
    try {
      // 先尝试使用会话管理Store重置未读计数（同时更新数据库和Redis）
      await conversationStore.clearUnreadCount(userInfo.value.id, contact.id)
      console.log(`已通过会话管理Store重置联系人 ${contact.name} 的未读计数（数据库和Redis都已更新）`)
    } catch (error) {
      console.error(`通过会话管理Store重置未读计数失败:`, error)

      // 如果会话Store失败，尝试直接调用API
      try {
        const { resetUnreadCount } = await import('@/api/chat')
        await resetUnreadCount(userInfo.value.id, contact.id)
        console.log(`已通过API直接重置联系人 ${contact.name} 的未读计数`)
      } catch (apiError) {
        console.error(`通过API重置未读计数也失败:`, apiError)
      }
    }
  }

  // 先尝试从本地缓存加载消息
  await loadCachedMessages(contact.id)

  // 然后从服务器加载最新消息
  await loadChatHistory(contact.id);

  // 批量标记所有未读消息为已读
  try {
    // 检查用户是否在线
    const isOnline = navigator.onLine;

    if (!isOnline) {
      console.log('用户离线，不标记消息为已读');
      return;
    }

    // 检查WebSocket连接状态
    let wsConnected = false;
    try {
      const { isWebSocketConnected } = await import('@/utils/websocket/index');
      wsConnected = isWebSocketConnected();
      console.log('WebSocket连接状态:', wsConnected ? '已连接' : '未连接');
    } catch (error) {
      console.error('检查WebSocket连接状态失败:', error);
    }

    if (!wsConnected) {
      console.log('WebSocket未连接，不标记消息为已读');
      return;
    }

    // 收集所有未读消息ID
    const unreadMessages = messages.value.filter(msg =>
      !msg.isRead &&
      String(msg.senderId) === String(contact.id) &&
      String(msg.receiverId) === String(userInfo.value.id)
    );

    if (unreadMessages.length > 0) {
      console.log(`找到 ${unreadMessages.length} 条未读消息需要标记为已读`);

      // 收集消息ID
      const messageIds = unreadMessages.map(msg => msg.id);
      console.log('未读消息ID列表:', messageIds);

      // 使用WebSocket发送批量已读回执
      try {
        const { sendReadReceipt } = await import('@/utils/websocket/index');
        await sendReadReceipt(messageIds, contact.id, userInfo.value.id);
        console.log('已通过WebSocket发送批量已读回执，消息数量:', messageIds.length);

        // 更新本地消息状态
        unreadMessages.forEach(msg => {
          const index = messages.value.findIndex(m => String(m.id) === String(msg.id));
          if (index !== -1) {
            messages.value[index].isRead = true;
          }
        });
      } catch (error) {
        console.error('发送批量已读回执失败:', error);
      }
    } else {
      console.log('没有未读消息需要标记为已读');
    }
  } catch (error) {
    console.error('批量标记已读失败:', error.response?.data || error.message);
  }

  // 重置之前联系人的通知冷却时间
  // 这样当用户切换回来时，如果该联系人上线，会立即收到通知
  if (previousContactId) {
    resetNotificationCooldown(previousContactId)
  }
}

// 加载缓存的消息
const loadCachedMessages = async (contactId) => {
  if (!contactId || !userInfo.value) return;

  try {
    const conversationId = dbManager.getConversationId(userInfo.value.id, contactId);
    console.log(`尝试从本地缓存加载会话 ${conversationId} 的消息`);

    const cachedMessages = await dbManager.getConversationMessages(conversationId, 50);

    if (cachedMessages.length > 0) {
      console.log(`从本地缓存加载了 ${cachedMessages.length} 条消息`);

      // 格式化消息
      const formattedMessages = cachedMessages.map(message => ({
        ...message,
        isSelf: String(message.sender) === String(userInfo.value.id)
      }));

      // 设置消息列表
      messages.value = formattedMessages;

      // 滚动到底部
      nextTick(() => scrollToBottom());
    } else {
      console.log('本地缓存中没有找到消息');
      messages.value = []; // 清空消息列表
    }
  } catch (error) {
    console.error('加载缓存消息失败:', error);
    messages.value = []; // 出错时清空消息列表
  }
}

// 加载聊天历史
const loadChatHistory = async (contactId) => {
  if (!contactId || !userInfo.value || !userInfo.value.id) {
    console.warn('加载聊天历史失败：缺少必要参数', { contactId, userId: userInfo.value?.id });
    return;
  }

  loadingMessages.value = true;
  currentPage.value = 1;
  messages.value = []; // 清空现有消息
  noMoreMessages.value = false; // 重置是否还有更多消息的标志
  anchorMessageId.value = null; // 重置锚点消息ID

  // 每次切换联系人时，重置页面大小为初始值
  pageSize.value = 20;

  try {
    console.log('加载聊天历史：', userInfo.value.id, contactId);
    const res = await getChatHistory(userInfo.value.id, contactId, currentPage.value, pageSize.value);
    console.log('聊天历史响应：', res);

    if (res.code === 200) {
      let newMessages = [];

      // 处理不同格式的响应数据
      if (res.data && res.data.list && Array.isArray(res.data.list)) {
        newMessages = res.data.list;
        // 初始加载时不设置noMoreMessages为true，除非明确知道没有更多消息
        if (res.data.hasNextPage === false && newMessages.length === 0) {
          noMoreMessages.value = true;
        } else {
          noMoreMessages.value = false; // 确保初始加载时可以点击"查看更多"
        }
      } else if (Array.isArray(res.data)) {
        newMessages = res.data;
        // 初始加载时不设置noMoreMessages为true，除非没有任何消息
        if (newMessages.length === 0) {
          noMoreMessages.value = true;
        } else {
          noMoreMessages.value = false; // 确保初始加载时可以点击"查看更多"
        }
      } else {
        messages.value = [];
        noMoreMessages.value = true;
        return;
      }

      if (newMessages.length === 0) {
        messages.value = [];
        noMoreMessages.value = true;
        return;
      }

      // 格式化消息
      const formattedMessages = newMessages.map(msg => ({
        ...msg,
        id: msg.id || msg.messageId || `history-${Date.now()}-${Math.random()}`,
        content: msg.content || msg.message || '[空消息]',
        senderId: msg.senderId || msg.sender,
        receiverId: msg.receiverId || msg.receiver,
        messageType: msg.messageType || 'TEXT',
        time: msg.time || msg.timestamp || new Date().toISOString(),
        isSelf: msg.senderId === userInfo.value.id || msg.sender === userInfo.value.id,
        status: msg.status || (msg.isRead ? 'READ' : 'SENT'),
        isRead: !!msg.isRead
      }));

      // 按时间排序，确保最早的消息在前面
      const sortedMessages = [...formattedMessages].sort((a, b) =>
        new Date(a.time) - new Date(b.time)
      );

      // 设置消息数组
      messages.value = sortedMessages;
      console.log(`加载了 ${messages.value.length} 条历史消息`);

      // 保存消息到IndexedDB
      try {
        // 为每条消息添加会话ID
        const messagesWithConversationId = sortedMessages.map(message => ({
          ...message,
          conversationId: dbManager.getConversationId(userInfo.value.id, contactId)
        }));

        // 批量保存消息
        await dbManager.saveMessages(messagesWithConversationId);
        console.log(`已将 ${messagesWithConversationId.length} 条消息保存到本地数据库`);
      } catch (dbError) {
        console.error('保存消息到本地数据库失败:', dbError);
      }

      // 滚动到底部
      nextTick(() => scrollToBottom());
    } else {
      console.error('加载聊天历史失败:', res);
      messages.value = [];
      ElMessage.error('加载聊天历史失败');
    }
  } catch (error) {
    console.error('加载聊天历史失败:', error);
    messages.value = [];
    ElMessage.error('加载聊天历史失败');
  } finally {
    loadingMessages.value = false;
  }
};

// 增量更新聊天历史 - 只获取新消息而不替换现有消息
const incrementalUpdateChatHistory = async (contactId) => {
  if (!contactId || !userInfo.value || !userInfo.value.id) {
    console.warn('增量更新聊天历史失败：缺少必要参数', { contactId, userId: userInfo.value?.id });
    return;
  }

  try {
    // 获取当前消息列表中最新消息的时间
    let latestMessageTime = null;
    if (messages.value.length > 0) {
      // 按时间排序，找出最新的消息
      const sortedMessages = [...messages.value].sort((a, b) =>
        new Date(b.time) - new Date(a.time)
      );
      latestMessageTime = sortedMessages[0].time;
    }

    console.log('增量更新聊天历史，最新消息时间:', latestMessageTime);

    // 调用API获取新消息
    const res = await getChatHistory(
      userInfo.value.id,
      contactId,
      1, // 始终从第一页获取
      10, // 获取较少的消息，减少网络负担
      latestMessageTime // 传递最新消息时间，后端可以根据此时间过滤
    );

    if (res.code === 200) {
      let newMessages = [];

      // 处理不同格式的响应数据
      if (res.data && res.data.list && Array.isArray(res.data.list)) {
        newMessages = res.data.list;
      } else if (Array.isArray(res.data)) {
        newMessages = res.data;
      }

      if (newMessages.length === 0) {
        console.log('没有新消息');
        return;
      }

      console.log(`获取到 ${newMessages.length} 条新消息`);

      // 格式化消息
      const formattedMessages = newMessages.map(msg => ({
        ...msg,
        id: msg.id || msg.messageId || `history-${Date.now()}-${Math.random()}`,
        content: msg.content || msg.message || '[空消息]',
        senderId: msg.senderId || msg.sender,
        receiverId: msg.receiverId || msg.receiver,
        messageType: msg.messageType || 'TEXT',
        time: msg.time || msg.timestamp || new Date().toISOString(),
        isSelf: msg.senderId === userInfo.value.id || msg.sender === userInfo.value.id,
        status: msg.status || (msg.isRead ? 'READ' : 'SENT'),
        isRead: !!msg.isRead
      }));

      // 过滤掉已有的消息
      const existingMessageIds = messages.value.map(m => String(m.id));
      const uniqueNewMessages = formattedMessages.filter(
        msg => !existingMessageIds.includes(String(msg.id))
      );

      if (uniqueNewMessages.length === 0) {
        console.log('所有新消息已存在于当前聊天中');
        return;
      }

      console.log(`添加 ${uniqueNewMessages.length} 条新消息到聊天`);

      // 按时间排序，确保最早的消息在前面
      const sortedMessages = [...uniqueNewMessages].sort((a, b) =>
        new Date(a.time) - new Date(b.time)
      );

      // 添加到消息数组
      messages.value = [...messages.value, ...sortedMessages];

      // 保存消息到IndexedDB
      try {
        // 为每条消息添加会话ID
        const messagesWithConversationId = sortedMessages.map(message => ({
          ...message,
          conversationId: dbManager.getConversationId(userInfo.value.id, contactId)
        }));

        // 批量保存消息
        await dbManager.saveMessages(messagesWithConversationId);
        console.log(`已将 ${messagesWithConversationId.length} 条新消息保存到本地数据库`);
      } catch (dbError) {
        console.error('保存消息到本地数据库失败:', dbError);
      }

      // 滚动到底部
      nextTick(() => {
        scrollToBottom();
      });
    }
  } catch (error) {
    console.error('增量更新聊天历史失败:', error);
  }
};

// 加载更多消息
const loadMoreMessages = async () => {
  if (loadingMore.value) return

  loadingMore.value = true
  currentPage.value++

  try {
    console.log('加载更多消息：', currentPage.value, pageSize.value)

    // 保存第一条消息的ID作为锚点
    anchorMessageId.value = messages.value.length > 0 ? messages.value[0].id : null

    // 获取当前最早消息的时间戳，用于确保只加载更早的消息
    const oldestMsgTime = messages.value.length > 0
      ? new Date(messages.value[0].time).getTime()
      : Date.now()

    console.log('当前最早消息时间戳:', new Date(oldestMsgTime).toISOString())
    console.log('锚点消息ID:', anchorMessageId)

    // 添加最小延迟，确保加载动画显示
    await new Promise(resolve => setTimeout(resolve, 500))

    const res = await getChatHistory(
      userInfo.value.id,
      currentContact.value.id,
      currentPage.value,
      pageSize.value
    )

    console.log('加载更多消息响应:', res)

    if (res.code === 200) {
      let newMessages = []

      // 处理不同格式的响应数据
      if (res.data && res.data.list && Array.isArray(res.data.list)) {
        newMessages = res.data.list
        // 只有当服务器明确表示没有下一页且没有返回消息时才设置noMoreMessages为true
        if (res.data.hasNextPage === false && newMessages.length === 0) {
          noMoreMessages.value = true
          ElMessage.info('没有更多历史消息了')
        }
      } else if (Array.isArray(res.data)) {
        newMessages = res.data
        // 只有当返回的消息数量为0时才设置noMoreMessages为true
        if (newMessages.length === 0) {
          noMoreMessages.value = true
          ElMessage.info('没有更多历史消息了')
        }
      } else {
        loadingMore.value = false
        return
      }

      if (newMessages.length === 0) {
        noMoreMessages.value = true
        ElMessage.info('没有更多历史消息了')
        // 添加延迟，确保加载动画显示足够长的时间
        await new Promise(resolve => setTimeout(resolve, 300))
        return
      }

      // 格式化消息
      const formattedMessages = newMessages.map(msg => ({
        ...msg,
        id: msg.id || msg.messageId || `history-${Date.now()}-${Math.random()}`,
        content: msg.content || msg.message || '[空消息]',
        senderId: msg.senderId || msg.sender,
        receiverId: msg.receiverId || msg.receiver,
        messageType: msg.messageType || 'TEXT',
        time: msg.time || msg.timestamp || new Date().toISOString(),
        isSelf: msg.senderId === userInfo.value.id || msg.sender === userInfo.value.id,
        status: msg.status || (msg.isRead ? 'READ' : 'SENT'),
        isRead: !!msg.isRead
      }))

      // 反转消息顺序，确保最早的消息在前面
      const sortedMessages = [...formattedMessages].sort((a, b) =>
        new Date(a.time) - new Date(b.time)
      )

      // 检查消息是否已存在，避免重复
      const existingIds = new Set(messages.value.map(m => m.id))
      const uniqueNewMessages = sortedMessages.filter(m => !existingIds.has(m.id))

      console.log(`加载了 ${newMessages.length} 条消息，过滤重复后剩余 ${uniqueNewMessages.length} 条`)

      if (uniqueNewMessages.length === 0) {
        // 如果过滤后没有新消息，但API返回了消息，说明可能有重复，继续加载下一页
        if (newMessages.length > 0) {
          loadingMore.value = false
          // 递归调用自身，加载下一页
          setTimeout(() => loadMoreMessages(), 100)
          return
        } else {
          noMoreMessages.value = true
          ElMessage.info('没有更多新消息了')
          return
        }
      }

      // 锚点消息ID已在函数开始时设置，这里不需要重复设置
      console.log('使用锚点消息ID:', anchorMessageId.value);

      // 将新消息添加到列表前面（因为这些是更早的消息）
      messages.value = [...uniqueNewMessages, ...messages.value]

      // 使用scrollToElement函数滚动到锚点消息
      scrollToElement('.message-item[data-anchor="true"]', 50);

      // 清除锚点ID，避免影响后续操作
      setTimeout(() => {
        anchorMessageId.value = null;
      }, 200);

      console.log('加载了更多历史消息，并保持了正确的时间顺序')

      // 保存消息到IndexedDB
      try {
        // 为每条消息添加会话ID
        const messagesWithConversationId = uniqueNewMessages.map(message => ({
          ...message,
          conversationId: dbManager.getConversationId(userInfo.value.id, currentContact.value.id)
        }))

        // 批量保存消息
        await dbManager.saveMessages(messagesWithConversationId)
        console.log(`已将 ${messagesWithConversationId.length} 条消息保存到本地数据库`)
      } catch (dbError) {
        console.error('保存消息到本地数据库失败:', dbError)
      }
    } else {
      ElMessage.warning('加载消息失败，请稍后重试')
    }
  } catch (error) {
    console.error('加载更多消息失败:', error)
    ElMessage.error({
      message: '加载更多消息失败',
      duration: 2000
    })
    currentPage.value--
  } finally {
    loadingMore.value = false
  }
}

// 处理Enter键按下
const handleEnterPress = (e) => {
  // 如果按下Shift+Enter，则插入换行
  if (e.shiftKey) {
    return
  }

  // 否则发送消息
  sendMessage()
}

// 发送消息
const sendMessage = async () => {
  if ((!inputMessage.value.trim() && !previewList.value.length) || sending.value) return
  if (!currentContact.value) {
    ElMessage.warning('请先选择联系人')
    return
  }
  if (!userInfo.value || !userInfo.value.id) {
    ElMessage.error('用户未登录或用户信息不完整')
    return
  }

  // 使用修复版本的WebSocket发送函数
  sending.value = true

  try {
    // 动态导入修复版本的WebSocket发送函数
    const { sendChatMessage } = await import('@/utils/websocket-fix-sender')
    chatLogger.info('已导入修复版本的WebSocket发送函数')

    // 检查网络连接状态
    const isOnline = navigator.onLine
    let connected = false;

    if (isOnline) {
      try {
        // 动态导入WebSocket服务
        const { isWebSocketConnected, initializeWebSocketService } = await import('@/utils/websocket/index');

        // 初始化WebSocket服务
        await initializeWebSocketService();

        // 检查连接状态
        connected = isWebSocketConnected();

        chatLogger.info('WebSocket连接状态:', connected ? '已连接' : '未连接');
      } catch (error) {
        console.error('检查WebSocket连接状态失败:', error);
        connected = false;
      }
    }

    chatLogger.info('连接状态检查结果:', {
      网络状态: isOnline ? '在线' : '离线',
      WebSocket状态: connected ? '已连接' : '未连接'
    })

    console.log('开始发送消息，网络状态:', isOnline ? '在线' : '离线', 'WebSocket连接状态:', connected ? '已连接' : '未连接')

    // 保存当前输入框中的文本，用于文件上传
    const currentInputMessage = inputMessage.value.trim()

    // 标记是否已经发送了消息，避免重复发送
    let messageSent = false

    // 先检查是否有文件需要上传
    if (previewList.value.length > 0) {
      console.log('检测到文件上传，总数:', previewList.value.length)
      console.log('文件类型:', previewList.value.map(item => item.type))

      // 使用统一的连接状态判断逻辑
      if (isOnline && connected) {
        // 如果有文本和文件，将文本作为文件消息的内容
        if (currentInputMessage) {
          console.log('发送带文本的文件消息:', currentInputMessage)
          await uploadAllFiles(currentInputMessage)

          // 如果成功上传了文件并包含了文本，清空输入框，避免再次发送文本
          inputMessage.value = ''
        } else {
          // 否则，正常上传文件
          console.log('发送不带文本的文件消息')
          await uploadAllFiles()
        }

        // 标记消息已发送，并设置全局标记，避免创建重复的离线消息
        messageSent = true
        window._fileMessageSent = true // 添加全局标记，表示文件消息已发送
        console.log('文件消息已发送，不会再发送纯文本消息')

        // 延迟清除全局标记，确保其他地方不会误用
        setTimeout(() => {
          window._fileMessageSent = false
          console.log('清除文件消息已发送标记')
        }, 2000)
      } else {
        // 离线状态或WebSocket未连接，提示用户
        const reason = !isOnline ? '网络离线' : 'WebSocket未连接';
        console.warn(`${reason}，无法发送文件消息`);
        ElMessage.warning(`${reason}，暂不支持发送文件，请等待网络恢复后再试`);
        return
      }
    } else {
      console.log('没有检测到文件，将发送纯文本消息')
    }

    // 再发文本消息，但只有在之前没有发送过消息的情况下才发送
    if (inputMessage.value.trim() && !messageSent) {
      console.log('发送纯文本消息')
      // 创建临时消息ID
      const tempId = `temp-${Date.now()}`
      const clientMessageId = `client-${Date.now()}`

      // 创建临时消息对象 - 简化版，只保留必要字段
      const tempMessage = {
        sender: userInfo.value.id,
        receiver: currentContact.value.id,
        content: inputMessage.value,
        messageType: 'TEXT',
        time: new Date().toISOString(),
        senderId: userInfo.value.id,
        receiverId: currentContact.value.id,
        isSelf: true,
        id: tempId,
        isRead: false, // 只保留已读状态
        // 添加会话ID，用于离线消息处理
        conversationId: dbManager.getConversationId(userInfo.value.id, currentContact.value.id),
        // 添加客户端消息ID，用于前端消息ID映射
        clientMessageId: clientMessageId,
        // 添加消息状态
        status: 'SENDING'
      }

      // 添加临时消息到聊天窗口
      messages.value.push(tempMessage)

      // 滚动到底部
      scrollToBottom()

      // 如果在线，使用修复版本的WebSocket发送函数发送消息
      if (isOnline && connected) {
        // 准备发送消息DTO - 添加clientMessageId用于前端消息ID映射
        const messageDTO = {
          senderId: userInfo.value.id,
          receiverId: currentContact.value.id,
          message: inputMessage.value,
          messageType: 'TEXT',
          clientMessageId: clientMessageId, // 添加客户端消息ID，用于前端消息ID映射
          timestamp: Date.now(), // 添加时间戳（使用毫秒时间戳）
          conversationId: currentContact.value.conversationId // 添加会话ID
        }

        // 添加详细日志
        chatLogger.info('准备发送消息:', {
          clientMessageId: clientMessageId,
          senderId: userInfo.value.id,
          receiverId: currentContact.value.id,
          messageType: 'TEXT',
          contentLength: inputMessage.value.length
        });
        chatLogger.info('WebSocket连接状态:', connected ? '已连接' : '未连接');
        chatLogger.info('接收者信息:', {
          id: currentContact.value.id,
          name: currentContact.value.name
        });

        try {
          // 标记消息为发送中状态，避免被误判为离线消息
          updateMessageStatus(tempId, false, {
            status: 'SENDING',
            _isWebSocketSending: true // 添加标记，表示正在通过WebSocket发送
          });

          // 使用修复版本的WebSocket发送函数发送消息
          chatLogger.info('使用修复版本的WebSocket发送函数发送消息...')
          const res = await sendChatMessage(messageDTO)
          chatLogger.info('发送文本消息后端响应:', {
            code: res.code,
            message: res.message,
            dataId: res.data?.id,
            clientMessageId: res.data?.clientMessageId
          })

          // 消息已经发送，无论成功与否，都清除WebSocket发送标记
          const currentMessage = messages.value.find(m => m.id === tempId);
          if (currentMessage) {
            currentMessage._isWebSocketSending = false;
          }

          if (res && res.code === 200) {
            if (res.data && res.data.id) {
              // 更新消息ID和时间
              updateMessageStatus(tempId, false, {
                id: res.data.id,
                time: res.data.timestamp || tempMessage.time,
                status: 'SENT',
                clientMessageId: messageDTO.clientMessageId,
                _isWebSocketSending: false // 确保清除标记
              })

              // 更新联系人列表中的最新消息（使用防抖版本）
              debouncedUpdateContactLastMessage({
                ...tempMessage,
                id: res.data.id,
                status: 'SENT',
                clientMessageId: messageDTO.clientMessageId
              })

              chatLogger.info('消息发送成功，详情:', {
                tempId: tempId,
                newId: res.data.id,
                clientMessageId: messageDTO.clientMessageId,
                senderId: messageDTO.senderId,
                receiverId: messageDTO.receiverId,
                content: messageDTO.message,
                timestamp: res.data.timestamp
              })
            } else {
              // 如果响应中没有ID，保持临时ID
              updateMessageStatus(tempId, false, {
                status: 'SENT',
                clientMessageId: messageDTO.clientMessageId,
                _isWebSocketSending: false // 确保清除标记
              })

              // 更新联系人列表中的最新消息（使用防抖版本）
              debouncedUpdateContactLastMessage({
                ...tempMessage,
                status: 'SENT',
                clientMessageId: messageDTO.clientMessageId
              })

              chatLogger.info('消息发送成功，但未返回ID，使用临时ID:', tempId)
            }
          } else {
            // 发送失败，更新消息状态
            updateMessageStatus(tempId, false, {
              errorMessage: res?.message || '发送失败',
              status: 'ERROR',
              _isWebSocketSending: false // 确保清除标记
            })

            chatLogger.error('消息发送失败:', res?.message || '未知错误')
            ElMessage.error('消息发送失败: ' + (res?.message || '未知错误'))

            // 只有在明确失败时才保存到离线队列
            try {
              await dbManager.saveMessage({
                ...tempMessage,
                status: 'OFFLINE',
                _isWebSocketSending: false // 确保清除标记
              })

              chatLogger.info('消息已保存到离线队列')
              ElMessage.info('消息已保存，将在网络恢复后重试发送')
            } catch (dbError) {
              chatLogger.error('保存离线消息失败:', dbError)
              ElMessage.error('保存离线消息失败，请稍后重试')
              return // 发送失败时提前返回，不清空输入框
            }
          }
        } catch (error) {
          // 发送出错，更新消息状态
          updateMessageStatus(tempId, false, {
            errorMessage: error.message || '发送失败',
            status: 'ERROR',
            _isWebSocketSending: false // 确保清除标记
          })

          chatLogger.error('消息发送出错:', error)
          ElMessage.error('消息发送失败: ' + (error.message || '未知错误'))

          // 只有在明确出错时才保存到离线队列
          try {
            await dbManager.saveMessage({
              ...tempMessage,
              status: 'OFFLINE',
              _isWebSocketSending: false // 确保清除标记
            })

            chatLogger.info('消息已保存到离线队列')
            ElMessage.info('消息已保存，将在网络恢复后重试发送')
          } catch (dbError) {
            chatLogger.error('保存离线消息失败:', dbError)
            ElMessage.error('保存离线消息失败，请稍后重试')
            return // 发送失败时提前返回，不清空输入框
          }
        }
      }
    } else {
      // 离线状态，保存到离线消息队列
      console.log('网络离线，保存消息到离线队列')

      // 创建临时消息ID
      const tempId = `temp-${Date.now()}`
      const clientMessageId = `client-${Date.now()}`

      // 创建临时消息对象 - 简化版，只保留必要字段
      const tempMessage = {
        sender: userInfo.value.id,
        receiver: currentContact.value.id,
        content: inputMessage.value,
        messageType: 'TEXT',
        time: new Date().toISOString(),
        senderId: userInfo.value.id,
        receiverId: currentContact.value.id,
        isSelf: true,
        id: tempId,
        isRead: false, // 只保留已读状态
        // 添加会话ID，用于离线消息处理
        conversationId: dbManager.getConversationId(userInfo.value.id, currentContact.value.id),
        // 添加客户端消息ID，用于前端消息ID映射
        clientMessageId: clientMessageId,
        // 添加消息状态
        status: 'OFFLINE'
      }

      try {
        // 保存到IndexedDB
        await dbManager.saveMessage(tempMessage)

        // 添加临时消息到聊天窗口
        messages.value.push(tempMessage)

        // 更新消息离线信息
        updateMessageStatus(tempId, false, {
          errorMessage: '消息已保存，将在网络恢复后发送'
        })

        // 更新联系人列表中的最新消息
        debouncedUpdateContactLastMessage({
          ...tempMessage
        })

        ElMessage.info('网络离线，消息已保存，将在网络恢复后发送')
      } catch (error) {
        console.error('保存离线消息失败:', error)

        // 更新消息错误信息
        updateMessageStatus(tempId, false, {
          errorMessage: '保存离线消息失败: ' + (error.message || '未知错误')
        })

        ElMessage.error('保存离线消息失败，请稍后重试')
        return // 保存失败时提前返回，不清空输入框
      }
    }

    // 如果有chatListRef，刷新联系人列表（使用防抖版本）
    if (chatListRef.value && isOnline) {
      chatListRef.value.debouncedFetchContacts()
    }

    // 清空
    inputMessage.value = ''
    previewList.value.forEach(item => item.url && URL.revokeObjectURL(item.url))
    previewList.value = []
    scrollToBottom()

    if (isOnline) {
      ElMessage.success('发送成功')
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送失败')
  } finally {
    sending.value = false
  }
}

// 表情相关函数
const toggleEmojiPicker = () => {
  // 延迟切换表情选择器状态，避免DOM元素引用问题
  setTimeout(() => {
    showEmojiPicker.value = !showEmojiPicker.value
  }, 0)
}

// 关闭表情选择器
const closeEmojiPicker = () => {
  // 延迟关闭表情选择器，避免DOM元素引用问题
  setTimeout(() => {
    showEmojiPicker.value = false
  }, 0)
}

// 点击页面其他区域关闭表情选择器
const handleClickOutside = (e) => {
  if (showEmojiPicker.value && e.target && !e.target.closest('.emoji-picker-fix') && !e.target.closest('.el-button[icon="SmileFace"]')) {
    closeEmojiPicker()
  }
}

// 使用vue3-emoji-picker的选择表情方法
const onEmojiSelect = (emoji) => {
  // emoji.i是表情的Unicode字符
  if (emoji && emoji.i) {
    inputMessage.value += emoji.i
    // 选择表情后不要立即关闭选择器，让用户可以继续选择
  }
}

// 图片上传相关函数
const openImageUpload = () => {
  imageUploadVisible.value = true
  selectedImage.value = null
}

const handleImageSelected = (file) => {
  selectedImage.value = file.raw
}

// 处理文件选择
const handleFileSelected = (file) => {
  selectedFile.value = file.raw
}

// 发送图片消息
const sendImageMessage = async () => {
  if (!selectedImage.value || !currentContact.value) return
  if (!userInfo.value || !userInfo.value.id) {
    ElMessage.error('用户未登录或用户信息不完整')
    return
  }

  // 设置全局标记，表示正在上传图片
  window._uploadingImage = true;

  try {
    sending.value = true

    // 创建临时消息ID
    const tempId = `temp-${Date.now()}`;
    const clientMessageId = `client-img-${Date.now()}`;

    // 添加临时消息到聊天窗口
    messages.value.push({
      sender: userInfo.value.id,
      receiver: currentContact.value.id,
      content: '图片消息',
      messageType: 'IMAGE',
      time: new Date().toISOString(),
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      isSelf: true,
      id: tempId,
      isRead: false, // 只保留已读状态
      // 添加本地图片预览
      imageUrl: URL.createObjectURL(selectedImage.value),
      // 添加客户端消息ID，用于前端消息ID映射
      clientMessageId: clientMessageId,
      // 添加消息状态
      status: 'SENDING'
    });

    // 滚动到底部
    scrollToBottom();

    // 准备消息数据 - 符合后端 MessageDTO 格式
    const messageDTO = {
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      message: '图片消息',
      messageType: 'IMAGE',
      clientMessageId: clientMessageId, // 添加客户端消息ID，用于前端消息ID映射
      timestamp: Date.now(), // 添加时间戳（使用毫秒时间戳）
      conversationId: currentContact.value.conversationId // 添加会话ID
    }

    // 使用修复版本的 sendChatMessage 函数发送，传入文件对象
    const { sendChatMessage } = await import('@/utils/websocket-fix-sender');
    const result = await sendChatMessage(messageDTO, selectedImage.value)

    if (result && result.code === 200) {
      // 更新消息ID、时间和状态
      updateMessageStatus(tempId, false, {
        id: result.data.id,
        time: result.data.timestamp || new Date().toISOString(),
        fileUrl: result.data.fileUrl || null,
        status: 'SENT' // 明确设置状态为已发送
      });

      // 关闭对话框
      imageUploadVisible.value = false

      // 清除选中的图片
      selectedImage.value = null

      ElMessage.success('图片发送成功')
    } else {
      // 更新消息错误信息和状态
      updateMessageStatus(tempId, false, {
        errorMessage: result?.message || '发送失败',
        status: 'ERROR' // 明确设置状态为错误
      });

      ElMessage.error('图片发送失败')
    }
  } catch (error) {
    console.error('发送图片失败:', error)

    // 查找临时消息并更新状态
    const tempMessage = messages.value.find(m => m.id === `temp-${Date.now()}`);
    if (tempMessage) {
      updateMessageStatus(tempMessage.id, false, {
        errorMessage: error.message || '发送失败',
        status: 'ERROR' // 明确设置状态为错误
      });
    }

    ElMessage.error({
      message: '图片发送失败',
      duration: 2000
    })
  } finally {
    sending.value = false

    // 清除上传图片标记
    setTimeout(() => {
      window._uploadingImage = false;
      console.log('清除上传图片标记');
    }, 1000); // 延迟1秒清除，确保其他错误处理已完成
  }
}

// 使用现有的文件上传函数

// 发送文件消息
const sendFileMessage = async () => {
  if (!selectedFile.value || !currentContact.value) return
  if (!userInfo.value || !userInfo.value.id) {
    ElMessage.error('用户未登录或用户信息不完整')
    return
  }

  try {
    sending.value = true

    // 创建临时消息ID
    const tempId = `temp-${Date.now()}`;
    const clientMessageId = `client-file-${Date.now()}`;

    // 添加临时消息到聊天窗口
    messages.value.push({
      sender: userInfo.value.id,
      receiver: currentContact.value.id,
      content: selectedFile.value.name,
      messageType: 'DOCUMENT',
      time: new Date().toISOString(),
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      isSelf: true,
      id: tempId,
      isRead: false, // 只保留已读状态
      fileName: selectedFile.value.name,
      fileSize: selectedFile.value.size,
      // 添加客户端消息ID，用于前端消息ID映射
      clientMessageId: clientMessageId,
      // 添加消息状态
      status: 'SENDING'
    });

    // 滚动到底部
    scrollToBottom();

    // 准备消息数据 - 符合后端 MessageDTO 格式
    const messageDTO = {
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      message: selectedFile.value.name, // 文件名作为消息内容
      messageType: 'DOCUMENT',
      clientMessageId: clientMessageId, // 添加客户端消息ID，用于前端消息ID映射
      timestamp: Date.now(), // 添加时间戳（使用毫秒时间戳）
      conversationId: currentContact.value.conversationId // 添加会话ID
    }

    // 使用修复版本的 sendChatMessage 函数发送，传入文件对象
    const { sendChatMessage } = await import('@/utils/websocket-fix-sender');
    const result = await sendChatMessage(messageDTO, selectedFile.value)

    if (result && result.code === 200) {
      // 更新消息ID、时间和状态
      updateMessageStatus(tempId, false, {
        id: result.data.id,
        time: result.data.timestamp || new Date().toISOString(),
        fileUrl: result.data.fileUrl || null,
        status: 'SENT' // 明确设置状态为已发送
      });

      // 关闭对话框
      fileUploadVisible.value = false

      // 清除选中的文件
      selectedFile.value = null

      ElMessage.success('文件发送成功')
    } else {
      // 更新消息错误信息和状态
      updateMessageStatus(tempId, false, {
        errorMessage: result?.message || '发送失败',
        status: 'ERROR' // 明确设置状态为错误
      });

      ElMessage.error('文件发送失败')
    }
  } catch (error) {
    console.error('发送文件失败:', error)

    // 查找临时消息并更新状态
    const tempMessage = messages.value.find(m => m.id === `temp-${Date.now()}`);
    if (tempMessage) {
      updateMessageStatus(tempMessage.id, false, {
        errorMessage: error.message || '发送失败',
        status: 'ERROR' // 明确设置状态为错误
      });
    }

    ElMessage.error({
      message: '文件发送失败',
      duration: 2000
    })
  } finally {
    sending.value = false
  }
}

const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  }
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (messagesContainer.value) {
    const scrollbar = messagesContainer.value.querySelector('.el-scrollbar__wrap')
    if (scrollbar) {
      scrollbar.scrollTop = scrollbar.scrollHeight
    }
  }
}

// 处理消息滚动事件
const handleMessageScroll = (e) => {
  if (!currentContact.value || loadingMore.value || noMoreMessages.value) return;

  const scrollbar = e.target;
  const scrollTop = scrollbar.scrollTop;

  // 当滚动到顶部附近时（距离顶部50px以内），加载更多消息
  if (scrollTop < 50) {
    console.log('滚动到顶部附近，加载更多历史消息');
    loadMoreMessages();
  }

  // 标记正在滚动
  isScrolling.value = true;

  // 清除之前的定时器
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value);
  }

  // 设置新的定时器，滚动停止后重置状态
  scrollTimer.value = setTimeout(() => {
    isScrolling.value = false;
  }, 200);
}

// 滚动到指定元素
const scrollToElement = (elementSelector, fallbackScrollTop = 50) => {
  if (!messagesContainer.value) return;

  nextTick(() => {
    // 等待DOM完全更新
    setTimeout(() => {
      const element = document.querySelector(elementSelector);
      if (element) {
        // 使用scrollIntoView滚动到元素，添加平滑滚动效果
        element.scrollIntoView({
          block: 'start',
          behavior: 'smooth' // 使用平滑滚动
        });
        console.log('平滑滚动到指定元素:', elementSelector);

        // 添加高亮效果
        element.classList.add('message-highlight');

        // 移除高亮效果
        setTimeout(() => {
          element.classList.remove('message-highlight');
        }, 1500);
      } else {
        console.log('未找到指定元素，使用备用滚动位置');
        // 备用方案：如果找不到元素，使用指定的滚动位置
        const scrollbar = messagesContainer.value.querySelector('.el-scrollbar__wrap');
        if (scrollbar) {
          // 使用平滑滚动效果
          scrollbar.style.scrollBehavior = 'smooth';
          scrollbar.scrollTop = fallbackScrollTop;

          // 恢复默认滚动行为
          setTimeout(() => {
            scrollbar.style.scrollBehavior = '';
          }, 500);
        }
      }
    }, 100); // 延长延迟时间，确保DOM已完全更新
  });
}

// 格式化日期
const formatDate = (time) => {
  if (!time) return ''

  const date = new Date(time)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  // 如果是今天
  if (date.toDateString() === today.toDateString()) {
    return '今天'
  }

  // 如果是昨天
  if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  }

  // 其他日期
  return date.toLocaleDateString([], {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''

  const date = new Date(time)
  return date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 创建新聊天
const createNewChat = () => {
  ElMessage({
    message: '请在失物招领详情页找到物品主人开始聊天',
    type: 'info'
  });
}

// 处理会话操作
const handleConversationAction = async (event) => {
  const { action, contact } = event

  if (!contact || !contact.conversationId) {
    ElMessage.warning('无法执行操作：会话ID不存在')
    return
  }

  try {
    switch (action) {
      case 'pin':
        // 置顶会话
        if (await conversationStore.updatePinned(contact.conversationId, true)) {
          // 更新本地联系人数据
          const updatedContact = contacts.value.find(c => c.id === contact.id)
          if (updatedContact) {
            updatedContact.isPinned = true
            // 触发响应式更新
            contacts.value = [...contacts.value]
            ElMessage.success('会话已置顶')
          }
        }
        break

      case 'unpin':
        // 取消置顶
        if (await conversationStore.updatePinned(contact.conversationId, false)) {
          // 更新本地联系人数据
          const updatedContact = contacts.value.find(c => c.id === contact.id)
          if (updatedContact) {
            updatedContact.isPinned = false
            // 触发响应式更新
            contacts.value = [...contacts.value]
            ElMessage.success('已取消置顶')
          }
        }
        break

      case 'mute':
        // 静音会话
        if (await conversationStore.updateMuted(contact.conversationId, true)) {
          // 更新本地联系人数据
          const updatedContact = contacts.value.find(c => c.id === contact.id)
          if (updatedContact) {
            updatedContact.isMuted = true
            // 触发响应式更新
            contacts.value = [...contacts.value]
            ElMessage.success('会话已静音')
          }
        }
        break

      case 'unmute':
        // 取消静音
        if (await conversationStore.updateMuted(contact.conversationId, false)) {
          // 更新本地联系人数据
          const updatedContact = contacts.value.find(c => c.id === contact.id)
          if (updatedContact) {
            updatedContact.isMuted = false
            // 触发响应式更新
            contacts.value = [...contacts.value]
            ElMessage.success('已取消静音')
          }
        }
        break

      case 'archive':
        // 归档会话
        ElMessageBox.confirm('确定要归档此会话吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          if (await conversationStore.updateStatus(contact.conversationId, 'ARCHIVED')) {
            // 从联系人列表中移除
            contacts.value = contacts.value.filter(c => c.id !== contact.id)
            ElMessage.success('会话已归档')

            // 如果当前选中的是被归档的联系人，清除选择
            if (currentContact.value && currentContact.value.id === contact.id) {
              currentContact.value = null
              messages.value = []
            }
          }
        }).catch(() => {})
        break

      case 'delete':
        // 删除会话
        ElMessageBox.confirm('确定要删除此会话吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          if (await conversationStore.updateStatus(contact.conversationId, 'DELETED')) {
            // 从联系人列表中移除
            contacts.value = contacts.value.filter(c => c.id !== contact.id)
            ElMessage.success('会话已删除')

            // 如果当前选中的是被删除的联系人，清除选择
            if (currentContact.value && currentContact.value.id === contact.id) {
              currentContact.value = null
              messages.value = []
            }
          }
        }).catch(() => {})
        break

      default:
        console.warn(`未知的会话操作: ${action}`)
    }
  } catch (error) {
    console.error(`执行会话操作 ${action} 失败:`, error)
    ElMessage.error(`操作失败: ${error.message || '未知错误'}`)
  }
}

// 运行WebSocket诊断
const runWebSocketDiagnostics = async () => {
  console.group('WebSocket诊断');
  chatLogger.info('开始WebSocket诊断');

  try {
    // 运行诊断
    const diagnosticResult = await diagnoseWebSocketIssues();
    chatLogger.info('诊断结果:', diagnosticResult);

    // 显示诊断结果
    if (diagnosticResult.hasIssues) {
      ElMessage.warning({
        message: `发现 ${diagnosticResult.issues.length} 个连接问题，请查看控制台`,
        duration: 5000
      });

      chatLogger.error('WebSocket连接问题:');
      diagnosticResult.issues.forEach((issue, index) => {
        chatLogger.error(`${index + 1}. ${issue}`);
      });

      // 尝试重新连接
      chatLogger.info('尝试重新初始化WebSocket服务...');
      const { initializeWebSocketService } = await import('@/utils/websocket/index');
      const initialized = await initializeWebSocketService();

      if (initialized) {
        chatLogger.info('WebSocket服务重新初始化成功');
        ElMessage.success('WebSocket服务已重新连接');
      } else {
        chatLogger.error('WebSocket服务重新初始化失败');
        ElMessage.error('WebSocket服务重新连接失败');
      }
    } else {
      ElMessage.success({
        message: 'WebSocket连接正常',
        duration: 3000
      });

      chatLogger.info('WebSocket连接正常');
    }
  } catch (error) {
    chatLogger.error('WebSocket诊断失败:', error);
    ElMessage.error('WebSocket诊断失败，请查看控制台');
  }

  console.groupEnd();
}

// 从URL参数获取要打开的联系人ID
const openContactFromUrl = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const contactId = urlParams.get('contactId');

  if (contactId && contacts.value.length > 0) {
    const contact = contacts.value.find(c => c.id.toString() === contactId);
    if (contact) {
      selectContact(contact);
    }
  }
}



// 处理刷新联系人列表事件 - 只更新最新消息，不刷新整个列表
const handleRefreshContacts = (event) => {
  try {
    console.log('收到刷新联系人列表事件:', event);
    const { message } = event.detail;

    if (!message) {
      console.warn('刷新联系人列表事件缺少消息数据');
      return;
    }

    console.log('更新最新消息 - 消息:', message);

    // 只更新联系人列表中的最新消息，不刷新整个列表
    updateContactLastMessage(message);

    // 更新联系人在线状态
    if (chatListRef.value && typeof chatListRef.value.updateContactsOnlineStatus === 'function') {
      setTimeout(() => {
        chatListRef.value.updateContactsOnlineStatus();
      }, 500);
    }
  } catch (error) {
    console.error('处理刷新联系人列表事件时出错:', error);
  }
};

// 注释：不再使用强制刷新聊天事件，避免页面闪烁
// 相关事件监听已在onMounted和onUnmounted中注释掉

// 更新网络状态
const updateNetworkStatus = (status) => {
  networkStatus.value = { ...networkStatus.value, ...status };

  // 如果连接成功，更新最后连接时间
  if (status.connected === true) {
    networkStatus.value.lastConnected = Date.now();
  }

  console.log('网络状态已更新:', networkStatus.value);
};

// 处理WebSocket连接成功事件
const handleWebSocketConnected = async (event) => {
  try {
    console.log('收到WebSocket连接成功事件:', event);
    console.log('WebSocket连接详情:', {
      timestamp: new Date().toISOString(),
      eventType: event.type,
      detail: event.detail,
      currentUserId: userInfo.value?.id
    });

    // 更新网络状态
    updateNetworkStatus({
      connected: true,
      reconnecting: false,
      attempts: 0
    });

    // 连接成功后，检查WebSocket订阅状态
    setTimeout(async () => {
      try {
        // 检查WebSocket连接状态
        const { isWebSocketConnected } = await import('@/utils/websocket/index');
        const connected = await isWebSocketConnected();

        if (connected) {
          // 诊断WebSocket连接状态
          try {
            const diagnosis = await diagnoseWebSocketIssues();

            if (diagnosis.hasIssues) {
              chatLogger.warn('WebSocket连接成功后诊断发现问题:', diagnosis.issues);

              // 尝试重新初始化WebSocket服务
              const { initializeWebSocketService } = await import('@/utils/websocket/index');
              await initializeWebSocketService();

              chatLogger.info('WebSocket服务已重新初始化');
            } else {
              chatLogger.info('WebSocket连接成功后诊断未发现问题');
            }
          } catch (error) {
            chatLogger.error('WebSocket诊断失败:', error);
          }
        }
      } catch (error) {
        console.error('WebSocket连接成功后检查订阅状态失败:', error);
      }
    }, 1000); // 延迟1秒执行，确保WebSocket连接已完全建立

    // 只更新联系人在线状态，不刷新整个列表
    if (chatListRef.value && typeof chatListRef.value.updateContactsOnlineStatus === 'function') {
      console.log('WebSocket连接成功，只更新联系人在线状态');
      setTimeout(() => {
        chatListRef.value.updateContactsOnlineStatus();

        // 记录联系人详情，帮助排查问题
        contacts.value.forEach(contact => {
          console.log('联系人详情:', {
            id: contact.id,
            name: contact.name,
            unreadCount: contact.unreadCount,
            online: contact.online ? '在线' : '离线'
          });
        });
      }, 1000);
    }

    // 处理离线消息 - 检查所有联系人的未读消息
    if (contacts.value && contacts.value.length > 0) {
      console.log('开始处理离线消息，检查所有联系人的未读消息');

      // 如果用户已经选择了联系人，确保当前选中联系人的未读计数为0
      if (currentContact.value) {
        console.log('用户已选择联系人:', currentContact.value.name);
        // 确保当前选中联系人的未读计数为0
        const currentContactIndex = contacts.value.findIndex(c => String(c.id) === String(currentContact.value.id));
        if (currentContactIndex !== -1) {
          // 无论是否有未读消息，都重置为0，确保一致性
          const oldUnreadCount = contacts.value[currentContactIndex].unreadCount || 0;
          if (oldUnreadCount > 0) {
            console.log(`重置当前联系人 ${contacts.value[currentContactIndex].name} 的未读计数: ${oldUnreadCount} -> 0`);
            contacts.value[currentContactIndex].unreadCount = 0;

            // 同步到服务器
            try {
              await conversationStore.clearUnreadCount(userInfo.value.id, currentContact.value.id);
              console.log(`已通过会话管理Store重置联系人 ${currentContact.value.name} 的未读计数（数据库和Redis都已更新）`);
            } catch (error) {
              console.error('重置未读计数失败:', error);
            }
          } else {
            console.log(`当前联系人 ${contacts.value[currentContactIndex].name} 没有未读消息，无需重置`);
          }
        }
      }

      // 过滤掉当前联系人后，检查其他联系人是否有未读消息
      const otherContactsWithUnread = contacts.value.filter(contact =>
        contact.unreadCount > 0 &&
        (!currentContact.value || String(contact.id) !== String(currentContact.value.id))
      );

      console.log(`发现 ${otherContactsWithUnread.length} 个其他联系人有未读消息`);

      if (otherContactsWithUnread.length > 0) {
        // 显示通知
        ElMessage({
          message: `您有来自 ${otherContactsWithUnread.length} 个联系人的未读消息`,
          type: 'info',
          duration: 5000
        });
      }
    }

    // 如果有当前联系人，刷新聊天历史和在线状态
    if (currentContact.value && currentContact.value.id) {
      console.log('WebSocket连接成功，刷新当前聊天历史');
      await loadChatHistory(currentContact.value.id);

      // 重新订阅联系人状态
      subscribeContactStatus(currentContact.value.id.toString());

      // 强制刷新联系人在线状态
      const isOnline = isContactOnline(currentContact.value.id);
      chatLogger.info(`WebSocket连接成功后，重新检查联系人 ${currentContact.value.name} 在线状态: ${isOnline ? '在线' : '离线'}`);

      // 更新状态
      if (currentContact.value.online !== isOnline) {
        currentContact.value.online = isOnline;
        // 强制更新视图
        currentContact.value = { ...currentContact.value };
        chatLogger.info(`已更新当前联系人 ${currentContact.value.name} 的在线状态为: ${isOnline ? '在线' : '离线'}`);
      }
    }

    // 不在这里播放声音，WebSocket连接成功不需要播放声音
    // 用户上线的声音由全局消息处理器统一管理
    console.log('WebSocket连接成功，不播放声音');
  } catch (error) {
    console.error('处理WebSocket连接成功事件时出错:', error);
  }
};

// 处理全局刷新联系人列表事件 - 只更新在线状态，不刷新整个列表
const handleGlobalRefreshContacts = (event) => {
  try {
    console.log('收到全局刷新联系人列表事件:', event);

    // 只更新联系人在线状态，不刷新整个列表
    if (chatListRef.value && typeof chatListRef.value.updateContactsOnlineStatus === 'function') {
      console.log('全局事件 - 只更新联系人在线状态');
      chatListRef.value.updateContactsOnlineStatus();
    }
  } catch (error) {
    console.error('处理全局刷新联系人列表事件时出错:', error);
  }
};

// 处理WebSocket断开连接事件
const handleWebSocketDisconnected = (event) => {
  try {
    console.log('收到WebSocket断开连接事件:', event);

    // 更新网络状态
    updateNetworkStatus({
      connected: false,
      reconnecting: false
    });

    // 显示网络断开提示
    ElMessage.warning({
      message: '网络连接已断开，正在尝试重新连接...',
      duration: 5000
    });
  } catch (error) {
    console.error('处理WebSocket断开连接事件时出错:', error);
  }
};

// 处理WebSocket重连事件
const handleWebSocketReconnecting = (event) => {
  try {
    console.log('收到WebSocket重连事件:', event);
    const { attempts } = event.detail || {};

    // 更新网络状态
    updateNetworkStatus({
      connected: false,
      reconnecting: true,
      attempts: attempts || networkStatus.value.attempts + 1
    });
  } catch (error) {
    console.error('处理WebSocket重连事件时出错:', error);
  }
};

// 处理用户在线状态更新事件
const handleUserOnlineStatus = (event) => {
  try {
    const { userId, isOnline } = event.detail
    chatLogger.info(`收到用户在线状态更新: 用户 ${userId} 状态为 ${isOnline ? '在线' : '离线'}`)

    // 如果是当前联系人，更新其在线状态
    if (currentContact.value && String(currentContact.value.id) === String(userId)) {
      if (currentContact.value.online !== isOnline) {
        currentContact.value.online = isOnline
        chatLogger.info(`已更新当前联系人 ${currentContact.value.name} 的在线状态为: ${isOnline ? '在线' : '离线'}`)
      }
    } else {
      // 如果不是当前联系人，查找联系人列表中的联系人
      const contact = contacts.value.find(c => String(c.id) === String(userId))
      if (contact && isOnline) {
        // 如果联系人上线，使用通知管理器显示通知
        showContactOnlineNotification(contact, currentContact.value)
      }
    }
  } catch (error) {
    chatLogger.error('处理用户在线状态更新事件错误:', error)
  }
};

// 处理联系人状态更新事件
const handleContactStatusUpdate = (event) => {
  try {
    const { contactId, status } = event.detail
    const isOnline = status && status.online

    // 添加更详细的调试日志
    chatLogger.info(`收到联系人状态更新事件: 联系人 ${contactId} 状态为 ${isOnline ? '在线' : '离线'}`);
    console.log('状态更新事件详情:', event.detail);
    console.log('当前联系人:', currentContact.value);

    // 如果是当前联系人，更新其在线状态
    if (currentContact.value && String(currentContact.value.id) === String(contactId)) {
      chatLogger.info(`匹配到当前联系人 ${currentContact.value.name}，当前状态: ${currentContact.value.online ? '在线' : '离线'}`);

      if (currentContact.value.online !== isOnline) {
        // 更新状态前记录
        chatLogger.info(`准备更新联系人状态: ${currentContact.value.online ? '在线' : '离线'} -> ${isOnline ? '在线' : '离线'}`);

        // 更新状态
        currentContact.value.online = isOnline;

        // 强制更新视图
        currentContact.value = { ...currentContact.value };

        chatLogger.info(`已更新当前联系人 ${currentContact.value.name} 的在线状态为: ${isOnline ? '在线' : '离线'}`);
      } else {
        chatLogger.info(`联系人 ${currentContact.value.name} 状态未变化，保持 ${isOnline ? '在线' : '离线'}`);
      }
    } else {
      chatLogger.info('收到的联系人状态更新不是当前选中的联系人');

      // 查找联系人列表中的联系人
      const contact = contacts.value.find(c => String(c.id) === String(contactId))
      if (contact && isOnline) {
        // 如果联系人上线，使用通知管理器显示通知
        showContactOnlineNotification(contact, currentContact.value)
      }
    }
  } catch (error) {
    chatLogger.error('处理联系人状态更新事件错误:', error);
    console.error('错误详情:', error);
  }
};

// 处理在线用户列表更新事件
const handleOnlineUsersUpdated = (event) => {
  try {
    const { users } = event.detail
    chatLogger.info(`收到在线用户列表更新: ${users.length} 个用户在线`)

    // 如果当前有选中的联系人，检查其在线状态
    if (currentContact.value && currentContact.value.id) {
      const isOnline = users.includes(String(currentContact.value.id))
      if (currentContact.value.online !== isOnline) {
        currentContact.value.online = isOnline
        chatLogger.info(`根据在线用户列表更新，当前联系人 ${currentContact.value.name} 的在线状态为: ${isOnline ? '在线' : '离线'}`)
      }
    }

    // 检查联系人列表中的其他联系人
    contacts.value.forEach(contact => {
      if (currentContact.value && String(contact.id) === String(currentContact.value.id)) {
        return; // 跳过当前联系人
      }

      const wasOnline = contact.online;
      const isNowOnline = users.includes(String(contact.id));

      // 如果状态从离线变为在线，显示通知
      if (!wasOnline && isNowOnline) {
        showContactOnlineNotification(contact, currentContact.value);
      }

      // 更新联系人状态
      if (wasOnline !== isNowOnline) {
        contact.online = isNowOnline;
      }
    });
  } catch (error) {
    chatLogger.error('处理在线用户列表更新事件错误:', error)
  }
};

// 注意：diagnoseWebSocketIssues 函数已在上面定义，这里不需要重复定义

// 处理网络状态变化
const handleNetworkStatusChange = async () => {
  const isOnline = navigator.onLine
  chatLogger.info('网络状态变化:', isOnline ? '在线' : '离线')

  // 更新网络状态
  networkStatus.value.connected = isOnline

  if (isOnline) {
    try {
      // 网络恢复，尝试重连WebSocket
      const { initializeWebSocketService, isWebSocketConnected } = await import('@/utils/websocket/index');

      // 初始化WebSocket服务
      await initializeWebSocketService();

      // 检查连接状态
      const connected = await isWebSocketConnected();
      chatLogger.info('WebSocket连接状态:', connected ? '已连接' : '未连接');

      if (connected) {
        // 更新离线消息计数
        await updateOfflineMessageCount();

        // 自动处理离线消息
        if (offlineMessageCount.value > 0) {
          await processOfflineMessages();
        }

        // 刷新联系人列表
        if (chatListRef.value) {
          chatListRef.value.fetchContacts();
        }

        // 如果有当前联系人，刷新聊天历史
        if (currentContact.value) {
          await loadChatHistory(currentContact.value.id);
        }

        ElMessage.success('网络已恢复，离线消息已发送');
      }
    } catch (error) {
      chatLogger.error('网络恢复后初始化WebSocket失败:', error);
      ElMessage.warning('网络已恢复，但聊天服务连接失败');
    }
  } else {
    // 网络断开
    ElMessage.warning('网络已断开，消息将在网络恢复后发送');
  }
}

// 更新离线消息计数
const updateOfflineMessageCount = async () => {
  try {
    const offlineMessages = await dbManager.getOfflineMessages()
    offlineMessageCount.value = offlineMessages.length
    console.log(`发现 ${offlineMessageCount.value} 条离线消息`)
  } catch (error) {
    console.error('获取离线消息计数失败:', error)
    offlineMessageCount.value = 0
  }
}

// 手动处理离线消息
const processOfflineMessages = async () => {
  if (!navigator.onLine) {
    ElMessage.warning('网络离线，无法发送消息')
    return
  }

  try {
    // 检查是否有文件消息正在发送
    if (window._fileMessageSent) {
      console.log('检测到文件消息正在发送，延迟处理离线消息');
      ElMessage.info('有文件消息正在发送，请稍后再试');

      // 延迟2秒后再次尝试
      setTimeout(() => {
        if (!window._fileMessageSent) {
          processOfflineMessages();
        } else {
          console.log('文件消息仍在发送，取消处理离线消息');
        }
      }, 2000);

      return;
    }

    ElMessage.info('正在发送离线消息...')

    // 确保离线消息处理器已初始化
    if (!offlineMessageHandler.value) {
      offlineMessageHandler.value = await createOfflineMessageHandler();
    }

    await offlineMessageHandler.value.processOfflineMessages()

    // 更新离线消息计数
    await updateOfflineMessageCount()

    if (offlineMessageCount.value === 0) {
      ElMessage.success('所有离线消息已发送')
    } else {
      ElMessage.warning(`仍有 ${offlineMessageCount.value} 条消息未发送成功`)
    }
  } catch (error) {
    console.error('处理离线消息失败:', error)
    ElMessage.error('处理离线消息失败，请稍后重试')
  }
}

// 加载缓存的联系人
const loadCachedContacts = async () => {
  try {
    const cachedContacts = await dbManager.getAllContacts()

    if (cachedContacts.length > 0) {
      console.log(`从本地缓存加载了 ${cachedContacts.length} 个联系人`)
      contacts.value = cachedContacts

      // 从URL参数获取要打开的联系人ID
      const urlParams = new URLSearchParams(window.location.search)
      const contactId = urlParams.get('contactId')

      if (contactId) {
        const contact = cachedContacts.find(c => c.id.toString() === contactId)
        if (contact) {
          selectContact(contact)
        }
      }
    }
  } catch (error) {
    console.error('加载缓存联系人失败:', error)
  }
}

// 跟踪用户活跃状态
const trackUserActivity = () => {
  window._userActiveTimestamp = Date.now();
}

// 在组件挂载时初始化
onMounted(async () => {
  // 添加临时 WebSocket 诊断函数
  const diagnoseWebSocketConnection = async () => {
    console.log('【临时诊断】开始 WebSocket 连接诊断...');

    try {
      // 检查 WebSocket 连接状态
      const { isWebSocketConnected, getSubscriptions } = await import('@/utils/websocket/index');
      const connected = await isWebSocketConnected();

      console.log('【临时诊断】WebSocket 连接状态:', connected ? '已连接' : '未连接');

      if (connected) {
        // 获取当前订阅信息
        const subscriptions = await getSubscriptions();
        console.log('【临时诊断】当前 WebSocket 订阅:', subscriptions);

        // 检查是否有 /user/queue/private 订阅
        const hasPrivateSubscription = subscriptions.some(sub =>
          sub.destination && sub.destination.includes('/queue/private'));

        console.log('【临时诊断】是否订阅了私人消息:', hasPrivateSubscription);

        if (!hasPrivateSubscription) {
          console.warn('【临时诊断】警告: 未订阅私人消息路径 /user/queue/private');
        }
      } else {
        console.warn('【临时诊断】警告: WebSocket 未连接，尝试重新连接...');

        // 尝试重新连接
        const { initializeWebSocketService } = await import('@/utils/websocket/index');
        const initialized = await initializeWebSocketService();

        console.log('【临时诊断】WebSocket 重新连接结果:', initialized ? '成功' : '失败');
      }
    } catch (error) {
      console.error('【临时诊断】WebSocket 诊断出错:', error);
    }
  };

  // 执行 WebSocket 诊断
  diagnoseWebSocketConnection();

  // 添加临时调试代码，直接监听 WebSocket 消息
  window.addEventListener('chat-message', (event) => {
    console.log('【临时调试】直接监听到聊天消息:', event.detail);
    // 尝试直接添加消息到聊天窗口
    try {
      const message = event.detail;
      if (message && message.senderId && message.receiverId) {
        console.log('【临时调试】消息发送者:', message.senderId, '接收者:', message.receiverId);
        console.log('【临时调试】当前用户ID:', userInfo.value.id);
        console.log('【临时调试】当前联系人ID:', currentContact.value?.id);

        // 检查消息是否与当前用户相关
        const isRelatedToCurrentUser =
          String(message.senderId) === String(userInfo.value.id) ||
          String(message.receiverId) === String(userInfo.value.id);

        console.log('【临时调试】消息是否与当前用户相关:', isRelatedToCurrentUser);

        if (isRelatedToCurrentUser) {
          // 检查是否是当前聊天
          const isSenderCurrentContact = currentContact.value && String(message.senderId) === String(currentContact.value.id);
          const isReceiverCurrentContact = currentContact.value && String(message.receiverId) === String(currentContact.value.id);
          const isCurrentChat = isSenderCurrentContact || isReceiverCurrentContact;

          console.log('【临时调试】是否是当前聊天:', isCurrentChat);

          if (isCurrentChat) {
            // 格式化消息
            const formattedMessage = {
              id: message.id || `debug-${Date.now()}`,
              content: message.content || message.message || '[空消息]',
              senderId: message.senderId,
              receiverId: message.receiverId,
              messageType: message.messageType || 'TEXT',
              time: message.time || message.timestamp || new Date().toISOString(),
              isSelf: String(message.senderId) === String(userInfo.value.id),
              status: 'SENT'
            };

            console.log('【临时调试】格式化后的消息:', formattedMessage);

            // 检查消息是否已存在
            const isDuplicate = messages.value.some(m =>
              m.id === formattedMessage.id ||
              (m.content === formattedMessage.content &&
               String(m.senderId) === String(formattedMessage.senderId) &&
               Math.abs(new Date(m.time) - new Date(formattedMessage.time)) < 5000)
            );

            console.log('【临时调试】消息是否重复:', isDuplicate);

            if (!isDuplicate) {
              console.log('【临时调试】直接添加消息到聊天窗口');
              messages.value.push(formattedMessage);

              // 滚动到底部
              nextTick(() => scrollToBottom());
            }
          }
        }
      }
    } catch (error) {
      console.error('【临时调试】处理消息出错:', error);
    }
  });

  // 初始化用户活跃状态跟踪
  window._userActiveTimestamp = Date.now();

  // 添加用户活跃状态跟踪事件
  const activityEvents = ['mousedown', 'keydown', 'touchstart', 'click', 'scroll'];
  activityEvents.forEach(event => {
    document.addEventListener(event, trackUserActivity);
  });

  // 添加备用事件监听器
  window.addEventListener('chat-message-backup', (event) => {
    console.log('【临时调试】通过备用事件监听到聊天消息:', event.detail);
    // 尝试直接添加消息到聊天窗口
    try {
      const message = event.detail;
      if (message && message.senderId && message.receiverId) {
        console.log('【临时调试】备用事件 - 消息发送者:', message.senderId, '接收者:', message.receiverId);
        console.log('【临时调试】备用事件 - 当前用户ID:', userInfo.value.id);
        console.log('【临时调试】备用事件 - 当前联系人ID:', currentContact.value?.id);

        // 检查消息是否与当前用户相关
        const isRelatedToCurrentUser =
          String(message.senderId) === String(userInfo.value.id) ||
          String(message.receiverId) === String(userInfo.value.id);

        console.log('【临时调试】备用事件 - 消息是否与当前用户相关:', isRelatedToCurrentUser);

        if (isRelatedToCurrentUser) {
          // 检查是否是当前聊天
          const isSenderCurrentContact = currentContact.value && String(message.senderId) === String(currentContact.value.id);
          const isReceiverCurrentContact = currentContact.value && String(message.receiverId) === String(currentContact.value.id);
          const isCurrentChat = isSenderCurrentContact || isReceiverCurrentContact;

          console.log('【临时调试】备用事件 - 是否是当前聊天:', isCurrentChat);

          if (isCurrentChat) {
            // 格式化消息
            const formattedMessage = {
              id: message.id || `debug-backup-${Date.now()}`,
              content: message.content || message.message || '[空消息]',
              senderId: message.senderId,
              receiverId: message.receiverId,
              messageType: message.messageType || 'TEXT',
              time: message.time || message.timestamp || new Date().toISOString(),
              isSelf: String(message.senderId) === String(userInfo.value.id),
              status: 'SENT'
            };

            console.log('【临时调试】备用事件 - 格式化后的消息:', formattedMessage);

            // 检查消息是否已存在
            const isDuplicate = messages.value.some(m =>
              m.id === formattedMessage.id ||
              (m.content === formattedMessage.content &&
               String(m.senderId) === String(formattedMessage.senderId) &&
               Math.abs(new Date(m.time) - new Date(formattedMessage.time)) < 5000)
            );

            console.log('【临时调试】备用事件 - 消息是否重复:', isDuplicate);

            if (!isDuplicate) {
              console.log('【临时调试】备用事件 - 直接添加消息到聊天窗口');
              messages.value.push(formattedMessage);

              // 滚动到底部
              nextTick(() => scrollToBottom());
            }
          }
        }
      }
    } catch (error) {
      console.error('【临时调试】备用事件 - 处理消息出错:', error);
    }
  });

  // 添加原始消息事件监听器
  window.addEventListener('chat-message-raw', (event) => {
    console.log('【临时调试】收到原始消息事件:', event.detail);
    try {
      if (event.detail && event.detail.body) {
        const rawBody = event.detail.body;
        console.log('【临时调试】原始消息体:', rawBody);

        // 尝试解析原始消息
        try {
          const parsedMessage = JSON.parse(rawBody);
          console.log('【临时调试】成功解析原始消息:', parsedMessage);

          // 手动触发 chat-message 事件
          window.dispatchEvent(new CustomEvent('chat-message', {
            detail: parsedMessage
          }));
          console.log('【临时调试】已手动触发 chat-message 事件');
        } catch (parseError) {
          console.error('【临时调试】解析原始消息失败:', parseError);
        }
      }
    } catch (error) {
      console.error('【临时调试】处理原始消息事件出错:', error);
    }
  });

  // 初始化IndexedDB
  try {
    await dbManager.open()
    chatLogger.info('IndexedDB初始化成功')

    // 初始化离线消息处理器
    offlineMessageHandler.value = await createOfflineMessageHandler();
    chatLogger.info('离线消息处理器初始化成功');

    // 加载本地缓存的联系人
    await loadCachedContacts()

    // 更新离线消息计数
    await updateOfflineMessageCount()

    // 定期清理过期数据
    const lastCleanupTime = await dbManager.getSetting('lastCleanupTime')
    const now = Date.now()

    if (!lastCleanupTime || now - lastCleanupTime > 7 * 24 * 60 * 60 * 1000) { // 每周清理一次
      chatLogger.info('开始清理过期数据')
      await dbManager.clearExpiredData(30) // 清理30天前的数据
      await dbManager.saveSetting('lastCleanupTime', now)
    }
  } catch (error) {
    chatLogger.error('IndexedDB初始化失败:', error)
  }

  await initChat()

  // 添加已读回执事件监听
  window.addEventListener('read-receipt', handleReadReceipt);

  // 添加网络状态变化监听
  window.addEventListener('online', handleNetworkStatusChange);
  window.addEventListener('offline', handleNetworkStatusChange);

  // 添加刷新联系人列表事件监听
  window.addEventListener('refresh-contacts', handleRefreshContacts);

  // 不再添加强制刷新聊天事件监听，避免页面闪烁
  // window.addEventListener('force-refresh-chat', handleForceRefreshChat);

  // 添加新消息通知事件监听
  window.addEventListener('new-message-notification', handleNewMessageEvent);

  // 添加聊天消息事件监听
  window.addEventListener('chat-message', handleNewMessageEvent);

  // 添加WebSocket连接成功事件监听
  window.addEventListener('websocket-connected', handleWebSocketConnected);

  // 添加WebSocket断开连接事件监听
  window.addEventListener('websocket-disconnected', handleWebSocketDisconnected);

  // 添加WebSocket重连事件监听
  window.addEventListener('websocket-reconnecting', handleWebSocketReconnecting);

  // 添加全局刷新联系人列表事件监听
  window.addEventListener('global-refresh-contacts', handleGlobalRefreshContacts);

  // 添加消息发送事件监听器
  window.addEventListener('message-sent', handleMessageSent);

  // 添加在线状态更新事件监听器
  window.addEventListener('user-online-status', handleUserOnlineStatus);
  window.addEventListener('contact-status-updated', handleContactStatusUpdate);
  window.addEventListener('online-users-updated', handleOnlineUsersUpdated);

  // 点击其他区域关闭表情选择器
  // 使用捕获阶段，确保在事件冒泡前处理
  document.addEventListener('click', handleClickOutside, true);

  // 设置定时器只更新当前联系人的在线状态，不刷新联系人列表
  const chatRefreshInterval = setInterval(() => {
    // 只有在页面处于活动状态且用户没有活跃操作时才更新
    if (document.visibilityState === 'visible' && !window._userActiveTimestamp ||
        (Date.now() - window._userActiveTimestamp > 10000)) { // 用户10秒内没有操作才更新

      // 只在必要时刷新聊天历史
      if (currentContact.value && currentContact.value.id) {
        // 检查是否需要刷新聊天历史（最后一条消息超过2分钟）
        const lastMessageTime = messages.value.length > 0 ?
          new Date(messages.value[messages.value.length - 1].time).getTime() : 0;
        const now = Date.now();

        if (now - lastMessageTime > 120000) { // 2分钟
          console.log('定时增量更新聊天历史');
          incrementalUpdateChatHistory(currentContact.value.id);
        }

        // 定时刷新当前联系人在线状态
        const isOnline = isContactOnline(currentContact.value.id);
        if (currentContact.value.online !== isOnline) {
          chatLogger.info(`定时检测到联系人 ${currentContact.value.name} 在线状态变化: ${currentContact.value.online ? '在线' : '离线'} -> ${isOnline ? '在线' : '离线'}`);
          currentContact.value.online = isOnline;
          // 强制更新视图
          currentContact.value = { ...currentContact.value };
        }
      }

      // 注意：已移除联系人列表的定时刷新
    }
  }, 60000); // 每60秒检查一次，只更新当前聊天和在线状态

  // 将定时器ID保存到ref中，以便在组件卸载时清除
  refreshIntervalId.value = chatRefreshInterval;
})

// 组件卸载时清理
onUnmounted(() => {
  // 移除事件监听
  // 确保使用与添加时相同的参数移除事件监听器
  document.removeEventListener('click', handleClickOutside, true);

  // 移除所有 chat-message 事件监听器，包括临时调试代码添加的
  console.log('【调试】移除所有 chat-message 事件监听器');

  // 确保移除所有 chat-message 事件监听器
  window.removeEventListener('chat-message', handleNewMessageEvent);
  window.removeEventListener('chat-message', handleChatMessage);

  // 移除备用事件监听器
  window.removeEventListener('chat-message-backup', () => console.log('备用事件监听器已移除'));
  window.removeEventListener('chat-message-raw', () => console.log('原始消息事件监听器已移除'));

  window.removeEventListener('read-receipt', handleReadReceipt);
  window.removeEventListener('refresh-contacts', handleRefreshContacts);
  // window.removeEventListener('force-refresh-chat', handleForceRefreshChat); // 不再需要移除
  window.removeEventListener('new-message-notification', handleNewMessageEvent);
  window.removeEventListener('websocket-connected', handleWebSocketConnected);
  window.removeEventListener('websocket-disconnected', handleWebSocketDisconnected);
  window.removeEventListener('websocket-reconnecting', handleWebSocketReconnecting);
  window.removeEventListener('global-refresh-contacts', handleGlobalRefreshContacts);
  window.removeEventListener('online', handleNetworkStatusChange);
  window.removeEventListener('offline', handleNetworkStatusChange);
  window.removeEventListener('message-sent', handleMessageSent);

  // 移除在线状态更新事件监听器
  window.removeEventListener('user-online-status', handleUserOnlineStatus);
  window.removeEventListener('contact-status-updated', handleContactStatusUpdate);
  window.removeEventListener('online-users-updated', handleOnlineUsersUpdated);

  // 移除用户活跃状态跟踪事件
  const activityEvents = ['mousedown', 'keydown', 'touchstart', 'click', 'scroll'];
  activityEvents.forEach(event => {
    document.removeEventListener(event, trackUserActivity);
  });

  // 清除定时刷新
  if (refreshIntervalId.value) {
    clearInterval(refreshIntervalId.value);
    console.log('已清除定时刷新定时器');
  }

  // 清理离线消息处理器
  if (offlineMessageHandler.value) {
    offlineMessageHandler.value.destroy();
  }

  // 关闭数据库连接
  dbManager.close();

  console.log('已移除所有事件监听器并清理资源')
})

// 使用 ChatRound 作为表情图标（更像笑脸）
const SmileFace = ChatRound

// 文件上传预览
const uploadPreview = ref({
  file: null,
  type: null,
  url: null
})

const imageInput = ref(null)
const fileInput = ref(null)
const audioInput = ref(null)
const videoInput = ref(null)

// 触发文件选择
const triggerImageUpload = () => {
  imageInput.value.click()
}

const triggerFileUpload = () => {
  fileInput.value.click()
}

const triggerAudioUpload = () => {
  audioInput.value.click()
}

const triggerVideoUpload = () => {
  videoInput.value.click()
}

// 处理直接选择图片
const handleImageUploadDirect = (event) => {
  const files = Array.from(event.target.files)
  files.forEach(file => {
    if (file.size > MAX_IMAGE_SIZE_MB * 1024 * 1024) {
      ElMessage.error('图片不能超过10MB')
      return
    }
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      ElMessage.error('仅支持jpg/png/gif格式图片')
      return
    }
    previewList.value.push({
      file,
      type: 'IMAGE',
      url: URL.createObjectURL(file),
      progress: 0,
      status: 'ready',
      errorMsg: ''
    })
  })
  event.target.value = null
}

// 处理直接选择文件
const handleFileUploadDirect = (event) => {
  const files = Array.from(event.target.files)
  files.forEach(file => {
    if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
      ElMessage.error('文件不能超过20MB')
      return
    }
    previewList.value.push({
      file,
      type: 'DOCUMENT',
      url: null,
      progress: 0,
      status: 'ready',
      errorMsg: ''
    })
  })
  event.target.value = null
}

const handleAudioUploadDirect = (event) => {
  const files = Array.from(event.target.files)
  files.forEach(file => {
    if (file.size > MAX_AUDIO_SIZE_MB * 1024 * 1024) {
      ElMessage.error('音频不能超过20MB')
      return
    }
    if (!ALLOWED_AUDIO_TYPES.includes(file.type)) {
      ElMessage.error('仅支持mp3/wav/ogg格式音频')
      return
    }
    previewList.value.push({
      file,
      type: 'AUDIO',
      url: URL.createObjectURL(file),
      progress: 0,
      status: 'ready',
      errorMsg: ''
    })
  })
  event.target.value = null
}

const handleVideoUploadDirect = (event) => {
  const files = Array.from(event.target.files)
  files.forEach(file => {
    if (file.size > MAX_VIDEO_SIZE_MB * 1024 * 1024) {
      ElMessage.error('视频不能超过50MB')
      return
    }
    if (!ALLOWED_VIDEO_TYPES.includes(file.type)) {
      ElMessage.error('仅支持mp4/webm/ogg格式视频')
      return
    }
    previewList.value.push({
      file,
      type: 'VIDEO',
      url: URL.createObjectURL(file),
      progress: 0,
      status: 'ready',
      errorMsg: ''
    })
  })
  event.target.value = null
}

// 清除预览 - 在图片上传对话框关闭时调用
function clearUploadPreview() {
  if (uploadPreview.value.url) {
    URL.revokeObjectURL(uploadPreview.value.url)
  }
  uploadPreview.value = {
    file: null,
    type: null,
    url: null
  }
}

// 确保在图片上传对话框关闭时调用清除预览函数
watch(imageUploadVisible, (newVal) => {
  if (!newVal) {
    clearUploadPreview();
  }
});

// 移除预览
const removePreview = (idx) => {
  const item = previewList.value[idx]
  if (item.url) URL.revokeObjectURL(item.url)
  previewList.value.splice(idx, 1)
}

// 重试上传
const retryUpload = async (item, idx) => {
  item.status = 'ready'
  item.errorMsg = ''
  await uploadSingleFile(item, idx)
}

// 上传单个文件，带进度
const uploadSingleFile = async (item, idx, customMessage = null) => {
  console.log(`开始上传第 ${idx + 1} 个文件:`, item.file.name)

  // 检查网络连接状态
  const isOnline = navigator.onLine

  // 检查WebSocket连接状态
  let wsConnected = false
  try {
    // 动态导入WebSocket服务
    const { isWebSocketConnected } = await import('@/utils/websocket/index')
    wsConnected = isWebSocketConnected()
    console.log('文件上传前WebSocket连接状态:', wsConnected ? '已连接' : '未连接')
  } catch (error) {
    console.error('检查WebSocket连接状态失败:', error)
  }

  console.log('文件上传前连接状态:', {
    网络状态: isOnline ? '在线' : '离线',
    WebSocket状态: wsConnected ? '已连接' : '未连接'
  })

  // 如果网络离线或WebSocket未连接，提示用户
  if (!isOnline || !wsConnected) {
    const reason = !isOnline ? '网络离线' : 'WebSocket未连接'
    console.warn(`${reason}，无法上传文件`)
    ElMessage.warning('网络连接不稳定，请稍后再试')

    // 更新上传状态
    item.status = 'error'
    item.errorMsg = '网络连接不稳定，请稍后再试'
    return
  }

  // 使用自定义消息内容（如果提供），否则使用输入框中的文本或默认消息
  const messageContent = customMessage !== null ?
    customMessage :
    (inputMessage.value.trim() || (item.type === 'IMAGE' ? '图片消息' : item.file.name))

  // 创建客户端消息ID
  const clientMessageId = `client-file-${Date.now()}-${idx}`

  const messageDTO = {
    senderId: userInfo.value.id,
    receiverId: currentContact.value.id,
    message: messageContent,
    messageType: item.type,
    clientMessageId: clientMessageId,
    timestamp: Date.now() // 添加毫秒时间戳
  }

  console.log('文件上传消息DTO:', {
    senderId: messageDTO.senderId,
    receiverId: messageDTO.receiverId,
    messageType: messageDTO.messageType,
    clientMessageId: messageDTO.clientMessageId,
    timestamp: messageDTO.timestamp,
    messageContent: messageContent.length > 50 ? messageContent.substring(0, 50) + '...' : messageContent
  })

  // 更新上传状态
  item.status = 'uploading'
  item.progress = 0

  try {
    // 创建临时消息ID
    const tempId = `temp-file-${Date.now()}-${idx}`

    // 创建临时消息对象
    const tempMessage = {
      sender: userInfo.value.id,
      receiver: currentContact.value.id,
      content: messageDTO.message,
      messageType: item.type,
      time: new Date().toISOString(),
      senderId: userInfo.value.id,
      receiverId: currentContact.value.id,
      isSelf: true,
      id: tempId,
      status: 'SENDING', // 设置为发送中状态，而不是离线状态
      isRead: false,
      file: item.file,
      conversationId: dbManager.getConversationId(userInfo.value.id, currentContact.value.id),
      clientMessageId: messageDTO.clientMessageId, // 添加客户端消息ID
      timestamp: messageDTO.timestamp, // 添加毫秒时间戳
      _isPostRequest: true, // 标记为POST请求，避免被误判为离线消息
      _isFileUpload: true // 添加标记，表示这是文件上传
    }

    // 添加临时消息到聊天窗口
    messages.value.push(tempMessage)

    // 滚动到底部
    scrollToBottom()

    console.log('创建临时消息:', {
      id: tempId,
      clientMessageId: messageDTO.clientMessageId,
      status: 'SENDING',
      messageType: item.type,
      _isPostRequest: true,
      _isFileUpload: true
    })

    // 发送消息
    const result = await sendChatMessageWithProgress(messageDTO, item.file, percent => {
      item.progress = percent

      // 更新临时消息的上传进度
      if (messages.value.find(m => m.id === tempId)) {
        updateMessageStatus(tempId, 'SENDING', { uploadProgress: percent })
      }
    })

    // 记录完整的响应
    console.log('文件上传完整响应:', result);

    // 更新状态
    item.status = 'success'
    item.progress = 100

    // 更新消息状态
    if (result && result.data) {
      // 确保fileUrl字段存在
      const fileUrl = result.data.fileUrl || result.data.url || result.data.imageUrl;

      console.log('文件URL:', fileUrl);

      // 更新消息状态为"已发送"
      updateMessageStatus(tempId, false, {
        id: result.data.id || tempId,
        fileUrl: fileUrl,
        status: 'SENT', // 明确设置状态为"已发送"
        errorMessage: null, // 清除任何错误消息
        _isPostRequest: false, // 清除POST请求标记，表示已完成
        time: result.data.timestamp || tempMessage.time
      });

      // 记录更新后的消息
      const updatedMessage = messages.value.find(m => m.id === tempId || m.id === result.data.id);
      console.log('更新后的消息:', updatedMessage);
    } else {
      // 即使没有返回数据，也更新状态为已发送
      updateMessageStatus(tempId, false, {
        status: 'SENT',
        errorMessage: null,
        _isPostRequest: false
      });

      console.log('文件上传成功，但没有返回数据，已更新消息状态为已发送');
    }
  } catch (error) {
    console.error(`文件 ${idx + 1} 上传失败:`, error)
    item.status = 'error'
    item.errorMsg = '上传失败，请重试'
  }
}

// 批量上传
const uploadAllFiles = async (customMessage = null) => {
  console.log('批量上传文件，自定义消息:', customMessage)

  for (let i = 0; i < previewList.value.length; i++) {
    const item = previewList.value[i]
    if (item.status === 'ready' || item.status === 'error') {
      await uploadSingleFile(item, i, customMessage)
    }
  }
}

// 带进度的上传实现（需配合后端支持）
const sendChatMessageWithProgress = async (messageDTO, file, onProgress) => {
  try {
    // 检查网络连接状态
    const isOnline = navigator.onLine
    if (!isOnline) {
      console.warn('网络离线，无法上传文件')
      throw new Error('网络离线，请检查网络连接')
    }

    // 检查WebSocket连接状态
    let wsConnected = false
    try {
      // 动态导入WebSocket服务
      const { isWebSocketConnected } = await import('@/utils/websocket/index')
      wsConnected = isWebSocketConnected()
      console.log('文件上传前WebSocket连接状态:', wsConnected ? '已连接' : '未连接')
    } catch (error) {
      console.error('检查WebSocket连接状态失败:', error)
      wsConnected = false
    }

    console.log('文件上传前连接状态:', {
      网络状态: isOnline ? '在线' : '离线',
      WebSocket状态: wsConnected ? '已连接' : '未连接'
    })

    // 如果WebSocket未连接，提示用户
    if (!wsConnected) {
      console.warn('WebSocket未连接，无法上传文件')
      throw new Error('聊天服务连接不稳定，请稍后再试')
    }

    // 创建FormData对象
    const formData = new FormData()

    // 添加必需参数
    formData.append('senderId', messageDTO.senderId)
    formData.append('receiverId', messageDTO.receiverId)

    // 添加消息内容（可选）
    if (messageDTO.message) {
      formData.append('message', messageDTO.message)
    }

    // 添加消息类型
    formData.append('messageType', messageDTO.messageType)

    // 添加客户端消息ID（如果有）
    if (messageDTO.clientMessageId) {
      formData.append('clientMessageId', messageDTO.clientMessageId)
      console.log('添加客户端消息ID:', messageDTO.clientMessageId)
    }

    // 添加文件（如果有）
    if (file) {
      formData.append('file', file)
      console.log('添加文件到表单:', file.name, file.type, file.size)
    }

    // 添加音频时长（如果是音频消息）
    if (messageDTO.messageType === 'AUDIO' && messageDTO.audioDuration) {
      formData.append('audioDuration', messageDTO.audioDuration)
    }

    // 添加视频时长（如果是视频消息）
    if (messageDTO.messageType === 'VIDEO' && messageDTO.videoDuration) {
      formData.append('videoDuration', messageDTO.videoDuration)
    }

    // 使用chat.js中的sendPrivateMessage函数
    console.log('使用chat.js中的sendPrivateMessage函数发送消息:', {
      senderId: messageDTO.senderId,
      receiverId: messageDTO.receiverId,
      messageType: messageDTO.messageType,
      clientMessageId: messageDTO.clientMessageId,
      hasFile: !!file,
      fileType: file ? file.type : null,
      fileSize: file ? file.size : null,
      message: messageDTO.message
    })

    // 导入sendPrivateMessage函数
    const { sendPrivateMessage } = await import('@/api/chat')

    // 创建自定义axios实例，以支持进度回调
    const axios = (await import('axios')).default
    const axiosInstance = axios.create()

    // 添加请求拦截器，设置token
    axiosInstance.interceptors.request.use(config => {
      // 从localStorage获取token
      const token = localStorage.getItem('token')
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`
      }
      return config
    })

    // 如果提供了进度回调，使用自定义axios实例
    if (typeof onProgress === 'function') {
      console.log('使用自定义axios实例发送请求，支持进度回调')

      // 发送请求
      const response = await axiosInstance.post('/api/chat/privateMessage', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: e => {
          const percent = Math.round((e.loaded * 100) / e.total)
          console.log('上传进度:', percent + '%')
          onProgress(percent)
        }
      })

      console.log('文件上传响应:', response.data)
      return response.data
    } else {
      // 否则使用普通的sendPrivateMessage函数
      console.log('使用普通sendPrivateMessage函数发送请求')
      const response = await sendPrivateMessage(formData)
      console.log('文件上传响应:', response)
      return response
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    throw error
  }
}

// 获取联系人列表
const fetchContacts = async () => {
  chatLogger.info('开始获取联系人列表')

  if (!userInfo.value || !userInfo.value.id) {
    chatLogger.error('获取联系人列表：用户未登录或ID不存在')
    return
  }

  loadingContacts.value = true

  try {
    // 使用会话管理Store获取会话列表
    const conversationList = await conversationStore.fetchConversations(true)
    chatLogger.info(`会话管理Store获取到 ${conversationList.length} 个会话`)

    // 将会话列表转换为联系人列表格式
    const formattedContacts = conversationList.map(conv => ({
      id: conv.contactId,
      conversationId: conv.conversationId,
      name: conv.name,
      avatar: conv.avatar,
      avatarText: conv.avatarText || conv.name.charAt(0).toUpperCase(),
      lastMessage: conv.lastMessage,
      lastTime: conv.lastTime,
      messageType: conv.messageType,
      unreadCount: conv.unreadCount || 0,
      online: false, // 默认离线，后续会更新
      isPinned: conv.isPinned || false,
      isMuted: conv.isMuted || false,
      status: conv.status || 'ACTIVE'
    }))

    // 更新联系人列表
    contacts.value = formattedContacts
    chatLogger.info(`联系人列表更新完成，共 ${formattedContacts.length} 个联系人`)

    // 更新联系人在线状态 - 延迟2秒执行，确保WebSocket连接稳定
    setTimeout(() => {
      // 如果有ChatList引用，调用其updateContactsOnlineStatus方法
      if (chatListRef.value && typeof chatListRef.value.updateContactsOnlineStatus === 'function') {
        chatLogger.info('通过ChatList组件引用更新联系人在线状态');
        chatListRef.value.updateContactsOnlineStatus();
      } else {
        // 否则使用简单的方法更新联系人在线状态
        chatLogger.info('通过本地方法更新联系人在线状态');
        debouncedUpdateContactsOnlineStatus();
      }
    }, 2000)

    return formattedContacts
  } catch (error) {
    chatLogger.error('获取联系人列表失败:', error)
    ElMessage.error('获取联系人列表失败')
    return []
  } finally {
    loadingContacts.value = false
  }
}

// 加载联系人列表
const loadContacts = async () => {
  console.log('开始加载联系人列表函数')

  if (!chatListRef.value) {
    console.error('chatListRef 不存在，无法加载联系人列表')
    return
  }

  console.log('调用 fetchContacts 方法（初始加载）')
  try {
    // 使用新的 fetchContacts 方法获取联系人列表
    await fetchContacts()
    console.log('联系人列表加载成功，数量:', contacts.value.length)
    console.log('联系人列表详情:', contacts.value)
  } catch (error) {
    console.error('加载联系人列表失败:', error)
    console.error('错误详情:', error.stack || error.message || error)
  }
}

// 初始Chat组件
async function initChat() {
  chatLogger.info('开始初始化聊天组件')
  chatLogger.info('当前用户信息:', userInfo.value)

  try {
    // 使用新的WebSocket服务初始化
    const { initializeWebSocketService, isWebSocketConnected } = await import('@/utils/websocket/index')

    // 检查WebSocket连接
    const wsConnected = isWebSocketConnected()
    chatLogger.info('WebSocket连接状态:', wsConnected ? '已连接' : '未连接')

    if (!wsConnected) {
      chatLogger.info('WebSocket未连接，尝试初始化WebSocket服务')

      // 初始化WebSocket服务
      const initialized = await initializeWebSocketService()

      if (initialized) {
        chatLogger.info('WebSocket服务初始化成功')
      } else {
        chatLogger.error('WebSocket服务初始化失败')
        ElMessage.warning({
          message: '聊天服务连接失败，部分功能可能不可用',
          duration: 5000
        })
      }
    } else {
      chatLogger.info('WebSocket已连接，无需重新初始化')
    }

    // 加载联系人列表
    chatLogger.info('开始加载联系人列表')
    await loadContacts()
    chatLogger.info('联系人列表加载完成，联系人数量:', contacts.value.length)

    // 从URL参数获取要打开的联系人ID
    chatLogger.info('检查URL参数中的联系人ID')
    openContactFromUrl()

    // 处理离线消息
    if (offlineMessageCount.value > 0) {
      chatLogger.info(`发现 ${offlineMessageCount.value} 条离线消息，尝试发送`)
      await processOfflineMessages()
    }

    chatLogger.info('聊天组件初始化完成')
  } catch (error) {
    chatLogger.error('初始化聊天组件失败:', error)
    ElMessage.error('聊天组件初始化失败，请刷新页面重试')
  }
}

// 处理新消息事件
const handleNewMessageEvent = (event) => {
  try {
    chatLogger.info('收到新消息事件:', {
      type: event.type,
      timestamp: new Date().toISOString()
    });

    const message = event.detail;

    // 记录完整的消息数据
    console.log('收到的完整消息数据:', message);

    // 增加消息有效性验证
    if (!message) {
      console.warn('收到无效消息，消息对象为空');
      return;
    }

    // 记录消息的关键属性
    chatLogger.info('消息关键属性:', {
      id: message.id || message.messageId,
      clientMessageId: message.clientMessageId,
      senderId: message.senderId,
      receiverId: message.receiverId,
      messageType: message.messageType,
      contentLength: (message.content || message.message || '').length,
      time: message.time || message.timestamp,
      status: message.status,
      isRead: message.isRead,
      source: event.type // 记录消息来源
    });

    // 检查是否是文件消息，如果是，检查全局标记
    if (message.messageType === 'IMAGE' || message.messageType === 'DOCUMENT' ||
        message.messageType === 'AUDIO' || message.messageType === 'VIDEO') {
      if (window._fileMessageSent) {
        console.log('检测到文件消息已发送标记，避免创建重复的离线消息');
      }
    }

    // 确保消息有必要的字段
    if (!message.senderId || !message.receiverId) {
      console.warn('消息缺少必要的发送者或接收者ID:', message);
      return;
    }

    // 检查是否是重复消息
    const existingMessage = messages.value.find(m =>
      (m.id && m.id === message.id) ||
      (m.clientMessageId && m.clientMessageId === message.clientMessageId)
    );

    if (existingMessage) {
      console.log('检测到重复消息，仅更新状态:', {
        existingId: existingMessage.id,
        newId: message.id,
        clientMessageId: message.clientMessageId
      });

      // 仅更新状态，不创建新消息
      updateMessageStatus(existingMessage.id, message.isRead, {
        id: message.id || existingMessage.id,
        status: message.status || existingMessage.status,
        fileUrl: message.fileUrl || existingMessage.fileUrl,
        time: message.timestamp || message.time || existingMessage.time
      });

      // 如果这是确认消息，可以直接返回，不需要再处理
      if (event.type === 'message-sent') {
        return;
      }
    }

    // 如果消息包含clientMessageId，尝试查找并更新临时消息
    if (message.clientMessageId && !existingMessage) {
      const tempMessage = messages.value.find(m => m.clientMessageId === message.clientMessageId);
      if (tempMessage) {
        chatLogger.info('找到匹配的临时消息:', {
          tempId: tempMessage.id,
          clientMessageId: message.clientMessageId
        });

        // 更新临时消息
        updateMessageStatus(tempMessage.id, false, {
          id: message.id,
          time: message.timestamp || message.time,
          status: message.status || 'SENT',
          fileUrl: message.fileUrl || message.url || message.imageUrl || tempMessage.fileUrl
        });

        // 如果这是确认消息，可以直接返回，不需要再处理
        if (event.type === 'message-sent') {
          return;
        }
      }
    }

    // 检查是否有临时消息ID格式的消息，这可能是重复创建的
    if (message.id && typeof message.id === 'string' && message.id.startsWith('temp-')) {
      // 检查是否已经有相同时间戳的消息
      const timestamp = message.id.split('-')[1];
      const similarMessages = messages.value.filter(m =>
        typeof m.id === 'string' &&
        m.id.startsWith('temp-') &&
        m.id.includes(timestamp)
      );

      if (similarMessages.length > 0) {
        console.warn('检测到可能的重复临时消息:', {
          newId: message.id,
          similarIds: similarMessages.map(m => m.id)
        });

        // 如果已经有相同时间戳的消息，不处理这个新消息
        if (window._fileMessageSent) {
          console.log('文件消息已发送，忽略这个临时消息');
          return;
        }
      }
    }

    // 记录当前用户信息
    console.log('当前用户信息:', {
      id: userInfo.value?.id,
      username: userInfo.value?.username
    });

    // 记录当前联系人信息
    console.log('当前联系人信息:', currentContact.value ? {
      id: currentContact.value.id,
      name: currentContact.value.name
    } : '无当前联系人');

    // 检查消息是否与当前用户相关
    const isRelatedToCurrentUser =
      String(message.senderId) === String(userInfo.value.id) ||
      String(message.receiverId) === String(userInfo.value.id);

    if (!isRelatedToCurrentUser) {
      console.log('消息与当前用户无关，忽略消息');
      return;
    }

    // 检查这是否是当前聊天的消息
    console.log('检查消息是否属于当前聊天:', {
      currentContactId: currentContact.value?.id,
      messageSenderId: message.senderId,
      messageReceiverId: message.receiverId
    });

    // 检查消息是否属于当前聊天
    const isSenderCurrentContact = currentContact.value && String(message.senderId) === String(currentContact.value.id);
    const isReceiverCurrentContact = currentContact.value && String(message.receiverId) === String(currentContact.value.id);
    const isCurrentChat = isSenderCurrentContact || isReceiverCurrentContact;

    console.log('消息是否属于当前聊天:', isCurrentChat);

    // 设置消息是否是自己发送的
    const isSelfMessage = String(message.senderId) === String(userInfo.value.id);
    message.isSelf = isSelfMessage;
    console.log('消息是否是自己发送的:', isSelfMessage);

    // 强制刷新联系人列表 - 确保最新消息显示
    // 无论是发送方还是接收方，都需要刷新联系人列表
    if (chatListRef.value) {
      console.log('强制刷新联系人列表（使用防抖版本）');
      chatListRef.value.debouncedFetchContacts();
    }

    // 更新联系人列表中的最新消息 - 无论是否是当前聊天，都需要更新（使用防抖版本）
    debouncedUpdateContactLastMessage(message);

    // 如果是当前聊天，添加到聊天窗口
    if (isCurrentChat) {
      // 添加到当前聊天窗口
      const formattedMessage = formatMessage(message);
      console.log('格式化后的消息:', formattedMessage);

      // 使用增强的消息重复检测逻辑
      const isDuplicate = isMessageDuplicate(formattedMessage, messages.value);

      if (!isDuplicate) {
        console.log('添加新消息到聊天窗口');

        // 为接收方的图片消息添加懒加载机制
        if (!formattedMessage.isSelf && formattedMessage.messageType === 'IMAGE') {
          console.log('接收方图片消息，添加懒加载机制:', formattedMessage.id);

          // 检查是否有图片URL
          const hasImageUrl = !!(formattedMessage.fileUrl || formattedMessage.url || formattedMessage.imageUrl);
          console.log('图片消息是否有URL:', hasImageUrl, {
            fileUrl: formattedMessage.fileUrl,
            url: formattedMessage.url,
            imageUrl: formattedMessage.imageUrl
          });

          // 创建一个"加载中"状态的消息
          const loadingMessage = {
            ...formattedMessage,
            _imageLoading: true, // 添加标记，表示图片正在加载
            status: 'LOADING',   // 设置状态为"加载中"
            _originalImageUrl: hasImageUrl ? (formattedMessage.fileUrl || formattedMessage.url || formattedMessage.imageUrl) : null
          };

          // 添加到消息列表
          messages.value.push(loadingMessage);

          // 滚动到底部
          nextTick(() => scrollToBottom());

          // 延迟一小段时间后更新状态，模拟加载过程
          setTimeout(() => {
            // 查找刚刚添加的消息
            const index = messages.value.findIndex(m => m.id === loadingMessage.id);
            if (index !== -1) {
              // 确定图片URL
              const imageUrl = loadingMessage._originalImageUrl;

              console.log('更新接收方图片消息状态:', {
                id: loadingMessage.id,
                imageUrl: imageUrl
              });

              // 更新消息状态
              updateMessageStatus(loadingMessage.id, false, {
                status: 'SENT',
                _imageLoading: false,
                fileUrl: imageUrl,
                imageUrl: imageUrl,
                url: imageUrl
              });
            }
          }, 500); // 500毫秒的延迟，足够触发视图更新
        } else {
          // 非图片消息或发送方消息，直接添加
          messages.value.push(formattedMessage);
        }

        // 标记为已读 - 只有接收方需要标记消息为已读
        if (!formattedMessage.isSelf && formattedMessage.id) {
          console.log('标记消息为已读:', formattedMessage.id);

          // 不再尝试转换为数字类型，保持原始ID类型
          const messageId = formattedMessage.id;
          const userId = userInfo.value.id;

          console.log('消息ID:', messageId, typeof messageId);
          console.log('用户ID:', userId, typeof userId);

          // 无论ID类型如何，都尝试标记为已读
          markMessageAsRead(messageId, userId, formattedMessage.senderId)
            .then(response => {
              console.log('消息已标记为已读响应:', response);
              if (response && response.code === 200) {
                console.log('消息已成功标记为已读 - 消息ID:', messageId);

                // 更新本地消息状态
                updateMessageReadStatus(messageId);

                // 确保当前联系人的未读计数为0
                if (currentContact.value) {
                  const contactId = String(currentContact.value.id);
                  const contact = contacts.value.find(c => String(c.id) === contactId);
                  if (contact) {
                    contact.unreadCount = 0;
                    console.log(`重置当前联系人 ${contact.name} 的未读计数为0`);
                  }
                }

                // 发送已读回执
                (async () => {
                  try {
                    // 检查用户是否在线
                    const isOnline = navigator.onLine;

                    if (!isOnline) {
                      console.log('用户离线，不发送已读回执');
                      return;
                    }

                    // 检查WebSocket连接状态
                    let wsConnected = false;
                    try {
                      const { isWebSocketConnected } = await import('@/utils/websocket/index');
                      wsConnected = isWebSocketConnected();
                      console.log('WebSocket连接状态:', wsConnected ? '已连接' : '未连接');
                    } catch (error) {
                      console.error('检查WebSocket连接状态失败:', error);
                    }

                    if (!wsConnected) {
                      console.log('WebSocket未连接，不发送已读回执');
                      return;
                    }

                    // 使用WebSocket发送已读回执
                    const { sendReadReceipt } = await import('@/utils/websocket/index');
                    await sendReadReceipt([messageId], formattedMessage.senderId, userId);
                    console.log('已发送已读回执，消息ID:', messageId);
                  } catch (error) {
                    console.error('发送已读回执失败:', error);
                  }
                })();
              }
            })
            .catch(err => {
              console.error('标记消息已读失败:', err);
              console.error('错误详情:', err.response || err.message || err);
            });
        }

        // 滚动到底部
        nextTick(() => scrollToBottom());

        // 如果不是自己发送的消息，播放通知声音
        if (!formattedMessage.isSelf) {
          playNotificationSound();
        }
      } else {
        console.log('忽略重复消息');
      }
    } else {
      console.log('消息不属于当前聊天，不添加到聊天窗口');

      // 如果不是当前聊天且不是自己发送的消息，播放通知声音
      if (!message.isSelf) {
        playNotificationSound();
      }

      // 如果是自己发送的消息，延迟一段时间后模拟对方已读
      if (message.isSelf && message.id) {
        // 延迟2秒后更新已读状态，模拟对方已读
        setTimeout(() => {
          // 找到对应的消息并更新状态
          const contactId = message.receiverId;
          const contact = contacts.value.find(c => String(c.id) === String(contactId));

          if (contact && contact.online) {
            console.log('模拟对方已读消息:', message.id);
            updateMessageReadStatus(message.id);
          }
        }, 2000);
      }

      // 更新未读消息计数 - 只有在不是自己发送的消息且不是当前聊天时才增加未读计数
      if (!isSelfMessage && (!currentContact.value || String(currentContact.value.id) !== String(message.senderId))) {
        // 找到对应的联系人
        const contactId = message.senderId;
        const contact = contacts.value.find(c => String(c.id) === String(contactId));

        if (contact) {
          // 增加未读计数
          contact.unreadCount = (contact.unreadCount || 0) + 1;
          console.log(`更新联系人 ${contact.name} 的未读消息计数: ${contact.unreadCount}`);

          // 同步更新Redis中的未读计数
          try {
            // 使用会话管理Store增加未读计数
            if (contact.conversationId) {
              conversationStore.increaseUnreadCount(userInfo.value.id, contactId)
                .then(() => {
                  console.log(`已通过会话管理Store增加联系人 ${contact.name} 的未读计数`);
                })
                .catch(error => {
                  console.error(`通过会话管理Store增加未读计数失败:`, error);
                });
            }
          } catch (error) {
            console.error('同步未读计数到Redis失败:', error);
          }
        }
      } else if (isSelfMessage) {
        console.log('自己发送的消息，不增加未读计数');
      } else {
        console.log('当前聊天的消息，不增加未读计数');
      }
    }
  } catch (error) {
    console.error('处理新消息事件时出错:', error);
    console.error('错误详情:', error.stack);
  }
};

// 格式化消息
const formatMessage = (message) => {
  try {
    // 基本消息格式验证
    if (!message) {
      chatLogger.warn('formatMessage: 消息对象为空');
      return {
        id: `temp-${Date.now()}`,
        content: '[无效消息]',
        time: new Date().toISOString(),
        isSelf: false,
        messageType: 'TEXT',
        status: 'ERROR'
      };
    }

    // 处理时间戳格式
    let formattedTime = message.time || message.timestamp || new Date().toISOString();

    // 检查时间是否为字符串格式，例如: "Mon Apr 28 22:51:54 CST 2025"
    if (typeof formattedTime === 'string' && formattedTime.includes('CST')) {
      try {
        // 解析非标准时间格式
        const date = new Date(formattedTime);
        if (!isNaN(date.getTime())) {
          formattedTime = date.toISOString();
        }
      } catch (timeError) {
        chatLogger.warn('时间戳格式解析失败:', timeError);
        formattedTime = new Date().toISOString();
      }
    }

    // 处理图片URL
    let fileUrl = null;
    if (message.messageType === 'IMAGE') {
      fileUrl = message.fileUrl || message.url || message.imageUrl;
      console.log('格式化图片消息，URL:', fileUrl);

      // 如果是图片消息但没有URL，记录警告
      if (!fileUrl) {
        console.warn('警告：图片消息没有URL字段:', message);
      }
    }

    // 确保消息有所有必要的属性
    return {
      ...message,
      id: message.id || message.messageId || `temp-${Date.now()}`,
      content: message.content || message.message || '[空消息]',
      senderId: message.senderId || message.sender || userInfo.value.id,
      receiverId: message.receiverId || message.receiver || currentContact.value?.id,
      messageType: message.messageType || 'TEXT',
      time: formattedTime,
      isSelf: message.isSelf !== undefined ? message.isSelf :
              (String(message.senderId) === String(userInfo.value.id) || String(message.sender) === String(userInfo.value.id)),
      status: message.status || (message.isRead ? 'READ' : 'SENT'),
      clientMessageId: message.clientMessageId,
      conversationId: message.conversationId,
      // 确保设置所有可能的URL字段，以确保兼容性
      fileUrl: fileUrl,
      url: fileUrl,
      imageUrl: fileUrl
    };
  } catch (error) {
    console.error('格式化消息时出错:', error);
    // 返回安全的默认消息
    return {
      id: `error-${Date.now()}`,
      content: '[消息格式错误]',
      time: new Date().toISOString(),
      isSelf: false,
      messageType: 'TEXT'
    };
  }
};

// 增强的消息重复检测函数
const isMessageDuplicate = (newMsg, existingMsgs) => {
  if (!newMsg || !existingMsgs || !Array.isArray(existingMsgs)) {
    return false;
  }

  return existingMsgs.some(m => {
    // 1. 精确匹配ID (非临时ID)
    if (m.id === newMsg.id && !m.id.toString().startsWith('temp-') && !m.id.toString().startsWith('error-')) {
      chatLogger.info('消息重复检测: ID精确匹配', m.id);
      return true;
    }

    // 2. 客户端消息ID匹配
    if (m.clientMessageId && newMsg.clientMessageId && m.clientMessageId === newMsg.clientMessageId) {
      chatLogger.info('消息重复检测: 客户端消息ID匹配', m.clientMessageId);
      return true;
    }

    // 3. 临时ID匹配（处理发送中的消息）
    if (m.id.toString().startsWith('temp-') && newMsg.id.toString().startsWith('temp-') &&
        m.content === newMsg.content &&
        String(m.senderId) === String(newMsg.senderId) &&
        Math.abs(new Date(m.time) - new Date(newMsg.time)) < 2000) {
      chatLogger.info('消息重复检测: 临时ID匹配', m.id, newMsg.id);
      return true;
    }

    // 4. 内容+发送者+接收者+时间匹配（处理没有ID或ID不同的消息）
    if (m.content === newMsg.content &&
        String(m.senderId) === String(newMsg.senderId) &&
        String(m.receiverId) === String(newMsg.receiverId) &&
        Math.abs(new Date(m.time) - new Date(newMsg.time)) < 5000) {
      chatLogger.info('消息重复检测: 内容+发送者+接收者+时间匹配');
      return true;
    }

    // 5. 处理WebSocket重连导致的重复消息
    if (m.content === newMsg.content &&
        String(m.senderId) === String(newMsg.senderId) &&
        Math.abs(new Date(m.time) - new Date(newMsg.time)) < 30000) { // 30秒内的相同内容和发送者
      chatLogger.info('消息重复检测: WebSocket重连导致的重复消息');
      return true;
    }

    // 6. 消息内容和发送者/接收者完全相同的消息（可能是重复发送）
    if (m.content === newMsg.content &&
        String(m.senderId) === String(newMsg.senderId) &&
        String(m.receiverId) === String(newMsg.receiverId) &&
        Math.abs(new Date(m.time) - new Date(newMsg.time)) < 60000) { // 1分钟内的相同内容和发送者/接收者
      chatLogger.info('消息重复检测: 内容和发送者/接收者完全相同');
      return true;
    }

    return false;
  });
};

// 播放通知声音函数已在上面定义

// 创建防抖版本的更新联系人列表函数
const debouncedUpdateContactLastMessage = debounce((message) => {
  updateContactLastMessage(message);
}, 300);

// 创建防抖版本的更新联系人在线状态函数
const debouncedUpdateContactsOnlineStatus = (() => {
  let timer = null;
  let lastUpdateTime = 0;
  const MIN_UPDATE_INTERVAL = 10000; // 最小更新间隔10秒

  return async () => {
    // 如果距离上次更新不足10秒，取消本次更新
    const now = Date.now();
    if (now - lastUpdateTime < MIN_UPDATE_INTERVAL) {
      chatLogger.debug(`距离上次更新联系人状态不足${MIN_UPDATE_INTERVAL/1000}秒，跳过本次更新`);
      return;
    }

    // 清除之前的定时器
    if (timer) {
      clearTimeout(timer);
    }

    // 设置新的定时器
    timer = setTimeout(async () => {
      if (!contacts.value.length) return;

      lastUpdateTime = Date.now();
      chatLogger.info('开始更新联系人在线状态, 联系人数量:', contacts.value.length);

      try {
        // 获取所有联系人ID
        const contactIds = contacts.value.map(contact => contact.id.toString());

        // 从contact-status模块导入函数
        const { getContactsStatus, isContactOnline } = await import('@/utils/contact-status');

        // 批量获取联系人在线状态
        await getContactsStatus(contactIds);

        // 更新联系人状态
        let updatedCount = 0;
        contacts.value.forEach(contact => {
          if (contact.id) {
            const userId = contact.id.toString();
            const isOnline = isContactOnline(userId);

            // 只在状态变化时更新
            if (contact.online !== isOnline) {
              chatLogger.info(`更新联系人 ${contact.name} 在线状态: ${isOnline ? '在线' : '离线'}`);
              contact.online = isOnline;
              updatedCount++;
            }
          }
        });

        if (updatedCount > 0) {
          chatLogger.info(`联系人在线状态更新完成，共更新 ${updatedCount} 个联系人状态`);
        } else {
          chatLogger.debug('联系人在线状态无变化');
        }
      } catch (error) {
        chatLogger.error('更新联系人在线状态失败:', error);

        // 如果新方法失败，回退到旧方法
        try {
          const { isUserOnline } = await import('@/services/onlineStatusService');

          // 更新联系人状态
          let updatedCount = 0;
          contacts.value.forEach(contact => {
            if (contact.id) {
              const userId = contact.id.toString();
              const isOnline = isUserOnline(userId);

              // 只在状态变化时更新
              if (contact.online !== isOnline) {
                chatLogger.info(`(回退方法)更新联系人 ${contact.name} 在线状态: ${isOnline ? '在线' : '离线'}`);
                contact.online = isOnline;
                updatedCount++;
              }
            }
          });

          if (updatedCount > 0) {
            chatLogger.info(`(回退方法)联系人在线状态更新完成，共更新 ${updatedCount} 个联系人状态`);
          }
        } catch (fallbackError) {
          chatLogger.error('回退方法更新联系人在线状态也失败:', fallbackError);
        }
      }

      timer = null;
    }, 500); // 500ms防抖
  };
})();

// 兼容旧的函数名 - 使用 export 避免未使用警告
// eslint-disable-next-line
const updateContactsOnlineStatus = debouncedUpdateContactsOnlineStatus;

// 更新联系人列表中的最新消息
const updateContactLastMessage = (message) => {
  try {
    // 确保消息有必要的字段
    if (!message.senderId || !message.receiverId) {
      chatLogger.warn('updateContactLastMessage: 消息缺少必要的发送者或接收者ID:', message);
      return;
    }

    // 确定消息是否是自己发送的
    const isSelfMessage = String(message.senderId) === String(userInfo.value.id);
    message.isSelf = isSelfMessage;

    // 找到相关联系人 - 如果是自己发送的消息，联系人是接收者；否则联系人是发送者
    const contactId = isSelfMessage ? message.receiverId : message.senderId;
    chatLogger.info('更新联系人最新消息:', { contactId, messageId: message.id, isSelfMessage });

    if (!contactId) {
      chatLogger.warn('updateContactLastMessage: 无法确定联系人ID');
      return;
    }

    // 使用会话管理Store更新会话
    conversationStore.updateConversationWithMessage({
      id: message.id,
      senderId: message.senderId,
      receiverId: message.receiverId,
      message: message.content || message.message || '[消息]',
      messageType: message.messageType || 'TEXT',
      timestamp: message.time || new Date().toISOString(),
      senderName: message.senderName,
      senderAvatar: message.senderAvatar
    });

    // 查找联系人
    let contact = contacts.value.find(c => String(c.id) === String(contactId));

    // 如果找不到联系人，尝试刷新联系人列表
    if (!contact) {
      chatLogger.warn('未找到消息对应的联系人:', contactId);

      // 强制刷新联系人列表
      fetchContacts().then(() => {
        // 刷新后再次尝试查找联系人
        contact = contacts.value.find(c => String(c.id) === String(contactId));

        if (contact) {
          // 找到联系人后更新
          updateContactInfo(contact, message, isSelfMessage, contactId);
        } else {
          chatLogger.error('刷新后仍未找到联系人:', contactId);
        }
      });
      return;
    }

    // 找到联系人后更新
    updateContactInfo(contact, message, isSelfMessage, contactId);
  } catch (error) {
    chatLogger.error('更新联系人最新消息时出错:', error);
  }
};



// 更新联系人信息
const updateContactInfo = (contact, message, isSelfMessage, contactId) => {
  // 更新联系人的最新消息
  contact.lastMessage = message.content || message.message || '[消息]';
  contact.lastTime = message.time || new Date().toISOString();
  contact.messageType = message.messageType || 'TEXT';

  // 如果消息包含文件URL，也更新到联系人信息中
  if (message.fileUrl) {
    if (!contact.messageAttachmentDTO) {
      contact.messageAttachmentDTO = {};
    }
    contact.messageAttachmentDTO.fileUrl = message.fileUrl;

    // 根据消息类型推断文件类型
    if (message.messageType) {
      const messageType = message.messageType;
      if (messageType === 'IMAGE') {
        contact.messageAttachmentDTO.fileType = 'IMAGE';
      } else if (messageType === 'AUDIO') {
        contact.messageAttachmentDTO.fileType = 'AUDIO';
      } else if (messageType === 'VIDEO') {
        contact.messageAttachmentDTO.fileType = 'VIDEO';
      } else if (messageType === 'DOCUMENT') {
        contact.messageAttachmentDTO.fileType = 'DOCUMENT';
      }
    }
  }

  // 如果是自己发送的消息，不增加未读计数
  if (isSelfMessage) {
    // 自己发送的消息不增加未读计数，确保未读计数不变
    console.log(`自己发送的消息，不增加联系人 ${contact.name} 的未读计数`);
  }
  // 如果不是当前聊天且不是自己发的消息，增加未读计数
  else if (!currentContact.value || String(currentContact.value.id) !== String(contactId)) {
    contact.unreadCount = (contact.unreadCount || 0) + 1;
    console.log(`更新联系人 ${contact.name} 的未读消息计数: ${contact.unreadCount}`);

    // 同步更新Redis中的未读计数
    try {
      // 使用会话管理Store增加未读计数
      if (contact.conversationId) {
        conversationStore.increaseUnreadCount(userInfo.value.id, contactId)
          .then(() => {
            console.log(`已通过会话管理Store增加联系人 ${contact.name} 的未读计数`);
          })
          .catch(error => {
            console.error(`通过会话管理Store增加未读计数失败:`, error);
          });
      }
    } catch (error) {
      console.error('同步未读计数到Redis失败:', error);
    }
  } else if (currentContact.value && String(currentContact.value.id) === String(contactId)) {
    // 如果是当前聊天，确保未读数为0
    contact.unreadCount = 0;
  }

  console.log('联系人最新消息已更新:', contact);

  // 强制更新联系人列表顺序 - 将最新消息的联系人移到顶部
  if (contacts.value.length > 1) {
    const index = contacts.value.findIndex(c => String(c.id) === String(contactId));
    if (index > 0) {
      // 从数组中移除联系人
      const updatedContact = contacts.value.splice(index, 1)[0];
      // 将联系人添加到数组开头
      contacts.value.unshift(updatedContact);
      console.log('联系人已移至列表顶部');
    }
  }
};
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: row; /* 强制使用水平布局 */
  height: calc(100vh - 70px); /* 减去顶部导航栏的高度 */
  background-color: #f0f2f5;
  position: relative; /* 使用相对定位，避免在某些情况下的布局问题 */
  z-index: 10; /* 确保在其他内容之上，但低于导航栏 */
  overflow: hidden; /* 防止滚动穿透 */
  width: 100%; /* 确保宽度为100% */
  min-height: 600px; /* 设置最小高度，确保在大屏幕下也能正常显示 */
}

/* 当公告栏显示时，调整聊天容器位置 */
body.has-announcement .chat-container {
  top: 40px; /* 公告栏的高度 */
  height: calc(100vh - 110px); /* 减去顶部导航栏和公告栏的高度 */
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin: 16px;
  overflow: hidden; /* 确保内容不溢出 */
  height: calc(100% - 32px); /* 确保高度撑满容器，减去margin */
  min-height: 500px; /* 设置最小高度，确保在大屏幕下也能正常显示 */
  z-index: 20; /* 确保聊天区域在其他元素之上 */
  position: relative; /* 确保z-index生效 */
  visibility: visible !important; /* 强制可见 */
}

.chat-header {
  padding: 10px 16px;
  border-bottom: 1px solid #e9edef;
  background-color: #f0f2f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  z-index: 1;
  height: 60px;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 6px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.contact-info:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.contact-details {
  display: flex;
  flex-direction: column;
}

.contact-name {
  font-size: 16px;
  font-weight: 600;
  color: #111b21;
}

.contact-status {
  font-size: 12px;
  margin-top: 2px;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 4px;
}

.contact-status::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 2px;
}

.online-text {
  color: #00a884;
}

.online-text::before {
  background-color: #00a884;
}

.offline-text {
  color: #8696a0;
}

.offline-text::before {
  background-color: #8696a0;
}

.online-status {
  font-size: 12px;
  color: #00a884;
  margin-top: 2px;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 4px;
}

.online-status::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #00a884;
  margin-right: 2px;
}

.chat-messages {
  flex: 1;
  overflow: hidden; /* 确保内容不溢出 */
  background-color: #e5ddd5;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='100' viewBox='0 0 600 100'%3E%3Cg stroke='%23d9d9d9' stroke-width='0.5' stroke-opacity='0.5'%3E%3Ccircle fill='%23e5ddd5' cx='0' cy='0' r='1800'/%3E%3Ccircle fill='%23e3dbd3' cx='0' cy='0' r='1700'/%3E%3Ccircle fill='%23e1d9d1' cx='0' cy='0' r='1600'/%3E%3Ccircle fill='%23dfd7cf' cx='0' cy='0' r='1500'/%3E%3Ccircle fill='%23ddd5cd' cx='0' cy='0' r='1400'/%3E%3Ccircle fill='%23dbd3cb' cx='0' cy='0' r='1300'/%3E%3Ccircle fill='%23d9d1c9' cx='0' cy='0' r='1200'/%3E%3Ccircle fill='%23d7cfc7' cx='0' cy='0' r='1100'/%3E%3Ccircle fill='%23d5cdc5' cx='0' cy='0' r='1000'/%3E%3Ccircle fill='%23d3cbc3' cx='0' cy='0' r='900'/%3E%3Ccircle fill='%23d1c9c1' cx='0' cy='0' r='800'/%3E%3Ccircle fill='%23cfc7bf' cx='0' cy='0' r='700'/%3E%3Ccircle fill='%23cdc5bd' cx='0' cy='0' r='600'/%3E%3Ccircle fill='%23cbc3bb' cx='0' cy='0' r='500'/%3E%3Ccircle fill='%23c9c1b9' cx='0' cy='0' r='400'/%3E%3Ccircle fill='%23c7bfb7' cx='0' cy='0' r='300'/%3E%3Ccircle fill='%23c5bdb5' cx='0' cy='0' r='200'/%3E%3Ccircle fill='%23c3bbb3' cx='0' cy='0' r='100'/%3E%3C/g%3E%3C/svg%3E");
  background-attachment: fixed;
  background-size: cover;
  position: relative;
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 垂直方向排列 */
  height: calc(100% - 60px - 120px); /* 减去头部和输入框的高度 */
  min-height: 300px; /* 确保最小高度 */
  max-height: none; /* 移除最大高度限制 */
}

.chat-messages::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.05);
  pointer-events: none;
}

.chat-messages :deep(.el-scrollbar) {
  height: 100%; /* 确保滚动条高度为100% */
  overflow: hidden; /* 防止滚动穿透 */
  width: 100%; /* 确保宽度为100% */
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 垂直方向排列 */
}

.chat-messages :deep(.el-scrollbar__wrap) {
  overflow-x: hidden; /* 隐藏水平滚动条 */
  height: 100% !important; /* 确保滚动区域高度为100% */
  width: 100%; /* 确保宽度为100% */
}

.chat-messages :deep(.el-scrollbar__view) {
  padding: 16px;
  display: flex;
  flex-direction: column;
  min-height: 100%;
  width: 100%; /* 确保宽度为100% */
  flex: 1; /* 让内容区域占满可用空间 */
}

.no-messages {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #94a3b8;
  animation: fadeIn 0.5s ease-in-out;
}

.empty-description {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10px;
}

.empty-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 消息高亮效果 */
.message-highlight {
  animation: highlightMessage 1.5s ease;
}

@keyframes highlightMessage {
  0% { background-color: rgba(64, 158, 255, 0); }
  30% { background-color: rgba(64, 158, 255, 0.2); }
  70% { background-color: rgba(64, 158, 255, 0.2); }
  100% { background-color: rgba(64, 158, 255, 0); }
}

.load-more-button {
  display: flex;
  justify-content: center;
  padding: 10px 0;
  margin-bottom: 10px;
}

.load-more-button .el-button {
  font-size: 13px;
  color: #8a8a8a;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: rgba(203, 213, 225, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.load-more-button .el-button:hover {
  color: #409EFF;
  background-color: rgba(203, 213, 225, 0.4);
  transform: translateY(-1px);
}

.load-more-button .el-button:active {
  transform: translateY(0);
}

.loading-more-skeleton {
  animation: fadeIn 0.3s ease-in-out;
  padding: 5px 0;
}

.no-more-messages {
  display: flex;
  justify-content: center;
  padding: 10px 0;
  margin-bottom: 10px;
  color: #909399;
  font-size: 12px;
}

.load-more {
  text-align: center;
  margin-bottom: 20px;
}

.load-more :deep(.el-button) {
  color: #64748b;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: rgba(203, 213, 225, 0.2);
}

.load-more :deep(.el-button:hover) {
  background-color: rgba(203, 213, 225, 0.4);
}

.message-date-divider {
  text-align: center;
  margin: 24px 0;
  position: relative;
}

.message-date-divider span {
  background-color: #fafbfd;
  padding: 0 14px;
  color: #64748b;
  font-size: 13px;
  position: relative;
  z-index: 1;
  font-weight: 500;
}

.message-date-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e2e8f0;
  z-index: 0;
}

.wechat-chat-input {
  background: #f0f2f5;
  padding: 8px 16px 12px;
  margin: 0;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #e9edef;
  min-height: 120px; /* 设置最小高度 */
  max-height: 200px; /* 设置最大高度 */
  overflow: visible; /* 允许内容溢出（表情选择器） */
  z-index: 20; /* 确保输入框在其他元素之上 */
}
.toolbar-top {
  display: flex;
  flex-direction: row;
  gap: 6px;
  align-items: center;
  margin-bottom: 6px;
  padding: 0 4px;
}
.toolbar-top :deep(.el-button) {
  color: #54656f;
  border: none;
  background: transparent;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s;
  font-size: 18px;
}
.toolbar-top :deep(.el-button:hover) {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}
.toolbar-top :deep(.el-button:active) {
  transform: scale(0.95);
}
.input-row {
  display: flex;
  align-items: center;
  gap: 10px;
}
.wechat-textarea, .input-box {
  flex: 1;
  border: none;
  background: #ffffff;
  border-radius: 24px;
  min-height: 42px;
  font-size: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  padding: 10px 16px;
  transition: all 0.2s;
  margin-right: 0;
}
.wechat-textarea:focus-within, .input-box:focus-within {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}
.send-btn {
  min-width: 42px;
  height: 42px;
  border-radius: 50%;
  font-size: 20px;
  background: #00a884;
  color: #fff;
  border: none;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.send-btn:disabled {
  background: #e9edef;
  color: #a8bec8;
  box-shadow: none;
}
.send-btn:not(:disabled):hover {
  background: #008f72;
  transform: scale(1.05);
}
.send-btn:not(:disabled):active {
  transform: scale(0.95);
}
.preview-list {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f2f5;
  padding-bottom: 10px;
  flex-wrap: wrap;
}
.preview-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #f0f2f5;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}
.preview-item:hover {
  transform: scale(1.05);
}
.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}
.preview-remove {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0,0,0,0.5);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  cursor: pointer;
  z-index: 2;
  opacity: 0.8;
  transition: opacity 0.2s, background-color 0.2s;
}
.preview-remove:hover {
  opacity: 1;
  background: rgba(0,0,0,0.7);
}
.emoji-picker-fix {
  position: absolute;
  left: 16px;
  bottom: 120px;
  width: calc(100% - 32px);
  z-index: 100;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  background-color: #fff;
}

.emoji-picker-fix :deep(.emoji-picker) {
  border: none;
  box-shadow: none;
  border-radius: 0;
  width: 100%;
}
.preview-audio {
  width: 60px;
  height: 40px;
  outline: none;
  border-radius: 8px;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-video {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
}
.upload-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
  color: #f56c6c;
}

/* 网络状态提示样式 */
.network-status-alert {
  position: absolute; /* 绝对定位，相对于.chat-container */
  top: 0;
  left: 0;
  right: 0;
  background-color: #f56c6c;
  color: white;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100; /* 确保在其他内容之上，但不要太高 */
  font-size: 14px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.network-status-alert.reconnecting {
  background-color: #e6a23c;
}

.network-status-alert .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.network-status-alert.reconnecting {
  animation: pulse 1.5s infinite;
}

.network-status-alert.offline {
  background-color: #f56c6c;
}

.network-status-alert.offline-message {
  background-color: #409eff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
}

.network-status-alert.offline-message .el-button {
  margin-left: 16px;
  background-color: #ffffff;
  color: #409eff;
  border: none;
  font-weight: bold;
}

/* 响应式布局样式 */
@media screen and (max-width: 768px) {
  .chat-container {
    flex-direction: column;
    height: calc(100vh - 70px);
  }

  .chat-area {
    margin: 8px;
    height: auto;
    flex: 1;
    width: calc(100% - 16px); /* 100% - 左右边距 */
    min-height: 400px; /* 确保最小高度 */
    z-index: 30; /* 确保聊天区域在其他元素之上 */
    position: relative; /* 确保z-index生效 */
    visibility: visible !important; /* 强制可见 */
    opacity: 1 !important; /* 强制不透明 */
    display: flex !important; /* 强制显示 */
  }

  .chat-messages {
    height: calc(100vh - 70px - 60px - 120px - 16px); /* 视口高度 - 顶部导航 - 聊天头部 - 输入框 - 边距 */
    min-height: 200px; /* 确保最小高度 */
  }
}

@media screen and (min-width: 769px) and (max-width: 1200px) {
  .chat-container {
    flex-direction: row;
    height: calc(100vh - 70px);
  }

  .chat-area {
    margin: 12px;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 94px); /* 视口高度 - 顶部导航 - 边距 */
    min-height: 500px;
    min-width: 350px; /* 确保最小宽度 */
    width: calc(100% - 344px); /* 100% - 联系人列表宽度(320px) - 联系人列表左边距(12px) - 聊天区域左边距(12px) */
    z-index: 30; /* 确保聊天区域在其他元素之上 */
    position: relative; /* 确保z-index生效 */
    visibility: visible !important; /* 强制可见 */
    opacity: 1 !important; /* 强制不透明 */
    display: flex !important; /* 强制显示 */
  }

  .chat-messages {
    flex: 1;
    height: auto !important; /* 覆盖默认高度计算 */
    min-height: 300px;
  }
}

@media screen and (min-width: 1201px) {
  .chat-container {
    flex-direction: row;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    left: auto;
    right: auto;
    height: calc(100vh - 70px);
  }

  .chat-area {
    margin: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 102px); /* 视口高度 - 顶部导航 - 边距 */
    min-height: 600px;
    min-width: 400px; /* 确保最小宽度 */
    width: calc(100% - 382px); /* 100% - 联系人列表宽度(350px) - 联系人列表左边距(16px) - 聊天区域左边距(16px) */
    z-index: 30; /* 确保聊天区域在其他元素之上 */
    position: relative; /* 确保z-index生效 */
    visibility: visible !important; /* 强制可见 */
    opacity: 1 !important; /* 强制不透明 */
    display: flex !important; /* 强制显示 */
  }

  .chat-messages {
    flex: 1;
    height: auto !important; /* 覆盖默认高度计算 */
    min-height: 400px;
  }
}
</style>