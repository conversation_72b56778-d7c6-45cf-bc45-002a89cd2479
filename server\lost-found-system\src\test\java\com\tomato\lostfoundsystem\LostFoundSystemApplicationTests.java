package com.tomato.lostfoundsystem;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootTest
class LostFoundSystemApplicationTests {

    @Test
    void contextLoads() {
    }
    @RestController
    @RequestMapping("/api/test")
    public class TestController {

        @GetMapping("/ping")
        public String ping() {
            return "pong!";
        }
    }


}
