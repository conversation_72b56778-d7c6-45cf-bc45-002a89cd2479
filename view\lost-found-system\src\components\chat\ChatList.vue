<template>
  <div class="chat-list">
    <div class="list-header">
      <h3 class="list-title">聊天消息</h3>
      <el-badge :value="totalUnreadCount" :hidden="totalUnreadCount === 0" type="danger">
        <el-button icon="Plus" circle size="small" @click="$emit('create-chat')" />
      </el-badge>
    </div>
    <div class="search-box">
      <el-input
        v-model="searchQuery"
        placeholder="搜索联系人"
        :prefix-icon="Search"
        clearable
      />
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-contacts">
      <div class="contact-skeleton" v-for="i in 5" :key="i">
        <div class="skeleton-avatar"></div>
        <div class="skeleton-content">
          <div class="skeleton-name"></div>
          <div class="skeleton-message"></div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="contacts.length === 0" class="empty-contacts">
      <el-empty description="暂无聊天记录" :image-size="80" />
    </div>

    <!-- 联系人列表 -->
    <div v-else class="contact-list">
      <div
        v-for="contact in filteredContacts"
        :key="contact.id"
        class="contact-item"
        :class="{
          active: selectedContact?.id === contact.id,
          unread: contact.unreadCount > 0,
          pinned: contact.isPinned,
          muted: contact.isMuted
        }"
        @click="selectContact(contact)"
      >
        <!-- 置顶标记 -->
        <div v-if="contact.isPinned" class="pin-indicator">
          <el-icon><Top /></el-icon>
        </div>

        <!-- Instagram 风格头像 -->
        <div class="instagram-avatar">
          <el-avatar
            :size="56"
            :src="contact.avatar"
            class="contact-avatar"
            :style="!contact.avatar ? {
              backgroundColor: getContactColor(contact.id, currentUserColor),
              color: '#ffffff',
              fontWeight: '500',
              fontSize: '20px',
              textTransform: 'uppercase'
            } : {}"
          >
            <span class="avatar-text">{{ contact.avatarText || contact.name.substring(0, 1) }}</span>
          </el-avatar>

          <!-- 在线状态指示器 -->
          <div v-if="contact.online" class="online-indicator"></div>
        </div>

        <!-- 消息内容区域 -->
        <div class="message-content">
          <!-- 用户名和时间行 -->
          <div class="name-time-row">
            <div class="contact-name">{{ contact.name }}</div>
            <div class="message-meta">
              <span v-if="contact.online" class="active-status">在线</span>
              <span v-if="contact.isMuted" class="muted-status">
                <el-icon><Mute /></el-icon>
              </span>
              <span class="contact-time">{{ formatTime(contact.lastTime) || '4月25日' }}</span>
            </div>
          </div>

          <!-- 消息预览行 -->
          <div class="message-preview-row">
            <!-- 消息预览 -->
            <div class="message-preview" :class="{ 'text-bold': contact.unreadCount > 0 }">
              <!-- 根据消息类型显示不同的内容 -->
              <template v-if="contact.messageType === 'TEXT'">
                {{ contact.lastMessage || '暂无消息' }}
              </template>

              <template v-else-if="contact.messageType === 'IMAGE'">
                <el-icon><Picture /></el-icon> 发送了一张图片
              </template>

              <template v-else-if="contact.messageType === 'AUDIO'">
                <el-icon><Headset /></el-icon>
                发送了一条语音消息
              </template>

              <template v-else-if="contact.messageType === 'VIDEO'">
                <el-icon><VideoCamera /></el-icon>
                发送了一段视频
              </template>

              <template v-else-if="contact.messageType === 'DOCUMENT'">
                <el-icon><Document /></el-icon>
                发送了一个文件
              </template>

              <template v-else>
                {{ contact.lastMessage || '暂无消息' }}
              </template>
            </div>

            <!-- 未读消息标识 -->
            <div v-if="contact.unreadCount > 0" class="unread-indicator">
              <span class="unread-badge">{{ contact.unreadCount }}</span>
            </div>
          </div>
        </div>

        <!-- 会话操作按钮 -->
        <div class="contact-actions" @click.stop>
          <el-dropdown trigger="click" @command="handleContactAction($event, contact)">
            <el-button type="text" class="action-button">
              <el-icon><More /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="contact.isPinned ? 'unpin' : 'pin'">
                  {{ contact.isPinned ? '取消置顶' : '置顶' }}
                </el-dropdown-item>
                <el-dropdown-item :command="contact.isMuted ? 'unmute' : 'mute'">
                  {{ contact.isMuted ? '取消静音' : '静音' }}
                </el-dropdown-item>
                <el-dropdown-item command="archive">归档</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 底部操作区 -->
    <div class="list-footer">
      <el-tooltip content="新建聊天" placement="top">
        <el-button circle :icon="Plus" type="primary" @click="createNewChat" />
      </el-tooltip>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Search, Picture, Headset, VideoCamera, Document, Plus, Top, Mute, More } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getContacts } from '@/api/chat'
import { useUserStore, useConversationStore } from '@/stores'
import { getUserColor, getRandomDarkColorExcept, getContactColor } from '@/utils/avatar-utils'
// 导入防抖函数
import { debounce } from 'lodash-es'
// 使用统一的WebSocket功能入口
import {
  isWebSocketConnected
} from '@/utils/websocket/index'
// 从联系人状态管理模块导入联系人状态相关函数
import {
  subscribeContactStatus,
  unsubscribeContactStatus,
  getContactsStatus,
  isContactOnline
} from '@/utils/contact-status'
// 从在线状态服务导入在线状态相关函数
import {
  getOnlineUsers,
  isUserOnline,
  requestOnlineUsers as requestAllOnlineUsers
} from '@/services/onlineStatusService'
// 导入全局消息处理器
import globalMessageHandler from '@/utils/globalMessageHandler'
// 导入联系人通知管理器
import { showContactOnlineNotification } from '@/utils/contactNotificationManager'

// 获取当前用户信息
const userStore = useUserStore()
const conversationStore = useConversationStore()
const currentUserColor = computed(() => getUserColor(userStore.userInfo?.username))

// 计算未读消息总数
const totalUnreadCount = computed(() => {
  return props.contacts.reduce((sum, contact) => sum + (contact.unreadCount || 0), 0)
})

const props = defineProps({
  contacts: {
    type: Array,
    default: () => []
  },
  selectedContact: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  userId: {
    type: [Number, String],
    default: ''
  }
})

const emit = defineEmits(['select', 'create-chat', 'update:loading', 'update:contacts', 'conversation-action'])

const searchQuery = ref('')

// 过滤联系人列表
const filteredContacts = computed(() => {
  if (!searchQuery.value) return props.contacts
  const query = searchQuery.value.toLowerCase()
  return props.contacts.filter(contact =>
    contact.name.toLowerCase().includes(query)
  )
})

// 获取用户的联系人数据
const fetchContacts = async () => {
  if (!props.userId) {
    console.error('获取联系人列表：userId 不能为空')
    return
  }

  try {
    emit('update:loading', true) // 显示加载状态
    console.log('开始获取联系人列表，用户ID:', props.userId)
    const response = await getContacts(props.userId)
    console.log('获取联系人列表原始响应:', response)

    if (response && (response.code === 200 || response.status === 200)) {
      // 检查响应中的data字段
      console.log('响应中的data字段类型:', typeof response.data)
      console.log('响应中的data字段值:', response.data)

      // 检查数据是否为null或undefined
      if (!response.data) {
        console.log('响应数据为空，设置空联系人列表')
        emit('update:contacts', [])
        return
      }

      if (Array.isArray(response.data)) {
        // 处理数组数据
        if (response.data.length === 0) {
          console.log('联系人列表为空')
          emit('update:contacts', [])
          return
        }

        // 保存当前联系人列表，用于合并数据
        const currentContacts = [...props.contacts];

        // 处理新的联系人数据
        const contactsData = response.data.map(contact => {
          console.log('处理联系人:', contact)
          const userId = contact.contactId || contact.id
          // 检查用户在线状态
          const online = isUserOnline(userId)

          // 查找现有联系人数据
          const existingContact = currentContacts.find(c => String(c.id) === String(userId));

          // 合并数据，保留现有联系人的在线状态
          return {
            ...contact,
            // 使用contactId作为id，如果contactId不存在则使用id
            id: userId || Date.now() + Math.random(),
            // 确保有默认avatar文字
            avatarText: contact.avatarText || (contact.name ? contact.name.substring(0, 1).toUpperCase() : 'U'),
            // 确保有默认的lastMessage
            lastMessage: contact.lastMessage || '暂无消息',
            // 确保未读消息计数是数字
            unreadCount: parseInt(contact.unreadCount || 0, 10),
            // 保留现有联系人的在线状态，如果存在的话
            online: existingContact ? existingContact.online : online
          }
        })

        console.log('处理后的联系人数据:', contactsData)

        // 按最后消息时间排序，确保最新消息的联系人在顶部
        contactsData.sort((a, b) => {
          // 首先按未读消息数量排序
          if (a.unreadCount && !b.unreadCount) return -1;
          if (!a.unreadCount && b.unreadCount) return 1;
          if (a.unreadCount && b.unreadCount) {
            if (a.unreadCount > b.unreadCount) return -1;
            if (a.unreadCount < b.unreadCount) return 1;
          }

          // 然后按最后消息时间排序
          if (a.lastTime && b.lastTime) {
            return new Date(b.lastTime) - new Date(a.lastTime);
          }
          // 如果只有一个有lastTime，有lastTime的排在前面
          if (a.lastTime && !b.lastTime) return -1;
          if (!a.lastTime && b.lastTime) return 1;
          // 如果都没有lastTime，保持原顺序
          return 0;
        });

        // 更新组件内联系人列表
        emit('update:contacts', contactsData)

        // 更新全局联系人列表
        globalMessageHandler.updateContacts(contactsData)
      } else if (typeof response.data === 'object' && response.data !== null) {
        // 处理单个对象
        const contact = response.data
        console.log('处理单个联系人:', contact)

        const userId = contact.contactId || contact.id
        // 检查用户在线状态
        const online = isUserOnline(userId)

        // 查找现有联系人数据
        const existingContact = props.contacts.find(c => String(c.id) === String(userId));

        const contactData = {
          ...contact,
          id: userId || Date.now() + Math.random(),
          avatarText: contact.avatarText || (contact.name ? contact.name.substring(0, 1).toUpperCase() : 'U'),
          lastMessage: contact.lastMessage || '暂无消息',
          unreadCount: parseInt(contact.unreadCount || 0, 10),
          // 保留现有联系人的在线状态，如果存在的话
          online: existingContact ? existingContact.online : online
        }

        console.log('处理后的单个联系人数据:', contactData)

        // 如果已有联系人列表，将新联系人添加到列表顶部
        if (props.contacts.length > 0) {
          const newContacts = [...props.contacts];
          // 检查联系人是否已存在
          const existingIndex = newContacts.findIndex(c => String(c.id) === String(userId));
          if (existingIndex !== -1) {
            // 更新现有联系人
            newContacts[existingIndex] = contactData;
            // 将联系人移到顶部
            const updatedContact = newContacts.splice(existingIndex, 1)[0];
            newContacts.unshift(updatedContact);
          } else {
            // 添加新联系人到顶部
            newContacts.unshift(contactData);
          }
          // 更新组件内联系人列表
          emit('update:contacts', newContacts);

          // 更新全局联系人列表
          globalMessageHandler.updateContacts(newContacts);
        } else {
          // 更新组件内联系人列表
          emit('update:contacts', [contactData]);

          // 更新全局联系人列表
          globalMessageHandler.updateContacts([contactData]);
        }
      } else {
        console.warn('获取联系人列表：返回的数据格式不符合预期', response.data)
        emit('update:contacts', [])

        // 更新全局联系人列表
        globalMessageHandler.updateContacts([])
      }
    } else {
      console.error('获取联系人列表失败:', response?.message || '未知错误')
      ElMessage.error('获取联系人列表失败')
      emit('update:contacts', [])

      // 更新全局联系人列表
      globalMessageHandler.updateContacts([])
    }
  } catch (error) {
    console.error('获取联系人列表发生错误:', error)
    console.log('处理错误，设置空联系人列表')
    ElMessage.error('获取联系人列表失败')
    emit('update:contacts', [])

    // 更新全局联系人列表
    globalMessageHandler.updateContacts([])
  } finally {
    emit('update:loading', false) // 隐藏加载状态
  }
}

// 选择联系人
const selectContact = (contact) => {
  // 订阅联系人在线状态
  if (contact && contact.id) {
    console.log(`订阅联系人 ${contact.name} (ID: ${contact.id}) 的在线状态`);
    subscribeContactStatus(contact.id.toString());
  }

  emit('select', contact);
}

// 创建新聊天
const createNewChat = () => {
  emit('create-chat')
  ElMessage.info({
    message: '新建聊天功能即将上线',
    duration: 2000
  })
}

// 处理会话操作
const handleContactAction = (command, contact) => {
  console.log(`执行会话操作: ${command}，联系人:`, contact)

  if (!contact || !contact.id) {
    ElMessage.error('无效的联系人信息')
    return
  }

  // 将会话操作传递给父组件处理
  emit('conversation-action', { action: command, contact })
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''

  try {
    // 处理非标准时间格式
    let date;

    if (typeof time === 'string' && time.includes('CST')) {
      // 尝试直接解析英文日期格式 "Mon Apr 28 22:51:54 CST 2025"
      date = new Date(time);
    } else {
      // 标准格式
      date = new Date(time);
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的时间格式:', time);
      return '';
    }

    const now = new Date()

  // 今天
    if (date.getDate() === now.getDate() &&
        date.getMonth() === now.getMonth() &&
        date.getFullYear() === now.getFullYear()) {
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

    // 计算昨天的日期
    const yesterday = new Date(now)
    yesterday.setDate(now.getDate() - 1)

  // 昨天
    if (date.getDate() === yesterday.getDate() &&
        date.getMonth() === yesterday.getMonth() &&
        date.getFullYear() === yesterday.getFullYear()) {
    return '昨天'
  }

    // 今年内的消息
    if (date.getFullYear() === now.getFullYear()) {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }

    // 更早的消息
    return `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, '0')}/${String(date.getDate()).padStart(2, '0')}`
  } catch (error) {
    console.error('格式化时间出错:', error, time);
    return '';
  }
}

// 格式化时长
const formatDuration = (duration) => {
  if (!duration) return ''

  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return `${seconds}秒`
  }
}

// 使用导入的getContactAvatarColor函数，但需要确保与当前用户颜色不同
// 这里我们不再定义本地函数，而是使用导入的函数



// 处理用户在线状态变更事件
const handleUserOnlineStatus = (event) => {
  const { userId, isOnline } = event.detail
  console.log(`收到用户 ${userId} 在线状态变更事件:`, isOnline)

  // 更新联系人列表中的用户状态
  const contact = props.contacts.find(c => c.id.toString() === userId.toString())
  if (contact) {
    // 只有状态变化时才更新
    if (contact.online !== isOnline) {
      contact.online = isOnline
      console.log(`联系人 ${contact.name} 在线状态已更新为: ${isOnline ? '在线' : '离线'}`)

      // 如果用户上线，使用通知管理器显示通知
      if (isOnline) {
        showContactOnlineNotification(contact, props.selectedContact)
      }
    }
  }
}

// 更新所有联系人的在线状态
const updateContactsOnlineStatus = async () => {
  if (!props.contacts.length) return

  console.log('开始更新联系人在线状态, 联系人数量:', props.contacts.length)

  // 记录更新前的在线状态
  const previousStatus = {};
  props.contacts.forEach(contact => {
    if (contact.id) {
      previousStatus[contact.id] = contact.online || false;
    }
  });

  try {
    // 获取所有联系人ID
    const contactIds = props.contacts.map(contact => contact.id.toString());

    // 批量获取联系人在线状态
    await getContactsStatus(contactIds);

    // 更新联系人状态
    let updatedCount = 0;
    props.contacts.forEach(contact => {
      if (contact.id) {
        const userId = contact.id.toString();
        const isOnline = isContactOnline(userId);

        console.log(`检查联系人 ${contact.name} (ID: ${userId}) 在线状态: ${isOnline ? '在线' : '离线'}`);

        // 只在状态变化时更新
        if (contact.online !== isOnline) {
          console.log(`更新联系人 ${contact.name} 在线状态: ${isOnline ? '在线' : '离线'}`);
          contact.online = isOnline;
          updatedCount++;

          // 如果用户上线，使用通知管理器显示通知
          if (isOnline && !previousStatus[contact.id]) {
            showContactOnlineNotification(contact, props.selectedContact);
          }
        }
      }
    });

    console.log(`联系人在线状态更新完成，共更新 ${updatedCount} 个联系人状态`);
  } catch (error) {
    console.error('更新联系人在线状态失败:', error);

    // 如果新方法失败，回退到旧方法
    const onlineUsers = getOnlineUsers();
    console.log('从缓存获取所有在线用户:', onlineUsers);

    // 更新联系人状态
    let updatedCount = 0;
    props.contacts.forEach(contact => {
      if (contact.id) {
        const userId = contact.id.toString();
        const isOnline = isUserOnline(userId);

        // 只在状态变化时更新
        if (contact.online !== isOnline) {
          console.log(`(回退方法)更新联系人 ${contact.name} 在线状态: ${isOnline ? '在线' : '离线'}`);
          contact.online = isOnline;
          updatedCount++;
        }
      }
    });

    console.log(`(回退方法)联系人在线状态更新完成，共更新 ${updatedCount} 个联系人状态`);

    // 如果没有在线用户，尝试请求获取最新的在线用户列表
    if (Object.keys(onlineUsers).length === 0 && isWebSocketConnected()) {
      console.log('未检测到在线用户，尝试请求获取最新在线用户列表');
      requestAllOnlineUsers();
    }
  }
}

// 处理联系人状态更新事件
const handleContactStatusUpdate = (event) => {
  const { contactId, status } = event.detail;

  // 更新联系人列表中的用户状态
  const contact = props.contacts.find(c => c.id.toString() === contactId.toString());
  if (contact) {
    // 只有状态变化时才更新
    if (contact.online !== status.online) {
      console.log(`联系人 ${contact.name} 在线状态已更新为: ${status.online ? '在线' : '离线'}`);
      contact.online = status.online;

      // 如果用户上线，使用通知管理器显示通知
      if (status.online) {
        showContactOnlineNotification(contact, props.selectedContact);
      }
    }
  }
};

// 创建防抖版本的fetchContacts函数，500毫秒内的多次调用会被合并为一次
const debouncedFetchContacts = debounce(fetchContacts, 500);

// 处理刷新联系人列表事件 - 只更新在线状态，不刷新整个列表
const handleRefreshContacts = (event) => {
  try {
    console.log('ChatList组件收到刷新联系人列表事件:', event);

    // 只更新联系人在线状态，不刷新整个列表
    if (props.userId) {
      console.log('ChatList组件只更新联系人在线状态');
      updateContactsOnlineStatus();
    }
  } catch (error) {
    console.error('ChatList组件处理刷新联系人列表事件时出错:', error);
  }
};

// 处理全局刷新联系人列表事件 - 只更新在线状态，不刷新整个列表
const handleGlobalRefreshContacts = (event) => {
  try {
    console.log('ChatList组件收到全局刷新联系人列表事件:', event);

    // 只更新联系人在线状态，不刷新整个列表
    if (props.userId) {
      console.log('ChatList组件只更新联系人在线状态（全局事件）');
      updateContactsOnlineStatus();
    }
  } catch (error) {
    console.error('ChatList组件处理全局刷新联系人列表事件时出错:', error);
  }
};

// 组件加载时获取联系人信息
onMounted(() => {
  console.log('ChatList组件挂载');

  // 添加WebSocket重连事件监听
  window.addEventListener('websocket-connected', () => {
    console.log('WebSocket已重连，更新联系人在线状态');
    setTimeout(updateContactsOnlineStatus, 1000);

    // 注意：已移除重连后刷新联系人列表的代码，只更新在线状态
  });

  // 监听用户在线状态变更事件
  window.addEventListener('user-online-status', handleUserOnlineStatus);

  // 监听联系人状态更新事件
  window.addEventListener('contact-status-updated', handleContactStatusUpdate);

  // 监听在线用户列表更新事件
  window.addEventListener('online-users-updated', () => {
    console.log('收到在线用户列表更新事件，更新所有联系人状态');
    updateContactsOnlineStatus();
  });

  // 监听刷新联系人列表事件
  window.addEventListener('refresh-contacts', handleRefreshContacts);

  // 监听全局刷新联系人列表事件
  window.addEventListener('global-refresh-contacts', handleGlobalRefreshContacts);

  // 监听全局联系人列表更新事件
  window.addEventListener('contacts-updated', (event) => {
    console.log('ChatList组件收到全局联系人列表更新事件:', event);
    if (event.detail && event.detail.contacts) {
      // 更新组件内联系人列表
      emit('update:contacts', event.detail.contacts);
    }
  });

  if (props.userId) {
    // 检查是否已有联系人列表数据
    if (props.contacts && props.contacts.length > 0) {
      console.log('已有联系人列表数据，只更新在线状态');
      // 只更新在线状态，不重新获取联系人列表
      setTimeout(updateContactsOnlineStatus, 1000);
    } else {
      // 只有在没有联系人列表数据时才获取
      console.log('没有联系人列表数据，进行初始加载');
      // WebSocket连接状态检查
      if (isWebSocketConnected()) {
        console.log('WebSocket已连接，直接获取联系人信息');
        // 使用非防抖版本进行初始加载
        fetchContacts();
        // 延迟更新在线状态，确保联系人列表已加载
        setTimeout(updateContactsOnlineStatus, 1000);
      } else {
        console.log('WebSocket未连接，延迟获取联系人信息');
        // 延迟获取联系人信息，确保WebSocket有时间连接
        setTimeout(() => {
          // 使用非防抖版本进行初始加载
          fetchContacts();
          // 再次延迟更新在线状态
          setTimeout(updateContactsOnlineStatus, 1000);
        }, 2000);
      }
    }
  }
});

// 组件卸载时清除事件监听器
onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('user-online-status', handleUserOnlineStatus);
  window.removeEventListener('contact-status-updated', handleContactStatusUpdate);
  window.removeEventListener('websocket-connected', () => {});
  window.removeEventListener('online-users-updated', () => {});
  window.removeEventListener('refresh-contacts', handleRefreshContacts);
  window.removeEventListener('global-refresh-contacts', handleGlobalRefreshContacts);
  window.removeEventListener('contacts-updated', () => {});

  // 取消所有联系人订阅
  props.contacts.forEach(contact => {
    if (contact && contact.id) {
      unsubscribeContactStatus(contact.id.toString());
    }
  });

  console.log('ChatList组件卸载');
});

// 监听联系人列表变化
watch(() => props.contacts, (newContacts) => {
  if (newContacts.length > 0 && isWebSocketConnected()) {
    console.log('联系人列表已更新，更新在线状态');

    // 订阅所有联系人的在线状态
    newContacts.forEach(contact => {
      if (contact && contact.id) {
        subscribeContactStatus(contact.id.toString());
      }
    });

    // 延迟更新在线状态，确保联系人列表已完全更新
    setTimeout(updateContactsOnlineStatus, 500);
  }
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  fetchContacts,
  debouncedFetchContacts
})
</script>

<style scoped>
.chat-list {
  width: 350px; /* Instagram 风格宽度 */
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff; /* Instagram 纯白背景 */
  border-right: 1px solid #dbdbdb; /* Instagram 边框色 */
  overflow: hidden;
}

.list-header {
  padding: 16px;
  border-bottom: 1px solid #dbdbdb; /* Instagram 边框色 */
  background-color: #ffffff;
}

.list-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626; /* Instagram 黑色 */
  text-align: center;
}

.search-box {
  padding: 12px 16px;
  border-bottom: 1px solid #dbdbdb; /* Instagram 边框色 */
  background-color: #ffffff;
}

.search-box :deep(.el-input__wrapper) {
  border-radius: 8px; /* Instagram 风格圆角 */
  padding-left: 12px;
  box-shadow: 0 0 0 1px #dbdbdb inset; /* Instagram 边框色 */
  transition: all 0.2s;
  background-color: #fafafa; /* Instagram 浅灰背景 */
}

.search-box :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c7c7c7 inset;
}

.search-box :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #8e8e8e inset; /* Instagram 灰色 */
  background-color: #ffffff;
}

.loading-contacts {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.contact-skeleton {
  display: flex;
  padding: 12px;
  gap: 12px;
  margin-bottom: 8px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f2f5;
  flex-shrink: 0;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-name {
  height: 16px;
  width: 70%;
  background-color: #f0f2f5;
  border-radius: 4px;
}

.skeleton-message {
  height: 12px;
  width: 100%;
  background-color: #f0f2f5;
  border-radius: 4px;
}

.empty-contacts {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.contact-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: #ffffff;
}

.contact-item {
  display: flex;
  padding: 10px 16px;
  cursor: pointer;
  align-items: center;
  transition: all 0.2s ease;
  position: relative;
  gap: 12px;
  border-bottom: 1px solid #f2f2f2;
  background-color: #ffffff;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
  height: 68px;
}

.contact-item:hover {
  background-color: #f5f6f6;
}

.contact-item.active {
  background-color: #f0f2f5;
  position: relative;
}

.contact-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #00a884;
  border-radius: 0 4px 4px 0;
}

.contact-item.unread {
  background-color: #f0f7ff;
}

.contact-item.unread.active {
  background-color: #e6f7ff;
}

.contact-item.unread::after {
  content: '';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #00a884;
  box-shadow: 0 0 0 2px rgba(0, 168, 132, 0.2);
}



/* Instagram 风格头像容器 */
.instagram-avatar {
  position: relative;
  flex-shrink: 0;
}

/* 消息内容区域 */
.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  height: 100%;
}

.contact-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  border: none;
  position: relative;
  overflow: visible;
  width: 56px; /* Instagram 风格尺寸 */
  height: 56px; /* Instagram 风格尺寸 */
  border-radius: 50%;
  flex-shrink: 0;
}

/* 在线状态指示器 */
.online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 14px;
  height: 14px;
  background-color: #4dc247; /* WhatsApp 绿色 */
  border-radius: 50%;
  border: 2px solid #ffffff;
  z-index: 1;
}

.avatar-online, .avatar-offline {
  background-color: #c62828; /* 深红色背景，与登录头像一致 */
  color: #ffffff; /* 白色文字 */
  border: none;
  box-shadow: none;
  background-image: none;
}

.avatar-text {
  font-weight: 500;
  font-size: 20px; /* 更大的字体 */
  letter-spacing: 0;
  text-shadow: none;
  text-transform: uppercase; /* 确保字母大写 */
}

.contact-item:hover .contact-avatar {
  transform: none;
  box-shadow: none;
}

/* 添加在线状态指示器 - 绿色风格 */
.avatar-online::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background-color: #07C160; /* 微信绿色 */
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: none;
  animation: none;
}

/* 添加离线状态指示器 - 灰色风格 */
.avatar-offline::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background-color: #999999; /* 暗灰色 */
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: none;
  animation: none;
}

.contact-info {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 2px;
  width: calc(100% - 60px);
}

/* 用户名和时间行 */
.name-time-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  width: 100%;
}

/* 消息预览行 */
.message-preview-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 消息元数据 */
.message-meta {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #667781;
}

/* 在线状态 */
.active-status {
  color: #00a884;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.active-status::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #00a884;
  margin-right: 4px;
}

.contact-name {
  font-size: 15px;
  font-weight: 600;
  color: #111b21;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  padding-right: 8px;
}

.contact-time {
  font-size: 11px;
  color: #667781;
  white-space: nowrap;
  text-align: right;
  font-weight: normal;
}

.unread .contact-time {
  color: #00a884;
  font-weight: 600;
}

/* 消息预览 */
.message-preview {
  font-size: 13px;
  color: #667781;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  padding-right: 8px;
  line-height: 1.4;
}

.unread .message-preview {
  font-weight: 500;
  color: #111b21;
}

/* 状态区域 */
.status-area {
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
}

.text-bold {
  font-weight: 600;
  color: #262626; /* Instagram 黑色 */
}

.contact-status {
  position: absolute;
  top: 16px;
  right: 16px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.online {
  background-color: #67c23a;
  box-shadow: 0 0 4px #67c23a;
}

.status-indicator.offline {
  background-color: #909399;
}

.list-footer {
  padding: 12px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: center;
}

.time-status-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 45px;
  justify-content: center;
  gap: 4px;
}

.status-time-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}



.status-area {
  position: absolute;
  right: 16px;
  top: 12px;
  bottom: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  gap: 6px;
}

.status-badge {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
  display: inline-block;
  font-weight: normal;
  text-align: center;
  margin-bottom: 0;
  letter-spacing: 0;
  box-shadow: none;
  transition: none;
}

.status-badge:hover {
  box-shadow: none;
}

.status-online {
  color: white;
  background-color: #07C160; /* 微信绿色 */
  background-image: none;
}

.status-offline {
  display: none; /* 隐藏离线状态标识 */
}

/* 在线/离线状态样式 - 基础样式 */
.online-status {
  font-size: 11px;
  padding: 1px 6px;
  border-radius: 3px;
  white-space: nowrap;
  display: inline-block;
  font-weight: normal;
  text-align: center;
  box-shadow: none;
}

/* 在线状态样式 - 绿色风格 */
.status-online-text {
  color: #07C160; /* 微信绿色 */
  background-color: #f0f9eb; /* 浅绿色背景 */
  border: 1px solid #e1f3d8; /* 浅绿色边框 */
}

/* 离线状态样式 - 灰色风格 */
.status-offline-text {
  color: #909399; /* 暗灰色 */
  background-color: #f4f4f5; /* 浅灰色背景 */
  border: 1px solid #e9e9eb; /* 浅灰色边框 */
}

/* 未读消息指示器容器 */
.unread-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 未读消息标识 */
.unread-badge {
  background-color: #0095f6; /* Instagram 蓝色 */
  color: white;
  border-radius: 50%;
  padding: 0;
  font-size: 12px;
  min-width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  font-weight: bold;
}

/* 这部分样式已经在上面定义，这里删除重复的部分 */

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}



/* 会话管理相关样式 */
.contact-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: 8px;
}

.contact-item:hover .contact-actions {
  opacity: 1;
}

.action-button {
  padding: 4px;
  color: #8e8e8e;
}

.action-button:hover {
  color: #262626;
}

/* 置顶标记 */
.pin-indicator {
  position: absolute;
  top: 8px;
  left: 8px;
  color: #0095f6;
  font-size: 12px;
}

/* 静音标记 */
.muted-status {
  color: #8e8e8e;
  margin-right: 4px;
}

/* 置顶会话样式 */
.contact-item.pinned {
  background-color: rgba(0, 149, 246, 0.05);
  border-left: 3px solid #0095f6;
}

/* 静音会话样式 */
.contact-item.muted .message-preview {
  color: #8e8e8e;
  font-style: italic;
}

@media screen and (max-width: 768px) {
  .chat-list {
    width: 100%;
    height: auto;
    min-height: 200px; /* 确保最小高度 */
    max-height: 40vh;
    margin: 0;
    border-radius: 0;
    flex: 0 0 auto; /* 不允许伸缩 */
  }

  .list-header, .list-footer {
    display: none;
  }

  .contact-actions {
    opacity: 1;
  }
}

@media screen and (min-width: 769px) and (max-width: 1200px) {
  .chat-list {
    width: 320px;
    min-width: 320px;
    max-width: 320px; /* 限制最大宽度 */
    flex: 0 0 320px; /* 不允许伸缩 */
    height: 100%;
    margin: 12px 0 12px 12px;
  }
}

@media screen and (min-width: 1201px) {
  .chat-list {
    width: 350px;
    min-width: 350px;
    max-width: 350px; /* 限制最大宽度 */
    flex: 0 0 350px; /* 不允许伸缩 */
    height: 100%;
    margin: 16px 0 16px 16px;
  }
}
</style>