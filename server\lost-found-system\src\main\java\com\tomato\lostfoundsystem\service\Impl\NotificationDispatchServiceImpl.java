package com.tomato.lostfoundsystem.service.Impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.dto.NotificationDTO;
import com.tomato.lostfoundsystem.service.KafkaProducerService;
import com.tomato.lostfoundsystem.service.NotificationDispatchService;
import com.tomato.lostfoundsystem.service.NotificationEventService;
import com.tomato.lostfoundsystem.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 通知分发服务实现类
 * 负责根据用户在线状态选择合适的通知发送方式
 */
@Slf4j
@Service
public class NotificationDispatchServiceImpl implements NotificationDispatchService {

    @Autowired
    private RedisService redisService;
    
    @Autowired
    private NotificationEventService notificationEventService;
    
    @Autowired
    private KafkaProducerService kafkaProducerService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public void dispatchNotification(Long userId, String title, String message, Long notificationId, String notificationType) {
        dispatchNotification(userId, title, message, notificationId, notificationType, null, null, null);
    }
    
    @Override
    public void dispatchNotification(Long userId, String title, String message, Long notificationId, 
                                    String notificationType, Long relatedItemId, String relatedItemType) {
        dispatchNotification(userId, title, message, notificationId, notificationType, relatedItemId, relatedItemType, null);
    }
    
    @Override
    public void dispatchNotification(Long userId, String title, String message, Long notificationId, 
                                    String notificationType, String metadata) {
        dispatchNotification(userId, title, message, notificationId, notificationType, null, null, metadata);
    }
    
    @Override
    public void dispatchNotification(Long userId, String title, String message, Long notificationId, 
                                    String notificationType, Long relatedItemId, String relatedItemType, String metadata) {
        try {
            log.info("【通知分发】开始分发通知 - 用户ID: {}, 标题: {}, 通知ID: {}", userId, title, notificationId);
            
            // 检查用户是否在线
            boolean isUserOnline = redisService.isUserOnline(userId);
            
            if (isUserOnline) {
                // 用户在线，通过WebSocket推送通知
                log.info("【通知分发】用户在线，通过WebSocket推送通知 - 用户ID: {}", userId);
                notificationEventService.sendWebSocketNotification(userId, title, message, notificationId);
            } else {
                // 用户离线，通过Kafka存储离线通知
                log.info("【通知分发】用户离线，通过Kafka存储离线通知 - 用户ID: {}", userId);
                
                // 构建通知消息
                Map<String, Object> notificationMessage = new HashMap<>();
                notificationMessage.put("type", "NOTIFICATION");
                notificationMessage.put("receiverId", userId);
                notificationMessage.put("title", title);
                notificationMessage.put("message", message);
                notificationMessage.put("notificationType", notificationType);
                notificationMessage.put("notificationId", notificationId);
                
                // 添加相关物品信息（如果有）
                if (relatedItemId != null) {
                    notificationMessage.put("relatedItemId", relatedItemId);
                }
                if (relatedItemType != null) {
                    notificationMessage.put("relatedItemType", relatedItemType);
                }
                
                // 添加元数据（如果有）
                if (metadata != null) {
                    notificationMessage.put("metadata", metadata);
                }
                
                // 转换为JSON并发送到Kafka
                String jsonMessage = objectMapper.writeValueAsString(notificationMessage);
                kafkaProducerService.sendNotification(jsonMessage);
                
                log.info("【通知分发】离线通知已发送到Kafka - 用户ID: {}, 通知ID: {}", userId, notificationId);
            }
        } catch (Exception e) {
            log.error("【通知分发】分发通知失败: {}", e.getMessage(), e);
        }
    }
}
