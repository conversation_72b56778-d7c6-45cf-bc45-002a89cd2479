package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.BatchMessageDTO;
import com.tomato.lostfoundsystem.dto.MessageDTO;
import com.tomato.lostfoundsystem.service.ChatMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 离线消息控制器
 * 处理离线消息的批量发送和同步
 */
@Slf4j
@RestController
@RequestMapping("/api/offline-messages")
public class OfflineMessageController {

    @Autowired
    private ChatMessageService chatMessageService;

    /**
     * 批量发送离线消息
     * @param batchMessageDTO 批量消息DTO，包含多条消息
     * @return 处理结果
     */
    @PostMapping("/batch")
    public Result<List<MessageDTO>> sendBatchMessages(@RequestBody BatchMessageDTO batchMessageDTO) {
        log.info("收到批量离线消息请求，消息数量: {}", batchMessageDTO.getMessages().size());

        List<MessageDTO> results = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        // 处理每条消息
        for (MessageDTO message : batchMessageDTO.getMessages()) {
            try {
                // 保存消息到数据库并发送
                MessageDTO savedMessage = chatMessageService.saveChatMessage(message, null);
                results.add(savedMessage);
                log.info("离线消息已处理: {}", savedMessage.getId());
            } catch (Exception e) {
                log.error("处理离线消息失败: {}", e.getMessage(), e);
                errors.add("消息处理失败: " + e.getMessage());
            }
        }

        if (errors.isEmpty()) {
            return Result.success("批量消息处理成功", results);
        } else {
            return Result.success("部分消息处理成功，失败: " + String.join(", ", errors), results);
        }
    }

    /**
     * 批量发送带附件的离线消息
     * 注意：此方法仅用于演示，实际实现可能需要更复杂的处理
     */
    @PostMapping("/batch-with-files")
    public Result<String> sendBatchMessagesWithFiles(
            @RequestParam("messages") String messagesJson,
            @RequestParam("files") List<MultipartFile> files) {

        log.info("收到批量带附件的离线消息请求，文件数量: {}", files.size());

        // 实际实现中，需要解析messagesJson，并将文件与消息关联
        // 这里仅返回成功，实际项目中需要完整实现

        return Result.success("批量带附件的离线消息请求已接收");
    }

    /**
     * 获取用户的离线消息数量
     */
    @GetMapping("/count/{userId}")
    public Result<Integer> getOfflineMessageCount(@PathVariable Long userId) {
        log.info("获取用户 {} 的离线消息数量", userId);

        // 实际实现中，需要从数据库或缓存中获取离线消息数量
        // 这里返回0，实际项目中需要完整实现

        return Result.success(0);
    }
}
