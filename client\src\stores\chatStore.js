import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import axios from 'axios';
import { useWebSocketStore } from './webSocketStore';
import { useConversationStore } from './conversationStore';

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const newMessage = ref(null);
  const currentPage = ref(1);
  const totalPages = ref(1);
  const currentUserId = ref(null);
  const currentContactId = ref(null);
  
  // WebSocket Store
  const webSocketStore = useWebSocketStore();
  const conversationStore = useConversationStore();
  
  // 计算属性
  const hasMoreMessages = computed(() => currentPage.value < totalPages.value);
  
  // 方法
  async function fetchMessages(userId, contactId, page = 1, size = 20) {
    if (!userId || !contactId) return;
    
    // 如果是新的会话，清空消息列表
    if (currentUserId.value !== userId || currentContactId.value !== contactId) {
      messages.value = [];
      currentPage.value = 1;
      totalPages.value = 1;
    }
    
    currentUserId.value = userId;
    currentContactId.value = contactId;
    loading.value = true;
    error.value = null;
    
    try {
      const response = await axios.get('/api/chat/history', {
        params: {
          userId,
          otherUserId: contactId,
          page,
          size
        }
      });
      
      if (response.data.code === 200) {
        const data = response.data.data;
        
        // 如果是第一页，替换消息列表；否则，添加到消息列表前面
        if (page === 1) {
          messages.value = data.list;
        } else {
          messages.value = [...data.list, ...messages.value];
        }
        
        currentPage.value = data.pageNum;
        totalPages.value = data.pages;
      } else {
        error.value = response.data.message || '获取聊天记录失败';
      }
    } catch (err) {
      console.error('获取聊天记录失败:', err);
      error.value = err.message || '获取聊天记录失败';
    } finally {
      loading.value = false;
    }
  }
  
  async function loadMoreMessages() {
    if (loading.value || !hasMoreMessages.value) return;
    
    await fetchMessages(currentUserId.value, currentContactId.value, currentPage.value + 1);
  }
  
  async function sendTextMessage(senderId, receiverId, message) {
    if (!senderId || !receiverId || !message) return;
    
    // 生成客户端消息ID
    const clientMessageId = `client-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    
    // 创建临时消息
    const tempMessage = {
      id: clientMessageId,
      clientMessageId,
      senderId,
      receiverId,
      message,
      messageType: 'TEXT',
      timestamp: Date.now(),
      status: 'SENDING'
    };
    
    // 添加到消息列表
    messages.value.push(tempMessage);
    
    try {
      const response = await axios.post('/api/chat/privateMessage', {
        senderId,
        receiverId,
        message,
        messageType: 'TEXT',
        clientMessageId
      });
      
      if (response.data.code === 200) {
        // 更新临时消息
        const serverMessage = response.data.data;
        const index = messages.value.findIndex(msg => msg.clientMessageId === clientMessageId);
        
        if (index !== -1) {
          messages.value[index] = {
            ...serverMessage,
            status: 'SENT'
          };
        }
        
        // 更新会话列表
        conversationStore.updateConversationWithMessage(serverMessage);
        
        return serverMessage;
      } else {
        // 更新临时消息状态为发送失败
        const index = messages.value.findIndex(msg => msg.clientMessageId === clientMessageId);
        if (index !== -1) {
          messages.value[index].status = 'FAILED';
        }
        
        throw new Error(response.data.message || '发送消息失败');
      }
    } catch (err) {
      console.error('发送消息失败:', err);
      
      // 更新临时消息状态为发送失败
      const index = messages.value.findIndex(msg => msg.clientMessageId === clientMessageId);
      if (index !== -1) {
        messages.value[index].status = 'FAILED';
      }
      
      throw err;
    }
  }
  
  async function sendFileMessage(senderId, receiverId, file, messageType) {
    if (!senderId || !receiverId || !file) return;
    
    // 生成客户端消息ID
    const clientMessageId = `client-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    
    // 创建临时消息
    const tempMessage = {
      id: clientMessageId,
      clientMessageId,
      senderId,
      receiverId,
      message: file.name,
      messageType: messageType || 'FILE',
      timestamp: Date.now(),
      fileUrl: URL.createObjectURL(file),
      fileSize: file.size,
      status: 'SENDING'
    };
    
    // 添加到消息列表
    messages.value.push(tempMessage);
    
    try {
      const formData = new FormData();
      formData.append('senderId', senderId);
      formData.append('receiverId', receiverId);
      formData.append('message', file.name);
      formData.append('messageType', messageType || 'FILE');
      formData.append('clientMessageId', clientMessageId);
      formData.append('file', file);
      
      const response = await axios.post('/api/chat/privateMessage', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      if (response.data.code === 200) {
        // 更新临时消息
        const serverMessage = response.data.data;
        const index = messages.value.findIndex(msg => msg.clientMessageId === clientMessageId);
        
        if (index !== -1) {
          messages.value[index] = {
            ...serverMessage,
            status: 'SENT'
          };
        }
        
        // 更新会话列表
        conversationStore.updateConversationWithMessage(serverMessage);
        
        return serverMessage;
      } else {
        // 更新临时消息状态为发送失败
        const index = messages.value.findIndex(msg => msg.clientMessageId === clientMessageId);
        if (index !== -1) {
          messages.value[index].status = 'FAILED';
        }
        
        throw new Error(response.data.message || '发送文件失败');
      }
    } catch (err) {
      console.error('发送文件失败:', err);
      
      // 更新临时消息状态为发送失败
      const index = messages.value.findIndex(msg => msg.clientMessageId === clientMessageId);
      if (index !== -1) {
        messages.value[index].status = 'FAILED';
      }
      
      throw err;
    }
  }
  
  function sendImageMessage(senderId, receiverId, file) {
    return sendFileMessage(senderId, receiverId, file, 'IMAGE');
  }
  
  function sendAudioMessage(senderId, receiverId, file, duration) {
    const message = sendFileMessage(senderId, receiverId, file, 'AUDIO');
    
    // 如果有音频时长，更新消息
    if (duration && message) {
      const index = messages.value.findIndex(msg => msg.id === message.id);
      if (index !== -1) {
        messages.value[index].audioDuration = duration;
      }
    }
    
    return message;
  }
  
  function sendVideoMessage(senderId, receiverId, file) {
    return sendFileMessage(senderId, receiverId, file, 'VIDEO');
  }
  
  async function markMessageAsRead(messageId, userId) {
    if (!messageId || !userId) return;
    
    try {
      const response = await axios.put(`/api/chat/message/${messageId}/read`, null, {
        params: { userId }
      });
      
      if (response.data.code === 200) {
        // 更新消息的已读状态
        const index = messages.value.findIndex(msg => msg.id === messageId);
        if (index !== -1) {
          messages.value[index].isRead = true;
        }
        return true;
      }
      return false;
    } catch (err) {
      console.error('标记消息为已读失败:', err);
      return false;
    }
  }
  
  async function markAllAsRead(userId, contactId) {
    if (!userId || !contactId) return;
    
    try {
      const response = await axios.put('/api/chat/message/read-all', null, {
        params: { userId, contactId }
      });
      
      if (response.data.code === 200) {
        // 更新所有消息的已读状态
        messages.value.forEach(msg => {
          if (msg.senderId === contactId && msg.receiverId === userId) {
            msg.isRead = true;
          }
        });
        
        // 重置会话的未读计数
        conversationStore.resetUnreadCount(userId, contactId);
        
        return true;
      }
      return false;
    } catch (err) {
      console.error('批量标记消息为已读失败:', err);
      return false;
    }
  }
  
  function clearMessages() {
    messages.value = [];
  }
  
  // 处理接收到的消息
  function handleReceivedMessage(message) {
    // 设置新消息
    newMessage.value = message;
    
    // 如果是当前会话的消息，添加到消息列表
    if ((currentUserId.value === message.receiverId && currentContactId.value === message.senderId) ||
        (currentUserId.value === message.senderId && currentContactId.value === message.receiverId)) {
      
      // 检查消息是否已存在
      const existingIndex = messages.value.findIndex(msg => 
        msg.id === message.id || msg.clientMessageId === message.clientMessageId
      );
      
      if (existingIndex === -1) {
        messages.value.push(message);
      } else {
        // 更新现有消息
        messages.value[existingIndex] = {
          ...messages.value[existingIndex],
          ...message,
          status: 'SENT'
        };
      }
      
      // 如果是接收到的消息，标记为已读
      if (message.senderId === currentContactId.value && message.receiverId === currentUserId.value) {
        markMessageAsRead(message.id, currentUserId.value);
      }
    }
    
    // 更新会话列表
    conversationStore.updateConversationWithMessage(message);
  }
  
  // 处理已读回执
  function handleReadReceipt(readReceipt) {
    if (!readReceipt || !readReceipt.messageIds) return;
    
    // 更新消息的已读状态
    readReceipt.messageIds.forEach(messageId => {
      const index = messages.value.findIndex(msg => msg.id === messageId);
      if (index !== -1) {
        messages.value[index].isRead = true;
      }
    });
  }
  
  // 初始化 WebSocket 消息处理
  function initWebSocketHandlers() {
    webSocketStore.onMessage((data) => {
      if (data.type === 'PRIVATE_CHAT') {
        handleReceivedMessage(data.content);
      } else if (data.type === 'READ_RECEIPT') {
        handleReadReceipt(data.content);
      }
    });
  }
  
  // 初始化
  initWebSocketHandlers();
  
  return {
    messages,
    loading,
    error,
    newMessage,
    hasMoreMessages,
    fetchMessages,
    loadMoreMessages,
    sendTextMessage,
    sendImageMessage,
    sendAudioMessage,
    sendVideoMessage,
    sendFileMessage,
    markMessageAsRead,
    markAllAsRead,
    clearMessages,
    handleReceivedMessage,
    handleReadReceipt
  };
});
