<script setup>
import { onMounted, onUnmounted, watch, ref, nextTick } from 'vue'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { useUserStore, useAuthStore } from './stores'
import { useNotificationStore } from './stores/notificationStore'
import { useRoute } from 'vue-router'
import Header from './components/Header.vue'
import SystemAnnouncement from './components/SystemAnnouncement.vue'
import LoginDialog from './components/LoginDialog.vue'
import Footer from './components/Footer.vue'
// 使用统一的WebSocket功能入口
import { initWebSocketClient, disconnect, initializeWebSocketService } from './utils/websocket/index'
// 导入在线状态服务
import { initOnlineStatusService } from '@/services/onlineStatusService'
// 导入全局消息处理器
import globalMessageHandler from './utils/globalMessageHandler'
// 导入日志工具
import logger from './utils/logger'

// 导入emoji-picker样式
// 注意：public目录中的文件在根路径提供，无需添加/public前缀
// import '../public/vue3-emoji-style.css' // 旧的导入方式
// 已在index.html中全局引入，此处无需重复导入

const userStore = useUserStore()
const authStore = useAuthStore()
const notificationStore = useNotificationStore()
const route = useRoute()

// 登录对话框相关
const loginDialogVisible = ref(false)
const defaultLoginTab = ref('login')

// 初始化用户状态
const initUserState = () => {
  const token = localStorage.getItem('token')
  const userInfoStr = localStorage.getItem('userInfo')

  if (token && userInfoStr) {
    try {
      const userInfo = JSON.parse(userInfoStr)
      userStore.setToken(token)
      userStore.setUser(userInfo)
      console.log('从本地存储恢复用户状态:', userInfo)
    } catch (error) {
      console.error('解析本地用户信息失败:', error)
      localStorage.removeItem('userInfo')
      localStorage.removeItem('token')
    }
  }
}

// 初始化通知状态
const initNotificationState = () => {
  if (userStore.isLoggedIn) {
    console.log('【通知优化】用户已登录，初始化通知状态')
    // 使用节流版本的方法获取未读通知数量
    notificationStore.fetchAllUnreadCounts()
  }
}

// 初始化在线状态监听
const initOnlineStatus = () => {
  const token = userStore.token
  if (token) {
    console.log('【通知调试】用户已登录，初始化WebSocket客户端')
    // 只有登录用户才初始化WebSocket
    try {
      // 初始化WebSocket客户端
      const wsClient = initWebSocketClient()

      // 添加WebSocket连接成功事件监听器
      window.addEventListener('websocket-connected', (event) => {
        console.log('【通知调试】WebSocket连接成功，触发全局刷新事件', event.detail)

        // 获取当前用户ID
        const userId = userStore.userInfo?.id
        if (userId) {
          console.log('【通知调试】当前用户ID:', userId)

          // 确保订阅通知主题
          setTimeout(() => {
            if (wsClient && typeof wsClient.subscribeAll === 'function') {
              console.log('【通知调试】尝试订阅所有主题...')
              wsClient.subscribeAll(userId)
                .then(success => {
                  if (success) {
                    console.log('【通知调试】所有主题订阅成功')
                  } else {
                    console.error('【通知调试】主题订阅失败')
                  }
                })
                .catch(err => console.error('【通知调试】订阅主题出错:', err))
            } else {
              console.warn('【通知调试】wsClient.subscribeAll 方法不可用')
            }
          }, 2000)
        }

        // 触发全局刷新联系人列表事件
        window.dispatchEvent(new CustomEvent('global-refresh-contacts', {
          detail: { timestamp: Date.now() }
        }))

        // 触发全局刷新通知事件
        window.dispatchEvent(new CustomEvent('global-refresh-notifications', {
          detail: { timestamp: Date.now() }
        }))
      })

      // 添加通知WebSocket连接成功事件监听器
      window.addEventListener('notification-websocket-connected', (event) => {
        console.log('【通知调试】通知WebSocket连接成功，触发全局刷新通知事件', event.detail)

        // 触发全局刷新通知事件
        window.dispatchEvent(new CustomEvent('global-refresh-notifications', {
          detail: { timestamp: Date.now() }
        }))
      })
    } catch (e) {
      console.error('【通知调试】初始化WebSocket客户端失败:', e)
    }
  }
}

// 添加键盘快捷键监听
const setupKeyboardShortcuts = () => {
  window.addEventListener('keydown', (event) => {
    // 键盘快捷键处理
  })
}

onMounted(() => {
  // 先初始化用户状态
  initUserState()

  // 初始化通知状态
  initNotificationState()

  // 初始化全局消息处理器
  globalMessageHandler.init()
  logger.info('App.vue: 全局消息处理器已初始化')

  // 设置键盘快捷键
  setupKeyboardShortcuts()
  logger.info('App.vue: 键盘快捷键已设置，按Alt+L可切换日志查看器')

  // 延迟初始化在线状态监听和WebSocket服务，确保用户状态已完全恢复
  setTimeout(() => {
    logger.info('App.vue: 延迟初始化WebSocket服务和在线状态监听')

    // 先初始化WebSocket服务
    if (userStore.isLoggedIn) {
      logger.info('App.vue: 用户已登录，初始化WebSocket服务')
      initializeWebSocketService().then(connected => {
        logger.info(`App.vue: WebSocket服务初始化结果: ${connected ? '成功' : '失败'}`)

        // 初始化在线状态服务
        if (connected) {
          logger.info('App.vue: 初始化在线状态服务')
          initOnlineStatusService()
        } else {
          logger.warn('App.vue: WebSocket未连接，在线状态服务可能无法正常工作')
          // 仍然初始化服务，以便在WebSocket连接成功后自动工作
          setTimeout(() => {
            initOnlineStatusService()
          }, 2000)
        }
      }).catch(error => {
        logger.error('App.vue: WebSocket服务初始化失败:', error)
      })
    } else {
      logger.info('App.vue: 用户未登录，跳过WebSocket服务初始化')
    }

    // 初始化在线状态监听
    initOnlineStatus()
  }, 1000)

  // 检查是否需要显示登录对话框
  nextTick(() => {
    checkNeedLogin()
  })

  // 注册全局事件，用于接收WebSocket通知
  window.addEventListener('websocket-notification', (event) => {
    if (event.detail) {
      // 使用Store处理新通知
      notificationStore.handleNewNotification(event.detail)
    }
  })
})

// 检查是否需要显示登录对话框
const checkNeedLogin = () => {
  const needLogin = sessionStorage.getItem('needLogin')
  const loginRedirect = sessionStorage.getItem('loginRedirect')

  if (needLogin === 'true') {
    // 清除会话存储中的标记
    sessionStorage.removeItem('needLogin')

    // 显示登录对话框
    authStore.showLoginDialog({
      tab: 'login',
      onSuccess: (userInfo) => {
        // 登录成功后，如果有重定向路径，则跳转
        if (loginRedirect) {
          sessionStorage.removeItem('loginRedirect')
          window.location.href = loginRedirect
        }

        // 登录成功后初始化通知状态
        initNotificationState()
      }
    })
  }
}

// 监听用户登录状态变化
watch(() => userStore.isLoggedIn, (newValue) => {
  if (newValue) {
    console.log('用户登录状态变更为已登录')

    // 初始化WebSocket服务
    logger.info('用户登录状态变更，初始化WebSocket服务')
    initializeWebSocketService().then(connected => {
      logger.info(`WebSocket服务初始化结果: ${connected ? '成功' : '失败'}`)

      // 初始化在线状态服务
      if (connected) {
        initOnlineStatusService()
      }
    }).catch(error => {
      logger.error('WebSocket服务初始化失败:', error)
    })

    // 初始化在线状态监听
    initOnlineStatus()

    // 登录状态变更为已登录时，初始化通知状态
    initNotificationState()
  } else {
    console.log('用户已登出')
    disconnect()
  }
})

// 在组件卸载时断开连接和移除事件监听
onUnmounted(() => {
  disconnect()

  // 移除全局WebSocket通知事件监听
  window.removeEventListener('websocket-notification', (event) => {
    if (event.detail) {
      notificationStore.handleNewNotification(event.detail)
    }
  })
})
</script>

<template>
  <el-config-provider :locale="zhCn">
    <div class="app">
      <!-- 始终显示Header和系统公告 -->
      <Header />
      <SystemAnnouncement />

      <!-- 主内容区域 -->
      <main class="main-content">
        <router-view></router-view>
      </main>

      <!-- 页脚 -->
      <Footer />

      <!-- 全局登录对话框 -->
      <LoginDialog
        v-model:visible="authStore.loginDialogVisible"
        :default-tab="authStore.defaultLoginTab"
        @login-success="authStore.handleLoginSuccess"
        @close="authStore.handleLoginCancel"
      />
    </div>
  </el-config-provider>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  color: #333;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 确保主内容区域能够伸展，推动页脚到底部 */
.main-content {
  flex: 1;
  margin-top: 70px; /* 更新为新的Header高度 */
  padding: 0;
  position: relative;
  z-index: 1;
  min-height: calc(100vh - 70px); /* 确保最小高度，减去Header高度 */
}

/* 当公告栏显示时，为内容区域添加额外的上边距 */
body.has-announcement .main-content {
  margin-top: 110px; /* 新的Header高度 + 公告栏高度 */
  min-height: calc(100vh - 110px); /* 确保最小高度，减去Header和公告栏高度 */
}

#app {
  width: 100%;
  height: 100vh;
}
</style>
