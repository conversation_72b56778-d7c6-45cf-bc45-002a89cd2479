@startuml 校园失物招领系统用例图

' 设置全局样式 - 更接近标准示例图
skinparam backgroundColor white
skinparam defaultFontName SimSun
skinparam defaultFontSize 12
skinparam usecase {
  BackgroundColor white
  BorderColor black
  ArrowColor black
  FontName SimSun
  FontSize 12
  shadowing false
  StereotypeFontSize 10
}
skinparam actor {
  BackgroundColor white
  BorderColor black
  FontName SimSun
  FontSize 12
  shadowing false
}
skinparam rectangle {
  FontName SimSun
  FontSize 12
  shadowing false
  BorderColor black
}
skinparam defaultTextAlignment center
skinparam linetype polyline

' 定义布局方向
left to right direction

' 左侧角色
actor "普通用户" as User

' 系统边界
rectangle "校园失物招领系统" {
  ' 用例 - 简洁命名，两列排列
  usecase "注册" as Register
  usecase "登录" as Login
  usecase "发布失物信息" as PostLost
  usecase "查询匹配结果" as QueryMatch
}

' 右侧角色
actor "管理员" as Admin
actor "超级管理员" as SuperAdmin

' 普通用户关系
User --> Register
User --> Login
User --> PostLost
User --> QueryMatch

' 管理员关系
Admin --> Login

' 超级管理员继承管理员（垂直关系）
SuperAdmin -up-|> Admin

' 包含关系
PostLost ..> QueryMatch : <<include>>

@enduml
