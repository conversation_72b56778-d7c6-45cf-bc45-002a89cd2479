<template>
  <div class="admin-statistics">
    <el-card class="statistics-card">
      <template #header>
        <div class="card-header">
          <span>活跃用户统计</span>
          <el-button type="primary" size="small" @click="refreshStatistics">刷新数据</el-button>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ activeUsers.daily }}</div>
            <div class="stat-label">日活跃用户 (DAU)</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ activeUsers.weekly }}</div>
            <div class="stat-label">周活跃用户 (WAU)</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ activeUsers.monthly }}</div>
            <div class="stat-label">月活跃用户 (MAU)</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    
    <el-card class="statistics-card">
      <template #header>
        <div class="card-header">
          <span>统计数据趋势</span>
          <el-select v-model="selectedTrendType" placeholder="选择统计类型" @change="fetchTrendData">
            <el-option v-for="item in trendTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </template>
      
      <div class="chart-container" ref="chartContainer"></div>
    </el-card>
    
    <el-card class="statistics-card">
      <template #header>
        <div class="card-header">
          <span>系统管理</span>
        </div>
      </template>
      
      <el-button type="primary" @click="updateCache">更新统计缓存</el-button>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { getActiveUsersStatistics, getStatisticsTrend, updateStatisticsCache } from '@/api/statistics'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 活跃用户数据
const activeUsers = ref({
  daily: 0,
  weekly: 0,
  monthly: 0
})

// 趋势图类型
const trendTypes = [
  { label: '日活跃用户', value: 'DAU' },
  { label: '周活跃用户', value: 'WAU' },
  { label: '月活跃用户', value: 'MAU' },
  { label: '总物品数', value: 'TOTAL_ITEMS' },
  { label: '已归还物品数', value: 'RETURNED_ITEMS' },
  { label: '成功匹配数', value: 'SUCCESS_MATCHES' }
]
const selectedTrendType = ref('DAU')

// 图表相关
const chartContainer = ref(null)
let chart = null

// 获取活跃用户统计数据
const fetchActiveUsers = async () => {
  try {
    const res = await getActiveUsersStatistics()
    if (res.code === 200 && res.data) {
      activeUsers.value = res.data
      console.log('获取到的活跃用户统计数据:', activeUsers.value)
    } else {
      ElMessage.warning(res.message || '获取活跃用户统计数据失败')
    }
  } catch (error) {
    console.error('获取活跃用户统计数据失败:', error)
    ElMessage.error('获取活跃用户统计数据失败，请稍后重试')
  }
}

// 获取趋势数据
const fetchTrendData = async () => {
  try {
    const res = await getStatisticsTrend(selectedTrendType.value, 30)
    if (res.code === 200 && res.data) {
      renderChart(res.data)
      console.log('获取到的趋势数据:', res.data)
    } else {
      ElMessage.warning(res.message || '获取趋势数据失败')
    }
  } catch (error) {
    console.error('获取趋势数据失败:', error)
    ElMessage.error('获取趋势数据失败，请稍后重试')
  }
}

// 渲染图表
const renderChart = (data) => {
  if (!chart) {
    chart = echarts.init(chartContainer.value)
  }
  
  const dates = data.data.map(item => item.date)
  const values = data.data.map(item => item.value)
  
  const option = {
    title: {
      text: getTrendTypeLabel(data.type) + '趋势图',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: getTrendTypeLabel(data.type),
        type: 'line',
        data: values,
        smooth: true,
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      }
    ]
  }
  
  chart.setOption(option)
}

// 获取趋势类型标签
const getTrendTypeLabel = (type) => {
  const found = trendTypes.find(item => item.value === type)
  return found ? found.label : type
}

// 刷新统计数据
const refreshStatistics = async () => {
  await fetchActiveUsers()
  await fetchTrendData()
  ElMessage.success('统计数据已刷新')
}

// 更新统计缓存
const updateCache = async () => {
  try {
    const res = await updateStatisticsCache()
    if (res.code === 200) {
      ElMessage.success('统计缓存更新成功')
      // 刷新数据
      refreshStatistics()
    } else {
      ElMessage.warning(res.message || '更新统计缓存失败')
    }
  } catch (error) {
    console.error('更新统计缓存失败:', error)
    ElMessage.error('更新统计缓存失败，请稍后重试')
  }
}

// 窗口大小变化时重绘图表
const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

// 生命周期钩子
onMounted(async () => {
  // 获取活跃用户统计数据
  await fetchActiveUsers()
  
  // 获取趋势数据
  await fetchTrendData()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)
  
  // 销毁图表
  if (chart) {
    chart.dispose()
    chart = null
  }
})
</script>

<style scoped>
.admin-statistics {
  padding: 20px;
}

.statistics-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--el-color-primary);
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.chart-container {
  height: 400px;
  width: 100%;
}
</style>
