package com.tomato.lostfoundsystem.common;

import com.tomato.lostfoundsystem.entity.LostItem;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用返回结果类
 */
@Data
public class Result<T> {

    private int code;           // HTTP 状态码
    private String message;     // 操作消息
    private T data;             // 返回的数据

    // 成功返回方法，传入数据
    public static <T> Result<T> success(T data) {
        Result<T> r = new Result<>();
        r.code = 200;  // 200表示请求成功
        r.message = "操作成功";
        r.data = data;
        return r;
    }

    // 成功返回方法，无数据
    public static <T> Result<T> success() {
        Result<T> r = new Result<>();
        r.code = 200;  // 200表示请求成功
        r.message = "操作成功";
        return r;
    }

    // 失败返回方法，传入错误消息
    public static <T> Result<T> fail(String message) {
        Result<T> r = new Result<>();
        r.code = 400;  // 400表示请求错误
        r.message = message;
        return r;
    }

    // 成功返回方法，传入消息和数据
    public static <T> Result<T> success(String message, T data) {
        Result<T> result = new Result<>();
        result.code = 200;
        result.message = message;
        result.data = data;
        return result;
    }

    // 错误返回方法，传入状态码和消息
    public static <T> Result<T> error(int code, String message) {
        Result<T> result = new Result<>();
        result.code = code;  // 传入具体的错误码
        result.message = message;
        return result;
    }

    // 错误返回方法，只传入消息，使用默认错误码400
    public static <T> Result<T> error(String message) {
        return error(400, message);
    }

    public static <T> Result<Map<String, Object>> success(List<T> data, long total, int totalPages) {
        Result<Map<String, Object>> result = new Result<>();
        result.code = 200; // 设置成功的状态码
        result.message = "查询成功";

        // 组织分页数据
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("data", data);
        responseData.put("total", total); // 总记录数
        responseData.put("totalPages", totalPages); // 总页数

        result.data = responseData;
        return result;
    }

}
