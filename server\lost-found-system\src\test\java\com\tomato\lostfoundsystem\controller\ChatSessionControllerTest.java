package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.service.ConversationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ChatSessionControllerTest {

    @InjectMocks
    private ChatSessionController chatSessionController;

    @Mock
    private ConversationService conversationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testUpdateStatus_Success() {
        // 准备测试数据
        Long conversationId = 100L;
        String status = "ARCHIVED";

        // 模拟 conversationService.updateStatus 方法
        doNothing().when(conversationService).updateStatus(conversationId, status);

        // 调用被测试方法
        ResponseEntity<Result<Void>> response = chatSessionController.updateStatus(conversationId, status);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(200, response.getBody().getCode());
        assertEquals("success", response.getBody().getMessage());

        // 验证 conversationService.updateStatus 被调用
        verify(conversationService).updateStatus(conversationId, status);
    }

    @Test
    void testUpdateStatus_Exception() {
        // 准备测试数据
        Long conversationId = 100L;
        String status = "ARCHIVED";

        // 模拟 conversationService.updateStatus 方法抛出异常
        doThrow(new RuntimeException("Test exception")).when(conversationService).updateStatus(conversationId, status);

        // 调用被测试方法
        ResponseEntity<Result<Void>> response = chatSessionController.updateStatus(conversationId, status);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(500, response.getBody().getCode());
        assertEquals("Test exception", response.getBody().getMessage());

        // 验证 conversationService.updateStatus 被调用
        verify(conversationService).updateStatus(conversationId, status);
    }

    @Test
    void testUpdatePinned_Success() {
        // 准备测试数据
        Long conversationId = 100L;
        Boolean isPinned = true;

        // 模拟 conversationService.updatePinned 方法
        doNothing().when(conversationService).updatePinned(conversationId, isPinned);

        // 调用被测试方法
        ResponseEntity<Result<Void>> response = chatSessionController.updatePinned(conversationId, isPinned);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(200, response.getBody().getCode());
        assertEquals("success", response.getBody().getMessage());

        // 验证 conversationService.updatePinned 被调用
        verify(conversationService).updatePinned(conversationId, isPinned);
    }

    @Test
    void testUpdatePinned_Exception() {
        // 准备测试数据
        Long conversationId = 100L;
        Boolean isPinned = true;

        // 模拟 conversationService.updatePinned 方法抛出异常
        doThrow(new RuntimeException("Test exception")).when(conversationService).updatePinned(conversationId, isPinned);

        // 调用被测试方法
        ResponseEntity<Result<Void>> response = chatSessionController.updatePinned(conversationId, isPinned);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(500, response.getBody().getCode());
        assertEquals("Test exception", response.getBody().getMessage());

        // 验证 conversationService.updatePinned 被调用
        verify(conversationService).updatePinned(conversationId, isPinned);
    }

    @Test
    void testUpdateMuted_Success() {
        // 准备测试数据
        Long conversationId = 100L;
        Boolean isMuted = true;

        // 模拟 conversationService.updateMuted 方法
        doNothing().when(conversationService).updateMuted(conversationId, isMuted);

        // 调用被测试方法
        ResponseEntity<Result<Void>> response = chatSessionController.updateMuted(conversationId, isMuted);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(200, response.getBody().getCode());
        assertEquals("success", response.getBody().getMessage());

        // 验证 conversationService.updateMuted 被调用
        verify(conversationService).updateMuted(conversationId, isMuted);
    }

    @Test
    void testUpdateMuted_Exception() {
        // 准备测试数据
        Long conversationId = 100L;
        Boolean isMuted = true;

        // 模拟 conversationService.updateMuted 方法抛出异常
        doThrow(new RuntimeException("Test exception")).when(conversationService).updateMuted(conversationId, isMuted);

        // 调用被测试方法
        ResponseEntity<Result<Void>> response = chatSessionController.updateMuted(conversationId, isMuted);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(500, response.getBody().getCode());
        assertEquals("Test exception", response.getBody().getMessage());

        // 验证 conversationService.updateMuted 被调用
        verify(conversationService).updateMuted(conversationId, isMuted);
    }

    @Test
    void testResetUnreadCount_Success() {
        // 准备测试数据
        Long userId = 1L;
        Long contactId = 2L;

        // 模拟 conversationService.resetUnreadCount 方法
        doNothing().when(conversationService).resetUnreadCount(userId, contactId);

        // 调用被测试方法
        ResponseEntity<Result<Void>> response = chatSessionController.resetUnreadCount(userId, contactId);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(200, response.getBody().getCode());
        assertEquals("success", response.getBody().getMessage());

        // 验证 conversationService.resetUnreadCount 被调用
        verify(conversationService).resetUnreadCount(userId, contactId);
    }

    @Test
    void testResetUnreadCount_Exception() {
        // 准备测试数据
        Long userId = 1L;
        Long contactId = 2L;

        // 模拟 conversationService.resetUnreadCount 方法抛出异常
        doThrow(new RuntimeException("Test exception")).when(conversationService).resetUnreadCount(userId, contactId);

        // 调用被测试方法
        ResponseEntity<Result<Void>> response = chatSessionController.resetUnreadCount(userId, contactId);

        // 验证结果
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(500, response.getBody().getCode());
        assertEquals("Test exception", response.getBody().getMessage());

        // 验证 conversationService.resetUnreadCount 被调用
        verify(conversationService).resetUnreadCount(userId, contactId);
    }
}
