package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.MatchHistory;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 匹配历史Mapper接口
 */
@Mapper
public interface MatchHistoryMapper {

    /**
     * 插入匹配历史记录
     *
     * @param matchHistory 匹配历史对象
     * @return 影响的行数
     */
    @Insert("INSERT INTO match_history(user_id, query_type, query_image_url, query_text, item_type, result_count) " +
            "VALUES(#{userId}, #{queryType}, #{queryImageUrl}, #{queryText}, #{itemType}, #{resultCount})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertMatchHistory(MatchHistory matchHistory);

    /**
     * 根据用户ID查询匹配历史记录
     *
     * @param userId 用户ID
     * @return 匹配历史列表
     */
    @Select("SELECT * FROM match_history WHERE user_id = #{userId} ORDER BY created_at DESC LIMIT 20")
    List<MatchHistory> getMatchHistoryByUserId(@Param("userId") Long userId);

    /**
     * 根据ID查询匹配历史记录
     *
     * @param id 匹配历史ID
     * @return 匹配历史对象
     */
    @Select("SELECT * FROM match_history WHERE id = #{id}")
    MatchHistory getMatchHistoryById(@Param("id") Long id);

    /**
     * 更新匹配结果数量
     *
     * @param id 匹配历史ID
     * @param resultCount 结果数量
     * @return 影响的行数
     */
    @Update("UPDATE match_history SET result_count = #{resultCount} WHERE id = #{id}")
    int updateResultCount(@Param("id") Long id, @Param("resultCount") int resultCount);

    /**
     * 删除匹配历史记录
     *
     * @param id 匹配历史ID
     * @return 影响的行数
     */
    @Delete("DELETE FROM match_history WHERE id = #{id}")
    int deleteMatchHistory(@Param("id") Long id);
}
