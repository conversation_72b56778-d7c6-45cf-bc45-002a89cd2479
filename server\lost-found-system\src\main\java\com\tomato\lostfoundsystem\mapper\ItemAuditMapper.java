package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.ItemAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ItemAuditMapper {
    // 插入审核记录
    int insertLostItemAudit(ItemAudit itemAudit);

    int insertFoundItemAudit(ItemAudit itemAudit);

    // 根据物品ID查询审核记录
    List<ItemAudit> selectByItemId(Long itemId);

    // 根据ID查询单条审核记录
    ItemAudit selectById(Long id);

    // 查询所有审核记录
    List<ItemAudit> selectAll();

    // 更新审核记录
    int updateAuditRecord(ItemAudit itemAudit);

    // 删除审核记录
    int deleteAuditRecord(Long id);

    /**
     * 获取失物项目的最新审核记录
     * @param itemId 物品ID
     * @return 审核记录
     */
    @Select("SELECT * FROM lost_item_audit WHERE item_id = #{itemId} ORDER BY audit_time DESC LIMIT 1")
    ItemAudit getLatestLostItemAudit(Long itemId);

    /**
     * 获取拾物项目的最新审核记录
     * @param itemId 物品ID
     * @return 审核记录
     */
    @Select("SELECT * FROM found_item_audit WHERE item_id = #{itemId} ORDER BY audit_time DESC LIMIT 1")
    ItemAudit getLatestFoundItemAudit(Long itemId);
}
