package com.tomato.lostfoundsystem.service.Impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.FoundItemDTO;
import com.tomato.lostfoundsystem.dto.FoundItemDetailDTO;
import com.tomato.lostfoundsystem.entity.FoundItem;
import com.tomato.lostfoundsystem.entity.ItemImage;
import com.tomato.lostfoundsystem.entity.LostItem;
import com.tomato.lostfoundsystem.enums.AuditStatusEnum;
import com.tomato.lostfoundsystem.mapper.FoundItemMapper;
import com.tomato.lostfoundsystem.service.AsyncTaskService;
import com.tomato.lostfoundsystem.service.FoundItemService;
import com.tomato.lostfoundsystem.service.ItemImageService;
import com.tomato.lostfoundsystem.utils.AliyunOSSUtil;
import com.tomato.lostfoundsystem.utils.DateUtils;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class FoundItemServiceImpl implements FoundItemService {

    private final FoundItemMapper foundItemMapper;
    private final AliyunOSSUtil aliyunOSSUtil;

    private final SecurityUtil securityUtil;

    private final DateUtils dateUtils;

    @Autowired
    private AsyncTaskService asyncTaskService;

    @Autowired
    private ItemImageService itemImageService;

    public FoundItemServiceImpl(FoundItemMapper foundItemMapper, AliyunOSSUtil aliyunOSSUtil, SecurityUtil securityUtil, DateUtils dateUtils) {
        this.foundItemMapper = foundItemMapper;
        this.aliyunOSSUtil = aliyunOSSUtil;
        this.securityUtil = securityUtil;
        this.dateUtils = dateUtils;
    }

    @Override
    public Result<Object> publishFoundItem(FoundItemDTO dto) {
        try {
            // 验证是否有图片上传
            if ((dto.getImage() == null || dto.getImage().isEmpty()) &&
                (dto.getImages() == null || dto.getImages().isEmpty())) {
                log.warn("用户未上传任何图片");
                return Result.fail("请至少上传一张拾物照片");
            }

            // 1. 统一处理图片上传 - 将所有图片合并到一个列表中处理
            List<MultipartFile> allImages = new ArrayList<>();
            List<String> uploadedImageUrls = new ArrayList<>(); // 存储所有上传后的图片URL

            // 如果有单独的主图，添加到列表开头
            if (dto.getImage() != null && !dto.getImage().isEmpty()) {
                allImages.add(dto.getImage());
            }

            // 如果有多图片，添加到列表中
            if (dto.getImages() != null && !dto.getImages().isEmpty()) {
                allImages.addAll(dto.getImages());
            }

            // 确定主图索引
            int mainIndex = 0; // 默认使用第一张图片作为主图

            // 如果指定了主图索引且使用的是多图上传，则使用指定的索引
            if (dto.getMainImageIndex() != null && dto.getImage() == null) {
                // 确保索引在有效范围内
                mainIndex = Math.min(dto.getMainImageIndex(), allImages.size() - 1);
                mainIndex = Math.max(0, mainIndex);
            }

            log.info("处理图片上传 - 总图片数: {}, 主图索引: {}", allImages.size(), mainIndex);

            // 先上传所有图片，避免重复上传
            try {
                for (int i = 0; i < allImages.size(); i++) {
                    MultipartFile image = allImages.get(i);
                    if (image != null && !image.isEmpty()) {
                        String imageUrl = AliyunOSSUtil.uploadFoundImage(image);
                        uploadedImageUrls.add(imageUrl);
                        log.info("图片{}上传成功，URL: {}", i, imageUrl);
                    } else {
                        uploadedImageUrls.add(null); // 保持索引一致
                    }
                }
            } catch (Exception e) {
                log.error("图片上传失败: {}", e.getMessage(), e);
                return Result.fail("图片上传失败，请重试");
            }

            // 获取主图URL
            String mainImageUrl = null;
            if (!uploadedImageUrls.isEmpty() && mainIndex < uploadedImageUrls.size()) {
                mainImageUrl = uploadedImageUrls.get(mainIndex);
                log.info("主图URL: {}", mainImageUrl);
            }

            // 如果主图上传失败，返回错误
            if (mainImageUrl == null) {
                return Result.fail("主图上传失败，请重试");
            }

            // 2. 创建并保存拾物信息
            FoundItem foundItem = new FoundItem();
            BeanUtils.copyProperties(dto, foundItem);
            foundItem.setFoundTime(dto.getFoundTime());
            foundItem.setFoundLocation(dto.getFoundLocation());
            foundItem.setImageUrl(mainImageUrl); // 设置主图URL
            foundItem.setStatus("UNCLAIMED"); // 初始状态
            foundItem.setAuditStatus(AuditStatusEnum.PENDING); // 设置初始审核状态
            foundItem.setCreatedAt(LocalDateTime.now());

            // 3. 保存到数据库
            int result = foundItemMapper.insertFoundItem(foundItem);
            if (result > 0) {
                // 记录生成的ID
                log.info("拾物信息保存成功，生成的ID: {}", foundItem.getId());

                if (foundItem.getId() == null) {
                    log.error("拾物ID为null，无法保存图片");
                    return Result.fail("系统错误，请稍后重试");
                }

                // 4. 保存所有图片到item_images表
                try {
                    log.info("开始保存图片到item_images表，拾物ID: {}, 图片数量: {}", foundItem.getId(), uploadedImageUrls.size());

                    // 使用已上传的图片URL列表，而不是重新上传图片
                    List<String> savedImageUrls = itemImageService.saveItemImageUrls(foundItem.getId(), "FOUND", uploadedImageUrls);
                    log.info("成功保存{}张图片到item_images表，URLs: {}", uploadedImageUrls.size(), savedImageUrls);

                    // 验证保存的图片数量是否正确
                    if (savedImageUrls.size() != uploadedImageUrls.size()) {
                        log.warn("保存的图片数量({})与上传的图片数量({})不一致", savedImageUrls.size(), uploadedImageUrls.size());
                    }
                } catch (Exception e) {
                    log.error("保存图片到item_images表失败: {}", e.getMessage(), e);
                    // 不影响主流程，继续执行
                }

                // 5. 如果审核状态为自动通过，则触发自动匹配
                if (AuditStatusEnum.APPROVED.equals(foundItem.getAuditStatus())) {
                    // 使用异步任务服务处理特征提取和智能匹配
                    asyncTaskService.processItemAutoMatchAsync(foundItem.getId(), foundItem.getUserId(), "FOUND");
                }

                return Result.success("提交成功，等待管理员审核", foundItem);
            } else {
                return Result.fail("发布失败，请重试");
            }
        } catch (Exception e) {
            log.error("发布拾物信息失败: {}", e.getMessage(), e);
            return Result.fail("发布失败，请稍后重试");
        }
    }

    @Override
    public Result<Object> searchFoundItems(String keyword, String foundLocation, String status, String timeRange, String timeFilterType, String startDate, String endDate, int page, int size) {
        // 获取时间范围的起始和结束时间
        LocalDateTime[] dateRange = dateUtils.getDateRange(timeRange, startDate, endDate);
        LocalDateTime startDateTime = dateRange[0];
        LocalDateTime endDateTime = dateRange[1];

        // 设置分页信息
        PageHelper.startPage(page, size);

        log.info("查询条件{},{},{},{},{},{}",keyword,foundLocation,status,startDateTime,endDateTime,timeFilterType);

        // 调用 Mapper 查询数据库
        List<FoundItem> foundItems = foundItemMapper.selectFoundItems(keyword, foundLocation, status, startDateTime, endDateTime, timeFilterType);

        // 包装分页信息
        PageInfo<FoundItem> pageInfo = new PageInfo<>(foundItems);

        // 如果没有找到任何拾物信息
        if (foundItems == null || foundItems.isEmpty()) {
            return Result.error("没有找到符合条件的拾物信息");
        }

        // 创建包含分页信息的响应数据
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("data", foundItems);
        responseData.put("total", pageInfo.getTotal());
        responseData.put("totalPages", pageInfo.getPages());

        return Result.success(responseData);
    }

    @Override
    public Result<Object> getFoundItemDetails(Long id) {
        // 查询拾物的详细信息
        FoundItemDetailDTO foundItemDetails = foundItemMapper.selectFoundItemDetailsById(id);

        // 如果拾物信息不存在，返回失败的结果
        if (foundItemDetails == null) {
            return Result.error("拾物信息不存在");
        }

        // 获取物品的所有图片
        try {
            List<ItemImage> itemImages = itemImageService.getItemImages(id, "FOUND");
            foundItemDetails.setItemImages(itemImages);

            // 构建所有图片URL列表
            List<String> allImageUrls = new ArrayList<>();
            String mainImageUrl = foundItemDetails.getImageUrl();

            // 从item_images表获取所有图片URL
            if (itemImages != null && !itemImages.isEmpty()) {
                // 按照sortOrder排序
                itemImages.sort(Comparator.comparing(ItemImage::getSortOrder));

                // 将所有图片URL添加到列表中
                for (ItemImage image : itemImages) {
                    String imageUrl = image.getImageUrl();
                    if (imageUrl != null && !imageUrl.isEmpty()) {
                        allImageUrls.add(imageUrl);
                    }
                }
            } else if (mainImageUrl != null && !mainImageUrl.isEmpty()) {
                // 如果没有额外图片，但有主图，则添加主图
                allImageUrls.add(mainImageUrl);
            }

            log.info("构建图片URL列表 - 主图: {}, 总图片数: {}", mainImageUrl, allImageUrls.size());
            foundItemDetails.setImageUrls(allImageUrls);
        } catch (Exception e) {
            log.error("获取拾物图片失败: {}", e.getMessage(), e);
            // 不影响主流程，继续返回基本信息
        }

        // 返回成功的结果，包含拾物详情
        return Result.success(foundItemDetails);
    }

    @Override
    public FoundItem getFoundItemById(Long id) {
        return foundItemMapper.selectById(id);
    }


    @Override
    public Result<Object> updateFoundItem(Long id, FoundItemDTO foundItemDTO) {
        try {
            // 获取当前登录用户的ID
            Long userId = securityUtil.getCurrentUserId();
            if (userId == null) {
                return Result.fail("用户未登录");
            }

            // 获取拾物信息
            FoundItem foundItem = foundItemMapper.selectById(id);
            if (foundItem == null) {
                return Result.fail("拾物信息不存在");
            }

            // 检查当前用户是否是该拾物信息的发布者
            if (!foundItem.getUserId().equals(userId)) {
                return Result.fail("无权限修改该拾物信息");
            }

            // 1. 统一处理图片上传 - 将所有图片合并到一个列表中处理
            List<MultipartFile> allImages = new ArrayList<>();

            // 如果有单独的主图，添加到列表开头
            if (foundItemDTO.getImage() != null && !foundItemDTO.getImage().isEmpty()) {
                allImages.add(foundItemDTO.getImage());
            }

            // 如果有多图片，添加到列表中
            if (foundItemDTO.getImages() != null && !foundItemDTO.getImages().isEmpty()) {
                allImages.addAll(foundItemDTO.getImages());
            }

            // 处理主图
            String imageUrl = foundItem.getImageUrl();  // 默认保持原主图
            List<String> uploadedImageUrls = new ArrayList<>(); // 存储所有上传后的图片URL

            if (!allImages.isEmpty()) {
                // 确定主图索引
                int mainIndex = 0; // 默认使用第一张图片作为主图

                // 如果指定了主图索引且使用的是多图上传，则使用指定的索引
                if (foundItemDTO.getMainImageIndex() != null && foundItemDTO.getImage() == null) {
                    // 确保索引在有效范围内
                    mainIndex = Math.min(foundItemDTO.getMainImageIndex(), allImages.size() - 1);
                    mainIndex = Math.max(0, mainIndex);
                }

                log.info("更新图片 - 总图片数: {}, 主图索引: {}", allImages.size(), mainIndex);

                // 先上传所有图片，避免重复上传
                try {
                    for (int i = 0; i < allImages.size(); i++) {
                        MultipartFile image = allImages.get(i);
                        if (image != null && !image.isEmpty()) {
                            String url = AliyunOSSUtil.uploadFoundImage(image);
                            uploadedImageUrls.add(url);
                            log.info("图片{}上传成功，URL: {}", i, url);
                        } else {
                            uploadedImageUrls.add(null); // 保持索引一致
                        }
                    }
                } catch (Exception e) {
                    log.error("图片上传失败: {}", e.getMessage(), e);
                    return Result.fail("图片上传失败，请重试");
                }

                // 获取主图URL
                if (!uploadedImageUrls.isEmpty() && mainIndex < uploadedImageUrls.size()) {
                    imageUrl = uploadedImageUrls.get(mainIndex);
                    log.info("新主图URL: {}", imageUrl);
                }
            }

            // 2. 更新拾物基本信息
            foundItem.setItemName(foundItemDTO.getItemName());
            foundItem.setDescription(foundItemDTO.getDescription());
            foundItem.setFoundTime(foundItemDTO.getFoundTime());
            foundItem.setFoundLocation(foundItemDTO.getFoundLocation());

            // 如果有新主图，更新主图URL并删除旧主图
            if (imageUrl != null && !imageUrl.equals(foundItem.getImageUrl()) && foundItem.getImageUrl() != null) {
                try {
                    String oldObjectName = AliyunOSSUtil.extractObjectNameFromUrl(foundItem.getImageUrl());
                    if (oldObjectName != null) {
                        boolean deleted = AliyunOSSUtil.deleteObject(oldObjectName);
                        log.info("删除旧主图 {}: {}", oldObjectName, deleted ? "成功" : "失败");
                    }
                } catch (Exception e) {
                    log.error("删除旧主图失败: {}", e.getMessage(), e);
                    // 不影响主流程，继续执行
                }
                foundItem.setImageUrl(imageUrl);
            }

            // 3. 更新拾物信息
            int result = foundItemMapper.updateById(foundItem);
            if (result == 1) {
                // 4. 处理所有图片
                if (!allImages.isEmpty()) {
                    try {
                        // 获取原有的图片，并从OSS中删除
                        List<ItemImage> oldImages = itemImageService.getItemImages(id, "FOUND");
                        for (ItemImage oldImage : oldImages) {
                            try {
                                if (oldImage.getImageUrl() != null && !oldImage.getImageUrl().isEmpty()) {
                                    String oldObjectName = AliyunOSSUtil.extractObjectNameFromUrl(oldImage.getImageUrl());
                                    if (oldObjectName != null) {
                                        boolean deleted = AliyunOSSUtil.deleteObject(oldObjectName);
                                        log.info("删除旧图片 {}: {}", oldObjectName, deleted ? "成功" : "失败");
                                    }
                                }
                            } catch (Exception e) {
                                log.error("删除旧图片失败: {}", e.getMessage(), e);
                                // 继续处理其他图片
                            }
                        }

                        // 从数据库中删除原有的图片记录
                        itemImageService.deleteItemImages(id, "FOUND");

                        // 使用已上传的图片URL列表，而不是重新上传图片
                        List<String> savedImageUrls = itemImageService.saveItemImageUrls(foundItem.getId(), "FOUND", uploadedImageUrls);
                        log.info("成功更新{}张图片到item_images表，URLs: {}", uploadedImageUrls.size(), savedImageUrls);

                        // 验证保存的图片数量是否正确
                        if (savedImageUrls.size() != uploadedImageUrls.size()) {
                            log.warn("保存的图片数量({})与上传的图片数量({})不一致", savedImageUrls.size(), uploadedImageUrls.size());
                        }
                    } catch (Exception e) {
                        log.error("更新图片失败: {}", e.getMessage(), e);
                        // 不影响主流程，继续执行
                    }
                } else if (foundItemDTO.isKeepExistingImages()) {
                    // 如果没有新上传的图片，但是设置了保留原有图片
                    log.info("保留原有图片，不进行图片更新操作");

                    // 如果主图索引变化了，需要更新主图
                    if (foundItemDTO.isMainImageChanged()) {
                        log.info("主图索引变化，需要更新主图");

                        // 获取所有图片
                        List<ItemImage> itemImages = itemImageService.getItemImages(id, "FOUND");

                        // 检查主图索引是否有效
                        if (foundItemDTO.getMainImageIndex() != null &&
                            foundItemDTO.getMainImageIndex() >= 0 &&
                            foundItemDTO.getMainImageIndex() < itemImages.size()) {

                            // 获取新的主图URL
                            String newMainImageUrl = itemImages.get(foundItemDTO.getMainImageIndex()).getImageUrl();

                            // 更新主图URL
                            foundItem.setImageUrl(newMainImageUrl);
                            foundItemMapper.updateById(foundItem);

                            log.info("成功更新主图URL: {}", newMainImageUrl);
                        } else {
                            log.warn("主图索引无效: {}, 图片数量: {}", foundItemDTO.getMainImageIndex(), itemImages.size());
                        }
                    }
                }

                // 修改返回消息格式，与publishFoundItem保持一致
                return Result.success("提交成功，等待管理员审核", foundItem);
            } else {
                return Result.fail("更新失败，请重试");
            }
        } catch (Exception e) {
            log.error("更新拾物信息时发生错误: {}", e.getMessage(), e);
            return Result.fail("更新失败，请稍后重试");
        }
    }


    @Override
    public Result<Object> deleteFoundItem(Long id, Long userId) {
        try {
            // 查询拾物信息
            FoundItem foundItem = foundItemMapper.selectById(id);
            if (foundItem == null) {
                return Result.error("拾物信息不存在");
            }

            // 检查当前用户是否是该拾物的发布者
            if (!foundItem.getUserId().equals(userId)) {
                return Result.error("您无权删除此拾物信息");
            }

            // 收集所有需要删除的图片URL
            List<String> imageUrls = new ArrayList<>();

            // 添加主图片URL
            if (foundItem.getImageUrl() != null && !foundItem.getImageUrl().isEmpty()) {
                imageUrls.add(foundItem.getImageUrl());
            }

            // 获取并添加额外图片URL
            List<ItemImage> itemImages = itemImageService.getItemImages(id, "FOUND");
            for (ItemImage image : itemImages) {
                if (image.getImageUrl() != null && !image.getImageUrl().isEmpty()) {
                    imageUrls.add(image.getImageUrl());
                }
            }

            // 从OSS删除图片文件
            for (String url : imageUrls) {
                try {
                    String objectName = AliyunOSSUtil.extractObjectNameFromUrl(url);
                    if (objectName != null) {
                        boolean deleted = AliyunOSSUtil.deleteObject(objectName);
                        log.info("删除OSS图片 {}: {}", objectName, deleted ? "成功" : "失败");
                    }
                } catch (Exception e) {
                    log.error("删除OSS图片失败: {}", e.getMessage(), e);
                    // 继续处理其他图片，不影响主流程
                }
            }

            // 删除数据库中的图片记录
            try {
                itemImageService.deleteItemImages(id, "FOUND");
                log.info("已删除拾物ID={}的相关图片记录", id);
            } catch (Exception e) {
                log.error("删除拾物图片记录失败: {}", e.getMessage(), e);
                // 不影响主流程，继续执行
            }

            // 执行删除操作
            int result = foundItemMapper.deleteById(id);
            if (result > 0) {
                return Result.success("拾物信息删除成功");
            } else {
                return Result.error("删除失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("删除拾物信息时发生错误: {}", e.getMessage(), e);
            return Result.error("删除失败，请稍后重试");
        }
    }

    @Override
    public Result<Object> getMyFoundItems(Long userId) {
        try {
            // 查询该用户发布的所有拾物信息
            List<FoundItem> foundItems = foundItemMapper.findFoundItemsByUserId(userId);
            return Result.success("查询成功", foundItems);
        } catch (Exception e) {
            log.error("获取发布的拾物信息失败: {}", e.getMessage(), e);
            return Result.error("获取失败，请稍后重试");
        }
    }

    @Override
    public Result<Object> claimFoundItem(Long id, Long userId) {
        try {
            // 1. 查询拾物信息
            FoundItem foundItem = foundItemMapper.selectById(id);
            if (foundItem == null) {
                return Result.error("拾物信息不存在");
            }

            // 2. 检查拾物状态
            if ("RETURNED".equals(foundItem.getStatus())) {
                return Result.error("该物品已被认领");
            }

            // 3. 检查审核状态
            if (!AuditStatusEnum.APPROVED.equals(foundItem.getAuditStatus())) {
                return Result.error("该物品尚未通过审核，无法认领");
            }

            // 4. 更新拾物状态为已归还
            foundItem.setStatus("RETURNED");
            int result = foundItemMapper.updateById(foundItem);

            if (result > 0) {
                // 5. 发送通知给拾物发布者
                try {
                    // 这里可以添加发送通知的代码
                    log.info("用户 {} 认领了拾物 {}", userId, id);
                } catch (Exception e) {
                    log.error("发送认领通知失败: {}", e.getMessage(), e);
                    // 通知发送失败不影响认领结果
                }

                return Result.success("认领成功");
            } else {
                return Result.error("认领失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("认领拾物失败: {}", e.getMessage(), e);
            return Result.error("认领失败，请稍后重试");
        }
    }

    @Override
    public Result<Object> updateFoundItemStatus(Long id, String status, Long userId) {
        try {
            // 1. 查询拾物信息
            FoundItem foundItem = foundItemMapper.selectById(id);
            if (foundItem == null) {
                return Result.error("拾物信息不存在");
            }

            // 2. 验证用户是否有权限更新该拾物信息
            if (!foundItem.getUserId().equals(userId)) {
                return Result.error("您无权更新此拾物信息");
            }

            // 3. 检查审核状态
            if (!AuditStatusEnum.APPROVED.equals(foundItem.getAuditStatus())) {
                return Result.error("该物品尚未通过审核，无法更新状态");
            }

            // 4. 校验状态变更是否合法 - 只允许从未认领(UNCLAIMED)变为已认领(RETURNED)
            if (!"RETURNED".equals(status)) {
                return Result.error("无效的状态值，只能标记为已认领");
            }

            if (!"UNCLAIMED".equals(foundItem.getStatus())) {
                return Result.error("该物品当前状态不是未认领，无法更新");
            }

            // 5. 更新拾物状态
            int result = foundItemMapper.updateFoundItemStatus(id, status);

            if (result > 0) {
                return Result.success("物品已标记为认领");
            } else {
                return Result.error("状态更新失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("更新拾物状态失败: {}", e.getMessage(), e);
            return Result.error("更新失败，请稍后重试");
        }
    }

}
