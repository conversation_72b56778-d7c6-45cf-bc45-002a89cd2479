package com.tomato.lostfoundsystem.dto;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tomato.lostfoundsystem.enums.NotificationType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Data
public class NotificationDTO {

    private Long id;               // 通知ID
    private Long userId;           // 用户ID
    private String title;          // 通知标题
    private String message;        // 通知内容
    private String status;         // 通知状态 ("UNREAD" 或 "READ")
    private LocalDateTime createdAt;  // 创建时间
    private LocalDateTime updatedAt;  // 更新时间
    private String role;           // 用户类型的选择

    // 新增字段
    private String type;           // 通知类型
    private Long relatedItemId;    // 关联物品ID
    private String relatedItemType; // 关联物品类型(LOST/FOUND)
    private String metadata;       // 额外元数据(JSON格式)
    private Long auditorId;        // 审核员ID(仅审核通知)

    // 便于前端使用的辅助字段，不存储在数据库中
    private Map<String, Object> metadataMap;

    /**
     * 获取解析后的元数据
     * @return 元数据Map
     */
    public Map<String, Object> getMetadataMap() {
        if (metadata == null || metadata.isEmpty()) {
            return new HashMap<>();
        }

        if (metadataMap != null) {
            return metadataMap;
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            metadataMap = mapper.readValue(metadata, new TypeReference<Map<String, Object>>() {});
            return metadataMap;
        } catch (JsonProcessingException e) {
            return new HashMap<>();
        }
    }

    /**
     * 获取通知类型枚举
     * @return 通知类型枚举
     */
    public NotificationType getNotificationType() {
        if (type != null) {
            return NotificationType.fromCode(type);
        }

        // 如果type为空，尝试从标题推断
        return NotificationType.inferFromTitle(title);
    }
}
