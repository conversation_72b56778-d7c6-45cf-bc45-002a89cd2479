package com.tomato.lostfoundsystem.service.Impl;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.entity.*;
import com.tomato.lostfoundsystem.mapper.*;
import com.tomato.lostfoundsystem.service.IntelligentMatchService;
import com.tomato.lostfoundsystem.service.MatchNotificationService;
import com.tomato.lostfoundsystem.service.SystemConfigService;
import com.tomato.lostfoundsystem.utils.AliyunOSSUtil;
import com.tomato.lostfoundsystem.utils.ClipFaissClientRefactored;
import com.tomato.lostfoundsystem.utils.KeywordExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.SocketTimeoutException;

import java.math.BigDecimal;
import java.util.*;

/**
 * 智能匹配服务实现类
 */
@Slf4j
@Service
public class IntelligentMatchServiceImpl implements IntelligentMatchService {

    @Autowired
    private ClipFaissClientRefactored clipFaissClient;

    @Autowired
    private ItemFeatureVectorMapper itemFeatureVectorMapper;

    @Autowired
    private MatchHistoryMapper matchHistoryMapper;

    @Autowired
    private MatchResultMapper matchResultMapper;

    @Autowired
    private LostItemMapper lostItemMapper;

    @Autowired
    private FoundItemMapper foundItemMapper;

    @Autowired
    private MatchNotificationService matchNotificationService;

    private static final int MAX_RESULTS = 20;
    private static final String VECTOR_VERSION = "v1";

    @Value("${match.notification.similarity-threshold:0.7}")
    private Float notificationSimilarityThreshold;

    @Value("${match.result.similarity-threshold:0.5}")
    private Float resultSimilarityThreshold;

    @Value("${match.notification.auto-notify:true}")
    private Boolean autoNotify;

    // 不同匹配类型的过滤阈值
    private Float textToTextThreshold;
    private Float imageToImageThreshold;
    private Float textToImageThreshold;
    private Float imageToTextThreshold;
    private Float combinedThreshold;

    @Autowired
    private SystemConfigService systemConfigService;

    // 使用动态配置，不再使用@Value注入
    private String getClipApiUrl() {
        return systemConfigService.getConfigValue("autodl.clip.api.url", "http://localhost:8000");
    }

    private boolean isServiceCheckEnabled() {
        String value = systemConfigService.getConfigValue("autodl.clip.service.check-enabled", "true");
        return Boolean.parseBoolean(value);
    }

    private int getConnectionTimeout() {
        String value = systemConfigService.getConfigValue("autodl.clip.service.connection-timeout", "3000");
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 3000; // 默认值
        }
    }

    /**
     * 初始化不同匹配类型的过滤阈值
     * 在每次匹配操作前调用，确保使用最新的配置值
     */
    private void initFilterThresholds() {
        // 从系统配置中获取各类型的阈值，如果不存在则使用默认值
        textToTextThreshold = parseFloat(
            systemConfigService.getConfigValue("match.filter.text-to-text-threshold", "0.60"),
            0.60f
        );

        imageToImageThreshold = parseFloat(
            systemConfigService.getConfigValue("match.filter.image-to-image-threshold", "0.55"),
            0.55f
        );

        textToImageThreshold = parseFloat(
            systemConfigService.getConfigValue("match.filter.text-to-image-threshold", "0.45"),
            0.45f
        );

        imageToTextThreshold = parseFloat(
            systemConfigService.getConfigValue("match.filter.image-to-text-threshold", "0.45"),
            0.45f
        );

        combinedThreshold = parseFloat(
            systemConfigService.getConfigValue("match.filter.combined-threshold", "0.50"),
            0.50f
        );

        log.info("【智能匹配】过滤阈值初始化完成: 文本-文本={}, 图像-图像={}, 文本-图像={}, 图像-文本={}, 综合={}",
                textToTextThreshold, imageToImageThreshold, textToImageThreshold, imageToTextThreshold, combinedThreshold);
    }

    /**
     * 解析浮点数，如果解析失败则返回默认值
     */
    private float parseFloat(String value, float defaultValue) {
        try {
            return Float.parseFloat(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 检查CLIP+FAISS服务是否可用
     *
     * @return 服务是否可用
     */
    private boolean isClipServiceAvailable() {
        if (!isServiceCheckEnabled()) {
            log.info("【智能匹配】CLIP+FAISS服务检查已禁用，默认认为服务可用");
            return true; // 如果禁用了服务检查，默认认为服务可用
        }

        try {
            String apiUrl = getClipApiUrl();
            log.info("【智能匹配】检查CLIP+FAISS服务是否可用: {}", apiUrl);

            // 首先尝试使用/health端点
            try {
                URL healthUrl = new URL(apiUrl + "/health");
                HttpURLConnection healthConnection = (HttpURLConnection) healthUrl.openConnection();
                healthConnection.setRequestMethod("GET");
                healthConnection.setConnectTimeout(getConnectionTimeout());
                healthConnection.setReadTimeout(getConnectionTimeout());

                int healthResponseCode = healthConnection.getResponseCode();
                if (healthResponseCode == 200) {
                    log.info("【智能匹配】CLIP+FAISS服务健康检查成功，服务可用");
                    return true;
                }
                log.warn("【智能匹配】CLIP+FAISS服务健康检查失败，响应码: {}", healthResponseCode);
            } catch (Exception e) {
                log.warn("【智能匹配】CLIP+FAISS服务健康检查异常: {}, 尝试根路径检查", e.getMessage());
            }

            // 如果健康检查失败，尝试根路径
            URL rootUrl = new URL(apiUrl);
            HttpURLConnection rootConnection = (HttpURLConnection) rootUrl.openConnection();
            rootConnection.setRequestMethod("GET");
            rootConnection.setConnectTimeout(getConnectionTimeout());
            rootConnection.setReadTimeout(getConnectionTimeout());

            int rootResponseCode = rootConnection.getResponseCode();
            boolean isAvailable = (rootResponseCode == 200);

            log.info("【智能匹配】CLIP+FAISS服务根路径检查结果: {}, 响应码: {}",
                    isAvailable ? "可用" : "不可用", rootResponseCode);

            return isAvailable;
        } catch (SocketTimeoutException e) {
            log.warn("【智能匹配】CLIP+FAISS服务连接超时: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.warn("【智能匹配】CLIP+FAISS服务检查异常: {}", e.getMessage());
            return false;
        }
    }

    @Override
    @Transactional
    public Result<Map<String, Object>> matchByImage(Long userId, MultipartFile imageFile, String itemType) {
        try {
            log.info("【智能匹配】开始图片匹配，用户ID: {}, 物品类型: {}", userId, itemType);

            // 这是用户自行搜索，不需要发送通知
            // 设置自动通知标志为false
            autoNotify = false;

            // 初始化过滤阈值
            initFilterThresholds();

            log.info("【智能匹配】过滤阈值已初始化: 文本-文本={}, 图像-图像={}, 文本-图像={}, 图像-文本={}, 综合={}",
                    textToTextThreshold, imageToImageThreshold, textToImageThreshold, imageToTextThreshold, combinedThreshold);

            log.info("【智能匹配】当前结果相似度阈值: {}, 通知相似度阈值: {}", resultSimilarityThreshold, notificationSimilarityThreshold);

            // 0. 检查服务是否可用
            if (!isClipServiceAvailable()) {
                return Result.fail("智能匹配服务暂时不可用，请稍后再试");
            }

            // 1. 上传图片到OSS
            String imageUrl = AliyunOSSUtil.uploadImage(imageFile);
            log.info("图片上传成功，URL: {}", imageUrl);

            // 2. 提取图像特征向量
            log.info("【智能匹配】开始提取图像特征向量");
            byte[] imageFeatures = clipFaissClient.extractImageFeatures(imageFile);
            if (imageFeatures == null) {
                log.error("【智能匹配】提取图像特征失败");
                return Result.fail("提取图像特征失败");
            }
            log.info("【智能匹配】成功提取图像特征，向量长度: {} 字节", imageFeatures.length);

            // 3. 创建匹配历史记录
            MatchHistory matchHistory = new MatchHistory();
            matchHistory.setUserId(userId);
            matchHistory.setQueryType("IMAGE");
            matchHistory.setQueryImageUrl(imageUrl);
            matchHistory.setItemType(itemType);
            matchHistory.setResultCount(0);
            matchHistoryMapper.insertMatchHistory(matchHistory);
            log.info("创建匹配历史记录，ID: {}", matchHistory.getId());

            // 4. 使用图像文件搜索相似物品
            String targetType = "LOST".equals(itemType) ? "FOUND" : "LOST";
            Map<String, Object> searchResults = clipFaissClient.searchByImageFile(imageFile, targetType, MAX_RESULTS);
            log.info("【智能匹配】图像搜索完成，结果: {}", searchResults != null ? "成功" : "失败");

            // 5. 处理搜索结果
            return processImageSearchResults(searchResults, matchHistory, targetType);
        } catch (Exception e) {
            log.error("图片匹配过程中发生异常", e);
            return Result.fail("匹配过程中发生错误: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Map<String, Object>> matchByText(Long userId, String description, String itemType) {
        try {
            log.info("【智能匹配】开始文本匹配，用户ID: {}, 物品类型: {}, 描述: {}", userId, itemType, description);

            // 这是用户自行搜索，不需要发送通知
            // 设置自动通知标志为false
            autoNotify = false;

            // 初始化过滤阈值
            initFilterThresholds();

            log.info("【智能匹配】过滤阈值已初始化: 文本-文本={}, 图像-图像={}, 文本-图像={}, 图像-文本={}, 综合={}",
                    textToTextThreshold, imageToImageThreshold, textToImageThreshold, imageToTextThreshold, combinedThreshold);

            log.info("【智能匹配】当前结果相似度阈值: {}, 通知相似度阈值: {}", resultSimilarityThreshold, notificationSimilarityThreshold);

            // 0. 检查服务是否可用
            if (!isClipServiceAvailable()) {
                log.error("【智能匹配】CLIP+FAISS服务不可用");
                return Result.fail("智能匹配服务暂时不可用，请稍后再试");
            }
            log.info("【智能匹配】CLIP+FAISS服务可用，开始提取文本特征");

            // 1. 使用HanLP提取关键词
            String keywords = KeywordExtractor.extractKeywords("", description);
            log.info("【智能匹配】从描述中提取的关键词: {}", keywords);

            // 2. 提取文本特征向量（使用提取的关键词）
            log.info("【特征向量验证】开始调用clipFaissClient.extractTextFeatures提取文本特征");
            byte[] textFeatures = clipFaissClient.extractTextFeatures(keywords);
            if (textFeatures == null) {
                log.error("【智能匹配】提取文本特征失败");
                return Result.fail("提取文本特征失败");
            }
            log.info("【特征向量验证】成功提取文本特征，向量长度: {} 字节", textFeatures.length);

            // 注意：搜索查询的特征向量不保存到数据库
            // 特征向量只在物品发布和审核通过时保存，用于构建索引

            log.info("【智能匹配】成功提取文本特征，向量长度: {}", textFeatures.length);

            // 2. 创建匹配历史记录 - 使用match_history表
            MatchHistory matchHistory = new MatchHistory();
            matchHistory.setUserId(userId);
            matchHistory.setQueryType("TEXT");
            matchHistory.setQueryText(description);
            matchHistory.setItemType(itemType);
            matchHistory.setResultCount(0);
            matchHistoryMapper.insertMatchHistory(matchHistory);
            log.info("【数据库表使用】向match_history表插入记录，ID: {}", matchHistory.getId());

            // 3. 搜索相似向量
            String targetType = "LOST".equals(itemType) ? "FOUND" : "LOST";
            Map<String, Object> searchResults = clipFaissClient.searchByTextVector(textFeatures, targetType, MAX_RESULTS);
            log.info("【智能匹配】文本搜索完成，结果: {}", searchResults != null ? "成功" : "失败");

            // 4. 处理搜索结果 - 使用专门的文本搜索结果处理方法
            return processTextSearchResults(searchResults, matchHistory, targetType);
        } catch (Exception e) {
            log.error("文本匹配过程中发生异常", e);
            return Result.fail("匹配过程中发生错误: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Map<String, Object>> matchByMixed(Long userId, MultipartFile imageFile, String description, String itemType) {
        try {
            log.info("【智能匹配】开始混合匹配，用户ID: {}, 物品类型: {}, 描述: {}", userId, itemType, description);

            // 这是用户自行搜索，不需要发送通知
            // 设置自动通知标志为false
            autoNotify = false;

            // 初始化过滤阈值
            initFilterThresholds();

            log.info("【智能匹配】过滤阈值已初始化: 文本-文本={}, 图像-图像={}, 文本-图像={}, 图像-文本={}, 综合={}",
                    textToTextThreshold, imageToImageThreshold, textToImageThreshold, imageToTextThreshold, combinedThreshold);

            log.info("【智能匹配】当前结果相似度阈值: {}, 通知相似度阈值: {}", resultSimilarityThreshold, notificationSimilarityThreshold);

            // 0. 检查服务是否可用
            if (!isClipServiceAvailable()) {
                return Result.fail("智能匹配服务暂时不可用，请稍后再试");
            }

            // 1. 上传图片到OSS
            String imageUrl = null;
            byte[] imageFeatures = null;

            if (imageFile != null && !imageFile.isEmpty()) {
                imageUrl = AliyunOSSUtil.uploadImage(imageFile);
                log.info("图片上传成功，URL: {}", imageUrl);

                // 2. 提取图像特征向量
                imageFeatures = clipFaissClient.extractImageFeatures(imageFile);
                if (imageFeatures == null) {
                    return Result.fail("提取图像特征失败");
                }
                log.info("成功提取图像特征，向量长度: {} 字节", imageFeatures.length);
            }

            // 3. 提取文本特征向量
            byte[] textFeatures = null;
            if (description != null && !description.trim().isEmpty()) {
                // 使用HanLP提取关键词
                String keywords = KeywordExtractor.extractKeywords("", description);
                log.info("【智能匹配】从描述中提取的关键词: {}", keywords);

                // 使用提取的关键词提取特征向量
                textFeatures = clipFaissClient.extractTextFeatures(keywords);
                if (textFeatures == null) {
                    return Result.fail("提取文本特征失败");
                }
                log.info("成功提取文本特征，向量长度: {} 字节", textFeatures.length);
            }

            // 如果两种特征都没有，返回错误
            if (imageFeatures == null && textFeatures == null) {
                return Result.fail("请提供图片或文本描述");
            }

            // 4. 创建匹配历史记录
            MatchHistory matchHistory = new MatchHistory();
            matchHistory.setUserId(userId);
            matchHistory.setQueryType("MIXED");
            matchHistory.setQueryImageUrl(imageUrl);
            matchHistory.setQueryText(description);
            matchHistory.setItemType(itemType);
            matchHistory.setResultCount(0);
            matchHistoryMapper.insertMatchHistory(matchHistory);
            log.info("创建匹配历史记录，ID: {}", matchHistory.getId());

            // 5. 搜索相似向量
            String targetType = "LOST".equals(itemType) ? "FOUND" : "LOST";
            Map<String, Object> searchResults;

            // 使用混合搜索
            searchResults = clipFaissClient.searchByMixedVectors(imageFeatures, textFeatures, targetType, MAX_RESULTS);
            log.info("混合搜索完成，结果: {}", searchResults != null ? "成功" : "失败");

            // 记录详细的搜索结果信息
            if (searchResults != null && !searchResults.isEmpty()) {
                log.info("混合搜索结果包含以下类型: {}", searchResults.keySet());

                // 记录各类型结果数量
                if (searchResults.containsKey("image_to_image")) {
                    List<?> results = (List<?>) searchResults.get("image_to_image");
                    log.info("图像-图像匹配结果数量: {}", results != null ? results.size() : 0);
                }

                if (searchResults.containsKey("image_to_text")) {
                    List<?> results = (List<?>) searchResults.get("image_to_text");
                    log.info("图像-文本匹配结果数量: {}", results != null ? results.size() : 0);
                }

                if (searchResults.containsKey("text_to_image")) {
                    List<?> results = (List<?>) searchResults.get("text_to_image");
                    log.info("文本-图像匹配结果数量: {}", results != null ? results.size() : 0);
                }

                if (searchResults.containsKey("text_to_text")) {
                    List<?> results = (List<?>) searchResults.get("text_to_text");
                    log.info("文本-文本匹配结果数量: {}", results != null ? results.size() : 0);
                }

                if (searchResults.containsKey("combined")) {
                    List<?> results = (List<?>) searchResults.get("combined");
                    log.info("综合匹配结果数量: {}", results != null ? results.size() : 0);
                }
            }

            // 6. 处理搜索结果 - 使用专门的混合搜索结果处理方法
            return processMixedSearchResults(searchResults, matchHistory, targetType);
        } catch (Exception e) {
            log.error("混合匹配过程中发生异常", e);
            return Result.fail("匹配过程中发生错误: " + e.getMessage());
        }
    }

    @Override
    public Result<List<MatchHistory>> getMatchHistory(Long userId) {
        try {
            log.info("获取用户匹配历史，用户ID: {}", userId);
            List<MatchHistory> historyList = matchHistoryMapper.getMatchHistoryByUserId(userId);
            return Result.success(historyList);
        } catch (Exception e) {
            log.error("获取用户匹配历史时发生异常", e);
            return Result.error("获取匹配历史失败: " + e.getMessage());
        }
    }

    @Override
    public Result<MatchHistory> getMatchHistoryDetail(Long matchHistoryId) {
        try {
            log.info("获取匹配历史详情，ID: {}", matchHistoryId);

            // 1. 获取匹配历史记录
            MatchHistory matchHistory = matchHistoryMapper.getMatchHistoryById(matchHistoryId);
            if (matchHistory == null) {
                return Result.error("匹配历史记录不存在");
            }

            // 2. 获取匹配结果
            List<MatchResult> results = matchResultMapper.getMatchResultsByHistoryId(matchHistoryId);

            // 3. 获取物品详情
            for (MatchResult result : results) {
                if ("LOST".equals(result.getItemType())) {
                    LostItem lostItem = lostItemMapper.selectById(result.getItemId());
                    result.setItemDetail(lostItem);
                } else {
                    FoundItem foundItem = foundItemMapper.selectById(result.getItemId());
                    result.setItemDetail(foundItem);
                }
            }

            matchHistory.setResults(results);
            return Result.success(matchHistory);
        } catch (Exception e) {
            log.error("获取匹配历史详情时发生异常", e);
            return Result.error("获取匹配历史详情失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<String> generateFeatureVectorsForAllItems() {
        try {
            log.info("【智能匹配】开始为所有物品生成特征向量");

            // 0. 检查服务是否可用
            if (!isClipServiceAvailable()) {
                return Result.error("智能匹配服务暂时不可用，请稍后再试");
            }

            // 1. 获取所有失物信息
            List<LostItem> lostItems = lostItemMapper.selectLostItems(null, null, null, null, null, null);
            int lostCount = 0;

            for (LostItem item : lostItems) {
                // 检查是否已有特征向量 - 使用item_feature_vectors表
                if (itemFeatureVectorMapper.checkFeatureVectorExists(item.getId(), "LOST") == 0) {
                    log.info("【数据库表使用】检查item_feature_vectors表中是否存在失物ID: {}的记录", item.getId());

                    // 使用HanLP提取关键词
                    String keywords = KeywordExtractor.extractKeywords(item.getItemName(), item.getDescription());
                    log.info("【智能匹配】失物提取的关键词: {}", keywords);

                    // 提取文本特征向量（使用提取的关键词）
                    byte[] textFeatures = clipFaissClient.extractTextFeatures(keywords);

                    if (textFeatures != null) {
                        log.info("【智能匹配】成功提取失物文本特征，物品ID: {}, 向量长度: {} 字节", item.getId(), textFeatures.length);

                        ItemFeatureVector vector = new ItemFeatureVector();
                        vector.setItemId(item.getId());
                        vector.setItemType("LOST");
                        vector.setTextVector(textFeatures);
                        vector.setVectorVersion(VECTOR_VERSION);

                        // 插入特征向量 - 使用item_feature_vectors表
                        itemFeatureVectorMapper.insertFeatureVector(vector);
                        log.info("【数据库表使用】向item_feature_vectors表插入失物特征向量，物品ID: {}", item.getId());

                        // 添加特征向量到索引
                        Long vectorId = clipFaissClient.addVectorToIndex(item.getId(), "LOST", textFeatures, "TEXT");
                        if (vectorId != null) {
                            log.info("【智能匹配】成功添加失物文本特征向量到索引，物品ID: {}, 向量ID: {}", item.getId(), vectorId);
                        } else {
                            log.error("【智能匹配】添加失物文本特征向量到索引失败，物品ID: {}", item.getId());
                        }

                        lostCount++;
                    } else {
                        log.error("【智能匹配】提取失物文本特征失败，物品ID: {}", item.getId());
                    }
                }
            }

            // 2. 获取所有拾物信息
            List<FoundItem> foundItems = foundItemMapper.selectFoundItems(null, null, null, null, null, null);
            int foundCount = 0;

            for (FoundItem item : foundItems) {
                // 检查是否已有特征向量 - 使用item_feature_vectors表
                if (itemFeatureVectorMapper.checkFeatureVectorExists(item.getId(), "FOUND") == 0) {
                    log.info("【数据库表使用】检查item_feature_vectors表中是否存在拾物ID: {}的记录", item.getId());

                    // 使用HanLP提取关键词
                    String keywords = KeywordExtractor.extractKeywords(item.getItemName(), item.getDescription());
                    log.info("【智能匹配】拾物提取的关键词: {}", keywords);

                    // 提取文本特征向量（使用提取的关键词）
                    byte[] textFeatures = clipFaissClient.extractTextFeatures(keywords);

                    if (textFeatures != null) {
                        log.info("【智能匹配】成功提取拾物文本特征，物品ID: {}, 向量长度: {} 字节", item.getId(), textFeatures.length);

                        ItemFeatureVector vector = new ItemFeatureVector();
                        vector.setItemId(item.getId());
                        vector.setItemType("FOUND");
                        vector.setTextVector(textFeatures);
                        vector.setVectorVersion(VECTOR_VERSION);

                        // 插入特征向量 - 使用item_feature_vectors表
                        itemFeatureVectorMapper.insertFeatureVector(vector);
                        log.info("【数据库表使用】向item_feature_vectors表插入拾物特征向量，物品ID: {}", item.getId());

                        // 添加特征向量到索引
                        Long vectorId = clipFaissClient.addVectorToIndex(item.getId(), "FOUND", textFeatures, "TEXT");
                        if (vectorId != null) {
                            log.info("【智能匹配】成功添加拾物文本特征向量到索引，物品ID: {}, 向量ID: {}", item.getId(), vectorId);
                        } else {
                            log.error("【智能匹配】添加拾物文本特征向量到索引失败，物品ID: {}", item.getId());
                        }

                        foundCount++;
                    } else {
                        log.error("【智能匹配】提取拾物文本特征失败，物品ID: {}", item.getId());
                    }
                }
            }

            String message = String.format("成功生成特征向量：%d个失物，%d个拾物", lostCount, foundCount);
            log.info(message);
            return Result.success(message);
        } catch (Exception e) {
            log.error("生成特征向量时发生异常", e);
            return Result.error("生成特征向量失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Map<String, Object>> performAutoMatch(Long userId, Long itemId, String itemType) {
        try {
            log.info("【智能匹配】开始执行自动匹配: 用户ID={}, 物品ID={}, 类型={}", userId, itemId, itemType);

            // 初始化过滤阈值 - 确保在每次匹配前都初始化阈值
            initFilterThresholds();

            log.info("【智能匹配】当前结果相似度阈值: {}, 通知相似度阈值: {}", resultSimilarityThreshold, notificationSimilarityThreshold);
            log.info("【智能匹配】过滤阈值已初始化: 文本-文本={}, 图像-图像={}, 文本-图像={}, 图像-文本={}, 综合={}",
                    textToTextThreshold, imageToImageThreshold, textToImageThreshold, imageToTextThreshold, combinedThreshold);

            // 验证用户是否有权限触发该物品的匹配
            boolean hasPermission = false;
            if ("LOST".equalsIgnoreCase(itemType)) {
                LostItem lostItem = lostItemMapper.selectById(itemId);
                hasPermission = lostItem != null && lostItem.getUserId().equals(userId);
            } else if ("FOUND".equalsIgnoreCase(itemType)) {
                FoundItem foundItem = foundItemMapper.selectById(itemId);
                hasPermission = foundItem != null && foundItem.getUserId().equals(userId);
            }

            if (!hasPermission) {
                return Result.fail("您没有权限触发该物品的匹配");
            }

            // 检查是否是从AsyncTaskServiceImpl调用的（审核通过后的自动匹配）
            // 通过检查调用栈来判断
            boolean isFromAsyncTask = false;
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            for (StackTraceElement element : stackTrace) {
                if (element.getClassName().contains("AsyncTaskServiceImpl") &&
                    element.getMethodName().contains("processItemAutoMatchAsync")) {
                    isFromAsyncTask = true;
                    break;
                }
            }

            if (isFromAsyncTask) {
                // 这是审核通过后的自动匹配，需要发送通知
                autoNotify = true;
                log.info("【智能匹配】这是审核通过后的自动匹配，将发送通知");
            } else {
                // 这是用户手动触发的匹配，不需要发送通知
                autoNotify = false;
                log.info("【智能匹配】这是用户手动触发的匹配，不发送通知");
            }

            // 0. 检查服务是否可用
            if (!isClipServiceAvailable()) {
                log.error("【智能匹配】CLIP+FAISS服务不可用");
                return Result.fail("智能匹配服务暂时不可用，请稍后再试");
            }

            // 1. 确定目标物品类型
            String targetType = "LOST".equals(itemType) ? "FOUND" : "LOST";
            log.info("【智能匹配】目标物品类型: {}", targetType);

            // 2. 从数据库获取特征向量
            log.info("【智能匹配】从数据库获取特征向量: 物品ID={}, 类型={}", itemId, itemType);
            ItemFeatureVector vector = itemFeatureVectorMapper.getFeatureVector(itemId, itemType);

            if (vector == null) {
                log.warn("【智能匹配】未找到物品特征向量，物品ID: {}, 类型: {}", itemId, itemType);
                return Result.fail("未找到物品特征向量，无法执行匹配");
            }

            // 记录特征向量信息
            log.info("【智能匹配】成功获取特征向量: 物品ID={}, 类型={}, 文本特征={}, 图像特征={}",
                    itemId, itemType,
                    vector.getTextVector() != null ? vector.getTextVector().length + "字节" : "无",
                    vector.getImageVector() != null ? vector.getImageVector().length + "字节" : "无");

            if (vector.getTextVector() == null && vector.getImageVector() == null) {
                log.warn("【智能匹配】特征向量为空，物品ID: {}, 类型: {}", itemId, itemType);
                return Result.fail("特征向量为空，无法执行匹配");
            }

            // 3. 创建匹配历史记录
            MatchHistory matchHistory = new MatchHistory();
            matchHistory.setUserId(userId);
            matchHistory.setQueryType("MIXED");
            matchHistory.setItemType(targetType);
            matchHistory.setResultCount(0);

            // 插入匹配历史记录
            matchHistoryMapper.insertMatchHistory(matchHistory);
            Long matchHistoryId = matchHistory.getId();
            log.info("【智能匹配】创建匹配历史记录，ID: {}", matchHistoryId);

            // 4. 执行搜索
            // 根据特征向量类型选择不同的搜索方法
            if (vector.getTextVector() != null && vector.getImageVector() != null) {
                // 使用文本和图像特征进行混合搜索
                log.info("【智能匹配】使用文本和图像特征进行混合搜索");
                Map<String, Object> mixedSearchResults = clipFaissClient.searchByMixedVectors(
                        vector.getImageVector(), vector.getTextVector(), targetType, MAX_RESULTS);

                // 使用processMixedSearchResults处理混合搜索结果
                log.info("【智能匹配】处理混合搜索结果");
                return processMixedSearchResults(mixedSearchResults, matchHistory, targetType);
            } else if (vector.getTextVector() != null) {
                // 只使用文本特征搜索
                log.info("【智能匹配】只使用文本特征进行搜索");
                Map<String, Object> textSearchResults = clipFaissClient.searchByTextVector(
                        vector.getTextVector(), targetType, MAX_RESULTS);

                // 使用processTextSearchResults处理文本搜索结果
                log.info("【智能匹配】处理文本搜索结果");
                return processTextSearchResults(textSearchResults, matchHistory, targetType);
            } else if (vector.getImageVector() != null) {
                // 只使用图像特征搜索
                log.info("【智能匹配】只使用图像特征进行搜索: 特征向量大小={} 字节, 目标类型={}, 最大结果数={}",
                        vector.getImageVector().length, targetType, MAX_RESULTS);

                // 记录调用前的详细信息
                log.info("【智能匹配】调用searchByImageVector方法: 物品ID={}, 类型={}", itemId, itemType);

                Map<String, Object> imageSearchResults = clipFaissClient.searchByImageVector(
                        vector.getImageVector(), targetType, MAX_RESULTS);

                // 记录调用后的详细信息
                log.info("【智能匹配】searchByImageVector方法调用完成: 结果={}",
                        imageSearchResults != null ? "成功，包含 " + imageSearchResults.size() + " 个键" : "失败");

                if (imageSearchResults != null && !imageSearchResults.isEmpty()) {
                    log.info("【智能匹配】搜索结果包含以下键: {}", imageSearchResults.keySet());
                }

                // 使用processImageSearchResults处理图像搜索结果
                log.info("【智能匹配】开始处理图像搜索结果");
                return processImageSearchResults(imageSearchResults, matchHistory, targetType);
            } else {
                // 如果没有有效的特征向量
                log.warn("【智能匹配】没有有效的特征向量，无法执行匹配");
                return Result.fail("没有有效的特征向量，无法执行匹配");
            }
        } catch (Exception e) {
            log.error("【智能匹配】执行自动匹配时发生异常", e);
            return Result.fail("自动匹配失败: " + e.getMessage());
        }
    }



    /**
     * 处理混合搜索结果，只包含综合匹配结果，但包含四种相似度的详细信息
     *
     * @param searchResults CLIP+FAISS搜索结果
     * @param matchHistory 匹配历史记录
     * @param targetType 目标物品类型
     * @return 处理后的结果
     */
    @SuppressWarnings("unchecked")
    private Result<Map<String, Object>> processMixedSearchResults(Map<String, Object> searchResults, MatchHistory matchHistory, String targetType) {
        try {
            if (searchResults == null || searchResults.isEmpty()) {
                log.info("【智能匹配】未找到匹配结果，匹配历史ID: {}", matchHistory.getId());
                matchHistoryMapper.updateResultCount(matchHistory.getId(), 0);
                return Result.success(Map.of("results", Map.of(
                    "combined", List.of()
                )));
            }

            // 获取综合匹配结果 - 从Python API返回的results字段中获取
            List<Map<String, Object>> combinedResults = (List<Map<String, Object>>) searchResults.getOrDefault("results", List.of());
            log.info("【智能匹配】找到综合匹配结果: {} 个", combinedResults.size());

            // 记录前几个结果的详细信息，特别是四种相似度
            if (combinedResults != null && !combinedResults.isEmpty()) {
                for (int i = 0; i < Math.min(3, combinedResults.size()); i++) {
                    Map<String, Object> result = combinedResults.get(i);
                    log.info("【智能匹配】综合结果 #{}: ID={}, 相似度={}, 匹配类型={}",
                            i+1, result.get("item_id"), result.get("similarity"), result.get("match_type"));

                    // 记录四种相似度的详细信息
                    if (result.containsKey("match_details")) {
                        Map<String, Object> details = (Map<String, Object>) result.get("match_details");
                        log.info("【智能匹配】四种相似度: TEXT_TO_TEXT={}, IMAGE_TO_IMAGE={}, TEXT_TO_IMAGE={}, IMAGE_TO_TEXT={}",
                                details.get("TEXT_TO_TEXT"), details.get("IMAGE_TO_IMAGE"),
                                details.get("TEXT_TO_IMAGE"), details.get("IMAGE_TO_TEXT"));
                    } else {
                        log.warn("【智能匹配】结果项缺少match_details字段");
                    }
                }
            }

            // 处理结果，填充物品详情
            List<Map<String, Object>> processedCombinedResults = processResultsWithItemDetails(combinedResults, targetType);
            log.info("【智能匹配】处理后的综合匹配结果: {} 个", processedCombinedResults.size());

            // 更新匹配历史记录的结果数量
            int totalResults = processedCombinedResults.size();
            matchHistory.setResultCount(totalResults);
            matchHistoryMapper.updateResultCount(matchHistory.getId(), totalResults);

            // 保存综合匹配结果到数据库
            // 这些结果已经经过相似度阈值过滤，只包含相似度 >= combinedThreshold 的结果
            List<MatchResult> matchResultsList = new ArrayList<>();
            for (Map<String, Object> result : processedCombinedResults) {
                Long itemId = Long.valueOf(result.get("id").toString());
                Float similarity = (Float) result.get("similarity");

                MatchResult matchResult = new MatchResult();
                matchResult.setMatchHistoryId(matchHistory.getId());
                matchResult.setItemId(itemId);
                matchResult.setItemType(targetType);
                matchResult.setSimilarityScore(new BigDecimal(similarity.toString()));
                matchResultsList.add(matchResult);
            }

            // 批量保存匹配结果
            if (!matchResultsList.isEmpty()) {
                matchResultMapper.batchInsertMatchResults(matchResultsList);
                log.info("【智能匹配】保存了 {} 条匹配结果到数据库", matchResultsList.size());
            }

            // 处理高相似度匹配并发送通知
            if (autoNotify && !processedCombinedResults.isEmpty()) {
                log.info("【智能匹配】准备发送匹配通知，autoNotify={}, 结果数量={}, 通知阈值={}",
                        autoNotify, processedCombinedResults.size(), notificationSimilarityThreshold);

                // 记录前几个结果的详细信息，用于调试
                for (int i = 0; i < Math.min(3, processedCombinedResults.size()); i++) {
                    Map<String, Object> result = processedCombinedResults.get(i);
                    log.info("【智能匹配】通知候选结果 #{}: ID={}, 相似度={}, 匹配类型={}",
                            i+1, result.get("id"), result.get("similarity"), result.get("match_type"));
                }

                Result notifyResult = matchNotificationService.processHighSimilarityMatches(
                    processedCombinedResults,
                    matchHistory.getId(),
                    matchHistory.getUserId(),
                    notificationSimilarityThreshold
                );

                log.info("【智能匹配】发送匹配通知结果: code={}, message={}",
                        notifyResult.getCode(), notifyResult.getMessage());
            } else {
                log.info("【智能匹配】不发送匹配通知，autoNotify={}, 结果为空={}",
                        autoNotify, processedCombinedResults.isEmpty());
            }

            // 返回结果
            Map<String, Object> results = new HashMap<>();
            results.put("matchHistoryId", matchHistory.getId());
            results.put("results", Map.of(
                "combined", processedCombinedResults
            ));

            return Result.success(results);
        } catch (Exception e) {
            log.error("处理混合搜索结果时发生异常", e);
            return Result.fail("处理搜索结果失败: " + e.getMessage());
        }
    }



    /**
     * 处理文本搜索结果，包含文本-文本和文本-图像匹配结果
     *
     * @param searchResults CLIP+FAISS搜索结果
     * @param matchHistory 匹配历史记录
     * @param targetType 目标物品类型
     * @return 处理后的结果
     */
    @SuppressWarnings("unchecked")
    private Result<Map<String, Object>> processTextSearchResults(Map<String, Object> searchResults, MatchHistory matchHistory, String targetType) {
        try {
            if (searchResults == null || searchResults.isEmpty()) {
                log.info("【智能匹配】未找到匹配结果，匹配历史ID: {}", matchHistory.getId());
                matchHistoryMapper.updateResultCount(matchHistory.getId(), 0);
                return Result.success(Map.of("results", Map.of(
                    "text_to_text", List.of(),
                    "text_to_image", List.of()
                )));
            }

            // 获取不同类型的结果
            List<Map<String, Object>> textToTextResults = (List<Map<String, Object>>) searchResults.getOrDefault("text_to_text", List.of());
            List<Map<String, Object>> textToImageResults = (List<Map<String, Object>>) searchResults.getOrDefault("text_to_image", List.of());

            log.info("【智能匹配】找到匹配结果: 文本-文本={}, 文本-图像={}",
                    textToTextResults.size(), textToImageResults.size());

            // 处理结果，填充物品详情
            List<Map<String, Object>> processedTextToTextResults = processResultsWithItemDetails(textToTextResults, targetType);
            List<Map<String, Object>> processedTextToImageResults = processResultsWithItemDetails(textToImageResults, targetType);

            // 更新匹配历史记录的结果数量 - 使用两种结果的总和
            int totalResults = processedTextToTextResults.size() + processedTextToImageResults.size();
            matchHistory.setResultCount(totalResults);
            matchHistoryMapper.updateResultCount(matchHistory.getId(), totalResults);

            // 合并两种结果用于保存到数据库和发送通知
            List<Map<String, Object>> allResults = new ArrayList<>();
            allResults.addAll(processedTextToTextResults);
            allResults.addAll(processedTextToImageResults);

            // 保存所有匹配结果到数据库
            List<MatchResult> matchResultsList = new ArrayList<>();
            for (Map<String, Object> result : allResults) {
                Long itemId = Long.valueOf(result.get("id").toString());
                Float similarity = (Float) result.get("similarity");

                MatchResult matchResult = new MatchResult();
                matchResult.setMatchHistoryId(matchHistory.getId());
                matchResult.setItemId(itemId);
                matchResult.setItemType(targetType);
                matchResult.setSimilarityScore(new BigDecimal(similarity.toString()));
                matchResultsList.add(matchResult);
            }

            // 批量保存匹配结果
            if (!matchResultsList.isEmpty()) {
                matchResultMapper.batchInsertMatchResults(matchResultsList);
                log.info("【智能匹配】保存了 {} 条匹配结果到数据库", matchResultsList.size());
            }

            // 处理高相似度匹配并发送通知
            if (autoNotify && !allResults.isEmpty()) {
                log.info("【智能匹配】准备发送匹配通知，autoNotify={}, 结果数量={}, 通知阈值={}",
                        autoNotify, allResults.size(), notificationSimilarityThreshold);

                // 记录前几个结果的详细信息，用于调试
                for (int i = 0; i < Math.min(3, allResults.size()); i++) {
                    Map<String, Object> result = allResults.get(i);
                    log.info("【智能匹配】通知候选结果 #{}: ID={}, 相似度={}, 匹配类型={}",
                            i+1, result.get("id"), result.get("similarity"), result.get("match_type"));
                }

                Result<?> notifyResult = matchNotificationService.processHighSimilarityMatches(
                    allResults,
                    matchHistory.getId(),
                    matchHistory.getUserId(),
                    notificationSimilarityThreshold
                );

                log.info("【智能匹配】发送匹配通知结果: code={}, message={}",
                        notifyResult.getCode(), notifyResult.getMessage());
            } else {
                log.info("【智能匹配】不发送匹配通知，autoNotify={}, 结果为空={}",
                        autoNotify, allResults.isEmpty());
            }

            // 返回结果
            Map<String, Object> results = new HashMap<>();
            results.put("matchHistoryId", matchHistory.getId());
            results.put("results", Map.of(
                "text_to_text", processedTextToTextResults,
                "text_to_image", processedTextToImageResults
            ));

            return Result.success(results);
        } catch (Exception e) {
            log.error("处理文本搜索结果时发生异常", e);
            return Result.fail("处理搜索结果失败: " + e.getMessage());
        }
    }

    /**
     * 处理图像搜索结果，包含图像-图像和图像-文本匹配结果
     *
     * @param searchResults CLIP+FAISS搜索结果
     * @param matchHistory 匹配历史记录
     * @param targetType 目标物品类型
     * @return 处理后的结果
     */
    @SuppressWarnings("unchecked")
    private Result<Map<String, Object>> processImageSearchResults(Map<String, Object> searchResults, MatchHistory matchHistory, String targetType) {
        try {
            if (searchResults.isEmpty()) {
                log.info("【智能匹配】未找到匹配结果，匹配历史ID: {}", matchHistory.getId());
                matchHistoryMapper.updateResultCount(matchHistory.getId(), 0);
                return Result.success(Map.of("results", Map.of(
                    "image_to_image", List.of(),
                    "image_to_text", List.of()
                )));
            }

            // 获取不同类型的结果
            List<Map<String, Object>> imageToImageResults = (List<Map<String, Object>>) searchResults.getOrDefault("image_to_image", List.of());
            List<Map<String, Object>> imageToTextResults = (List<Map<String, Object>>) searchResults.getOrDefault("image_to_text", List.of());

            log.info("【智能匹配】找到匹配结果: 图像-图像={}, 图像-文本={}",
                    imageToImageResults.size(), imageToTextResults.size());

            // 处理结果，填充物品详情
            List<Map<String, Object>> processedImageToImageResults = processResultsWithItemDetails(imageToImageResults, targetType);
            List<Map<String, Object>> processedImageToTextResults = processResultsWithItemDetails(imageToTextResults, targetType);

            // 更新匹配历史记录的结果数量 - 使用两种结果的总和
            int totalResults = processedImageToImageResults.size() + processedImageToTextResults.size();
            matchHistory.setResultCount(totalResults);
            matchHistoryMapper.updateResultCount(matchHistory.getId(), totalResults);

            // 合并两种结果用于保存到数据库和发送通知
            List<Map<String, Object>> allResults = new ArrayList<>();
            allResults.addAll(processedImageToImageResults);
            allResults.addAll(processedImageToTextResults);

            // 保存所有匹配结果到数据库
            List<MatchResult> matchResultsList = new ArrayList<>();
            for (Map<String, Object> result : allResults) {
                Long itemId = Long.valueOf(result.get("id").toString());
                Float similarity = (Float) result.get("similarity");

                MatchResult matchResult = new MatchResult();
                matchResult.setMatchHistoryId(matchHistory.getId());
                matchResult.setItemId(itemId);
                matchResult.setItemType(targetType);
                matchResult.setSimilarityScore(new BigDecimal(similarity.toString()));
                matchResultsList.add(matchResult);
            }

            // 批量保存匹配结果
            if (!matchResultsList.isEmpty()) {
                matchResultMapper.batchInsertMatchResults(matchResultsList);
                log.info("【智能匹配】保存了 {} 条匹配结果到数据库", matchResultsList.size());
            }

            // 处理高相似度匹配并发送通知
            if (autoNotify && !allResults.isEmpty()) {
                log.info("【智能匹配】准备发送匹配通知，autoNotify={}, 结果数量={}, 通知阈值={}",
                        autoNotify, allResults.size(), notificationSimilarityThreshold);

                // 记录前几个结果的详细信息，用于调试
                for (int i = 0; i < Math.min(3, allResults.size()); i++) {
                    Map<String, Object> result = allResults.get(i);
                    log.info("【智能匹配】通知候选结果 #{}: ID={}, 相似度={}, 匹配类型={}",
                            i+1, result.get("id"), result.get("similarity"), result.get("match_type"));
                }

                Result<?> notifyResult = matchNotificationService.processHighSimilarityMatches(
                    allResults,
                    matchHistory.getId(),
                    matchHistory.getUserId(),
                    notificationSimilarityThreshold
                );

                log.info("【智能匹配】发送匹配通知结果: code={}, message={}",
                        notifyResult.getCode(), notifyResult.getMessage());
            } else {
                log.info("【智能匹配】不发送匹配通知，autoNotify={}, 结果为空={}",
                        autoNotify, allResults.isEmpty());
            }

            // 返回结果
            Map<String, Object> results = new HashMap<>();
            results.put("matchHistoryId", matchHistory.getId());
            results.put("results", Map.of(
                "image_to_image", processedImageToImageResults,
                "image_to_text", processedImageToTextResults
            ));

            return Result.success(results);
        } catch (Exception e) {
            log.error("处理图像搜索结果时发生异常", e);
            return Result.fail("处理搜索结果失败: " + e.getMessage());
        }
    }

    /**
     * 处理搜索结果，填充物品详情
     *
     * @param results 搜索结果列表
     * @param itemType 物品类型
     * @return 处理后的结果列表
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> processResultsWithItemDetails(List<Map<String, Object>> results, String itemType) {
        if (results == null || results.isEmpty()) {
            return List.of();
        }

        List<Map<String, Object>> processedResults = new ArrayList<>();
        int filteredCount = 0;

        for (Map<String, Object> result : results) {
            try {
                // 获取物品ID
                Long itemId = Long.valueOf(result.get("item_id").toString());

                // 获取相似度
                Float similarity = Float.valueOf(result.get("similarity").toString());

                // 获取匹配类型
                String matchType = (String) result.getOrDefault("match_type", "");

                // 根据匹配类型选择适当的阈值
                float thresholdToApply = resultSimilarityThreshold; // 默认使用配置的阈值

                switch (matchType) {
                    case "TEXT_TO_TEXT":
                        thresholdToApply = textToTextThreshold;
                        break;
                    case "IMAGE_TO_IMAGE":
                        thresholdToApply = imageToImageThreshold;
                        break;
                    case "TEXT_TO_IMAGE":
                        thresholdToApply = textToImageThreshold;
                        break;
                    case "IMAGE_TO_TEXT":
                        thresholdToApply = imageToTextThreshold;
                        break;
                    default:
                        // 对于综合结果或未知类型，使用综合阈值
                        thresholdToApply = combinedThreshold;
                        break;
                }

                // 添加相似度阈值过滤 - 只保留相似度 >= 阈值的结果
                // 这样可以减少无效数据存储，提高匹配质量，只向用户展示有意义的结果
                if (similarity < thresholdToApply) {
                    log.debug("【智能匹配】过滤低相似度结果: itemId={}, matchType={}, similarity={}, threshold={}",
                             itemId, matchType, similarity, thresholdToApply);
                    filteredCount++;
                    continue;  // 跳过低相似度的结果，不会保存到数据库
                }

                // 获取匹配详情
                Map<String, Object> matchDetails = (Map<String, Object>) result.getOrDefault("match_details", null);

                // 根据物品类型获取详情
                Map<String, Object> itemDetails = new HashMap<>();
                itemDetails.put("id", itemId);
                itemDetails.put("similarity", similarity);
                itemDetails.put("match_type", matchType);

                if (matchDetails != null) {
                    itemDetails.put("match_details", matchDetails);
                }

                if ("LOST".equals(itemType)) {
                    LostItem lostItem = lostItemMapper.selectById(itemId);
                    if (lostItem == null) {
                        log.warn("未找到失物信息，ID: {}", itemId);
                        continue;
                    }

                    itemDetails.put("name", lostItem.getItemName());
                    itemDetails.put("description", lostItem.getDescription());
                    itemDetails.put("location", lostItem.getLostLocation());
                    itemDetails.put("time", lostItem.getLostTime());
                    itemDetails.put("imageUrl", lostItem.getImageUrl());
                    itemDetails.put("status", lostItem.getStatus());
                    itemDetails.put("userId", lostItem.getUserId());
                    itemDetails.put("itemType", "LOST");
                } else {
                    FoundItem foundItem = foundItemMapper.selectById(itemId);
                    if (foundItem == null) {
                        log.warn("未找到拾物信息，ID: {}", itemId);
                        continue;
                    }

                    itemDetails.put("name", foundItem.getItemName());
                    itemDetails.put("description", foundItem.getDescription());
                    itemDetails.put("location", foundItem.getFoundLocation());
                    itemDetails.put("time", foundItem.getFoundTime());
                    itemDetails.put("imageUrl", foundItem.getImageUrl());
                    itemDetails.put("status", foundItem.getStatus());
                    itemDetails.put("userId", foundItem.getUserId());
                    itemDetails.put("itemType", "FOUND");
                }

                processedResults.add(itemDetails);
            } catch (Exception e) {
                log.error("处理搜索结果项时发生异常", e);
            }
        }

        if (filteredCount > 0) {
            log.info("【智能匹配】相似度过滤: 过滤了 {} 个低于阈值的结果，剩余 {} 个结果 (阈值: 文本-文本={}, 图像-图像={}, 文本-图像={}, 图像-文本={}, 综合={})",
                    filteredCount, processedResults.size(),
                    textToTextThreshold, imageToImageThreshold, textToImageThreshold, imageToTextThreshold, combinedThreshold);
        }

        return processedResults;
    }
}
