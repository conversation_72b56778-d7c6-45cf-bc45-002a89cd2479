import { jwtDecode } from 'jwt-decode'

// 获取token
export function getToken() {
  return localStorage.getItem('token')
}

// 设置token
export function setToken(token) {
  localStorage.setItem('token', token)
}

// 移除token
export function removeToken() {
  localStorage.removeItem('token')
}

// 获取用户信息
export function getUserInfo() {
  // 首先尝试从localStorage获取用户信息
  const userInfoStr = localStorage.getItem('userInfo')
  if (userInfoStr) {
    try {
      const userInfo = JSON.parse(userInfoStr)
      // 确保roles字段存在且为数组
      if (userInfo && userInfo.roles) {
        return userInfo
      }
    } catch (error) {
      console.error('解析本地用户信息失败：', error)
    }
  }

  // 如果本地存储中没有有效的用户信息，尝试从token解析
  const token = getToken()
  if (!token) return null

  try {
    const decoded = jwtDecode(token)

    // 确保用户基本信息存在
    if (!decoded.userId || !decoded.username) {
      return null
    }

    // 处理角色信息
    let roles = []
    if (decoded.roles) {
      // 如果roles是字符串，转换为数组
      if (typeof decoded.roles === 'string') {
        roles = [decoded.roles]
      } else if (Array.isArray(decoded.roles)) {
        roles = decoded.roles
      }
    }

    // 构建用户信息对象
    const userInfo = {
      id: decoded.userId,
      username: decoded.username,
      roles: roles
    }

    // 更新本地存储
    localStorage.setItem('userInfo', JSON.stringify(userInfo))

    return userInfo
  } catch (error) {
    // 只在开发环境输出错误
    if (process.env.NODE_ENV === 'development') {
      console.error('解析token失败：', error)
    }
    return null
  }
}

// 判断是否为管理员
export function isAdmin() {
  const userInfo = getUserInfo()
  // 移除频繁的日志输出
  if (!userInfo || !Array.isArray(userInfo.roles)) {
    return false
  }
  return userInfo.roles.some(role => ['ADMIN', 'SUPER_ADMIN'].includes(role))
}

// 判断是否为超级管理员
export function isSuperAdmin() {
  const userInfo = getUserInfo()
  if (!userInfo || !Array.isArray(userInfo.roles)) return false
  return userInfo.roles.includes('SUPER_ADMIN')
}

// 调试函数：打印token信息
export function debugToken() {
  const token = getToken()
  if (!token) {
    console.log('未找到token')
    return
  }

  console.log('原始token:', token)

  try {
    const decoded = jwtDecode(token)
    console.log('解析后的token信息:', decoded)
    console.log('用户ID:', decoded.userId)
    console.log('用户名:', decoded.username)
    console.log('角色:', decoded.roles)
    console.log('过期时间:', new Date(decoded.exp * 1000).toLocaleString())

    // 打印本地存储的用户信息
    const userInfoStr = localStorage.getItem('userInfo')
    if (userInfoStr) {
      try {
        const userInfo = JSON.parse(userInfoStr)
        console.log('本地存储的用户信息:', userInfo)
      } catch (error) {
        console.error('解析本地用户信息失败：', error)
      }
    } else {
      console.log('本地存储中没有用户信息')
    }
  } catch (error) {
    console.error('token解析失败:', error)
  }
}