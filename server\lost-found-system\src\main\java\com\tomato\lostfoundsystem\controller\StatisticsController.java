package com.tomato.lostfoundsystem.controller;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.HomeStatisticsDTO;
import com.tomato.lostfoundsystem.service.StatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 统计数据Controller
 */
@RestController
@RequestMapping("/api/statistics")
@Slf4j
public class StatisticsController {
    
    @Autowired
    private StatisticsService statisticsService;
    
    /**
     * 获取首页统计数据
     */
    @GetMapping("/home")
    public Result<HomeStatisticsDTO> getHomePageStatistics() {
        return statisticsService.getHomePageStatistics();
    }
    
    /**
     * 获取活跃用户统计数据
     */
    @GetMapping("/active-users")
    public Result<Map<String, Object>> getActiveUsersStatistics() {
        return statisticsService.getActiveUsersStatistics();
    }
    
    /**
     * 获取统计数据趋势
     */
    @GetMapping("/trend")
    public Result<Map<String, Object>> getStatisticsTrend(
            @RequestParam String type,
            @RequestParam(defaultValue = "30") int days) {
        return statisticsService.getStatisticsTrend(type, days);
    }
    
    /**
     * 手动更新统计数据缓存（仅管理员可用）
     */
    @PostMapping("/update-cache")
    public Result<String> updateStatisticsCache() {
        try {
            statisticsService.updateStatisticsCache();
            return Result.success("统计数据缓存更新成功");
        } catch (Exception e) {
            log.error("手动更新统计数据缓存失败", e);
            return Result.error("更新统计数据缓存失败: " + e.getMessage());
        }
    }
}
