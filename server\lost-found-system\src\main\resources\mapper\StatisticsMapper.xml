<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomato.lostfoundsystem.mapper.StatisticsMapper">

    <!-- 获取总物品数 -->
    <select id="getTotalItems" resultType="int">
        SELECT
            (SELECT COUNT(*) FROM lost_items WHERE audit_status = 'APPROVED') +
            (SELECT COUNT(*) FROM found_items WHERE audit_status = 'APPROVED') AS total_items
    </select>

    <!-- 获取已归还物品数 -->
    <select id="getReturnedItems" resultType="int">
        SELECT COUNT(*)
        FROM found_items
        WHERE status = 'RETURNED' AND audit_status = 'APPROVED'
    </select>

    <!-- 获取已找回物品数 -->
    <select id="getSuccessMatches" resultType="int">
        SELECT COUNT(*)
        FROM lost_items
        WHERE status = 'FOUND' AND audit_status = 'APPROVED'
    </select>

    <!-- 保存统计数据历史 -->
    <insert id="saveStatisticsHistory" parameterType="com.tomato.lostfoundsystem.entity.StatisticsHistory">
        INSERT INTO statistics_history (stat_date, stat_type, stat_value, created_at)
        VALUES (#{statDate}, #{statType}, #{statValue}, NOW())
        ON DUPLICATE KEY UPDATE
            stat_value = #{statValue},
            created_at = NOW()
    </insert>

    <!-- 获取指定日期和类型的统计数据 -->
    <select id="getStatisticsByDateAndType" resultType="com.tomato.lostfoundsystem.entity.StatisticsHistory">
        SELECT id, stat_date, stat_type, stat_value, created_at
        FROM statistics_history
        WHERE stat_date = #{statDate} AND stat_type = #{statType}
    </select>

    <!-- 获取统计数据趋势 -->
    <select id="getStatisticsTrend" resultType="java.util.Map">
        SELECT stat_date as date, stat_value as value
        FROM statistics_history
        WHERE stat_type = #{statType}
          AND stat_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY stat_date
    </select>

</mapper>
