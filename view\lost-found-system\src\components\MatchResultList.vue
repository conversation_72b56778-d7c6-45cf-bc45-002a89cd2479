<template>
  <div class="match-result-list">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    
    <div v-else-if="results.length === 0" class="empty-container">
      <el-empty description="暂无匹配结果" />
    </div>
    
    <div v-else class="results-container">
      <div class="result-count">
        找到 <span class="highlight">{{ results.length }}</span> 条匹配结果
      </div>
      
      <el-card v-for="item in results" :key="item.id" class="result-card">
        <div class="result-content">
          <div class="result-image">
            <el-image 
              :src="item.imageUrl" 
              fit="cover"
              :preview-src-list="[item.imageUrl]"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </div>
          
          <div class="result-info">
            <h3 class="item-name">{{ item.itemName }}</h3>
            <p class="item-description">{{ item.description }}</p>
            <div class="item-details">
              <div class="detail-item">
                <el-icon><Location /></el-icon>
                <span>{{ item.location }}</span>
              </div>
              <div class="detail-item">
                <el-icon><Calendar /></el-icon>
                <span>{{ item.date }} {{ item.time }}</span>
              </div>
              <div class="detail-item">
                <el-icon><User /></el-icon>
                <span>{{ item.contactPerson }}</span>
              </div>
            </div>
          </div>
          
          <div class="result-similarity">
            <div class="similarity-circle" :style="{ 
              background: getSimilarityColor(item.similarity),
              boxShadow: `0 0 10px ${getSimilarityColor(item.similarity, 0.5)}`
            }">
              {{ (item.similarity * 100).toFixed(0) }}%
            </div>
            <div class="similarity-text">相似度</div>
            
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="viewDetail(item)">查看详情</el-button>
              <el-button type="success" size="small" @click="contactOwner(item)">联系{{ item.itemType === 'LOST' ? '失主' : '拾主' }}</el-button>
            </div>
          </div>
        </div>
        
        <div class="result-status">
          <el-tag :type="getStatusType(item.status)">
            {{ getStatusText(item.status) }}
          </el-tag>
        </div>
      </el-card>
      
      <div v-if="hasMore" class="load-more">
        <el-button :loading="loadingMore" @click="loadMore">加载更多</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Picture, Location, Calendar, User } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  results: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  hasMore: {
    type: Boolean,
    default: false
  },
  loadingMore: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['load-more', 'view-detail', 'contact-owner'])

// 获取相似度颜色
const getSimilarityColor = (similarity, alpha = 1) => {
  if (similarity >= 0.9) {
    return `rgba(103, 194, 58, ${alpha})`; // 高相似度 - 绿色
  } else if (similarity >= 0.7) {
    return `rgba(230, 162, 60, ${alpha})`; // 中等相似度 - 黄色
  } else {
    return `rgba(245, 108, 108, ${alpha})`; // 低相似度 - 红色
  }
};

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'FOUND':
    case 'RETURNED':
      return 'success';
    case 'LOST':
    case 'UNCLAIMED':
      return 'warning';
    default:
      return 'info';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'FOUND':
      return '已找回';
    case 'LOST':
      return '未找回';
    case 'RETURNED':
      return '已归还';
    case 'UNCLAIMED':
      return '未认领';
    default:
      return '未知状态';
  }
};

// 加载更多
const loadMore = () => {
  emit('load-more');
};

// 查看详情
const viewDetail = (item) => {
  emit('view-detail', item);
};

// 联系物主
const contactOwner = (item) => {
  emit('contact-owner', item);
};
</script>

<style scoped>
.match-result-list {
  width: 100%;
}

.loading-container, .empty-container {
  padding: 40px 0;
  text-align: center;
}

.results-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-count {
  font-size: 16px;
  margin-bottom: 16px;
  color: #606266;
}

.highlight {
  color: #409EFF;
  font-weight: bold;
}

.result-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.result-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.result-content {
  display: flex;
  gap: 16px;
}

.result-image {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
  border-radius: 4px;
  overflow: hidden;
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 24px;
}

.result-info {
  flex: 1;
  min-width: 0;
}

.item-name {
  margin: 0 0 8px;
  font-size: 18px;
  color: #303133;
}

.item-description {
  margin: 0 0 12px;
  font-size: 14px;
  color: #606266;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #909399;
}

.result-similarity {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  flex-shrink: 0;
}

.similarity-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

.similarity-text {
  font-size: 12px;
  color: #909399;
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.result-status {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

.load-more {
  text-align: center;
  margin-top: 16px;
  padding: 8px 0;
}
</style>
