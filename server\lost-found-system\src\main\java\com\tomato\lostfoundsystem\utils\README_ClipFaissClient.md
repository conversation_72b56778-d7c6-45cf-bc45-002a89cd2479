# ClipFaissClient 重构说明

## 重构背景

原有的 `ClipFaissClient` 类存在以下问题：

1. 接口不完整，未充分利用 Python API 提供的所有功能
2. 代码结构不够清晰，存在重复逻辑
3. 错误处理不够统一
4. 与 Python API 的接口匹配度不高

因此，我们对 `ClipFaissClient` 进行了重构，创建了 `ClipFaissClientRefactored` 类，以解决上述问题。

## 重构后的类结构

`ClipFaissClientRefactored` 类是一个纯通信客户端，只负责与 CLIP+FAISS API 服务进行通信，不包含业务逻辑。它提供了以下主要方法：

### 服务检查

- `isServiceAvailable()` - 检查 CLIP+FAISS 服务是否可用

### 特征提取

- `extractTextFeatures(String text)` - 从文本中提取特征向量
- `extractImageFeatures(MultipartFile imageFile)` - 从图像文件中提取特征向量
- `extractImageFeaturesFromUrl(String imageUrl)` - 从图像 URL 中提取特征向量
- `uploadAndExtractFeatures(MultipartFile imageFile, String itemType)` - 上传图像并提取特征向量
- `extractImageDescription(MultipartFile imageFile)` - 从图像中提取描述文本

### 索引管理

- `addVectorToIndex(Long itemId, String itemType, byte[] featureVector, String indexType)` - 将特征向量添加到索引
- `removeVectorFromIndex(Long itemId, String itemType, String indexType)` - 从索引中移除向量
- `saveIndices()` - 保存所有索引到磁盘
- `getIndexStats()` - 获取索引统计信息
- `rebuildIndex(String itemType, String indexType)` - 重建索引

### 搜索功能

- `searchSimilarVectors(byte[] featureVector, String targetType, String indexType, int limit)` - 搜索相似向量
- `searchMixedVectors(byte[] textFeatures, byte[] imageFeatures, String targetType, int limit)` - 混合搜索
- `searchByImageVector(byte[] imageFeatures, String targetType, int limit)` - 使用图像特征向量搜索
- `searchByTextVector(byte[] textFeatures, String targetType, int limit)` - 使用文本特征向量搜索

## 使用示例

### 特征提取

```java
// 从文本中提取特征向量
String description = "这是一把钥匙，银色，有三个齿";
byte[] textFeatures = clipFaissClient.extractTextFeatures(description);

// 从图像文件中提取特征向量
MultipartFile imageFile = ...; // 从请求中获取
byte[] imageFeatures = clipFaissClient.extractImageFeatures(imageFile);

// 从图像URL中提取特征向量
String imageUrl = "https://example.com/images/key.jpg";
byte[] imageFeatures = clipFaissClient.extractImageFeaturesFromUrl(imageUrl);
```

### 索引管理

```java
// 添加特征向量到索引
Long vectorId = clipFaissClient.addVectorToIndex(itemId, "LOST", textFeatures, "TEXT");

// 从索引中移除向量
boolean removed = clipFaissClient.removeVectorFromIndex(itemId, "LOST", null); // 移除所有类型的向量

// 保存索引
boolean saved = clipFaissClient.saveIndices();

// 获取索引统计信息
Map<String, Object> stats = clipFaissClient.getIndexStats();

// 重建索引
boolean rebuilt = clipFaissClient.rebuildIndex("LOST", "TEXT");
```

### 搜索功能

```java
// 搜索相似向量
List<Map<String, Object>> results = clipFaissClient.searchSimilarVectors(
    textFeatures, "FOUND", "TEXT", 20);

// 混合搜索
List<Map<String, Object>> mixedResults = clipFaissClient.searchMixedVectors(
    textFeatures, imageFeatures, "FOUND", 20);

// 使用图像特征向量搜索
Map<String, Object> imageSearchResults = clipFaissClient.searchByImageVector(
    imageFeatures, "FOUND", 20);

// 使用文本特征向量搜索
Map<String, Object> textSearchResults = clipFaissClient.searchByTextVector(
    textFeatures, "FOUND", 20);
```

## 配置说明

`ClipFaissClientRefactored` 类需要在 `application.yml` 中配置以下参数：

```yaml
autodl:
  clip:
    api:
      url: http://localhost:8000  # CLIP+FAISS API 服务地址
    service:
      check-enabled: true  # 是否启用服务可用性检查
      connection-timeout: 3000  # 连接超时时间（毫秒）
```

## 迁移指南

从原有的 `ClipFaissClient` 迁移到 `ClipFaissClientRefactored` 的步骤：

1. 在需要使用 `ClipFaissClient` 的地方注入 `ClipFaissClientRefactored`
2. 将原有的方法调用替换为新的方法调用，注意参数和返回值的变化
3. 更新错误处理逻辑，适应新的返回值类型

例如，原有代码：

```java
byte[] features = clipFaissClient.extractTextFeatures(description);
if (features == null) {
    return Result.fail("提取文本特征失败");
}
```

替换为：

```java
byte[] features = clipFaissClientRefactored.extractTextFeatures(description);
if (features == null) {
    return Result.fail("提取文本特征失败");
}
```

## 注意事项

1. `ClipFaissClientRefactored` 类是一个纯通信客户端，不包含业务逻辑，因此在使用时需要在业务层处理业务逻辑
2. 所有方法都有详细的错误处理，失败时会返回 `null` 或空集合/Map，调用方需要进行空值检查
3. 日志记录使用了 `log.debug` 级别，可以通过配置日志级别来控制日志输出
4. 部分方法（如 `extractImageDescription`）需要 Python API 提供相应的支持，使用前请确认 Python API 是否支持
