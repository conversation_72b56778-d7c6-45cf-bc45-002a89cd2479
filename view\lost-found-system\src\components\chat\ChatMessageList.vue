<template>
  <div class="message-list" ref="messageListRef">
    <el-scrollbar ref="scrollbarRef" class="message-scrollbar" @scroll="handleScroll">
      <!-- 加载更多按钮 -->
      <div v-if="!noMoreMessages && messages.length > 0" class="load-more-container">
        <el-button 
          :loading="loadingMore" 
          @click="$emit('load-more')" 
          text
        >
          加载更多消息
        </el-button>
      </div>
      
      <!-- 消息列表 -->
      <div class="messages-container">
        <template v-if="messages.length > 0">
          <div 
            v-for="(message, index) in messages" 
            :key="message.id"
            class="message-wrapper"
          >
            <!-- 日期分隔线 -->
            <div 
              v-if="shouldShowDateDivider(message, index)" 
              class="date-divider"
            >
              <span>{{ formatMessageDate(message.time) }}</span>
            </div>
            
            <!-- 消息组件 -->
            <chat-message
              :message="message"
              :user="user"
              :contact="contact"
              @retry="$emit('retry', message.id)"
              @preview-image="$emit('preview-image', $event)"
            />
          </div>
        </template>
        
        <div v-else-if="loading" class="loading-messages">
          <el-skeleton :rows="3" animated />
        </div>
        
        <div v-else class="empty-messages">
          <el-empty description="暂无消息" />
        </div>
      </div>
    </el-scrollbar>
    
    <!-- 滚动到底部按钮 -->
    <div 
      v-if="showScrollToBottom" 
      class="scroll-to-bottom"
      @click="scrollToBottom"
    >
      <el-icon><ArrowDown /></el-icon>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
import ChatMessage from './ChatMessage.vue'
import { formatDate } from '@/utils/chat-utils'

const props = defineProps({
  messages: {
    type: Array,
    default: () => []
  },
  user: {
    type: Object,
    default: () => ({})
  },
  contact: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingMore: {
    type: Boolean,
    default: false
  },
  noMoreMessages: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['load-more', 'retry', 'preview-image'])

// 引用
const messageListRef = ref(null)
const scrollbarRef = ref(null)
const showScrollToBottom = ref(false)
const isScrolledToBottom = ref(true)

// 监听消息变化，自动滚动到底部
watch(() => props.messages, async (newMessages, oldMessages) => {
  if (!newMessages || !oldMessages) return
  
  // 如果是新消息（消息数量增加）且当前滚动在底部，则滚动到底部
  if (newMessages.length > oldMessages.length && isScrolledToBottom.value) {
    await nextTick()
    scrollToBottom()
  }
}, { deep: true })

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  if (scrollbarRef.value) {
    const scrollbar = scrollbarRef.value.$el.querySelector('.el-scrollbar__wrap')
    if (scrollbar) {
      scrollbar.scrollTop = scrollbar.scrollHeight
      isScrolledToBottom.value = true
      showScrollToBottom.value = false
    }
  }
}

// 处理滚动事件
const handleScroll = (e) => {
  const scrollbar = e.target
  const scrollHeight = scrollbar.scrollHeight
  const scrollTop = scrollbar.scrollTop
  const clientHeight = scrollbar.clientHeight
  
  // 判断是否滚动到底部（允许5px的误差）
  isScrolledToBottom.value = scrollHeight - scrollTop - clientHeight < 5
  
  // 如果距离底部超过200px，显示滚动到底部按钮
  showScrollToBottom.value = !isScrolledToBottom.value && scrollHeight - scrollTop - clientHeight > 200
}

// 格式化消息日期
const formatMessageDate = (time) => {
  if (!time) return ''
  
  const date = new Date(time)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  // 如果是今天
  if (date.toDateString() === today.toDateString()) {
    return '今天'
  }
  
  // 如果是昨天
  if (date.toDateString() === yesterday.toDateString()) {
    return '昨天'
  }
  
  // 其他日期
  return formatDate(time)
}

// 判断是否应该显示日期分隔线
const shouldShowDateDivider = (message, index) => {
  if (index === 0) return true
  
  const currentDate = new Date(message.time)
  const prevDate = new Date(props.messages[index - 1].time)
  
  return currentDate.toDateString() !== prevDate.toDateString()
}

// 组件挂载后滚动到底部
onMounted(() => {
  scrollToBottom()
})
</script>

<style scoped>
.message-list {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.message-scrollbar {
  flex: 1;
  overflow: hidden;
}

.messages-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.message-wrapper {
  margin-bottom: 8px;
}

.date-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0;
  position: relative;
}

.date-divider::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  height: 1px;
  background-color: #e9edef;
  z-index: 0;
}

.date-divider span {
  background-color: #f0f2f5;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  color: #8696a0;
  position: relative;
  z-index: 1;
}

.load-more-container {
  display: flex;
  justify-content: center;
  padding: 8px 0;
}

.loading-messages, .empty-messages {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.scroll-to-bottom {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  z-index: 10;
}

.scroll-to-bottom:hover {
  background-color: #f5f5f5;
  transform: scale(1.05);
}

.scroll-to-bottom .el-icon {
  font-size: 20px;
  color: #54656f;
}
</style>
