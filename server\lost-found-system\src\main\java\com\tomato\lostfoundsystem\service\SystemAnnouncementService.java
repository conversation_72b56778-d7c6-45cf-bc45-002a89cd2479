package com.tomato.lostfoundsystem.service;

import com.tomato.lostfoundsystem.common.Result;
import com.tomato.lostfoundsystem.dto.SystemAnnouncementDTO;

import java.util.List;

/**
 * 系统公告服务接口
 */
public interface SystemAnnouncementService {
    /**
     * 创建系统公告
     *
     * @param announcementDTO 公告DTO
     * @param adminId 管理员ID
     * @return 结果
     */
    Result<Long> createAnnouncement(SystemAnnouncementDTO announcementDTO, Long adminId);

    /**
     * 更新系统公告
     *
     * @param announcementDTO 公告DTO
     * @param adminId 管理员ID
     * @return 结果
     */
    Result<String> updateAnnouncement(SystemAnnouncementDTO announcementDTO, Long adminId);

    /**
     * 删除系统公告
     *
     * @param id 公告ID
     * @param adminId 管理员ID
     * @return 结果
     */
    Result<String> deleteAnnouncement(Long id, Long adminId);

    /**
     * 获取系统公告详情
     *
     * @param id 公告ID
     * @param userId 用户ID
     * @return 结果
     */
    Result<SystemAnnouncementDTO> getAnnouncementDetail(Long id, Long userId);

    /**
     * 获取有效的系统公告列表（用户视图）
     *
     * @param userId 用户ID
     * @return 结果
     */
    Result<List<SystemAnnouncementDTO>> getValidAnnouncements(Long userId);

    /**
     * 获取最新的系统公告
     *
     * @param userId 用户ID
     * @return 结果
     */
    Result<SystemAnnouncementDTO> getLatestAnnouncement(Long userId);

    /**
     * 获取所有系统公告（管理员视图）
     *
     * @return 结果
     */
    Result<List<SystemAnnouncementDTO>> getAllAnnouncements();

    /**
     * 发布系统公告
     *
     * @param id 公告ID
     * @param adminId 管理员ID
     * @return 结果
     */
    Result<String> publishAnnouncement(Long id, Long adminId);

    /**
     * 标记公告为已读
     *
     * @param announcementId 公告ID
     * @param userId 用户ID
     * @return 结果
     */
    Result<String> markAnnouncementAsRead(Long announcementId, Long userId);

    /**
     * 获取未读公告数量
     *
     * @param userId 用户ID
     * @return 结果
     */
    Result<Integer> getUnreadCount(Long userId);
}
