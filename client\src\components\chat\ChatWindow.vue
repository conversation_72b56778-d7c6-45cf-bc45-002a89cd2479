<template>
  <div class="chat-window">
    <div v-if="!selectedContact" class="empty-chat">
      <div class="empty-chat-content">
        <i class="el-icon-chat-dot-round"></i>
        <p>选择一个联系人开始聊天</p>
      </div>
    </div>
    
    <template v-else>
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="contact-info">
          <span class="contact-name">{{ selectedContact.name }}</span>
          <span v-if="isOnline" class="online-status">在线</span>
        </div>
        
        <div class="header-actions">
          <el-tooltip content="静音" placement="bottom" :disabled="selectedContact.isMuted">
            <el-tooltip content="取消静音" placement="bottom" :disabled="!selectedContact.isMuted">
              <i 
                :class="['action-icon', selectedContact.isMuted ? 'el-icon-turn-off-microphone' : 'el-icon-microphone']"
                @click="toggleMuted"
              ></i>
            </el-tooltip>
          </el-tooltip>
          
          <el-dropdown trigger="click" @command="handleCommand">
            <i class="el-icon-more action-icon"></i>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="selectedContact.isPinned ? 'unpin' : 'pin'">
                  {{ selectedContact.isPinned ? '取消置顶' : '置顶' }}
                </el-dropdown-item>
                <el-dropdown-item command="clear">清空聊天记录</el-dropdown-item>
                <el-dropdown-item command="delete" divided>删除会话</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 聊天内容 -->
      <div class="chat-messages" ref="messagesContainer">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <span>加载中...</span>
        </div>
        
        <div v-else-if="error" class="error-container">
          <span class="error-message">{{ error }}</span>
          <button @click="loadMessages" class="retry-button">重试</button>
        </div>
        
        <template v-else>
          <div v-if="messages.length === 0" class="empty-messages">
            <p>暂无消息</p>
          </div>
          
          <div v-else class="message-list">
            <div 
              v-for="(message, index) in messages" 
              :key="message.id || message.clientMessageId"
              class="message-item"
              :class="{ 
                'own-message': message.senderId === currentUserId,
                'other-message': message.senderId !== currentUserId
              }"
            >
              <!-- 日期分隔线 -->
              <div 
                v-if="shouldShowDateDivider(message, index)" 
                class="date-divider"
              >
                {{ formatDate(message.timestamp) }}
              </div>
              
              <!-- 消息内容 -->
              <div class="message-content">
                <!-- 头像 -->
                <div class="message-avatar" v-if="message.senderId !== currentUserId">
                  <img 
                    v-if="selectedContact.avatar" 
                    :src="selectedContact.avatar" 
                    :alt="selectedContact.name" 
                    class="avatar"
                  />
                  <div v-else class="avatar-text">
                    {{ selectedContact.avatarText || selectedContact.name.charAt(0).toUpperCase() }}
                  </div>
                </div>
                
                <!-- 消息气泡 -->
                <div class="message-bubble">
                  <!-- 文本消息 -->
                  <div v-if="message.messageType === 'TEXT'" class="text-message">
                    {{ message.message }}
                  </div>
                  
                  <!-- 图片消息 -->
                  <div v-else-if="message.messageType === 'IMAGE'" class="image-message">
                    <img 
                      :src="message.fileUrl" 
                      alt="图片" 
                      @click="previewImage(message.fileUrl)"
                      class="message-image"
                    />
                  </div>
                  
                  <!-- 音频消息 -->
                  <div v-else-if="message.messageType === 'AUDIO'" class="audio-message">
                    <div class="audio-player" @click="playAudio(message.fileUrl)">
                      <i class="el-icon-headset"></i>
                      <span>{{ formatDuration(message.audioDuration) }}</span>
                    </div>
                  </div>
                  
                  <!-- 视频消息 -->
                  <div v-else-if="message.messageType === 'VIDEO'" class="video-message">
                    <video 
                      :src="message.fileUrl" 
                      controls 
                      class="message-video"
                    ></video>
                  </div>
                  
                  <!-- 文件消息 -->
                  <div v-else-if="message.messageType === 'FILE'" class="file-message">
                    <div class="file-container" @click="downloadFile(message.fileUrl)">
                      <i class="el-icon-document"></i>
                      <div class="file-info">
                        <span class="file-name">{{ getFileName(message.fileUrl) }}</span>
                        <span class="file-size">{{ formatFileSize(message.fileSize) }}</span>
                      </div>
                      <i class="el-icon-download"></i>
                    </div>
                  </div>
                  
                  <!-- 消息时间和状态 -->
                  <div class="message-meta">
                    <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                    <span 
                      v-if="message.senderId === currentUserId" 
                      class="message-status"
                    >
                      <i 
                        v-if="message.status === 'SENDING'" 
                        class="el-icon-loading"
                      ></i>
                      <i 
                        v-else-if="message.status === 'SENT' || message.isRead === false" 
                        class="el-icon-check"
                      ></i>
                      <i 
                        v-else-if="message.isRead === true" 
                        class="el-icon-check-double"
                      ></i>
                    </span>
                  </div>
                </div>
                
                <!-- 自己的消息头像 -->
                <div class="message-avatar" v-if="message.senderId === currentUserId">
                  <img 
                    v-if="currentUserAvatar" 
                    :src="currentUserAvatar" 
                    alt="我" 
                    class="avatar"
                  />
                  <div v-else class="avatar-text">
                    {{ currentUserName ? currentUserName.charAt(0).toUpperCase() : 'Me' }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
      
      <!-- 聊天输入框 -->
      <div class="chat-input">
        <div class="input-actions">
          <i class="el-icon-picture-outline action-icon" @click="openImageUpload"></i>
          <i class="el-icon-microphone action-icon" @click="startRecordAudio"></i>
          <i class="el-icon-video-camera action-icon" @click="openVideoUpload"></i>
          <i class="el-icon-folder-add action-icon" @click="openFileUpload"></i>
          <input 
            type="file" 
            ref="fileInput" 
            style="display: none" 
            @change="handleFileSelected"
          />
        </div>
        
        <div class="input-container">
          <textarea 
            v-model="messageText" 
            class="message-textarea" 
            placeholder="输入消息..." 
            @keydown.enter.prevent="sendMessage"
          ></textarea>
          <button 
            class="send-button" 
            :disabled="!messageText.trim()" 
            @click="sendMessage"
          >
            发送
          </button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { useUserStore } from '@/stores/userStore';
import { useConversationStore } from '@/stores/conversationStore';
import { useWebSocketStore } from '@/stores/webSocketStore';
import { useChatStore } from '@/stores/chatStore';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  selectedContact: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update-conversation']);

// Store
const userStore = useUserStore();
const conversationStore = useConversationStore();
const webSocketStore = useWebSocketStore();
const chatStore = useChatStore();

// Refs
const messagesContainer = ref(null);
const fileInput = ref(null);
const messageText = ref('');
const currentUploadType = ref(null);

// Computed
const currentUserId = computed(() => userStore.user?.id);
const currentUserName = computed(() => userStore.user?.username);
const currentUserAvatar = computed(() => userStore.user?.avatar);
const messages = computed(() => chatStore.messages);
const loading = computed(() => chatStore.loading);
const error = computed(() => chatStore.error);
const isOnline = computed(() => {
  return props.selectedContact && webSocketStore.onlineUsers.includes(props.selectedContact.contactId);
});

// Methods
function loadMessages() {
  if (!props.selectedContact) return;
  
  chatStore.fetchMessages(currentUserId.value, props.selectedContact.contactId);
}

function sendMessage() {
  if (!messageText.value.trim()) return;
  
  chatStore.sendTextMessage(
    currentUserId.value,
    props.selectedContact.contactId,
    messageText.value
  );
  
  messageText.value = '';
}

function openImageUpload() {
  currentUploadType.value = 'IMAGE';
  fileInput.value.accept = 'image/*';
  fileInput.value.click();
}

function openVideoUpload() {
  currentUploadType.value = 'VIDEO';
  fileInput.value.accept = 'video/*';
  fileInput.value.click();
}

function openFileUpload() {
  currentUploadType.value = 'FILE';
  fileInput.value.accept = '*/*';
  fileInput.value.click();
}

function handleFileSelected(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  switch (currentUploadType.value) {
    case 'IMAGE':
      chatStore.sendImageMessage(currentUserId.value, props.selectedContact.contactId, file);
      break;
    case 'VIDEO':
      chatStore.sendVideoMessage(currentUserId.value, props.selectedContact.contactId, file);
      break;
    case 'FILE':
      chatStore.sendFileMessage(currentUserId.value, props.selectedContact.contactId, file);
      break;
  }
  
  // 重置文件输入
  event.target.value = '';
}

function startRecordAudio() {
  ElMessage.info('录音功能开发中...');
}

function playAudio(url) {
  const audio = new Audio(url);
  audio.play();
}

function previewImage(url) {
  // 实现图片预览
}

function downloadFile(url) {
  window.open(url, '_blank');
}

function getFileName(url) {
  if (!url) return '未知文件';
  return url.split('/').pop();
}

function formatFileSize(size) {
  if (!size) return '';
  
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB';
  } else {
    return (size / (1024 * 1024)).toFixed(1) + ' MB';
  }
}

function formatTime(timestamp) {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
}

function formatDate(timestamp) {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  const now = new Date();
  
  // 今天
  if (date.toDateString() === now.toDateString()) {
    return '今天';
  }
  
  // 昨天
  const yesterday = new Date(now);
  yesterday.setDate(now.getDate() - 1);
  if (date.toDateString() === yesterday.toDateString()) {
    return '昨天';
  }
  
  // 一周内
  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const dayDiff = Math.floor((now - date) / (24 * 60 * 60 * 1000));
  if (dayDiff < 7) {
    return weekDays[date.getDay()];
  }
  
  // 其他日期
  return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
}

function formatDuration(seconds) {
  if (!seconds) return '';
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  if (minutes > 0) {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${remainingSeconds}秒`;
  }
}

function shouldShowDateDivider(message, index) {
  if (index === 0) return true;
  
  const currentDate = new Date(message.timestamp).toDateString();
  const prevDate = new Date(messages.value[index - 1].timestamp).toDateString();
  
  return currentDate !== prevDate;
}

async function toggleMuted() {
  if (!props.selectedContact || !props.selectedContact.conversationId) return;
  
  const isMuted = !props.selectedContact.isMuted;
  const success = await conversationStore.updateConversationMuted(
    props.selectedContact.conversationId, 
    isMuted
  );
  
  if (success) {
    emit('update-conversation', {
      ...props.selectedContact,
      isMuted
    });
    
    ElMessage.success(isMuted ? '会话已静音' : '已取消静音');
  }
}

async function handleCommand(command) {
  if (!props.selectedContact || !props.selectedContact.conversationId) return;
  
  switch (command) {
    case 'pin':
      const pinSuccess = await conversationStore.updateConversationPinned(
        props.selectedContact.conversationId, 
        true
      );
      if (pinSuccess) {
        emit('update-conversation', {
          ...props.selectedContact,
          isPinned: true
        });
        ElMessage.success('会话已置顶');
      }
      break;
      
    case 'unpin':
      const unpinSuccess = await conversationStore.updateConversationPinned(
        props.selectedContact.conversationId, 
        false
      );
      if (unpinSuccess) {
        emit('update-conversation', {
          ...props.selectedContact,
          isPinned: false
        });
        ElMessage.success('已取消置顶');
      }
      break;
      
    case 'clear':
      ElMessageBox.confirm('确定要清空聊天记录吗？此操作不可恢复。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        chatStore.clearMessages();
        ElMessage.success('聊天记录已清空');
      }).catch(() => {});
      break;
      
    case 'delete':
      ElMessageBox.confirm('确定要删除此会话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const success = await conversationStore.updateConversationStatus(
          props.selectedContact.conversationId, 
          'DELETED'
        );
        if (success) {
          emit('update-conversation', null);
          ElMessage.success('会话已删除');
        }
      }).catch(() => {});
      break;
  }
}

// 滚动到底部
function scrollToBottom() {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
}

// 监听选中的联系人变化
watch(() => props.selectedContact, (newContact) => {
  if (newContact) {
    loadMessages();
    
    // 重置未读计数
    if (newContact.unreadCount > 0) {
      conversationStore.resetUnreadCount(currentUserId.value, newContact.contactId);
    }
  }
}, { immediate: true });

// 监听消息列表变化，滚动到底部
watch(() => messages.value.length, () => {
  scrollToBottom();
});

// 监听新消息
watch(() => chatStore.newMessage, (newMessage) => {
  if (newMessage) {
    // 如果是当前会话的消息，标记为已读
    if (props.selectedContact && 
        ((newMessage.senderId === props.selectedContact.contactId && newMessage.receiverId === currentUserId.value) ||
         (newMessage.receiverId === props.selectedContact.contactId && newMessage.senderId === currentUserId.value))) {
      chatStore.markMessageAsRead(newMessage.id, currentUserId.value);
    }
  }
});

// 生命周期钩子
onMounted(() => {
  scrollToBottom();
});
</script>

<style scoped>
.chat-window {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.empty-chat {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f9f9f9;
}

.empty-chat-content {
  text-align: center;
  color: #999;
}

.empty-chat-content i {
  font-size: 48px;
  margin-bottom: 16px;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.contact-info {
  display: flex;
  align-items: center;
}

.contact-name {
  font-weight: bold;
  font-size: 16px;
  margin-right: 8px;
}

.online-status {
  font-size: 12px;
  color: #2ecc71;
}

.header-actions {
  display: flex;
  align-items: center;
}

.action-icon {
  font-size: 18px;
  color: #666;
  margin-left: 16px;
  cursor: pointer;
}

.action-icon:hover {
  color: #3498db;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.loading-container, .error-container, .empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  margin-top: 10px;
  padding: 5px 15px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.message-list {
  display: flex;
  flex-direction: column;
}

.date-divider {
  text-align: center;
  margin: 16px 0;
  color: #999;
  font-size: 12px;
}

.message-item {
  margin-bottom: 16px;
}

.message-content {
  display: flex;
  align-items: flex-start;
}

.own-message .message-content {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 8px;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-text {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
}

.message-bubble {
  max-width: 70%;
  border-radius: 12px;
  padding: 8px 12px;
  position: relative;
}

.own-message .message-bubble {
  background-color: #dcf8c6;
  border-top-right-radius: 0;
}

.other-message .message-bubble {
  background-color: white;
  border-top-left-radius: 0;
}

.text-message {
  word-break: break-word;
}

.image-message {
  padding: 4px;
}

.message-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
}

.audio-message {
  padding: 4px;
}

.audio-player {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  padding: 8px 12px;
  cursor: pointer;
}

.audio-player i {
  margin-right: 8px;
}

.video-message {
  padding: 4px;
}

.message-video {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
}

.file-message {
  padding: 4px;
}

.file-container {
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
}

.file-container i {
  margin-right: 8px;
}

.file-info {
  flex: 1;
  margin: 0 8px;
}

.file-name {
  display: block;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.message-meta {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}

.message-time {
  margin-right: 4px;
}

.message-status i {
  font-size: 14px;
}

.chat-input {
  background-color: white;
  border-top: 1px solid #eee;
  padding: 12px 16px;
}

.input-actions {
  display: flex;
  margin-bottom: 8px;
}

.input-container {
  display: flex;
}

.message-textarea {
  flex: 1;
  height: 40px;
  max-height: 120px;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 10px 16px;
  resize: none;
  outline: none;
  font-family: inherit;
  font-size: 14px;
}

.send-button {
  margin-left: 8px;
  padding: 0 20px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
}

.send-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
</style>
