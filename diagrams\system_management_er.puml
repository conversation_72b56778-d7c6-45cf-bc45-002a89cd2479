@startuml 系统管理ER图

skinparam backgroundColor white
skinparam handwritten false
skinparam defaultFontName Arial
skinparam roundcorner 8
skinparam shadowing false

skinparam class {
  BackgroundColor #F0F8FF
  BorderColor #2C3E50
  ArrowColor #2C3E50
  FontColor #2C3E50
}

skinparam note {
  BackgroundColor #FFFACD
  BorderColor #DAA520
}

title 系统管理模块ER图

package "系统管理" {
  class SystemAnnouncement {
    <<Entity>>
    --主键--
    + id: Long
    --属性--
    + title: String
    + content: String
    + startTime: LocalDateTime
    + endTime: LocalDateTime
    + status: Integer
    + priority: Integer
    + type: String
    + createdBy: Long
    + createdAt: LocalDateTime
    + updatedAt: LocalDateTime
  }
  
  class AnnouncementReadRecord {
    <<Entity>>
    --主键--
    + id: Long
    --外键--
    + announcementId: Long
    + userId: Long
    --属性--
    + readTime: LocalDateTime
    + createdAt: LocalDateTime
  }
  
  class ConfigurationItem {
    <<Entity>>
    --主键--
    + id: Long
    --属性--
    + configKey: String
    + configValue: String
    + description: String
    + createdAt: LocalDateTime
    + updatedAt: LocalDateTime
    + createdBy: Long
    + updatedBy: Long
  }
  
  class OperationLog {
    <<Entity>>
    --主键--
    + id: Long
    --属性--
    + userId: Long
    + operation: String
    + method: String
    + params: String
    + ip: String
    + location: String
    + status: Integer
    + errorMsg: String
    + operationTime: LocalDateTime
    + createdAt: LocalDateTime
  }
  
  class StatisticsData {
    <<Entity>>
    --主键--
    + id: Long
    --属性--
    + statDate: LocalDate
    + userCount: Integer
    + lostItemCount: Integer
    + foundItemCount: Integer
    + matchCount: Integer
    + claimCount: Integer
    + messageCount: Integer
    + createdAt: LocalDateTime
  }
  
  class UserFeedback {
    <<Entity>>
    --主键--
    + id: Long
    --外键--
    + userId: Long
    --属性--
    + content: String
    + contactInfo: String
    + status: Integer
    + type: String
    + reply: String
    + replyBy: Long
    + replyTime: LocalDateTime
    + createdAt: LocalDateTime
    + updatedAt: LocalDateTime
  }
}

' 关系定义
SystemAnnouncement "1" -- "0..*" AnnouncementReadRecord : 包含 >
UserFeedback "0..*" -- "1" OperationLog : 关联 >

note bottom of SystemAnnouncement
  系统公告实体，用于管理系统级别的通知，
  包括公告的发布、更新和过期管理
end note

note right of ConfigurationItem
  系统配置项实体，用于存储系统各项可配置参数，
  支持动态修改系统行为而无需重启
end note

note bottom of OperationLog
  操作日志实体，记录用户的关键操作，
  用于系统审计和问题排查
end note

note right of StatisticsData
  统计数据实体，存储系统每日运行数据，
  用于生成报表和分析系统使用情况
end note

@enduml