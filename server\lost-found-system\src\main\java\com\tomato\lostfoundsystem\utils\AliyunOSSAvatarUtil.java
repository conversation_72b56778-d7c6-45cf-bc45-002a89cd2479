package com.tomato.lostfoundsystem.utils;

import com.aliyun.oss.OSS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 阿里云OSS头像上传工具类
 */
@Slf4j
@Component
public class AliyunOSSAvatarUtil {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.access-key-id}")
    private String accessKeyId;

    @Value("${aliyun.oss.access-key-secret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucket-name}")
    private String bucketName;

    @Value("${aliyun.oss.avatar-directory:avatars}")
    private String avatarDirectory;

    @Value("${aliyun.cdn.domain:#{null}}")
    private String cdnDomain;

    @jakarta.annotation.Resource
    private com.tomato.lostfoundsystem.service.CDNConfigService cdnConfigService;

    /**
     * 上传用户头像到阿里云OSS
     *
     * @param file   头像文件
     * @param userId 用户ID
     * @return 头像访问URL及不同尺寸的变体
     * @throws IOException 如果上传过程中发生错误
     */
    public Map<String, String> uploadAvatar(MultipartFile file, Long userId) throws IOException {
        // 验证文件
        OSSUtils.validateImageFile(file, 2 * 1024 * 1024); // 2MB限制

        // 生成唯一文件名
        String extension = OSSUtils.getFileExtension(file.getOriginalFilename());
        String baseFilename = "avatar_" + userId + "_" + UUID.randomUUID().toString().replace("-", "");
        String filename = baseFilename + extension;

        // 定义头像尺寸
        Map<String, Integer> sizes = new HashMap<>();
        sizes.put("large", 200);
        sizes.put("medium", 100);
        sizes.put("small", 50);

        // 上传原始图像和生成变体
        return storeImageWithVariants(file, avatarDirectory, baseFilename, extension, sizes);
    }

    /**
     * 存储图像及其变体
     *
     * @param file         图像文件
     * @param directory    存储目录
     * @param baseFilename 基础文件名（不含扩展名）
     * @param extension    文件扩展名（包含点，如".jpg"）
     * @param sizes        尺寸映射，键为尺寸名称，值为尺寸大小
     * @return 不同尺寸的图像URL映射
     * @throws IOException 如果处理过程中发生错误
     */
    private Map<String, String> storeImageWithVariants(MultipartFile file, String directory,
                                                      String baseFilename, String extension,
                                                      Map<String, Integer> sizes) throws IOException {
        Map<String, String> urls = new HashMap<>();
        OSS ossClient = null;

        try {
            // 使用OSSUtils创建客户端
            ossClient = OSSUtils.createOSSClient(endpoint, accessKeyId, accessKeySecret);

            // 读取原始图像
            BufferedImage originalImage = ImageIO.read(file.getInputStream());
            if (originalImage == null) {
                throw new IOException("无法读取上传的图像文件");
            }

            // 上传原始图像
            String originalKey = directory + "/" + baseFilename + extension;
            try (InputStream is = file.getInputStream()) {
                // 使用OSSUtils上传文件
                OSSUtils.putObject(ossClient, bucketName, originalKey, is,
                                  file.getContentType(), file.getSize());
            }
            // 优先使用CDN配置服务获取CDN域名
            String currentCdnDomain = null;
            try {
                currentCdnDomain = cdnConfigService.getCDNDomain();
            } catch (Exception e) {
                log.warn("从CDN配置服务获取域名失败，将使用静态配置: {}", e.getMessage());
                currentCdnDomain = cdnDomain;
            }

            // 使用OSSUtils获取文件URL
            urls.put("original", OSSUtils.getFileUrl(bucketName, endpoint, originalKey, currentCdnDomain));

            // 生成并上传不同尺寸的变体
            for (Map.Entry<String, Integer> entry : sizes.entrySet()) {
                String sizeName = entry.getKey();
                int size = entry.getValue();

                // 处理图像
                BufferedImage resizedImage = ImageUtils.resizeAndCropToSquare(originalImage, size);

                // 转换为输入流
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                ImageIO.write(resizedImage, extension.substring(1), os);
                byte[] imageBytes = os.toByteArray();

                // 上传到OSS
                String variantKey = directory + "/" + baseFilename + "_" + sizeName + extension;
                try (InputStream is = new ByteArrayInputStream(imageBytes)) {
                    // 使用OSSUtils上传文件
                    OSSUtils.putObject(ossClient, bucketName, variantKey, is,
                                      file.getContentType(), imageBytes.length);
                }

                // 使用OSSUtils获取文件URL（使用之前获取的CDN域名）
                urls.put(sizeName, OSSUtils.getFileUrl(bucketName, endpoint, variantKey, currentCdnDomain));
            }

            return urls;
        } catch (Exception e) {
            log.error("上传头像到阿里云OSS失败", e);
            throw new IOException("上传头像失败: " + e.getMessage(), e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 删除用户头像
     *
     * @param avatarUrl 头像URL
     * @return 是否删除成功
     */
    public boolean deleteAvatar(String avatarUrl) {
        if (avatarUrl == null || avatarUrl.isEmpty()) {
            return false;
        }

        OSS ossClient = null;
        try {
            // 使用OSSUtils创建客户端
            ossClient = OSSUtils.createOSSClient(endpoint, accessKeyId, accessKeySecret);

            // 优先使用CDN配置服务获取CDN域名
            String currentCdnDomain = null;
            try {
                currentCdnDomain = cdnConfigService.getCDNDomain();
            } catch (Exception e) {
                log.warn("从CDN配置服务获取域名失败，将使用静态配置: {}", e.getMessage());
                currentCdnDomain = cdnDomain;
            }

            // 从URL中提取对象键
            String objectKey = OSSUtils.extractObjectKeyFromUrl(avatarUrl, bucketName, endpoint, currentCdnDomain);
            if (objectKey == null) {
                return false;
            }

            // 删除原始图像
            OSSUtils.deleteObject(ossClient, bucketName, objectKey);

            // 尝试删除所有变体
            String baseKey = objectKey.substring(0, objectKey.lastIndexOf("."));
            String extension = objectKey.substring(objectKey.lastIndexOf("."));

            // 删除各种尺寸的变体
            OSSUtils.deleteObject(ossClient, bucketName, baseKey + "_large" + extension);
            OSSUtils.deleteObject(ossClient, bucketName, baseKey + "_medium" + extension);
            OSSUtils.deleteObject(ossClient, bucketName, baseKey + "_small" + extension);

            return true;
        } catch (Exception e) {
            log.error("删除头像失败", e);
            return false;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }


}
