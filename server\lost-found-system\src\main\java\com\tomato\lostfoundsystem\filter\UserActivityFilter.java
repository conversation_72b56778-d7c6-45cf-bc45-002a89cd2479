package com.tomato.lostfoundsystem.filter;

import com.tomato.lostfoundsystem.service.StatisticsService;
import com.tomato.lostfoundsystem.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * 用户活动过滤器
 * 用于记录用户活动，统计活跃用户
 */
@Component
@Order(Ordered.LOWEST_PRECEDENCE - 100)
public class UserActivityFilter extends OncePerRequestFilter {

    // 手动定义日志对象
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(UserActivityFilter.class);

    @Autowired
    private StatisticsService statisticsService;

    @Autowired
    private SecurityUtil securityUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // 先执行过滤链，确保请求正常处理
        filterChain.doFilter(request, response);

        // 请求处理完成后，异步记录用户活动
        try {
            Long userId = securityUtil.getCurrentUserId();
            log.debug("UserActivityFilter - 当前用户ID: {}", userId);
            if (userId != null) {
                CompletableFuture.runAsync(() -> {
                    statisticsService.recordUserActivity(userId);
                });
            }
        } catch (Exception e) {
            log.error("记录用户活动失败", e);
            // 不影响正常请求处理
        }
    }
}
