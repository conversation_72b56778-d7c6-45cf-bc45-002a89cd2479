package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.ItemFeatureVector;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 物品特征向量Mapper接口
 */
@Mapper
public interface ItemFeatureVectorMapper {

    /**
     * 插入物品特征向量
     *
     * @param featureVector 特征向量对象
     * @return 影响的行数
     */
    @Insert("INSERT INTO item_feature_vectors(item_id, item_type, image_vector, text_vector, vector_version) " +
            "VALUES(#{itemId}, #{itemType}, #{imageVector}, #{textVector}, #{vectorVersion})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertFeatureVector(ItemFeatureVector featureVector);

    /**
     * 更新物品特征向量
     *
     * @param featureVector 特征向量对象
     * @return 影响的行数
     */
    @Update("UPDATE item_feature_vectors SET " +
            "image_vector = #{imageVector}, " +
            "text_vector = #{textVector}, " +
            "vector_version = #{vectorVersion}, " +
            "updated_at = NOW() " +
            "WHERE item_id = #{itemId} AND item_type = #{itemType}")
    int updateFeatureVector(ItemFeatureVector featureVector);

    /**
     * 根据物品ID和类型查询特征向量
     *
     * @param itemId 物品ID
     * @param itemType 物品类型
     * @return 特征向量对象
     */
    @Select("SELECT * FROM item_feature_vectors WHERE item_id = #{itemId} AND item_type = #{itemType}")
    ItemFeatureVector getFeatureVector(@Param("itemId") Long itemId, @Param("itemType") String itemType);

    /**
     * 检查物品是否已有特征向量
     *
     * @param itemId 物品ID
     * @param itemType 物品类型
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) FROM item_feature_vectors WHERE item_id = #{itemId} AND item_type = #{itemType}")
    int checkFeatureVectorExists(@Param("itemId") Long itemId, @Param("itemType") String itemType);

    /**
     * 删除物品特征向量
     *
     * @param itemId 物品ID
     * @param itemType 物品类型
     * @return 影响的行数
     */
    @Delete("DELETE FROM item_feature_vectors WHERE item_id = #{itemId} AND item_type = #{itemType}")
    int deleteFeatureVector(@Param("itemId") Long itemId, @Param("itemType") String itemType);
}
