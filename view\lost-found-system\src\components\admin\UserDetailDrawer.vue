<template>
  <el-drawer
    v-model="visible"
    :title="title"
    direction="rtl"
    size="35%"
    :destroy-on-close="true"
  >
    <div class="user-detail-container">
      <!-- 用户基本信息卡片 -->
      <el-card class="detail-card">
        <template #header>
          <div class="card-header">
            <el-avatar :size="64" :src="user?.avatar">
              {{ user?.username?.charAt(0)?.toUpperCase() }}
            </el-avatar>
            <div class="user-title">
              <h3>{{ user?.username }}</h3>
              <el-tag :type="getRoleType(user?.role)">{{ getRoleLabel(user?.role) }}</el-tag>
            </div>
          </div>
        </template>
        
        <el-descriptions :column="1" border>
          <el-descriptions-item label="用户ID">{{ user?.id }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ user?.email || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ user?.phone || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="user?.isActive ? 'true' : 'false'">
              {{ user?.isActive ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatDate(user?.createTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { formatDate } from '@/utils/date'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 添加更详细的 watch 来观察用户数据
watch(() => props.user, (newUser) => {
  console.log('=== 用户详情数据调试信息 ===')
  console.log('完整用户数据:', JSON.stringify(newUser, null, 2))
  console.log('isActive 原始值:', newUser?.isActive)
  console.log('isActive 类型:', typeof newUser?.isActive)
  console.log('isActive 转换为数字:', Number(newUser?.isActive))
  console.log('isActive 转换为布尔值:', Boolean(newUser?.isActive))
  console.log('=== 调试信息结束 ===')
}, { immediate: true })

const title = computed(() => `用户详情 - ${props.user?.username || ''}`)

// 获取角色标签类型
const getRoleType = (role) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return 'danger'
    case 'ADMIN':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取角色显示文本
const getRoleLabel = (role) => {
  switch (role) {
    case 'SUPER_ADMIN':
      return '超级管理员'
    case 'ADMIN':
      return '管理员'
    default:
      return '普通用户'
  }
}
</script>

<style scoped>
.user-detail-container {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-title {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-title h3 {
  margin: 0;
  font-size: 18px;
}

:deep(.el-descriptions__label) {
  width: 100px;
  justify-content: flex-end;
}
</style> 