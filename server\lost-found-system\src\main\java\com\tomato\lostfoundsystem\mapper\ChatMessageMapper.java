package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.ChatMessage;
import com.tomato.lostfoundsystem.entity.LastMessageInfo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ChatMessageMapper {

    // 插入聊天消息
    void insertChatMessage(ChatMessage chatMessage);

    // 获取用户和目标用户之间的聊天记录
    List<ChatMessage> getChatHistory(@Param("userId")Long userId, @Param("otherUserId")Long otherUserId);

    // 获取最后一条聊天信息
    LastMessageInfo getLastMessageInfo(@Param("userId") Long userId, @Param("contactId") Long contactId);

    // 更新聊天消息的音频和视频时长
    void updateChatMessageWithDuration(ChatMessage chatMessage);

    // 根据消息ID获取消息
    ChatMessage getMessageById(@Param("messageId") Long messageId);
}

