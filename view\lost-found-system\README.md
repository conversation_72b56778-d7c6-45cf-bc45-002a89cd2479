# Vue 3 + Vite

This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about IDE Support for Vue in the [Vue Docs Scaling up Guide](https://vuejs.org/guide/scaling-up/tooling.html#ide-support).

# 失物招领系统

## 登录流程说明

### 1. 登录方式

系统提供三种登录方式：
- 账号密码登录
- 邮箱验证码登录
- 手机验证码登录

### 2. 登录流程详解

#### 2.1 账号密码登录流程

1. 用户输入：
   - 用户名
   - 密码
   - 图形验证码

2. 前端验证：
   ```javascript
   const loginRules = {
     username: [
       { required: true, message: '请输入用户名', trigger: 'blur' }
     ],
     password: [
       { required: true, message: '请输入密码', trigger: 'blur' },
       { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
     ],
     verifyCode: [
       { required: true, message: '请输入图形验证码', trigger: 'blur' }
     ]
   }
   ```

3. 图形验证码获取：
   ```javascript
   const refreshCaptcha = async () => {
     try {
       const res = await getCaptcha()
       captchaUrl.value = res.data
     } catch (error) {
       ElMessage.error('获取验证码失败')
     }
   }
   ```

#### 2.2 邮箱验证码登录流程

1. 用户输入：
   - 邮箱地址
   - 验证码

2. 前端验证：
   ```javascript
   const loginRules = {
     email: [
       { required: true, message: '请输入邮箱', trigger: 'blur' },
       { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
     ],
     code: [
       { required: true, message: '请输入验证码', trigger: 'blur' },
       { len: 6, message: '验证码长度应为6位', trigger: 'blur' }
     ]
   }
   ```

3. 发送验证码：
   ```javascript
   const sendEmailCode = async () => {
     if (!loginForm.email) {
       ElMessage.warning('请先输入邮箱')
       return
     }
     try {
       await sendEmailCode({
         email: loginForm.email,
         isRegister: false
       })
       ElMessage.success('验证码已发送')
       // 开始倒计时
       emailCodeTimer.value = 60
       emailTimer = setInterval(() => {
         if (emailCodeTimer.value > 0) {
           emailCodeTimer.value--
         } else {
           clearInterval(emailTimer)
         }
       }, 1000)
     } catch (error) {
       ElMessage.error('发送验证码失败')
     }
   }
   ```

#### 2.3 手机验证码登录流程

1. 用户输入：
   - 手机号
   - 验证码

2. 前端验证：
   ```javascript
   const loginRules = {
     phone: [
       { required: true, message: '请输入手机号', trigger: 'blur' },
       { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
     ],
     code: [
       { required: true, message: '请输入验证码', trigger: 'blur' },
       { len: 6, message: '验证码长度应为6位', trigger: 'blur' }
     ]
   }
   ```

3. 发送验证码：
   ```javascript
   const sendPhoneCode = async () => {
     if (!loginForm.phone) {
       ElMessage.warning('请先输入手机号')
       return
     }
     try {
       await sendPhoneCode({
         phone: loginForm.phone,
         isRegister: false
       })
       ElMessage.success('验证码已发送')
       // 开始倒计时
       phoneCodeTimer.value = 60
       phoneTimer = setInterval(() => {
         if (phoneCodeTimer.value > 0) {
           phoneCodeTimer.value--
         } else {
           clearInterval(phoneTimer)
         }
       }, 1000)
     } catch (error) {
       ElMessage.error('发送验证码失败')
     }
   }
   ```

### 3. 登录请求处理

1. 表单验证：
   ```javascript
   const handleLogin = async () => {
     if (!loginFormRef.value) return
     
     try {
       await loginFormRef.value.validate()
       loading.value = true
       
       const loginData = {
         ...loginForm,
         verifyCode: loginType.value === 'account' ? loginForm.verifyCode : undefined,
         code: loginType.value !== 'account' ? loginForm.code : undefined
       }
       
       const res = await login(loginData)
       if (res.code === 200) {
         userStore.setToken(res.data.token)
         userStore.setUser(res.data.user)
         ElMessage.success('登录成功')
         router.push('/')
       }
     } catch (error) {
       ElMessage.error(error.message || '登录失败')
     } finally {
       loading.value = false
     }
   }
   ```

2. 状态管理：
   ```javascript
   export const useUserStore = defineStore('user', {
     state: () => ({
       token: localStorage.getItem('token') || '',
       user: JSON.parse(localStorage.getItem('user') || '{}')
     }),
     
     getters: {
       getToken: (state) => state.token,
       getUser: (state) => state.user,
       getIsAuthenticated: (state) => !!state.token
     },
     
     actions: {
       setToken(token) {
         this.token = token
         localStorage.setItem('token', token)
       },
       
       setUser(user) {
         this.user = user
         localStorage.setItem('user', JSON.stringify(user))
       }
     }
   })
   ```

### 4. 请求拦截器

```javascript
const request = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    const token = userStore.getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code === 200) {
      return res
    } else {
      ElMessage.error(res.message || '请求失败')
      return Promise.reject(new Error(res.message || '请求失败'))
    }
  },
  error => {
    ElMessage.error(error.message || '请求失败')
    return Promise.reject(error)
  }
)
```

### 5. API接口

```javascript
// 获取图形验证码
export function getCaptcha() {
  return request({
    url: '/user/generateCaptcha',
    method: 'post'
  })
}

// 发送邮箱验证码
export function sendEmailCode(data) {
  return request({
    url: '/verify/sendEmailCode',
    method: 'post',
    data
  })
}

// 发送手机验证码
export function sendPhoneCode(data) {
  return request({
    url: '/verify/sendPhoneCode',
    method: 'post',
    data
  })
}

// 用户登录
export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}
```

### 6. 注意事项

1. 验证码发送有60秒冷却时间
2. 登录成功后会自动跳转到首页
3. 登录状态会保存在localStorage中
4. 所有请求都会自动带上token
5. 统一的错误处理和提示

## 用户注册流程

### 1. 注册页面布局
- 使用 Element Plus 组件库构建美观的注册界面
- 采用卡片式布局，提供清晰的视觉层次
- 响应式设计，适配不同屏幕尺寸

### 2. 注册表单字段
- 用户名：3-20个字符
- 登录方式：支持邮箱或手机号二选一
- 验证码：6位数字验证码
- 密码：6-20个字符
- 确认密码：需与密码一致
- 用户协议：必须同意才能注册

### 3. 表单验证规则
- 用户名：必填，长度3-20字符
- 邮箱：必填，符合邮箱格式
- 手机号：必填，符合手机号格式（1开头的11位数字）
- 验证码：必填，6位数字
- 密码：必填，长度6-20字符
- 确认密码：必填，必须与密码一致
- 用户协议：必须勾选

### 4. 验证码发送流程
1. 用户选择登录方式（邮箱/手机号）
2. 输入对应的联系方式
3. 点击"获取验证码"按钮
4. 系统验证联系方式格式
5. 发送验证码到用户邮箱/手机
6. 启动60秒倒计时，防止频繁发送

### 5. 注册流程
1. 用户填写所有必要信息
2. 勾选用户协议和隐私政策
3. 点击注册按钮
4. 前端进行表单验证
5. 发送注册请求到后端
6. 处理注册结果：
   - 成功：跳转到登录页
   - 失败：显示错误信息

### 6. 接口说明
- 注册接口：`POST /api/user/register`
- 邮箱验证码：`POST /api/verify/sendEmailCode`
- 手机验证码：`POST /api/verify/sendPhoneCode`

### 7. 数据格式
```typescript
// 注册请求数据
interface RegisterRequest {
  username: string;        // 用户名
  password: string;        // 密码
  confirmPassword: string; // 确认密码
  code: string;           // 验证码
  email?: string;         // 邮箱（二选一）
  phone?: string;         // 手机号（二选一）
}

// 验证码请求数据
interface VerifyCodeRequest {
  email?: string;         // 邮箱
  phone?: string;         // 手机号
  isRegister: boolean;    // 是否注册用途
}
```

### 8. 错误处理
- 表单验证错误：实时显示在对应字段下方
- 验证码发送失败：显示错误提示
- 注册失败：显示后端返回的具体错误信息
- 网络错误：显示友好的错误提示

### 9. 安全性考虑
- 密码输入框使用密文显示
- 验证码发送有频率限制
- 表单提交前进行完整性验证
- 用户协议必须同意才能注册

### 10. 用户体验优化
- 输入框添加图标提示
- 验证码按钮状态反馈
- 加载状态显示
- 表单验证即时反馈
- 错误信息清晰明确
- 登录入口便捷可见
