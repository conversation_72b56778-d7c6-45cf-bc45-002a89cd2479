package com.tomato.lostfoundsystem.mapper;

import com.tomato.lostfoundsystem.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface UserMapper {
    User findByUsername(@Param("username") String username);
    User findByPhone(String phone);
    User findByEmail(String email);
    int insertUser(User user);

    // 获取手机号绑定的邮箱（如果有）
    String getEmailByPhone(@Param("phone") String phone);

    User findById(Long userId);

    int updateUser(User currentUser);

    List<User> searchUsers(@Param("keyword") String keyword,
                           @Param("isActive") Boolean isActive,
                           @Param("role") String role,
                           @Param("deleted") Boolean deleted);


   //修改用户角色
   void updateUserRole(@Param("id") Long id, @Param("role") String role);

   //启用和禁用角色
   void updateUserStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);

    void updateUserPassword(@Param("id") Long id, @Param("password") String password);

    void logicalDeleteUser(@Param("id") Long id);

    List<Long> getUserIdsByRole(@Param("role")String role);

    List<Long> getAllUserIds();  // 获取所有用户ID

    /**
     * 更新用户头像
     *
     * @param id 用户ID
     * @param avatar 头像URL
     * @return 影响的行数
     */
    int updateUserAvatar(@Param("id") Long id, @Param("avatar") String avatar);

    /**
     * 统计总用户数（未删除的）
     *
     * @return 用户总数
     */
    int countTotalUsers();

    /**
     * 统计指定日期注册的用户数
     *
     * @param date 注册日期
     * @return 用户数
     */
    int countUsersByRegistrationDate(@Param("date") LocalDate date);

    /**
     * 统计指定月份注册的用户数
     *
     * @param year 年份
     * @param month 月份
     * @return 用户数
     */
    int countUsersByRegistrationMonth(@Param("year") int year, @Param("month") int month);
}
