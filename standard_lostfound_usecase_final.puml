@startuml 校园失物招领系统用例图

' 设置全局样式 - 符合中文论文规范
skinparam backgroundColor white
skinparam defaultFontName SimSun
skinparam defaultFontSize 12
skinparam usecase {
  BackgroundColor white
  BorderColor black
  ArrowColor black
  FontName SimSun
  FontSize 12
  shadowing false
  StereotypeFontSize 10
}
skinparam actor {
  BackgroundColor white
  BorderColor black
  FontName SimSun
  FontSize 12
  shadowing false
}
skinparam rectangle {
  FontName SimSun
  FontSize 14
  shadowing false
  BorderColor black
}
skinparam defaultTextAlignment center
skinparam linetype polyline
skinparam nodesep 60
skinparam ranksep 40

' 定义布局方向
left to right direction

' 左侧角色
actor "普通用户" as User

' 系统边界
rectangle "校园失物招领系统" {
  ' 普通用户相关用例（左侧）
  usecase "注册" as Register
  usecase "登录" as Login
  usecase "发布失物信息" as PostLost
  usecase "发布拾物信息" as PostFound
  usecase "查询匹配结果" as QueryMatch
  usecase "即时通讯" as Chat
  usecase "管理个人信息" as ManageProfile
  
  ' 管理员相关用例（右侧）
  usecase "审核物品信息" as ReviewItems
  usecase "管理系统公告" as ManageAnnouncement
  usecase "查看用户列表" as ViewUsers
  usecase "启用/禁用用户" as ManageUserStatus
  usecase "重置用户密码" as ResetPassword
  
  ' 超级管理员相关用例
  usecase "添加管理员" as AddAdmin
  usecase "修改用户角色" as ChangeUserRole
  usecase "系统配置管理" as ManageSystem
}

' 右侧角色
actor "管理员" as Admin
actor "超级管理员" as SuperAdmin

' 普通用户关系
User --> Register
User --> Login
User --> PostLost
User --> PostFound
User --> QueryMatch
User --> Chat
User --> ManageProfile

' 管理员关系
Admin --> Login
Admin --> ReviewItems
Admin --> ManageAnnouncement
Admin --> ViewUsers
Admin --> ManageUserStatus
Admin --> ResetPassword

' 超级管理员继承管理员（垂直关系，子类在下方）
SuperAdmin -up-|> Admin
SuperAdmin --> AddAdmin
SuperAdmin --> ChangeUserRole
SuperAdmin --> ManageSystem

' 包含和扩展关系
PostLost ..> QueryMatch : <<include>>
PostFound ..> QueryMatch : <<include>>
QueryMatch ..> Chat : <<extend>>
ReviewItems ..> ManageAnnouncement : <<extend>>

@enduml
