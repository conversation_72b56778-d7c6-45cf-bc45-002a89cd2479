import request from '../utils/request'

/**
 * 发布失物信息
 * @param {Object} data 失物信息
 * @returns {Promise} 返回请求结果
 */
export function publishLostItem(data) {
  return request({
    url: '/lost-items/publish',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取失物列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回请求结果
 */
export function getLostItems(params = {}) {
  // 确保params是对象
  params = params || {}

  // 处理时间参数
  if (params.startDate && params.endDate) {
    // 确保开始时间是当天的0点
    const startDate = new Date(params.startDate)
    startDate.setHours(0, 0, 0, 0)
    // 确保结束时间是当天的23:59:59
    const endDate = new Date(params.endDate)
    endDate.setHours(23, 59, 59, 999)

    params.startDate = startDate.toISOString().split('.')[0]
    params.endDate = endDate.toISOString().split('.')[0]
  }

  // 确保分页参数
  params.page = params.page || 1
  params.size = params.size || 12

  console.log('API 发送请求参数:', params)

  return request({
    url: '/lost-items/search',
    method: 'get',
    params
  })
}

/**
 * 获取失物详情
 * @param {String} id 失物ID
 * @returns {Promise} 返回请求结果
 */
export function getLostItemDetail(id) {
  return request({
    url: `/lost-items/detail/${id}`,
    method: 'get'
  })
}

/**
 * 更新失物信息
 * @param {String} id 失物ID
 * @param {Object} data 更新的数据
 * @returns {Promise} 返回请求结果
 */
export function updateLostItem(id, data) {
  console.log('更新失物信息:', { id, data })
  return request({
    url: `/lost-items/update/${id}`,
    method: 'put',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).catch(error => {
    console.error('更新失物信息失败:', error)
    throw error
  })
}

/**
 * 删除失物信息
 * @param {String} id 失物ID
 * @returns {Promise} 返回请求结果
 */
export function deleteLostItem(id) {
  return request({
    url: `/lost-items/delete/${id}`,
    method: 'delete'
  })
}


// 认领失物
export function claimLostItem(id) {
  return request({
    url: `/lost-items/${id}/claim`,
    method: 'post'
  })
}

/**
 * 更新失物图片
 * @param {String} id 失物ID
 * @param {Object} data 图片数据
 * @returns {Promise} 返回请求结果
 */
export function updateLostItemImages(id, data) {
  console.log('更新失物图片:', { id, data })
  return request({
    url: `/lost-items/update-images/${id}`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }).catch(error => {
    console.error('更新失物图片失败:', error)
    throw error
  })
}

/**
 * 更新失物状态（未找回/已找回）
 * @param {String} id 失物ID
 * @param {String} status 状态值（LOST/FOUND）
 * @returns {Promise} 返回请求结果
 */
export function updateLostItemStatus(id, status) {
  return request({
    url: `/lost-items/${id}/status`,
    method: 'put',
    data: { status }
  })
}