# 在线状态管理重构计划

## 目标

使用 Pinia Store 统一管理在线状态，减少冗余代码，提高可维护性，同时不修改后端代码。

## 重构步骤

### 第一阶段：准备工作

1. **创建新文件**
   - ✅ `src/services/onlineStatusService.js` - 统一的在线状态服务
   - ✅ `src/utils/online-status-adapter.js` - 兼容层，保持旧API
   - ✅ `src/utils/online-status-new.js` - 新版在线状态管理模块
   - ✅ `src/utils/contact-status-new.js` - 新版联系人状态管理模块

2. **验证新文件**
   - 检查文件语法和逻辑错误
   - 确保导入和导出正确

### 第二阶段：集成测试（低风险）

1. **创建测试页面**
   - 创建一个新的测试页面，使用新的在线状态服务
   - 测试基本功能：获取在线状态、订阅状态变化等
   - 确保与现有系统不冲突

2. **初始化服务**
   - 在 `main.js` 中添加初始化代码，但不替换现有功能
   ```javascript
   // main.js
   import { initOnlineStatusService } from '@/services/onlineStatusService'
   
   // 初始化在线状态服务，但不影响现有功能
   initOnlineStatusService()
   ```

### 第三阶段：渐进式替换（中风险）

1. **替换 websocket.js 中的在线状态函数**
   - 修改 `websocket.js`，使用适配器中的函数
   ```javascript
   // websocket.js
   import { 
     isUserOnline, 
     getOnlineUsers, 
     getOnlineUserCount 
   } from '@/utils/online-status-adapter'
   
   // 导出这些函数，保持API兼容性
   export { isUserOnline, getOnlineUsers, getOnlineUserCount }
   ```

2. **替换 online-status.js**
   - 备份现有文件 `online-status.js` -> `online-status.js.bak`
   - 将 `online-status-new.js` 重命名为 `online-status.js`

3. **替换 contact-status.js**
   - 备份现有文件 `contact-status.js` -> `contact-status.js.bak`
   - 将 `contact-status-new.js` 重命名为 `contact-status.js`

### 第四阶段：清理（高风险）

1. **移除冗余代码**
   - 移除 `websocket.js` 中的 `userOnlineStatus` 对象
   - 移除其他冗余代码

2. **移除适配器**
   - 当确认所有功能正常后，可以考虑移除适配器，直接使用服务

## 实施注意事项

### 备份策略

在每次修改前，确保备份相关文件：
```bash
cp src/utils/websocket.js src/utils/websocket.js.bak
cp src/utils/online-status.js src/utils/online-status.js.bak
cp src/utils/contact-status.js src/utils/contact-status.js.bak
```

### 回滚策略

如果出现问题，可以快速回滚：
```bash
cp src/utils/websocket.js.bak src/utils/websocket.js
cp src/utils/online-status.js.bak src/utils/online-status.js
cp src/utils/contact-status.js.bak src/utils/contact-status.js
```

### 测试策略

1. **单元测试**
   - 为新服务添加单元测试
   - 测试基本功能：获取在线状态、订阅状态变化等

2. **集成测试**
   - 测试与其他组件的集成
   - 测试与WebSocket的集成

3. **功能测试**
   - 测试用户在线状态显示
   - 测试联系人在线状态显示
   - 测试在线用户数量显示

## 验证清单

- [ ] 在线状态服务能够正确初始化
- [ ] 用户在线状态能够正确显示
- [ ] 联系人在线状态能够正确显示
- [ ] 在线用户数量能够正确显示
- [ ] WebSocket断开重连后，在线状态能够正确恢复
- [ ] 没有控制台错误或警告
- [ ] 性能没有明显下降

## 风险评估

| 风险 | 可能性 | 影响 | 缓解策略 |
|------|--------|------|----------|
| WebSocket连接失败 | 中 | 高 | 保留原有重连逻辑，确保新服务能够处理连接失败 |
| 状态不同步 | 低 | 中 | 添加定期同步机制，确保状态一致性 |
| 性能下降 | 低 | 中 | 优化状态更新逻辑，减少不必要的更新 |
| 组件依赖错误 | 中 | 高 | 使用适配器保持API兼容性，渐进式替换 |

## 结论

通过使用Pinia统一管理在线状态，我们可以显著改善代码质量和可维护性，同时不需要修改后端代码。渐进式的重构策略可以降低风险，确保系统稳定性。
